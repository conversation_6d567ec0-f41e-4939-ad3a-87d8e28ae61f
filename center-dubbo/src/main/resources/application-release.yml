server:
  port: 8080

spring:
  datasource:
    url: ***************************************************************************************************************************************************************************************************************************************
    username: MYS<PERSON>(uULLmQ1th6LHecllz29tzA==)
    password: MYSQL(0n5QcDspnsYEuPS+y8IC3pOulEwb6oxu)
  redis:
    password: dfdr3r44DwsdCK
    cluster:
      nodes: uc-hb2-scrm-rds-node1.online.local:7000,uc-hb2-scrm-rds-node2.online.local:7000,uc-hb2-scrm-rds-node3.online.local:7000,uc-hb2-scrm-rds-node4.online.local:7000,uc-hb2-scrm-rds-node5.online.local:7000,uc-hb2-scrm-rds-node6.online.local:7000

dubbo:
  registry:
    address: zookeeper://uc-hb2-pt-zk-node01.plat.cn:2181,uc-hb2-pt-zk-node02.plat.cn:2181,uc-hb2-pt-zk-node03.plat.cn:2181

xxl:
  job:
    accessToken:
    admin:
      #调度中心地址
      addresses: http://bj-zw-xds-ds-api-omo.online.local/xxl-job-admin
    executor:
      appname: scrm-center-job-release
      address:
      ip:
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 3
      port: 9351
      oneTimesJob:
        timeout: 10000

rocketmq:
  name-server: uc-hb2-gg-mq-node1.online.local:9876;uc-hb2-gg-mq-node2.online.local:9876
  producer:
    accessKey: MQ(mCy3PT7me91t5vZGM4ivcLHa8ZT/xQ8M)
    secretKey: MQ(hCCdvFpmV7F61ujvcEiv8qmrRUrnQW349VkN4XWM7Qw=)
  consumer:
    accessKey: MQ(mCy3PT7me91t5vZGM4ivcLHa8ZT/xQ8M)
    secretKey: MQ(hCCdvFpmV7F61ujvcEiv8qmrRUrnQW349VkN4XWM7Qw=)

nacos:
  config:
    server-addr: uc-hb2-ds-ncs-node1.online.local:8080,uc-hb2-ds-ncs-node2.online.local:8080,uc-hb2-ds-ncs-node3.online.local:8080 # nacos服务器列表
    namespace: scrm
    username: NACOS(6C2r4kfn2z+0eSp1eXcy0A==)
    password: NACOS(T8U1r9y86VymGuHppeeot0pNPbWuFJP2P2WufBK4nOc=)

logging:
  config: classpath:logback-release.xml

robot:
  force-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=88e722a4-0d55-4140-9117-47efc5655a42
  weak-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e0dc48ae-97f7-4de2-afad-2a5e5a2b8008

sequence:
  zkAddress: uc-hb2-pt-zk-node01.plat.cn
  zkPort: 2181

bigdata:
  enterpriseInfo: http://local-gateway.datauns.cn/ce22highlevelsearch/highSearch/baseinfo/company_detail?company_name=

third:
  customer:
    secret: 59c0a4c88fe137b44da98dbf509d1783