package com.ce.scrm.center.dubbo.service;

import com.ce.scrm.center.dubbo.api.CustomerProtectDubbo;
import com.ce.scrm.center.dubbo.entity.dto.CustomerProtectDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/22
 */
@DubboService(interfaceClass = CustomerProtectDubbo.class)
public class CustomerProtectDubboService implements CustomerProtectDubbo {

    @Override
    public DubboResult<String> save(CustomerProtectDubboDto customerProtectDubboDto) {
        return null;
    }

    @Override
    public DubboResult<CustomerProtectDubboVew> selectCustomerById(String customerId) {
        return null;
    }

    @Override
    public DubboResult<List<CustomerProtectDubboVew>> selectCustomerByUncid(String uncid) {
        return null;
    }

    @Override
    public DubboResult<CustomerProtectDubboVew> selectCustomerByName(String customerName) {
        return null;
    }

    @Override
    public DubboResult<List<CustomerProtectDubboVew>> selectCustomerBySalerId(String salerId) {
        return null;
    }

    @Override
    public DubboResult<List<CustomerProtectDubboVew>> selectByCondition(CustomerProtectDubboDto customerProtectDubboDto) {
        return null;
    }

    @Override
    public DubboResult<Integer> updateById(CustomerProtectDubboDto customerProtectDubboDto) {
        return null;
    }

    @Override
    public DubboResult<Integer> updateByCustomerId(CustomerProtectDubboDto customerProtectDubboDto) {
        return null;
    }

    @Override
    public DubboResult<Integer> deleteByCustomerId(String customerId) {
        return null;
    }
}
