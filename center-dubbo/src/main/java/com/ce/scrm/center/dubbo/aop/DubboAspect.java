package com.ce.scrm.center.dubbo.aop;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.support.log.LogObject;
import com.ce.scrm.center.util.constant.UtilConstant;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import org.apache.dubbo.rpc.RpcContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * dubbo业务切面
 * <AUTHOR>
 * @date 2023/4/6 20:48
 * @version 1.0.0
 **/
@Order(2)
@Aspect
@Component
public class DubboAspect {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(DubboAspect.class);

    /**
     * 执行成功消息
     */
    private final static String EXECUTE_SUCCESS = "success";

    /**
     * 定义cat监控扫描路径
     * 定义切点
     * <AUTHOR>
     * @date 2023/4/6 20:48
     **/
    @Pointcut("execution(public * com.ce.scrm.center.dubbo.service.*.*(..))")
    public void requestConfigPointCut() {
    }

    /**
     * 对于监控到的方法进行监控增强处理
     * 定义环绕类型的Advise（增强器）
     * @param joinPoint 切面连接点（被代理方法的相关封装）
     * <AUTHOR>
     * @date 2023/4/6 20:48
     * @return java.lang.Object
     **/
    @Around("requestConfigPointCut()")
    public Object around(ProceedingJoinPoint joinPoint) {
        //添加cat监控
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Transaction t = Cat.newTransaction("SERVICE", className + UtilConstant.CAT_SEPARATOR + methodName);
        t.setStatus(Transaction.SUCCESS);
        Object proceed = null;
        LogObject logObject = new LogObject();
        logObject.setInvokerName(RpcContext.getContext().getRemoteApplicationName())
                .setInvokerIp(RpcContext.getContext().getRemoteAddressString())
                .setEventName(className + UtilConstant.DATA_SEPARATOR + methodName)
                .setTraceId(RpcContext.getContext().getAttachment(UtilConstant.Mdc.REQUEST_ID_NAME))
                .setRequest(joinPoint.getArgs());
        String msg = EXECUTE_SUCCESS;
        try {
            //执行方法
            proceed = joinPoint.proceed();
        } catch (Throwable e) {
            DubboResult<Object> dubboResult = DubboResult.error(DubboCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
            msg = e.getMessage();
            t.setStatus(e);
            Cat.logError(e);
            LOGGER.error(className + UtilConstant.DATA_SEPARATOR + methodName + "方法异常:", e);
            //全局异常捕获
            return (proceed = dubboResult);
        } finally {
            if (proceed instanceof DubboResult) {
                DubboResult<Object> resultEntity = (DubboResult<Object>) proceed;
                String traceId = MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME);
                resultEntity.setTraceId(traceId);
                if (!resultEntity.checkSuccess()) {
                    logObject.setResponse(JSON.toJSONString(proceed));
                }
            }
            t.complete();
            logObject.setMsg(msg).setCostTime(System.currentTimeMillis() - Long.parseLong(MDC.get(UtilConstant.Mdc.START_TIME)));
            LOGGER.info(JSON.toJSONString(logObject));
        }
        return proceed;
    }
}
