package com.ce.scrm.center.dubbo.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.GcBusinessOpportunity;
import com.ce.scrm.center.dao.service.GcBusinessOpportunityService;
import com.ce.scrm.center.dubbo.api.GcBusinessOpportunityDubbo;
import com.ce.scrm.center.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.center.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.view.GcBusinessOpportunityDubboView;
import com.ce.scrm.center.dubbo.entity.view.GcBusinessOpportunityOperationRecordView;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 高呈商机dubbo实现
 * <AUTHOR>
 * @date 2024/5/15 上午11:41
 * @version 1.0.0
 */
@Slf4j
@DubboService
public class GcBusinessOpportunityDubboService implements GcBusinessOpportunityDubbo {

    @Resource
    private GcBusinessOpportunityService gcBusinessOpportunityService;

    /**
     * 高呈商机客户分页列表
     * @param gcBusinessOpportunityPageDubboDto 筛选参数，只需传queryStr客户名称参数
     * <AUTHOR>
     * @date 2024/5/20 10:41
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.response.DubboPageInfo < com.ce.scrm.center.dubbo.entity.view.GcBusinessOpportunityDubboView>>
     */
    @Override
    public DubboResult<DubboPageInfo<GcBusinessOpportunityDubboView>> getCustPageList(GcBusinessOpportunityPageDubboDto gcBusinessOpportunityPageDubboDto) {
        if (gcBusinessOpportunityPageDubboDto == null) {
            log.warn("高呈商机客户分页列表，参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.PARAM_EMPTY);
        }
        String gc_saler_id = "2";       //todo 确定高呈大客户经理的empId
        Page<GcBusinessOpportunity> page = Page.of(gcBusinessOpportunityPageDubboDto.getPageNum(), gcBusinessOpportunityPageDubboDto.getPageSize());
        LambdaQueryChainWrapper<GcBusinessOpportunity> lambdaQueryChainWrapper = gcBusinessOpportunityService.lambdaQuery()
                .select(GcBusinessOpportunity::getCustomerId,
                        GcBusinessOpportunity::getCustomerName,
                        GcBusinessOpportunity::getSubName,
                        GcBusinessOpportunity::getSalerName)
                .eq(GcBusinessOpportunity::getGcSalerId, gc_saler_id)
                .eq(GcBusinessOpportunity::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                ;
        if (StrUtil.isNotBlank(gcBusinessOpportunityPageDubboDto.getQueryStr())) {
            lambdaQueryChainWrapper.and(wrapper -> wrapper.like(GcBusinessOpportunity::getCustomerName, gcBusinessOpportunityPageDubboDto.getQueryStr()));
        }
        lambdaQueryChainWrapper
                .groupBy(GcBusinessOpportunity::getCustomerId)
                .orderByDesc(GcBusinessOpportunity::getUpdateTime)
        ;

        Page<GcBusinessOpportunity> gcBusinessOpportunityPage = lambdaQueryChainWrapper.page(page);
        DubboPageInfo<GcBusinessOpportunityDubboView> dubboViewDubboPageInfo = CglibUtil.copy(gcBusinessOpportunityPage, DubboPageInfo.class);
        if (dubboViewDubboPageInfo.getTotal() < 1) {
            return DubboResult.success(dubboViewDubboPageInfo);
        }
        List<GcBusinessOpportunity> gcBusinessOpportunityList = gcBusinessOpportunityPage.getRecords();
        dubboViewDubboPageInfo.setList(CglibUtil.copyList(gcBusinessOpportunityList, GcBusinessOpportunityDubboView::new));
        return DubboResult.success(dubboViewDubboPageInfo);
    }

    /**
     * 获取高呈商机客户的项目列表
     * @param dubboDto 客户ID必传
     * <AUTHOR>
     * @date 2024/5/20 12:00
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.response.DubboPageInfo < com.ce.scrm.center.dubbo.entity.view.GcBusinessOpportunityDubboView>>
     */
    @Override
    public DubboResult<DubboPageInfo<GcBusinessOpportunityDubboView>> custProjectPageList(GcBusinessOpportunityPageDubboDto dubboDto) {
        String customerId = dubboDto.getCustomerId();
        if (StrUtil.isBlank(customerId)) {
            log.warn("获取高呈商机客户的项目列表，客户ID不能为空");
            return DubboResult.error(DubboCodeMessageEnum.PARAM_EMPTY);
        }
        Page<GcBusinessOpportunity> page = Page.of(dubboDto.getPageNum(), dubboDto.getPageSize());
        LambdaQueryChainWrapper<GcBusinessOpportunity> lambdaQueryChainWrapper = gcBusinessOpportunityService.lambdaQuery()
                .select(GcBusinessOpportunity::getId,
                        GcBusinessOpportunity::getCustomerId,
                        GcBusinessOpportunity::getCustomerName,
                        GcBusinessOpportunity::getRequirementType,  // 项目类型
                        GcBusinessOpportunity::getState,            // 高呈商机状态，商机状态与来源有关
                        GcBusinessOpportunity::getGcFollowState,    // 高呈跟进状态
                        GcBusinessOpportunity::getSource,           // 来源
                        GcBusinessOpportunity::getLinkmanName,
                        GcBusinessOpportunity::getLinkmanPhone,
                        GcBusinessOpportunity::getLinkmanEmail,
                        GcBusinessOpportunity::getLinkmanEmail,
                        GcBusinessOpportunity::getAreaId,           //提单 区域ID
                        GcBusinessOpportunity::getAreaName,         //提单 区域名称
                        GcBusinessOpportunity::getSubId,            //提单 分司ID
                        GcBusinessOpportunity::getSubName,          //提单 分司名称
                        GcBusinessOpportunity::getDeptId,           //提单 部门ID
                        GcBusinessOpportunity::getDeptName,         //提单 部门名称
                        GcBusinessOpportunity::getSalerId,          //提单 商务ID
                        GcBusinessOpportunity::getSalerName)        //提单 商务名称
                .eq(GcBusinessOpportunity::getCustomerId, customerId)
                .eq(GcBusinessOpportunity::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .orderByDesc(GcBusinessOpportunity::getUpdateTime);

        Page<GcBusinessOpportunity> gcBusinessOpportunityPage = lambdaQueryChainWrapper.page(page);
        DubboPageInfo<GcBusinessOpportunityDubboView> dubboViewDubboPageInfo = CglibUtil.copy(gcBusinessOpportunityPage, DubboPageInfo.class);
        if (dubboViewDubboPageInfo.getTotal() < 1) {
            return DubboResult.success(dubboViewDubboPageInfo);
        }
        List<GcBusinessOpportunity> gcBusinessOpportunityList = gcBusinessOpportunityPage.getRecords();
        dubboViewDubboPageInfo.setList(CglibUtil.copyList(gcBusinessOpportunityList, GcBusinessOpportunityDubboView::new));
        return DubboResult.success(dubboViewDubboPageInfo);
    }

    /**
     * 获取高呈商机客户的项目详情
     * @param sjId 商机ID
     * <AUTHOR>
     * @date 2024/5/20 13:40
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.response.DubboPageInfo < com.ce.scrm.center.dubbo.entity.view.GcBusinessOpportunityDubboView>>
     */
    @Override
    public DubboResult<GcBusinessOpportunityDubboView> getCustProjectDetail(String sjId) {
        if (StrUtil.isBlank(sjId)) {
            log.warn("获取高呈商机客户的项目详情，商机ID不能为空");
            return DubboResult.error(DubboCodeMessageEnum.PARAM_EMPTY);
        }
        LambdaQueryWrapper<GcBusinessOpportunity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GcBusinessOpportunity::getId, sjId);
        GcBusinessOpportunity gcBusinessOpportunity = gcBusinessOpportunityService.getOne(queryWrapper);
        if (gcBusinessOpportunity == null) {
            log.warn("获取高呈商机客户的项目详情，商机不存在，商机ID为:{}", sjId);
            return DubboResult.success(null);
        }
        //TODO attachment_ids '附件ID（多个使用,分隔）',    根据id调国强的接口返回，而不是用 t_sj_to_omp_file
        return DubboResult.success(CglibUtil.copy(gcBusinessOpportunity, GcBusinessOpportunityDubboView.class));
    }

    /**
     * 获取高呈商机客户的项目的操作记录列表
     * @param sjId 商机ID
     * <AUTHOR>
     * @date 2024/5/20 13:58
     * //TODO 待定 取数来源表    2024年5月20日：待确定要不要创建一张新的表，老表为 scrm_extend.sj_to_omp_log
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.response.DubboPageInfo < com.ce.scrm.center.dubbo.entity.view.GcBusinessOpportunityDubboView>>
     */
    @Override
    public DubboResult<List<GcBusinessOpportunityOperationRecordView>> getCustProjectOperationRecordList(String sjId) {
        if (StrUtil.isBlank(sjId)) {
            log.warn("获取高呈商机客户的项目详情，商机ID不能为空");
            return DubboResult.error(DubboCodeMessageEnum.PARAM_EMPTY);
        }
        //TODO 取数来源表
        LambdaQueryWrapper<GcBusinessOpportunity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GcBusinessOpportunity::getId, sjId);
        //TODO 待定 取数来源表
        List<GcBusinessOpportunity> gcBusinessOpportunity = gcBusinessOpportunityService.list(queryWrapper);
        if (CollectionUtil.isEmpty(gcBusinessOpportunity)) {
            log.warn("获取高呈商机客户的项目操作记录列表不存在，商机ID为:{}", sjId);
            return DubboResult.success(null);
        }
        //TODO 待定 取数来源表
        return DubboResult.success(null);
    }
}