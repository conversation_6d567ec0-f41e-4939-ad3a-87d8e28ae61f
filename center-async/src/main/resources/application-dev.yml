spring:
  datasource:
    url: ***************************************************************************************************************************************************************************************************
    username: MYS<PERSON>(hs930YRVa7tBBmikUYezNw==)
    password: MYSQL(06NOLLrj3J7RK3LoWUJ5nczQkaXzG0MZ)
  redis:
    password: mpRedis@019
    cluster:
      nodes: redis.ep:7000,redis.ep:7001,redis.ep:7002

dubbo:
  registry:
    address: ************:2181,************:2181,************:2181

sequence:
  zkAddress: ************
  zkPort: 2181

xxl:
  job:
    accessToken:
    admin:
      #调度中心地址
      addresses: http://pre-omo.aiyouyi.cn/xxl-job-admin/
    executor:
      appname: scrm-center-job-pre
      address:
      ip:
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 3
      oneTimesJob:
        timeout: 10000

rocketmq:
  name-server: mq.ep:9876
  producer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)
  consumer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)

nacos:
  config:
    server-addr: common.et:8848
    username: NACOS(oWUVxx3OeK0iuxxdbVU4Hw==)
    password: NACOS(H9klTNl9ZJoALwkWQzmeST4Wx7eEnHUiDC7BdC8YfWU=)
    namespace: pre

logging:
  config: classpath:logback-dev.xml

robot:
  force-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d199f33a-949e-4370-8eee-c573a51d185e

bigdata:
  enterpriseInfo: https://gateway.datauns.cn/ce22highlevelsearch/highSearch/baseinfo/company_detail?company_name=

third:
  customer:
    key: scrm
    secret: 31b9763b659c49489d348c2e6a261a64
  file:
    url: http://test-cesupport-images.ceboss.cn/upload

wx:
  isopen: 1

mq:
  prefix: TEST_

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

couponReleaseUrl: https://test-api-support.ceboss.cn/
sign:
  privateKey: JKUQ45qenW

sensors:
  #projectId: 3
  projectId: 2

file:
  upload:
    dir: /data/share/www/offline/upload
    url: http://test-cesupport-images.ceboss.cn/upload