<?xml version="1.0" encoding="UTF-8" ?>
<configuration>

    <property name="logPath" value="/data/appcenter/logs/scrm-center-async"/>
    <property name="file_log_pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%t] %C.%M:%L - [traceId:%X{traceId}] %m%n"/>

    <appender name="fileLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logPath}/scrm-center-async.log</file>
        <encoder>
            <pattern>${file_log_pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!--滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--路径-->
            <fileNamePattern>${logPath}/scrm-center-async.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>100MB</maxFileSize>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>
    <appender name="sendErrorMsgAppender" class="com.ce.scrm.center.support.message.SendErrorMsgAppender"/>
    <!--dubbo日志输出级别为info-->
    <logger name="cn.apache.dubbo" level="error" additivity="false">
        <appender-ref ref="fileLog"/>
    </logger>
    <logger name="org.apache.rocketmq" level="error" additivity="false">
        <appender-ref ref="fileLog"/>
    </logger>
    <logger name="org.apache.zookeeper" level="error" additivity="false">
        <appender-ref ref="fileLog"/>
    </logger>
    <logger name="cn.ce.common.redis.switcher" level="warn" additivity="false">
        <appender-ref ref="fileLog"/>
    </logger>
    <root level="INFO">
        <appender-ref ref="fileLog"/>
        <appender-ref ref="sendErrorMsgAppender"/>
    </root>
</configuration>