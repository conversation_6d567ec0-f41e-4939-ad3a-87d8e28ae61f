spring:
  datasource:
    url: ***************************************************************************************************************************************************************************************************
    username: MYS<PERSON>(hs930YRVa7tBBmikUYezNw==)
    password: MYSQL(06NOLLrj3J7RK3LoWUJ5nczQkaXzG0MZ)
  redis:
    password: mpRedis@019
    cluster:
      nodes: redis.ep:7000,redis.ep:7001,redis.ep:7002

dubbo:
  registry:
    address: ************:2181,************:2181,************:2181

sequence:
  zkAddress: ************
  zkPort: 2181

xxl:
  job:
    accessToken:
    admin:
      #调度中心地址
      addresses: http://pre-omo.aiyouyi.cn/xxl-job-admin/
    executor:
      appname: scrm-center-job-pre
      address:
      ip:
      port: 7458
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 3
      oneTimesJob:
        timeout: 10000

rocketmq:
  name-server: mq.ep:9876
  producer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)
  consumer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)

nacos:
  config:
    server-addr: common.et:8848
    username: NACOS(oWUVxx3OeK0iuxxdbVU4Hw==)
    password: NACOS(H9klTNl9ZJoALwkWQzmeST4Wx7eEnHUiDC7BdC8YfWU=)
    namespace: pre

logging:
  config: classpath:logback-pre.xml
  level:
    com.ce.scrm.center.dao.mapper: DEBUG
    org.mybatis: DEBUG
    java.sql: DEBUG
robot:
  force-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d199f33a-949e-4370-8eee-c573a51d185e

bigdata:
  enterpriseInfo: https://gateway.datauns.cn/ce22highlevelsearch/highSearch/baseinfo/company_detail?company_name=

third:
  customer:
    key: scrm
    secret: 31b9763b659c49489d348c2e6a261a64
  file:
    url: http://test-cesupport-images.ceboss.cn/upload

wx:
  isopen: 1

mq:
  prefix: TEST_
mybatis-plus:
  mapper-locations: classpath:mapper/*Mapper.xml
  type-aliases-package: com.ce.scrm.center.dao.entity
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
couponReleaseUrl: https://test-api-support.ceboss.cn/security/couponGiveRecord/saveCouponRecordNotLogin
#易企秀
ecmp:
  signatureKey: ktWVfoqNCSxEQU47
  encodingKey: og9pVN52dCeCXoEFx0sTbXTamcVkhx7SMi2LX1MoLwe
  secretKey: qOdqk33y8F6mMankOrEUl5Vh3WUQM5io
  secretId: 5222YVS
sign:
  privateKey: JKUQ45qenW

sensors:
  #projectId: 3
  projectId: 2

file:
  upload:
    dir: /data/share/www/offline/upload
    url: http://test-cesupport-images.ceboss.cn/upload
pptNotify: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cc8dc04a-1206-448f-aa7e-5d5329318724
viewUrl: https://test-cescrm.ceboss.cn/ftree_scrm_xsmanager/ftree_scrm_khgl/ftree_scrm_audiolist/intelligent/audio-list
voiceNotifyUrl: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7a53f019-a62b-40f6-baa4-f8aaefe83eeb


#企微保护列表
abm:
  protectionListUrl: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wwf1c477c862181a7f&redirect_uri=https%3A%2F%2Ftest-ewx-crm.ceboss.cn%2Fwecom-scrm%2Ffollow-client%2Flist&response_type=code&scope=snsapi_base&state=N2Wsxr#wechat_redirect