server:
  port: 8080

spring:
  datasource:
    url: *******************************************************************************************************************************************************************************************************************************
    username: MYS<PERSON>(uULLmQ1th6LHecllz29tzA==)
    password: MYSQL(0n5QcDspnsYEuPS+y8IC3pOulEwb6oxu)
  redis:
    password: dfdr3r44DwsdCK
    cluster:
      nodes: uc-hb2-scrm-rds-node1.online.local:7000,uc-hb2-scrm-rds-node2.online.local:7000,uc-hb2-scrm-rds-node3.online.local:7000,uc-hb2-scrm-rds-node4.online.local:7000,uc-hb2-scrm-rds-node5.online.local:7000,uc-hb2-scrm-rds-node6.online.local:7000

dubbo:
  registry:
    address: zookeeper://uc-hb2-pt-zk-node01.plat.cn:2181,uc-hb2-pt-zk-node02.plat.cn:2181,uc-hb2-pt-zk-node03.plat.cn:2181

sequence:
  zkAddress: uc-hb2-pt-zk-node01.plat.cn
  zkPort: 2181

xxl:
  job:
    accessToken:
    admin:
      #调度中心地址
      addresses: http://bj-zw-xds-ds-api-omo.online.local/xxl-job-admin
    executor:
      appname: scrm-center-job-release
      address:
      ip:
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 3
      port: 9472
      oneTimesJob:
        timeout: 10000

rocketmq:
  name-server: uc-hb2-gg-mq-node1.online.local:9876;uc-hb2-gg-mq-node2.online.local:9876
  producer:
    accessKey: MQ(mCy3PT7me91t5vZGM4ivcLHa8ZT/xQ8M)
    secretKey: MQ(hCCdvFpmV7F61ujvcEiv8qmrRUrnQW349VkN4XWM7Qw=)
  consumer:
    accessKey: MQ(mCy3PT7me91t5vZGM4ivcLHa8ZT/xQ8M)
    secretKey: MQ(hCCdvFpmV7F61ujvcEiv8qmrRUrnQW349VkN4XWM7Qw=)

nacos:
  config:
    server-addr: uc-hb2-ds-ncs-node1.online.local:8080,uc-hb2-ds-ncs-node2.online.local:8080,uc-hb2-ds-ncs-node3.online.local:8080 # nacos服务器列表
    namespace: scrm
    username: NACOS(6C2r4kfn2z+0eSp1eXcy0A==)
    password: NACOS(T8U1r9y86VymGuHppeeot0pNPbWuFJP2P2WufBK4nOc=)

logging:
  config: classpath:logback-release.xml

robot:
  force-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=57f8697b-5fc9-4857-84b4-ca395144ccdf
  weak-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e0dc48ae-97f7-4de2-afad-2a5e5a2b8008

bigdata:
  enterpriseInfo: http://local-gateway.datauns.cn/ce22highlevelsearch/highSearch/baseinfo/company_detail?company_name=


third:
  customer:
    key: scrm
    secret: 9082a38bcaf04fb6adcf4823dbc80f21
  file:
    url: http://cesupport-images.ceboss.cn

wx:
  isopen: 1

mq:
  prefix: ""

couponReleaseUrl: https://api-support.ceboss.cn/security/couponGiveRecord/saveCouponRecordNotLogin
#易企秀
ecmp:
  signatureKey: ktWVfoqNCSxEQU47
  encodingKey: og9pVN52dCeCXoEFx0sTbXTamcVkhx7SMi2LX1MoLwe
  secretKey: qOdqk33y8F6mMankOrEUl5Vh3WUQM5io
  secretId: 5222YVS
sign:
  privateKey: iBTioEAzgch9jcn6pTGB

sensors:
  #projectId: 3
  projectId: 2

file:
  upload:
    dir: /usr/ftp_home/cesupport
    url: http://cesupport-images.ceboss.cn
pptNotify: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ae26e2c3-192c-4cb6-8b9e-5d9ae71f5812
viewUrl: https://cescrm.ceboss.cn/ftree_scrm_xsmanager/ftree_scrm_khgl/ftree_scrm_audiolist/intelligent/audio-list
voiceNotifyUrl: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9fd18da0-8795-491a-9372-8f6676dcbf7f

#企微保护列表
abm:
  protectionListUrl: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wwf1c477c862181a7f&redirect_uri=https%3A%2F%2Fewx-crm.ceboss.cn%2Fwecom-scrm%2Ffollow-client%2Flist&response_type=code&scope=snsapi_base&state=N2Wsxr#wechat_redirect
