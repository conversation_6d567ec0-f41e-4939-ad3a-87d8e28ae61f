package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.service.SegmentDetailService;
import com.ce.scrm.center.dao.service.SegmentService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.ProtectCustTypeEnum;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.Set;

/**
 * 监听保护关系binlog 同步到客户分群信息表
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CM_CUST_PROTECT_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_CUSTOMER_GROUP_PROTECTED_INFO_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class SegmentCustomerProtectInfoConsumer extends AbstractBinlogConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private SegmentDetailService segmentDetailService;

    @Resource
    private SegmentService segmentService;

    @Override
    public void onMessage(MessageExt messageExt) {
        super.dealMqMsg(messageExt);
    }

    /**
     * protect_status      保护状态
     * deal_status         成交客户状态 0:未成交 1:已成交
     * protect_time        首次保护时间，初始为空,不为空的时候不能更新，保护关系表 变成1的时候，取保护时间
     * visit_last_time     最近一次拜访时间,初始为空，地理打卡表binlog
     * sign_first_time     首次签单时间，初始为空，不为空的时候不能更新，保护关系表 cust_type 变成3或4 取首次签单时间
     *
     * @param topic
     * @param msgId
     * @param after
     */
    @Override
    public void dealInsertEvent(String topic, String msgId, JSONObject after) {
        String customerId = after.getString("CUST_ID");
        String salerId = after.getString("SALER_ID");
        this.dealEvent(topic, msgId, customerId, salerId, after, after);
    }

    @Override
    public void dealUpdateEvent(String topic, String msgId, JSONObject before, JSONObject after) {
        String customerId = after.getString("CUST_ID");
        String salerId = after.getString("SALER_ID");
        if (StringUtils.isBlank(salerId)) {
            salerId = before.getString("SALER_ID");
        }
        this.dealEvent(topic, msgId, customerId, salerId, before, after);
    }

    public void dealEvent(String topic, String msgId, String customerId, String salerId, JSONObject before, JSONObject after) {
        if (before.containsKey("STATUS") && after.containsKey("STATUS") && after.getIntValue("STATUS") == ProtectStateEnum.PROTECT.getState()) {
            Date protectTime = after.getDate("PROTECT_TIME");
            this.setProtectTime(topic, msgId, customerId, salerId, protectTime);

        } else if (before.containsKey("CUST_TYPE") && after.containsKey("CUST_TYPE")
                && (after.getIntValue("CUST_TYPE") == ProtectCustTypeEnum.ORDERED.getValue()
                || after.getIntValue("CUST_TYPE") == ProtectCustTypeEnum.SECOND_DEVELOPMENT.getValue())) {
            Date lastSignTime = after.getDate("db_update_time");
            this.setLastSignTime(topic, msgId, customerId, salerId, lastSignTime);
        }
    }

    /**
     * 设置保护时间
     *
     * @param topic
     * @param msgId
     * @param customerId
     * @param salerId
     * @param protectTime
     * @return
     */
    private boolean setProtectTime(String topic, String msgId, String customerId, String salerId, Date protectTime) {
        if (StringUtils.isBlank(salerId) || StringUtils.isBlank(customerId) || Objects.isNull(protectTime)) {
            log.error("customerId|salerId|protectTime为空，topic={}, msgId={}", topic, msgId);
            return false;
        }
        Set<String> validSegmentIds = segmentService.getValidSegmentIds(protectTime);
        if (CollectionUtils.isEmpty(validSegmentIds)) {
            log.warn("获取的有效分群信息为空，topic={}, msgId={}, protectTime={}", topic, msgId, protectTime);
            return false;
        }
        return segmentDetailService.setProtectTime(validSegmentIds, customerId, salerId, protectTime);
    }

    /**
     * 设置签约时间
     *
     * @param topic
     * @param msgId
     * @param customerId
     * @param salerId
     * @param lastSignTime
     * @return
     */
    private boolean setLastSignTime(String topic, String msgId, String customerId, String salerId, Date lastSignTime) {
        if (StringUtils.isBlank(salerId) || StringUtils.isBlank(customerId) || Objects.isNull(lastSignTime)) {
            log.error("customerId|salerId|lastSignTime为空，topic={}, msgId={}", topic, msgId);
            return false;
        }
        Set<String> validSegmentIds = segmentService.getValidSegmentIds(lastSignTime);
        if (CollectionUtils.isEmpty(validSegmentIds)) {
            log.warn("获取的有效分群信息为空，topic={}, msgId={}, lastSignTime={}", topic, msgId, lastSignTime);
            return false;
        }
        return segmentDetailService.setLastSignTime(validSegmentIds, customerId, salerId, lastSignTime);
    }

    @Override
    protected void dealDeleteEvent(String topic, String msgId, JSONObject after) {
        log.info("DELETE类型暂不处理! topic={} msgId {}", topic, msgId);
    }

}
