package com.ce.scrm.center.async.util;

import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.TreeMap;

/**
 * @Author: JCccc
 * @CreateTime: 2018-10-30
 * @Description:
 */
public class CouponSignUtils {

    public static String getTimestamp() {
        //生成时间戳
        long timestampLong = System.currentTimeMillis();
        return String.valueOf(timestampLong);
    }

    //类似微信接口的签名生成方法
    public static String createSign(Map<String, Object> params, String privateKey) {
        StringBuilder sb = new StringBuilder();
        // 将参数以参数名的字典升序排序
        Map<String, Object> sortParams = new TreeMap<>(params);
        // 遍历排序的字典,并拼接"key=value"格式
        for (Map.Entry<String, Object> entry : sortParams.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue().toString().trim();
            if (!StringUtils.isEmpty(value))
                sb.append("&").append(key).append("=").append(value);
        }
        String stringA = sb.toString().replaceFirst("&", "");
        String stringSignTemp = stringA + "&" + "appkey=" + privateKey;
        //将签名使用MD5加密并全部字母变为大写
        return DigestUtils.md5DigestAsHex(stringSignTemp.getBytes()).toUpperCase();
    }


}