package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dao.entity.Segment;
import com.ce.scrm.center.dao.service.SegmentService;
import com.ce.scrm.center.service.business.SegmentBusiness;
import com.ce.scrm.center.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 分群下发消费者
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_GROUP_CUSTOMER_DISTRIBUTE_STATUS_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.CDP_GROUP_CUSTOMER_DISTRIBUTE_STATUS_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class SegmentEndCustomerConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private SegmentBusiness segmentBusiness;

    @Resource
    private SegmentService segmentService;

    @Override
    public void onMessage(MessageExt messageExt) {
        String messageId = messageExt.getMsgId();
        String message = new String(messageExt.getBody());
        if (StringUtils.isBlank(message)) {
            return;
        }
        log.info("CDP_GROUP_CUSTOMER_DISTRIBUTE_STATUS_TOPIC，msgId={},消息内容：{}", messageId, message);
        try {
            Segment segment = JSON.parseObject(message, Segment.class);
            if (Objects.nonNull(segment) && StringUtils.isNotBlank(segment.getSegmentId())) {
                Segment detail = segmentBusiness.detail(segment.getSegmentId());
                if (detail!=null){
                    detail.setSegmentStatus(2);
                    segmentService.updateById(detail);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
