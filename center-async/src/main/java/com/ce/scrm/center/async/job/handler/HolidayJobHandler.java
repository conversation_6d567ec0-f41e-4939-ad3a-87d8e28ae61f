package com.ce.scrm.center.async.job.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.HolidayInfo;
import com.ce.scrm.center.dao.service.HolidayInfoService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Description: 每年获取一次节假日入Mysql库
 * @Author: lijinpeng
 * @Date: 2024/10/8 11:04
 */
@Slf4j
@Component
public class HolidayJobHandler {

    @Resource
    private HolidayInfoService holidayInfoService;

    @Resource
    private RestTemplate restTemplate;

    /*
     * @Description 每年节假日获取入mysql
     * <AUTHOR>
     * @date 2024/10/8 11:25
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_GET_HOLIDAY_JOB_HANDLER)
    public ReturnT<String> getHoliday(String param) {
        log.info("===========节假日获取开始==========");

        String baseUrl = "http://timor.tech/api/holiday/year/";
        String year = param;

        // 构造 URL 带参数
        URI uri = UriComponentsBuilder.fromHttpUrl(baseUrl)
                .pathSegment(year)
                .queryParam("week", "Y")
                .build()
                .toUri();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
        headers.add("Accept", "*/*");
        headers.add("Connection", "keep-alive");
        headers.add("Content-Type", "application/json;charset=UTF-8");


        //发起请求,直接返回对象（带参数请求）
        HttpEntity<String> entity = new HttpEntity<>(null, headers);
        log.info("节假日请求开始，year参数为：{}",year);
        ResponseEntity<String> response = restTemplate.exchange(uri, HttpMethod.GET, entity, String.class);
        try {
            String body = response.getBody();
            JSONObject jsonObject = JSON.parseObject(body);
            Map<String, Object> holidayMap = jsonObject.getJSONObject("holiday");
            List<HolidayInfo> holidayInfos = new ArrayList<>();
            holidayMap.forEach((key, value) -> {
                HolidayInfo holidayInfo = JSON.parseObject(value.toString(), HolidayInfo.class);
                if(holidayInfo.getHoliday().equals(1)) {
                    holidayInfos.add(holidayInfo);
                }
            });
            holidayInfoService.saveBatch(holidayInfos);
        }catch (Exception e){
            log.error("节假日获取结果失败，message:{}",e.getMessage());
        }

        log.info("===========节假日获取结束==========");
        return ReturnT.SUCCESS;
    }



}
