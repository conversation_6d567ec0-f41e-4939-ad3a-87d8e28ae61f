
package com.ce.scrm.center.async.mq.consumer;

import cn.ce.cesupport.enums.SalesStageEnum;
import cn.ce.cesupport.framework.base.vo.EmployeeVO;
import cn.ce.cesupport.framework.base.vo.MapResultBean;
import cn.ce.cesupport.sma.service.CustomerFollowAppService;
import cn.ce.cesupport.sma.vo.CmCustVisitLogVo;
import cn.ce.cesupport.sma.vo.CustomerFollowVo;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.AiVoiceAnalyze;
import com.ce.scrm.center.dao.service.AiVoiceAnalyzeService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.extend.dubbo.enums.VisitTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

import static com.ce.scrm.center.async.mq.consumer.AiTencentcloudapiConsumer.genjinneirong;
import static com.ce.scrm.center.async.mq.consumer.AiTencentcloudapiConsumer.xiaoshoujieduan;

@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_VOICE_AI_NOTIFY_FOLLOW_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_VOICE_AI_NOTIFY_FOLLOW_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 10)
public class AiFollowAnalyzeConsumer implements RocketMQListener<MessageExt> {


    @Autowired
    private AiVoiceAnalyzeService aiVoiceAnalyzeService;

    @DubboReference
    private CustomerFollowAppService customerFollowAppService;

    @Autowired
    private CustomerThirdService customerThirdService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Autowired
    private OrgThirdService orgThirdService;

    @Override
    public void onMessage(MessageExt messageExt) {
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            return;
        }
        try {
            JSONObject jsonObject = JSONObject.parseObject(data);
            Long id = jsonObject.getLong("id");
            AiVoiceAnalyze aiVoiceAnalyze = aiVoiceAnalyzeService.getById(id);
            JSONObject jsonObjectRes = JSONObject.parseObject(aiVoiceAnalyze.getBusinessChatResponse());
            String analyze = jsonObjectRes.getString(genjinneirong);
            String salesStage = jsonObjectRes.getString(xiaoshoujieduan);
            String extralInfo = aiVoiceAnalyze.getExtralInfo();
            CustomerFollowVo customerFollowVo = null;
            CmCustVisitLogVo cmCustVisitLogVo = new CmCustVisitLogVo();
            if (StringUtils.isNotBlank(extralInfo)) {
                JSONObject extralInfoJson = JSONObject.parseObject(extralInfo);
                cmCustVisitLogVo = JSONObject.parseObject(JSONObject.toJSONString(extralInfoJson), CmCustVisitLogVo.class);
            }
            EmployeeDataThirdView employeeInfoThirdDto = employeeThirdService.getEmployeeData(aiVoiceAnalyze.getEmployeeId()).orElse(new EmployeeDataThirdView());
            log.info("请求获取员工信息结果:{}", JSONObject.toJSONString(employeeInfoThirdDto));
            EmployeeVO employeeVO = new EmployeeVO();
            BeanUtils.copyProperties(employeeInfoThirdDto, employeeVO);
            MapResultBean mapResultBean = customerFollowAppService.getCustomerFollowInfo(aiVoiceAnalyze.getCustId(), employeeVO);
            log.info("请求获取历史跟进记录:{}", JSONObject.toJSONString(mapResultBean));
            if (null != mapResultBean.getData() && mapResultBean.getStatus() == 101) {
                Map<String, Object> map = mapResultBean.getData();
                if (CollectionUtil.isNotEmpty(map) && null != map.get("data")) {
                    String dataValue = JSONObject.toJSONString(map.get("data"));
                    log.info("获取到 data:{}", dataValue);
                    customerFollowVo = JSONObject.parseObject(dataValue, CustomerFollowVo.class);
                    CmCustVisitLogVo custVisitLogVo = new CmCustVisitLogVo();
                    custVisitLogVo.set_id(null);
                    custVisitLogVo.setIsAutoFlag(1);
                    custVisitLogVo.setLinkManMobile(cmCustVisitLogVo.getLinkManMobile());
                    custVisitLogVo.setLinkManName(cmCustVisitLogVo.getLinkManName());
                    custVisitLogVo.setLinkManPostion(cmCustVisitLogVo.getLinkManPostion());
                    custVisitLogVo.setContent(analyze);
                    if (StringUtils.isBlank(customerFollowVo.getSalesStage()) || null == getById(customerFollowVo.getSalesStage())) {
                        SalesStageEnum salesStageEnum = getByName(salesStage);
                        custVisitLogVo.setSalesStage(salesStageEnum.getId());
                        custVisitLogVo.setSalesStageStr(salesStageEnum.getName());
                    } else {
                        custVisitLogVo.setSalesStage(customerFollowVo.getSalesStage());
                        custVisitLogVo.setSalesStageStr(getById(customerFollowVo.getSalesStage()).getName());
                    }
                    custVisitLogVo.setVisitType(VisitTypeEnum.VISIT.getValue());
                    custVisitLogVo.setVisitTypeLable(VisitTypeEnum.VISIT.getLable());
                    custVisitLogVo.setCustId(customerFollowVo.getCustomerId());
                    custVisitLogVo.setCustName(customerFollowVo.getCustomerName());
                    customerFollowVo.setCmCustVisitLogVo(custVisitLogVo);
                    customerFollowVo.setDeleteFlag(null);
                }
            }
            if (null == customerFollowVo) {
                customerFollowVo = new CustomerFollowVo();
                cmCustVisitLogVo.setContent(analyze);
                SalesStageEnum salesStageEnum = getByName(salesStage);
                cmCustVisitLogVo.setSalesStage(salesStageEnum.getId());
                cmCustVisitLogVo.setSalesStageStr(salesStageEnum.getName());
                cmCustVisitLogVo.setVisitType(VisitTypeEnum.VISIT.getValue());
                cmCustVisitLogVo.setVisitTypeLable(VisitTypeEnum.VISIT.getLable());
                cmCustVisitLogVo.setIsAutoFlag(1);
                cmCustVisitLogVo.setCustId(aiVoiceAnalyze.getCustId());
                customerFollowVo.setCmCustVisitLogVo(cmCustVisitLogVo);
                log.info("请求获取员工信息:{}", aiVoiceAnalyze.getEmployeeId());
            }
            try {
                if (StringUtils.isBlank(customerFollowVo.getCustomerId()) || StringUtils.isBlank(customerFollowVo.getCustomerName())) {
                    customerFollowVo.setCustomerId(aiVoiceAnalyze.getCustId());
                    CustomerDataThirdView customerDataThirdView = customerThirdService.getCustomerData(aiVoiceAnalyze.getCustId()).orElse(new CustomerDataThirdView());
                    customerFollowVo.setCustomerName(customerDataThirdView.getCustomerName());
                    cmCustVisitLogVo.setCustName(customerDataThirdView.getCustomerName());
                }
                log.info("请求写跟进的请求参数customerFollowVo:{},employeeVO:{}", JSONObject.toJSONString(customerFollowVo), JSONObject.toJSONString(employeeVO));
                MapResultBean mapResultBeanFollow = customerFollowAppService.saveCustomerFollow(customerFollowVo, employeeVO);
                log.info("请求写跟进响应:{}", JSONObject.toJSONString(mapResultBeanFollow));
                if (mapResultBeanFollow.getStatus() != 101) {
                    log.error("请求写跟进返回异常:{}", JSONObject.toJSONString(mapResultBeanFollow));
                    throw new RuntimeException();
                }
            } catch (Exception e) {
                log.error("请求写跟进失败", e);
                log.error("请求写跟进的请求参数customerFollowVo:{},employeeVO:{}", JSONObject.toJSONString(customerFollowVo), JSONObject.toJSONString(employeeVO));
                throw new RuntimeException();
            }
        } catch (Exception e) {
            if (messageExt.getReconsumeTimes() > 5) {
                log.error("请求写跟进失败", e);
            } else {
                log.warn("请求写跟进失败", e);
            }
            throw new RuntimeException();
        }

    }


    public static SalesStageEnum getByName(String name) {
        return Arrays.stream(SalesStageEnum.values()).filter(T -> Objects.equals(T.getName(), name)).findAny().orElse(null);
    }

    public static SalesStageEnum getById(String id) {
        return Arrays.stream(SalesStageEnum.values()).filter(T -> Objects.equals(T.getId(), id)).findAny().orElse(null);
    }
}
