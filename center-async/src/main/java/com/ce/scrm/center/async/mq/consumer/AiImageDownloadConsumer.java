package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.business.ImageDownload;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.config.NlpProperties;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

import static com.ce.scrm.center.service.constant.ServiceConstant.LockKey.AICRM_IMAGE_GENERATOR_LIMIT;
import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_AI_IMAGE_GENERATOR_TOPIC;

@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_AI_IMAGE_GENERATOR_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_AI_IMAGE_GENERATOR_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 4)
public class AiImageDownloadConsumer implements RocketMQListener<MessageExt> {

    @Autowired
    private NlpProperties nlpProperties;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RocketMqOperate rocketMqOperate;

    @Autowired
    private ImageDownload imageDownload;

    @Autowired
    private SendWxMessage sendWxMessage;

    @Override
    public void onMessage(MessageExt messageExt) {
        String body = new String(messageExt.getBody());
        if (StringUtils.isBlank(body)) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        String url = jsonObject.getString("originUrl");
        String savePath = jsonObject.getString("savePath");
        String viewUrl = jsonObject.getString("viewUrl");
        String question = jsonObject.getString("question");
        String employeeId = jsonObject.getString("employeeId");
        String employeeName = jsonObject.getString("employeeName");
        String message = "员工姓名：" + employeeName + "\n" +
                "图片描述：" + question + "\n";
        try {
            if (StringUtils.isBlank(url) || StringUtils.isBlank(savePath) || StringUtils.isBlank(viewUrl)) {
                log.error("mq消息体内容缺失:{},messageId:{}", body, messageExt.getMsgId());
                return;
            }
            int limitimage = Integer.parseInt(nlpProperties.getImageGeneratorLimit());
            try {
                long limit = stringRedisTemplate.opsForValue().increment(AICRM_IMAGE_GENERATOR_LIMIT, 1L);
                stringRedisTemplate.expire(AICRM_IMAGE_GENERATOR_LIMIT, 10, TimeUnit.MINUTES);
                if (limit > limitimage) {
                    log.info("请求生成图片的人太多了,继续重试");
                    rocketMqOperate.syncSend(SCRM_AI_IMAGE_GENERATOR_TOPIC, JSONObject.toJSONString(jsonObject), 3);
                    return;
                }
                imageDownload.downAndSaveImage(url, savePath);
                //发送 企微消息
                message = message + "图片URL：" + viewUrl;
                log.info("生成图片成功发送消息:{},{}", employeeId, message);
                //成功
                this.sendWxMessage("异步生成\n" + message, "black");
                sendWxMessage.sendMessage(employeeId, message);
            } finally {
                stringRedisTemplate.opsForValue().decrement(AICRM_IMAGE_GENERATOR_LIMIT, 1L);
            }
        } catch (Exception e) {
            message = message + "图片URL：图片生成失败。";
            if (messageExt.getReconsumeTimes() > 15) {
                log.error("生成图片失败发送消息:{},{}", employeeId, message);
                sendWxMessage.sendMessage(employeeId, message);
            }
            // 失败
            message = message + "当前重试次数:" + messageExt.getReconsumeTimes() + "次";
            this.sendWxMessage("异步生成\n" + message, "red");
            log.warn("生成图片失败发送消息:{},{}", employeeId, message);
            throw new RuntimeException(e);
        }
    }


    public void sendWxMessage(String message, String type) {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .connectTimeout(10, TimeUnit.SECONDS)  // 连接超时时间
                .readTimeout(10, TimeUnit.SECONDS)     // 读取超时时间
                .writeTimeout(10, TimeUnit.SECONDS)    // 写入超时时间
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\n  \"msgtype\": \"markdown\",\n  \"markdown\": {\n    \"content\": \"<font color=\\\"" + type + "\\\">" + message + "</font>\"\n  }\n}");
        Request request = new Request.Builder()
                .url("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=044c53b3-939f-4c8b-be7c-b75bcebdf91f")
                .method("POST", body)
                .addHeader("Authorization", "Bearer sk-7de039270e844634be26fb38451b4867")
                .addHeader("Content-Type", "application/json")
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                log.info("发送企业微信消息成功:{}", response.body().string());
            } else {
                log.error("发送企业微信消息失败:{}", response.message());
            }
        } catch (Exception e) {
            log.error("发送企微消息失败", e);
        }
    }
}
