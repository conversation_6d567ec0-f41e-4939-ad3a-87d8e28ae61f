package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.center.async.mq.entity.CustomerKeepAndLossMqData;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerCirculationSetting;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.ICustomerCirculationSettingService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.ProtectCustTypeEnum;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * 成交客户保有流失消费组
 * 流转step 1
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CUST_ID_FOR_CIRCULATION_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.SCRM_CUSTOMER_KEEP_AND_LOSS_GROUP, consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class CustomerKeepAndLossConsumer implements RocketMQListener<String> {

    @Resource
    private RocketMqOperate rocketMqOperate;
    @Resource
    private CmCustProtectService cmCustProtectService;
    @Resource
    private ICustomerCirculationSettingService customerCirculationSettingService;

    @Override
    public void onMessage(String message) {
        if (StringUtils.isEmpty(message)) {
            return;
        }
        try {
            // 从保护关系查询是否是成交客户
            CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, message).one();
            if (Objects.isNull(cmCustProtect)) {
                //log.warn("CustomerKeepAndLossConsumer未查询到该客户有相关保护关系:{}", message);
                return;
            }
            if (!Objects.equals(cmCustProtect.getCustType(), ProtectCustTypeEnum.ORDERED.getValue()) && !Objects.equals(cmCustProtect.getCustType(), ProtectCustTypeEnum.SECOND_DEVELOPMENT.getValue())) {
                //log.warn("CustomerKeepAndLossConsumer该客户不属于成交客户:{}，custType:{}", message, cmCustProtect.getCustType());
                return;
            }
            // 查询保有流失配置
            CustomerCirculationSetting circulationSetting = customerCirculationSettingService.getOne(new LambdaQueryWrapper<CustomerCirculationSetting>().eq(CustomerCirculationSetting::getSubId, cmCustProtect.getSubcompanyId()));
            if (Objects.isNull(circulationSetting)) {
                //log.warn("CustomerKeepAndLossConsumer客户:{}对应的分司配置为空:{}", message, cmCustProtect.getSubcompanyId());
                return;
            }
            // 组装消息体
            CustomerKeepAndLossMqData mqData = new CustomerKeepAndLossMqData();
            BeanUtils.copyProperties(circulationSetting, mqData);
            mqData.setCustId(message);
            // 发送消息
            SendResult sendResult = rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_CUSTOMER_KEEP_AND_LOSS_TOPIC, JSON.toJSONString(mqData));
            //log.info("CustomerKeepAndLossConsumer发送消息:{},发送结果:{}", JSON.toJSONString(mqData), sendResult.toString());
        } catch (Exception e) {
            log.error("CustomerKeepAndLossConsumer消费异常:{}", message, e);
            throw new RuntimeException(e);
        }
    }
}
