package com.ce.scrm.center.async.job.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.business.CirculationLossBusiness;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 每天凌晨待流转客户会全量放入customer_will_circulation表，任务完成后，昨日及以前的数据已没用，需清理。
 *              该job每日物理删除 OFFSET_DAYS 天前insert的待流转客户。
 * @author: JiuDD
 * date: 2024/09/07
 */
@Slf4j
@Component
public class ClearExpiredCustomerWillCirculationJobHandler {
    /**
     * 删除 OFFSET_DAYS 天前insert的待流转客户
     */
    public static final int OFFSET_DAYS = 10;
    @Resource
    private CirculationLossBusiness circulationLossBusiness;

    /**
     * Description: 待流转客户每天全量放入customer_will_circulation表。每天凌晨跑一次，跑完后，昨日及以前的数据已没用，需清理。
     *              该job每日物理删除 OFFSET_DAYS 天前insert的待流转客户。
     * @author: JiuDD
     * @param param 日期。可指定多个，以英文逗号分割，格式为yyyy-MM-dd的字符串，且必需是十天前的日期，否则该job不会执行. 日期格式如：2024-09-01,2024-09-02,2024-09-03
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * date: 2024/09/07 14:27
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.CLEAR_EXPIRED_CUSTOMER_WILL_CIRCULATION)
    public ReturnT<String> clearExpiredCustomerWillCirculation(String param) {
        ReturnT<String> returnT = new ReturnT<>();
        try {
            // OFFSET_DAYS 天前的日期
            Date offsetDay = DateUtil.truncate(DateUtil.offsetDay(DateUtil.date(), -10), DateField.DAY_OF_YEAR);
            log.info("{} 天前的日期：{}", OFFSET_DAYS, offsetDay);

            //1. 如果xxl-job控制台给了参数，则按照参数给定日期物理删除 OFFSET_DAYS 天前insert的待流转客户
            if (StringUtils.isNotBlank(param)) {
                List<String> createDateList = getEffectiveCreateDateList(param, offsetDay);
                if (CollectionUtil.isEmpty(createDateList)) {
                    log.error("按照给定日期物理删除 {} 天前insert的待流转客户---日期参数有误. 原始参数：{}, 有效日期：[]", OFFSET_DAYS, param);
                    returnT.setCode(ReturnT.FAIL_CODE);
                    returnT.setMsg("按照给定日期物理删除 " + OFFSET_DAYS + " 天前insert的待流转客户---日期参数有误. 原始参数：" + param + ", 有效日期：" + JSON.toJSONString(createDateList));
                    return returnT;
                }
                log.info("按照给定日期 {} 物理删除 {} 天前insert的待流转客户开始. 原始参数：{}", JSON.toJSONString(createDateList), OFFSET_DAYS, param);
                circulationLossBusiness.deleteByOriginAndCreateDateList(createDateList);
                log.info("按照给定日期 {} 物理删除 {} 天前insert的待流转客户结束. 原始参数：{}", JSON.toJSONString(createDateList), OFFSET_DAYS, param);
                return ReturnT.SUCCESS;
            } else {
                //2. 如果xxl-job控制台没有给参数，则按照OFFSET_DAYS 天前的日期物理删除 OFFSET_DAYS 天前insert的待流转客户
                log.info("删除 {} 天前insert的待流转客户.", OFFSET_DAYS);
                circulationLossBusiness.deleteByOriginAndCreateDate(offsetDay);
                log.info("删除 {} 天前insert的待流转客户结束.", OFFSET_DAYS);
                return ReturnT.SUCCESS;
            }
        } catch (Exception e) {
            log.error("从smadb.customer_will_circulation表物理删除 {} 天前insert的待流转客户失败. 参数：{}", OFFSET_DAYS, param, e);
        }
        return ReturnT.FAIL;
    }

    /**
     * Description: 从 xxl-job 控制台给定的参数中，获取有效的创建日期。
     * @author: JiuDD
     * @param param xxl-job 控制台给定的参数
     * @param offsetDay OFFSET_DAYS 天前的日期
     * @return java.util.List<java.lang.String> 有效的日期列表
     * date: 2024/9/7 16:45
     */
    private static List<String> getEffectiveCreateDateList(String param, Date offsetDay) {
        if (StringUtils.isBlank(param) || Objects.isNull(offsetDay)) {
            return Lists.newArrayList();
        }
        JSONObject jsonObject = JSONObject.parseObject(param);
        String createDateList = jsonObject.getString("createDate");
        if (StringUtils.isBlank(createDateList)) {
            return Lists.newArrayList();
        }
        return Arrays.stream(createDateList.split(",")).filter(x -> DateUtil.parseDate(x).isBeforeOrEquals(offsetDay)).collect(Collectors.toList());
    }

}
