package com.ce.scrm.center.async.job.handler;

import com.ce.scrm.center.service.business.ProtectBusiness;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 定时发送微信消息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/19
 */
@Slf4j
@Component
public class MajorSendWxJobHandler {

    @Resource
    private ProtectBusiness protectBusiness;

    /**
     * 过去24小时的未处理的消息，发送对应商务企微
     *
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2024/9/19
     * @version 1.0.0
     **/
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_MAJOR_SEND_WX_UNHANDLED_MSG_JOB_HANDLER)
    public ReturnT<String> MajorSendMsg(String param) {
        log.info("===========定时给商务发送未处理消息开始==========");
        protectBusiness.sendMajorWxMsg();
        log.info("===========定时给商务发送未处理消息结束==========");
        return ReturnT.SUCCESS;
    }

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_MANAGER_SEND_WX_UNHANDLED_MSG_JOB_HANDLER)
    public ReturnT<String> mangerSendMsg(String param) {
        log.info("===========定时任务：经理有待分配（线索、商机、未成交、成交）给经理发企微通知开始==========");
        protectBusiness.mangerSendMsg();
        log.info("===========定时任务：经理有待分配（线索、商机、未成交、成交）给经理发企微通知结束==========");
        return ReturnT.SUCCESS;
    }

}
