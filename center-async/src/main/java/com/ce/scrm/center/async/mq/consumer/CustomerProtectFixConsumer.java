package com.ce.scrm.center.async.mq.consumer;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Optional;

/**
 * Description: 误流失客户保护关系binlog 打印
 * @author: JiuDD
 * date: 2024/7/17
 */
@Slf4j
@Deprecated
//@Component
//@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CM_CUST_PROTECT_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.SCRM_CUSTOMER_PROTECTED_FIX_GROUP, consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class CustomerProtectFixConsumer implements RocketMQListener<MessageExt> {
    @Resource
    private CustomerThirdService customerThirdService;

    @Override
    public void onMessage(MessageExt messageExt) {
        String data = new String(messageExt.getBody());
        log.info("收到保有客户流转MQ消息，msgId={},消息内容：{}", messageExt.getMsgId(), data);
        if (StringUtils.isEmpty(data)) {
            return;
        }
        try {
            JSONObject binlogJson = JSON.parseObject(data);
            Boolean isDdl = binlogJson.getBoolean("isDdl");
            if (isDdl) {
                return;
            }
            String type = binlogJson.getString("type");
            if (!"UPDATE".equals(type)) {
                return;
            }
            JSONArray afterAry = binlogJson.getJSONArray("data");
            if (afterAry.isEmpty()) {
                return;
            }
            JSONArray beforeAry = binlogJson.getJSONArray("old");
            for (int i = 0; i < afterAry.size(); i++) {
                JSONObject after = afterAry.getJSONObject(i);
                JSONObject before = beforeAry.getJSONObject(i);

                CmCustProtect cmCustProtectAfter = JSONObject.parseObject(after.toJSONString(), CmCustProtect.class);
                CmCustProtect cmCustProtectBefore = JSONObject.parseObject(before.toJSONString(), CmCustProtect.class);

                Date dbUpdateTime = cmCustProtectAfter.getDbUpdateTime();
                if (dbUpdateTime.after(DateUtil.parse("2024-08-06 19:00:00"))) {
                    continue;
                }
                if (dbUpdateTime.before(DateUtil.parse("2024-08-06 18:00:00"))) {
                    continue;
                }

                Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(cmCustProtectAfter.getCustId());
                if (!customerData.isPresent()) {
                    log.error("获取客户信息失败，custId:{}", afterAry.getJSONObject(i).getString("CUST_ID"));
                    continue;
                }
                CustomerDataThirdView customer = customerData.get();
                Date tagLostTime = customer.getTagLostTime();
                log.info("客户{}的tagLostTime={}", customer.getCustomerId(), tagLostTime);
                if (cmCustProtectAfter.getStatus() != 4) {
                    log.info("客户{}的presentStage={}，不处理", customer.getCustomerId(), cmCustProtectAfter.getStatus());
                    continue;
                }

                if (tagLostTime != null && tagLostTime.after(DateUtil.parse("2024-02-08 00:00:00"))) {
                    log.info("客户{}误流失,before={},after={}", customer.getCustomerId(), before, after);
                    //log.info("before_column: {},{},{},{},{},{},{},{},{},{},@,{},{},{},{},{},{},{},{},{},{}",
                    //        cmCustProtectAfter.getCustId(),
                    //        DateUtil.format(cmCustProtectBefore.getAssignDate(), "yyyy-MM-dd HH:mm:ss"),
                    //        DateUtil.format(cmCustProtectBefore.getProtectTime(), "yyyy-MM-dd HH:mm:ss"),
                    //        DateUtil.format(cmCustProtectBefore.getExceedTime(), "yyyy-MM-dd HH:mm:ss"),
                    //        cmCustProtectBefore.getStatus(),
                    //        cmCustProtectBefore.getSource(), cmCustProtectBefore.getRegProvince(), cmCustProtectBefore.getRegCity(), cmCustProtectBefore.getRegRegion(), cmCustProtectBefore.getCustType(),
                    //
                    //        cmCustProtectAfter.getCustId(),
                    //        DateUtil.format(cmCustProtectAfter.getAssignDate(), "yyyy-MM-dd HH:mm:ss"),
                    //        DateUtil.format(cmCustProtectAfter.getProtectTime(), "yyyy-MM-dd HH:mm:ss"),
                    //        DateUtil.format(cmCustProtectAfter.getExceedTime(), "yyyy-MM-dd HH:mm:ss"),
                    //        cmCustProtectAfter.getStatus(),
                    //        cmCustProtectAfter.getSource(), cmCustProtectAfter.getRegProvince(), cmCustProtectAfter.getRegCity(), cmCustProtectAfter.getRegRegion(), cmCustProtectAfter.getCustType());

                    // CUST_ID ASSIGN_DATE PROTECT_TIME EXCEED_TIME STATUS SOURCE REG_PROVINCE REG_CITY REG_REGION CUST_TYPE
                    log.info("before_column:update table cm_cust_protect " +
                                    "   set ASSIGN_DATE = '{}' " +
                                    ", PROTECT_TIME = '{}' " +
                                    ", EXCEED_TIME = '{}' " +
                                    ", STATUS = {} " +
                                    ", SOURCE = {} " +
                                    ", REG_PROVINCE = '{}' " +
                                    ", REG_CITY = '{}' " +
                                    ", REG_REGION = '{}' " +
                                    ", CUST_TYPE = {} " +
                                    "where CUST_ID = '{}' "
                            ,
                            DateUtil.format(cmCustProtectBefore.getAssignDate(), "yyyy-MM-dd HH:mm:ss"),
                            DateUtil.format(cmCustProtectBefore.getProtectTime(), "yyyy-MM-dd HH:mm:ss"),
                            DateUtil.format(cmCustProtectBefore.getExceedTime(), "yyyy-MM-dd HH:mm:ss"),
                            cmCustProtectBefore.getStatus(),
                            cmCustProtectBefore.getSource(), cmCustProtectBefore.getRegProvince(), cmCustProtectBefore.getRegCity(), cmCustProtectBefore.getRegRegion(), cmCustProtectBefore.getCustType()
                            ,cmCustProtectAfter.getCustId());

                    log.info("after_column:update table cm_cust_protect " +
                                    "   set ASSIGN_DATE = '{}' " +
                                    ", PROTECT_TIME = '{}' " +
                                    ", EXCEED_TIME = '{}' " +
                                    ", STATUS = {} " +
                                    ", SOURCE = {} " +
                                    ", REG_PROVINCE = '{}' " +
                                    ", REG_CITY = '{}' " +
                                    ", REG_REGION = '{}' " +
                                    ", CUST_TYPE = {} " +
                                    "where CUST_ID = '{}' "
                            ,
                            DateUtil.format(cmCustProtectAfter.getAssignDate(), "yyyy-MM-dd HH:mm:ss"),
                            DateUtil.format(cmCustProtectAfter.getProtectTime(), "yyyy-MM-dd HH:mm:ss"),
                            DateUtil.format(cmCustProtectAfter.getExceedTime(), "yyyy-MM-dd HH:mm:ss"),
                            cmCustProtectAfter.getStatus(),
                            cmCustProtectAfter.getSource(), cmCustProtectAfter.getRegProvince(), cmCustProtectAfter.getRegCity(), cmCustProtectAfter.getRegRegion(), cmCustProtectAfter.getCustType()
                            ,cmCustProtectAfter.getCustId());
                }


            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
