package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.mq.entity.AudioList;
import com.ce.scrm.center.async.util.AsrClientCe;
import com.ce.scrm.center.async.util.DescribeTaskStatusResponse;
import com.ce.scrm.center.dao.entity.AiVoiceAnalyze;
import com.ce.scrm.center.dao.service.AiVoiceAnalyzeService;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.support.redis.RedisOperator;
import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskRequest;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskResponse;
import com.tencentcloudapi.asr.v20190614.models.DescribeTaskStatusRequest;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.profile.Language;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_AI_SPEECH_RECOGNITION_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_SPEECH_RECOGNITION_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 10)
public class AiSpeechRecognitionConsumer implements RocketMQListener<MessageExt> {

    private static final Credential cred = new Credential("AKIDHRQCl9LK8j6IG1YsETVKvXzEG7mDceXD", "dGi0gDZieNsEYhgU6XzIWXounMm01pUy");

    private static final ClientProfile clientProfile = new ClientProfile();

    static {
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setReqMethod("GET");
        httpProfile.setConnTimeout(30);
        httpProfile.setWriteTimeout(30);
        httpProfile.setReadTimeout(30);
        httpProfile.setEndpoint("asr.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        clientProfile.setSignMethod(ClientProfile.SIGN_TC3_256);
        // 自3.1.80版本开始，SDK 支持打印日志。
        clientProfile.setHttpProfile(httpProfile);
        clientProfile.setDebug(true);
        // 从3.1.16版本开始，支持设置公共参数 Language, 默认不传，选择(ZH_CN or EN_US)
        clientProfile.setLanguage(Language.EN_US);
    }

    @Resource
    private RocketMqOperate rocketMqOperate;

    @Autowired
    private AiVoiceAnalyzeService aiVoiceAnalyzeService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Autowired
    private SendWxMessage sendWxMessage;
    @Autowired
    private RedisOperator redisOperator;

    @Value("${voiceNotifyUrl:}")
    private String voiceNotifyUrl;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void onMessage(MessageExt messageExt) {
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            log.warn("语音分析结果不存在");
            return;
        }
        AudioList audioList = JSONObject.parseObject(data, AudioList.class);
        log.info("SCRM_AI_SPEECH_RECOGNITION_TOPIC MESSAGE={}", JSONObject.toJSONString(audioList));
        Long id = audioList.getId();
        AiVoiceAnalyze analyze = aiVoiceAnalyzeService.getById(id);
        if (null == analyze) {
            log.warn("语音分析结果不存在:{}", id);
            throw new RuntimeException("语音分析结果不存在");
        }
        // 判断语音分析状态  只有group type 为 1 的情况下是语音识别
        if (!Objects.equals(analyze.getGroupType(), 1)) {
            log.warn("消息消费异常 group_type 不正确 :{},id:{}", analyze.getGroupType(), analyze.getId());
            return;
        }
        aiVoiceAnalyzeService.lambdaUpdate().eq(AiVoiceAnalyze::getId, id)
                .set(AiVoiceAnalyze::getMergeUrl, audioList.getCombineUrl())
                .set(AiVoiceAnalyze::getOriginUrl, String.join(",", audioList.getAudioUrls())).update();
        // 0 未开始 1 进行中 2 完成 3 异常
        if (Objects.equals(analyze.getVoiceStatus(), 0)) {
            log.info("进行语言识别请求:{}", analyze.getId());
            // 执行上传腾讯操作
            AsrClient client = new AsrClient(cred, "ap-shanghai", clientProfile);
            // 实例化一个cvm实例信息查询请求对象,每个接口都会对应一个request对象。
            CreateRecTaskRequest req = new CreateRecTaskRequest();
            req.setEngineModelType("16k_zh");
            req.setChannelNum(1L);
            req.setSourceType(0L);
            req.setUrl(audioList.getCombineUrl());
            req.setResTextFormat(1L);
            req.setSpeakerDiarization(1L);
            req.setSpeakerNumber(5L);
            CreateRecTaskResponse resp = null;
            log.info("请求腾讯请求参数：{}", JSONObject.toJSONString(req));
            try {
                resp = client.CreateRecTask(req);
                log.info("请求腾讯响应：{},请求ID:{}", JSONObject.toJSONString(resp), analyze.getId());
                audioList.setStartTime(System.currentTimeMillis());
                boolean flag = aiVoiceAnalyzeService.lambdaUpdate()
                        .set(AiVoiceAnalyze::getReqInfo, JSONObject.toJSONString(resp))
                        .set(AiVoiceAnalyze::getVoiceStatus, 1L)
                        .eq(AiVoiceAnalyze::getId, id).update();
                if (!flag) {
                    log.error("更新语音识别状态失败");
                    throw new RuntimeException();
                }
                rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.SCRM_AI_SPEECH_RECOGNITION_TOPIC, JSONObject.toJSONString(audioList), 3);
            } catch (TencentCloudSDKException e) {
                log.error("请求腾讯响应：{},请求ID:{}", JSONObject.toJSONString(resp), analyze.getId());
                log.error("请求腾讯语音识别失败", e);
                throw new RuntimeException(e);
            }
        } else if (Objects.equals(analyze.getVoiceStatus(), 1)) {
            // 通过 taskId 进行查询
            // 获取时间戳
            Long startTime = audioList.getStartTime();
            try {
                CreateRecTaskResponse createRecTaskResponse = JSONObject.parseObject(analyze.getReqInfo(), CreateRecTaskResponse.class);
                DescribeTaskStatusRequest describeTaskStatusRequest = new DescribeTaskStatusRequest();
                describeTaskStatusRequest.setTaskId(createRecTaskResponse.getData().getTaskId());
                DescribeTaskStatusResponse resp = new AsrClientCe(cred, "ap-shanghai", clientProfile).DescribeTaskStatus(describeTaskStatusRequest);
                // 0:任务等待 1：进行中 2:成功 3:失败
                if (Objects.equals(resp.getData().getStatus(), 2L)) {
                    //存储识别结果
                    //存储原始消息
                    StringBuilder stringBuilder = new StringBuilder();
                    if (null == resp.getData().getResultDetail()) {
                        this.sendErrorMessage(analyze);
                        return;
                    }
                    for (DescribeTaskStatusResponse.SentenceDetail sentenceDetail : resp.getData().getResultDetail()) {
                        String res = "说话人" + sentenceDetail.getSpeakerId() + ":" + sentenceDetail.getFinalSentence();
                        stringBuilder.append(res).append("\n");
                    }
                    boolean flag = aiVoiceAnalyzeService.lambdaUpdate()
                            .set(AiVoiceAnalyze::getRespFormat, stringBuilder.toString())
                            //.set(AiVoiceAnalyze::getRespInfo, JSONObject.toJSONString(resp))
                            .set(AiVoiceAnalyze::getVoiceStatus, 2)
                            //.set(AiVoiceAnalyze::getAnalyzeStatus, 1)
                            .eq(AiVoiceAnalyze::getId, id).update();
                    if (!flag) {
                        log.error("更新分析结果失败");
                        throw new RuntimeException();
                    }
                    rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.SCRM_VOICE_AI_NOTIFY_TOPIC, JSONObject.toJSONString(audioList));
                    // 错误状态或者是超时 24小时的消息进行丢弃
                } else if (Objects.equals(resp.getData().getStatus(), 3L)) {
                    log.error("请求腾讯响应：{},请求ID:{}", JSONObject.toJSONString(resp), analyze.getId());
                    if (audioList.getConsumeTimes() > 10) {
                        this.sendErrorMessage(analyze);
                        return;
                    }
                    audioList.setConsumeTimes(audioList.getConsumeTimes() + 1);
                    aiVoiceAnalyzeService.lambdaUpdate()
                            .set(AiVoiceAnalyze::getVoiceStatus, 0L)
                            .eq(AiVoiceAnalyze::getId, id).update();
                    rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.SCRM_AI_SPEECH_RECOGNITION_TOPIC, JSONObject.toJSONString(audioList));
                } else if ((System.currentTimeMillis() - startTime) > 24 * 3600 * 1000) {
                    this.sendErrorMessage(analyze);
                } else {
                    log.info("请求信息：{},识别响应信息：{}", data, JSONObject.toJSONString(resp));
                    // 重复 mq 延时消息
                    rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.SCRM_AI_SPEECH_RECOGNITION_TOPIC, JSONObject.toJSONString(audioList), 5);
                }
            } catch (Exception e) {
                if (messageExt.getReconsumeTimes() > 15) {
                    this.sendErrorMessage(analyze);
                    return;
                }
                log.error("腾讯语音识别结果查询MQ消息消费异常id：{}", analyze.getId(), e);
                throw new RuntimeException(e);
            }
        } else if (Objects.equals(analyze.getVoiceStatus(), 2)) {
            rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.SCRM_VOICE_AI_NOTIFY_TOPIC, JSONObject.toJSONString(audioList));
        } else if (Objects.equals(analyze.getVoiceStatus(), 3)) {
            log.info("语音识别失败，进行错误处理");
            this.sendErrorMessage(analyze);
        }
    }


    private void sendErrorMessage(AiVoiceAnalyze analyze) {
        // 按照group 进行锁定
        boolean flag = redisOperator.setIfAbsent(ServiceConstant.MqConstant.Topic.SCRM_AI_SPEECH_RECOGNITION_TOPIC + ":" + analyze.getId(), "1", 5L, TimeUnit.MINUTES);
        if (!flag) {
            return;
        }
        log.error("腾讯语音识别结果查询MQ消息消费异常id：{}", analyze.getId());

        aiVoiceAnalyzeService.lambdaUpdate()
                .set(AiVoiceAnalyze::getVoiceStatus, 3L)
                .eq(AiVoiceAnalyze::getGroupId, analyze.getGroupId())
                .eq(AiVoiceAnalyze::getGroupType, 1)
                .update();

        aiVoiceAnalyzeService.lambdaUpdate()
                .set(AiVoiceAnalyze::getVoiceStatus, 3L)
                .eq(AiVoiceAnalyze::getId, analyze.getId()).update();
        String messageError = "员工姓名：" + analyze.getEmployeeName() + "\n" +
                "客户名称：" + analyze.getCustName() + "\n" +
                "录音上传时间：" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(analyze.getCreateTime()) + "\n" +
                "录音分析结果：录音识别失败,请检查录音文件后再次尝试,失败录音文件:" + analyze.getOriginUrl();
        sendWxMessage.sendMessage(analyze.getEmployeeId(), messageError);
        sendWechatMessage(messageError);
    }


    // 临时发送消息
    public Boolean sendWechatMessage(String message) {
        if (StringUtils.isBlank(voiceNotifyUrl)) {
            log.warn("语音分析通知地址未配置");
            return false;
        }
        try {
            Map<String, String> contentMap = new HashMap<>();
            contentMap.put("content", message);
            Map<String, Object> param = new HashMap<>();
            param.put("msgtype", "markdown");
            param.put("markdown", contentMap);
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(param));
            Request request = new Request.Builder()
                    .url(voiceNotifyUrl)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            response.close();
        } catch (Exception e) {
            log.error("活动消息通知发送失败");
        }
        return true;
    }


}
