

package com.ce.scrm.center.async.util;

import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.tencentcloudapi.asr.v20190614.models.DescribeTaskStatusRequest;
import com.tencentcloudapi.common.AbstractClient;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.JsonResponseModel;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;

import java.lang.reflect.Type;

public class AsrClientCe extends AbstractClient {

    private static String endpoint = "asr.tencentcloudapi.com";
    private static String version = "2019-06-14";

    public AsrClientCe(Credential credential, String region, ClientProfile profile) {
        super(endpoint, version, credential, region, profile);
    }

    public DescribeTaskStatusResponse DescribeTaskStatus(DescribeTaskStatusRequest req) throws TencentCloudSDKException {
        JsonResponseModel<DescribeTaskStatusResponse> rsp = null;
        String rspStr = "";
        try {
            Type type = (new TypeToken<JsonResponseModel<DescribeTaskStatusResponse>>() {
            }).getType();
            rspStr = this.internalRequest(req, "DescribeTaskStatus");
            rsp = this.gson.fromJson(rspStr, type);
        } catch (JsonSyntaxException var5) {
            JsonSyntaxException e = var5;
            throw new TencentCloudSDKException("response message: " + rspStr + ".\n Error message: " + e.getMessage());
        }
        return rsp.response;
    }

}
