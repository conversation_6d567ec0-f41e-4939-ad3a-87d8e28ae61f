package com.ce.scrm.center.async.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

@Slf4j
public class JsonToMarkdown {
    public static String convert2TmarkDown(String jsonString) {
        try {
            Object root = JSON.parse(jsonString, Feature.OrderedField);  // 保序解析
            return convert(root, "", 2, 0);  // 从 h2 开始，缩进从0层
        } catch (Exception e) {
            log.error("转换json to markdown失败", e);
        }
        return jsonString;
    }

    public static String convert(Object obj, String title, int level, int indentLevel) {
        StringBuilder markdown = new StringBuilder();
        String indent = repeat("> ", indentLevel);
        String headingPrefix = repeat("#", Math.min(level, 6));
        String fullHeading = indent + headingPrefix + " " + title + "\n\n";
        if (obj instanceof JSONObject) {
            markdown.append(fullHeading);
            JSONObject jsonObj = (JSONObject) obj;
            for (String key : jsonObj.keySet()) {
                Object value = jsonObj.get(key);
                if (value instanceof JSONArray || value instanceof JSONObject) {
                    markdown.append(convert(value, key + "明细", level + 1, indentLevel + 1));
                } else {
                    markdown.append(indent).append("- ").append(key).append(": ").append(value).append("\n");
                }
            }
            markdown.append("\n");
        } else if (obj instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray) obj;
            markdown.append(fullHeading);
            boolean isPrimitive = true;
            for (Object item : jsonArray) {
                if (item instanceof JSONObject || item instanceof JSONArray) {
                    isPrimitive = false;
                    break;
                }
            }
            if (isPrimitive) {
                markdown.append(indent).append("| 序号 | 值 |\n").append(indent).append("| --- | --- |\n");
                for (int i = 0; i < jsonArray.size(); i++) {
                    markdown.append(indent).append("| ").append(i + 1).append(" | ").append(jsonArray.get(i)).append(" |\n");
                }
                markdown.append("\n");
                return markdown.toString();
            }
            // 收集字段（以第一条为准补全）
            LinkedHashSet<String> fieldSet = new LinkedHashSet<>();
            for (Object item : jsonArray) {
                if (item instanceof JSONObject) {
                    JSONObject row = (JSONObject) item;
                    fieldSet.addAll(row.keySet());
                }
            }
            // 表头
            markdown.append(indent).append("| 序号 | ");
            for (String field : fieldSet) {
                markdown.append(field).append(" | ");
            }
            markdown.append("\n").append(indent).append("| --- | ");
            for (int i = 0; i < fieldSet.size(); i++) {
                markdown.append("--- | ");
            }
            markdown.append("\n");
            // 表体
            List<String> subTables = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject row = jsonArray.getJSONObject(i);
                markdown.append(indent).append("| ").append(i + 1).append(" | ");
                for (String field : fieldSet) {
                    Object cell = row.get(field);
                    if (cell instanceof JSONArray || cell instanceof JSONObject) {
                        String subTitle = field + "明细（来自 " + title + " 第" + (i + 1) + "条）";
                        markdown.append("[查看").append(subTitle).append("] | ");
                        subTables.add(convert(cell, subTitle, level + 1, indentLevel + 1));
                    } else {
                        markdown.append(cell != null ? cell.toString() : "").append(" | ");
                    }
                }
                markdown.append("\n");
            }
            for (String sub : subTables) {
                markdown.append("\n").append(sub);
            }
            markdown.append("\n");
        }
        return markdown.toString();
    }

    private static String repeat(String s, int count) {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < count; i++) {
            builder.append(s);
        }
        return builder.toString();
    }
}
