package com.ce.scrm.center.async.mq.consumer;

import cn.ce.cesupport.enums.cooperation.ApplyCooperationStatusEnum;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.business.entity.dto.EntWeChatApprovalCallBackDto;
import com.ce.scrm.center.dao.entity.ApplyCooperation;
import com.ce.scrm.center.dao.entity.CmEntWechatApproveDetail;
import com.ce.scrm.center.dao.entity.SmaDictionaryItem;
import com.ce.scrm.center.dao.service.ApplyCooperationService;
import com.ce.scrm.center.dao.service.CmEntWechatApproveDetailService;
import com.ce.scrm.center.dao.service.SmaDictionaryItemService;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.constant.SmsTemplateConstants;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * @version 1.0
 * @Description: 企业微信审批回调  从中企发过来  审批回调参数文档：https://developer.work.weixin.qq.com/document/path/91815
 * @Author: lijinpeng
 * @Date: 2024/10/16 18:03
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${mq.prefix}"+ServiceConstant.MqConstant.Topic.SCRM_COOPERATION_APPROVE_TOPIC,
        consumerGroup = "${mq.prefix}"+ServiceConstant.MqConstant.Group.SCRM_COOPERATION_APPROVE_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class EntWeChatApproverConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CmEntWechatApproveDetailService entWechatApproveDetailService;

    @Resource
    private ApplyCooperationService applyCooperationService;

    @Resource
    private SendWxMessage sendWxMessage;

    @Resource
    private SmaDictionaryItemService smaDictionaryItemService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onMessage(MessageExt messageExt) {
        String msgId = messageExt.getMsgId();
        String topic = messageExt.getTopic();
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            return;
        }
        try {
            JSONObject jsonObject = JSONObject.parseObject(data);
            log.info("企业微信审批回调开始，消息体为：{}", JSON.toJSONString(jsonObject));
            String body = jsonObject.getString("xml");
            EntWeChatApprovalCallBackDto entWeChatApprovalCallBackDto = JSON.parseObject(body, EntWeChatApprovalCallBackDto.class);
            if(entWeChatApprovalCallBackDto == null
                    || entWeChatApprovalCallBackDto.getApprovalInfo() == null
                    || entWeChatApprovalCallBackDto.getApprovalInfo().getSpNo() == null) {
                log.error("企业微信审批回调数据异常：{}",entWeChatApprovalCallBackDto);
                return;
            }
            Long spNo = entWeChatApprovalCallBackDto.getApprovalInfo().getSpNo();
            ApplyCooperation applyCooperation = applyCooperationService.lambdaQuery().eq(ApplyCooperation::getSpNo, spNo).one();
            if(applyCooperation == null) {
                log.error("企业微信审批回调，查询合作信息为空，spNo={}",spNo);
                return;
            }
            if(!Objects.equals(applyCooperation.getState(),ApplyCooperationStatusEnum.APPROVE.getState())) {//审批中
//                log.error("企业微信审批回调，合作状态异常");
                return;
            }

            // 构建save数据
            CmEntWechatApproveDetail cmEntWechatApproveDetail = BeanUtil.copyProperties(entWeChatApprovalCallBackDto, CmEntWechatApproveDetail.class);
            EntWeChatApprovalCallBackDto.ApprovalInfo approvalInfo = entWeChatApprovalCallBackDto.getApprovalInfo();
            cmEntWechatApproveDetail.setSpNo(approvalInfo.getSpNo());
            cmEntWechatApproveDetail.setSpName(approvalInfo.getSpName());
            cmEntWechatApproveDetail.setApplyerUserId(approvalInfo.getApplyer() != null?approvalInfo.getApplyer().getUserId():"");
            cmEntWechatApproveDetail.setTemplateId(approvalInfo.getTemplateId());
            cmEntWechatApproveDetail.setStatuChangeEvent(approvalInfo.getStatuChangeEvent());
            cmEntWechatApproveDetail.setApplyTime(new Date(approvalInfo.getApplyTime() * 1000L));
            cmEntWechatApproveDetail.setSpStatus(approvalInfo.getSpStatus());
            cmEntWechatApproveDetail.setSpRecord(JSON.toJSONString(approvalInfo.getSpRecord()));
            cmEntWechatApproveDetail.setCreateTime(new Date(entWeChatApprovalCallBackDto.getCreateTime() * 1000L));
            boolean save = entWechatApproveDetailService.save(cmEntWechatApproveDetail);
            if(!save){
                log.error("企业微信审批回调，保存审批信息失败!");
                return;
            }
            // 最后要调用 判断状态   修改状态  走原逻辑
            Integer spStatus = approvalInfo.getSpStatus();
            if(spStatus == null) {
                return;
            }
            if(Objects.equals(spStatus, 2)) {// 同意
                //  审批流程中 审批通过判断合作状态 如果是审批中（商务异动，导致审批状态变成已取消，不作后面处理）  继续走下面流程
//                if(!Objects.equals(applyCooperation.getState(),ApplyCooperationStatusEnum.APPROVE.getState())) {
//                    return;
//                }

                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, applyCooperation.getPeriodValidityDay() == null?180:applyCooperation.getPeriodValidityDay());
                Date time = calendar.getTime();

                // 处理合作表状态
                applyCooperationService.lambdaUpdate()
                        .eq(ApplyCooperation::getId, applyCooperation.getId())
                        .set(ApplyCooperation::getState,ApplyCooperationStatusEnum.HEZUOZHONG.getState())
                        .set(ApplyCooperation::getExceedTime,time)
                        .set(ApplyCooperation::getHandleTime,new Date())
                        .set(ApplyCooperation::getUpdateDate,new Date())
                        .set(ApplyCooperation::getUpdateUser,"企业微信审批") //操作人 详情请看企业微信审批表 cm_ent_wechat_approve_detail
                        .update();

                // 审批同意之后，发送消息
                SmaDictionaryItem smaDictionaryItem = smaDictionaryItemService.lambdaQuery().eq(SmaDictionaryItem::getCode, SmsTemplateConstants.SCRM_COOPERATION_APPROVE_AGREE).one();
                if(smaDictionaryItem == null) {
                    log.error("发送企业微信失败，未找到消息模板,{}", SmsTemplateConstants.SCRM_COOPERATION_APPROVE_AGREE);
                }
                Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(applyCooperation.getCustId());
                if(!customerData.isPresent()) {
                    log.error("客户不存在,custId:{}", applyCooperation.getCustId());
                    throw new RuntimeException("客户不存在");
                }
                String customerName = customerData.get().getCustomerName();
                log.info("smaDictionaryItem.getName,:{}customerName:{}",smaDictionaryItem.getName(), customerName);
                String message = MessageFormat.format(smaDictionaryItem.getName(), customerName);
                // 给保护商务发送合作审批成功消息
                sendWxMessage.sendMessage(applyCooperation.getProtectSalerId(),message);
                // 给合作商务发送合作审批成功消息
                sendWxMessage.sendMessage(applyCooperation.getCooperationSalerId(),message);

            }else if(Objects.equals(spStatus, 3) || Objects.equals(spStatus, 4)) {// 拒绝
                // 处理合作表状态
                applyCooperationService.lambdaUpdate()
                        .eq(ApplyCooperation::getId, applyCooperation.getId())
                        .set(ApplyCooperation::getState,ApplyCooperationStatusEnum.SHOUDONGJUJUE.getState())
                        .set(ApplyCooperation::getHandleTime,new Date())
                        .set(ApplyCooperation::getUpdateDate,new Date())
                        .set(ApplyCooperation::getUpdateUser,"企业微信审批") //操作人 详情请看企业微信审批表 cm_ent_wechat_approve_detail
                        .update();

                // 审批拒绝之后，发送消息
                SmaDictionaryItem smaDictionaryItem = smaDictionaryItemService.lambdaQuery().eq(SmaDictionaryItem::getCode, SmsTemplateConstants.SCRM_COOPERATION_APPROVE_DISAGREE).one();
                if(smaDictionaryItem == null) {
                    log.error("发送企业微信失败，未找到消息模板,{}", SmsTemplateConstants.SCRM_COOPERATION_APPROVE_DISAGREE);
                }
                Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(applyCooperation.getCustId());
                if(!customerData.isPresent()) {
                    log.error("客户不存在,custId:{}", applyCooperation.getCustId());
                    throw new RuntimeException("客户不存在");
                }
                String customerName = customerData.get().getCustomerName();
                String message = MessageFormat.format(smaDictionaryItem.getName(), customerName);
                // 给保护商务发送合作审批成功消息
                sendWxMessage.sendMessage(applyCooperation.getProtectSalerId(),message);
                // 给合作商务发送合作审批成功消息
                sendWxMessage.sendMessage(applyCooperation.getCooperationSalerId(),message);

            }else {
                return;
            }
        } catch (Exception e) {
            log.error("处理RocketMq消息失败! topic={} msgId {},{}", topic, msgId, e.getMessage());
            throw new RuntimeException(e);
        }
    }

}
