package com.ce.scrm.center.async.job.handler;

import cn.ce.cesupport.enums.favorites.ConvertRelationEnum;
import cn.ce.cesupport.sma.service.SjIntentSalerIndexAppService;
import cn.ce.cesupport.sma.vo.SjIntentSalerIndexVo;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.third.invoke.SmaConvertLogThirdService;
import com.google.gson.JsonObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * @program: scrm-center
 * @ClassName SjThreeDayUnconfirmedProtectJobHandler
 * @description:
 * @author: lijinpeng
 * @create: 2025-07-31 09:29
 * @Version 1.0
 **/
@Slf4j
@Component
public class SjThreeDayUnconfirmedProtectJobHandler {

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;

    @DubboReference
    private SjIntentSalerIndexAppService sjIntentSalerIndexAppService;

    @Resource
    private EmployeeInfoBusiness employeeInfoBusiness;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_SJ_THREE_DAY_UNCONFIRMED_PROTECT_JOB_HANDLER)
    public ReturnT<String> calculate(String param) {
        log.info("三天未确认保护 SjThreeDayUnconfirmedProtectJobHandler，start");
        try {
            start();
        } catch (Exception e) {
            log.error("SjThreeDayUnconfirmedProtectJobHandler,calculate 任务失败", e);
        }
        log.info("三天未确认保护 SjThreeDayUnconfirmedProtectJobHandler，end");
        return ReturnT.SUCCESS;
    }

    private void start() {

        // 获取当前时间减三天的Date对象
        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
        Date afterThreeDayDate = Date.from(threeDaysAgo.atZone(ZoneId.systemDefault()).toInstant());
        List<CmCustProtect> cmCustProtectList = cmCustProtectService.lambdaQuery()
                .ge(CmCustProtect::getCustType, 2)
                .in(CmCustProtect::getBusinessOpportunityConfirmationFlag, Arrays.asList(1, 2))
                .eq(CmCustProtect::getStatus, ProtectStateEnum.PROTECT.getState())
                .lt(CmCustProtect::getProtectTime, afterThreeDayDate).list();

        log.info("SjThreeDayUnconfirmedProtectJobHandler,一共 cmCustProtectList.size()={}", cmCustProtectList.size());

        Date currentDate = new Date();
        for (CmCustProtect cmCustProtect : cmCustProtectList) {

            if (Objects.equals(cmCustProtect.getBusinessOpportunityConfirmationFlag(),1)) {
                // 分给本分司其他商务
                String newSalerId = sjIntentSalerIndexAppService.dealAndFindSalersBySubId(cmCustProtect.getSubcompanyId());
                log.info("SjThreeDayUnconfirmedProtectJobHandler,cmCustProtect.getSubcompanyId={},newSalerId={}", cmCustProtect.getSubcompanyId(), newSalerId);
                if (StringUtils.isNotBlank(newSalerId)) {
                    EmployeeInfoBusinessDto newEmployee = employeeInfoBusiness.getEmployeeInfoByEmpId(newSalerId);
                    if (newEmployee == null) {
                        log.error("SjThreeDayUnconfirmedProtectJobHandler,newSalerId={} 不存在,cmCustProtect={}", newSalerId, JSONObject.toJSONString(cmCustProtect));
                        continue;
                    }
                    cmCustProtect.setSalerId(newEmployee.getId());
                    cmCustProtect.setBussdeptId(newEmployee.getOrgId());
                    cmCustProtect.setBuId(newEmployee.getBuId());
                    cmCustProtect.setProtectTime(currentDate);
                    cmCustProtect.setBusinessOpportunityConfirmationFlag(2);
                    cmCustProtect.setBindFlag(0);
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DATE, 3);
                    Date newDate = calendar.getTime();
                    cmCustProtect.setExceedTime(newDate);
                    cmCustProtectService.updateByCustId(cmCustProtect);

                    // 流转日志build
                    ConvertLogBusinessDto convertLogBusinessDto = ConvertLogBusinessDto.builder()
                            .salerId(cmCustProtect.getSalerId())
                            .deptOfSalerId(cmCustProtect.getBussdeptId())
                            .buOfSalerId(cmCustProtect.getBuId())
                            .subcompanyOfSalerId(cmCustProtect.getSubcompanyId())
                            .areaOfSalerId(cmCustProtect.getAreaId())
                            .createTime(currentDate)
                            .createBy("admin")
                            .convertType(ConvertRelationEnum.SJ_UNTREATED_CONFIRM_NEXT_SALER.getValue())
                            .custId(cmCustProtect.getCustId())
                            .custName(cmCustProtect.getCustName())
                            .curSalerId(newEmployee.getId())
                            .deptOfCurSalerId(newEmployee.getOrgId())
                            .buOfCurSalerId(newEmployee.getBuId())
                            .subcompanyOfCurSalerId(newEmployee.getSubId())
                            .areaOfCurSalerId(newEmployee.getAreaId())
                            .build();
                    smaConvertLogThirdService.insertLog(convertLogBusinessDto);
                }else {
                    // 释放客户掉公海
                    releaseCustomer(cmCustProtect,currentDate);
                }
            }else if (Objects.equals(cmCustProtect.getBusinessOpportunityConfirmationFlag(),2)) {
                // 释放客户掉公海
                releaseCustomer(cmCustProtect,currentDate);
            }

        }

    }

    private void releaseCustomer(CmCustProtect cmCustProtect,Date currentDate) {

        cmCustProtect.setStatus(ProtectStateEnum.CUSTOMER_POOL.getState());
        cmCustProtect.setOccupy(0);
        cmCustProtect.setBusinessOpportunityConfirmationFlag(0);
        cmCustProtect.setBindFlag(0);
        cmCustProtectService.updateByCustId(cmCustProtect);

        // 流转日志build
        ConvertLogBusinessDto convertLogBusinessDto = ConvertLogBusinessDto.builder()
                .salerId(cmCustProtect.getSalerId())
                .deptOfSalerId(cmCustProtect.getBussdeptId())
                .buOfSalerId(cmCustProtect.getBuId())
                .subcompanyOfSalerId(cmCustProtect.getSubcompanyId())
                .areaOfSalerId(cmCustProtect.getAreaId())
                .createTime(currentDate)
                .createBy("admin")
                .convertType(ConvertRelationEnum.SJ_UNTREATED_CONFIRM.getValue())
                .custId(cmCustProtect.getCustId())
                .custName(cmCustProtect.getCustName())
//                .curSalerId(newEmployee.getId())
//                .deptOfCurSalerId(newEmployee.getOrgId())
//                .buOfCurSalerId(newEmployee.getBuId())
//                .subcompanyOfCurSalerId(newEmployee.getSubId())
//                .areaOfCurSalerId(newEmployee.getAreaId())
                .build();
        smaConvertLogThirdService.insertLog(convertLogBusinessDto);
    }

}
