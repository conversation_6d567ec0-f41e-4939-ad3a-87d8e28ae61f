package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.AiEventLog;
import com.ce.scrm.center.dao.service.AiEventLogService;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.xfppt.PPTService;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Level.ONE_MINUTE;
import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Level.THIRTY_SECOND;
import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_KEXF_MESSAGE_PPT_AI_NEW_NOTIFY_TOPIC;

/**
 * 易企秀消息监听
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = SCRM_KEXF_MESSAGE_PPT_AI_NEW_NOTIFY_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_KEXF_MESSAGE_PPT_AI_NEW_NOTIFY_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class XFPPTDataNewReceiverConsumer implements RocketMQListener<MessageExt> {

    @Autowired
    private PPTService pptService;

    @Autowired
    private SendWxMessage sendWxMessage;

    @Autowired
    private RocketMqOperate rocketMqOperate;

    @Value("${pptNotify:}")
    private String pptNotify;

    @Autowired
    private AiEventLogService aiEventLogService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public static String prefixPPt = "SCRM:PPT:AI:";


    @Override
    public void onMessage(MessageExt messageExt) {
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            return;
        }
        try {
            JSONObject jsonObject = JSONObject.parseObject(data);
            String id = jsonObject.getString("id");
            AiEventLog aiEventLog = aiEventLogService.getById(Long.valueOf(id));
            String pptKey = stringRedisTemplate.opsForValue().get(prefixPPt + aiEventLog.getEmployeeId());
            PPTService.PPTKeyReq pptRequestParamKey = new PPTService.PPTKeyReq();
            if (StringUtils.isNotBlank(pptKey)) {
                pptRequestParamKey = JSONObject.parseObject(pptKey, PPTService.PPTKeyReq.class);
                if (Objects.equals(aiEventLog.getPptStatus(), 0)) {
                    String content = limitString(aiEventLog.getPptGenerateContent(), 8000);
                    JSONObject jsonObjectSid = pptService.getSid(content, pptRequestParamKey);
                    String sid = null;
                    try {
                        sid = jsonObjectSid.getJSONObject("data").getString("sid");
                    } catch (Exception e) {
                        sid = null;
                    }
                    if (StringUtils.isBlank(sid)) {
                        if (messageExt.getReconsumeTimes() > 5) {
                            log.warn("获取sid异常:{}", id);
                            sendWxMessage.sendMessage(aiEventLog.getEmployeeId(), "非常抱歉，生成PPT功能的免费试用期已结束，请复制整合好的方案，在网络上自行搜索PPT生成工具。\n" +
                                    "给您带来的不便，敬请谅解，我们后续会持续寻找更合适的解决方案");
                            aiEventLogService.lambdaUpdate().eq(AiEventLog::getId, aiEventLog.getId()).set(AiEventLog::getPptGenerateResult, JSONObject.toJSONString(jsonObjectSid)).set(AiEventLog::getPptStatus, 3).update();
                            return;
                        } else {
                            throw new RuntimeException();
                        }
                    }
                    aiEventLogService.lambdaUpdate().eq(AiEventLog::getId, aiEventLog.getId()).set(AiEventLog::getPptStatus, 1).update();
                    jsonObject.put("startTime", System.currentTimeMillis());
                    jsonObject.put("sid", sid);
                    rocketMqOperate.syncSend(SCRM_KEXF_MESSAGE_PPT_AI_NEW_NOTIFY_TOPIC, JSONObject.toJSONString(jsonObject), ONE_MINUTE);
                } else if (Objects.equals(aiEventLog.getPptStatus(), 1)) {
                    JSONObject pptUrljson = pptService.getPPTUrl(jsonObject.getString("sid"), pptRequestParamKey);
                    String pptUrl = null;
                    try {
                        pptUrl = pptUrljson.getJSONObject("data").getString("pptUrl");
                    } catch (Exception e) {
                        log.warn("获取url失败");
                    }
                    if (StringUtils.isNotBlank(pptUrl)) {
                        //发送消息
                        StringBuilder stringBuilder = new StringBuilder();
                        if (StringUtils.isNotBlank(aiEventLog.getCustName())) {
                            stringBuilder.append("客户名称: ").append(aiEventLog.getCustName()).append("\n");
                        }
                        stringBuilder.append("商务名称: ").append(aiEventLog.getEmployeeName()).append("\n");
                        stringBuilder.append("PPT下载地址: ").append(pptUrl).append("\n");
                        if (StringUtils.isNotBlank(aiEventLog.getChatId())) {
                            stringBuilder.append("对话ID: ").append(aiEventLog.getChatId()).append("\n");
                        }
                        stringBuilder.append("记录ID: ").append(aiEventLog.getId()).append("\n");
                        this.sendWechatMessage(stringBuilder.toString());
                        sendWxMessage.sendMessage(aiEventLog.getEmployeeId(), stringBuilder.toString());
                        aiEventLogService.lambdaUpdate().eq(AiEventLog::getId, aiEventLog.getId()).set(AiEventLog::getPptStatus, 2).set(AiEventLog::getPptGenerateResult, JSONObject.toJSONString(pptUrljson)).update();
                    } else {
                        aiEventLogService.lambdaUpdate().eq(AiEventLog::getId, aiEventLog.getId()).set(AiEventLog::getPptGenerateResult, JSONObject.toJSONString(pptUrljson)).update();
                        Long startTime = jsonObject.getLong("startTime");
                        if (((System.currentTimeMillis() - startTime) / 1000 / 3600) > 1) {
                            log.error("生成PPT失败:{}", JSONObject.toJSONString(jsonObject));
                            aiEventLogService.lambdaUpdate().eq(AiEventLog::getId, aiEventLog.getId()).set(AiEventLog::getPptStatus, 3).update();
                        } else {
                            rocketMqOperate.syncSend(SCRM_KEXF_MESSAGE_PPT_AI_NEW_NOTIFY_TOPIC, JSONObject.toJSONString(jsonObject), THIRTY_SECOND);
                        }
                    }
                } else {
                    log.warn("ppt状态不需要处理:{}",data);
                }
            } else {
                log.error("pptkey获取异常employeeId：{}", aiEventLog.getEmployeeId());
                aiEventLogService.lambdaUpdate().eq(AiEventLog::getId, aiEventLog.getId()).set(AiEventLog::getPptGenerateResult, "pptkey获取异常employeeId").set(AiEventLog::getPptStatus, 3).update();
            }
        } catch (Exception e) {
            log.warn("消息接收异常,{}", data, e);
            throw new RuntimeException("消息接收异常");
        }
    }


    // 临时发送消息
    public Boolean sendWechatMessage(String message) {
        try {
            Map<String, String> contentMap = new HashMap<>();
            contentMap.put("content", message);
            Map<String, Object> param = new HashMap<>();
            param.put("msgtype", "markdown");
            param.put("markdown", contentMap);
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(param));
            Request request = new Request.Builder()
                    .url(pptNotify)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            response.close();
        } catch (Exception e) {
            log.error("活动消息通知发送失败");
        }
        return true;
    }


    public static String limitString(String input, int maxLength) {
        if (input == null) {
            return "";
        }
        if (input.length() > maxLength) {
            log.warn("字符串超长，实际长度：{}，将截取前 {} 个字符", input.length(), maxLength);
            return input.substring(0, maxLength);
        }
        return input;
    }
}
