package com.ce.scrm.center.async.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class AIJsonChecker {

    static String system = "你是一个规则判断工具,必须返回标准的json格式\n" +
            "            请根据用户输入的规则判断是否需要流转\n" +
            "规则如下\n" +
            "1. 分公司ID不能为空\n" +
            "2. 未打卡自然月数 大于 0\n" +
            "3. 流转方式 总监待分 或 经理待分，不能为其他值。\n" +
            "4. 存在保护关系\n" +
            "5. 不在不流转控制表中\n" +
            "6. 客户的保护状态 不能处于下面的状态 总监待分配 或者 经理待分配或者 事业部总监待分配   或者 客户池\n" +
            "7. 客户类型必须是 网站客户或 者 非网站客户 这两种类型\n" +
            "8. 在考察期内且考核周期内不能有打卡次数\n" +
            "\n" +
            "\n" +
            "输入:\n" +
            "你是谁\n" +
            "\n" +
            "输出:\n" +
            "{\n" +
            "    \"need_transfer\": false,\n" +
            "    \"reason\": \"用户输入的内容不包含流转关键词（请、谢谢、麻烦、帮我）。\"\n" +
            "}";
    static String user = "分公司ID ： 2\n" +
            "未打卡自然月数： 10\n" +
            "流转方式： 总监待分\n" +
            "存在保护关系： 存在\n" +
            "不在不流转控制表中： 不在\n" +
            "客户的保护状态： 无\n" +
            "客户类型： 潜在客户\n" +
            "在考察期内： 是\n" +
            "考核周期内打卡次数： 0";

    public static void main(String[] args) {
        // String result = getResult(system, user);
        System.out.println(system);
    }

    public static String getResult(String system, String user) {
        Transaction t = Cat.newTransaction("SERVICE", "aiReqCompletions");
        t.setStatus(Transaction.SUCCESS);
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(1, TimeUnit.MINUTES)
                    .readTimeout(1, TimeUnit.MINUTES)
                    .writeTimeout(1, TimeUnit.MINUTES)
                    .build();
            String url = "http://10.24.0.14:8080/v1/chat/completions";
            JSONObject payloadObj = JSONObject.of(
                    "model", "zhongqidongli",
                    "messages", Arrays.asList(JSONObject.of(
                            "role", "system",
                            "content", system
                    ), JSONObject.of(
                            "role", "user",
                            "content", user
                    )),
                    "stream", false
            );
            String payload = JSON.toJSONString(payloadObj);
            log.info("请求到ai的数据:{}", JSON.toJSONString(payload));
            RequestBody body = RequestBody.create(MediaType.parse("application/json"), payload);
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            int maxRetries = 4; // 最多重试4次
            int attempt = 0;
            while (attempt < maxRetries) {
                long startTime = System.currentTimeMillis();
                try (Response response = client.newCall(request).execute()) {
                    long endTime = System.currentTimeMillis();
                    double duration = (endTime - startTime) / 1000.0;
                    if (response.body() != null) {
                        String responseBody = response.body().string();
                        log.info("请求耗时: " + String.format("%.3f", duration) + "秒");
                        log.info("ai返回的数据:{}", responseBody);
                        JSONObject jsonObjectRes = JSONObject.parseObject(
                                extractJson(JSON.parseObject(responseBody)
                                        .getJSONArray("choices")
                                        .getJSONObject(0)
                                        .getJSONObject("message")
                                        .getString("content"))
                        );
                        return JSONObject.toJSONString(jsonObjectRes);
                    } else {
                        throw new RuntimeException("请求失败，响应体为空");
                    }
                } catch (IOException | RuntimeException e) {
                    attempt++;
                    log.error("请求失败，第 " + attempt + " 次重试，错误信息: " + e.getMessage());
                    if (attempt >= maxRetries) {
                        throw new RuntimeException("重试" + maxRetries + "次后仍失败", e);
                    }
                    try {
                        Thread.sleep(1000); // 休眠1秒后再试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("线程中断", ie);
                    }
                }
            }
            return null;
        } finally {
            t.complete();
        }

    }

    public static String extractJson(String text) {
        // 加上 Pattern.DOTALL，让 . 匹配包括换行符
        Pattern pattern = Pattern.compile("\\{.*?\\}", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            return matcher.group();
        } else {
            throw new RuntimeException("未找到JSON内容");
        }
    }
}
