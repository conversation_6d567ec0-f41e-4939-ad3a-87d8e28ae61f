package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.center.async.mq.entity.EqixiuMessageHolder;
import com.ce.scrm.center.async.mq.entity.RewardType;
import com.ce.scrm.center.dao.entity.EqixiuActivityInfo;
import com.ce.scrm.center.dao.entity.EqixiuDataReceiverOrigin;
import com.ce.scrm.center.dao.entity.EqixiuDataResult;
import com.ce.scrm.center.dao.service.IEqixiuActivityInfoService;
import com.ce.scrm.center.dao.service.IEqixiuDataReceiverOriginService;
import com.ce.scrm.center.dao.service.IEqixiuDataResultService;
import com.ce.scrm.center.service.config.UniqueIdService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.eqixiu.support.entity.EqixiuCodeInfo;
import com.ce.scrm.center.service.eqixiu.support.entity.WinInfo;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.support.redis.RedisOperator;
import com.ce.scrm.center.util.constant.UtilConstant;
import com.ce.scrm.extend.dubbo.api.ShortUrlDubboService;
import com.ce.scrm.extend.dubbo.entity.view.ShortUrlView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.net.URL;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.ce.scrm.center.cache.constant.CacheConstant.CACHE_KEY_SEPARATOR;
import static com.ce.scrm.center.cache.constant.CacheConstant.CACHE_PREFIX;
import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_EQIXIU_MESSAGE_COUPON_NOTIFY_TOPIC;
import static com.ce.scrm.center.service.eqixiu.support.entity.EqixiuConstant.JOIN_LOTTERY;
import static com.ce.scrm.center.service.eqixiu.support.entity.EqixiuConstant.PREVIEW_VIEW;

/**
 * 易企秀消息监听
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_EQIXIU_MESSAGE_NOTIFY_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_EQIXIU_MESSAGE_NOTIFY_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class EqiXiuDataReceiverConsumer implements RocketMQListener<MessageExt> {

    public static final String E_QI_XIU_CACHE = CACHE_PREFIX + CACHE_KEY_SEPARATOR + "eqixiu:cache:";
    public static final String E_QI_XIU_LOCK = CACHE_PREFIX + CACHE_KEY_SEPARATOR + "eqixiu:lock:";
    public static final String E_QI_XIU_SEND_LOCK = CACHE_PREFIX + CACHE_KEY_SEPARATOR + "eqixiu:send:";


    private static final List<String> EVENT_TYPES = Arrays.asList(
            PREVIEW_VIEW,
            JOIN_LOTTERY
    );

    @Autowired
    private RedisOperator redisOperator;

    @Autowired
    private IEqixiuActivityInfoService eqixiuActivityInfoService;

    @Autowired
    private IEqixiuDataResultService eqxiuDataResultService;

    @Autowired
    private IEqixiuDataReceiverOriginService eqixiuDataReceiverOriginService;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private ShortUrlDubboService shortUrlDubboService;

    @Autowired
    private RocketMqOperate rocketMqOperate;

    @Autowired
    private UniqueIdService uniqueIdService;

    @Autowired
    private CampaignConsumer campaignConsumer;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void onMessage(MessageExt messageExt) {
        if (true){
            return;
        }
        String topic = messageExt.getTopic();
        String msgId = messageExt.getMsgId();
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            return;
        }
        String lockKey = "";
        try {
            JSONObject jsonObject = JSONObject.parseObject(data);
            String eventType = jsonObject.getString("eventType");
            if (!EVENT_TYPES.contains(eventType)) {
                return;
            }
            JSONObject userInfo = jsonObject.getJSONObject("customerInfo");
            if (userInfo == null) {
                log.warn("用户信息获取失败:{}", data);
                return;
            }
            String headImgUrl = userInfo.getString("headImgUrl");
            EqixiuCodeInfo eqixiuCodeInfo = getEqixiuCodeDto(headImgUrl);
            if (eqixiuCodeInfo == null) {
                log.info("解析code失败:{}", headImgUrl);
                return;
            }
            EqixiuDataResult eqixiuDataResult = eqxiuDataResultService.getById(eqixiuCodeInfo.getEqixiuDataResultId());
            if (null == eqixiuDataResult) {
                log.error("获取分享信息失败:{}", eqixiuCodeInfo.getEqixiuDataResultId());
                return;
            }
            String eventId = jsonObject.getString("eventId");
            lockKey = E_QI_XIU_LOCK + eventId;
            long count = redisOperator.incr(lockKey, 1);
            redisOperator.expire(lockKey, 1, TimeUnit.DAYS);
            if (count > 1) {
                //重新发送消息
                log.info("消息重复:{}", jsonObject.toJSONString());
                return;
            }
            String activityId = jsonObject.getString("id");
            List<EqixiuActivityInfo> eqixiuActivityInfos = eqixiuActivityInfoService.list(new LambdaQueryWrapper<EqixiuActivityInfo>()
                    .eq(EqixiuActivityInfo::getActivityId, activityId));
            if (CollectionUtils.isEmpty(eqixiuActivityInfos)) {
                log.warn("活动信息未配置:{}", activityId);
                return;
            }
            eqixiuDataResult.setId(uniqueIdService.getId());
            eqixiuDataResult.setRecentShareTime(null);
            if (PREVIEW_VIEW.equals(eventType)) {
                //打开页面
                eqixiuDataResult.setEventType(PREVIEW_VIEW);
                eqixiuDataResult.setRecentOpenTime(new Date());
                campaignConsumer.sendMessage(eqixiuDataResult);
            } else if (JOIN_LOTTERY.equals(eventType)) {
                eqixiuDataResult.setEventType(JOIN_LOTTERY);
                eqixiuDataResult.setRecentLotteryTime(new Date());
                int isWin = Integer.parseInt(jsonObject.getOrDefault("isWin", "0").toString());
                eqixiuDataResult.setIsWin(isWin);
                if (isWin == 1) {
                    WinInfo winInfo = new WinInfo();
                    winInfo.setLevelName(jsonObject.getString("levelName"));
                    winInfo.setPrizeType(jsonObject.getString("prizeType"));
                    winInfo.setPrizeName(jsonObject.getString("prizeName"));
                    winInfo.setRankNum(jsonObject.getString("rankNum"));
                    winInfo.setAmount(jsonObject.getString("amount"));
                    winInfo.setWinCode(jsonObject.getString("winCode"));
                    winInfo.setLevel(jsonObject.getString("level"));
                    winInfo.setPrizeCode(jsonObject.getString("prizeCode"));
                    EqixiuMessageHolder eqixiuMessageHolder = new EqixiuMessageHolder();
                    eqixiuMessageHolder.setEqixiuDataResult(eqixiuDataResult);
                    eqixiuMessageHolder.setWinInfo(winInfo);
                    eqixiuMessageHolder.setEventId(eventId);
                    rocketMqOperate.syncSend(SCRM_EQIXIU_MESSAGE_COUPON_NOTIFY_TOPIC, JSONObject.toJSONString(eqixiuMessageHolder));
                    Integer prizeType = Integer.parseInt(winInfo.getPrizeType());
                    if (Objects.equals(RewardType.E_VOUCHER.getCode(), prizeType)) {
                        //按照活动ID 加缓存  xxljob 5 分钟扫描一次缓存
                        redisOperator.set(E_QI_XIU_CACHE, "1");
                    }
                    eqixiuDataResult.setWinInfo(JSONObject.toJSONString(winInfo));
                }
            }
            EqixiuDataReceiverOrigin eqixiuDataReceiverOrigin = new EqixiuDataReceiverOrigin();
            eqixiuDataReceiverOrigin.setId(uniqueIdService.getId());
            eqixiuDataReceiverOrigin.setEventId(eventId);
            eqixiuDataReceiverOrigin.setEventType(eventType);
            eqixiuDataReceiverOrigin.setActivityId(activityId);
            eqixiuDataReceiverOrigin.setDataOrigin(data);
            eqixiuDataReceiverOrigin.setCodeDecoderInfo(JSONObject.toJSONString(eqixiuCodeInfo));
            // 插入到原始信息表
            eqixiuDataReceiverOriginService.save(eqixiuDataReceiverOrigin);
            eqixiuDataResult.setTriggerId(eqixiuDataReceiverOrigin.getId());
            eqixiuDataResult.setCreateTime(new Date());
            eqixiuDataResult.setUpdateTime(new Date());
            eqxiuDataResultService.save(eqixiuDataResult);
            redisOperator.expire(lockKey, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("处理消息失败messageId:{},topic:{}", msgId, topic, e);
            if (StringUtils.isNotBlank(lockKey)) {
                redisOperator.del(lockKey);
            }
            throw new RuntimeException(e);
        }
    }

    private static Map<String, String> getQueryParams(String urlString) {
        try {
            if (StringUtils.isNotBlank(urlString)) {
                URL url = new URL(urlString);
                String query = url.getQuery();
                Map<String, String> params = new HashMap<>();
                String[] pairs = query.split("&");
                for (String pair : pairs) {
                    String[] keyValue = pair.split("=");
                    String key = URLDecoder.decode(keyValue[0], "UTF-8");
                    String value = keyValue.length > 1 ? URLDecoder.decode(keyValue[1], "UTF-8") : "";
                    params.put(key, value);
                }
                return params;
            }
        } catch (Exception e) {
            log.warn("获取链接上的参数失败messageId");
        }
        return new HashMap<>();
    }


    public EqixiuCodeInfo getEqixiuCodeDto(String headImgUrl) {
        try {
            if (StringUtils.isBlank(headImgUrl)) {
                log.warn("用户头像为空: {}", headImgUrl);
                return null;
            }
            Map<String, String> param = getQueryParams(headImgUrl);
            String code = param.get("code");
            if (StringUtils.isBlank(code)) {
                log.info("获取code信息失败:{}", headImgUrl);
                return null;
            }
            EqixiuCodeInfo eqixiuCodeInfo = null;
            try {
                ShortUrlView shortUrlView = shortUrlDubboService.getLongUrlByUniqueCode(code).getData();
                eqixiuCodeInfo = JSONObject.parseObject(shortUrlView.getLongUrl(), EqixiuCodeInfo.class);
                if (null == eqixiuCodeInfo){
                    log.error("解析code失败:{}", code);
                }
            } catch (Exception e) {
                log.error("解析code失败", e);
                return null;
            }
            return eqixiuCodeInfo;
        } catch (Exception e) {
            log.error("通过个人头像获取分享信息失败", e);
        }
        return null;
    }

}
