package com.ce.scrm.center.async.mq.consumer;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.emp.enums.OrgTypeEnum;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerCirculation;
import com.ce.scrm.center.dao.entity.CustomerLayerChangeLog;
import com.ce.scrm.center.dao.entity.CustomerWillCirculation;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.CustomerCirculationService;
import com.ce.scrm.center.dao.service.CustomerLayerChangeLogService;
import com.ce.scrm.center.dao.service.CustomerWillCirculationService;
import com.ce.scrm.center.service.business.OrgInfoBusiness;
import com.ce.scrm.center.service.business.entity.dto.LastReachDto;
import com.ce.scrm.center.service.business.entity.dto.org.OrgChildrenQueryBusinessDto;
import com.ce.scrm.center.service.business.entity.view.org.OrgChildrenQueryBusinessView;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum;
import com.ce.scrm.center.service.enums.CustomerLayerEnum;
import com.ce.scrm.center.service.enums.LastReachTypeEnum;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.third.entity.dto.ClockConditionQueryThirdDto;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerPageThirdDto;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerShareQueryThirdDto;
import com.ce.scrm.center.service.third.entity.view.*;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmpCustSiteClockThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.util.date.DateUtils;
import com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView;
import io.netty.handler.codec.serialization.ObjectEncoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 插入customer_circulation流转表
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_CUSTOMER_ID_BATCH_SEND_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_CUSTOMER_CIRCULATION_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class CustomerCirculationConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CustomerCirculationService customerCirculationService;

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private CustomerLayerChangeLogService customerLayerChangeLogService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private EmpCustSiteClockThirdService empCustSiteClockThirdService;

    @Resource
    private OrgThirdService orgThirdService;

    @Resource
    private CustomerWillCirculationService customerWillCirculationService;

    @Resource
    private OrgInfoBusiness orgInfoBusiness;

    private final List<Integer> customerType = new ArrayList<>(Arrays.asList(3,4));

    private final List<Integer> customerLayer = new ArrayList<>(
            Arrays.asList(
                    CustomerLayerEnum.VIP.getCode(),
                    CustomerLayerEnum.HIGH_VALUE.getCode(),
                    CustomerLayerEnum.GENERAL.getCode()
            )
    );

    @Override
    public void onMessage(MessageExt messageExt) {
        try {
            log.info("计算客户流转，start");
            byte[] body = messageExt.getBody();
            List<String> customerIdList = JSON.parseArray(body, String.class);
            log.info("计算客户流转,customerIdList={}", JSON.toJSONString(customerIdList));
            customerCirculation(customerIdList);
            log.info("计算客户流转，end");
        }catch (Exception e){
            log.error("计算客户流转 失败,",e);
        }
    }

    /**
     * 客户流转
     * @param customerIdList
     */
    private void customerCirculation(List<String> customerIdList) {

        if (CollectionUtil.isEmpty(customerIdList)) {
            return;
        }

        Date nowTime = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String nowDateStr = dateFormat.format(nowTime);
        List<CustomerWillCirculation> list = customerWillCirculationService.lambdaQuery()
                .eq(CustomerWillCirculation::getCreateDate, nowDateStr)
                .in(CustomerWillCirculation::getCustId, customerIdList)
                .list();
        if (CollectionUtil.isNotEmpty(list)) {
            customerIdList.removeAll(list.stream().map(CustomerWillCirculation::getCustId).collect(Collectors.toList()));
        }
        if (CollectionUtil.isEmpty(customerIdList)) {
            return;
        }

        List<CmCustProtect> protectList = cmCustProtectService.lambdaQuery().in(CmCustProtect::getCustId, customerIdList).list();
        Map<String, CmCustProtect> protectMap = protectList.stream()
                .filter(item -> customerType.contains(item.getCustType())) // 取已成交
                .filter(item -> Objects.equals(item.getStatus(), ProtectStateEnum.PROTECT.getState())) // 取status=1
                .collect(Collectors.toMap(CmCustProtect::getCustId, item -> item, (key1, key2) -> key1));

        // 这里只支持最大200的查询 后面括大处理要注意
        List<CustomerDubboView> customerList = customerThirdService.getListByCondition(
                CustomerPageThirdDto.builder().customerIdList(customerIdList).build()
        );
        Map<String, CustomerDubboView> customerMap = customerList.stream()
                .filter(item -> customerLayer.contains(item.getCustomerLayer())) // 取 vip、高价值、一般客户
                .collect(Collectors.toMap(CustomerDubboView::getCustomerId, item -> item, (key1, key2) -> key1));

        Set<String> customerIdSet = protectMap.keySet();
        customerIdSet.retainAll(customerMap.keySet());

        List<CustomerWillCirculation> customerWillCirculationList = new ArrayList<>();
        List<CustomerCirculation> customerCirculationList = new ArrayList<>();
        for (String customerId : customerIdSet) {

            CustomerDubboView customer  = customerMap.get(customerId);
            CmCustProtect cmCustProtect = protectMap.get(customerId);

            Date lastCirculationDate = getLastCirculationDate(customerId,cmCustProtect.getFirstSignTime());

            Date nextCirculationDate = addPeriod(lastCirculationDate,customer.getCustomerLayer());

            // 下次流转日期 在 绝对保护器内 那么 +1周期
            if (cmCustProtect.getAbsoluteProtectTime() != null && cmCustProtect.getAbsoluteProtectTime().after(nextCirculationDate)) {
                nextCirculationDate = addPeriod(nextCirculationDate,customer.getCustomerLayer());
            }

            LastReachDto lastReachDto = getLastReach(customerId, customer.getCustomerLayer(), cmCustProtect,lastCirculationDate);
            // 在本周期内是否有拜访打卡记录 如果有 那么 +1周期
            if (lastReachDto != null && checkVisitTime(nextCirculationDate, customer.getCustomerLayer(), lastReachDto.getTime())) {
                nextCirculationDate = addPeriod(nextCirculationDate,customer.getCustomerLayer());
            }

            List<String> orgIdList = Arrays.asList(cmCustProtect.getAreaId(), cmCustProtect.getSubcompanyId(), cmCustProtect.getBussdeptId(), cmCustProtect.getBuId());
            Map<String, OrgDataThirdView> orgDataThirdViewMap = orgThirdService.getOrgData(orgIdList);


            if (isSameDay(nextCirculationDate, nowTime)) {
                // 流转表
                buildDateCustomerCirculation(customerId, cmCustProtect, orgDataThirdViewMap, nextCirculationDate, nowTime, customer, lastReachDto, customerCirculationList,lastCirculationDate);
                // 待流转表 deleteFlag = 1
                buildDateCustomerWillCirculation(customerId, cmCustProtect, orgDataThirdViewMap, nextCirculationDate, nowTime, customer, lastReachDto, customerWillCirculationList,1);
            }else {
                // 待流转表 deleteFlag = 0
                buildDateCustomerWillCirculation(customerId, cmCustProtect, orgDataThirdViewMap, nextCirculationDate, nowTime, customer, lastReachDto, customerWillCirculationList,0);
            }

        }
        // 待流转比表 供商务每天查看 不参与流转
        customerWillCirculationService.saveBatch(customerWillCirculationList);
        // 流转表 实际流转的表，只有流转日期是当天的才会进去
        customerCirculationService.saveBatch(customerCirculationList);

    }

    /**
     * 组装数据
     * @param customerId
     * @param cmCustProtect
     * @param orgDataThirdViewMap
     * @param nextCirculationDate
     * @param nowTime
     * @param customer
     * @param lastReachDto
     * @param customerCirculationList
     * @param lastCirculationDate
     */
    private void buildDateCustomerCirculation(String customerId, CmCustProtect cmCustProtect,
                                              Map<String, OrgDataThirdView> orgDataThirdViewMap,
                                              Date nextCirculationDate, Date nowTime, CustomerDubboView customer,
                                              LastReachDto lastReachDto, List<CustomerCirculation> customerCirculationList,
                                              Date lastCirculationDate) {
        CustomerCirculation willCirculation = new CustomerCirculation();
        willCirculation.setAreaId(cmCustProtect.getAreaId());
        willCirculation.setAreaName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getAreaId(), new OrgDataThirdView()).getName());
        willCirculation.setSubId(cmCustProtect.getSubcompanyId());
        willCirculation.setSubName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getSubcompanyId(), new OrgDataThirdView()).getName());
        willCirculation.setBuId(cmCustProtect.getBuId());
        willCirculation.setBuName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBuId(), new OrgDataThirdView()).getName());
        willCirculation.setDeptId(cmCustProtect.getBussdeptId());
        willCirculation.setDeptName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBussdeptId(), new OrgDataThirdView()).getName());
        willCirculation.setSalerId(cmCustProtect.getSalerId());
        willCirculation.setSalerName(employeeThirdService.getEmployeeData(cmCustProtect.getSalerId()).orElse(new EmployeeDataThirdView()).getName());
        willCirculation.setCustId(customerId);
        willCirculation.setCustName(cmCustProtect.getCustName());
        willCirculation.setPreDate(nextCirculationDate);
        willCirculation.setCreateTime(nowTime);
        willCirculation.setCreateDate(nowTime);
        willCirculation.setUpdateTime(nowTime);
        willCirculation.setCustomerLayer(customer.getCustomerLayer());
        if (lastReachDto != null) {
            willCirculation.setLastEmpId(lastReachDto.getLastEmpId());
            willCirculation.setLastEmpName(lastReachDto.getLastEmpName());
            willCirculation.setLastReachType(lastReachDto.getType());
            willCirculation.setLastReachTime(lastReachDto.getTime());
        }
        String reason = getReason(customer.getCustomerLayer());
        willCirculation.setReason(reason);
        willCirculation.setLastCirculationDate(lastCirculationDate);
        customerCirculationList.add(willCirculation);
    }

    /**
     * 组装数据
     * @param customerId
     * @param cmCustProtect
     * @param orgDataThirdViewMap
     * @param nextCirculationDate
     * @param nowTime
     * @param customer
     * @param lastReachDto
     * @param customerWillCirculationList
     */
    private void buildDateCustomerWillCirculation(String customerId, CmCustProtect cmCustProtect,
                                                  Map<String, OrgDataThirdView> orgDataThirdViewMap,
                                                  Date nextCirculationDate, Date nowTime, CustomerDubboView customer,
                                                  LastReachDto lastReachDto, List<CustomerWillCirculation> customerWillCirculationList,Integer deleteFlag) {
        CustomerWillCirculation willCirculation = new CustomerWillCirculation();
        willCirculation.setAreaId(cmCustProtect.getAreaId());
        willCirculation.setAreaName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getAreaId(), new OrgDataThirdView()).getName());
        willCirculation.setSubId(cmCustProtect.getSubcompanyId());
        willCirculation.setSubName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getSubcompanyId(), new OrgDataThirdView()).getName());
        willCirculation.setBuId(cmCustProtect.getBuId());
        willCirculation.setBuName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBuId(), new OrgDataThirdView()).getName());
        willCirculation.setDeptId(cmCustProtect.getBussdeptId());
        willCirculation.setDeptName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBussdeptId(), new OrgDataThirdView()).getName());
        willCirculation.setSalerId(cmCustProtect.getSalerId());
        willCirculation.setSalerName(employeeThirdService.getEmployeeData(cmCustProtect.getSalerId()).orElse(new EmployeeDataThirdView()).getName());
        willCirculation.setCustId(customerId);
        willCirculation.setCustName(cmCustProtect.getCustName());
        willCirculation.setOrigin(AssignCustSourceSpecialEnum.CIRCULATION.getValue());
        willCirculation.setStatus(null);
        willCirculation.setDeleteFlag(deleteFlag);
        willCirculation.setPreDate(nextCirculationDate);
        willCirculation.setCreateTime(nowTime);
        willCirculation.setCreateDate(nowTime);
        willCirculation.setUpdateTime(nowTime);
        willCirculation.setCustomerLayer(customer.getCustomerLayer());
        if (lastReachDto != null) {
            willCirculation.setLastEmpId(lastReachDto.getLastEmpId());
            willCirculation.setLastEmpName(lastReachDto.getLastEmpName());
            willCirculation.setLastReachType(lastReachDto.getType());
            willCirculation.setLastReachTime(lastReachDto.getTime());
        }
        String reason = getReason(customer.getCustomerLayer());
        willCirculation.setReason(reason);
        customerWillCirculationList.add(willCirculation);
    }


    /**
     * 判断拜访时间是否在这个考核周期内
     * @param nextCirculationDate
     * @param customerLayer
     * @param visitTime
     * @return
     */
    private boolean checkVisitTime(Date nextCirculationDate, Integer customerLayer, Date visitTime) {
        if (nextCirculationDate == null || customerLayer == null || visitTime == null) {
            return false;
        }

        Date lastCirculationDate = subtractPeriod(nextCirculationDate, customerLayer);

        return (visitTime.compareTo(lastCirculationDate) >= 0) && (visitTime.compareTo(nextCirculationDate) <= 0);
    }

    private Date addPeriod(Date lastCirculationDate, Integer customerLayer) {
        if (lastCirculationDate == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(lastCirculationDate); // 设置为当前日期
        if (Objects.equals(customerLayer, CustomerLayerEnum.VIP.getCode())) {
            calendar.add(Calendar.MONTH, 12);
        }else if (Objects.equals(customerLayer, CustomerLayerEnum.HIGH_VALUE.getCode())) {
            calendar.add(Calendar.MONTH, 3);
        }else if (Objects.equals(customerLayer, CustomerLayerEnum.GENERAL.getCode())) {
            calendar.add(Calendar.MONTH, 3);
        } else {
            // error
        }

        return calendar.getTime();
    }

    private Date subtractPeriod(Date nextCirculationDate, Integer customerLayer) {
        if (nextCirculationDate == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(nextCirculationDate); // 设置为当前日期
        if (Objects.equals(customerLayer, CustomerLayerEnum.VIP.getCode())) {
            calendar.add(Calendar.MONTH, -12);
        }else if (Objects.equals(customerLayer, CustomerLayerEnum.HIGH_VALUE.getCode())) {
            calendar.add(Calendar.MONTH, -3);
        }else if (Objects.equals(customerLayer, CustomerLayerEnum.GENERAL.getCode())) {
            calendar.add(Calendar.MONTH, -3);
        } else {
            // error
        }

        return calendar.getTime();
    }

    /**
     * 获取“上次流转时间” （其实并非是上次流转时间，这是一个计算下次流转时间的基准）  下次流转时间 = 上次流转时间 + 周期
     * @param customerId
     * @param firstSignTime
     * @return
     */
    private Date getLastCirculationDate(String customerId, Date firstSignTime) {
        // 计算上次流转时间
        CustomerCirculation customerCirculation = customerCirculationService.lambdaQuery()
                .eq(CustomerCirculation::getCustId, customerId)
//                .eq(CustomerCirculation::getStatus,YesOrNoEnum.YES.getCode())
                .orderByDesc(CustomerCirculation::getCreateDate)
                .last("limit 1").one();
        // 查询上次客户分层customerLayer变化时间
        CustomerLayerChangeLog customerLayerChangeLog = customerLayerChangeLogService.lambdaQuery()
                .eq(CustomerLayerChangeLog::getCustomerId, customerId)
                .orderByDesc(CustomerLayerChangeLog::getCreateTime)
                .last("limit 1")
                .one();
        Date customerLayerChangeDate = customerLayerChangeLog != null ? getFirstDayOfNextMonth(customerLayerChangeLog.getCreateTime()) : null;
        Date lastCirculationDate = null;
        if (customerCirculation == null) {
            //利用初始的流转规则推算上次流转时间
            if (firstSignTime == null) {
                // 没有首次签单时间 以 2024.8.1为初次流转时间 依次+3 推出现在应该是 2025.5.1
                Calendar calendar = Calendar.getInstance();
                calendar.set(2025, Calendar.MAY, 1, 0, 0, 0);
                Date date = calendar.getTime();// 2025-05-01 00:00:00
                lastCirculationDate = getMaxDate(date,customerLayerChangeDate);
            }else {
                Date date = getDateByFirstSign(firstSignTime);
                lastCirculationDate = getMaxDate(date,customerLayerChangeDate);
            }
        }else {
            lastCirculationDate = getMaxDate(customerCirculation.getPreDate(),customerLayerChangeDate);
        }
        return lastCirculationDate;
    }

    /**
     * 根据首次签单日期推算 上一次流转日期 firstSignTime(下个月1号)+...3 < 当前时间
     * @param firstSignTime
     * @return
     */
    private Date getDateByFirstSign(Date firstSignTime) {

        if (firstSignTime == null) {
            return null;
        }

        // 获取Calendar实例
        Calendar calendar2 = Calendar.getInstance();
        // 减去一天
        calendar2.add(Calendar.DATE, -1);
        // 转换为Date对象
        Date yesterday = calendar2.getTime();

        Date date = getFirstDayOfNextMonth(firstSignTime);

        // 此处 yesterday本来是new Date 但是6月1日计算流转的时候  会把本应6月1日要流转的数据，计算上一次流转日期就算为6月1日
        int l = monthsBetween(date, yesterday);
        int x = l % 3;

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date); // 设置为当前日期
        calendar.add(Calendar.MONTH, l-x);
        return calendar.getTime();
    }

    /**
     * 计算两个Date之间差了多少个月
     * @param startDate
     * @param endDate
     * @return
     */
    private int monthsBetween(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }

        if(startDate.after(endDate)) {
            return 0;
        }

        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return Math.toIntExact(ChronoUnit.MONTHS.between(start, end));
    }

    /**
     * 给定Date对象 获取 下个月 1号的Date
     * @param date
     * @return
     */
    private Date getFirstDayOfNextMonth(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 设置为下个月1号
        calendar.add(Calendar.MONTH, 1);      // 月份+1
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为1号
        calendar.set(Calendar.HOUR_OF_DAY, 0);  // 可选：时间设为00:00:00
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    /**
     * 比较两个Date 将大的返回
     * @param date1
     * @param date2
     * @return
     */
    private Date getMaxDate(Date date1, Date date2) {

        if (date1 == null && date2 == null) {
            return null;
        }

        if (date1 == null) {
            return date2;
        }

        if (date2 == null) {
            return date1;
        }

        return date1.after(date2) ? date1 : date2;
    }

    /**
     * 根据custId 获取上次触达相关信息
     * @param custId
     * @param customerLayer
     * @param cmCustProtect
     * @param lastCirculationDate
     * @return
     */
    private LastReachDto getLastReach(String custId, Integer customerLayer, CmCustProtect cmCustProtect,Date lastCirculationDate) {
        if (org.apache.commons.lang3.StringUtils.isBlank(custId) || customerLayer == null || cmCustProtect == null) {
            return null;
        }

        List<LastReachDto> resultList = new ArrayList<>();
        String salerId = cmCustProtect.getSalerId();

        // 一般客户 判断内容分享 只有保护商务分享的算
        if (Objects.equals(customerLayer, CustomerLayerEnum.GENERAL.getCode())) {

            List<CustomerShareTemporaryThirdView> customerShareList = customerThirdService.getCustomerShareTemporaryByCustomerId(
                    CustomerShareQueryThirdDto.builder().customerId(custId).startTime(lastCirculationDate).build()
            );
            for (CustomerShareTemporaryThirdView item : customerShareList) {
                // 分享
                String shareEmpId = item.getSalerId();
                if (Objects.equals(shareEmpId, salerId)) {
                    Optional<EmployeeInfoThirdDto> employeeByEmpId = employeeThirdService.getEmployeeByEmpId(shareEmpId);
                    if (!employeeByEmpId.isPresent()) {
                        continue;
                    }
                    EmployeeInfoThirdDto employeeInfoThirdDto = employeeByEmpId.get();
                    LastReachTypeEnum lastReachTypeEnum = getShareType(employeeInfoThirdDto.getPosition());
                    resultList.add(LastReachDto.builder()
                            .lastEmpId(employeeInfoThirdDto.getId())
                            .lastEmpName(employeeInfoThirdDto.getName())
                            .type(lastReachTypeEnum == null ? null : lastReachTypeEnum.getCode())
                            .typeName(lastReachTypeEnum == null ? null : lastReachTypeEnum.getDescription())
                            .time(item.getShareTime())
                            .build());
                }
            }

        }

        // 打卡拜访
        List<EmpCustSiteClockThirdView> clockThirdViewList = empCustSiteClockThirdService.selectByCondition(
                ClockConditionQueryThirdDto.builder().custId(custId).startTime(lastCirculationDate).markClockValidFlag(2).build()
        );
        for (EmpCustSiteClockThirdView item : clockThirdViewList) {
            if (Objects.equals(customerLayer, CustomerLayerEnum.GENERAL.getCode())) {
                // 一般客户
                /**
                 * 一般客户，任何人打卡都算
                 */
                String visitEmpId = null;
                if (Objects.equals(item.getReplaceClockFlag(), YesOrNoEnum.YES.getCode())) {
                    visitEmpId = item.getReplaceClockEmpId();
                } else {
                    visitEmpId = item.getEmpId();
                }
                Optional<EmployeeInfoThirdDto> employeeByEmpId = employeeThirdService.getEmployeeByEmpId(visitEmpId);
                if (!employeeByEmpId.isPresent()) {
                    continue;
                }
                EmployeeInfoThirdDto employeeInfoThirdDto = employeeByEmpId.get();
                LastReachTypeEnum lastReachTypeEnum = getVisitType(employeeInfoThirdDto.getPosition());
                resultList.add(LastReachDto.builder()
                        .lastEmpId(employeeInfoThirdDto.getId())
                        .lastEmpName(employeeInfoThirdDto.getName())
                        .type(lastReachTypeEnum == null ? null : lastReachTypeEnum.getCode())
                        .typeName(lastReachTypeEnum == null ? null : lastReachTypeEnum.getDescription())
                        .time(item.getCreateTime())
                        .build());
            }else if (Objects.equals(customerLayer, CustomerLayerEnum.VIP.getCode())) {
                /**
                 * 每年本分司总监或经理或主管拜访一次，
                 * 经理或主管可以是本分司任意部门的。
                 * 商务拜访不算有效拜访。
                 */
                if (Objects.equals(item.getReplaceClockFlag(), YesOrNoEnum.YES.getCode())) {
                    String visitEmpId = item.getReplaceClockEmpId();
                    // 查询分司下的所有部门、包括事业部 (其实这个时候 事业部下面的部门没查到 需要再查)
                    List<String> leaderList = getLeaderList(cmCustProtect.getSubcompanyId());
                    Map<String, EmployeeLiteThirdView> orgMap = new HashMap<>(leaderList.size());
                    for (String leaderId : leaderList) {
                        Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(leaderId);
                        orgLeader.ifPresent(employeeLiteThirdView -> orgMap.put(employeeLiteThirdView.getId(), employeeLiteThirdView));
                    }
                    if (orgMap.containsKey(visitEmpId)) {
                        EmployeeLiteThirdView employeeLiteThirdView = orgMap.get(visitEmpId);
                        LastReachTypeEnum lastReachTypeEnum = getVisitType(employeeLiteThirdView.getPosition());
                        resultList.add(LastReachDto.builder()
                                .lastEmpId(employeeLiteThirdView.getId())
                                .lastEmpName(employeeLiteThirdView.getName())
                                .type(lastReachTypeEnum == null ? null : lastReachTypeEnum.getCode())
                                .typeName(lastReachTypeEnum == null ? null : lastReachTypeEnum.getDescription())
                                .time(item.getCreateTime())
                                .build());
                    }
                }
            }else if (Objects.equals(customerLayer, CustomerLayerEnum.HIGH_VALUE.getCode())) {
                /**
                 * 每三个月由本分司总监、经理/组长（支持其他部门经理/组长）、保护商务拜访打卡一次
                 */
                if (Objects.equals(item.getReplaceClockFlag(), YesOrNoEnum.YES.getCode())) {
                    String visitEmpId = item.getReplaceClockEmpId();
                    // 查询分司下的所有部门、包括事业部 (其实这个时候 事业部下面的部门没查到 需要再查)
                    List<String> leaderList = getLeaderList(cmCustProtect.getSubcompanyId());
                    Map<String, EmployeeLiteThirdView> orgMap = new HashMap<>(leaderList.size());
                    for (String leaderId : leaderList) {
                        Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(leaderId);
                        orgLeader.ifPresent(employeeLiteThirdView -> orgMap.put(employeeLiteThirdView.getId(), employeeLiteThirdView));
                    }
                    if (orgMap.containsKey(visitEmpId)) {
                        EmployeeLiteThirdView employeeLiteThirdView = orgMap.get(visitEmpId);
                        LastReachTypeEnum lastReachTypeEnum = getVisitType(employeeLiteThirdView.getPosition());
                        resultList.add(LastReachDto.builder()
                                .lastEmpId(employeeLiteThirdView.getId())
                                .lastEmpName(employeeLiteThirdView.getName())
                                .type(lastReachTypeEnum == null ? null : lastReachTypeEnum.getCode())
                                .typeName(lastReachTypeEnum == null ? null : lastReachTypeEnum.getDescription())
                                .time(item.getCreateTime())
                                .build());
                    }
                }else {
                    String visitEmpId = item.getEmpId();
                    if (Objects.equals(visitEmpId, cmCustProtect.getSalerId())) {
                        Optional<EmployeeInfoThirdDto> employeeByEmpId = employeeThirdService.getEmployeeByEmpId(visitEmpId);
                        if (!employeeByEmpId.isPresent()) {
                            continue;
                        }
                        EmployeeInfoThirdDto employeeInfoThirdDto = employeeByEmpId.get();
                        LastReachTypeEnum lastReachTypeEnum = getVisitType(employeeInfoThirdDto.getPosition());
                        resultList.add(LastReachDto.builder()
                                .lastEmpId(employeeInfoThirdDto.getId())
                                .lastEmpName(employeeInfoThirdDto.getName())
                                .type(lastReachTypeEnum == null ? null : lastReachTypeEnum.getCode())
                                .typeName(lastReachTypeEnum == null ? null : lastReachTypeEnum.getDescription())
                                .time(item.getCreateTime())
                                .build());
                    }
                }
            }
        }

        if (resultList.isEmpty()) {
            return null;
        }

        resultList.sort(Comparator.comparing(LastReachDto::getTime).reversed());

        LastReachDto result = resultList.get(0);

        return result;
    }

    @NotNull
    private List<String> getLeaderList(String subId) {
        if (StringUtils.isBlank(subId)) {
            return Collections.emptyList();
        }
        List<String> leaderList = new ArrayList<>();
        leaderList.add(subId);
        OrgChildrenQueryBusinessView orgChildrenQueryBusinessView = orgInfoBusiness.getChildrenList(OrgChildrenQueryBusinessDto.builder().id(subId).buFlag(1).build());
        List<OrgThirdDto> children = orgChildrenQueryBusinessView.getChildren();
        if (CollectionUtil.isNotEmpty(children)) {
            leaderList.addAll(children.stream().map(OrgThirdDto::getId).collect(Collectors.toList()));
            List<String> buIdList = children.stream().filter(o -> Objects.equals(OrgTypeEnum.BU.getType(), o.getType())).map(OrgThirdDto::getId).collect(Collectors.toList());
            // 查事业部下面的部门
            if (CollectionUtil.isNotEmpty(buIdList)) {
                buIdList.forEach(buId -> {
                    OrgChildrenQueryBusinessView buChildren = orgInfoBusiness.getChildrenList(OrgChildrenQueryBusinessDto.builder().id(buId).build());
                    List<OrgThirdDto> buChildrenList = buChildren.getChildren();
                    if (CollectionUtil.isNotEmpty(buChildrenList)) {
                        leaderList.addAll(buChildrenList.stream().map(OrgThirdDto::getId).collect(Collectors.toList()));
                    }
                });
            }
        }
        return leaderList.stream().distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    /**
     * 根据岗位 根据岗位，获取分享类型
     * @param position
     * @return
     */
    private LastReachTypeEnum getShareType(String position) {
        if (StringUtils.isBlank(position)) {
            return null;
        }

        if(PositionUtil.isBusinessSaler(position)) {
            return LastReachTypeEnum.BUSINESS_SEND;
        }else if(PositionUtil.isBusinessManager(position)) {
            return LastReachTypeEnum.MANAGER_SEND;
        }else if(PositionUtil.isBusinessBu(position)) {
            return LastReachTypeEnum.DIVISION_DIRECTOR_SEND;
        }else if(PositionUtil.isBusinessMajor(position)) {
            return LastReachTypeEnum.BRANCH_DIRECTOR_SEND;
        }else {
            return null;
        }
    }

    /**
     * 根据岗位 根据岗位，获取拜访类型
     * @param position
     * @return
     */
    private LastReachTypeEnum getVisitType(String position) {
        if (StringUtils.isBlank(position)) {
            return null;
        }

        if(PositionUtil.isBusinessSaler(position)) {
            return LastReachTypeEnum.BUSINESS_VISIT;
        }else if(PositionUtil.isBusinessManager(position)) {
            return LastReachTypeEnum.MANAGER_VISIT;
        }else if(PositionUtil.isBusinessBu(position)) {
            return LastReachTypeEnum.DIVISION_DIRECTOR_VISIT;
        }else if(PositionUtil.isBusinessMajor(position)) {
            return LastReachTypeEnum.BRANCH_DIRECTOR_VISIT;
        }else {
            return null;
        }
    }

    /**
     * 获取原因
     * @param customerLayer
     * @return
     */
    private String getReason(Integer customerLayer) {
        if (customerLayer == null) {
            return null;
        }

        switch (customerLayer) {
            case 1://CustomerLayerEnum.VIP.getCode()
                return "一年内未拜访";
            case 2://CustomerLayerEnum.GENERAL.getCode()
                return "三个月未触达";
            case 4://CustomerLayerEnum.HIGH_VALUE.getCode()
                return "三个月未拜访";
            default:
                return "";
        }

    }

    /**
     * 是否是同一天
     * @param date1
     * @param date2
     * @return
     */
    private boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }

        Calendar cal1 = new GregorianCalendar();
        cal1.setTime(date1);
        Calendar cal2 = new GregorianCalendar();
        cal2.setTime(date2);

        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
                cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }

}
