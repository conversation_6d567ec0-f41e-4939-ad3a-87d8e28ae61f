package com.ce.scrm.center.async.mq.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * description: 表记录变更类型
 *
 * @author: DD.Jiu
 * date: 2023/12/22.
 */
@Getter
@AllArgsConstructor
public enum TableChangeEnum {
    /**
     * 表记录更新
     */
    insert("INSERT", "表记录插入", "DML"),
    /**
     * 表记录更新
     */
    update("UPDATE", "表记录更新", "DML"),
    /**
     * 表记录删除
     */
    delete("DELETE", "表记录删除", "DML");

    /**
     * binlog记录的操作类型
     */
    private final String field;
    /**
     * 描述
     */
    private final String description;
    /**
     * binlog记录的操作类型：DML OR DDL
     */
    private final String fieldType;

    public static TableChangeEnum getTableChangeEnumByField(String field) {
        return Arrays.stream(TableChangeEnum.values()).filter(T -> T.getField().equalsIgnoreCase(field)).findAny().orElse(null);
    }

}
