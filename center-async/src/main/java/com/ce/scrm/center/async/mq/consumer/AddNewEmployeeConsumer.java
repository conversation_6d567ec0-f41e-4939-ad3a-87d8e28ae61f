package com.ce.scrm.center.async.mq.consumer;

import cn.ce.cesupport.emp.enums.EmployeeChangeEnum;
import cn.ce.cesupport.emp.service.EmpChangeAppService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.mq.entity.SegmentMessageInfo;
import com.ce.scrm.center.dao.entity.AiAccessAuth;
import com.ce.scrm.center.dao.entity.SegmentDetail;
import com.ce.scrm.center.dao.service.AiAccessAuthService;
import com.ce.scrm.center.service.business.SegmentBusiness;
import com.ce.scrm.center.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 新员工入职处理
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CESUPPORT_GLOBAL_EMP_CHANGE_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_ADD_NEW_EMPLOYEE_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class AddNewEmployeeConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private AiAccessAuthService aiAccessAuthService;

    @DubboReference
    private EmpChangeAppService empChangeAppService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private static final String ORG_TYPE_KEY = "CRM:AI:org:employee:list";

    @Override
    public void onMessage(MessageExt message) {
        try {
            String empMqStr = new String(message.getBody());
            if(empMqStr.contains("old_chenxing") || empMqStr.contains("old_xinnet")){
                // 不处理新网和晨星的员工异动
                return;
            }
            log.info("AddNewEmployeeConsumer Receive message,messageId={},message={}",message.getMsgId(),empMqStr);
            EmployeeChangeEnum empChangeEnum = empChangeAppService.getEmpChangeEnumNew(empMqStr);
            if (empChangeEnum == null || !Objects.equals(EmployeeChangeEnum.ENTER.getValue(), empChangeEnum.getValue())) {
                return;
            }
            JSONObject jsonObject = JSONObject.parseObject(empMqStr);
            String empId = jsonObject.getString("empId");
            AiAccessAuth aiAccessAuth = aiAccessAuthService.lambdaQuery().eq(AiAccessAuth::getEmployeeId, empId).last("limit 1").one();
            if (aiAccessAuth == null) {
                Date currentDate = new Date();
                AiAccessAuth newAiAccessAuth = new AiAccessAuth();
                newAiAccessAuth.setEmployeeId(empId);
                newAiAccessAuth.setOperator("admin");
                newAiAccessAuth.setCreateTime(currentDate);
                newAiAccessAuth.setUpdateTime(currentDate);
                aiAccessAuthService.save(newAiAccessAuth);
//                log.error("新员工入职,添加权限,empId={}",empId);
                // 删除树的缓存
                stringRedisTemplate.opsForValue().set(ORG_TYPE_KEY + ":" + 1, "null", 1, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("员工异动-新员工入职出错", e);
        }


    }
}
