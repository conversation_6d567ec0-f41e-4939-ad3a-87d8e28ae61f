package com.ce.scrm.center.async.job.handler;

import cn.ce.cesupport.sma.constant.ApplyCooperationConstants;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dao.entity.ApplyCooperation;
import com.ce.scrm.center.dao.entity.SmaDictionaryItem;
import com.ce.scrm.center.dao.service.ApplyCooperationService;
import com.ce.scrm.center.dao.service.SmaDictionaryItemService;
import com.ce.scrm.center.service.business.CooperationBusiness;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.constant.SmsTemplateConstants;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @version 1.0
 * @Description: 商务合作定时任务
 * @Author: lijinpeng
 * @Date: 2024/12/30 09:40
 */
@Slf4j
@Component
public class CooperationJobHandler {

    @Resource
    private ApplyCooperationService applyCooperationService;

    @Resource
    private CooperationBusiness cooperationBusiness;

    @Resource
    private SmaDictionaryItemService smaDictionaryItemService;

    @Resource
    private SendWxMessage sendWxMessage;

    @Resource
    private CustomerThirdService customerThirdService;


    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_COOPERATION_PERIOD_VALIDITY_SEVEN_DAY_REMIND)
    public ReturnT<String> getHoliday(String param) {
        log.info("===========合作商务约定时间剩余七天提醒，任务开始==========");
        Calendar calendar = Calendar.getInstance(); // 获取当前日期和时间
        calendar.add(Calendar.DATE, +7); // 减去8天
        Date date1 = calendar.getTime(); // 获取计算后的日期
        calendar.add(Calendar.DATE, +1); // 减去8天
        Date date2 = calendar.getTime(); // 获取计算后的日期

        List<ApplyCooperation> list = applyCooperationService.lambdaQuery()
                .eq(ApplyCooperation::getState, ApplyCooperationConstants.HEZUOZHONG)
                .ge(ApplyCooperation::getExceedTime, date1)
                .lt(ApplyCooperation::getExceedTime, date2)
                .list();
        if(CollectionUtil.isEmpty(list)) {
            log.info("合作商务约定时间剩余七天提醒，今日无提醒");
            return ReturnT.SUCCESS;
        }
        log.info("合作商务约定时间剩余七天提醒，listSize={}", list.size());

        SmaDictionaryItem smaDictionaryItem = smaDictionaryItemService.lambdaQuery().eq(SmaDictionaryItem::getCode, SmsTemplateConstants.SCRM_COOPERATION_PERIOD_VALIDITY_SEVEN_DAY_REMIND_MESSAGE).one();
        if(smaDictionaryItem == null) {
            log.error("获取企微消息字典模版失败,code={}",SmsTemplateConstants.SCRM_COOPERATION_PERIOD_VALIDITY_SEVEN_DAY_REMIND_MESSAGE);
            throw new RuntimeException("获取企微消息字典模版失败");
        }

        for (ApplyCooperation applyCooperation : list) {
            if(applyCooperation.getCooperationSalerId() == null || applyCooperation.getProtectSalerId() == null) {
                log.error("合作商务约定时间剩余七天提醒，数据有问题，applyCooperation={}", JSON.toJSONString(applyCooperation));
                continue;
            }
            List<String> userIdList = cooperationBusiness.buildProcessUserIdListData(applyCooperation.getCooperationSalerId(), applyCooperation.getProtectSalerId());
            userIdList.add(applyCooperation.getCooperationSalerId());
            userIdList.add(applyCooperation.getProtectSalerId());

            String custId = applyCooperation.getCustId();
            if(StringUtils.isEmpty(custId)) {
                log.error("合作商务约定时间剩余七天提醒,custId为空");
                continue;
            }
            Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(custId);
            if (!customerData.isPresent()) {
                log.error("合作商务约定时间剩余七天提醒,客户找不到,custId={}", custId);
                continue;
            }

            String message = MessageFormat.format(smaDictionaryItem.getName(), customerData.get().getCustomerName());
            log.info("合作商务约定时间剩余七天提醒,message={}",message);
            userIdList.forEach(userId -> {
                log.info("合作商务约定时间剩余七天提醒,userId={}", userId);
                sendWxMessage.sendMessage(userId,message);
            });
        }

        log.info("===========合作商务约定时间剩余七天提醒，任务结束==========");
        return ReturnT.SUCCESS;
    }

}
