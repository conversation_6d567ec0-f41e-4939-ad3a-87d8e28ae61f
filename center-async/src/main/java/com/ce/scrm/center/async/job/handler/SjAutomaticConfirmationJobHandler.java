package com.ce.scrm.center.async.job.handler;

import cn.ce.cecloud.business.entity.BusinessOpportunity;
import cn.ce.cecloud.business.service.TelBusinessAppService;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.ce.cesupport.sma.service.SendMessageAppService;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.constant.SmsTemplateConstants;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @version 1.0
 * @Description: 商机48小时内提醒确认
 * @Author: lijinpeng
 * @Date: 2025/3/27 16:33
 */
@Slf4j
@Component
public class SjAutomaticConfirmationJobHandler {

    @DubboReference
    private TelBusinessAppService telBusinessAppService;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_SJ_AUTOMATIC_CONFIRMATION_JOB_HANDLER)
    public ReturnT<String> sjAutomaticConfirmation(String param) {
        log.info("===========商机超过48小时自动确认,开始==========");

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -2);
        Date startTime = calendar.getTime();

        log.info("sjAutomaticConfirmation,startTime={}", startTime);
        List<BusinessOpportunity> businessOpportunityList = telBusinessAppService.getNotAcceptBusinessOpportunity(startTime);
        String opportunityHandleStatus = "OPP_HANDLE_STATUS_14";
        if(CollectionUtils.isEmpty(businessOpportunityList)) {
            return ReturnT.SUCCESS;
        }
        log.info("sjAutomaticConfirmation,size={}", businessOpportunityList.size());
        for (BusinessOpportunity businessOpportunity : businessOpportunityList) {
            businessOpportunity.setAcceptFlag(YesOrNoEnum.YES.getCode());
            businessOpportunity.setHandleResult(opportunityHandleStatus);
            telBusinessAppService.updateBusinessOpportunity(businessOpportunity);
            // 记录操作日志
            telBusinessAppService.addSjOperateLog(businessOpportunity.getId(),businessOpportunity.getBusiOppoCode(),businessOpportunity.getStatus(),"1024","系统",opportunityHandleStatus);
        }


        log.info("===========商机超过48小时自动确认,结束==========");
        return ReturnT.SUCCESS;
    }

    @NotNull
    private static String getRemindTime(BusinessOpportunity businessOpportunity) {
        Date assignTime = businessOpportunity.getAssignTime();
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(assignTime);
        // 加48小时
        calendar2.add(Calendar.HOUR_OF_DAY, 48);
        // 提取月、日、时、分
        int month = calendar2.get(Calendar.MONTH) + 1; // 月份从0开始，需+1
        int day = calendar2.get(Calendar.DAY_OF_MONTH);
        int hour = calendar2.get(Calendar.HOUR_OF_DAY); // 24小时制
        int minute = calendar2.get(Calendar.MINUTE);
        String time = month+"月"+day+"日"+hour+"时"+minute+"分";
        return time;
    }

}
