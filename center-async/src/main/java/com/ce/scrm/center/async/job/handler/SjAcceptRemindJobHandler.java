package com.ce.scrm.center.async.job.handler;

import cn.ce.cecloud.business.entity.BusinessOpportunity;
import cn.ce.cecloud.business.service.TelBusinessAppService;
import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.sma.service.SendMessageAppService;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.business.SmaDictionaryItemBusiness;
import com.ce.scrm.center.service.business.entity.view.SmaDictionaryItemView;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.constant.SmsTemplateConstants;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.extend.dubbo.enums.ReleaseBussinessTagEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;

/**
 * @version 1.0
 * @Description: 商机48小时内提醒确认
 * @Author: lijinpeng
 * @Date: 2025/3/27 16:33
 */
@Slf4j
@Component
public class SjAcceptRemindJobHandler {

    @DubboReference
    private SendMessageAppService sendMessageAppService;

    @DubboReference
    private TelBusinessAppService telBusinessAppService;

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    SmaDictionaryItemBusiness smaDictionaryItemBusiness;

    @Resource
    private SendWxMessage sendWxMessage;

    /*
    时间计算逻辑：
        48小时超时系统自动确认，所以想提醒大于24小于48小时的
        所以：24 < nowTime - assignTime < 48
        为了求 assignTime 应该什么范围
        所以：-24 > assignTime - nowTime > -48
        所以：nowTime - 24 > assignTime > nowTime - 48

       定时任务触发:每天17：00 但是为了提前一个小时提醒所以开始计算从18：00开始算  这样就给提醒的人留一个小时时间了 但是会大于23小时
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_SJ_REMIND_JOB_HANDLER)
    public ReturnT<String> sjAcceptRemind(String param) {
        log.info("===========商机48小时内提醒确认开始==========");

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 18);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date yesterday6pm = calendar.getTime();
        // 获取前两天下午六点
        calendar.add(Calendar.DAY_OF_MONTH, -1); // 继续减1天
        Date twoDaysAgo6pm = calendar.getTime();

        List<BusinessOpportunity> businessOpportunityList = telBusinessAppService.getRemindBusinessOpportunityByAssignTime(twoDaysAgo6pm, yesterday6pm);
        if (CollectionUtils.isEmpty(businessOpportunityList)) {
            return ReturnT.SUCCESS;
        }

        SmaDictionaryItemView smaDictionaryItemView = null;
        Optional<SmaDictionaryItemView> byCode = smaDictionaryItemBusiness.findByCode(SmsTemplateConstants.SCRM_SMS_481290062);
        if (byCode.isPresent()) {
            smaDictionaryItemView = byCode.get();
        }

        for (BusinessOpportunity businessOpportunity : businessOpportunityList) {
            String custName = businessOpportunity.getCustName();
            String remindTime = getRemindTime(businessOpportunity);

            String phone = null;
            String sendEmpId = null;
            String areaId = null;
            if (StringUtils.isBlank(businessOpportunity.getBusiOppoCode())) {
                log.warn("商机48小时内提醒确认,商机code不存在，businessOpportunity={}",JSONObject.toJSONString(businessOpportunity));
                return ReturnT.SUCCESS;
            }
            CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getBusioppoCode, businessOpportunity.getBusiOppoCode()).last("limit 1").one();
            if (cmCustProtect == null) {
                log.warn("商机48小时内提醒确认,保护关系不存在,businessOpportunity={}",JSONObject.toJSONString(businessOpportunity));
                return ReturnT.SUCCESS;
            }
            Integer status = cmCustProtect.getStatus();
            if (Objects.equals(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState(), status)) {
                Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(cmCustProtect.getSubcompanyId());
                if (orgLeader.isPresent()) {
                    sendEmpId = orgLeader.get().getId();
                    areaId = orgLeader.get().getAreaId();
                    phone = orgLeader.get().getBindMobile();
                }
            }else if (Objects.equals(ProtectStateEnum.BU_WILL_ASSIGN.getState(), status)) {
                Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(cmCustProtect.getBuId());
                if (orgLeader.isPresent()) {
                    sendEmpId = orgLeader.get().getId();
                    areaId = orgLeader.get().getAreaId();
                    phone = orgLeader.get().getBindMobile();
                }
            }else if (Objects.equals(ProtectStateEnum.MANAGER_WILL_ASSIGN.getState(), status)) {
                Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(cmCustProtect.getBussdeptId());
                if (orgLeader.isPresent()) {
                    sendEmpId = orgLeader.get().getId();
                    areaId = orgLeader.get().getAreaId();
                    phone = orgLeader.get().getBindMobile();
                }
            } else if (Objects.equals(ProtectStateEnum.PROTECT.getState(), status)) {
                Optional<EmployeeInfoThirdDto> employeeByEmpId = employeeThirdService.getEmployeeByEmpId(cmCustProtect.getSalerId());
                if (employeeByEmpId.isPresent()) {
                    sendEmpId = employeeByEmpId.get().getId();
                    areaId = employeeByEmpId.get().getAreaId();
                    phone = employeeByEmpId.get().getBindMobile();
                }
            } else {
                log.error("商机48小时内提醒确认,状态异常,cmCustProtect={}",JSONObject.toJSONString(cmCustProtect));
                return ReturnT.SUCCESS;
            }
            if (PositionUtil.isKj(areaId)) {
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("name", custName);
                paramMap.put("time", remindTime);
                log.info("商机48小时内提醒确认：phone={},paramMap={}",phone, JSONObject.toJSONString(paramMap));
                if (StringUtils.isBlank(custName) || StringUtils.isBlank(phone)) {
                    log.error("商机48小时内参数有误：phone={},paramMap={}",phone, JSONObject.toJSONString(paramMap));
                    return ReturnT.SUCCESS;
                }
                sendMessageAppService.sendMessage(SmsTemplateConstants.SMS_481290062,phone,paramMap);


                if(smaDictionaryItemView != null && sendEmpId != null) {
                    String format = MessageFormat.format(smaDictionaryItemView.getName(), custName, remindTime);
                    log.info("发送的微信消息提醒为：{}", format);
                    sendWxMessage.sendMessage(sendEmpId, format);
                }
            }
        }


        log.info("===========商机48小时内提醒确认结束==========");
        return ReturnT.SUCCESS;
    }

    @NotNull
    private static String getRemindTime(BusinessOpportunity businessOpportunity) {
        Date assignTime = businessOpportunity.getAssignTime();
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(assignTime);
        // 加48小时
        calendar2.add(Calendar.HOUR_OF_DAY, 48);
        // 提取月、日、时、分
        int month = calendar2.get(Calendar.MONTH) + 1; // 月份从0开始，需+1
        int day = calendar2.get(Calendar.DAY_OF_MONTH);
        int hour = calendar2.get(Calendar.HOUR_OF_DAY); // 24小时制
        int minute = calendar2.get(Calendar.MINUTE);
        String time = month+"月"+day+"日"+hour+"时"+minute+"分";
        return time;
    }

}
