package com.ce.scrm.center.async.job.handler;

import cn.ce.cesupport.ehr.entity.BusinessWeekData;
import cn.ce.cesupport.ehr.service.BusinessWeekAppService;
import cn.ce.cesupport.emp.enums.OrgTypeEnum;
import cn.ce.cesupport.emp.enums.StateEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ce.scrm.center.dao.entity.CurrentBusinessMonthTemporaryTable;
import com.ce.scrm.center.dao.entity.DwsZqPerformanceOrgProductSummary;
import com.ce.scrm.center.dao.entity.ZqCustomerFollowStageSummary;
import com.ce.scrm.center.dao.entity.ZqPerformancePredictSummary;
import com.ce.scrm.center.dao.entity.view.CustomerFollowMoneySummaryView;
import com.ce.scrm.center.dao.service.CurrentBusinessMonthTemporaryTableService;
import com.ce.scrm.center.dao.service.CustomerFollowService;
import com.ce.scrm.center.dao.service.DwsZqPerformanceOrgProductSummaryService;
import com.ce.scrm.center.dao.service.ZqPerformancePredictSummaryService;
import com.ce.scrm.center.service.business.entity.view.CurrentBusinessMonthView;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.ZqSummaryOrgTypeEnum;
import com.ce.scrm.center.service.third.entity.dto.OrgConditionThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.invoke.AchForOtherThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计算 业绩预测汇总表
 * 每小时计算一次
 * 总监工作台展示需要
 */
@Slf4j
@Component
public class ZqPerformancePredictSummaryJobHandler {

    @Resource
    private AchForOtherThirdService achForOtherThirdService;

    @Resource
    private OrgThirdService orgThirdService;

    @Resource
    private CustomerFollowService customerFollowService;

    @Resource
    private ZqPerformancePredictSummaryService zqPerformancePredictSummaryService;

    @Resource
    private DwsZqPerformanceOrgProductSummaryService dwsZqPerformanceOrgProductSummaryService;
    
    @DubboReference
    private BusinessWeekAppService businessWeekAppService;

    @Resource
    private CurrentBusinessMonthTemporaryTableService currentBusinessMonthTemporaryTableService;

    @Resource
    private ZqCustomerFollowStageSummaryJobHandler zqCustomerFollowStageSummaryJobHandler;

    /**
     * 定时计算 每一小时一次
     * @param param
     * @return
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_ZQ_PERFORMANCE_PREDICT_SUMMARY_JOB_HANDLER)
    public ReturnT<String> calculate(String param) {

        log.info("业绩预测汇总表 ZqCustomerFollowStageSummaryJobHandler，start");
        try {
            CurrentBusinessMonthView currentBusinessMonth = achForOtherThirdService.getCurrentBusinessMonth();

            Date currentDate = new Date();
            if (currentDate.after(currentBusinessMonth.getEndDate())) {
                log.warn("当前商务月已经过时，无需计算,currentBusinessMonth={}", JSONObject.toJSONString(currentBusinessMonth));
                return ReturnT.SUCCESS;
            }
            calculateStart(currentBusinessMonth);
        }catch (Exception e){
            log.error("ZqCustomerFollowStageSummaryJobHandler,calculate 任务失败",e);
        }
        log.info("业绩预测汇总表 ZqCustomerFollowStageSummaryJobHandler，end");

        return ReturnT.SUCCESS;
    }

    /**
     * 月结计算两个表
     * @param param
     * @return
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_ZQ_CUSTOMER_MONTH_SUMMARY_JOB_HANDLER)
    public ReturnT<String> monthCalculate(String param) {

        log.info("中企跟进记录按照销售阶段客户数汇总表 月结 ZqCustomerFollowStageSummaryJobHandler，start");
        try {
            LambdaQueryWrapper<CurrentBusinessMonthTemporaryTable> queryWrapper = new LambdaQueryWrapper<CurrentBusinessMonthTemporaryTable>()
                    .eq(CurrentBusinessMonthTemporaryTable::getFlag, 0)
                    .isNotNull(CurrentBusinessMonthTemporaryTable::getEndDate)
                    .lt(CurrentBusinessMonthTemporaryTable::getEndDate, new Date());
            List<CurrentBusinessMonthTemporaryTable> list = currentBusinessMonthTemporaryTableService.list(queryWrapper);
            for (CurrentBusinessMonthTemporaryTable currentBusinessMonthTemporaryTable : list) {
                CurrentBusinessMonthView businessMonth = BeanUtil.copyProperties(currentBusinessMonthTemporaryTable, CurrentBusinessMonthView.class);
                zqCustomerFollowStageSummaryJobHandler.calculateStart(businessMonth);
                calculateStart(businessMonth);
                currentBusinessMonthTemporaryTable.setFlag(1);
                currentBusinessMonthTemporaryTableService.updateById(currentBusinessMonthTemporaryTable);
            }
        }catch (Exception e){
            log.error("ZqCustomerFollowStageSummaryJobHandler,calculate 任务失败",e);
        }
        log.info("中企跟进记录按照销售阶段客户数汇总表 月结 ZqCustomerFollowStageSummaryJobHandler，end");

        return ReturnT.SUCCESS;

    }

    private void calculateStart(CurrentBusinessMonthView currentBusinessMonth) {
        long l1 = System.currentTimeMillis();

        List<OrgThirdDto> areaList = orgThirdService.getOrgChildrenByCondition(
                OrgConditionThirdDto.builder()
                        .state(StateEnum.Enable.getValue())
                        .type(OrgTypeEnum.AREA.getType())
                        .build()
        );
        Map<String, String> areaIdMap = areaList.stream().collect(Collectors.toMap(OrgThirdDto::getId, OrgThirdDto::getName, (key1, key2) -> key2));

        List<OrgThirdDto> subInfoList = orgThirdService.getOrgChildrenByCondition(
                OrgConditionThirdDto.builder()
                        .state(StateEnum.Enable.getValue())
                        .type(OrgTypeEnum.SUB.getType()).build()
        );
        // 去除跨境
        List<String> notAreaIdList = Arrays.asList("3950","4343");
        subInfoList = subInfoList.stream().filter(s -> !notAreaIdList.contains(s.getParentId())).collect(Collectors.toList());

        Date nextNaturalMonthLastDay = getNextNaturalMonthLastDay(currentBusinessMonth.getBussinessMonth());
        List<CustomerFollowMoneySummaryView> zqPerformancePredictSummary = customerFollowService.getZqPerformancePredictSummary(
                currentBusinessMonth.getBeginDate(), currentBusinessMonth.getEndDate(),
                currentBusinessMonth.getEndDate(), nextNaturalMonthLastDay);
        Map<String, CustomerFollowMoneySummaryView> subSummaryMap = zqPerformancePredictSummary.stream().collect(
                        Collectors.toMap(CustomerFollowMoneySummaryView::getSubId, Function.identity(), (key1, key2) -> key2));

        // 本月业绩
        List<DwsZqPerformanceOrgProductSummary> performanceMonthList = dwsZqPerformanceOrgProductSummaryService.lambdaQuery()
                .eq(DwsZqPerformanceOrgProductSummary::getBusinessMonth, currentBusinessMonth.getBussinessMonth())
                .in(DwsZqPerformanceOrgProductSummary::getOrgType, Arrays.asList(ZqSummaryOrgTypeEnum.NATIONWIDE.getCode(),ZqSummaryOrgTypeEnum.AREA.getCode(),ZqSummaryOrgTypeEnum.SUB.getCode()))
                .eq(DwsZqPerformanceOrgProductSummary::getSummaryType, 1)
                .eq(DwsZqPerformanceOrgProductSummary::getFlag, 1)
                .list();
        Map<Integer, List<DwsZqPerformanceOrgProductSummary>> performanceMonthGroupByMap = performanceMonthList.stream().collect(Collectors.groupingBy(DwsZqPerformanceOrgProductSummary::getOrgType));
        // 本月业绩-全国
        List<DwsZqPerformanceOrgProductSummary> orDefault1 = performanceMonthGroupByMap.getOrDefault(ZqSummaryOrgTypeEnum.NATIONWIDE.getCode(), Collections.emptyList());
        BigDecimal performanceNationwideMonth = CollectionUtil.isEmpty(orDefault1) || orDefault1.get(0) == null  ? BigDecimal.valueOf(0) : BigDecimal.valueOf(orDefault1.get(0).getPerformanceMonth());
        // 本月业绩-区域
        Map<String, BigDecimal> performanceMonthAreaMap = performanceMonthGroupByMap.getOrDefault(ZqSummaryOrgTypeEnum.AREA.getCode(), Collections.emptyList())
                .stream().collect(
                        Collectors.toMap(
                                DwsZqPerformanceOrgProductSummary::getAreaId,
                                e -> e.getPerformanceMonth() != null ? BigDecimal.valueOf(e.getPerformanceMonth()) : new BigDecimal("0"),
                                (key1, key2) -> key2)
                );
        // 本月业绩-分司
        Map<String, BigDecimal> performanceMonthSubMap = performanceMonthGroupByMap.getOrDefault(ZqSummaryOrgTypeEnum.SUB.getCode(), Collections.emptyList())
                .stream().collect(
                Collectors.toMap(
                        DwsZqPerformanceOrgProductSummary::getSubId,
                        e -> e.getPerformanceMonth() != null ? BigDecimal.valueOf(e.getPerformanceMonth()) : new BigDecimal("0"),
                        (key1, key2) -> key2)
                );

        // 分司赋值
        List<ZqPerformancePredictSummary> subResultList = new ArrayList<>();
        for (OrgThirdDto subInfo : subInfoList) {

            String subId = subInfo.getId();

            ZqPerformancePredictSummary.ZqPerformancePredictSummaryBuilder itemBuild = ZqPerformancePredictSummary.builder()
                    .businessMonth(currentBusinessMonth.getBussinessMonth())
                    .orgType(ZqSummaryOrgTypeEnum.SUB.getCode())
                    .areaId(subInfo.getParentId())
                    .areaName(areaIdMap.getOrDefault(subInfo.getParentId(), null))
                    .subId(subId)
                    .subName(subInfo.getName())
                    .flag(0);

            CustomerFollowMoneySummaryView orDefault = subSummaryMap.getOrDefault(subId, new CustomerFollowMoneySummaryView());

            // 公式: 业绩缺口 = 业绩目标 - 本月累计业绩 - 本月预估到账
            
            BigDecimal performanceTargetMonth = getPerformanceTargetMonth(currentBusinessMonth.getBussinessMonth(),"SUB", subId);
            BigDecimal performanceMonth = performanceMonthSubMap.getOrDefault(subId, BigDecimal.valueOf(0));
            BigDecimal businessMoneyAccountSum = orDefault.getBusinessMoneyAccountSum() != null ? orDefault.getBusinessMoneyAccountSum() : BigDecimal.valueOf(0);
            BigDecimal performanceGapMonth = performanceTargetMonth.subtract(performanceMonth).subtract(businessMoneyAccountSum);

            itemBuild.performanceGapMonth(performanceGapMonth);
            itemBuild.performanceTargetMonth(performanceTargetMonth);
            itemBuild.signedAmtPredictMonth(orDefault.getBusinessMoneySignSum() != null ? orDefault.getBusinessMoneySignSum() : BigDecimal.valueOf(0));
            itemBuild.signedCustomerCountPredictMonth(orDefault.getBusinessCustomerSignCount() != null ? orDefault.getBusinessCustomerSignCount() : 0);
            itemBuild.receivedAmtPredictMonth(businessMoneyAccountSum);
            itemBuild.receivedCustomerCountPredictMonth(orDefault.getBusinessCustomerAccountCount() != null ? orDefault.getBusinessCustomerAccountCount() : 0);
            itemBuild.signedAmtPredictNextMonth(orDefault.getNaturalMoneySignSum() != null ? orDefault.getNaturalMoneySignSum() : BigDecimal.valueOf(0));
            itemBuild.receivedAmtPredictNextMonth(orDefault.getNaturalMoneyAccountSum() != null ? orDefault.getNaturalMoneyAccountSum() : BigDecimal.valueOf(0));
            itemBuild.performanceMonth(performanceMonth);

            subResultList.add(itemBuild.build());
        }

        // 区域
        List<ZqPerformancePredictSummary> areaResultList = new ArrayList<>(subResultList.stream().collect(Collectors.toMap(ZqPerformancePredictSummary::getAreaId, e -> {

            ZqPerformancePredictSummary item = BeanUtil.copyProperties(e, ZqPerformancePredictSummary.class);
            item.setOrgType(ZqSummaryOrgTypeEnum.AREA.getCode());
            item.setSubId(null);
            item.setSubName(null);
            return item;

        }, (e1, e2) -> {

            ZqPerformancePredictSummary item = BeanUtil.copyProperties(e1, ZqPerformancePredictSummary.class);

            item.setSignedAmtPredictMonth(e1.getSignedAmtPredictMonth().add(e2.getSignedAmtPredictMonth()));
            item.setSignedCustomerCountPredictMonth(e1.getSignedCustomerCountPredictMonth() + e2.getSignedCustomerCountPredictMonth());
            item.setReceivedAmtPredictMonth(e1.getReceivedAmtPredictMonth().add(e2.getReceivedAmtPredictMonth()));
            item.setReceivedCustomerCountPredictMonth(e1.getReceivedCustomerCountPredictMonth() + e2.getReceivedCustomerCountPredictMonth());
            item.setSignedAmtPredictNextMonth(e1.getSignedAmtPredictNextMonth().add(e2.getSignedAmtPredictNextMonth()));
            item.setReceivedAmtPredictNextMonth(e1.getReceivedAmtPredictNextMonth().add(e2.getReceivedAmtPredictNextMonth()));
            return item;
        })).values());

        BigDecimal nationwidePerformanceGapMonth = BigDecimal.ZERO;
        BigDecimal nationwidePerformanceTargetMonth = BigDecimal.ZERO;
        BigDecimal nationwideSignedAmtPredictMonth = BigDecimal.ZERO;
        int nationwideSignedCustomerCountPredictMonth = 0;
        BigDecimal nationwideReceivedAmtPredictMonth = BigDecimal.ZERO;
        int nationwideReceivedCustomerCountPredictMonth = 0;
        BigDecimal nationwideSignedAmtPredictNextMonth = BigDecimal.ZERO;
        BigDecimal nationwideReceivedAmtPredictNextMonth = BigDecimal.ZERO;
        for (ZqPerformancePredictSummary item : areaResultList) {

            // 公式: 业绩缺口 = 业绩目标 - 本月累计业绩 - 本月预估到账
            BigDecimal performanceTargetMonth = getPerformanceTargetMonth(currentBusinessMonth.getBussinessMonth(),"AREA", item.getAreaId()); // todo
            BigDecimal performanceMonth = performanceMonthAreaMap.getOrDefault(item.getAreaId(), BigDecimal.valueOf(0));
            BigDecimal businessMoneyAccountSum = item.getReceivedAmtPredictMonth();
            BigDecimal performanceGapMonth = performanceTargetMonth.subtract(performanceMonth).subtract(businessMoneyAccountSum);

            item.setPerformanceGapMonth(performanceGapMonth);
            item.setPerformanceTargetMonth(performanceTargetMonth);
            item.setPerformanceMonth(performanceMonth);

            nationwidePerformanceGapMonth = nationwidePerformanceGapMonth.add(item.getPerformanceGapMonth());
            nationwidePerformanceTargetMonth = nationwidePerformanceTargetMonth.add(item.getPerformanceTargetMonth());
            nationwideSignedAmtPredictMonth = nationwideSignedAmtPredictMonth.add(item.getSignedAmtPredictMonth());
            nationwideSignedCustomerCountPredictMonth = nationwideSignedCustomerCountPredictMonth + item.getSignedCustomerCountPredictMonth();
            nationwideReceivedAmtPredictMonth = nationwideReceivedAmtPredictMonth.add(item.getReceivedAmtPredictMonth());
            nationwideReceivedCustomerCountPredictMonth = nationwideReceivedCustomerCountPredictMonth + item.getReceivedCustomerCountPredictMonth();
            nationwideSignedAmtPredictNextMonth = nationwideSignedAmtPredictNextMonth.add(item.getSignedAmtPredictNextMonth());
            nationwideReceivedAmtPredictNextMonth = nationwideReceivedAmtPredictNextMonth.add(item.getReceivedAmtPredictNextMonth());
        }

        ZqPerformancePredictSummary nationwideResultObject = ZqPerformancePredictSummary.builder()
                .businessMonth(currentBusinessMonth.getBussinessMonth())
                .orgType(ZqSummaryOrgTypeEnum.NATIONWIDE.getCode())
                .areaId(null)
                .areaName(null)
                .subId(null)
                .subName(null)
                .performanceGapMonth(nationwidePerformanceGapMonth)
                .performanceTargetMonth(nationwidePerformanceTargetMonth)
                .signedAmtPredictMonth(nationwideSignedAmtPredictMonth)
                .signedCustomerCountPredictMonth(nationwideSignedCustomerCountPredictMonth)
                .receivedAmtPredictMonth(nationwideReceivedAmtPredictMonth)
                .receivedCustomerCountPredictMonth(nationwideReceivedCustomerCountPredictMonth)
                .signedAmtPredictNextMonth(nationwideSignedAmtPredictNextMonth)
                .receivedAmtPredictNextMonth(nationwideReceivedAmtPredictNextMonth)
                .performanceMonth(performanceNationwideMonth)
                .flag(0)
                .build();

        List<ZqPerformancePredictSummary> resultList = new ArrayList<>();
        resultList.add(nationwideResultObject);
        resultList.addAll(areaResultList);
        resultList.addAll(subResultList);


        zqPerformancePredictSummaryService.remove(new LambdaQueryWrapper<ZqPerformancePredictSummary>().eq(ZqPerformancePredictSummary::getBusinessMonth, currentBusinessMonth.getBussinessMonth()));
        zqPerformancePredictSummaryService.update(new LambdaUpdateWrapper<ZqPerformancePredictSummary>().set(ZqPerformancePredictSummary::getFlag,-1));
        zqPerformancePredictSummaryService.saveBatch(resultList);

        long l2 = System.currentTimeMillis();
        long l = l2 - l1;
        log.info("任务用时: {} ms", l);
    }

    private BigDecimal getPerformanceTargetMonth(String businessMonth,String dataType,String orgId) {

        if (StringUtils.isBlank(businessMonth) || StringUtils.isBlank(dataType) || StringUtils.isBlank(orgId)) {
            throw new RuntimeException("getPerformanceTargetMonth 异常，businessMonth=" + businessMonth + ", dataType=" + dataType + ", orgId=" + orgId);
        }

        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("bussinessMonth",businessMonth);
        objectObjectHashMap.put("dataType",dataType);
        objectObjectHashMap.put("organId",orgId);
        List<cn.ce.cesupport.ehr.entity.BusinessWeekData> businessWeekData = businessWeekAppService.selectDataByMap(objectObjectHashMap);
        if (CollectionUtil.isEmpty(businessWeekData)) {
            return BigDecimal.valueOf(0);
        }else {
            BusinessWeekData businessWeekData1 = businessWeekData.get(0);
            BigDecimal result = new BigDecimal("0");
            if (businessWeekData1.getW1Obj() != null) {
                result = result.add(businessWeekData1.getW1Obj());
            }
            if (businessWeekData1.getW2Obj() != null) {
                result = result.add(businessWeekData1.getW2Obj());
            }
            if (businessWeekData1.getW3Obj() != null) {
                result = result.add(businessWeekData1.getW3Obj());
            }
            if (businessWeekData1.getW4Obj() != null) {
                result = result.add(businessWeekData1.getW4Obj());
            }
            if (businessWeekData1.getW5Obj() != null) {
                result = result.add(businessWeekData1.getW5Obj());
            }
            return result;
        }

    }

    /**
     * 根据当前商务月 获取商务月下个月的自然月的最后一秒  businessMonth = "2025-06"  求 date = "2025-07-31 23:59:59"
     * @param businessMonth
     * @return
     */
    private Date getNextNaturalMonthLastDay(String businessMonth) {
        if (StringUtils.isBlank(businessMonth)) {
            throw new RuntimeException("getNextNaturalMonthLastDay,商务月为空");
        }
        try {
            // 1. 解析字符串为日期（月份）
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            Date date = sdf.parse(businessMonth);

            // 2. 使用Calendar类进行计算
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);

            // 3. 设置为下个月
            calendar.add(Calendar.MONTH, 1);

            // 4. 设置为该月的最后一天
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

            // 5. 设置为最后一秒（23:59:59）
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 0);

            // 6. 获取最终的Date对象
            return calendar.getTime();
        }catch (Exception e){
            log.error("getNextNaturalMonthLastDay,计算错误",e);
            throw new RuntimeException("getNextNaturalMonthLastDay,计算错误");
        }

    }

}
