package com.ce.scrm.center.async.mq.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class AudioList implements Serializable {

    private Long id;

    private List<String> audioUrls;

    private String combineUrl;

    private Long startTime;

    private long consumeTimes;

}
