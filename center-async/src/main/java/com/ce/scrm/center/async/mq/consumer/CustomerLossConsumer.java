package com.ce.scrm.center.async.mq.consumer;

import cn.ce.cesupport.enums.DeleteFlagEnum;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.mq.entity.CustomerKeepAndLossMqData;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerWillCirculation;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.CustomerWillCirculationService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.entity.view.OrgDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * Description: 流失客户消费者
 *
 * @author: liyechao
 * date: 2024/7/18
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_CUSTOMER_KEEP_AND_LOSS_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.SCRM_CUSTOMER_LOSS_GROUP, consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class CustomerLossConsumer implements RocketMQListener<MessageExt> {
    @Resource
    private CmCustProtectService cmCustProtectService;
    @Resource
    private CustomerThirdService customerThirdService;
    @Resource
    private CustomerWillCirculationService customerWillCirculationService;
    @Resource
    private OrgThirdService orgThirdService;
    @Resource
    private EmployeeThirdService employeeThirdService;

    @Override
    public void onMessage(MessageExt messageExt) {
        String data = new String(messageExt.getBody());
        //log.info("流失客户流转MQ消息，msgId={},消息内容：{}", messageExt.getMsgId(), data);
        if (StringUtils.isEmpty(data)) {
            return;
        }
        String custId = "";
        try {
            CustomerKeepAndLossMqData keepAndLossMqData = JSON.parseObject(data, CustomerKeepAndLossMqData.class);
            custId = keepAndLossMqData.getCustId();

            if (StringUtils.isEmpty(custId)) {
                //log.error("自动流失MQ获取到的参数错误，custId不存在，keepAndLossMqData:{}", data);
                return;
            }

            //获取客户详情 读取redis缓存
            Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(custId);
            if (customerData.isPresent()) {
                CustomerDataThirdView customerDataThirdView = customerData.get();
                //是否是流失客户
                Integer tagLostCust = customerDataThirdView.getTagLostCust();
                //是否是保有客户
                Integer tagRetainCust = customerDataThirdView.getTagRetainCust();

                //判断是否是流失客户或者是保有客户
                if ((Objects.nonNull(tagLostCust) && tagLostCust.equals(YesOrNoEnum.YES.getCode()))
                        || (Objects.nonNull(tagRetainCust) && tagRetainCust.equals(YesOrNoEnum.YES.getCode()))) {
                    // 保护关系表 不存在该客户，则不进行流转
                    CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, custId).one();
                    if (Objects.isNull(cmCustProtect)) {
                        //log.info("CustomerLossConsumer不存在保护关系直接返回！custId为：{}", custId);
                        return;
                    }
                    //查询流失流转表
                    CustomerWillCirculation willCirculation = customerWillCirculationService.lambdaQuery()
                            .eq(CustomerWillCirculation::getCustId, custId)
                            .eq(CustomerWillCirculation::getOrigin, AssignCustSourceSpecialEnum.LOSS.getValue())
                            .one();

                    List<String> orgIdList = Arrays.asList(cmCustProtect.getAreaId(), cmCustProtect.getSubcompanyId(), cmCustProtect.getBussdeptId());
                    Map<String, OrgDataThirdView> orgDataThirdViewMap = orgThirdService.getOrgData(orgIdList);

                    if (Objects.isNull(willCirculation)) {
                        willCirculation = new CustomerWillCirculation();
                        willCirculation.setCustId(custId);
                        willCirculation.setOrigin(AssignCustSourceSpecialEnum.LOSS.getValue());
                        willCirculation.setCreateTime(new Date());
                        //未删除
                        willCirculation.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
                        //未分配
                        willCirculation.setStatus(YesOrNoEnum.NO.getCode());
                        //表的基础信息
                        willCirculation.setUpdateTime(new Date());
                        willCirculation.setCustName(cmCustProtect.getCustName());
                        willCirculation.setAreaId(cmCustProtect.getAreaId());
                        willCirculation.setAreaName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getAreaId(), new OrgDataThirdView()).getName());
                        willCirculation.setSubId(cmCustProtect.getSubcompanyId());
                        willCirculation.setSubName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getSubcompanyId(), new OrgDataThirdView()).getName());
                        willCirculation.setDeptId(cmCustProtect.getBussdeptId());
                        willCirculation.setDeptName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBussdeptId(), new OrgDataThirdView()).getName());
                        willCirculation.setSalerId(cmCustProtect.getSalerId());
                        willCirculation.setSalerName(employeeThirdService.getEmployeeData(cmCustProtect.getSalerId()).orElse(new EmployeeDataThirdView()).getName());
                        //流失客户保存流失时间，保有客户保存到期时间。
                        if (YesOrNoEnum.YES.getCode().equals(tagLostCust)) {
                            //流失时间
                            willCirculation.setPreDate(customerDataThirdView.getTagLostTime());
                        } else {
                            //产品最后到期时间
                            willCirculation.setPreDate(customerDataThirdView.getTagRetainTime());
                        }
                        customerWillCirculationService.save(willCirculation);
                    } else{
                        Date preDate = YesOrNoEnum.YES.getCode().equals(tagLostCust) ? customerDataThirdView.getTagLostTime() : customerDataThirdView.getTagRetainTime();
                        //日期有更新 会更新流失表 将其改为未删除和未分配的状态
                        if ((preDate != null && willCirculation.getPreDate() == null)
                            || (preDate == null && willCirculation.getPreDate() != null)
                            || (preDate != null && DateUtil.beginOfDay(preDate).after(willCirculation.getPreDate()))){
                            log.info("CustomerLossConsumer更新流失表！custId为：{} customerDataThirdView:{} willCirculation:{}", custId, JSONObject.toJSONString(customerDataThirdView), JSONObject.toJSONString(willCirculation));
                            customerWillCirculationService.lambdaUpdate().eq(CustomerWillCirculation::getId, willCirculation.getId())
                                    .set(CustomerWillCirculation::getUpdateTime, new Date())
                                    .set(CustomerWillCirculation::getCustName, cmCustProtect.getCustName())
                                    .set(CustomerWillCirculation::getAreaId, cmCustProtect.getAreaId())
                                    .set(CustomerWillCirculation::getAreaName, orgDataThirdViewMap.getOrDefault(cmCustProtect.getAreaId(), new OrgDataThirdView()).getName())
                                    .set(CustomerWillCirculation::getSubId, cmCustProtect.getSubcompanyId())
                                    .set(CustomerWillCirculation::getSubName, orgDataThirdViewMap.getOrDefault(cmCustProtect.getSubcompanyId(), new OrgDataThirdView()).getName())
                                    .set(CustomerWillCirculation::getDeptId, cmCustProtect.getBussdeptId())
                                    .set(CustomerWillCirculation::getDeptName, orgDataThirdViewMap.getOrDefault(cmCustProtect.getBussdeptId(), new OrgDataThirdView()).getName())
                                    .set(CustomerWillCirculation::getSalerId, cmCustProtect.getSalerId())
                                    .set(CustomerWillCirculation::getSalerName, employeeThirdService.getEmployeeData(cmCustProtect.getSalerId()).orElse(new EmployeeDataThirdView()).getName())
                                    .set(CustomerWillCirculation::getPreDate, preDate)
                                    .set(CustomerWillCirculation::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                                    .set(CustomerWillCirculation::getStatus, YesOrNoEnum.NO.getCode())
                                    .update();
                        }

                    }
                }else{
                    //log.info("该客户既不是保有客户也不是流失客户！custId：{}", custId);
                }
            }else{
                //log.info("客户表未找到客户数据！custId：{}", custId);
            }
        } catch (Exception e) {
            log.error("流失客户异常,客户ID={} ", custId, e);
            throw new RuntimeException(e);
        }
    }
}
