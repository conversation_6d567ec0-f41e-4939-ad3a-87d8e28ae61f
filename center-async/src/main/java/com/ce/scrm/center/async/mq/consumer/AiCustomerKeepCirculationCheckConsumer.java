package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson2.JSONObject;
import com.ce.scrm.center.dao.entity.CustomerWillCirculation;
import com.ce.scrm.center.dao.entity.CustomerWillCirculationAi;
import com.ce.scrm.center.dao.service.CustomerWillCirculationAiService;
import com.ce.scrm.center.dao.service.CustomerWillCirculationService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.support.redis.RedisOperator;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Description: 待流转保有客户insert到db 消费者
 * 绝对保护期内的客户：只是insert到待流转表，但是不流转，只在列表展示
 * <p>
 * 流转step 2
 *
 * @author: JiuDD
 * date: 2024/7/17
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_CUSTOMER_KEEP_AND_LOSS_AI_CHECK_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.SCRM_CUSTOMER_KEEP_CONVERT_AI_CHECK_GROUP, consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 1)
public class AiCustomerKeepCirculationCheckConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CustomerWillCirculationAiService customerWillCirculationAiService;

    @Resource
    private CustomerWillCirculationService customerWillCirculationService;

    @Autowired
    private RedisOperator redisOperator;

    private ThreadLocal<String> threadLocal = new ThreadLocal<>();

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public void onMessage(MessageExt messageExt) {
        if(true){
            return;
        }
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            return;
        }
        try {
            threadLocal.set(data);
            JSONObject jsonObject = JSONObject.parseObject(data);
            String dateStr = jsonObject.getString("createDate");
            String custId = jsonObject.getString("custId");
            // 获取该天的开始和结束时间
            List<CustomerWillCirculationAi> aiList = customerWillCirculationAiService.lambdaQuery()
                    .eq(CustomerWillCirculationAi::getCreateDate, dateStr)
                    .eq(CustomerWillCirculationAi::getCustId, custId)
                    .eq(CustomerWillCirculationAi::getOrigin, 16)
                    .list();
            List<CustomerWillCirculation> list = customerWillCirculationService.lambdaQuery()
                    .ge(CustomerWillCirculation::getCreateDate, dateStr)
                    .eq(CustomerWillCirculation::getCustId, custId)
                    .eq(CustomerWillCirculation::getOrigin, 16)
                    .list();
            if (CollectionUtils.isEmpty(aiList) && CollectionUtils.isEmpty(list)) {
                return;
            }
            if (!CollectionUtils.isEmpty(aiList) && !CollectionUtils.isEmpty(list)) {
                if (aiList.size() != list.size()) {
                    errorAlert("获取到的信息数量不一致:" + data);
                    return;
                }
                CustomerWillCirculationAi customerWillCirculationAi = aiList.get(0);
                CustomerWillCirculation customerWillCirculation = list.get(0);
                if (!compare(customerWillCirculationAi, customerWillCirculation)) {
                    //errorAlert(data);
                } else {
                    long success = redisOperator.incr("CRM:AI:datachecker:success3", 1);
                    String error = redisOperator.get("CRM:AI:datachecker:error3");
                    if (StringUtils.hasText(error)) {
                        log.info("成功率:{}", success / (success + Double.parseDouble(error)));
                    } else {
                        log.info("成功率百分之百");
                    }
                    log.info("成功总数量:{}", success);
                    log.info("数据一致:{}", data);
                }
            }
            if (CollectionUtils.isEmpty(aiList) && !CollectionUtils.isEmpty(list)) {
                errorAlert("AI新的表不存在,旧表中存在:" + data);
                return;
            }
            if (!CollectionUtils.isEmpty(aiList) && CollectionUtils.isEmpty(list)) {
                errorAlert("AI新的表存在,旧表中不存在:" + data);
                return;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            threadLocal.remove();
        }
    }


    private void errorAlert(String message) {
        long error = redisOperator.incr("CRM:AI:datachecker:error3", 1);
        log.info("失败总数量:{}", error);
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
                .writeTimeout(1, TimeUnit.MINUTES)
                .build();
        log.info("数据不一致信息:{}", message);
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(JSONObject.of("msgtype", "text", "text",
                JSONObject.of("content", message))));
        Request request = new Request.Builder()
                .url("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6ae7dbaf-6d8d-4d5d-933b-09286449dfc0")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try (Response response = client.newCall(request).execute()) {

        } catch (Exception e) {
            log.error("发送消息异常", e);
        }
    }

    private boolean compare(CustomerWillCirculationAi customerWillCirculationAi, CustomerWillCirculation customerWillCirculation) {
        if (null != customerWillCirculationAi) {
            return true;
        }
        // 比较每个字段
        if (!compareField("areaId", customerWillCirculationAi.getAreaId(), customerWillCirculation.getAreaId()))
            return false;
        if (!compareField("areaName", customerWillCirculationAi.getAreaName(), customerWillCirculation.getAreaName()))
            return false;
        if (!compareField("subId", customerWillCirculationAi.getSubId(), customerWillCirculation.getSubId()))
            return false;
        if (!compareField("subName", customerWillCirculationAi.getSubName(), customerWillCirculation.getSubName()))
            return false;
        if (!compareField("buId", customerWillCirculationAi.getBuId(), customerWillCirculation.getBuId())) return false;
        if (!compareField("buName", customerWillCirculationAi.getBuName(), customerWillCirculation.getBuName()))
            return false;
        if (!compareField("deptId", customerWillCirculationAi.getDeptId(), customerWillCirculation.getDeptId()))
            return false;
        if (!compareField("deptName", customerWillCirculationAi.getDeptName(), customerWillCirculation.getDeptName()))
            return false;
//        if (!compareField("salerId", customerWillCirculationAi.getSalerId(), customerWillCirculation.getSalerId()))
//            return false;
//        if (!compareField("salerName", customerWillCirculationAi.getSalerName(), customerWillCirculation.getSalerName()))
//            return false;
        if (!compareField("custId", customerWillCirculationAi.getCustId(), customerWillCirculation.getCustId()))
            return false;
        if (!compareField("custName", customerWillCirculationAi.getCustName(), customerWillCirculation.getCustName()))
            return false;
//        if (!compareField("reason", customerWillCirculationAi.getReason(), customerWillCirculation.getReason()))
//            return false;
        if (!compareField("origin", customerWillCirculationAi.getOrigin(), customerWillCirculation.getOrigin()))
            return false;
        if (!compareField("status", customerWillCirculationAi.getStatus(), customerWillCirculation.getStatus()))
            return false;
        if (!compareField("deleteFlag", customerWillCirculationAi.getDeleteFlag(), customerWillCirculation.getDeleteFlag()))
            return false;
        if (!compareField("preDate", customerWillCirculationAi.getPreDate(), customerWillCirculation.getPreDate()))
            return false;
        if (!compareField("absoluteProtectFlag", customerWillCirculationAi.getAbsoluteProtectFlag(), customerWillCirculation.getAbsoluteProtectFlag()))
            return false;
        return true; // 所有字段一致返回 true
    }


    private boolean compareField(String fieldName, Object value1, Object value2) {
        if (value1 == null && value2 != null || value1 != null && !value1.equals(value2)) {
            String data = threadLocal.get();
            JSONObject jsonObject = JSONObject.parseObject(data);
            jsonObject.put("error", "字段 " + fieldName + " differs: " + value1 + " vs " + value2);
            errorAlert("数据不一致,信息" + JSONObject.toJSONString(jsonObject));
            return false; // 如果某个字段不一致，返回 false
        }
        return true; // 如果一致返回 true
    }
}
