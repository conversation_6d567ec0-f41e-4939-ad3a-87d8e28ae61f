package com.ce.scrm.center.async.util;

import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskRequest;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskResponse;
import com.tencentcloudapi.asr.v20190614.models.DescribeTaskStatusRequest;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.profile.Language;

public class DescribeInstances {
    public static void main(String[] args) {
        try {
            // Credential cred = new Credential("AKID03889ze5II3ZBbonW2Jp8HEwQhlaCcpx", "vb2LZiibIwRhk0TwX1KRL2VlncDk8KK8");
            Credential cred = new Credential("AKIDHRQCl9LK8j6IG1YsETVKvXzEG7mDceXD", "dGi0gDZieNsEYhgU6XzIWXounMm01pUy");
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setReqMethod("GET");
            httpProfile.setConnTimeout(30);
            httpProfile.setWriteTimeout(30);
            httpProfile.setReadTimeout(30);
            httpProfile.setEndpoint("asr.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setSignMethod(ClientProfile.SIGN_TC3_256);
            // 自3.1.80版本开始，SDK 支持打印日志。
            clientProfile.setHttpProfile(httpProfile);
            clientProfile.setDebug(true);
            // 从3.1.16版本开始，支持设置公共参数 Language, 默认不传，选择(ZH_CN or EN_US)
            clientProfile.setLanguage(Language.EN_US);
            // 实例化要请求产品(以cvm为例)的client对象,clientProfile是可选的
            AsrClient client = new AsrClient(cred, "ap-shanghai", clientProfile);
            // 实例化一个cvm实例信息查询请求对象,每个接口都会对应一个request对象。
            CreateRecTaskRequest req = new CreateRecTaskRequest();
            req.setEngineModelType("16k_zh");
            req.setChannelNum(1L);
            req.setSourceType(0L);
            req.setUrl("http://test-cesupport-images.ceboss.cn/upload/followup/20250530/ded9e45de20b4b2ba8d460262979a478.m4a");
            req.setResTextFormat(1L);
            req.setSpeakerDiarization(1L);
            req.setSpeakerNumber(5L);
            CreateRecTaskResponse resp = client.CreateRecTask(req);
            System.out.println(CreateRecTaskResponse.toJsonString(resp));
            while (true) {
                try {
                    DescribeTaskStatusRequest describeTaskStatusRequest = new DescribeTaskStatusRequest();
                    describeTaskStatusRequest.setTaskId(resp.getData().getTaskId());
                    DescribeTaskStatusResponse resp2 = new AsrClientCe(cred, "ap-shanghai", clientProfile).DescribeTaskStatus(describeTaskStatusRequest);
                    System.out.println(resp2.getData().getStatusStr());
                    for (DescribeTaskStatusResponse.SentenceDetail sentenceDetail : resp2.getData().getResultDetail()) {
                        String res = "说话人"  + sentenceDetail.getSpeakerId() + ":" + sentenceDetail.getFinalSentence();
                        System.out.println(res);
                    }
                    break;
                } catch (Exception e) {
                    Thread.sleep(2000);
                }
            }
        } catch (TencentCloudSDKException | InterruptedException e) {
            System.out.println(e.toString());
        }
    }
}
