package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.mq.entity.CampaignReportInfo;
import com.ce.scrm.center.dao.entity.EqixiuDataResult;
import com.ce.scrm.center.dao.service.IEqixiuDataResultService;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.config.UniqueIdService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.eqixiu.support.entity.EqixiuCodeInfo;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.util.constant.UtilConstant;
import com.ce.scrm.extend.dubbo.api.ShortUrlDubboService;
import com.ce.scrm.extend.dubbo.entity.view.ShortUrlView;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

import static com.ce.scrm.center.service.eqixiu.support.entity.EqixiuConstant.PREVIEW_VIEW;

/**
 * 三方活动上报内容监听
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_CAMPAIGN_REPORT_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_CAMPAIGN_REPORT_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class CampaignConsumer implements RocketMQListener<MessageExt> {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private ShortUrlDubboService shortUrlDubboService;
    @Autowired
    private IEqixiuDataResultService eqixiuDataResultService;
    @Autowired
    private SendWxMessage sendWxMessage;

    @Autowired
    private UniqueIdService uniqueIdService;

    @Resource
    private Environment environment;


    @Resource
    private EmployeeThirdService employeeThirdService;


    @Override
    public void onMessage(MessageExt messageExt) {
        String messageId = messageExt.getMsgId();
        String message = new String(messageExt.getBody());
        if (StringUtils.isBlank(message)) {
            return;
        }
        log.info("SCRM_CAMPAIGN_REPORT_TOPIC，msgId={},消息内容：{}", messageId, message);
        try {
            CampaignReportInfo campaignReportInfo = JSON.parseObject(message, CampaignReportInfo.class);
            if (StringUtils.isNotBlank(campaignReportInfo.getCode())) {
                //根据code获取长链
                ShortUrlView shortUrlView = shortUrlDubboService.getLongUrlByUniqueCode(campaignReportInfo.getCode()).getData();
                if (shortUrlView != null && StringUtils.isNotBlank(shortUrlView.getLongUrl())) {
                    EqixiuCodeInfo eqixiuCodeDto = JSONObject.parseObject(shortUrlView.getLongUrl(), EqixiuCodeInfo.class);
                    log.info("长链解析出来的内容={}", JSONObject.toJSONString(eqixiuCodeDto));
                    if (eqixiuCodeDto != null && StringUtils.isNotBlank(eqixiuCodeDto.getEqixiuDataResultId())) {
                        EqixiuDataResult byId = eqixiuDataResultService.getById(eqixiuCodeDto.getEqixiuDataResultId());
                        if (byId != null) {
                            byId.setId(uniqueIdService.getId());
                            byId.setRecentShareTime(null);
                            //打开页面
                            byId.setEventType(PREVIEW_VIEW);
                            byId.setRecentOpenTime(new Date());
                            byId.setCreateTime(new Date());
                            byId.setUpdateTime(new Date());
                            eqixiuDataResultService.save(byId);
                            this.sendMessage(byId);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 消息最大长度
     */
    private final static int MSG_MAX_LENGTH = 4096;

    public Boolean sendWechatMessage(String message) {
        try {
            if (message.length() > MSG_MAX_LENGTH) {
                message = message.substring(0, MSG_MAX_LENGTH);
            }
            Map<String, String> contentMap = new HashMap<>();
            contentMap.put("content", message);
            Map<String, Object> param = new HashMap<>();
            param.put("msgtype", "markdown");
            param.put("markdown", contentMap);
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(param));
            Request request = new Request.Builder()
                    .url("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8c0309df-43d6-41df-af22-03d4f4fc74d5")
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            response.close();
        } catch (Exception e) {
            log.error("活动消息通知发送失败");
        }
        return true;
    }


    public void sendMessage(EqixiuDataResult byId) {
        //发送企微通知
        String customerMessage = "你分享的【%s】，被 【%s】 打开浏览。";
        String customerMessageFormat = String.format(customerMessage,
                byId.getActivityName(),
                byId.getCustomerName()
        );
        sendWxMessage.sendMessage(byId.getSalerId(), customerMessageFormat);
        try {
            //发送报警通知
            String customerMessageRebot = "【%s】分享的【%s】，被 【%s】 打开浏览。";
            String customerMessageFormatRebot = String.format(customerMessageRebot,
                    byId.getSalerName(),
                    byId.getActivityName(),
                    byId.getCustomerName()
            );
            this.sendMessage(byId.getSalerDeptId(), byId.getSalerSubId(), customerMessageFormatRebot, byId.getSalerAreaId());
            sendWechatMessage(customerMessageFormatRebot);
        } catch (Exception e) {

        }
    }

    private void sendMessage(String deptId, String subId, String message, String areaId) {
        Optional<EmployeeLiteThirdView> orgLeaderCooperation = employeeThirdService.getOrgLeader(deptId);
        if (orgLeaderCooperation.isPresent() && !StringUtils.isEmpty(orgLeaderCooperation.get().getId())) {
            sendWxMessage.sendMessage(orgLeaderCooperation.get().getId(), message);
            log.info("给部门经理发送消息成功:{},{}", orgLeaderCooperation.get().getId(), message);
        }
//        Optional<EmployeeLiteThirdView> subLeaderProtect = employeeThirdService.getOrgLeader(subId);
//        if (subLeaderProtect.isPresent() && !StringUtils.isEmpty(subLeaderProtect.get().getId())) {
//            sendWxMessage.sendMessage(subLeaderProtect.get().getId(), message);
//            log.info("给部分司总监发送消息成功:{},{}", subLeaderProtect.get().getId(), message);
//        }
//        Optional<EmployeeLiteThirdView> areaLeaderProtect = employeeThirdService.getOrgLeader(areaId);
//        if (areaLeaderProtect.isPresent() && !StringUtils.isEmpty(areaLeaderProtect.get().getId())) {
//            sendWxMessage.sendMessage(areaLeaderProtect.get().getId(), message);
//            log.info("给部区域总监发送消息成功:{},{}", areaLeaderProtect.get().getId(), message);
//        }
    }

}
