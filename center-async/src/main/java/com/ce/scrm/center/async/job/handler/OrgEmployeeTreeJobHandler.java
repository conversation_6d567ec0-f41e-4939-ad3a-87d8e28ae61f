package com.ce.scrm.center.async.job.handler;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.service.business.OrgInfoBusiness;
import com.ce.scrm.center.service.business.ProtectBusiness;
import com.ce.scrm.center.service.business.entity.dto.org.QueryOrgTreeBusinessDto;
import com.ce.scrm.center.service.business.entity.view.org.OrgEmployeeBusinessView;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.ce.scrm.center.service.business.OrgInfoBusiness.ORG_EMPLOYEE_TREE_KEY;

/**
 * 定时发送微信消息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/19
 */
@Slf4j
@Component
public class OrgEmployeeTreeJobHandler {

    @Resource
    private OrgInfoBusiness orgInfoBusiness;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     *  3小时一次 缓存一下 组织机构员工树
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_ORG_EMPLOYEE_TREE_JOB_HANDLER)
    public ReturnT<String> buildTree(String param) {
        log.info("===========3小时一次缓存一下组织机构员工树,开始==========");
        List<OrgEmployeeBusinessView> treeDate = orgInfoBusiness.getTreeDate(new QueryOrgTreeBusinessDto());
        stringRedisTemplate.opsForValue().set(ORG_EMPLOYEE_TREE_KEY, JSON.toJSONString(treeDate), 6, TimeUnit.HOURS);
        log.info("===========3小时一次缓存一下组织机构员工树,结束==========");
        return ReturnT.SUCCESS;
    }

}
