package com.ce.scrm.center.async.job.handler;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.Transfertable;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.TransfertableService;
import com.ce.scrm.center.service.business.ProtectBusiness;
import com.ce.scrm.center.service.business.entity.dto.protect.ProtectAllotBusinessDto;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.TransferProcessingStatusEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 调拨超过一小时未确认系统自动确认通过
 * <AUTHOR>
 */
@Slf4j
@Component
public class TransferAutoPassHandler {
    @Resource
    private TransfertableService transfertableService;

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private ProtectBusiness protectBusiness;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_TRANSFER_AUTO_PASS_JOB_HANDLER)
    public ReturnT<String> TransferAutoPass(String param) {
        List<Transfertable> list = transfertableService.list(new LambdaQueryWrapper<Transfertable>().eq(Transfertable::getProcessingStatus, TransferProcessingStatusEnum.UNPROCESSED.getCode()));
        List<Transfertable> updateList = new ArrayList<>();
        for (Transfertable transfertable : list) {
            //判断申请时间+1小时工作时间是否在10：00-17:45范围内，如是未处理则系统处理：超时默认通过，在此范围之外的时间不做任何处理
            Date applicationTime = transfertable.getApplicationTime();
            Date oneHourLater = DateUtil.offsetHour(applicationTime, 1);

            if (isInWorkingHoursRange(oneHourLater)) {
                // 系统自动处理，默认通过
                transfertable.setProcessingStatus(TransferProcessingStatusEnum.PASS.getCode());
                transfertable.setProcessingTime(new Date());
                transfertable.setRemarks("系统自动处理：超时默认通过");
                transfertable.setProcessor("系统");
                updateList.add(transfertable);
            }
            CustProtectView custProtectView = cmCustProtectService.selectOneByCondition(CmCustProtect.builder().custId(transfertable.getCustomerId()).build());
            if (Objects.nonNull(custProtectView)) {
                ProtectAllotBusinessDto protectAllotBusinessDto = new ProtectAllotBusinessDto();
                protectAllotBusinessDto.setCustomerId(transfertable.getCustomerId());
                protectAllotBusinessDto.setOldSalerId(custProtectView.getSalerId());
                protectAllotBusinessDto.setNewSalerId(transfertable.getApplicantId());
                Boolean allot = protectBusiness.allot(protectAllotBusinessDto);
                if (!allot) {
                    log.error("处理调拨之后的保护关系失败,参数={}", JSON.toJSONString(protectAllotBusinessDto));
                } else {
                    log.info("处理调拨之后的保护关系成功,参数={}", JSON.toJSONString(protectAllotBusinessDto));
                }
            } else {
                log.error("调拨处理后查询客户的保护关系失败，客户id={}", transfertable.getCustomerId());
            }
        }
        boolean b = transfertableService.updateBatchById(updateList);
        if (!b) {
            log.error("系统处理超时未处理的申请调度信息失败,date={}", updateList);
            return ReturnT.FAIL;
        }


        return ReturnT.SUCCESS;
    }

    /**
     * 判断时间是否在10：00-17:45范围内
     * @param date 日期
     * @return 是否在工作时间范围内
     */
    private boolean isInWorkingHoursRange(Date date) {
        Date start = DateUtil.parse(DateUtil.formatDate(date) + " 10:00:00");
        Date end = DateUtil.parse(DateUtil.formatDate(date) + " 17:45:00");
        return date.compareTo(start) >= 0 && date.compareTo(end) <= 0;
    }
}
