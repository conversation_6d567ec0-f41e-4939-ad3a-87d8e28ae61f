package com.ce.scrm.center.async.job.handler;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dao.entity.SdrPushSaleReview;
import com.ce.scrm.center.dao.entity.SjAssignDetail;
import com.ce.scrm.center.dao.service.SjAssignDetailService;
import com.ce.scrm.center.service.business.abm.AbmUpdateProtectBusiness;
import com.ce.scrm.center.service.business.abm.BizOppDistributeBusiness;
import com.ce.scrm.center.service.business.abm.SdrPushSaleReviewBusiness;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmReleaseProtectDto;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.ConvertRelationEnum;
import com.ce.scrm.extend.dubbo.api.CmCustVisitLogDubboService;
import com.ce.scrm.extend.dubbo.entity.request.CmCustVisitLogReq;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * @version 1.0
 * @Description: 客户跟进确认
 * @Author: lijinpeng
 * @Date: 2024/10/8 11:04
 */
@Slf4j
@Component
public class AbmCustFollowCheckJobHandler {

    @Resource
    private SjAssignDetailService sjAssignDetailService;

    @Resource
    private BizOppDistributeBusiness bizOppDistributeBusiness;

    @Resource
    private SdrPushSaleReviewBusiness sdrPushSaleReviewBusiness;

    @Resource
    private AbmUpdateProtectBusiness abmUpdateProtectBusiness;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private CmCustVisitLogDubboService cmCustVisitLogDubboService;

    /**
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @Description abm商机下发后回执检查
     * <AUTHOR>
     * @date 2024/10/8 11:25
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_ABM_CUST_FOLLOW_CHECK_JOB_HANDLER)
    public ReturnT<String> checkReceipt(String param) {
        log.info("===========abm商机下发后-客户跟进确认-检查==========");
        if (StringUtils.isEmpty(param)) {
            return ReturnT.SUCCESS;
        }
        Long sjAssignDetailId = Long.valueOf(param);
        SjAssignDetail sjAssignDetail = sjAssignDetailService.getById(sjAssignDetailId);
        if (Objects.isNull(sjAssignDetail)) {
            log.warn("无效下发记录:{} ", sjAssignDetailId);
            return ReturnT.SUCCESS;
        }

        try {
            Integer receiptFlag = sjAssignDetail.getReceiptFlag();

            if (Objects.equals(receiptFlag, 0)) {
                // 未回执 问题数据

            } else {
                // 已回执
                Date followStartTime = sjAssignDetail.getFollowStartTime();
                Date followEndTime = sjAssignDetail.getFollowEndTime();
                Integer followCheck = sjAssignDetail.getFollowCheck();
                if (Objects.equals(followCheck, 1)) {
                    // 说明已经处理过
                    return ReturnT.SUCCESS;
                }

                // 校验已经重分配的次数
                Long reviewId = sjAssignDetail.getReviewId();
                Long times = bizOppDistributeBusiness.countCheckFollowTimesByReviewId(reviewId);

                // 过来就改状态
                sjAssignDetail.setFollowCheck(1);
                sjAssignDetail.setUpdatedId("1024");
                sjAssignDetail.setUpdatedTime(new Date());
                boolean update = sjAssignDetailService.updateById(sjAssignDetail);
                if (!update) {
                    log.error("更新回执状态失败,{}", JSON.toJSONString(sjAssignDetail));
                }

                // > 2次 那么释放到公海
                if (times <= 1) {
                    CmCustVisitLogReq req = new CmCustVisitLogReq();
                    req.setCustId(sjAssignDetail.getCustomerId());
                    req.setSalerId(sjAssignDetail.getSalerId());
                    req.setVisitTimeStart(followStartTime);
                    req.setVisitTimeEnd(followEndTime);
                    DubboResult<Integer> countByCondition = cmCustVisitLogDubboService.getCountByCondition(req);
                    if (countByCondition.checkSuccess()) {
                        Integer count = countByCondition.getData();
                        if (count == 0) {
                            // 限时之内-没跟进过客户
                            SdrPushSaleReview sdrPushSaleReview = sdrPushSaleReviewBusiness.getByReviewId(reviewId);
                            // 重新走分配流程
                            Optional<String> optional = bizOppDistributeBusiness.bizOppDistribute(sdrPushSaleReview);
                            optional.ifPresent(s -> log.error("重新分配失败，{} 记录 {}", s, JSON.toJSONString(sdrPushSaleReview)));
                        }
                    }
                } else {
                    // 释放接口
                    AbmReleaseProtectDto abmReleaseProtectDto = new AbmReleaseProtectDto();
                    abmReleaseProtectDto.setCustId(sjAssignDetail.getCustomerId());
//                    abmReleaseProtectDto.setReleaseReason("abm-商机跟进限时-超期释放客户");
                    abmReleaseProtectDto.setReleaseReason(ConvertRelationEnum.ABM_SJ_GENGJIN_CHAOQI_RELEASE.getLable());
                    abmReleaseProtectDto.setOperator("1024");
                    abmReleaseProtectDto.setConvertType(ConvertRelationEnum.ABM_SJ_GENGJIN_CHAOQI_RELEASE.getValue());
                    abmReleaseProtectDto.setConvertTypeDesc(ConvertRelationEnum.ABM_SJ_GENGJIN_CHAOQI_RELEASE.getLable());
                    Optional<String> optional = abmUpdateProtectBusiness.releaseProtect(abmReleaseProtectDto);
                    optional.ifPresent(s -> log.error("abm-商机跟进限时-超期释放客户失败,{}", s));
                }
            }
        } catch (Exception e) {
            log.error("abm商机下发后-商机跟进限时-检查失败，message:{}", e.getMessage());
        }

        log.info("===========abm商机下发-跟进限时-结束==========");
        return ReturnT.SUCCESS;
    }


}
