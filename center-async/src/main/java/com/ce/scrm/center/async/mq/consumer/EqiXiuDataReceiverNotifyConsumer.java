package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.mq.entity.CouponInfo;
import com.ce.scrm.center.async.mq.entity.EqixiuMessageHolder;
import com.ce.scrm.center.async.util.CouponSignUtils;
import com.ce.scrm.center.dao.entity.EqixiuDataResult;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.support.redis.RedisOperator;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.ce.scrm.center.async.mq.consumer.EqiXiuDataReceiverConsumer.E_QI_XIU_SEND_LOCK;

/**
 * 易企秀消息监听
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_EQIXIU_MESSAGE_COUPON_NOTIFY_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_EQIXIU_MESSAGE_COUPON_NOTIFY_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class EqiXiuDataReceiverNotifyConsumer implements RocketMQListener<MessageExt> {

    @Value("${couponReleaseUrl}")
    private String couponReleaseUrl;

    @Autowired
    private RedisOperator redisOperator;

    @Autowired
    private SendWxMessage sendWxMessage;

    @Autowired
    private EmployeeThirdService employeeThirdService;

    @Value("${sign.privateKey}")
    private String privateKey;

    private static final OkHttpClient client = new OkHttpClient().newBuilder()
            .writeTimeout(1, TimeUnit.MINUTES)
            .connectTimeout(1, TimeUnit.MINUTES)
            .readTimeout(1, TimeUnit.MINUTES)
            .callTimeout(1, TimeUnit.MINUTES)
            .build();

    @Override
    public void onMessage(MessageExt messageExt) {
        String topic = messageExt.getTopic();
        String msgId = messageExt.getMsgId();
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            return;
        }
        EqixiuMessageHolder eqixiuMessageHolder = JSONObject.parseObject(data, EqixiuMessageHolder.class);
        try {
            this.sendReward(eqixiuMessageHolder);
            this.sendWxMessage(eqixiuMessageHolder);
        } catch (IOException e) {
            log.error("发放优惠券失败,topic:{},messageId:{}", topic, msgId, e);
            throw new RuntimeException(e);
        }
    }

    private void sendReward(EqixiuMessageHolder eqixiuMessageHolder) throws IOException {
        if (StringUtils.isBlank(eqixiuMessageHolder.getWinInfo().getPrizeCode())) {
            return;
        }
        String eventId = eqixiuMessageHolder.getEventId();
        String lockKey = E_QI_XIU_SEND_LOCK + "sendReward:" + eventId;
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setCouponId(eqixiuMessageHolder.getWinInfo().getPrizeCode());
        couponInfo.setGiveWay(1);
        couponInfo.setGiveObject("TO_CUST_NOIMPORT");
        couponInfo.setIsNow(1);
        couponInfo.setGiveReason(eqixiuMessageHolder.getEqixiuDataResult().getActivityName() + ":" + eventId);
        couponInfo.setType(1);
        couponInfo.setCustId(eqixiuMessageHolder.getEqixiuDataResult().getCustomerId());
        couponInfo.setCustName(eqixiuMessageHolder.getEqixiuDataResult().getCustomerName());
        couponInfo.setDetailNum(1);
        long incr = redisOperator.incr(lockKey, 1);
        if (incr > 1) {
            return;
        }
        couponInfo.setSignDate(CouponSignUtils.getTimestamp());
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(couponInfo));
        String sign = CouponSignUtils.createSign(jsonObject, privateKey);
        jsonObject.put("sign", sign);
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(couponReleaseUrl);
        for (String key : jsonObject.keySet()) {
            uriComponentsBuilder.queryParam(key, jsonObject.getString(key));
        }
        String reqUrl = uriComponentsBuilder.build().toUriString();
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = new Request.Builder()
                .url(reqUrl)
                .method("POST", body)
                .build();
        try {
            String result = "";
            JSONObject jsonResult = new JSONObject();
            log.info("请求发放券信息:{}", reqUrl);
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                result = new String(response.body().bytes());
                log.info("请求发放券返回信息:{}", result);
                jsonResult = JSONObject.parseObject(result);
            }
            if (StringUtils.isBlank(result) || !Objects.equals(jsonResult.getString("status"), "200")) {
                throw new RuntimeException("请求发放券失败");
            }
            redisOperator.expire(lockKey, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("请求发放券失败", e);
            redisOperator.del(lockKey);
            throw e;
        }
    }


    private void sendWxMessage(EqixiuMessageHolder eqixiuMessageHolder) {
        EqixiuDataResult eqixiuDataResult = eqixiuMessageHolder.getEqixiuDataResult();
        String prizeName = eqixiuMessageHolder.getWinInfo().getPrizeName();
        String eventId = eqixiuMessageHolder.getEventId();
        String lockKey = E_QI_XIU_SEND_LOCK + "sendNotifyMessage:" + eventId;
        long incr = redisOperator.incr(lockKey, 1);
        if (incr > 1) {
            return;
        }
        try {
            // 给商务发送消息
            String customerMessage = "您邀请的%s %s %s，已参与%s活动，中奖结果：%s。";
            String customerMessageFormat = String.format(customerMessage,
                    eqixiuDataResult.getCustomerName(),
                    eqixiuDataResult.getActivityName(),
                    prizeName
            );
            sendWxMessage.sendMessage(eqixiuDataResult.getSalerId(), customerMessageFormat);
            log.info("给商务发送消息成功:{},{}", eqixiuDataResult.getSalerId(), customerMessageFormat);
            if (StringUtils.isNotBlank(eqixiuDataResult.getProtectSalerName())) {
                //都按照保护关系走
                String managerMessage = "商务%s邀请的%s ，已参与 %s 活动，中奖结果：%s。";
                String managerMessageOrgLeader = String.format(managerMessage,
                        eqixiuDataResult.getProtectSalerName(),
                        eqixiuDataResult.getCustomerName(),
                        eqixiuDataResult.getActivityName(),
                        prizeName
                );
                Optional<EmployeeLiteThirdView> orgLeaderCooperation = employeeThirdService.getOrgLeader(eqixiuDataResult.getProtectDeptId());
                if (orgLeaderCooperation.isPresent() && !StringUtils.isEmpty(orgLeaderCooperation.get().getId())) {
                    sendWxMessage.sendMessage(orgLeaderCooperation.get().getId(), managerMessageOrgLeader);
                    log.info("给部门经理发送消息成功:{},{}", orgLeaderCooperation.get().getId(), managerMessageOrgLeader);
                }
                // 分司总监
                String managerMessageSubLeader = String.format(managerMessage,
                        eqixiuDataResult.getProtectSalerName(),
                        eqixiuDataResult.getCustomerName(),
                        eqixiuDataResult.getActivityName(),
                        prizeName
                );
                Optional<EmployeeLiteThirdView> subLeaderProtect = employeeThirdService.getOrgLeader(eqixiuDataResult.getProtectSubId());
                if (subLeaderProtect.isPresent() && !StringUtils.isEmpty(subLeaderProtect.get().getId())) {
                    sendWxMessage.sendMessage(subLeaderProtect.get().getId(), managerMessageSubLeader);
                    log.info("给部分司总监发送消息成功:{},{}", orgLeaderCooperation.get().getId(), managerMessageOrgLeader);
                }
            }
            redisOperator.expire(lockKey, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("给分司总监发送企微消息失败:{}", JSONObject.toJSONString(eqixiuDataResult), e);
            redisOperator.del(lockKey);
            throw e;
        }
    }

}
