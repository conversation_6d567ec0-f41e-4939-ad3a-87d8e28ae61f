package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.mq.entity.MessageContentExtend;
import com.ce.scrm.center.dao.entity.AiChatTempLog;
import com.ce.scrm.center.dao.service.AiChatTempLogService;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.xfppt.PPTRequestParam;
import com.ce.scrm.center.service.xfppt.PPTService;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Level.ONE_MINUTE;
import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_TOPIC;

@Deprecated
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 1)
public class XFPPTDataReceiverConsumer implements RocketMQListener<MessageExt> {

    @Autowired
    private PPTService pptService;

    @Autowired
    private SendWxMessage sendWxMessage;

    @Autowired
    private RocketMqOperate rocketMqOperate;

    @Autowired
    private AiChatTempLogService aiChatTempLogService;


    @Value("${pptNotify:}")
    private String pptNotify;


    @Override
    public void onMessage(MessageExt messageExt) {
//        String data = new String(messageExt.getBody());
//        if (StringUtils.isEmpty(data)) {
//            return;
//        }
//        try {
//            PPTRequestParam pptRequestParam = JSONObject.parseObject(data, PPTRequestParam.class);
//            if (StringUtils.isBlank(pptRequestParam.getSid())) {
//                StringBuilder stringBuilder = new StringBuilder();
//                if (StringUtils.isNotBlank(pptRequestParam.getText())) {
//                    stringBuilder.append(pptRequestParam.getText());
//                } else {
//                    if (StringUtils.isBlank(pptRequestParam.getChatId())) {
//                        log.warn("ppt请求内容chatId为空");
//                        return;
//                    }
//                    AiChatTempLog chatTempLog = aiChatTempLogService.lambdaQuery().eq(AiChatTempLog::getChatId, pptRequestParam.getChatId()).list().stream().findAny().orElseGet(null);
//                    if (chatTempLog == null) {
//                        log.error("通过chatId:{}，获取聊天内容为空", pptRequestParam.getChatId());
//                        return;
//                    }
//                    List<MessageContentExtend> messageContentList = JSONArray.parseArray(chatTempLog.getContent(), MessageContentExtend.class);
//                    String contentSystem = messageContentList.stream().filter(T -> T.getRole().equals("system")).map(MessageContentExtend::getContent).collect(Collectors.joining("\n"));
//                    String result = null;
//                    String[] parts = contentSystem.split("#### 多维度分析结果 ####", 2);
//                    if (parts.length > 1) {
//                        result = "#### 多维度分析结果 ####" + parts[1]; // 重新加上分隔符
//                    } else {
//                        log.warn("未找到对话所对应的多维度分析结果");
//                        result = contentSystem;
//                    }
//                    //stringBuilder.append(result).append("\n");
//                    List<String> contentAssistant = messageContentList.stream().filter(T -> T.getRole().equals("assistant")).map(MessageContentExtend::getContent).collect(Collectors.toList());
//                    if (!CollectionUtils.isEmpty(contentAssistant)) {
//                        stringBuilder.append(contentAssistant.get(contentAssistant.size() - 1));
//                    }
//                }
//                String reqContent = limitString(stringBuilder.toString(), 8000);
//                String sid = pptService.getSid(reqContent, pptRequestParam.getEmployeeId(), pptRequestParam.getId());
//                if (Objects.equals(sid, "1")) {
//                    return;
//                }
//                pptRequestParam.setSid(sid);
//                rocketMqOperate.syncSend(SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_TOPIC, JSONObject.toJSONString(pptRequestParam), ONE_MINUTE);
//            } else {
//                String result = pptService.getPPTUrl(pptRequestParam);
//                if (StringUtils.isNotBlank(result)) {
//                    if (Objects.equals(result, "1")) {
//                        return;
//                    }
//                    //发送消息
//                    StringBuilder stringBuilder = new StringBuilder();
//                    stringBuilder.append("客户名称: ").append(pptRequestParam.getCustName()).append("\n");
//                    stringBuilder.append("商务名称: ").append(pptRequestParam.getEmployeeName()).append("\n");
//                    stringBuilder.append("PPT下载地址: ").append(result).append("\n");
//                    // sendWxMessage.sendMessage(pptRequestParam.getEmployeeId(), stringBuilder.toString());
//                    if (StringUtils.isNotBlank(pptRequestParam.getChatId())) {
//                        stringBuilder.append("对话ID: ").append(pptRequestParam.getChatId()).append("\n");
//                    }
//                    stringBuilder.append("记录ID: ").append(pptRequestParam.getId()).append("\n");
//                    this.sendWechatMessage(stringBuilder.toString());
//                    sendWxMessage.sendMessage(pptRequestParam.getEmployeeId(), stringBuilder.toString());
//                }
//            }
//        } catch (Exception e) {
//            throw new RuntimeException("查询 ppt生成进度异常", e);
//        }
    }

//
//    // 临时发送消息
//    public Boolean sendWechatMessage(String message) {
//        try {
//            Map<String, String> contentMap = new HashMap<>();
//            contentMap.put("content", message);
//            Map<String, Object> param = new HashMap<>();
//            param.put("msgtype", "markdown");
//            param.put("markdown", contentMap);
//            OkHttpClient client = new OkHttpClient().newBuilder()
//                    .build();
//            MediaType mediaType = MediaType.parse("application/json");
//            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(param));
//            Request request = new Request.Builder()
//                    .url(pptNotify)
//                    .method("POST", body)
//                    .addHeader("Content-Type", "application/json")
//                    .build();
//            Response response = client.newCall(request).execute();
//            response.close();
//        } catch (Exception e) {
//            log.error("活动消息通知发送失败");
//        }
//        return true;
//    }
//
//
//    public static String limitString(String input, int maxLength) {
//        if (input == null) {
//            return "";
//        }
//        if (input.length() > maxLength) {
//            log.warn("字符串超长，实际长度：{}，将截取前 {} 个字符", input.length(), maxLength);
//            return input.substring(0, maxLength);
//        }
//        return input;
//    }


}
