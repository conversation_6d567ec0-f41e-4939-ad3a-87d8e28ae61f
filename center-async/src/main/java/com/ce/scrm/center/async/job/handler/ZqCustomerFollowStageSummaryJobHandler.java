package com.ce.scrm.center.async.job.handler;

import cn.ce.cesupport.emp.enums.OrgTypeEnum;
import cn.ce.cesupport.emp.enums.StateEnum;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ce.scrm.center.dao.entity.CurrentBusinessMonthTemporaryTable;
import com.ce.scrm.center.dao.entity.ZqCustomerFollowStageSummary;
import com.ce.scrm.center.dao.entity.view.CustomerFollowStageSummaryView;
import com.ce.scrm.center.dao.service.CurrentBusinessMonthTemporaryTableService;
import com.ce.scrm.center.dao.service.CustomerFollowService;
import com.ce.scrm.center.dao.service.ZqCustomerFollowStageSummaryService;
import com.ce.scrm.center.service.business.entity.view.CurrentBusinessMonthView;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.ZqSummaryOrgTypeEnum;
import com.ce.scrm.center.service.third.entity.dto.OrgConditionThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.invoke.AchForOtherThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.util.date.RankingUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计算 中企跟进记录按照销售阶段客户数汇总表
 * 每小时计算一次
 * 总监工作台展示需要
 */
@Slf4j
@Component
public class ZqCustomerFollowStageSummaryJobHandler {

    @Resource
    private CustomerFollowService customerFollowService;

    @Resource
    private OrgThirdService orgThirdService;

    @Resource
    private ZqCustomerFollowStageSummaryService zqCustomerFollowStageSummaryService;

    @Resource
    private AchForOtherThirdService achForOtherThirdService;

    /**
     * 定时计算 每一小时一次
     * @param param
     * @return
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_ZQ_CUSTOMER_FOLLOW_STAGE_SUMMARY_JOB_HANDLER)
    public ReturnT<String> calculate(String param) {

        log.info("中企跟进记录按照销售阶段客户数汇总表 ZqCustomerFollowStageSummaryJobHandler，start");
        try {
            CurrentBusinessMonthView currentBusinessMonth = achForOtherThirdService.getCurrentBusinessMonth();

            Date currentDate = new Date();
            if (currentDate.after(currentBusinessMonth.getEndDate())) {
                log.warn("当前商务月已经过时，无需计算,currentBusinessMonth={}", JSONObject.toJSONString(currentBusinessMonth));
                return ReturnT.SUCCESS;
            }
            calculateStart(currentBusinessMonth);
        }catch (Exception e){
            log.error("ZqCustomerFollowStageSummaryJobHandler,calculate 任务失败",e);
        }
        log.info("中企跟进记录按照销售阶段客户数汇总表 ZqCustomerFollowStageSummaryJobHandler，end");

        return ReturnT.SUCCESS;

    }

    public void calculateStart(CurrentBusinessMonthView currentBusinessMonth) {
        long l1 = System.currentTimeMillis();

        List<OrgThirdDto> areaList = orgThirdService.getOrgChildrenByCondition(
                OrgConditionThirdDto.builder()
                        .state(StateEnum.Enable.getValue())
                        .type(OrgTypeEnum.AREA.getType())
                        .build()
        );
        Map<String, String> areaIdMap = areaList.stream().collect(Collectors.toMap(OrgThirdDto::getId, OrgThirdDto::getName, (key1, key2) -> key2));

        List<OrgThirdDto> subInfoList = orgThirdService.getOrgChildrenByCondition(
                OrgConditionThirdDto.builder()
                        .state(StateEnum.Enable.getValue())
                        .type(OrgTypeEnum.SUB.getType()).build()
        );
        // 去除跨境
        List<String> notAreaIdList = Arrays.asList("3950","4343");
        subInfoList = subInfoList.stream().filter(s -> !notAreaIdList.contains(s.getParentId())).collect(Collectors.toList());

        List<CustomerFollowStageSummaryView> zqCustomerFollowStageSummary = customerFollowService.getZqCustomerFollowStageSummary(currentBusinessMonth.getBeginDate(),currentBusinessMonth.getEndDate());
        Map<String, List<CustomerFollowStageSummaryView>> subStageQueryMap = zqCustomerFollowStageSummary.stream().collect(Collectors.groupingBy(CustomerFollowStageSummaryView::getSubId));

        List<ZqCustomerFollowStageSummary> subResultList = new ArrayList<>();
        for (OrgThirdDto subInfo : subInfoList) {

            String subId = subInfo.getId();

            ZqCustomerFollowStageSummary item = ZqCustomerFollowStageSummary.builder()
                    .businessMonth(currentBusinessMonth.getBussinessMonth())
                    .orgType(ZqSummaryOrgTypeEnum.SUB.getCode())
                    .areaId(subInfo.getParentId())
                    .areaName(areaIdMap.getOrDefault(subInfo.getParentId(),null))
                    .subId(subId)
                    .subName(subInfo.getName())
                    .flag(0)
                    .build();

            Map<String, CustomerFollowStageSummaryView> stageObjectMap = subStageQueryMap.getOrDefault(subId, Collections.emptyList())
                    .stream().collect(Collectors.toMap(CustomerFollowStageSummaryView::getSalesStage, Function.identity(), (key1, key2) -> key2));

            CustomerFollowStageSummaryView dictSalesStage001 = stageObjectMap.get("DICT_SALES_STAGE_001");
            item.setChubuJianlianCountToday(dictSalesStage001 != null ? dictSalesStage001.getCustomerSumToday() : 0);
            item.setChubuJianlianCountMonth(dictSalesStage001 != null ? dictSalesStage001.getCustomerSumMoney() : 0);

            CustomerFollowStageSummaryView dictSalesStage002 = stageObjectMap.get("DICT_SALES_STAGE_002");
            item.setXuqiuQuerenCountToday(dictSalesStage002 != null ? dictSalesStage002.getCustomerSumToday() : 0);
            item.setXuqiuQuerenCountMonth(dictSalesStage002 != null ? dictSalesStage002.getCustomerSumMoney() : 0);

            CustomerFollowStageSummaryView dictSalesStage003 = stageObjectMap.get("DICT_SALES_STAGE_003");
            item.setFanganHuibaoCountToday(dictSalesStage003 != null ? dictSalesStage003.getCustomerSumToday() : 0);
            item.setFanganHuibaoCountMonth(dictSalesStage003 != null ? dictSalesStage003.getCustomerSumMoney() : 0);

            CustomerFollowStageSummaryView dictSalesStage004 = stageObjectMap.get("DICT_SALES_STAGE_004");
            item.setHetongQianshuCountToday(dictSalesStage004 != null ? dictSalesStage004.getCustomerSumToday() : 0);
            item.setHetongQianshuCountMonth(dictSalesStage004 != null ? dictSalesStage004.getCustomerSumMoney() : 0);

            CustomerFollowStageSummaryView dictSalesStage005 = stageObjectMap.get("DICT_SALES_STAGE_005");
            item.setShangwuGoutongCountToday(dictSalesStage005 != null ? dictSalesStage005.getCustomerSumToday() : 0);
            item.setShangwuGoutongCountMonth(dictSalesStage005 != null ? dictSalesStage005.getCustomerSumMoney() : 0);

            CustomerFollowStageSummaryView dictSalesStage006 = stageObjectMap.get("DICT_SALES_STAGE_006");
            item.setChengjiaoQianyueCountToday(dictSalesStage006 != null ? dictSalesStage006.getCustomerSumToday() : 0);
            item.setChengjiaoQianyueCountMonth(dictSalesStage006 != null ? dictSalesStage006.getCustomerSumMoney() : 0);

            item.setYixiangCountToday(
                    item.getChubuJianlianCountToday() + item.getXuqiuQuerenCountToday() + item.getFanganHuibaoCountToday() +
                            item.getShangwuGoutongCountToday() + item.getHetongQianshuCountToday() + item.getChengjiaoQianyueCountToday()
            );
            item.setYixiangCountMonth(
                    item.getChubuJianlianCountMonth() + item.getXuqiuQuerenCountMonth() + item.getFanganHuibaoCountMonth() +
                            item.getShangwuGoutongCountMonth() + item.getHetongQianshuCountMonth() + item.getChengjiaoQianyueCountMonth()
            );

            subResultList.add(item);
        }
        // 分司排名
        RankingUtil.setRanking(subResultList,ZqCustomerFollowStageSummary::getYixiangCountMonth,ZqCustomerFollowStageSummary::setRanking);

        // 区域
        List<ZqCustomerFollowStageSummary> areaResultList = new ArrayList<>(subResultList.stream().collect(Collectors.toMap(ZqCustomerFollowStageSummary::getAreaId,
                a -> {
                    ZqCustomerFollowStageSummary copyObject = new ZqCustomerFollowStageSummary();
                    BeanUtils.copyProperties(a, copyObject);
                    copyObject.setOrgType(ZqSummaryOrgTypeEnum.AREA.getCode());
                    copyObject.setSubId(null);
                    copyObject.setSubName(null);
                    return copyObject;
                },
                (a, b) -> {
                    ZqCustomerFollowStageSummary areaItemObject = new ZqCustomerFollowStageSummary();
                    BeanUtils.copyProperties(a, areaItemObject);

                    areaItemObject.setChubuJianlianCountToday(a.getChubuJianlianCountToday() + b.getChubuJianlianCountToday());
                    areaItemObject.setChubuJianlianCountMonth(a.getChubuJianlianCountMonth() + b.getChubuJianlianCountMonth());

                    areaItemObject.setXuqiuQuerenCountToday(a.getXuqiuQuerenCountToday() + b.getXuqiuQuerenCountToday());
                    areaItemObject.setXuqiuQuerenCountMonth(a.getXuqiuQuerenCountMonth() + b.getXuqiuQuerenCountMonth());

                    areaItemObject.setFanganHuibaoCountToday(a.getFanganHuibaoCountToday() + b.getFanganHuibaoCountToday());
                    areaItemObject.setFanganHuibaoCountMonth(a.getFanganHuibaoCountMonth() + b.getFanganHuibaoCountMonth());

                    areaItemObject.setShangwuGoutongCountToday(a.getShangwuGoutongCountToday() + b.getShangwuGoutongCountToday());
                    areaItemObject.setShangwuGoutongCountMonth(a.getShangwuGoutongCountMonth() + b.getShangwuGoutongCountMonth());

                    areaItemObject.setHetongQianshuCountToday(a.getHetongQianshuCountToday() + b.getHetongQianshuCountToday());
                    areaItemObject.setHetongQianshuCountMonth(a.getHetongQianshuCountMonth() + b.getHetongQianshuCountMonth());

                    areaItemObject.setChengjiaoQianyueCountToday(a.getChengjiaoQianyueCountToday() + b.getChengjiaoQianyueCountToday());
                    areaItemObject.setChengjiaoQianyueCountMonth(a.getChengjiaoQianyueCountMonth() + b.getChengjiaoQianyueCountMonth());

                    areaItemObject.setYixiangCountToday(a.getYixiangCountToday() + b.getYixiangCountToday());
                    areaItemObject.setYixiangCountMonth(a.getYixiangCountMonth() + b.getYixiangCountMonth());

                    return areaItemObject;
                })).values());

        // 区域排名
        RankingUtil.setRanking(areaResultList,ZqCustomerFollowStageSummary::getYixiangCountMonth,ZqCustomerFollowStageSummary::setRanking);

        // 全国 NATIONWIDE
        ZqCustomerFollowStageSummary nationwideResultObject = ZqCustomerFollowStageSummary.builder()
                .businessMonth(currentBusinessMonth.getBussinessMonth())
                .orgType(ZqSummaryOrgTypeEnum.NATIONWIDE.getCode())
                .areaId(null)
                .areaName(null)
                .subId(null)
                .subName(null)
                .chubuJianlianCountToday(0)
                .chubuJianlianCountMonth(0)
                .xuqiuQuerenCountToday(0)
                .xuqiuQuerenCountMonth(0)
                .fanganHuibaoCountToday(0)
                .fanganHuibaoCountMonth(0)
                .shangwuGoutongCountToday(0)
                .shangwuGoutongCountMonth(0)
                .hetongQianshuCountToday(0)
                .hetongQianshuCountMonth(0)
                .chengjiaoQianyueCountToday(0)
                .chengjiaoQianyueCountMonth(0)
                .yixiangCountToday(0)
                .yixiangCountMonth(0)
                .flag(0)
                .build();

        areaResultList.stream().reduce(nationwideResultObject, (a, b) -> {
            a.setChubuJianlianCountToday(a.getChubuJianlianCountToday() + b.getChubuJianlianCountToday());
            a.setChubuJianlianCountMonth(a.getChubuJianlianCountMonth() + b.getChubuJianlianCountMonth());

            a.setXuqiuQuerenCountToday(a.getXuqiuQuerenCountToday() + b.getXuqiuQuerenCountToday());
            a.setXuqiuQuerenCountMonth(a.getXuqiuQuerenCountMonth() + b.getXuqiuQuerenCountMonth());

            a.setFanganHuibaoCountToday(a.getFanganHuibaoCountToday() + b.getFanganHuibaoCountToday());
            a.setFanganHuibaoCountMonth(a.getFanganHuibaoCountMonth() + b.getFanganHuibaoCountMonth());

            a.setShangwuGoutongCountToday(a.getShangwuGoutongCountToday() + b.getShangwuGoutongCountToday());
            a.setShangwuGoutongCountMonth(a.getShangwuGoutongCountMonth() + b.getShangwuGoutongCountMonth());

            a.setHetongQianshuCountToday(a.getHetongQianshuCountToday() + b.getHetongQianshuCountToday());
            a.setHetongQianshuCountMonth(a.getHetongQianshuCountMonth() + b.getHetongQianshuCountMonth());

            a.setChengjiaoQianyueCountToday(a.getChengjiaoQianyueCountToday() + b.getChengjiaoQianyueCountToday());
            a.setChengjiaoQianyueCountMonth(a.getChengjiaoQianyueCountMonth() + b.getChengjiaoQianyueCountMonth());

            a.setYixiangCountToday(a.getYixiangCountToday() + b.getYixiangCountToday());
            a.setYixiangCountMonth(a.getYixiangCountMonth() + b.getYixiangCountMonth());
            return a;
        });

        List<ZqCustomerFollowStageSummary> resultList = new ArrayList<>();
        resultList.add(nationwideResultObject);
        resultList.addAll(areaResultList);
        resultList.addAll(subResultList);

        zqCustomerFollowStageSummaryService.remove(new LambdaQueryWrapper<ZqCustomerFollowStageSummary>().eq(ZqCustomerFollowStageSummary::getBusinessMonth, currentBusinessMonth.getBussinessMonth()));
        zqCustomerFollowStageSummaryService.update(new LambdaUpdateWrapper<ZqCustomerFollowStageSummary>().set(ZqCustomerFollowStageSummary::getFlag,-1));
        zqCustomerFollowStageSummaryService.saveBatch(resultList);

        long l2 = System.currentTimeMillis();
        long l = l2 - l1;
        log.info("任务用时: {} ms", l);
    }

}
