package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dao.entity.SdrPushSaleReview;
import com.ce.scrm.center.service.business.abm.BizOppDistributeBusiness;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.support.redis.RedisOperator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * abm 审核通过mq
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CRM_ABM_REVIEW_PASS_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.CRM_ABM_REVIEW_PASS_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class AbmReviewPassConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private BizOppDistributeBusiness bizOppDistributeBusiness;

    public String cacheKeyPrefix = "SCRM:ABM:REVIEW:";

    @Resource
    private RedisOperator redisOperator;

    private final static String TASK_NAME = ServiceConstant.MqConstant.Topic.CRM_ABM_REVIEW_PASS_TOPIC;

    @Override
    public void onMessage(MessageExt messageExt) {
        String messageId = messageExt.getMsgId();
        String message = new String(messageExt.getBody());
        if (StringUtils.isBlank(message)) {
            return;
        }
        log.info("CRM_ABM_REVIEW_PASS_TOPIC，msgId={},消息内容：{}", messageId, message);
        String key = cacheKeyPrefix + messageId;
        boolean flag = redisOperator.setIfAbsent(key, "1", 1L, TimeUnit.DAYS);
        if (!flag) {
            log.info("{} 重复消费,本地忽略", TASK_NAME);
            return;
        }
        try {
            SdrPushSaleReview sdrPushSaleReview = JSON.parseObject(message, SdrPushSaleReview.class);
            if (Objects.isNull(sdrPushSaleReview)) {
                return;
            }
            Integer reviewStatus = sdrPushSaleReview.getReviewStatus();
            if (!Objects.equals(reviewStatus, 1)) {
                log.info("未审核通过 {}", JSON.toJSONString(sdrPushSaleReview));
            } else {
                Optional<String> optional = bizOppDistributeBusiness.bizOppDistribute(sdrPushSaleReview);
                if (optional.isPresent()) {
                    log.error("审核通过，{} 转商机失败,{}",JSON.toJSONString(sdrPushSaleReview), optional.get());
                } else {
                    log.info("审核通过，转商机成功");
                }
            }
        } catch (Exception e) {
            log.error("abm 审核通过mq处理异常, messageId={} message={}", messageId, message, e);
        }
    }
}
