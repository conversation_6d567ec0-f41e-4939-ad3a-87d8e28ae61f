package com.ce.scrm.center.async.mq.consumer;

import cn.ce.cesupport.enums.favorites.ConvertRelationEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.mq.entity.TableChangeEnum;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerLayerChangeLog;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.CustomerLayerChangeLogService;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogBusinessDto;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.CustomerLayerEnum;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.SmaConvertLogThirdService;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerUpdateDubboDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 监听保护关系binlog同步 维护customer表 customer_layer 、customer_grade字段到保护关系表
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CM_CUST_PROTECT_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_MAINTAIN_PROTECTED_INFO_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class MaintainProtectInfoConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private CustomerLayerChangeLogService customerLayerChangeLogService;

    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;

    @Override
    public void onMessage(MessageExt messageExt) {
        String topic = messageExt.getTopic();
        String msgId = messageExt.getMsgId();
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            return;
        }
        try {
            JSONObject binlogJson = JSON.parseObject(data);
            /**
             * 处理binlog记录的过程：
             * 1、判断是否操作类型：ddl、dml， ddl 直接发消息通知【企微】?
             * 2、dml时判断type:
             *     - INSERT：新增数据，则直接生产对应的事件
             *     - UPDATE：修改数据，判断事件字段是否发生变化(逻辑由子类实现)
             *     - DELETE：删除数据，暂时没定怎么处理？？
             */
            Boolean isDdl = binlogJson.getBoolean("isDdl");
            if (isDdl) {
                log.error("接收到ddl语句...,逻辑暂不支持!! topic={} msgId {}", topic, msgId);
                return;
            }

            // 针对dml语句进行校验
            Map<String, Boolean> repeatMsgMap = new HashMap<>();

            String type = binlogJson.getString("type");
            JSONArray afterAry = binlogJson.getJSONArray("data");
            JSONArray beforeAry = binlogJson.getJSONArray("old");
            int size = afterAry.size();
            for (int i = 0; i < size; i++) {
                JSONObject afterJson = afterAry.getJSONObject(i);
                String custId = afterJson.getString("CUST_ID");
                if(custId == null) {
                    continue;
                }
                if (repeatMsgMap.getOrDefault(afterJson.getString("id"), false)) {
                    continue;
                }
                if (TableChangeEnum.delete.getField().equals(type)) {
//                    startAsyncSql(custId);
                    continue;
                }
                if (TableChangeEnum.insert.getField().equals(type)) {
                    startAsyncSql(custId);
                    continue;
                }
                if (TableChangeEnum.update.getField().equals(type)) {
                    startAsyncSql(custId);
                    continue;
                }
            }
        } catch (Exception e) {
            log.error("MaintainProtectInfoConsumer失败! topic={} msgId {},{}", topic, msgId, e.getMessage());
        }
    }

    /**
     * 同步SQL
     * @param custId
     */
    private void startAsyncSql(String custId) {

        log.info("MaintainProtectInfoConsumer.startAsyncSql custId:{}",custId);
        if(StringUtils.isEmpty(custId)) {
            return;
        }
        // 查询当前客户信息
        Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(custId);
        CustomerDataThirdView customer = customerData.orElse(null);
        if(customer == null) {
            return;
        }
        // 查询当前保护关系
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, custId).one();
        if (cmCustProtect == null) {
            return;
        }
        if (!checkData(customer,cmCustProtect)) {
            Integer oldCustomerLayer = cmCustProtect.getCustomerLayer();
            Integer oldCustomerGrade = cmCustProtect.getCustomerGrade();
            cmCustProtect.setCustomerLayer(customer.getCustomerLayer());
            cmCustProtect.setCustomerGrade(customer.getCustomerGrade());
            cmCustProtectService.updateById(cmCustProtect);
            // 添加客户layer变化记录
            if (customer.getCustomerLayer() != null && oldCustomerLayer != null && !Objects.equals(oldCustomerLayer, customer.getCustomerLayer())) {
                customerLayerChangeLogService.save(
                        CustomerLayerChangeLog.builder()
                                .customerId(custId)
                                .oldLayer(customer.getCustomerLayer())
                                .newLayer(oldCustomerLayer)
                                .build()
                );
                // 流转日志build
                ConvertLogBusinessDto convertLogBusinessDto = ConvertLogBusinessDto.builder()
                        .custId(custId)
                        .createTime(new Date())
                        .createBy("1024")
                        .convertType(ConvertRelationEnum.CUSTOMER_LAYER_CHANGE.getValue())
                        .releaseReason("客户从["+ CustomerLayerEnum.getDescriptionByCode(oldCustomerLayer)
                                + "]变为["+CustomerLayerEnum.getDescriptionByCode(customer.getCustomerLayer())
                                + "]")
                        .custName(customer.getCustomerName())
                        .build();
                smaConvertLogThirdService.insertLog(convertLogBusinessDto);
            }
        }

    }

    private boolean checkData(CustomerDataThirdView customer, CmCustProtect cmCustProtect) {

        if (!Objects.equals(customer.getCustomerLayer(), cmCustProtect.getCustomerLayer())) {
            return false;
        }

        if (!Objects.equals(customer.getCustomerGrade(), cmCustProtect.getCustomerGrade())) {
            return false;
        }

        return true;
    }


}
