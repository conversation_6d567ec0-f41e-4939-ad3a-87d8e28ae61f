package com.ce.scrm.center.async.job.handler;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.SjAssignDetail;
import com.ce.scrm.center.dao.service.SjAssignDetailService;
import com.ce.scrm.center.service.business.abm.AbmMessageNoticeBusiness;
import com.ce.scrm.center.service.business.abm.AbmUpdateProtectBusiness;
import com.ce.scrm.center.service.business.abm.BizOppDistributeBusiness;
import com.ce.scrm.center.service.business.abm.SdrPushSaleReviewBusiness;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmMessageNoticeDto;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.support.redis.RedisOperator;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * @version 1.0
 * @Description: 回执提醒
 * @Author: lijinpeng
 * @Date: 2024/10/8 11:04
 */
@Slf4j
@Component
public class AbmReceiptRemindJobHandler {

    @Resource
    private SjAssignDetailService sjAssignDetailService;

    @Resource
    private BizOppDistributeBusiness bizOppDistributeBusiness;

    @Resource
    private AbmMessageNoticeBusiness abmMessageNoticeBusiness;

    @Resource
    private SdrPushSaleReviewBusiness sdrPushSaleReviewBusiness;

    @Resource
    private AbmUpdateProtectBusiness abmUpdateProtectBusiness;

    @Resource
    private RedisOperator redisOperator;

    /**
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @Description abm商机下发后回执检查
     * <AUTHOR>
     * @date 2024/10/8 11:25
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_ABM_RECEIPT_REMIND_JOB_HANDLER)
    public ReturnT<String> checkReceipt(String param) {
        log.info("===========abm商机下发后-提醒通知==========");
        if (StringUtils.isEmpty(param)) {
            return ReturnT.SUCCESS;
        }
        JSONObject jsonObject = JSONObject.parseObject(param);
        Long id = jsonObject.getLong("id");
        Integer remindType = jsonObject.getInteger("remindType");
        if (!Lists.newArrayList(1, 2).contains(remindType)) {
            log.info("无效通知类型 ={}", param);
            return ReturnT.SUCCESS;
        }
        SjAssignDetail sjAssignDetail = sjAssignDetailService.getById(id);
        if (Objects.isNull(sjAssignDetail)) {
            log.warn("无效下发记录:{} ", param);
            return ReturnT.SUCCESS;
        }

        try {
            Integer receiptFlag = sjAssignDetail.getReceiptFlag();
            if (Objects.equals(receiptFlag, 0)) {
                // 未回执
                String salerId = sjAssignDetail.getSalerId();
                String customerId = sjAssignDetail.getCustomerId();

                Optional<AbmMessageNoticeDto> noticeDtoOptional = abmMessageNoticeBusiness.getNoticeInfoByCustomerId(customerId);
                if (!noticeDtoOptional.isPresent()) {
                    log.warn("找不到当前应该提醒的人 {} param={}", customerId, param);
                    return ReturnT.SUCCESS;
                }
                AbmMessageNoticeDto abmMessageNoticeDto = noticeDtoOptional.get();
                String notifierUserId = abmMessageNoticeDto.getNotifierUserId();
                if (!Objects.equals(notifierUserId, salerId)) {
                    log.warn("当前保护人={} 与下发时预定的通知人={} 不一致，放弃通知", notifierUserId, salerId);
                    return ReturnT.SUCCESS;
                } else {
                    if (Objects.equals(remindType, 1)) {
                        String customerName = abmMessageNoticeDto.getCustomerName();
                        abmMessageNoticeBusiness.smsNotice(customerName, abmMessageNoticeDto.getNotifierMobile());

                        // 创建下一个: 回执剩余15分钟，AI电话提醒
                        bizOppDistributeBusiness.addReceiptRemindXxlTask(sjAssignDetail, 2, 15);
                    } else if (Objects.equals(remindType, 2)) {
                        abmMessageNoticeBusiness.aiCallNotice(abmMessageNoticeDto.getNotifierMobile());
                    }
                }
            } else {
                // 已回执 不处理
            }
        } catch (Exception e) {
            log.error("abm商机下发后-提醒通知失败，message:{}", e.getMessage());
        }

        log.info("===========abm商机下发-提醒通知结束==========");
        return ReturnT.SUCCESS;
    }


}
