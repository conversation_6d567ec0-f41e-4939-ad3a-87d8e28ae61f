package com.ce.scrm.center.async.mq.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.ce.scrm.center.async.mq.entity.CustomerKeepAndLossMqData;
import com.ce.scrm.center.async.util.AIJsonChecker;
import com.ce.scrm.center.async.util.JsonToMarkdown;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerCirculationSpecialSetting;
import com.ce.scrm.center.dao.entity.CustomerWillCirculation;
import com.ce.scrm.center.dao.entity.CustomerWillCirculationAi;
import com.ce.scrm.center.dao.service.CustomerWillCirculationAiService;
import com.ce.scrm.center.dao.service.ICustomerCirculationSpecialSettingService;
import com.ce.scrm.center.service.business.CirculationLossBusiness;
import com.ce.scrm.center.service.business.ProtectBusiness;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum;
import com.ce.scrm.center.service.enums.ProtectCustTypeEnum;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.entity.view.OrgDataThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.util.date.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_CUSTOMER_KEEP_AND_LOSS_AI_CHECK_TOPIC;

/**
 * Description: 待流转保有客户insert到db 消费者
 * 绝对保护期内的客户：只是insert到待流转表，但是不流转，只在列表展示
 * <p>
 * 流转step 2
 *
 * @author: JiuDD
 * date: 2024/7/17
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_CUSTOMER_KEEP_AND_LOSS_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.SCRM_CUSTOMER_KEEP_CONVERT_AI_GROUP, consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 1)
public class AiCustomerKeepCirculationConsumer implements RocketMQListener<MessageExt> {
    @Resource
    private ICustomerCirculationSpecialSettingService customerCirculationSpecialSettingService;
    @Resource
    private OrgThirdService orgThirdService;
    @Resource
    private EmployeeThirdService employeeThirdService;
    @Resource
    private CustomerWillCirculationAiService customerWillCirculationAiService;
    @Resource
    private CirculationLossBusiness circulationLossBusiness;
    @Resource
    private ProtectBusiness protectBusiness;

    @NacosValue(value = "${aiCirculationSystem:}", autoRefreshed = true)
    private String aiCirculationSystem;

    @Resource
    private RocketMqOperate rocketMqOperate;

    @Override
    public void onMessage(MessageExt messageExt) {
        if(true){
            return;
        }
        String data = new String(messageExt.getBody());
        //log.info("收到保有客户流转MQ消息，msgId={},消息内容：{}", messageExt.getMsgId(), data);
        if (StringUtils.isEmpty(data)) {
            return;
        }
        String custId = "";
        try {
            // AI 判断的前置条件
            CustomerKeepAndLossMqData keepAndLossMqData = JSON.parseObject(data, CustomerKeepAndLossMqData.class);
            custId = keepAndLossMqData.getCustId();
            String subId = keepAndLossMqData.getSubId();
            Integer monthWithoutClock = keepAndLossMqData.getMonthWithoutClock();
            JSONObject req = new JSONObject();
            req.put("分公司ID", subId);
            req.put("未打卡自然月数", keepAndLossMqData.getMonthWithoutClock());
            // 流转方式：1总监待分 2经理待分
            req.put("流转方式", new Object() {
                public String getType() {
                    if (Objects.equals(keepAndLossMqData.getCirculationType(), 1)) {
                        return "总监待分";
                    }
                    if (Objects.equals(keepAndLossMqData.getCirculationType(), 2)) {
                        return "经理待分";
                    }
                    return "";
                }
            }.getType());
            CmCustProtect cmCustProtect = protectBusiness.getCmCustProtect(custId);
            req.put("存在保护关系", Objects.isNull(cmCustProtect) ? "不存在" : "存在");
            req.put("流转控制表", checkSpecialSetting(custId) ? "不在" : "在");
            ProtectStateEnum protectStateEnum = ProtectStateEnum.getProtectStateEnum(cmCustProtect.getStatus());
            req.put("客户的保护状态", protectStateEnum == null ? "" : protectStateEnum.getStateName());
            ProtectCustTypeEnum protectCustTypeEnum = ProtectCustTypeEnum.of(cmCustProtect.getCustType());
            req.put("客户类型", protectCustTypeEnum == null ? "" : protectCustTypeEnum.getLable());
            // 当前客户是否在考核期
            DateUtils.CirculationLimitDateResult visitLimitDate = DateUtils.getVisitCirculationLimitDate(cmCustProtect.getFirstSignTime(), monthWithoutClock);
            req.put("是否在考核期内", DateUtils.isInAppraisalPeriod(visitLimitDate) ? "在" : "不在");
            Long custSiteClockCount = circulationLossBusiness.getCustSiteClockCount(cmCustProtect, visitLimitDate);
            req.put("考核期内的打卡次数", custSiteClockCount);
            // AI 判断条件组装结束
            String markDown = JsonToMarkdown.convert2TmarkDown(JSONObject.toJSONString(req));
            JSONObject jsonObjectAiResult = JSONObject.parseObject(AIJsonChecker.getResult(aiCirculationSystem, markDown));
            log.info("ai的返回数据:{}", JSONObject.toJSONString(jsonObjectAiResult));
            if (Boolean.parseBoolean(jsonObjectAiResult.getString("need_transfer"))) {
                try {
                    CustomerWillCirculation circulation = assembleCirculation(keepAndLossMqData, cmCustProtect, custSiteClockCount, visitLimitDate);
                    setAbsoluteProtectFields(circulation, cmCustProtect, monthWithoutClock);
                    CustomerWillCirculationAi circulationAi = new CustomerWillCirculationAi();
                    BeanUtil.copyProperties(circulation, circulationAi);
                    customerWillCirculationAiService.save(circulationAi);
                } catch (DuplicateKeyException e) {
                    log.warn("重复insert待流转客户, 放弃insert, 忽略. custId={}", custId);
                }
            } else {
                log.warn("判断不需要进行流转:{}", JSONObject.toJSONString(jsonObjectAiResult));
            }
            //TODO 发送 MQ延时消息
            rocketMqOperate.syncSend(CESUPPORT_SCRM_CUSTOMER_KEEP_AND_LOSS_AI_CHECK_TOPIC,
                    JSONObject.toJSONString(
                            JSONObject.of(
                                    "custId", custId,
                                    "createDate", (new SimpleDateFormat("yyyy-MM-dd")).format(new Date()),
                                    "aiReq", markDown,
                                    "aiJson", req,
                                    "aiRes", jsonObjectAiResult
                            )
                    ),
                    5);
        } catch (Exception e) {
            log.error("接受客户ID消息,insert客户到待流转表异常,客户ID={} ", custId, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * Description: 绝对保护期内的客户，设置绝对保护字段=1(不流转的标识)，并单独计算考核周期
     *
     * @param circulation       待流转
     * @param cmCustProtect     保护关系
     * @param monthWithoutClock 未打卡自然月数
     * @return void
     * date: 2025/1/14 10:46
     * @author: JiuDD
     */
    private void setAbsoluteProtectFields(CustomerWillCirculation circulation, CmCustProtect cmCustProtect, Integer monthWithoutClock) {
        if (Objects.nonNull(cmCustProtect.getAbsoluteProtectTime()) && cmCustProtect.getAbsoluteProtectTime().after(DateUtil.date())) {
            //log.warn("客户绝对保护期未结束，单独计算考核周期，并设置绝对保护字段=1，custId:{}, absoluteProtectTime:{}", cmCustProtect.getCustId(), DateUtil.formatDateTime(cmCustProtect.getAbsoluteProtectTime()));
            circulation.setAbsoluteProtectFlag(1);
            // 绝对保护期未结束，单独计算考核周期
            DateUtils.CirculationLimitDateResult visitLimitDate = DateUtils.getVisitCirculationLimitDate(cmCustProtect.getAbsoluteProtectTime(), cmCustProtect.getFirstSignTime(), monthWithoutClock);
            circulation.setPreDate(visitLimitDate.getNextLimitDate());
        }
    }

    /**
     * Description: 校验入参
     *
     * @param mqData 入参
     * @return boolean
     * date: 2024/7/29 21:16
     * @author: JiuDD
     */
    private static boolean checkParam(CustomerKeepAndLossMqData mqData) {
        if (StringUtils.isEmpty(mqData.getCustId()) || StringUtils.isEmpty(mqData.getSubId()) || Objects.isNull(mqData.getMonthWithoutClock())
                || Objects.isNull(mqData.getCirculationType()) || (mqData.getCirculationType() != 1 && mqData.getCirculationType() != 2)
        ) {
            return false;
        }
        return true;
    }

    /**
     * Description: 不流转控制表中存在该客户，则不insert待流转表
     *
     * @param custId 客户id
     * @return boolean true不流转，false流转
     * date: 2024/7/25 16:18
     * @author: JiuDD
     */
    private boolean checkSpecialSetting(String custId) {
        List<CustomerCirculationSpecialSetting> specialSettingList = customerCirculationSpecialSettingService.lambdaQuery()
                .eq(CustomerCirculationSpecialSetting::getNotCirculationType, 1)
                .eq(CustomerCirculationSpecialSetting::getCustId, custId)
                .eq(CustomerCirculationSpecialSetting::getDeleteFlag, 0)
                .list();
        if (!CollectionUtils.isEmpty(specialSettingList)) {
            log.warn("当前客户在不流转控制表中存在，不insert待流转表，custId:{}", custId);
            return false;
        }
        return true;
    }

    /**
     * Description: 校验当前客户是否是待流转客户
     *
     * @param cmCustProtect 保护关系
     * @return boolean true流转，false不流转
     * date: 2024/7/29 19:16
     * @author: JiuDD
     */
    private boolean ifRunCirculate(CmCustProtect cmCustProtect) {
        String custId = cmCustProtect.getCustId();
        // 检查不流转控制表中是否配置该客户不流转
        if (!checkSpecialSetting(custId)) {
            return false;
        }
        Integer status = cmCustProtect.getStatus();
        if (Objects.equals(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState(), status) || Objects.equals(ProtectStateEnum.MANAGER_WILL_ASSIGN.getState(), status) || Objects.equals(ProtectStateEnum.BU_WILL_ASSIGN.getState(), status)) {
            String position = "";
            if (Objects.equals(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState(), status)) {
                position = "总监";
            } else if (Objects.equals(ProtectStateEnum.MANAGER_WILL_ASSIGN.getState(), status)) {
                position = "经理";
            } else if (Objects.equals(ProtectStateEnum.BU_WILL_ASSIGN.getState(), status)) {
                position = "事业部总监";
            }
            log.warn("当前客户为{}待分，不insert待流转表，custId:{}", position, custId);
            return false;
        }
        if (Objects.equals(ProtectStateEnum.CUSTOMER_POOL.getState(), status)) {
            log.warn("当前客户在客户池，不insert待流转表， custId:{}", custId);
            return false;
        }
        if (!Objects.equals(cmCustProtect.getCustType(), ProtectCustTypeEnum.ORDERED.getValue()) && !Objects.equals(cmCustProtect.getCustType(), ProtectCustTypeEnum.SECOND_DEVELOPMENT.getValue())) {
            log.warn("该客户不属于成交客户，不insert待流转表，custId={}", custId);
            return false;
        }
        /* 绝对保护期未结束的客户，也要写入待流转表，因此这里不再阻断
        if (Objects.nonNull(cmCustProtect.getAbsoluteProtectTime()) && cmCustProtect.getAbsoluteProtectTime().after(DateUtil.date())) {
            log.warn("当前客户绝对保护期未结束，不insert待流转表，custId:{}, absoluteProtectTime:{}", custId, cmCustProtect.getAbsoluteProtectTime());
            return false;
        }
        */
        return true;
    }

    /**
     * Description: insert待流转记录
     *
     * @param keepAndLossMqData  自动流转配置
     * @param cmCustProtect      保护关系
     * @param custSiteClockCount 实际打卡次数
     * @param visitLimitDate     打卡考核周期
     * @return void
     * date: 2024/7/25 16:56
     * @author: JiuDD
     */
    private CustomerWillCirculation assembleCirculation(CustomerKeepAndLossMqData keepAndLossMqData, CmCustProtect cmCustProtect, Long custSiteClockCount, DateUtils.CirculationLimitDateResult visitLimitDate) {
        String custId = cmCustProtect.getCustId();
        // 预计流转日期
        Date preDate = null;
        String preCirculationReason = null;
        if (custSiteClockCount == 0) {
            preCirculationReason = String.format("%s个自然月未打卡拜访", keepAndLossMqData.getMonthWithoutClock());
            preDate = visitLimitDate.getNextLimitDate();
        }
        // 待流转表中存在该客户，则更新该记录，否则insert
        CustomerWillCirculation willCirculation = new CustomerWillCirculation();
        Date today = new Date();
        List<String> orgIdList = Arrays.asList(cmCustProtect.getAreaId(), cmCustProtect.getSubcompanyId(), cmCustProtect.getBussdeptId(), cmCustProtect.getBuId());
        Map<String, OrgDataThirdView> orgDataThirdViewMap = orgThirdService.getOrgData(orgIdList);
        willCirculation.setAreaId(cmCustProtect.getAreaId());
        willCirculation.setAreaName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getAreaId(), new OrgDataThirdView()).getName());
        willCirculation.setSubId(cmCustProtect.getSubcompanyId());
        willCirculation.setSubName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getSubcompanyId(), new OrgDataThirdView()).getName());
        willCirculation.setBuId(cmCustProtect.getBuId());
        willCirculation.setBuName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBuId(), new OrgDataThirdView()).getName());
        willCirculation.setDeptId(cmCustProtect.getBussdeptId());
        willCirculation.setDeptName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBussdeptId(), new OrgDataThirdView()).getName());
        willCirculation.setSalerId(cmCustProtect.getSalerId());
        willCirculation.setSalerName(employeeThirdService.getEmployeeData(cmCustProtect.getSalerId()).orElse(new EmployeeDataThirdView()).getName());
        willCirculation.setCustId(custId);
        willCirculation.setCustName(cmCustProtect.getCustName());
        willCirculation.setReason(preCirculationReason);
        willCirculation.setOrigin(AssignCustSourceSpecialEnum.CIRCULATION.getValue());
        willCirculation.setStatus(null);
        willCirculation.setDeleteFlag(0);
        willCirculation.setPreDate(preDate);
        willCirculation.setCreateTime(today);
        willCirculation.setCreateDate(today);
        willCirculation.setUpdateTime(today);
        return willCirculation;
    }

}
