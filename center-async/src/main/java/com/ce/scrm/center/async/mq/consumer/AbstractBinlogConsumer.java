package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.mq.entity.TableChangeEnum;
import com.ce.scrm.center.support.redis.RedisOperator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
public abstract class AbstractBinlogConsumer {
    @Resource
    protected RedisOperator redisOperator;

    /**
     * 消息重复消费阈值
     */
    private static final int MSG_REPEAT_TIMES_THRESHOLD = 9;

    /**
     * redis key前缀
     */
    private static final String REDIS_KEY_PREFIX = "CRM_BINLOG_EVENT_MSG:";

    public void dealMqMsg(MessageExt messageExt) {
        int reconsumeTimes = messageExt.getReconsumeTimes();
        String topic = messageExt.getTopic();
        String msgId = messageExt.getMsgId();

        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            return;
        }

        String key = REDIS_KEY_PREFIX + DigestUtils.md5DigestAsHex(data.getBytes());
        try {
            boolean flag = redisOperator.setIfAbsent(key, "1", 2L, TimeUnit.HOURS);
            if (!flag) {
                log.warn("消息重复:{}", data);
                return;
            }

            JSONObject binlogJson = JSON.parseObject(data);
            Boolean isDdl = binlogJson.getBoolean("isDdl");
            if (isDdl) {
                log.error("接收到DDL语句,暂不支持!! topic={} msgId {}", topic, msgId);
                return;
            }

            String type = binlogJson.getString("type");
            JSONArray afterArr = binlogJson.getJSONArray("data");
            JSONArray beforeArr = binlogJson.getJSONArray("old");

            int size = afterArr.size();
            for (int i = 0; i < size; i++) {
                switch (TableChangeEnum.getTableChangeEnumByField(type)){
                    case delete:
                        dealDeleteEvent(topic, msgId, afterArr.getJSONObject(i));
                        break;
                    case insert:
                        dealInsertEvent(topic, msgId, afterArr.getJSONObject(i));
                        break;
                    case update:
                        dealUpdateEvent(topic, msgId, beforeArr.getJSONObject(i), afterArr.getJSONObject(i));
                        break;
                    default:
                        log.error("未知的type:{}", type);
                }
            }
        } catch (Exception e) {
            redisOperator.del(key);
            if (reconsumeTimes > MSG_REPEAT_TIMES_THRESHOLD) {
                log.error("处理RocketMq消息失败! topic={} msgId {},{}", topic, msgId, e.getMessage());
            } else {
                log.warn("处理RocketMq消息失败! topic={} msgId {}", topic, msgId, e);
            }
            throw new RuntimeException(e);
        }
    }

    /**
     * 处理插入事件
     * @param topic
     * @param msgId
     * @param after
     */
    protected abstract void dealInsertEvent(String topic, String msgId, JSONObject after);

    /**
     * 处理更新事件
     * @param topic
     * @param msgId
     * @param before
     * @param after
     */
    protected abstract void dealUpdateEvent(String topic, String msgId, JSONObject before, JSONObject after);

    /**
     * 处理删除事件
     * @param topic
     * @param msgId
     * @param afterJson
     */
    protected abstract void dealDeleteEvent(String topic, String msgId, JSONObject afterJson);
}
