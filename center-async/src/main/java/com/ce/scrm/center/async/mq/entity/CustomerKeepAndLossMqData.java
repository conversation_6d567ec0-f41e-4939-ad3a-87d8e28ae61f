package com.ce.scrm.center.async.mq.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 成交客户保有流失消息体
 */
@Data
public class CustomerKeepAndLossMqData implements Serializable {

    /**
     * 客户Id
     */
    private String custId;

    /**
     * 分公司ID
     */
    private String subId;

    /**
     * 分公司名称
     */
    private String subName;

    /**
     * 未打卡自然月数
     */
    private Integer monthWithoutClock;

    /**
     * 流转方式：1总监待分 2经理待分
     */
    private Integer circulationType;

    /**
     * 修改时间
     */
    private Date updateTime;

}
