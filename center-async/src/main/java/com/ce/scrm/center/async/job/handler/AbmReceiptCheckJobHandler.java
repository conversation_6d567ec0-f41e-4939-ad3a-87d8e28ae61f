package com.ce.scrm.center.async.job.handler;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dao.entity.SdrPushSaleReview;
import com.ce.scrm.center.dao.entity.SjAssignDetail;
import com.ce.scrm.center.dao.service.SjAssignDetailService;
import com.ce.scrm.center.service.business.abm.AbmUpdateProtectBusiness;
import com.ce.scrm.center.service.business.abm.BizOppDistributeBusiness;
import com.ce.scrm.center.service.business.abm.SdrPushSaleReviewBusiness;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmReleaseProtectDto;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.ConvertRelationEnum;
import com.ce.scrm.center.support.redis.RedisOperator;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * @version 1.0
 * @Description: 回执确认
 * @Author: lijinpeng
 * @Date: 2024/10/8 11:04
 */
@Slf4j
@Component
public class AbmReceiptCheckJobHandler {

    @Resource
    private SjAssignDetailService sjAssignDetailService;

    @Resource
    private BizOppDistributeBusiness bizOppDistributeBusiness;

    @Resource
    private SdrPushSaleReviewBusiness sdrPushSaleReviewBusiness;

    @Resource
    private AbmUpdateProtectBusiness abmUpdateProtectBusiness;

    @Resource
    private RedisOperator redisOperator;

    /**
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @Description abm商机下发后回执检查
     * <AUTHOR>
     * @date 2024/10/8 11:25
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_ABM_RECEIPT_CHECK_JOB_HANDLER)
    public ReturnT<String> checkReceipt(String param) {
        log.info("===========abm商机下发后回执检查==========");
        if (StringUtils.isEmpty(param)) {
            return ReturnT.SUCCESS;
        }
        Long sjAssignDetailId = Long.valueOf(param);
        SjAssignDetail sjAssignDetail = sjAssignDetailService.getById(sjAssignDetailId);
        if (Objects.isNull(sjAssignDetail)) {
            log.warn("无效下发记录:{} ", sjAssignDetailId);
            return ReturnT.SUCCESS;
        }

        try {
            Integer receiptFlag = sjAssignDetail.getReceiptFlag();

            if (Objects.equals(receiptFlag, 0)) {
                // 未回执
                Date receiptEndTime = sjAssignDetail.getReceiptEndTime();
                // 校验已经重分配的次数
                Long reviewId = sjAssignDetail.getReviewId();
                Long times = bizOppDistributeBusiness.countDistributeTimesByReviewId(reviewId);

                // 回执标记 0:待回执 1:已回执 2：超时未回执
                sjAssignDetail.setReceiptFlag(2);
                sjAssignDetail.setUpdatedId("1024");
                sjAssignDetail.setUpdatedTime(new Date());
                boolean update = sjAssignDetailService.updateById(sjAssignDetail);
                if (!update) {
                    log.error("更新回执状态失败,{}", JSON.toJSONString(sjAssignDetail));
                }

                if (times <= 1) {
                    SdrPushSaleReview sdrPushSaleReview = sdrPushSaleReviewBusiness.getByReviewId(reviewId);
                    // 重新走分配流程
                    Optional<String> optional = bizOppDistributeBusiness.bizOppDistribute(sdrPushSaleReview);
                    optional.ifPresent(s -> log.error("重新分配失败，{} 记录 {}", s, JSON.toJSONString(sdrPushSaleReview)));
                } else {
                    // 释放接口
                    AbmReleaseProtectDto abmReleaseProtectDto = new AbmReleaseProtectDto();
                    String customerId = sjAssignDetail.getCustomerId();
                    abmReleaseProtectDto.setCustId(customerId);
                    abmReleaseProtectDto.setReleaseReason(ConvertRelationEnum.ABM_SJ_HUIZHI_CHAOQI_RELEASE.getLable());
                    abmReleaseProtectDto.setOperator("1024");
                    abmReleaseProtectDto.setConvertType(ConvertRelationEnum.ABM_SJ_HUIZHI_CHAOQI_RELEASE.getValue());
                    abmReleaseProtectDto.setConvertTypeDesc(ConvertRelationEnum.ABM_SJ_HUIZHI_CHAOQI_RELEASE.getLable());
                    Optional<String> optional = abmUpdateProtectBusiness.releaseProtect(abmReleaseProtectDto);
                    optional.ifPresent(s -> log.error("商机回执超期释放客户失败,{}", s));

                    Optional<String> cleared = abmUpdateProtectBusiness.clearRequireReceiptFlag(customerId, "1024", null);
                    cleared.ifPresent(s -> log.error("更新客户表-是否需要回执失败,{}", s));
                }
            } else {
                // 已回执 不处理
            }
        } catch (Exception e) {
            log.error("abm商机下发后回执检查失败，message:{}", e.getMessage());
        }

        log.info("===========abm商机下发后回执检查结束==========");
        return ReturnT.SUCCESS;
    }


}
