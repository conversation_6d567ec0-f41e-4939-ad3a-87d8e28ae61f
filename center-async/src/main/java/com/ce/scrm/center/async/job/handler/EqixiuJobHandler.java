package com.ce.scrm.center.async.job.handler;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.center.async.mq.consumer.EqiXiuDataReceiverConsumer;
import com.ce.scrm.center.dao.entity.EqixiuDataResult;
import com.ce.scrm.center.dao.entity.EqixiuLotteryDetail;
import com.ce.scrm.center.dao.service.IEqixiuActivityShareInfoService;
import com.ce.scrm.center.dao.service.IEqixiuDataResultService;
import com.ce.scrm.center.dao.service.IEqixiuLotteryDetailService;
import com.ce.scrm.center.service.config.UniqueIdService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.eqixiu.sdk.service.CreationService;
import com.ce.scrm.center.service.eqixiu.support.entity.EqixiuCodeInfo;
import com.ce.scrm.center.service.eqixiu.support.entity.UserInfo;
import com.ce.scrm.center.support.redis.RedisOperator;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ce.scrm.center.async.mq.consumer.EqiXiuDataReceiverConsumer.E_QI_XIU_CACHE;

@Slf4j
@Component
public class EqixiuJobHandler {

    @Autowired
    private CreationService creationService;

    @Autowired
    private IEqixiuLotteryDetailService eqixiuLotteryDetailService;

    @Autowired
    private EqiXiuDataReceiverConsumer eqiXiuDataReceiverConsumer;

    @Autowired
    private RedisOperator redisOperator;

    @Autowired
    private UniqueIdService uniqueIdService;

    @Autowired
    private IEqixiuActivityShareInfoService eqixiuActivityShareInfoService;

    @Autowired
    private IEqixiuDataResultService eqxiuDataResultService;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_EQIXIU_JOB_HANDLER)
    public ReturnT<String> scrmEqixiuJobHandler(String param) {
        String cacheValue = redisOperator.get(E_QI_XIU_CACHE);
        if (StringUtils.isBlank(cacheValue) && !Objects.equals(param, "hard")) {
            return ReturnT.SUCCESS;
        }

        log.info("开始拉取电子兑换码中奖信息:{}", param);
        try {
            // 查询活动信息表中的最后一个企业和个人的兑换码记录
            EqixiuLotteryDetail lastCorporateDetail = getLastLotteryDetailByType(2);
            EqixiuLotteryDetail lastPersonalDetail = getLastLotteryDetailByType(1);
            // 同步企业电子券
            syncGiftCodes(2, lastCorporateDetail, creationService::getGiftCodeHdGet);
            // 同步个人电子券
            syncGiftCodes(1, lastPersonalDetail, creationService::getGiftCodeMyGet);
            redisOperator.del(E_QI_XIU_CACHE);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步用户电子兑换码信息失败", e);
            return ReturnT.FAIL;
        }
    }

    private EqixiuLotteryDetail getLastLotteryDetailByType(int type) {
        return eqixiuLotteryDetailService.getOne(new LambdaQueryWrapper<EqixiuLotteryDetail>()
                .eq(EqixiuLotteryDetail::getType, type)
                .orderByDesc(EqixiuLotteryDetail::getThirdId)
                .last("limit 1"));
    }

    private void syncGiftCodes(int type, EqixiuLotteryDetail lastDetail, GiftCodeFetcher fetcher) {
        int page = 1;
        int pageSize = 10;
        while (true) {
            List<UserInfo> userInfos = fetcher.getInfo(String.valueOf(page), String.valueOf(page * pageSize));
            if (CollectionUtils.isEmpty(userInfos)) {
                log.info("获取{}电子券信息为空", type == 2 ? "企业" : "个人");
                break;
            }
            saveUserInfos(type, userInfos);
            // 检查是否达到最后一个已保存的ID，若是则停止同步
            if (lastDetail != null && userInfos.stream().anyMatch(u -> u.getId().equals(lastDetail.getThirdId()))) {
                break;
            }
            page++;
        }
    }

    private void saveUserInfos(int type, List<UserInfo> userInfos) {
        List<EqixiuLotteryDetail> detailsToSave = userInfos.stream()
                .map(userInfo -> createLotteryDetail(type, userInfo))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (!detailsToSave.isEmpty()) {
            try {
                for (EqixiuLotteryDetail eqixiuLotteryDetail : detailsToSave) {
                    try {
                        eqixiuLotteryDetailService.save(eqixiuLotteryDetail);
                    } catch (DuplicateKeyException e) {
                        log.warn("用户信息重复，保存失败: {}", eqixiuLotteryDetail.getId());
                    } catch (Exception e) {
                        log.error("创建用户信息失败: {}", JSONObject.toJSONString(eqixiuLotteryDetail), e);
                    }
                }
            } catch (Exception e) {
                log.error("批量保存用户信息失败", e);
            }
        }
    }

    private EqixiuLotteryDetail createLotteryDetail(int type, UserInfo userInfo) {
        try {
            EqixiuCodeInfo eqixiuCodeDto = eqiXiuDataReceiverConsumer.getEqixiuCodeDto(userInfo.getHeadImgUrl());
            if (eqixiuCodeDto == null) {
                log.warn("解析用户信息失败: {}", JSONObject.toJSONString(userInfo));
                eqixiuCodeDto = new EqixiuCodeInfo();
            }
            EqixiuDataResult eqixiuDataResult = eqxiuDataResultService.getById(eqixiuCodeDto.getEqixiuDataResultId());
            if (null == eqixiuDataResult) {
                log.error("获取分享信息失败:{}", eqixiuCodeDto.getEqixiuDataResultId());
                return null;
            }
            EqixiuLotteryDetail detail = new EqixiuLotteryDetail();
            detail.setId(uniqueIdService.getId());
            detail.setThirdId(userInfo.getId());
            detail.setLotteryName(userInfo.getName());
            detail.setLotteryCode(userInfo.getCode());
            detail.setCreationId(userInfo.getCreationId());
            detail.setPrizeLevel(String.valueOf(userInfo.getPrizeLevel()));
            detail.setLotteryStatus(userInfo.getStatus());
            detail.setNickName(userInfo.getNickName());
            detail.setCustomerId(eqixiuDataResult.getCustomerId());
            detail.setCodeDecoderInfo(JSONObject.toJSONString(eqixiuCodeDto));
            detail.setOpenId(userInfo.getOpenId());
            detail.setType(type);
            detail.setEncodeId(userInfo.getEncodeId());
            detail.setCreateTime(new Date());
            detail.setUpdateTime(new Date());
            return detail;
        } catch (Exception e) {
            log.error("创建用户信息失败: {}", JSONObject.toJSONString(userInfo), e);
        }
        return null;
    }

    // 定义一个函数接口以支持不同的获取方法
    @FunctionalInterface
    private interface GiftCodeFetcher {
        List<UserInfo> getInfo(String startId, String endId);
    }
}
