

package com.ce.scrm.center.async.util;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.tencentcloudapi.asr.v20190614.models.SentenceWords;
import com.tencentcloudapi.common.AbstractModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.HashMap;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class DescribeTaskStatusResponse extends AbstractModel {

    @SerializedName("Data")
    @Expose
    private TaskStatus Data;

    @SerializedName("RequestId")
    @Expose
    private String RequestId;

    public void toMap(HashMap<String, String> map, String prefix) {
        this.setParamObj(map, prefix + "Data.", this.Data);
        this.setParamSimple(map, prefix + "RequestId", this.RequestId);
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @NoArgsConstructor
    public static class TaskStatus extends AbstractModel {

        @SerializedName("TaskId")
        @Expose
        private Long TaskId;

        @SerializedName("Status")
        @Expose
        private Long Status;

        @SerializedName("StatusStr")
        @Expose
        private String StatusStr;

        @SerializedName("Result")
        @Expose
        private String Result;

        @SerializedName("ErrorMsg")
        @Expose
        private String ErrorMsg;

        @SerializedName("ResultDetail")
        @Expose
        private SentenceDetail[] ResultDetail;

        public void toMap(HashMap<String, String> map, String prefix) {
            this.setParamSimple(map, prefix + "TaskId", this.TaskId);
            this.setParamSimple(map, prefix + "Status", this.Status);
            this.setParamSimple(map, prefix + "StatusStr", this.StatusStr);
            this.setParamSimple(map, prefix + "Result", this.Result);
            this.setParamSimple(map, prefix + "ErrorMsg", this.ErrorMsg);
            this.setParamArrayObj(map, prefix + "ResultDetail.", this.ResultDetail);
        }

    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    @NoArgsConstructor
    public static class SentenceDetail extends AbstractModel {

        @SerializedName("FinalSentence")
        @Expose
        private String FinalSentence;

        @SerializedName("SliceSentence")
        @Expose
        private String SliceSentence;

        @SerializedName("StartMs")
        @Expose
        private Long StartMs;

        @SerializedName("EndMs")
        @Expose
        private Long EndMs;

        @SerializedName("WordsNum")
        @Expose
        private Long WordsNum;

        @SerializedName("Words")
        @Expose
        private SentenceWords[] Words;

        @SerializedName("SpeechSpeed")
        @Expose
        private Float SpeechSpeed;

        @SerializedName("SpeakerId")
        @Expose
        private Long SpeakerId;

        public void toMap(HashMap<String, String> map, String prefix) {
            this.setParamSimple(map, prefix + "FinalSentence", this.FinalSentence);
            this.setParamSimple(map, prefix + "SliceSentence", this.SliceSentence);
            this.setParamSimple(map, prefix + "StartMs", this.StartMs);
            this.setParamSimple(map, prefix + "EndMs", this.EndMs);
            this.setParamSimple(map, prefix + "WordsNum", this.WordsNum);
            this.setParamArrayObj(map, prefix + "Words.", this.Words);
            this.setParamSimple(map, prefix + "SpeechSpeed", this.SpeechSpeed);
        }
    }


}
