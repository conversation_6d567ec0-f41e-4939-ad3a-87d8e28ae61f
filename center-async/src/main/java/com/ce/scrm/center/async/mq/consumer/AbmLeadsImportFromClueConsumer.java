package com.ce.scrm.center.async.mq.consumer;

import cn.ce.cesupport.enums.abm.LeadsImportSourceEnum;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dao.entity.ClueInfo;
import com.ce.scrm.center.service.business.abm.CustomerLeadsBusiness;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerLeadsImportOrDistributeDto;
import com.ce.scrm.center.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 跨境abm CRM现有渠道 leads导入
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_SEND_CLUE_NEW_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_SEND_CLUE_NEW_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class AbmLeadsImportFromClueConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CustomerLeadsBusiness customerLeadsBusiness;

    @Override
    public void onMessage(MessageExt messageExt) {
        String messageId = messageExt.getMsgId();
        String message = new String(messageExt.getBody());
        if (StringUtils.isBlank(message)) {
			log.warn("收到SCRM_SEND_CLUE_NEW_TOPIC消息为空，msgId={}", messageId);
            return;
        }
        log.info("SCRM_SEND_CLUE_NEW_TOPIC，msgId={},消息内容：{}", messageId, message);
        try {
            List<ClueInfo> clueInfo = JSON.parseArray(message, ClueInfo.class);
            if (CollectionUtils.isEmpty(clueInfo)) {
				log.warn("CRM现有渠道 leads导入, 接受到的消息为空，message={}", message);
                return;
            }
	        CustomerLeadsImportOrDistributeDto importOrDistributeDto = new CustomerLeadsImportOrDistributeDto();
			importOrDistributeDto.setLeadsImportFrom(LeadsImportSourceEnum.INTENT_CLUE.getCode());
	        importOrDistributeDto.setClueList(clueInfo);
			log.info("CRM现有渠道-leads导入importOrDistributeDto={}", JSON.toJSONString(importOrDistributeDto));
	        Boolean b = customerLeadsBusiness.customerLeadsHandle(importOrDistributeDto);
			log.info("CRM现有渠道-leads导入结果={}", b);
        } catch (Exception e) {
            log.error("跨境abm,CRM现有渠道,leads导入, messageId={} message={}", messageId, message, e);
        }
    }
}
