package com.ce.scrm.center.async.job.handler;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.business.CirculationLossBusiness;
import com.ce.scrm.center.service.constant.LeadDayConstants;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 定时发送微信消息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/7/23
 */
@Slf4j
@Component
public class CustomerWillLossJobHandler {

    @Resource
    private CirculationLossBusiness circulationLossBusiness;

    /**
     * 发送商务即将流失的客户微信消息
     *
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2024/7/23
     * @version 1.0.0
     **/
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_WX_WILL_LOSS_JOB_HANDLER)
    public ReturnT<String> willLossSendMsg(String param) {
        log.info("===========定时给商务发送即将流失的微信消息开始==========");
        circulationLossBusiness.willCirculationLossSendMsg(AssignCustSourceSpecialEnum.LOSS);
        log.info("===========定时给商务发送即将流失的微信消息结束==========");
        return ReturnT.SUCCESS;
    }

    /**
     * 将客户放到客户池和到总监待分配
     *
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2024/7/25
     * @version 1.0.0
     **/
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_WILL_LOSS_POOL_MAJOR_JOB_HANDLER)
    public ReturnT<String> willLossToPoolOrMajor(String param) {
        log.info("===========定时将客户放到客户池和到总监待分配开始==========");
        DateTime beginDayZj = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -LeadDayConstants.GENERAL_LOSS_DAY_NUM);
        DateTime endDayZj = DateUtil.endOfDay(new Date());
        DateTime beginDayPool = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -LeadDayConstants.POOL_LOSS_DAY_NUM - LeadDayConstants.POOL_LOSS_ALL_DAY_NUM);
        DateTime endDayPool = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -LeadDayConstants.POOL_LOSS_DAY_NUM);
        // 控制台手动执行
        if (StringUtils.isNotBlank(param)) {
            JSONObject jsonObject = JSONObject.parseObject(param);
            if (jsonObject.getString("startDateTimeZj") != null && jsonObject.getString("endDateTimeZj") != null) {
                //放置总监时间
                String startTimeStrZj = jsonObject.getString("startDateTimeZj");
                String endTimeStrZj = jsonObject.getString("endDateTimeZj");
                beginDayZj = DateUtil.parse(startTimeStrZj, "yyyy-MM-dd HH:mm:ss");
                endDayZj = DateUtil.parse(endTimeStrZj,"yyyy-MM-dd HH:mm:ss");
            }
            if (jsonObject.getString("startDateTimePool") != null && jsonObject.getString("endDateTimePool") != null) {
                //放置客户池时间
                String startTimeStrPool = jsonObject.getString("startDateTimePool");
                String endTimeStrPool = jsonObject.getString("endDateTimePool");
                beginDayPool = DateUtil.parse(startTimeStrPool, "yyyy-MM-dd HH:mm:ss");
                endDayPool = DateUtil.parse(endTimeStrPool,"yyyy-MM-dd HH:mm:ss");
            }
        }
        // 放客户池      (产品到期超过180天,即流失)
        circulationLossBusiness.lossPool(beginDayPool, endDayPool);
        // 放总监待分配   (产品到期在180天之内,总监可以手动流失,也可以分配)
        circulationLossBusiness.generalDistribute(beginDayZj, endDayZj);
        log.info("===========定时将客户放到客户池和到总监待分配结束==========");
        return ReturnT.SUCCESS;
    }

    /**
     * Description: 客户即将流转时，给商务发送企微通知
     * @author: JiuDD
     * @param param  xxl job参数
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * date: 2024/7/25 19:12
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_WX_WILL_CIRCULATION_JOB_HANDLER)
    public ReturnT<String> willCirculationSendMsg(String param) {
        log.info("===========定时给商务发送即将流转的微信消息开始==========");
        circulationLossBusiness.willCirculationLossSendMsg(AssignCustSourceSpecialEnum.CIRCULATION);
        log.info("===========定时给商务发送即将流转的微信消息结束==========");
        return ReturnT.SUCCESS;
    }
}
