package com.ce.scrm.center.async.mq.consumer;

import cn.ce.cesupport.enums.favorites.ConvertRelationEnum;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.mq.entity.CustomerLabel;
import com.ce.scrm.center.async.mq.entity.TableChangeEnum;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerLayerChangeLog;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.CustomerLayerChangeLogService;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogBusinessDto;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.CustomerLayerEnum;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.SmaConvertLogThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 监听客户表binlog同步 维护customer表 customer_layer 、customer_grade字段到保护关系表
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CESUPPORT_SCRM_CUST_LABEL_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_MAINTAIN_PROTECTED_INFO_2_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class MaintainProtectInfo2Consumer implements RocketMQListener<MessageExt> {

    @Resource
    private CmCustProtectService cmCustProtectService;

//    @Resource
//    private CustomerThirdService customerThirdService;

    @Resource
    private CustomerLayerChangeLogService customerLayerChangeLogService;

    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;

    @Override
    public void onMessage(MessageExt messageExt) {
        String param = new String(messageExt.getBody());
        if (StrUtil.isBlank(param)) {
            log.error("cdp 同步数据 到scrm 参数为空，消息ID为:{}", messageExt.getMsgId());
            return;
        }
        try {
            CustomerLabel customerLabel = JSONObject.parseObject(param, CustomerLabel.class);
            if (customerLabel == null || StringUtils.isEmpty(customerLabel.getCustId())) {
                return;
            }
            startAsyncSql(customerLabel.getCustId(),customerLabel.getCustomerLayer(),customerLabel.getCustomerGrade(),customerLabel.getCustName());
        } catch (Exception e) {
            log.error("MaintainProtectInfoConsumer失败! param={} ", param,e);
        }
    }

    /**
     * 同步SQL
     * @param custId
     */
    private void startAsyncSql(String custId,Integer customerLayer,Integer customerGrade,String custName) {

        log.info("MaintainProtectInfoConsumer.startAsyncSql custId:{}",custId);
        if(StringUtils.isEmpty(custId)) {
            return;
        }
        // 查询当前保护关系
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, custId).one();
        if (cmCustProtect == null) {
            return;
        }
        if (!checkData(cmCustProtect,customerLayer,customerGrade)) {
            Integer oldCustomerLayer = cmCustProtect.getCustomerLayer();
            Integer oldCustomerGrade = cmCustProtect.getCustomerGrade();
            cmCustProtect.setCustomerLayer(customerLayer);
            cmCustProtect.setCustomerGrade(customerGrade);
            cmCustProtectService.updateById(cmCustProtect);
            // 添加客户layer变化记录
            if (customerLayer != null && oldCustomerLayer != null && !Objects.equals(oldCustomerLayer, customerLayer)) {
                customerLayerChangeLogService.save(
                        CustomerLayerChangeLog.builder()
                                .customerId(custId)
                                .oldLayer(customerLayer)
                                .newLayer(oldCustomerLayer)
                                .build()
                );
                // 流转日志build
                ConvertLogBusinessDto convertLogBusinessDto = ConvertLogBusinessDto.builder()
                        .custId(custId)
                        .createTime(new Date())
                        .createBy("1024")
                        .convertType(ConvertRelationEnum.CUSTOMER_LAYER_CHANGE.getValue())
                        .releaseReason("客户从["+ CustomerLayerEnum.getDescriptionByCode(oldCustomerLayer)
                                + "]变为["+CustomerLayerEnum.getDescriptionByCode(customerLayer)
                                + "]")
                        .custName(custName)
                        .build();
                smaConvertLogThirdService.insertLog(convertLogBusinessDto);
            }
        }

    }

    private boolean checkData(CmCustProtect cmCustProtect,Integer customerLayer,Integer customerGrade) {

        if (!Objects.equals(customerLayer, cmCustProtect.getCustomerLayer())) {
            return false;
        }

        if (!Objects.equals(customerGrade, cmCustProtect.getCustomerGrade())) {
            return false;
        }

        return true;
    }


}
