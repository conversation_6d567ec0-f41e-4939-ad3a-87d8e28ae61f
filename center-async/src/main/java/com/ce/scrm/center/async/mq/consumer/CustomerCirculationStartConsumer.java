package com.ce.scrm.center.async.mq.consumer;

import cn.ce.cesupport.base.constants.ScrmAppConstants;
import cn.ce.cesupport.enums.YesOrNoEnum;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.mq.entity.TableChangeEnum;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerCirculation;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.CustomerCirculationService;
import com.ce.scrm.center.service.business.ProtectBusiness;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogBusinessDto;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum;
import com.ce.scrm.center.service.enums.ConvertRelationEnum;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.third.invoke.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * customer_circulation流转表 开始流转 监听customer_circulation流转表binlog insert的时候 并且流转日期==今天
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_CUSTOMER_CIRCULATION_BINLOG_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_CUSTOMER_CIRCULATION_START_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class CustomerCirculationStartConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private ProtectBusiness protectBusiness;

    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private CustomerCirculationService customerCirculationService;

    @Override
    public void onMessage(MessageExt messageExt) {
        String topic = messageExt.getTopic();
        String msgId = messageExt.getMsgId();
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            return;
        }
        try {
            JSONObject binlogJson = com.alibaba.fastjson.JSON.parseObject(data);
            /**
             * 处理binlog记录的过程：
             * 1、判断是否操作类型：ddl、dml， ddl 直接发消息通知【企微】?
             * 2、dml时判断type:
             *     - INSERT：新增数据，则直接生产对应的事件
             *     - UPDATE：修改数据，判断事件字段是否发生变化(逻辑由子类实现)
             *     - DELETE：删除数据，暂时没定怎么处理？？
             */
            Boolean isDdl = binlogJson.getBoolean("isDdl");
            if (isDdl) {
                return;
            }

            // 针对dml语句进行校验
            Map<String, Boolean> repeatMsgMap = new HashMap<>();

            String type = binlogJson.getString("type");
            JSONArray afterAry = binlogJson.getJSONArray("data");
            JSONArray beforeAry = binlogJson.getJSONArray("old");
            int size = afterAry.size();
            for (int i = 0; i < size; i++) {
                JSONObject afterJson = afterAry.getJSONObject(i);
                if (repeatMsgMap.getOrDefault(afterJson.getString("id"), false)) {
                    continue;
                }
                if (TableChangeEnum.delete.getField().equals(type)) {
                    continue;
                }
                if (TableChangeEnum.insert.getField().equals(type)) {
                    customerCirculationStart(afterJson.toJSONString());
                    continue;
                }
                if (TableChangeEnum.update.getField().equals(type)) {
                    continue;
                }
            }
        } catch (Exception e) {
            log.error("客户流转失败! topic={} msgId {},{}", topic, msgId, e.getMessage());
        }
    }

    /**
     * 客户开始流转
     * @param jsonStr
     */
    private void customerCirculationStart(String jsonStr) {
        log.info("customerCirculationStart,开始流转 jsonStr={}", jsonStr);
        CustomerCirculation customerCirculation = JSONObject.parseObject(jsonStr, CustomerCirculation.class);
        if (customerCirculation == null || customerCirculation.getCustId() == null || customerCirculation.getPreDate() == null || customerCirculation.getId() == null) {
            log.error("客户开始流转,数据异常! jsonStr={}", jsonStr);
            return;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        Date dateWithoutTime = calendar.getTime();
        if(Objects.equals(dateWithoutTime,customerCirculation.getPreDate())) {
            circulationCust(customerCirculation.getCustId(),customerCirculation.getId());
        }
        log.info("customerCirculationStart,流转结束 jsonStr={}", jsonStr);
    }

    /**
     * Description: 流转客户到总监待分配------
     * @author: JiuDD
     * @param customerId 保护关系
     * @return void
     * date: 2024/9/4 19:00
     */

    @Transactional
    public void circulationCust(String customerId,Long customerCirculationId) {
        CmCustProtect cmCustProtect = protectBusiness.getCmCustProtect(customerId);
        if (Objects.isNull(cmCustProtect)){
            log.error("需要排查一下：流转的时候保护关系查询为空，customerId={}",customerId);
            return;
        }
        if (!Objects.equals(cmCustProtect.getStatus(),ProtectStateEnum.PROTECT.getState())) {
            // 特殊情况 今天这个被流失了 就不流转了
            customerCirculationService.lambdaUpdate()
                    .set(CustomerCirculation::getStatus, 2)
                    .eq(CustomerCirculation::getId, customerCirculationId)
                    .update();
            return;
        }
        // 1 [真正的流转动作] 更新保护关系表
        circulation(cmCustProtect);
        // 2 流转完成之后，将 待流转客户表 delete flag 置为 1
//        circulationLossBusiness.deleteCirculationLoss(new CirculationLossUpdateBusinessDto().setCustId(cmCustProtect.getCustId()).setOrigin(AssignCustSourceSpecialEnum.CIRCULATION.getValue()));
        // 3 修改新流转表状态
        customerCirculationService.lambdaUpdate()
                .set(CustomerCirculation::getStatus, YesOrNoEnum.YES.getCode())
                .eq(CustomerCirculation::getId, customerCirculationId)
                .update();
        // 4 添加流转记录
        insertSmaConvertLog(cmCustProtect);
    }

    /**
     * Description: [真正的流转动作] 更新保护关系表
     * @author: JiuDD
     * @param cmCustProtect 保护关系
     * @return void
     * date: 2024/7/25 17:06
     */
    public void circulation(CmCustProtect cmCustProtect) {
        // 目前 只流转到 总监待分配，将来根据需求再扩展到经理待分配
        Integer circulationType = 1;
        CmCustProtect updateCmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, cmCustProtect.getCustId()).one();
        updateCmCustProtect.setId(cmCustProtect.getId());
        updateCmCustProtect.setCustId(cmCustProtect.getCustId());
        updateCmCustProtect.setAssignCustSource(AssignCustSourceSpecialEnum.CIRCULATION.getValue());
        updateCmCustProtect.setStatus((1 == circulationType) ? ProtectStateEnum.MAJOR_WILL_ASSIGN.getState() : ProtectStateEnum.MANAGER_WILL_ASSIGN.getState());
        updateCmCustProtect.setUpdateTime(new Date());
        updateCmCustProtect.setUpdateBy(ScrmAppConstants.SYSTEM_ADMIN);
        cmCustProtectService.updateByCustId(updateCmCustProtect);
    }

    /**
     * Description: insert流转记录
     * @author: JiuDD
     * @param cmCustProtect 保护关系
     * @return void
     * date: 2024/7/25 17:27
     */
    public void insertSmaConvertLog(CmCustProtect cmCustProtect) {
        ConvertLogBusinessDto logBusinessDto = new ConvertLogBusinessDto();
        logBusinessDto.setSalerId(cmCustProtect.getSalerId());
        logBusinessDto.setDeptOfSalerId(cmCustProtect.getBussdeptId());
        logBusinessDto.setBuOfSalerId(cmCustProtect.getBuId());
        logBusinessDto.setSubcompanyOfSalerId(cmCustProtect.getSubcompanyId());
        logBusinessDto.setAreaOfSalerId(cmCustProtect.getAreaId());
        logBusinessDto.setAreaOfCurSalerId(cmCustProtect.getAreaId());
        logBusinessDto.setCustType(cmCustProtect.getCustType());
        logBusinessDto.setCustId(cmCustProtect.getCustId());
        logBusinessDto.setCustName(cmCustProtect.getCustName());
        logBusinessDto.setSubcompanyOfCurSalerId(cmCustProtect.getSubcompanyId());
        logBusinessDto.setDeptOfCurSalerId(null);
        logBusinessDto.setBuOfCurSalerId(null);
        logBusinessDto.setCreateBy(ScrmAppConstants.SYSTEM_ADMIN);
        logBusinessDto.setCreateTime(new Date());
        logBusinessDto.setConvertType(ConvertRelationEnum.CIRCULATION.getValue());
        logBusinessDto.setReleaseReason(ConvertRelationEnum.CIRCULATION.getLable());
        smaConvertLogThirdService.insertLog(logBusinessDto);
    }

}
