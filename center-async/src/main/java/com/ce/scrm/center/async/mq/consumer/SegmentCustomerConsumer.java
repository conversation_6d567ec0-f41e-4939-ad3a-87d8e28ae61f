package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.async.mq.entity.SegmentMessageInfo;
import com.ce.scrm.center.dao.entity.SegmentDetail;
import com.ce.scrm.center.service.business.SegmentBusiness;
import com.ce.scrm.center.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 分群下发消费者
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_SEGMENT_CUSTOMER_INFO_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.CDP_SEGMENT_CUSTOMER_INFO_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class SegmentCustomerConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private SegmentBusiness segmentBusiness;

    @Override
    public void onMessage(MessageExt messageExt) {
        String messageId = messageExt.getMsgId();
        String message = new String(messageExt.getBody());
        if (StringUtils.isBlank(message)) {
            return;
        }
        log.info("CDP_GROUP_CUSTOMER_INFO_TOPIC，msgId={},消息内容：{}", messageId, message);
        try {
            SegmentMessageInfo campaignReportInfo = JSON.parseObject(message, SegmentMessageInfo.class);
            if (StringUtils.isNotBlank(campaignReportInfo.getDistinctId()) && StringUtils.isNotBlank(campaignReportInfo.getGroupId())) {
                SegmentDetail segmentDetail = new SegmentDetail();
                segmentDetail.setSegmentId(campaignReportInfo.getGroupId());
                segmentDetail.setCustomerId(campaignReportInfo.getDistinctId());
                segmentBusiness.segmentDetailCreate(segmentDetail);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
