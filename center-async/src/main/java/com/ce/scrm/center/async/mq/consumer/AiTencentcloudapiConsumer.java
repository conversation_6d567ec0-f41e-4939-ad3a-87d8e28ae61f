package com.ce.scrm.center.async.mq.consumer;

import cn.ce.cesupport.enums.SalesStageEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.util.AIJsonChecker;
import com.ce.scrm.center.dao.entity.AiPromptInfo;
import com.ce.scrm.center.dao.entity.AiVoiceAnalyze;
import com.ce.scrm.center.dao.service.AiPromptInfoService;
import com.ce.scrm.center.dao.service.AiVoiceAnalyzeService;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.config.NlpProperties;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.support.redis.RedisOperator;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.rmi.RemoteException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_VOICE_AI_NOTIFY_TOPIC;

@Slf4j
@Component
@RocketMQMessageListener(topic = SCRM_VOICE_AI_NOTIFY_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_VOICE_AI_NOTIFY_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 10)
public class AiTencentcloudapiConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private RocketMqOperate rocketMqOperate;

    @Autowired
    private AiVoiceAnalyzeService aiVoiceAnalyzeService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Value("${viewUrl:}")
    private String viewUrl;

    @Autowired
    private SendWxMessage sendWxMessage;

    @Autowired
    private AiPromptInfoService aiPromptInfoService;

    public static final String resp_format = "{\n" +
            "  \"analyze\"： 使用 text 排版，控制在 300字以内，加标点和换行符 \n" +
            "  \"salesStage\": 客户销售阶段 ，从 初步建联，需求确认 ，方案汇报 ， 商务沟通， 合同签署 ， 赢单 ，丢单 中取一个不能超出这个范围\n" +
            "}\n" +
            "\n" +
            "严格返回 json 格式";

    public static final String genjinneirong = "flowContent";
    public static final String xiaoshoujieduan = "saleStep";
    @Autowired
    private RedisOperator redisOperator;

    @Autowired
    private AiSpeechRecognitionConsumer aiSpeechRecognitionConsumer;

    //@Transactional(rollbackFor = Exception.class)
    @Override
    public void onMessage(MessageExt messageExt) {
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            log.warn("语音分析结果不存在");
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(data);
        Long id = jsonObject.getLong("id");
        AiVoiceAnalyze analyze = aiVoiceAnalyzeService.getById(id);
        if (null == analyze) {
            log.warn("语音分析结果不存在:{}", id);
            throw new RuntimeException("语音分析结果不存在");
        }
        if (!Objects.equals(analyze.getGroupType(), 1)) {
            log.warn("消息消费异常 group_type 不正确 :{},id:{}", analyze.getGroupType(), analyze.getId());
            return;
        }
        if (Objects.equals(analyze.getAnalyzeStatus(), 2) || Objects.equals(analyze.getVoiceStatus(), 3)) {
            log.warn("语音识别已结束:{}", analyze.getId());
            return;
        }
//        // 查询 group 下单纯语音识别是否已经识别结束
//        List<AiVoiceAnalyze> list = aiVoiceAnalyzeService.lambdaQuery().select(AiVoiceAnalyze::getVoiceStatus, AiVoiceAnalyze::getRespFormat)
//                .eq(AiVoiceAnalyze::getGroupId, analyze.getGroupId())
//                .eq(AiVoiceAnalyze::getGroupType, 0).orderByAsc(AiVoiceAnalyze::getGroupOrder).list();
//        long count = list.stream().filter(T -> !Objects.equals(T.getVoiceStatus(), 2)).count();
//        if (!CollectionUtils.isEmpty(list) && count > 0) {
//            log.warn("group单纯语音识别未完成:{}", analyze.getGroupId());
//            // 语音识别完成会 重新触发消息
//            return;
//        }
//        if (CollectionUtils.isEmpty(list) && !Objects.equals(analyze.getVoiceStatus(), 2)) {
//            return;
//        }
//        if (!CollectionUtils.isEmpty(list)) {
//            String respFormat = list.stream().map(AiVoiceAnalyze::getRespFormat).collect(Collectors.joining("\n"));
//            aiVoiceAnalyzeService.lambdaUpdate().eq(AiVoiceAnalyze::getId, analyze.getId())
//                    .set(AiVoiceAnalyze::getVoiceStatus, 2)
//                   // .set(AiVoiceAnalyze::getRespFormat, respFormat)
//                    .update();
        // }
        String lockKey = "SCRM:" + SCRM_VOICE_AI_NOTIFY_TOPIC + ":" + analyze.getId();
        boolean flagLock = redisOperator.setIfAbsent(lockKey, "1", 5L, TimeUnit.MINUTES);
        if (!flagLock) {
            log.warn("获取语音识别锁失败:{}", analyze.getId());
            throw new RuntimeException();
        }
        // 没有group单纯主节点,存在的历史消息  重新分析
        String message = "员工姓名：" + analyze.getEmployeeName() + "\n客户名称：" + analyze.getCustName() + "\n录音上传时间：" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(analyze.getCreateTime()) + "\n录音分析结果URL：" + viewUrl;
        // ai 未分析完成  语音识别完成
        if (Objects.equals(analyze.getAnalyzeStatus(), 0) || Objects.equals(analyze.getAnalyzeStatus(), 1) || Objects.equals(analyze.getVoiceStatus(), 2)) {
            try {
                aiVoiceAnalyzeService.lambdaUpdate().eq(AiVoiceAnalyze::getId, analyze.getId()).set(AiVoiceAnalyze::getAnalyzeStatus, 1).update();
                if (null != analyze.getParentId()) {
                    aiVoiceAnalyzeService.lambdaUpdate().set(AiVoiceAnalyze::getAnalyzeStatus, 1L).eq(AiVoiceAnalyze::getId, analyze.getParentId()).update();
                }
                log.info("ai分析开始:{}", analyze.getId());
                if (StringUtils.isBlank(analyze.getChatResponse())) {
                    List<AiPromptInfo> aiPromptInfosNormalList = aiPromptInfoService.lambdaQuery().eq(AiPromptInfo::getPromptType, 4).eq(AiPromptInfo::getStartFlag, 1).eq(AiPromptInfo::getDeleteFlag, 0).list();
                    String analyzePrompt = null;
                    if (CollectionUtils.isEmpty(aiPromptInfosNormalList)) {
                        log.warn("语音分析提示词缺失，采用默认值");
                        analyzePrompt = "中企动力是一家以网站建设、网站优化、营销推广服务为主的公司，主要通过数字门户与应用场景及其周边生态产品，为企业级客户提供围绕品牌展示、市场营 销推广、外贸出海营销、业务线上化、服务线上化的全域、全链路的数字化服务。\\n\" +\n" +
                                "            \"你是商务助手，你需要根据给你的谈话内容提炼能够影响客户签约的主要信息，并按照重要程度进行输出。";
                    } else {
                        AiPromptInfo aiPromptInfoNormal = aiPromptInfosNormalList.get(0);
                        analyzePrompt = aiPromptInfoNormal.getContent();
                    }
                    analyzePrompt = analyzePrompt + "\n## 不要有流程图 graph td等";
                    String aiResultNormal = getAiResult(analyze.getRespFormat(), analyzePrompt, 8000L);
                    log.info("ai响应数据:{}", aiResultNormal);
                    boolean flag = aiVoiceAnalyzeService.lambdaUpdate().set(AiVoiceAnalyze::getChatResponse, aiResultNormal)
                            .eq(AiVoiceAnalyze::getId, id).update();
                    if (!flag) {
                        log.error("更新 ai 失败");
                        throw new RuntimeException();
                    }
                }
                if (Objects.equals(analyze.getVoiceType(), "1") && StringUtils.isBlank(analyze.getBusinessChatResponse())) {
                    List<AiPromptInfo> aiPromptInfosFlowList = aiPromptInfoService.lambdaQuery().eq(AiPromptInfo::getPromptType, 3).eq(AiPromptInfo::getStartFlag, 1).eq(AiPromptInfo::getDeleteFlag, 0).list();
                    String flowPrompt = null;
                    if (CollectionUtils.isEmpty(aiPromptInfosFlowList)) {
                        log.warn("跟进语音分析提示词缺失，采用默认值");
                        flowPrompt = "总结一下内容";
                    } else {
                        AiPromptInfo aiPromptInfoFlow = aiPromptInfosFlowList.get(0);
                        flowPrompt = aiPromptInfoFlow.getContent();
                    }
                    log.info("******************跟进的MQ消息******************");
                    String content = flowPrompt + resp_format;
                    String aiResult = getAiResult(analyze.getRespFormat(), content, 1000L);
                    log.info("ai响应数据:{}", aiResult);
                    String jsonObjectStr = AIJsonChecker.extractJson(aiResult);
                    JSONObject jsonObject1 = JSONObject.parseObject(jsonObjectStr);
                    log.info("ai分析结果：{}", JSONObject.toJSONString(jsonObject1));
                    String salesStage = jsonObject1.getString("salesStage");
                    SalesStageEnum salesStageEnum = AiFollowAnalyzeConsumer.getByName(salesStage);
                    if (null == salesStageEnum) {
                        log.warn("获取分析结果异常:{}", JSONObject.toJSONString(jsonObject1));
                        throw new RuntimeException();
                    }
                    JSONObject jsonObjectRenew = new JSONObject();
                    jsonObjectRenew.put(genjinneirong, jsonObject1.getString("analyze"));
                    jsonObjectRenew.put(xiaoshoujieduan, jsonObject1.getString("salesStage"));
                    //发送写跟进的mq 消息
                    boolean flag = aiVoiceAnalyzeService.lambdaUpdate().set(AiVoiceAnalyze::getBusinessChatResponse, JSONObject.toJSONString(jsonObjectRenew))
                            .set(AiVoiceAnalyze::getAnalyzeStatus, 2L)
                            .eq(AiVoiceAnalyze::getId, id).update();
                    if (!flag) {
                        log.error("更新跟进信息失败");
                        throw new RuntimeException();
                    }
                    rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.SCRM_VOICE_AI_NOTIFY_FOLLOW_TOPIC, jsonObject.toJSONString());
                    aiSpeechRecognitionConsumer.sendWechatMessage(message);
                    this.sendMessage(analyze.getOrgId(), analyze.getSubId(), analyze.getAreaId(), message, analyze.getEmployeeId(), analyze.getBuId());
                } else {
                    boolean flag = aiVoiceAnalyzeService.lambdaUpdate()
                            .set(AiVoiceAnalyze::getAnalyzeStatus, 2L)
                            .eq(AiVoiceAnalyze::getId, id).update();
                    if (!flag) {
                        log.error("更新分析状态失败");
                        throw new RuntimeException();
                    }
                    if (null != analyze.getParentId()) {
                        boolean flag2 = aiVoiceAnalyzeService.lambdaUpdate()
                                .set(AiVoiceAnalyze::getAnalyzeStatus, 2L)
                                .eq(AiVoiceAnalyze::getId, analyze.getParentId()).update();
                        if (!flag2) {
                            log.error("更新重新分析状态失败");
                            throw new RuntimeException();
                        }
                        message = message.replace("录音上传时间", "重新提炼时间");
                        message = "重新提炼结果\n" + message;
                        aiSpeechRecognitionConsumer.sendWechatMessage(message);
                        this.sendMessage(analyze.getOrgId(), analyze.getSubId(), analyze.getAreaId(), message, analyze.getEmployeeId(), analyze.getBuId());
                    } else {
                        aiSpeechRecognitionConsumer.sendWechatMessage(message);
                        this.sendMessage(analyze.getOrgId(), analyze.getSubId(), analyze.getAreaId(), message, analyze.getEmployeeId(), analyze.getBuId());
                    }
                }
            } catch (Exception e) {
                String messageError = "员工姓名：" + analyze.getEmployeeName() + "\n" +
                        "客户名称：" + analyze.getCustName() + "\n" +
                        "录音上传时间：" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(analyze.getCreateTime()) + "\n" +
                        "录音分析结果：录音信息提炼失败,请检查录音文件后再次尝试";
                if (messageExt.getReconsumeTimes() > 15) {
                    aiVoiceAnalyzeService.lambdaUpdate()
                            .set(AiVoiceAnalyze::getAnalyzeStatus, 3L)
                            .eq(AiVoiceAnalyze::getId, id).update();
                    if (null != analyze.getParentId()) {
                        aiVoiceAnalyzeService.lambdaUpdate()
                                .set(AiVoiceAnalyze::getAnalyzeStatus, 3L)
                                .eq(AiVoiceAnalyze::getId, analyze.getParentId()).update();
                    }
                    sendWxMessage.sendMessage(analyze.getEmployeeId(), messageError);
                    aiSpeechRecognitionConsumer.sendWechatMessage(messageError);
                    return;
                }
                log.warn("请求 ai 语音分析结果失败", e);
                throw new RuntimeException();
            } finally {
                // 释放锁
                redisOperator.del(lockKey);
            }
        }
    }

    public static String getAiResult(String userContent, String systemContent, Long maxTokens) throws IOException {
        // 构造请求 JSON 数据
        JSONObject requestBodyJson = new JSONObject();
        requestBodyJson.put("model", "qwen-max-latest");
        JSONArray messages = new JSONArray();
        JSONObject systemMessage = new JSONObject();
        systemMessage.put("role", "system");
        systemMessage.put("content", systemContent);
        JSONObject userMessage = new JSONObject();
        userMessage.put("role", "user");
        userMessage.put("content", userContent);
        messages.add(systemMessage);
        messages.add(userMessage);
        requestBodyJson.put("messages", messages);
        requestBodyJson.put("stream", false);
        requestBodyJson.put("max_tokens", maxTokens);
        // 转为 JSON 字符串
        String jsonString = JSON.toJSONString(requestBodyJson);
        // 构造 MediaType 和 RequestBody
        MediaType mediaType = MediaType.parse("application/json");
        log.info("请求AI信息:{}", jsonString);
        RequestBody body = RequestBody.create(mediaType, jsonString);
        // 构造 OkHttpClient 并设置超时时间
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.MINUTES)
                .readTimeout(10, TimeUnit.MINUTES)
                .writeTimeout(10, TimeUnit.MINUTES)
                .build();
        // 构造请求
        Request request = new Request.Builder()
                .url("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions")
                .method("POST", body)
                .addHeader("Authorization", "Bearer sk-7de039270e844634be26fb38451b4867")
                .addHeader("Content-Type", "application/json")
                .build();
        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSON.parseObject(response.body().string());
                JSONArray choices = jsonObject.getJSONArray("choices");
                if (choices != null && !choices.isEmpty()) {
                    JSONObject message = choices.getJSONObject(0).getJSONObject("message");
                    if (message != null) {
                        return message.getString("content");
                    }
                }
            } else {
                if (null != response.body()) {
                    log.warn("请求语音分析失败:{}", response.body().string());
                }
                throw new RuntimeException("请求语音分析失败");
            }
        }
        return null;
    }

    public void sendMessage(String deptId, String subId, String areaId, String message, String employeeId, String buId) {
        log.info("给员工发送消息成功:{},{}", employeeId, message);
        sendWxMessage.sendMessage(employeeId, message);
        if (StringUtils.isNotBlank(deptId)) {
            Optional<EmployeeLiteThirdView> orgLeaderCooperation = employeeThirdService.getOrgLeader(deptId);
            if (orgLeaderCooperation.isPresent() && !StringUtils.isEmpty(orgLeaderCooperation.get().getId())) {
                if (!Objects.equals(employeeId, orgLeaderCooperation.get().getId())) {
                    sendWxMessage.sendMessage(orgLeaderCooperation.get().getId(), message);
                    log.info("给部门经理发送消息成功:{},{}", orgLeaderCooperation.get().getId(), message);
                } else {
                    log.info("部门经理ID和员工ID相同: {}", employeeId);
                }
            } else {
                log.info("给部门经理发送消息失败:{},{}", employeeId, JSONObject.toJSONString(orgLeaderCooperation));
            }
        }
        if (StringUtils.isNotBlank(subId)) {
            Optional<EmployeeLiteThirdView> subLeaderProtect = employeeThirdService.getOrgLeader(subId);
            if (subLeaderProtect.isPresent() && !StringUtils.isEmpty(subLeaderProtect.get().getId())) {
                if (!Objects.equals(employeeId, subLeaderProtect.get().getId())) {
                    sendWxMessage.sendMessage(subLeaderProtect.get().getId(), message);
                    log.info("给分司总监发送消息成功:{},{}", subLeaderProtect.get().getId(), message);
                } else {
                    log.info("分司总监和员工ID相同: {}", employeeId);
                }
            } else {
                log.info("分司总监发送消息失败:{},{}", employeeId, JSONObject.toJSONString(subLeaderProtect));
            }
        }
        if (StringUtils.isNotBlank(buId)) {
            Optional<EmployeeLiteThirdView> buLeaderCooperation = employeeThirdService.getOrgLeader(buId);
            if (buLeaderCooperation.isPresent() && !StringUtils.isEmpty(buLeaderCooperation.get().getId())) {
                if (!Objects.equals(employeeId, buLeaderCooperation.get().getId())) {
                    sendWxMessage.sendMessage(buLeaderCooperation.get().getId(), message);
                    log.info("给事业部经理发送消息成功:{},{}", buLeaderCooperation.get().getId(), message);
                } else {
                    log.info("事业部ID和员工ID相同: {}", employeeId);
                }
            } else {
                log.info("给事业部发送消息失败:{},{}", employeeId, JSONObject.toJSONString(buLeaderCooperation));
            }
        }
    }
}
