package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.service.SegmentDetailService;
import com.ce.scrm.center.dao.service.SegmentService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.Set;

/**
 * 监听保护关系binlog同步cutomer数据
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CESUPPORT_CRM_SITE_CLOCK_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.CESUPPORT_CRM_SITE_CLOCK_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class SegmentCustomerVisitedInfoConsumer extends AbstractBinlogConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private SegmentDetailService segmentDetailService;

    @Resource
    private SegmentService segmentService;
    @Override
    public void onMessage(MessageExt messageExt) {
      super.dealMqMsg(messageExt);
    }

    @Override
    public void dealInsertEvent(String topic, String msgId, JSONObject after) {
        String customerId = after.getString("cust_id");
        Date lastVisitTime = after.getDate("create_time");
        String salerId = after.getString("emp_id");
        if(StringUtils.isBlank(customerId) || Objects.isNull(lastVisitTime) || StringUtils.isBlank(salerId)){
            log.error("customerId|create_time|emp_id为空，msgId={}", msgId);
            return;
        }
        Set<String> validSegmentIds = segmentService.getValidSegmentIds(lastVisitTime);
        if (CollectionUtils.isEmpty(validSegmentIds)){
            log.warn("获取的有效分群信息为空，topic={}, msgId={}, lastVisitTime={}",topic, msgId,lastVisitTime);
            return;
        }
        segmentDetailService.setLastVisitTime(validSegmentIds, customerId,salerId, lastVisitTime);
    }

    @Override
    public void dealUpdateEvent(String topic, String msgId, JSONObject before, JSONObject after) {
        log.info("UPDATE类型暂不处理! topic={} msgId {}", topic, msgId);
    }

    @Override
    protected void dealDeleteEvent(String topic, String msgId, JSONObject after) {
        log.info("DELETE类型暂不处理! topic={} msgId {}", topic, msgId);
    }
}
