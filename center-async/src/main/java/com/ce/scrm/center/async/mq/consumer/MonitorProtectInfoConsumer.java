package com.ce.scrm.center.async.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.async.mq.entity.TableChangeEnum;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerUpdateDubboDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 监听保护关系binlog同步customer数据
 */
@Slf4j
@Component // 用新的去进行同步 customer PROTECT_TO_CUSTOMER_GROUP
//@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CM_CUST_PROTECT_TOPIC,
//        consumerGroup = ServiceConstant.MqConstant.Group.SCRM_CUSTOMER_PROTECTED_ASYNC_GROUP,
//        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class MonitorProtectInfoConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private CustomerThirdService customerThirdService;

    private static final Set<String> columnSet = Arrays.asList(
            "CUST_ID", "SALER_ID", "BUSSDEPT_ID", "SUBCOMPANY_ID","BU_ID", "AREA_ID", "STATUS", "CUST_TYPE", "PROTECT_TIME","BIND_FLAG","BUSINESS_OPPORTUNITY_CONFIRMATION_FLAG"
    ).stream().collect(Collectors.toSet());

    @Override
    public void onMessage(MessageExt messageExt) {
        int reconsumeTimes = messageExt.getReconsumeTimes();
        String topic = messageExt.getTopic();
        String msgId = messageExt.getMsgId();
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            return;
        }
        try {
            JSONObject binlogJson = JSON.parseObject(data);
            /**
             * 处理binlog记录的过程：
             * 1、判断是否操作类型：ddl、dml， ddl 直接发消息通知【企微】?
             * 2、dml时判断type:
             *     - INSERT：新增数据，则直接生产对应的事件
             *     - UPDATE：修改数据，判断事件字段是否发生变化(逻辑由子类实现)
             *     - DELETE：删除数据，暂时没定怎么处理？？
             */
            Boolean isDdl = binlogJson.getBoolean("isDdl");
            if (isDdl) {
                log.error("接收到ddl语句...,逻辑暂不支持!! topic={} msgId {}", topic, msgId);
                return;
            }

            // 针对dml语句进行校验
            Map<String, Boolean> repeatMsgMap = new HashMap<>();

            String type = binlogJson.getString("type");
            JSONArray afterAry = binlogJson.getJSONArray("data");
            JSONArray beforeAry = binlogJson.getJSONArray("old");
            int size = afterAry.size();
            for (int i = 0; i < size; i++) {
                JSONObject afterJson = afterAry.getJSONObject(i);
                String custId = afterJson.getString("CUST_ID");
                if(custId == null) {
                    continue;
                }
                if (repeatMsgMap.getOrDefault(afterJson.getString("id"), false)) {
                    continue;
                }
                if (TableChangeEnum.delete.getField().equals(type)) {
                    startAsyncSql(custId);
                    continue;
                }
                if (TableChangeEnum.insert.getField().equals(type)) {
                    startAsyncSql(custId);
                    continue;
                }
                if (TableChangeEnum.update.getField().equals(type)) {
                    JSONObject beforeJson = beforeAry.getJSONObject(i);
                    if (isTableUpdateEvent(beforeJson)) {
                        // 如果发送变更的字段列表中包含 事件中所需的字段 则触发事件写入
                        startAsyncSql(custId);
                    }
                }
            }
        } catch (Exception e) {
            if (reconsumeTimes > 9) {
                log.error("处理RocketMq消息失败! topic={} msgId {},{}", topic, msgId, e.getMessage());
                log.warn("处理RocketMq消息失败! topic={} msgId {}", topic, msgId, e);
            } else {
                log.warn("处理RocketMq消息失败! topic={} msgId {}", topic, msgId, e);
            }
            throw new RuntimeException(e);
        }
    }

    /**
     * 同步SQL
     * @param custId
     */
    private void startAsyncSql(String custId) {

        log.info("startAsyncSql custId:{}",custId);
        if(StringUtils.isEmpty(custId)) {
            return;
        }
        // 查询当前客户信息
        Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(custId);
        CustomerDataThirdView customerDataThirdView = customerData.orElse(null);
        if(customerDataThirdView == null) {
            return;
        }
        // 查询当前保护关系
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, custId).one();
        // 要判断不是status 不等于1  因为status被更改 salerId可能不变
        if (cmCustProtect == null || !Objects.equals(cmCustProtect.getStatus(),1)) {
            cmCustProtect = new CmCustProtect();
        }
        CustomerUpdateDubboDto customerUpdateDubboDto = new CustomerUpdateDubboDto();
        customerUpdateDubboDto.setCustomerId(custId);
        customerUpdateDubboDto.setOperator("1024");
        customerUpdateDubboDto.setBindFlag(cmCustProtect.getBindFlag());
        customerUpdateDubboDto.setBusinessOpportunityConfirmationFlag(cmCustProtect.getBusinessOpportunityConfirmationFlag());
        customerUpdateDubboDto.setProtectCustType(String.valueOf(cmCustProtect.getCustType()));
        customerUpdateDubboDto.setProtectStatus(String.valueOf(cmCustProtect.getStatus()));
        customerUpdateDubboDto.setProtectProtectTime(cmCustProtect.getProtectTime());
        customerUpdateDubboDto.setProtectProtectendTime(cmCustProtect.getExceedTime());
        // 换保护商务
        if(!Objects.equals(customerDataThirdView.getProtectSalerId(),cmCustProtect.getSalerId())) {
            customerUpdateDubboDto.setCdpCurrentMonthClockCount(0);
            customerUpdateDubboDto.setCdpClockCount(0);
            customerUpdateDubboDto.setCdpProtectDay(0);
        }
        if (Objects.equals(cmCustProtect.getStatus(), ProtectStateEnum.PROTECT.getState())) {
            customerUpdateDubboDto.setProtectSalerId(cmCustProtect.getSalerId());
            customerUpdateDubboDto.setProtectBussdeptId(cmCustProtect.getBussdeptId());
            customerUpdateDubboDto.setProtectBuId(cmCustProtect.getBuId());
            customerUpdateDubboDto.setProtectSubcompanyId(cmCustProtect.getSubcompanyId());
            customerUpdateDubboDto.setProtectAreaId(cmCustProtect.getAreaId());
        } else if (Objects.equals(cmCustProtect.getStatus(), ProtectStateEnum.MAJOR_WILL_ASSIGN.getState())) {
            customerUpdateDubboDto.setProtectSalerId(null);
            customerUpdateDubboDto.setProtectBussdeptId(null);
            customerUpdateDubboDto.setProtectBuId(null);
            customerUpdateDubboDto.setProtectSubcompanyId(cmCustProtect.getSubcompanyId());
            customerUpdateDubboDto.setProtectAreaId(cmCustProtect.getAreaId());
        } else if (Objects.equals(cmCustProtect.getStatus(), ProtectStateEnum.MANAGER_WILL_ASSIGN.getState())) {
            customerUpdateDubboDto.setProtectSalerId(null);
            customerUpdateDubboDto.setProtectBussdeptId(cmCustProtect.getBussdeptId());
            customerUpdateDubboDto.setProtectBuId(cmCustProtect.getBuId());
            customerUpdateDubboDto.setProtectSubcompanyId(cmCustProtect.getSubcompanyId());
            customerUpdateDubboDto.setProtectAreaId(cmCustProtect.getAreaId());
        } else if (Objects.equals(cmCustProtect.getStatus(), ProtectStateEnum.CUSTOMER_POOL.getState())) {
            customerUpdateDubboDto.setProtectSalerId(null);
            customerUpdateDubboDto.setProtectBussdeptId(null);
            customerUpdateDubboDto.setProtectBuId(null);
            customerUpdateDubboDto.setProtectSubcompanyId(null);
            customerUpdateDubboDto.setProtectAreaId(null);
        } else if (Objects.equals(cmCustProtect.getStatus(), ProtectStateEnum.BU_WILL_ASSIGN.getState())) {
            customerUpdateDubboDto.setProtectSalerId(null);
            customerUpdateDubboDto.setProtectBussdeptId(null);
            customerUpdateDubboDto.setProtectBuId(cmCustProtect.getBuId());
            customerUpdateDubboDto.setProtectSubcompanyId(cmCustProtect.getSubcompanyId());
            customerUpdateDubboDto.setProtectAreaId(cmCustProtect.getAreaId());
        } else {
            log.error("监听保护关系binlog同步customer数据,未知保护状态！");
            return;
        }
        // 监听保护关系表binlog 然后同步customer
        customerThirdService.monitorProtectInfoConsumer(customerUpdateDubboDto);

    }

    public boolean isTableUpdateEvent(JSONObject beforeJson) {
        return columnSet.stream().anyMatch(beforeJson::containsKey);
    }

}
