<assembly>
    <id>${project.build.finalName}</id>
    <formats>
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <files>
        <file>
            <source>${project.build.directory}/${project.build.finalName}.jar</source>
            <outputDirectory>lib</outputDirectory>
        </file>
    </files>
    <fileSets>
        <fileSet>
            <directory>${project.basedir}/src/main/assembly/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <lineEnding>unix</lineEnding>
            <fileMode>0755</fileMode>
            <filtered>true</filtered>
        </fileSet>
    </fileSets>
</assembly>