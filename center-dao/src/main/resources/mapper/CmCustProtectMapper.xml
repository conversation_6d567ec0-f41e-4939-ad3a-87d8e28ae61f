<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ce.scrm.center.dao.mapper.CmCustProtectMapper" >
  <resultMap id="BaseResultMap" type="com.ce.scrm.center.dao.entity.CmCustProtect" >
    <id column="CUST_ID" property="custId" jdbcType="CHAR" />
    <result column="ID" property="id" jdbcType="CHAR" />
    <result column="CUST_NAME" property="custName" jdbcType="VARCHAR" />
    <result column="CUST_TYPE" property="custType" jdbcType="INTEGER" />
    <result column="INTENTIONALITY_TYPE" property="intentionalityType" jdbcType="INTEGER" />
    <result column="SALER_ID" property="salerId" jdbcType="CHAR" />
    <result column="BUSSDEPT_ID" property="bussdeptId" jdbcType="CHAR" />
    <result column="SUBCOMPANY_ID" property="subcompanyId" jdbcType="CHAR" />
    <result column="AREA_ID" property="areaId" jdbcType="CHAR" />
    <result column="MARK_ID" property="markId" jdbcType="VARCHAR" />
    <result column="PROTECT_TIME" property="protectTime" jdbcType="TIMESTAMP" />
    <result column="EXCEED_TIME" property="exceedTime" jdbcType="TIMESTAMP" />
    <result column="IS_VISIT_STATE" property="isVisitState" jdbcType="INTEGER" />
    <result column="CUST_SOURCE" property="custSource" jdbcType="INTEGER" />
    <result column="DATA_STATE" property="dataState" jdbcType="INTEGER" />
    <result column="LAST_VISIT_TIME" property="lastVisitTime" jdbcType="TIMESTAMP" />
    <result column="VISIT_EXCEED_TIME" property="visitExceedTime" jdbcType="TIMESTAMP" />
    <result column="ABSOLUTE_PROTECT_TIME" property="absoluteProtectTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_BY" property="createBy" jdbcType="CHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_BY" property="updateBy" jdbcType="CHAR" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="BUSIOPPO_CODE" property="busioppoCode" jdbcType="VARCHAR" />
    <result column="IS_STRESS" property="isStress" jdbcType="INTEGER" />
    <result column="ADD_STRESS_TIME" property="addStressTime" jdbcType="TIMESTAMP" />
    <result column="LAST_PAY_TIME" property="lastPayTime" jdbcType="TIMESTAMP" />
    <result column="CUST_SOURCE_SUB" property="custSourceSub" jdbcType="INTEGER" />
    <result column="IS_TURN_GC" property="isTurnGc" jdbcType="INTEGER" />
    <result column="COOPERATE" property="cooperate" jdbcType="INTEGER" />
    <result column="OCCUPY" property="occupy" jdbcType="INTEGER" />
    <result column="IS_CLOCK" property="isClock" jdbcType="INTEGER" />
    <result column="CLOCK_PROVINCE" property="clockProvince" jdbcType="VARCHAR" />
    <result column="CLOCK_CITY" property="clockCity" jdbcType="VARCHAR" />
    <result column="CLOCK_REGION" property="clockRegion" jdbcType="VARCHAR" />
    <result column="REG_PROVINCE" property="regProvince" jdbcType="VARCHAR" />
    <result column="REG_CITY" property="regCity" jdbcType="VARCHAR" />
    <result column="REG_REGION" property="regRegion" jdbcType="VARCHAR" />
    <result column="UNCID" property="uncid" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="INTEGER" />
    <result column="ASSIGN_TIME" property="assignTime" jdbcType="TIMESTAMP" />
    <result column="ASSIGN_CUST_SOURCE" property="assignCustSource" jdbcType="INTEGER" />
    <result column="ASSIGN_DATE" property="assignDate" jdbcType="TIMESTAMP" />
    <result column="ORIGINAL_DEPT_ID" property="originalDeptId" jdbcType="VARCHAR" />
    <result column="REASON" property="reason" jdbcType="VARCHAR" />
    <result column="SOURCE" property="source" jdbcType="VARCHAR" />
    <result column="tag_flag7" property="tagFlag7" jdbcType="VARCHAR" />
    <result column="tag_flag8" property="tagFlag8" jdbcType="VARCHAR" />
    <result column="custom_tags" property="customTags" jdbcType="VARCHAR" />
    <result column="first_sign_time" property="firstSignTime" jdbcType="TIMESTAMP" />
    <result column="BU_ID" property="buId" jdbcType="CHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CUST_ID, ID, CUST_NAME, CUST_TYPE, INTENTIONALITY_TYPE, SALER_ID, BUSSDEPT_ID, SUBCOMPANY_ID,
    AREA_ID, MARK_ID, PROTECT_TIME, EXCEED_TIME, IS_VISIT_STATE, CUST_SOURCE, DATA_STATE, LAST_VISIT_TIME, VISIT_EXCEED_TIME,
    ABSOLUTE_PROTECT_TIME, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, BUSIOPPO_CODE, IS_STRESS, ADD_STRESS_TIME,
    LAST_PAY_TIME,CUST_SOURCE_SUB,IS_TURN_GC,COOPERATE,OCCUPY,IS_CLOCK, CLOCK_PROVINCE,CLOCK_CITY, CLOCK_REGION,
    REG_PROVINCE,REG_CITY, REG_REGION, UNCID, STATUS,ASSIGN_TIME,ASSIGN_CUST_SOURCE,ASSIGN_DATE,ORIGINAL_DEPT_ID, REASON,SOURCE,tag_flag7,tag_flag8,custom_tags,
    first_sign_time,BU_ID
  </sql>

<!--  <insert id="insert" parameterType="cn.ce.cesupport.sma.entity.CmCustProtect" >-->
<!--    <selectKey keyProperty="id" resultType="String" order="BEFORE">-->
<!--        select replace(uuid(),'-','') from dual-->
<!--    </selectKey>-->
<!--    insert into CM_CUST_PROTECT (CUST_ID, ID, CUST_NAME,-->
<!--    CUST_TYPE, INTENTIONALITY_TYPE, SALER_ID,-->
<!--    BUSSDEPT_ID, SUBCOMPANY_ID, AREA_ID,-->
<!--    MARK_ID, PROTECT_TIME, EXCEED_TIME,-->
<!--    IS_VISIT_STATE, CREATE_BY, CREATE_TIME,-->
<!--    UPDATE_BY, UPDATE_TIME, CUST_SOURCE,-->
<!--    DATA_STATE, LAST_VISIT_TIME, VISIT_EXCEED_TIME,-->
<!--    ABSOLUTE_PROTECT_TIME, BUSIOPPO_CODE, IS_STRESS, ADD_STRESS_TIME, LAST_PAY_TIME,CUST_SOURCE_SUB, IS_TURN_GC, COOPERATE, OCCUPY,-->
<!--    IS_CLOCK, CLOCK_PROVINCE,CLOCK_CITY, CLOCK_REGION, REG_PROVINCE,REG_CITY, REG_REGION, UNCID, STATUS,-->
<!--    ASSIGN_TIME,ASSIGN_CUST_SOURCE,ASSIGN_DATE,ORIGINAL_DEPT_ID, SOURCE,REASON,FIRST_SIGN_TIME,BU_ID)-->
<!--    values (#{custId,jdbcType=CHAR}, #{id,jdbcType=CHAR}, #{custName,jdbcType=VARCHAR},-->
<!--    #{custType,jdbcType=INTEGER}, #{intentionalityType,jdbcType=INTEGER}, #{salerId,jdbcType=CHAR},-->
<!--    #{bussdeptId,jdbcType=CHAR}, #{subcompanyId,jdbcType=CHAR}, #{areaId,jdbcType=CHAR},-->
<!--    #{markId,jdbcType=VARCHAR}, #{protectTime,jdbcType=TIMESTAMP}, #{exceedTime,jdbcType=TIMESTAMP},-->
<!--    #{isVisitState,jdbcType=INTEGER}, #{createBy,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP},-->
<!--    #{updateBy,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{custSource,jdbcType=INTEGER},-->
<!--    #{dataState,jdbcType=INTEGER}, #{lastVisitTime,jdbcType=TIMESTAMP}, #{visitExceedTime,jdbcType=TIMESTAMP},-->
<!--    #{absoluteProtectTime,jdbcType=TIMESTAMP}, #{busiOppoCode, jdbcType=VARCHAR}, #{isStress, jdbcType=INTEGER},-->
<!--    #{addStressTime, jdbcType=TIMESTAMP},#{lastPayTime, jdbcType=TIMESTAMP},#{custSourceSub,jdbcType=INTEGER},-->
<!--    #{isTurnGc, jdbcType=INTEGER},#{cooperate, jdbcType=INTEGER},#{occupy, jdbcType=INTEGER},-->
<!--    #{isClock, jdbcType=INTEGER},#{clockProvince, jdbcType=CHAR},#{clockCity, jdbcType=CHAR},#{clockRegion, jdbcType=CHAR},-->
<!--    #{regProvince, jdbcType=CHAR},#{regCity, jdbcType=CHAR},#{regRegion, jdbcType=CHAR},#{uncid, jdbcType=VARCHAR}, #{status, jdbcType=INTEGER},-->
<!--    #{assignTime, jdbcType=TIMESTAMP},#{assignCustSource, jdbcType=INTEGER},#{assignDate, jdbcType=TIMESTAMP},#{originalDeptId, jdbcType=VARCHAR},-->
<!--    #{source, jdbcType=VARCHAR},#{reason, jdbcType=VARCHAR},#{firstSignTime, jdbcType=TIMESTAMP},#{buId,jdbcType=CHAR})-->
<!--  </insert>-->

<!--  <select id="selectByCustId" resultMap="BaseResultMap" parameterType="java.lang.String" >-->
<!--    select-->
<!--    <include refid="Base_Column_List" />-->
<!--    from CM_CUST_PROTECT-->
<!--    where CUST_ID = #{custId,jdbcType=CHAR}-->
<!--  </select>-->

<!--  <select id="selectByUncid" resultMap="BaseResultMap" parameterType="java.lang.String" >-->
<!--    select-->
<!--    <include refid="Base_Column_List" />-->
<!--    from CM_CUST_PROTECT-->
<!--    where UNCID = #{uncid,jdbcType=VARCHAR}-->
<!--  </select>-->

<!--  <select id="selectByCustomerName" resultMap="BaseResultMap" parameterType="java.lang.String" >-->
<!--    select-->
<!--    <include refid="Base_Column_List" />-->
<!--    from CM_CUST_PROTECT-->
<!--    where CUST_NAME = #{customerName,jdbcType=VARCHAR}-->
<!--  </select>-->

<!--  <select id="selectBySalerId" resultMap="BaseResultMap" parameterType="java.lang.String" >-->
<!--    select-->
<!--    <include refid="Base_Column_List" />-->
<!--    from CM_CUST_PROTECT-->
<!--    where SALER_ID = #{salerId,jdbcType=VARCHAR}-->
<!--  </select>-->
<!--  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="cn.ce.cesupport.sma.entity.CmCustProtect" >-->
<!--    select-->
<!--    <include refid="Base_Column_List" />-->
<!--    from CM_CUST_PROTECT-->
<!--    <where>-->
<!--      <if test="salerId != null " >-->
<!--          AND SALER_ID = #{salerId}-->
<!--      </if>-->
<!--      <if test="status != null " >-->
<!--          AND STATUS = #{status}-->
<!--      </if>-->
<!--    </where>-->
<!--    </select>-->

<!--  <update id="updateByCustId" parameterType="com.ce.scrm.center.dao.entity.CmCustProtect" >-->
<!--    update CM_CUST_PROTECT-->
<!--    <set>-->
<!--        ID = #{id,jdbcType=CHAR},-->
<!--        CUST_NAME = #{custName,jdbcType=VARCHAR},-->
<!--        CUST_TYPE = #{custType,jdbcType=INTEGER},-->
<!--        INTENTIONALITY_TYPE = #{intentionalityType,jdbcType=INTEGER},-->
<!--        SALER_ID = #{salerId,jdbcType=CHAR},-->
<!--        BUSSDEPT_ID = #{bussdeptId,jdbcType=CHAR},-->
<!--        SUBCOMPANY_ID = #{subcompanyId,jdbcType=CHAR},-->
<!--        AREA_ID = #{areaId,jdbcType=CHAR},-->
<!--        MARK_ID = #{markId,jdbcType=VARCHAR},-->
<!--        PROTECT_TIME = #{protectTime,jdbcType=TIMESTAMP},-->
<!--        EXCEED_TIME = #{exceedTime,jdbcType=TIMESTAMP},-->
<!--        IS_VISIT_STATE = #{isVisitState,jdbcType=INTEGER},-->
<!--        CREATE_BY = #{createBy,jdbcType=CHAR},-->
<!--        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},-->
<!--        UPDATE_BY = #{updateBy,jdbcType=CHAR},-->
<!--        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},-->
<!--        CUST_SOURCE = #{custSource,jdbcType=INTEGER},-->
<!--        DATA_STATE = #{dataState,jdbcType=INTEGER},-->
<!--        LAST_VISIT_TIME = #{lastVisitTime,jdbcType=TIMESTAMP},-->
<!--        VISIT_EXCEED_TIME = #{visitExceedTime,jdbcType=TIMESTAMP},-->
<!--        ABSOLUTE_PROTECT_TIME = #{absoluteProtectTime,jdbcType=TIMESTAMP},-->
<!--        BUSIOPPO_CODE = #{busioppoCode,jdbcType=VARCHAR},-->
<!--        IS_STRESS = #{isStress,jdbcType=INTEGER},-->
<!--        ADD_STRESS_TIME = #{addStressTime,jdbcType=TIMESTAMP},-->
<!--        LAST_PAY_TIME = #{lastPayTime,jdbcType=TIMESTAMP},-->
<!--        CUST_SOURCE_SUB=#{custSourceSub,jdbcType=INTEGER},-->
<!--        IS_TURN_GC = #{isTurnGc,jdbcType=INTEGER},-->
<!--        COOPERATE = #{cooperate,jdbcType=INTEGER},-->
<!--        OCCUPY = #{occupy,jdbcType=INTEGER},-->
<!--        IS_CLOCK = #{isClock,jdbcType=INTEGER},-->
<!--        CLOCK_PROVINCE = #{clockProvince,jdbcType=CHAR},-->
<!--        CLOCK_CITY = #{clockCity,jdbcType=CHAR},-->
<!--        CLOCK_REGION = #{clockRegion,jdbcType=CHAR},-->
<!--        REG_PROVINCE = #{regProvince,jdbcType=CHAR},-->
<!--        REG_CITY = #{regCity,jdbcType=CHAR},-->
<!--        REG_REGION = #{regRegion,jdbcType=CHAR},-->
<!--        UNCID = #{uncid,jdbcType=VARCHAR},-->
<!--        STATUS = #{status,jdbcType=INTEGER},-->
<!--        ASSIGN_TIME = #{assignTime,jdbcType=TIMESTAMP},-->
<!--        ASSIGN_CUST_SOURCE = #{assignCustSource,jdbcType=INTEGER},-->
<!--        ASSIGN_DATE = #{assignDate,jdbcType=TIMESTAMP},-->
<!--        ORIGINAL_DEPT_ID = #{originalDeptId,jdbcType=VARCHAR},-->
<!--        SOURCE = #{source,jdbcType=VARCHAR},-->
<!--        REASON = #{reason,jdbcType=VARCHAR},-->
<!--        custom_tags = #{customTags,jdbcType=VARCHAR},-->
<!--        BU_ID = #{buId,jdbcType=CHAR},-->
<!--        LAST_SALER_ID = #{lastSalerId ,jdbcType=CHAR},-->
<!--        LAST_BUSSDEPT_ID = #{lastBussdeptId ,jdbcType=CHAR},-->
<!--        LAST_BU_ID = #{lastBuId ,jdbcType=CHAR},-->
<!--        LAST_SUBCOMPANY_ID = #{lastSubcompanyId,jdbcType=CHAR},-->
<!--        LAST_AREA_ID = #{lastAreaId,jdbcType=CHAR}-->
<!--        <if test="firstSignTime != null " >-->
<!--            ,first_sign_time = #{firstSignTime,jdbcType=TIMESTAMP}-->
<!--        </if>-->
<!--    </set>-->
<!--    where CUST_ID = #{custId,jdbcType=CHAR}-->
<!--  </update>-->

    <update id="updateNullableByCustId" parameterType="com.ce.scrm.center.dao.entity.CmCustProtect" >
        update CM_CUST_PROTECT
        <set>
            ID = #{id,jdbcType=CHAR},
            CUST_NAME = #{custName,jdbcType=VARCHAR},
            CUST_TYPE = #{custType,jdbcType=INTEGER},
            INTENTIONALITY_TYPE = #{intentionalityType,jdbcType=INTEGER},
            SALER_ID = #{salerId,jdbcType=CHAR},
            BUSSDEPT_ID = #{bussdeptId,jdbcType=CHAR},
            SUBCOMPANY_ID = #{subcompanyId,jdbcType=CHAR},
            AREA_ID = #{areaId,jdbcType=CHAR},
            MARK_ID = #{markId,jdbcType=VARCHAR},
            PROTECT_TIME = #{protectTime,jdbcType=TIMESTAMP},
            EXCEED_TIME = #{exceedTime,jdbcType=TIMESTAMP},
            IS_VISIT_STATE = #{isVisitState,jdbcType=INTEGER},
            CREATE_BY = #{createBy,jdbcType=CHAR},
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_BY = #{updateBy,jdbcType=CHAR},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            CUST_SOURCE = #{custSource,jdbcType=INTEGER},
            DATA_STATE = #{dataState,jdbcType=INTEGER},
            LAST_VISIT_TIME = #{lastVisitTime,jdbcType=TIMESTAMP},
            VISIT_EXCEED_TIME = #{visitExceedTime,jdbcType=TIMESTAMP},
            ABSOLUTE_PROTECT_TIME = #{absoluteProtectTime,jdbcType=TIMESTAMP},
            BUSIOPPO_CODE = #{busioppoCode,jdbcType=VARCHAR},
            IS_STRESS = #{isStress,jdbcType=INTEGER},
            ADD_STRESS_TIME = #{addStressTime,jdbcType=TIMESTAMP},
            LAST_PAY_TIME = #{lastPayTime,jdbcType=TIMESTAMP},
            CUST_SOURCE_SUB=#{custSourceSub,jdbcType=INTEGER},
            IS_TURN_GC = #{isTurnGc,jdbcType=INTEGER},
            COOPERATE = #{cooperate,jdbcType=INTEGER},
            OCCUPY = #{occupy,jdbcType=INTEGER},
            IS_CLOCK = #{isClock,jdbcType=INTEGER},
            CLOCK_PROVINCE = #{clockProvince,jdbcType=CHAR},
            CLOCK_CITY = #{clockCity,jdbcType=CHAR},
            CLOCK_REGION = #{clockRegion,jdbcType=CHAR},
            REG_PROVINCE = #{regProvince,jdbcType=CHAR},
            REG_CITY = #{regCity,jdbcType=CHAR},
            REG_REGION = #{regRegion,jdbcType=CHAR},
            UNCID = #{uncid,jdbcType=VARCHAR},
            STATUS = #{status,jdbcType=INTEGER},
            ASSIGN_TIME = #{assignTime,jdbcType=TIMESTAMP},
            ASSIGN_CUST_SOURCE = #{assignCustSource,jdbcType=INTEGER},
            ASSIGN_DATE = #{assignDate,jdbcType=TIMESTAMP},
            ORIGINAL_DEPT_ID = #{originalDeptId,jdbcType=VARCHAR},
            SOURCE = #{source,jdbcType=VARCHAR},
            REASON = #{reason,jdbcType=VARCHAR},
            custom_tags = #{customTags,jdbcType=VARCHAR},
            BU_ID = #{buId,jdbcType=CHAR},
            LAST_SALER_ID = #{lastSalerId ,jdbcType=CHAR},
            LAST_BUSSDEPT_ID = #{lastBussdeptId ,jdbcType=CHAR},
            LAST_BU_ID = #{lastBuId ,jdbcType=CHAR},
            LAST_SUBCOMPANY_ID = #{lastSubcompanyId,jdbcType=CHAR},
            <if test="firstSignTime != null " >
                first_sign_time = #{firstSignTime,jdbcType=TIMESTAMP},
            </if>
            <if test="bindFlag != null " >
                bind_flag = #{bindFlag,jdbcType=INTEGER},
            </if>
            <if test="businessOpportunityConfirmationFlag != null " >
                business_opportunity_confirmation_flag = #{businessOpportunityConfirmationFlag,jdbcType=INTEGER},
            </if>
            LAST_AREA_ID = #{lastAreaId,jdbcType=CHAR}
        </set>
        where CUST_ID = #{custId,jdbcType=CHAR}
    </update>

<!--    <delete id="deleteByCustId" parameterType="java.lang.String" >-->
<!--        delete from CM_CUST_PROTECT-->
<!--        where CUST_ID = #{custId,jdbcType=VARCHAR}-->
<!--    </delete>-->

<!--    <select id="selectByPage" resultMap="BaseResultMap" parameterType="Map">-->
<!--        select-->
<!--        <include refid="Base_Column_List" />-->
<!--        from CM_CUST_PROTECT-->
<!--        <where>-->
<!--            <if test="params.customerNameList != null " >-->
<!--                AND cust_name IN-->
<!--                <foreach collection= "params.customerNameList" item="customerName" open="(" separator="," close=")">-->
<!--                    #{customerName}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="params.uncidList != null " >-->
<!--                AND uncid IN-->
<!--                <foreach collection= "params.uncidList" item="uncid" open="(" separator="," close=")">-->
<!--                    #{uncid}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="params.status != null" >-->
<!--                AND STATUS = #{params.status}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->
<!--    <update id="updateCustomerFlag" parameterType="cn.ce.cesupport.sma.entity.CmCustProtect" >-->
<!--        update CM_CUST_PROTECT-->
<!--        set tag_flag7 = #{tagFlag7,jdbcType=VARCHAR},-->
<!--            tag_flag8 = #{tagFlag8,jdbcType=VARCHAR},-->
<!--            tag_techcompany = #{tagTechcompany,jdbcType=VARCHAR}-->
<!--        where CUST_ID = #{custId,jdbcType=CHAR}-->
<!--    </update>-->
<!--    <update id="updateCustomTagById" parameterType="cn.ce.cesupport.sma.entity.CmCustProtect" >-->
<!--        update CM_CUST_PROTECT-->
<!--        set custom_tags = #{customTags,jdbcType=VARCHAR},-->
<!--            UPDATE_TIME = now()-->
<!--        where CUST_ID = #{custId,jdbcType=CHAR}-->
<!--    </update>-->
    <!-- 查询保护关系和保护阶段 -->
    <select id="selectCmCustProtects" resultType="com.ce.scrm.center.dao.entity.CmCustProtect">
        SELECT
        p.CUST_ID AS `CUST_ID`, p.ID AS `ID`, p.CUST_NAME AS `CUST_NAME`, p.CUST_TYPE AS `CUST_TYPE`,
        p.INTENTIONALITY_TYPE AS `INTENTIONALITY_TYPE`, p.SALER_ID AS `SALER_ID`, p.BUSSDEPT_ID AS `BUSSDEPT_ID`, p.BU_ID AS `BU_ID`,
        p.SUBCOMPANY_ID AS `SUBCOMPANY_ID`, p.AREA_ID AS `AREA_ID`, p.MARK_ID AS `MARK_ID`, p.PROTECT_TIME AS `PROTECT_TIME`,
        p.EXCEED_TIME AS `EXCEED_TIME`, p.IS_VISIT_STATE AS `IS_VISIT_STATE`, p.CUST_SOURCE AS `CUST_SOURCE`,
        p.DATA_STATE AS `DATA_STATE`, p.LAST_VISIT_TIME AS `LAST_VISIT_TIME`, p.VISIT_EXCEED_TIME AS `VISIT_EXCEED_TIME`,
        p.ABSOLUTE_PROTECT_TIME AS `ABSOLUTE_PROTECT_TIME`, p.CREATE_BY AS `CREATE_BY`, p.CREATE_TIME AS `CREATE_TIME`,
        p.UPDATE_BY AS `UPDATE_BY`, p.UPDATE_TIME AS `UPDATE_TIME`, p.BUSIOPPO_CODE AS `BUSIOPPO_CODE`, p.IS_TURN_GC AS `IS_TURN_GC`, p.CUST_SOURCE_SUB AS `CUST_SOURCE_SUB`,
        p.tag_flag7 AS `tag_flag7` ,p.tag_flag8 AS `tag_flag8`,p.custom_tags AS `custom_tags`,p.bind_flag AS `bind_flag`,p.BUSINESS_OPPORTUNITY_CONFIRMATION_FLAG AS `BUSINESS_OPPORTUNITY_CONFIRMATION_FLAG`
        FROM CM_CUST_PROTECT p
        <if test="params.visitStatus != null or (params.collectReason != null and params.collectReason != '')
	 						or (params.protectReason != null and params.protectReason != '')
	 						or (params.salesStage != null and params.salesStage != '')
	 						or (params.salesStageList != null and params.salesStageList.size() > 0 )
	 						or (params.forecastBusinessMonth != null and params.forecastBusinessMonth != '')
	 						or (params.noForecastBusinessMonth != null and params.noForecastBusinessMonth != '' )
	 						">
            LEFT JOIN CM_CUST_PROTECT_VIEW v ON p.CUST_ID = v.CUST_ID
        </if>
        <where>
            <if test="params.custId != null and params.custId !='' " >
                AND p.CUST_ID = #{params.custId}
            </if>
            <if test="params.salerId != null and params.salerId !='' " >
                AND p.SALER_ID = #{params.salerId}
            </if>
            <if test="params.bussdeptId != null and params.bussdeptId !='' " >
                AND p.BUSSDEPT_ID = #{params.bussdeptId}
            </if>
            <if test="params.buId != null and params.buId !='' " >
                AND p.BU_ID = #{params.buId}
            </if>
            <if test="params.subcompanyId != null and params.subcompanyId !='' " >
                AND p.SUBCOMPANY_ID = #{params.subcompanyId}
            </if>
            <if test="params.areaId != null and params.areaId !='' " >
                AND p.AREA_ID = #{params.areaId}
            </if>
            <if test="params.custTypes != null and params.custTypes.size()>0 ">
                AND p.CUST_TYPE IN
                <foreach collection= "params.custTypes" item="custType" open="(" separator="," close=")">
                    #{custType}
                </foreach>
            </if>
            <if test="params.custType != null " >
                AND p.CUST_TYPE = #{params.custType}
            </if>
            <if test="params.custName != null and params.custName !='' " >
                AND p.CUST_NAME LIKE CONCAT('%', #{params.custName}, '%')
            </if>
            <choose>
                <when test="params.isSj != null and params.isSj == 1 " >
                    AND BUSIOPPO_CODE IS NOT NULL
                </when>
                <when test="params.isSj != null and params.isSj == 0 " >
                    AND BUSIOPPO_CODE IS NULL
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="params.intentionalityType != null " >
                AND p.INTENTIONALITY_TYPE = #{params.intentionalityType}
            </if>
            <if test="params.lastVisitTime != null and params.lastVisitTime != '' ">
                AND p.LAST_VISIT_TIME &gt; #{params.lastVisitTime}
            </if>
            <if test="params.visitStatus != null ">
                AND v.VISITSTATUS = #{params.visitStatus}
            </if>
            <if test="params.salesStage != null and params.salesStage != '' ">
                AND v.SALESSTAGE = #{params.salesStage}
            </if>
            <if test="params.salesStageList != null and params.salesStageList.size() > 0 ">
                AND v.SALESSTAGE in
                <foreach collection="params.salesStageList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.forecastBusinessMonth != null and params.forecastBusinessMonth != '' ">
                AND v.FORECAST_BUSINESS_MONTH = #{params.forecastBusinessMonth}
            </if>
            <if test="params.noForecastBusinessMonth != null and params.noForecastBusinessMonth != '' ">
                AND v.FORECAST_BUSINESS_MONTH != #{params.noForecastBusinessMonth}
            </if>
            <if test="params.collectReason != null and params.collectReason != '' ">
                AND v.COLLECT_REASON = #{params.collectReason}
            </if>
            <if test="params.protectReason != null and params.protectReason != '' ">
                AND v.PROTECT_REASON = #{params.protectReason}
            </if>
            <if test="params.startProtectDate != null and params.startProtectDate != '' ">
                AND p.PROTECT_TIME &gt; #{params.startProtectDate}
            </if>
            <if test="params.endProtectDate != null and params.endProtectDate != '' ">
                AND p.PROTECT_TIME &lt; #{params.endProtectDate}
            </if>
            <if test="params.sjType != null and params.sjType !='' " >
                AND p.BUSIOPPO_CODE LIKE CONCAT(#{params.sjType}, '%')
            </if>
            <if test="params.isTurnGc != null" >
                AND p.IS_TURN_GC = #{params.isTurnGc}
            </if>
            <if test="params.custSourceSub != null" >
                AND p.CUST_SOURCE_SUB = #{params.custSourceSub}
            </if>
            <if test="params.custSources != null ">
                AND p.CUST_SOURCE in
                <foreach collection="params.custSources" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.isOccupy != null and params.isOccupy == 0" >
                AND ((p.OCCUPY = #{params.isOccupy} or p.OCCUPY is null) and (IS_TURN_GC != 1 OR IS_TURN_GC IS NULL))
            </if>
            <if test="params.isOccupy != null and params.isOccupy == 1" >
                AND (p.OCCUPY = #{params.isOccupy} or IS_TURN_GC = 1)
            </if>
            <if test="params.isClock != null" >
                AND p.IS_CLOCK = #{params.isClock}
            </if>
            <if test="params.clockProvince != null" >
                AND p.CLOCK_PROVINCE = #{params.clockProvince}
            </if>
            <if test="params.clockCity != null" >
                AND p.CLOCK_CITY = #{params.clockCity}
            </if>
            <if test="params.clockRegion != null" >
                AND p.CLOCK_REGION = #{params.clockRegion}
            </if>
            <if test="params.regProvince != null" >
                AND p.REG_PROVINCE = #{params.regProvince}
            </if>
            <if test="params.regCity != null" >
                AND p.REG_CITY = #{params.regCity}
            </if>
            <if test="params.regRegion != null" >
                AND p.REG_REGION = #{params.regRegion}
            </if>
            <if test="1==1" >
                AND STATUS = 1
            </if>
            <if test="params.flag7s != null and params.flag7s.size() > 0 ">
                AND p.tag_flag7 in
                <foreach collection="params.flag7s" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.flag8s != null  and params.flag8s.size() > 0  ">
                AND p.tag_flag8 in
                <foreach collection="params.flag8s" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.customTags != null" >
                AND p.custom_tags LIKE CONCAT('%', #{params.customTags}, '%')
            </if>
            <if test="params.bindFlag != null" >
                AND p.bind_flag = #{params.bindFlag}
            </if>
            <if test="params.queryType != null and params.queryType == 1" >
                AND p.BUSIOPPO_CODE is not null AND p.BUSINESS_OPPORTUNITY_CONFIRMATION_FLAG in (1,2)
            </if>
            <if test="params.queryType != null and params.queryType == 2" >
                AND p.BUSIOPPO_CODE is not null AND p.BUSINESS_OPPORTUNITY_CONFIRMATION_FLAG = 0
            </if>
            <if test="params.queryType != null and params.queryType == 3" >
                AND p.BUSIOPPO_CODE is not null
            </if>
        </where>
        ORDER BY p.EXCEED_TIME
    </select>
</mapper>