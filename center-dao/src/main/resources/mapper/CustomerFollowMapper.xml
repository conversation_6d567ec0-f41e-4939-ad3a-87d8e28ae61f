<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ce.scrm.center.dao.mapper.CustomerFollowMapper" >

    <select id="getZqCustomerFollowStageSummary" resultType="com.ce.scrm.center.dao.entity.view.CustomerFollowStageSummaryView">
        SELECT
            area_id AS area_id,
            sub_id AS sub_id,
            sales_stage AS sales_stage,
            COUNT(DISTINCT CASE WHEN DATE(follow_time) = CURDATE() THEN customer_id END) AS customer_sum_today,
            COUNT(DISTINCT customer_id) AS customer_sum_money
        FROM
            customer_follow
        WHERE
            sales_stage IS NOT NULL and follow_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY
            sub_id,
            sales_stage
    </select>

    <select id="getZqPerformancePredictSummary" resultType="com.ce.scrm.center.dao.entity.view.CustomerFollowMoneySummaryView">
        SELECT
            id,
            sub_id,
            IFNULL(SUM(CASE WHEN sales_stage in ("DICT_SALES_STAGE_004","DICT_SALES_STAGE_005") &amp;&amp; predict_sign_date BETWEEN #{businessMonthStartDate} AND #{businessMonthEndDate} THEN predict_sign_money END),0) AS business_money_sign_sum,
            IFNULL(SUM(CASE WHEN sales_stage = "DICT_SALES_STAGE_006" &amp;&amp; predict_account_date BETWEEN #{businessMonthStartDate} AND #{businessMonthEndDate} THEN predict_account_money END),0) AS business_money_account_sum,
            COUNT(CASE WHEN sales_stage in ("DICT_SALES_STAGE_004","DICT_SALES_STAGE_005") &amp;&amp; predict_sign_date BETWEEN #{businessMonthStartDate} AND #{businessMonthEndDate} THEN 1 END) AS business_customer_sign_count,
            COUNT(CASE WHEN sales_stage = "DICT_SALES_STAGE_006" &amp;&amp; predict_account_date BETWEEN #{businessMonthStartDate} AND #{businessMonthEndDate} THEN 1 END) AS business_customer_account_count,
            IFNULL(SUM(CASE WHEN sales_stage in ("DICT_SALES_STAGE_004","DICT_SALES_STAGE_005") &amp;&amp; predict_sign_date BETWEEN #{naturalMonthStartDate} AND #{naturalMonthEndDate} THEN predict_sign_money END),0) AS natural_money_sign_sum,
            IFNULL(SUM(CASE WHEN sales_stage = "DICT_SALES_STAGE_006" &amp;&amp; predict_account_date BETWEEN #{naturalMonthStartDate} AND #{naturalMonthEndDate} THEN predict_account_money END),0) AS natural_money_account_sum
        FROM
            customer_follow
        where id in (
            SELECT f.id as id
            FROM
                customer_follow f
                    INNER JOIN (
                    SELECT
                        customer_id,
                        sub_id,
                        MAX(follow_time) AS max_follow_time
                    FROM
                        customer_follow
                    WHERE
                        sales_stage in ("DICT_SALES_STAGE_004","DICT_SALES_STAGE_005","DICT_SALES_STAGE_006")
                    GROUP BY
                        sub_id,
                        customer_id) AS m ON f.sub_id = m.sub_id
                    AND f.customer_id = m.customer_id
                    AND f.follow_time = m.max_follow_time
        )
        GROUP BY
            sub_id
    </select>

</mapper>