<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ce.scrm.center.dao.mapper.SegmentMapper" >

    <update id="updateDistributeCount" parameterType="map">
        UPDATE segment
        SET segment_distribute_count = segment_distribute_count + #{distributeCount}
        WHERE segment_id = #{segmentId}
    </update>


    <select id="getDiffSubIdAndStatusPage" resultType="com.ce.scrm.center.dao.entity.Segment">
        SELECT * FROM (
        -- 查询员工能看到的非全员可见的分群
        SELECT seg.*
        FROM segment_emp see
        LEFT JOIN segment seg ON see.segment_id = seg.segment_id
        WHERE seg.delete_flag = 0
        AND seg.is_visible_all = 0
        AND see.emp_id = #{empId}
        <choose>
            <when test='loginSubId == "137"'>
                AND seg.segment_status IN (2, 3)
            </when>
            <when test='loginSubId == "169"'>
                AND seg.segment_status IN (2, 4)
            </when>
            <otherwise>
                AND seg.segment_status = 2
            </otherwise>
        </choose>

        UNION

        -- 查询全员可见的分群
        SELECT seg.*
        FROM segment seg
        WHERE seg.delete_flag = 0
        AND seg.is_visible_all = 1
        <choose>
            <when test='loginSubId == "137"'>
                AND seg.segment_status IN (2, 3)
            </when>
            <when test='loginSubId == "169"'>
                AND seg.segment_status IN (2, 4)
            </when>
            <otherwise>
                AND seg.segment_status = 2
            </otherwise>
        </choose>
        ) AS all_seg
        ORDER BY all_seg.create_time DESC
    </select>
</mapper>