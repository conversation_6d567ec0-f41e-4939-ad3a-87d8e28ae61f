<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ce.scrm.center.dao.mapper.PotentialCustomerMarketingRulesMapper">

    <select id="getWillingBehaviorSelected" resultType="com.ce.scrm.center.dao.entity.query.PotentialWillingBehaviorSelectedVo">
        SELECT intent_code AS value,
               CONCAT(intent_code, '-', intent_name) AS label
        FROM potential_customer_marketing_rules
        GROUP BY intent_code, intent_name

        UNION

        SELECT source_distinct_name_code AS value,
               source_name AS label
        FROM potential_customer_marketing_rules
        WHERE source_name_select_is_open = 1
        GROUP BY source_distinct_name_code, source_name
    </select>
</mapper>
