<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ce.scrm.center.dao.mapper.SegmentDetailMapper" >

    <select id="getSegmentPageByAreaId"
            resultType="com.ce.scrm.center.dao.entity.SegmentDetail"
            parameterType="com.ce.scrm.center.dao.entity.query.AreaSegmentQuery">
        select *,GROUP_CONCAT(SUB_ID ORDER BY SUB_ID SEPARATOR ',') AS subIdListStr
        FROM segment_detail
        <where>
            <if test=" 1 == 1 ">
                and delete_flag = 0
            </if>
            <if test=" query.areaId != null and query.areaId != ''">
                and area_id = #{query.areaId}
            </if>
            <if test=" query.segmentId != null and query.segmentId != ''">
                and segment_id = #{query.segmentId}
            </if>
            <if test=" query.provinceCode != null and query.provinceCode != ''">
                and reg_province = #{query.provinceCode}
            </if>
            <if test=" query.cityCode != null and query.cityCode != ''">
                and reg_city = #{query.cityCode}
            </if>
            <if test=" query.subId != null and query.subId != ''">
                <if test=' query.subId == "-1" '>
                    and area_distribute_time is null
                </if>
                <if test=' query.subId != "-1" '>
                    and sub_id = #{query.subId}
                </if>
            </if>
            <if test=" query.visitFlag != null and query.visitFlag != ''">
                <if test=' query.visitFlag == 1 '>
                    and visit_last_time is not null
                </if>
                <if test=' query.visitFlag == 0 '>
                    and visit_last_time is null
                </if>
            </if>
            <if test=" query.customerStatus != null and query.customerStatus != ''">
                <if test=' query.customerStatus == "1" '>
                    and protect_status = 0 and deal_status = 0
                </if>
                <if test=' query.customerStatus == "2" '>
                    and protect_status = 1 and deal_status = 0
                </if>
                <if test=' query.customerStatus == "3" '>
                    and protect_status = 1 and deal_status = 1
                </if>
            </if>
            <if test=" query.cityCodeList != null and query.cityCodeList.size() > 0 ">
                and reg_city in
                <foreach collection="query.cityCodeList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test=" query.firstIndustryCodeList != null and query.firstIndustryCodeList.size() > 0 ">
                and first_industry_code in
                <foreach collection="query.firstIndustryCodeList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test=" query.secondIndustryCodeList != null and query.secondIndustryCodeList.size() > 0 ">
                and second_industry_code in
                <foreach collection="query.secondIndustryCodeList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test=" query.thirdIndustryCodeList != null and query.thirdIndustryCodeList.size() > 0 ">
                and third_industry_code in
                <foreach collection="query.thirdIndustryCodeList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test=" query.fourthIndustryCodeList != null and query.fourthIndustryCodeList.size() > 0 ">
                and fourth_industry_code in
                <foreach collection="query.fourthIndustryCodeList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test=" query.registerCapitalMin != null ">
                and register_capital <![CDATA[>=]]> #{query.registerCapitalMin}
            </if>
            <if test=" query.registerCapitalMax != null and query.registerCapitalMax != ''">
                and register_capital <![CDATA[<]]> #{query.registerCapitalMax}
            </if>
            <if test=" query.icpFlag != null">
                and icp_flag = #{query.icpFlag}
            </if>
            <if test=" query.jingchukouFlag != null">
                and jingchukou_flag = #{query.jingchukouFlag}
            </if>
            <if test="query.likeRemark != null and query.likeRemark != ''">
                and remark like CONCAT('%',#{query.likeRemark,jdbcType=VARCHAR},'%')
            </if>
        </where>
        group by customer_id
    </select>

</mapper>