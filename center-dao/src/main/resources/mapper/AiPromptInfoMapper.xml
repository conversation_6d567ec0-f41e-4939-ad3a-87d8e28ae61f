<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ce.scrm.center.dao.mapper.AiPromptInfoMapper">

    <select id="getAiPromptInfoList" resultType="com.ce.scrm.center.dao.entity.AiPromptInfoPlus">
        select api.*
        from ai_prompt_info api
        LEFT join (select apai.ai_prompt_info_id
        from ai_prompt_auth_info apai where 1 = 1
        <if test="subId != null and subId != ''">
            AND apai.sub_id = #{subId}
        </if>
        <if test="areaId != null and areaId != ''">
            AND apai.area_id = #{areaId}
        </if>
        <if test="deptId != null and deptId != ''">
            AND apai.dept_id = #{deptId}
        </if>
        <if test="buId != null and buId != ''">
            AND apai.bu_id = #{buId}
        </if>
        group by apai.ai_prompt_info_id) a on a.ai_prompt_info_id = api.id
        WHERE api.delete_flag = 0
        <if test="promptTypeList != null and promptTypeList != ''">
            AND api.prompt_type In
            <foreach collection="promptTypeList" item="promptType" open="(" separator="," close=")">
                #{promptType}
            </foreach>
        </if>
        <if test="startFlag != null">
            AND api.start_flag = #{startFlag}
        </if>
        <if test="!master">
            AND api.company IN
            <foreach collection="companyList" item="company" open="(" separator="," close=")">
                #{company}
            </foreach>
        </if>
        ORDER BY api.create_time DESC,api.start_flag desc
    </select>
</mapper>
