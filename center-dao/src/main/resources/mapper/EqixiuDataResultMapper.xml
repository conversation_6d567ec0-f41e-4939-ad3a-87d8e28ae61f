<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ce.scrm.center.dao.mapper.EqixiuDataResultMapper">

    <resultMap id="BaseResultMap" type="com.ce.scrm.center.dao.entity.EqixiuDataResult">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="activityId" column="activity_id" jdbcType="VARCHAR"/>
        <result property="activityName" column="activity_name" jdbcType="VARCHAR"/>
        <result property="activityUrl" column="activity_url" jdbcType="VARCHAR"/>
        <result property="activityType" column="activity_type" jdbcType="INTEGER"/>
        <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="custType" column="cust_type" jdbcType="VARCHAR"/>
        <result property="salerId" column="saler_id" jdbcType="VARCHAR"/>
        <result property="salerName" column="saler_name" jdbcType="VARCHAR"/>
        <result property="salerDeptId" column="saler_dept_id" jdbcType="VARCHAR"/>
        <result property="salerDeptName" column="saler_dept_name" jdbcType="VARCHAR"/>
        <result property="salerSubId" column="saler_sub_id" jdbcType="VARCHAR"/>
        <result property="salerSubName" column="saler_sub_name" jdbcType="VARCHAR"/>
        <result property="salerAreaId" column="saler_area_id" jdbcType="VARCHAR"/>
        <result property="salerAreaName" column="saler_area_name" jdbcType="VARCHAR"/>
        <result property="salerBuId" column="saler_bu_id" jdbcType="VARCHAR"/>
        <result property="salerBuName" column="saler_bu_name" jdbcType="VARCHAR"/>
        <result property="protectSalerId" column="protect_saler_id" jdbcType="VARCHAR"/>
        <result property="protectSalerName" column="protect_saler_name" jdbcType="VARCHAR"/>
        <result property="protectAreaId" column="protect_area_id" jdbcType="VARCHAR"/>
        <result property="protectArea" column="protect_area" jdbcType="VARCHAR"/>
        <result property="protectSubId" column="protect_sub_id" jdbcType="VARCHAR"/>
        <result property="protectSubName" column="protect_sub_name" jdbcType="VARCHAR"/>
        <result property="protectDeptId" column="protect_dept_id" jdbcType="VARCHAR"/>
        <result property="protectDeptName" column="protect_dept_name" jdbcType="VARCHAR"/>
        <result property="protectBuId" column="protect_bu_id" jdbcType="VARCHAR"/>
        <result property="protectBuName" column="protect_bu_name" jdbcType="VARCHAR"/>
        <result property="contactId" column="contact_id" jdbcType="VARCHAR"/>
        <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
        <result property="contactPhone" column="contact_phone" jdbcType="VARCHAR"/>
        <result property="shareLink" column="share_link" jdbcType="VARCHAR"/>
        <result property="shareType" column="share_type" jdbcType="VARCHAR"/>
        <result property="operatingTerminal" column="operating_terminal" jdbcType="VARCHAR"/>
        <result property="triggerId" column="trigger_id" jdbcType="VARCHAR"/>
        <result property="eventType" column="event_type" jdbcType="VARCHAR"/>
        <result property="recentOpenTime" column="recent_open_time" jdbcType="TIMESTAMP"/>
        <result property="recentLotteryTime" column="recent_lottery_time" jdbcType="TIMESTAMP"/>
        <result property="recentShareTime" column="recent_share_time" jdbcType="TIMESTAMP"/>
        <result property="isWin" column="is_win" jdbcType="INTEGER"/>
        <result property="winInfo" column="win_info" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,activity_id,activity_name,
        activity_url,activity_type,customer_id,
        customer_name,cust_type,saler_id,
        saler_name,saler_dept_id,saler_dept_name,
        saler_sub_id,saler_sub_name,saler_area_id,
        saler_area_name,saler_bu_id,saler_bu_name,
        protect_saler_id,protect_saler_name,protect_area_id,
        protect_area,protect_sub_id,protect_sub_name,
        protect_dept_id,protect_dept_name,protect_bu_id,
        protect_bu_name,contact_id,contact_name,
        contact_phone,share_link,share_type,
        operating_terminal,trigger_id,event_type,
        recent_open_time,recent_lottery_time,recent_share_time,
        is_win,win_info,create_time,
        update_time
    </sql>


    <select id="selectActivityList" parameterType="com.ce.scrm.center.dao.entity.EqixiuDataResultQuery"
            resultType="com.ce.scrm.center.dao.entity.EqixiuDataResultAnalyze">
        select *
        from (
        SELECT
        saler_id,
        saler_dept_id,
        saler_area_id,
        saler_sub_id,
        activity_id,
        activity_type as activityType,
        max(customer_id) as customerId,
        max(activity_name) as activityName,
        max(customer_name) as customerName,
        max(contact_name) AS contactName,
        contact_phone as contactPhone,
        max(saler_name) as salerName,
        COUNT(CASE WHEN event_type = 'system_share' THEN 1 END) AS shareTimes,
        max(saler_area_name) as salerArea,
        max(saler_sub_name) as salerSub,
        max(saler_dept_name) as salerDept,
        max(saler_bu_name) as salerBuName,
        COUNT(CASE WHEN event_type = 'preview_view' THEN 1 END) AS openTimes,
        max(recent_open_time) as recentOpenTime,
        COUNT(CASE WHEN event_type = 'join_lottery' THEN 1 END) AS lotteryTimes,
        SUM(is_win) AS winTimes,
        -- GROUP_CONCAT(win_info) AS win_info_concat
        max(recent_share_time) as recentShareTime
        FROM eqixiu_data_result
        <where>
            <if test="query.activityType != null and query.activityType != ''">
                and activity_type = #{query.activityType}
            </if>
            <if test="query.activityId != null and query.activityId != ''">
                and activity_id = #{query.activityId}
            </if>
            <if test="null != query.salerId and '' != query.salerId">
                and saler_id = #{query.salerId}
            </if>
            <if test="null != query.customerName and '' != query.customerName">
                and customerName like concat('%', #{query.customerName}, '%')
            </if>
            <if test="null != query.deptId and '' != query.deptId">
                and saler_dept_id = #{query.deptId}
            </if>
            <if test="null != query.areaId and '' != query.areaId">
                and saler_area_id = #{query.areaId}
            </if>
            <if test="null != query.subId and '' != query.subId">
                and saler_sub_id = #{query.subId}
            </if>
            <if test="null != query.buId and '' != query.buId">
                and saler_bu_id = #{query.buId}
            </if>
        </where>
        GROUP BY customer_id, activity_id, saler_id
        ) tmp
        <where>
            <if test="null != query.lotteryTimes and '' != query.lotteryTimes">
                <if test="query.lotteryTimes == 0">
                    and tmp.lotteryTimes = 0
                </if>
                <if test="query.lotteryTimes == 1">
                    and tmp.lotteryTimes &gt; 0
                </if>
            </if>
            <if test="null != query.isWin and '' != query.isWin">
                <if test="query.isWin == 0">
                    and tmp.winTimes = 0
                </if>
                <if test="query.isWin == 1">
                    and tmp.winTimes &gt; 0
                </if>
            </if>
        </where>
        order by recentShareTime desc
        <choose>
            <when test="null != query.shareTimesSort and '' != query.shareTimesSort">
                ,tmp.shareTimes ${query.shareTimesSort == 1 ? 'desc' : 'asc'}
            </when>
            <when test="null != query.lotteryTimesSort and '' != query.lotteryTimesSort">
                ,tmp.lotteryTimes ${query.lotteryTimesSort == 1 ? 'desc' : 'asc'}
            </when>
            <when test="null != query.winTimesSort and '' != query.winTimesSort">
                ,tmp.winTimes ${query.winTimesSort == 1 ? 'desc' : 'asc'}
            </when>
            <when test="null != query.openTimesSort and '' != query.openTimesSort">
                ,tmp.openTimes ${query.openTimesSort == 1 ? 'desc' : 'asc'}
            </when>
        </choose>
    </select>


    <select id="getActivityStatistics" parameterType="com.ce.scrm.center.dao.entity.EqixiuDataResultQuery"
            resultType="com.ce.scrm.center.dao.entity.EqixiuDataResultAnalyze">
        SELECT
        -- 这个地方只会有唯一值
        max(activity_type) as activityType,
        max(activity_name) as activityName,
        max(saler_area_name) as salerArea,
        max(saler_sub_name) as salerSub,
        max(saler_dept_name) as salerDept,
        max(saler_name) as salerName,
        max(saler_bu_name) as salerBuName,
        COUNT(CASE WHEN event_type = 'system_share' THEN 1 END) AS shareTimes,
        COUNT(CASE WHEN event_type = 'preview_view' THEN 1 END) AS openTimes
        FROM eqixiu_data_result
        <where>
            <if test="query.activityType != null and query.activityType != ''">
                and activity_type = #{query.activityType}
            </if>
            <if test="null != query.salerId and '' != query.salerId">
                and saler_id = #{query.salerId}
            </if>
            <if test="null != query.deptId and '' != query.deptId">
                and saler_dept_id = #{query.deptId}
            </if>
            <if test="null != query.areaId and '' != query.areaId">
                and saler_area_id = #{query.areaId}
            </if>
            <if test="null != query.subId and '' != query.subId">
                and saler_sub_id = #{query.subId}
            </if>
            <if test="query.activityId != null and query.activityId != ''">
                and activity_id = #{query.activityId}
            </if>
            <if test="null != query.buId and '' != query.buId">
                and saler_bu_id = #{query.buId}
            </if>
        </where>
        <if test="query.level == 1">
            GROUP BY activity_id ,saler_area_id
        </if>
        <if test="query.level == 2">
            GROUP BY activity_id ,saler_sub_id
        </if>
        <if test="query.level == 3">
            GROUP BY activity_id ,saler_dept_id
        </if>
        <if test="query.level == 4">
            GROUP BY activity_id ,saler_id
        </if>
    </select>

</mapper>
