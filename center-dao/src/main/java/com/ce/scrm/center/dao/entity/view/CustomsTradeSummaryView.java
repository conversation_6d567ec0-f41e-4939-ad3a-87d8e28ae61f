package com.ce.scrm.center.dao.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/21
 */
@Data
public class CustomsTradeSummaryView implements Serializable {
    /**
     * 商品编码（唯一标识）
     * 对应数据库字段：product_code
     */
    private String productCode;

    /**
     * 商品全称
     * 对应数据库字段：product_name
     */
    private String productName;

    /**
     * 合作伙伴编码
     * 对应数据库字段：partner_code
     */
    private String partnerCode;

    /**
     * 合作伙伴全称
     * 对应数据库字段：partner_name
     */
    private String partnerName;

    /**
     * 总交易量（单位：美元）
     * 对应数据库字段：total
     * 聚合场景下使用SUM(total)别名total_sum
     */
    private BigDecimal totalSum;
}
