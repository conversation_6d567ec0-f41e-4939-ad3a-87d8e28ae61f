package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.SjAssignSalerConfig;
import com.ce.scrm.center.dao.mapper.SjAssignSalerConfigMapper;
import com.ce.scrm.center.dao.service.SjAssignSalerConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商机下发销售配置Service实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
@Slf4j
@Service
public class SjAssignSalerConfigServiceImpl extends ServiceImpl<SjAssignSalerConfigMapper, SjAssignSalerConfig> implements SjAssignSalerConfigService {

    @Override
    public SjAssignSalerConfig getNextSalerBySubId(String subId) {
        LambdaQueryWrapper<SjAssignSalerConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SjAssignSalerConfig::getSubId, subId)
                .eq(SjAssignSalerConfig::getStatus, 1) // 可用状态
                .orderByAsc(SjAssignSalerConfig::getOrderNum) // 按顺序升序
                .orderByAsc(SjAssignSalerConfig::getWeight) // 按权重升序
                .orderByAsc(SjAssignSalerConfig::getId) // 按ID升序
                .last("LIMIT 1");

        return this.getOne(queryWrapper);
    }

    @Override
    public List<SjAssignSalerConfig> getAvailableSalersBySubId(String subId) {
        LambdaQueryWrapper<SjAssignSalerConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SjAssignSalerConfig::getSubId, subId)
                .eq(SjAssignSalerConfig::getStatus, 1) // 可用状态
                .orderByAsc(SjAssignSalerConfig::getOrderNum) // 按顺序升序
                .orderByAsc(SjAssignSalerConfig::getWeight); // 按权重升序

        return this.list(queryWrapper);
    }

    @Override
    public boolean updateWeight(String salerId) {
        LambdaUpdateWrapper<SjAssignSalerConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SjAssignSalerConfig::getSalerId, salerId)
                .setSql("weight = weight + 1");
        return this.update(updateWrapper);
    }
}




