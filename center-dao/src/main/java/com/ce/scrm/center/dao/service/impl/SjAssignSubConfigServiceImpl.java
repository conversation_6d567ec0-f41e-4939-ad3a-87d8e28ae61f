package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.SjAssignSubConfig;
import com.ce.scrm.center.dao.mapper.SjAssignSubConfigMapper;
import com.ce.scrm.center.dao.service.SjAssignSubConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 商机下发分司配置Service实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
@Slf4j
@Service
public class SjAssignSubConfigServiceImpl extends ServiceImpl<SjAssignSubConfigMapper, SjAssignSubConfig> implements SjAssignSubConfigService {

    @Override
    public SjAssignSubConfig getNextSubConfig(List<String> subIdList) {
        if (CollectionUtils.isEmpty(subIdList)) {
            return null;
        }
        
        LambdaQueryWrapper<SjAssignSubConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SjAssignSubConfig::getStatus, 1) // 可用状态
                .in(SjAssignSubConfig::getMarketId, subIdList)
                .orderByAsc(SjAssignSubConfig::getOrderNum) // 按顺序升序
                .orderByAsc(SjAssignSubConfig::getWeight) // 按权重升序
                .orderByAsc(SjAssignSubConfig::getId) // 按ID升序
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean updateWeight(String subId) {
        LambdaUpdateWrapper<SjAssignSubConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SjAssignSubConfig::getSubId, subId)
                .setSql("weight = weight + 1");
        return this.update(updateWrapper);
    }
} 