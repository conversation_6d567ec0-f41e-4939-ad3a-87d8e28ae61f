package com.ce.scrm.center.dao.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 市场分司映射表查询条件
 * @Author: lijinpeng
 * @Date: 2025/1/16 10:08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SmaMarketSubcompanyQuery implements Serializable {

    private Integer id;

    /** 市场id */
    private String marketId;

    /** 分司id */
    private String subCompany;

    /** 类型(1专属，2周边，3共享) */
    private Integer type;

    /** 分公司的默认省code（仅type=1才有值） */
    private String defaultProvince;

    /** 分公司的默认市code（仅type=1才有值） */
    private String defaultCity;

    /** 分公司的默认区code（仅type=1才有值） */
    private String defaultArea;

    /**
     * 作为in条件拼接
     */
    private List<String> subIdList;

    /**
     * 作为in条件拼接
     */
    private List<String> marketIdList;

}
