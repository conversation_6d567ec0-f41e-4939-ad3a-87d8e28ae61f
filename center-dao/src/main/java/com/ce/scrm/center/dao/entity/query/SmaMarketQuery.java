package com.ce.scrm.center.dao.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 市场查询条件
 * @Author: lijinpeng
 * @Date: 2025/1/17 09:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SmaMarketQuery implements Serializable {

    private String id;

    /** 市场名称 */
    private String name;

    /** 市场名称 */
    private String code;

    /** 策略id(1时间，2顺序) */
    private Integer tacticeId;

    private List<String> idList;

}
