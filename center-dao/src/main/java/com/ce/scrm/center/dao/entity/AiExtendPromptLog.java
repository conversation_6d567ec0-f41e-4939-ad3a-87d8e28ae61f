package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @description ai拓展提示词记录表
 * <AUTHOR>
 * @date 2025-02-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value ="ai_extend_prompt_log")
public class AiExtendPromptLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键ID
     */
    private Long id;

    /**
     * pid
     */
    private String pid;

    /**
     * custId
     */
    private String custId;

    /**
     * ai拓展提示词
     */
    private String aiExtendPrompt;

    /**
     * 操作人ID
     */
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

}
