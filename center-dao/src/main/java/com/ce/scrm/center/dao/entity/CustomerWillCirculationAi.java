package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 即将流转（流失）客户表
 *
 * @author: JiuDD
 * date: 2024/7/19
 */
@Data
@TableName(value = "customer_will_circulation_ai")
public class CustomerWillCirculationAi implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 事业部id
     */
    private String buId;

    /**
     * 事业部名称
     */
    private String buName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 流转（流失）原因
     */
    private String reason;

    /**
     * 来源（流转、流失）
     *
     * @see com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum
     */
    private Integer origin;

    /**
     * 状态 总监是否已经分配 0：未分配 1：已分配
     */
    private Integer status;

    /**
     * 删除标记：1删除，0未删除
     */
    private Integer deleteFlag;

    /**
     * 预计流转（流失）日期
     */
    private Date preDate;

    /**
     * 绝对保护(绝对保护的客户不流转)：0-否，1-是
     */
    private Integer absoluteProtectFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建日期
     */
    private Date createDate;

    public CustomerWillCirculationAi() {
    }
}
