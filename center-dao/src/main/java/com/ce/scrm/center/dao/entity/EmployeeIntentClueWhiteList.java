package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Description: 市场部人员组织表
 * @author: JiuDD
 * date: 2024/11/5 9:48
 */
@Data
@TableName(value ="employee_intent_clue_white_list")
public class EmployeeIntentClueWhiteList implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 员工id
     */
    private String empId;

    /**
     * 组织类型: 0未知 1中企 2跨境
     */
    private Byte orgType;

    public EmployeeIntentClueWhiteList() {}
}