package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "zq_customer_follow_stage_summary")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZqCustomerFollowStageSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 商务月 yyyy-MM
     */
    private String businessMonth;

    /**
     * 机构类型 0:全国，1:区域，2:分司）
     * @see com.ce.scrm.center.service.enums.ZqSummaryOrgTypeEnum
     */
    private Integer orgType;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 意向客户数今日
     */
    private Integer yixiangCountToday;

    /**
     * 意向客户数当月
     */
    private Integer yixiangCountMonth;

    /**
     * 初步建联客户数今日
     */
    private Integer chubuJianlianCountToday;

    /**
     * 初步建联客户数当月
     */
    private Integer chubuJianlianCountMonth;

    /**
     * 需求确认客户数今日
     */
    private Integer xuqiuQuerenCountToday;

    /**
     * 需求确认客户数当月
     */
    private Integer xuqiuQuerenCountMonth;

    /**
     * 方案汇报客户数今日
     */
    private Integer fanganHuibaoCountToday;

    /**
     * 方案汇报客户数当月
     */
    private Integer fanganHuibaoCountMonth;

    /**
     * 商务沟通客户数今日
     */
    private Integer shangwuGoutongCountToday;

    /**
     * 商务沟通客户数当月
     */
    private Integer shangwuGoutongCountMonth;

    /**
     * 合同签署客户数今日
     */
    private Integer hetongQianshuCountToday;

    /**
     * 合同签署客户数当月
     */
    private Integer hetongQianshuCountMonth;

    /**
     * 成交签单客户数今日
     */
    private Integer chengjiaoQianyueCountToday;

    /**
     * 成交签单客户数当月
     */
    private Integer chengjiaoQianyueCountMonth;

    /**
     * 排名，并列跳过，1、2、2、4;全国颗粒度此字段空
     */
    private Integer ranking;

    /**
     * 是否有效 1:有效 0:无效
     */
    private Integer flag;

    /**
     * 创建时间
     */
    private Date dbInsertTime;

    /**
     * 更新时间
     */
    private Date dbUpdateTime;

}
