package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.SjAssignSubConfig;

import java.util.List;

/**
 * 商机下发分司配置Service接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
public interface SjAssignSubConfigService extends IService<SjAssignSubConfig> {

    /**
     * 获取下一个分司配置（轮询）
     *
     * @param subIdList 分司ID列表
     * @return 分司配置
     */
    SjAssignSubConfig getNextSubConfig(List<String> subIdList);

    /**
     * 更新权重
     *
     * @param subId 分司ID
     * @return 是否更新成功
     */
    boolean updateWeight(String subId);

}