package com.ce.scrm.center.dao.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 市场区域映射表查询条件
 * @Author: lijinpeng
 * @Date: 2025/1/16 10:16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SmaMarketAreaQuery implements Serializable {

    private Integer id;
    /**
     * 市场id
     */
    private String marketId;
    /**
     * 省code
     */
    private String provinceCode;
    /**
     * 市code
     */
    private String cityCode;
    /**
     *  区code
     */
    private String areaCode;
    /**
     * 省
     */
    private String provinceName;
    /**
     * 市
     */
    private String cityName;
    /**
     * 区
     */
    private String areaName;

    /**
     * 市场id作为in条件
     */
    private List<String> marketIdList;

}
