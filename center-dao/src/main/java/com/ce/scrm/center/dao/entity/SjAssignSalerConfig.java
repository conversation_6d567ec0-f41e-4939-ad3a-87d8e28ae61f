package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 商机下发销售配置表
 *
 * @TableName sj_assign_saler_config
 */
@TableName(value = "sj_assign_saler_config")
@Data
public class SjAssignSalerConfig {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 销售id
     */
    private String salerId;

    /**
     * 销售名称
     */
    private String salerName;

    /**
     * 是否可用 0:否 1:是
     */
    private Integer status;

    /**
     * 顺序
     */
    private Integer orderNum;

    /**
     * 权重，每分配一次+1
     */
    private Integer weight;

    /**
     * 创建者
     */
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updatedId;

    /**
     * 更新时间
     */
    private Date updatedTime;
}