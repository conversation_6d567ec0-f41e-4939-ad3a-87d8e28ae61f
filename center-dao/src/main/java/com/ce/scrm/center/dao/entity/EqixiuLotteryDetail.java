package com.ce.scrm.center.dao.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName eqixiu_lottery_detail
 */
@Data
public class EqixiuLotteryDetail implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 第三方ID
     */
    private String thirdId;

    /**
     * 兑奖码名称 
     */
    private String lotteryName;

    /**
     * 兑奖码 
     */
    private String lotteryCode;

    /**
     * 作品ID
     */
    private String creationId;

    /**
     * 兑奖码所属奖项 
     */
    private String prizeLevel;

    /**
     * 状态：0，使用中（已绑定到作品，未发到用户）、1:已发放（已绑定作品，用户中奖）、3:未使用（初始化导入，未绑定作品） 
     */
    private Integer lotteryStatus;

    /**
     * 中奖者昵称 
     */
    private String nickName;

    /**
     * 中奖者customer_id
     */
    private String customerId;

    /**
     * 
     */
    private String codeDecoderInfo;

    /**
     * 中奖者用户第三方标示：例如微信openId，第三方用户Id 
     */
    private String openId;

    /**
     * 1 个人 2 企业
     */
    private Integer type;

    /**
     * 中奖者用户唯一标示 
     */
    private String encodeId;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        EqixiuLotteryDetail other = (EqixiuLotteryDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getThirdId() == null ? other.getThirdId() == null : this.getThirdId().equals(other.getThirdId()))
            && (this.getLotteryName() == null ? other.getLotteryName() == null : this.getLotteryName().equals(other.getLotteryName()))
            && (this.getLotteryCode() == null ? other.getLotteryCode() == null : this.getLotteryCode().equals(other.getLotteryCode()))
            && (this.getCreationId() == null ? other.getCreationId() == null : this.getCreationId().equals(other.getCreationId()))
            && (this.getPrizeLevel() == null ? other.getPrizeLevel() == null : this.getPrizeLevel().equals(other.getPrizeLevel()))
            && (this.getLotteryStatus() == null ? other.getLotteryStatus() == null : this.getLotteryStatus().equals(other.getLotteryStatus()))
            && (this.getNickName() == null ? other.getNickName() == null : this.getNickName().equals(other.getNickName()))
            && (this.getCustomerId() == null ? other.getCustomerId() == null : this.getCustomerId().equals(other.getCustomerId()))
            && (this.getCodeDecoderInfo() == null ? other.getCodeDecoderInfo() == null : this.getCodeDecoderInfo().equals(other.getCodeDecoderInfo()))
            && (this.getOpenId() == null ? other.getOpenId() == null : this.getOpenId().equals(other.getOpenId()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getEncodeId() == null ? other.getEncodeId() == null : this.getEncodeId().equals(other.getEncodeId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getThirdId() == null) ? 0 : getThirdId().hashCode());
        result = prime * result + ((getLotteryName() == null) ? 0 : getLotteryName().hashCode());
        result = prime * result + ((getLotteryCode() == null) ? 0 : getLotteryCode().hashCode());
        result = prime * result + ((getCreationId() == null) ? 0 : getCreationId().hashCode());
        result = prime * result + ((getPrizeLevel() == null) ? 0 : getPrizeLevel().hashCode());
        result = prime * result + ((getLotteryStatus() == null) ? 0 : getLotteryStatus().hashCode());
        result = prime * result + ((getNickName() == null) ? 0 : getNickName().hashCode());
        result = prime * result + ((getCustomerId() == null) ? 0 : getCustomerId().hashCode());
        result = prime * result + ((getCodeDecoderInfo() == null) ? 0 : getCodeDecoderInfo().hashCode());
        result = prime * result + ((getOpenId() == null) ? 0 : getOpenId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getEncodeId() == null) ? 0 : getEncodeId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", thirdId=").append(thirdId);
        sb.append(", lotteryName=").append(lotteryName);
        sb.append(", lotteryCode=").append(lotteryCode);
        sb.append(", creationId=").append(creationId);
        sb.append(", prizeLevel=").append(prizeLevel);
        sb.append(", lotteryStatus=").append(lotteryStatus);
        sb.append(", nickName=").append(nickName);
        sb.append(", customerId=").append(customerId);
        sb.append(", codeDecoderInfo=").append(codeDecoderInfo);
        sb.append(", openId=").append(openId);
        sb.append(", type=").append(type);
        sb.append(", encodeId=").append(encodeId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}