package com.ce.scrm.center.dao.service.impl;

import cn.ce.cesupport.enums.YesOrNoEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.Segment;
import com.ce.scrm.center.dao.mapper.SegmentMapper;
import com.ce.scrm.center.dao.service.SegmentService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/2/27 11:47
 */
@Service
public class SegmentServiceImpl extends ServiceImpl<SegmentMapper, Segment> implements SegmentService {
    @Override
    public Segment getSegmentById(String segmentId) {
        return this.getOne(new LambdaQueryWrapper<Segment>().eq(Segment::getSegmentId, segmentId));
    }

    @Override
    public Set<String> getValidSegmentIds(Date referenceTime) {
        List<Segment> validSegments = this.list(new LambdaQueryWrapper<Segment>()
                .eq(Segment::getDeleteFlag, YesOrNoEnum.NO.getCode())
                .le(Segment::getSegmentBeginTime, referenceTime)
                .ge(Segment::getSegmentEndTime, referenceTime));
        if (CollectionUtils.isEmpty(validSegments)) {
            return Collections.emptySet();
        }
        return validSegments.stream().map(Segment::getSegmentId).collect(Collectors.toSet());
    }

    @Override
    public void updateDistributeCount(String segmentId, Integer distributeCount) {
        this.getBaseMapper().updateDistributeCount(segmentId, distributeCount);
    }

	@Override
	public Page<Segment> getDiffSubIdAndStatusPage(Page<Segment> page, String subId, String currentLoginEmpId) {
		return this.getBaseMapper().getDiffSubIdAndStatusPage(page, subId, currentLoginEmpId);
	}
}
