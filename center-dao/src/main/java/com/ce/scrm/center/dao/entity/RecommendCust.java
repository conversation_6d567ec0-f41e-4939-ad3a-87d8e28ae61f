package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 推荐客户
 * @createDate 2024-08-13
 */
@Data
@TableName("t_recommend_cust")
public class RecommendCust {

    @TableId
    private String id;

    /**
     * 被推荐客户id
     */
    private String recommendedCustId;
    /**
     * 被推荐客户所在分司id
     */
    private String subId;
    /**
     * 被推荐客户所在部门id
     */
    private String deptId;
    /**
     * 被推荐客户所在区域id
     */
    private String areaId;
    /**
     * 被推荐客户所在商务id
     */
    private String salerId;
    /**
     * 推荐客户id
     */
    private String recommendCustId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 是否签单
     */
    private Integer isSign;
    /**
     * 首次签单时间
     */
    private LocalDateTime firstSignTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}