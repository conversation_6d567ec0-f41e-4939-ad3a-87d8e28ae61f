package com.ce.scrm.center.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EqixiuDataResultAnalyze implements Serializable {

    private String salerId;
    private String salerDeptId;
    private String activityType;
    private String salerAreaId;
    private String salerSubId;
    private String activityName;
    private String customerName;
    private String contactName;
    private String contactPhone;
    private String salerName;
    private Integer shareTimes;
    private String salerArea;
    private String salerSub;
    private String salerDept;
    private Integer openTimes;
    private Date recentOpenTime;
    private Integer lotteryTimes;
    private Integer winTimes;
    private String winInfoConcat;
    private String customerId;
    private String salerBuName;
}