package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 当前商务月临时表
 */
@Data
@TableName(value ="current_business_month_temporary_table")
public class CurrentBusinessMonthTemporaryTable implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 商务月 yyyy-MM
     */
    private String bussinessMonth;

    /**
     * 开始时间
     */
    private Date beginDate;

    /**
     * 半月时间
     */
    private Date halfMonth;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 是否有效 1:已经执行 0:未执行
     */
    private Integer flag;

    /**
     * 创建时间
     */
    private Date dbInsertTime;

    /**
     * 更新时间
     */
    private Date dbUpdateTime;

}
