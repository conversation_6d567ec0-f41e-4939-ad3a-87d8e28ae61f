package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.SjAssignSalerConfig;

import java.util.List;

/**
 * 商机下发销售配置Service接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
public interface SjAssignSalerConfigService extends IService<SjAssignSalerConfig> {

    /**
     * 根据分司ID获取下一个销售配置（轮询）
     *
     * @param subId 分司ID
     * @return 销售配置
     */
    SjAssignSalerConfig getNextSalerBySubId(String subId);

    /**
     * 根据分司ID获取所有可用销售配置
     *
     * @param subId 分司ID
     * @return 销售配置列表
     */
    List<SjAssignSalerConfig> getAvailableSalersBySubId(String subId);

    /**
     * 更新销售配置权重
     *
     * @param salerId
     * @return 是否更新成功
     */
    boolean updateWeight(String salerId);
}
