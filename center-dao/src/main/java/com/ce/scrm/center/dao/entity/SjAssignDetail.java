package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 商机下发明细记录表
 * @TableName sj_assign_detail
 */
@TableName(value ="sj_assign_detail")
@Data
public class SjAssignDetail {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 审核id
     */
    private Long reviewId;

    /**
     * 审核来源类型 1：SDR 2：CC 必填非空
     */
    private Integer reviewSrcType;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 商务代表ID
     */
    private String salerId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 事业部ID
     */
    private String buId;

    /**
     * 分公司ID
     */
    private String subId;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 市场ID
     */
    private String marketId;

    /**
     * 回执标记 0:待回执 1:已回执 2：超时未回执
     */
    private Integer receiptFlag;

    /**
     * 跟进区间-到期时间
     */
    private Date receiptEndTime;

    /**
     * 跟进到期检查标志  0:未检查 1:已检查
     */
    private Integer followCheck;

    /**
     * 跟进区间-开始时间
     */
    private Date followStartTime;

    /**
     * 跟进到期时间
     */
    private Date followEndTime;

    /**
     * 创建者
     */
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updatedId;

    /**
     * 更新时间
     */
    private Date updatedTime;
}