package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.PotentialCustomerMarketingRules;
import com.ce.scrm.center.dao.entity.query.PotentialWillingBehaviorSelectedVo;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【potential_customer_marketing_rules(潜客营销规则配置表)】的数据库操作Service
* @createDate 2025-08-05 20:53:35
*/
public interface PotentialCustomerMarketingRulesService extends IService<PotentialCustomerMarketingRules> {

	/**
	 * ABM项目，SDR/主管工作台、公海的意愿行为下拉筛选框列表
	 */
	List<PotentialWillingBehaviorSelectedVo> getWillingBehaviorSelected();

}
