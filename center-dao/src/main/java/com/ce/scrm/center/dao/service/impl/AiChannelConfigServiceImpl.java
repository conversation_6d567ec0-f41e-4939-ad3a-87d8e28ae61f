package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.AiChannelConfig;
import com.ce.scrm.center.dao.mapper.AiChannelConfigMapper;
import com.ce.scrm.center.dao.service.AiChannelConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class AiChannelConfigServiceImpl extends ServiceImpl<AiChannelConfigMapper, AiChannelConfig> implements AiChannelConfigService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdateAiChannelConfig(AiChannelConfig aiChannelConfig) {
        if (aiChannelConfig.getStatus() != null && aiChannelConfig.getStatus() == 1) {
            this.lambdaUpdate()
                    .set(AiChannelConfig::getStatus, 0)
                    .eq(AiChannelConfig::getStatus, 1)
                    .update();
        }
        // 保存或更新
        this.saveOrUpdate(aiChannelConfig);
        List<AiChannelConfig> aiChannelConfigList = this.lambdaQuery().eq(AiChannelConfig::getStatus, 1).list();
        if (CollectionUtils.isEmpty(aiChannelConfigList)) {
            throw new RuntimeException("无可用配置，更新失败");
        }
        return true;
    }
}
