package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.EqixiuDataResult;
import com.ce.scrm.center.dao.mapper.EqixiuDataResultMapper;
import com.ce.scrm.center.dao.service.IEqixiuDataResultService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【eqixiu_data_result(按照    活动 汇总 )】的数据库操作Service实现
* @createDate 2024-11-28 18:42:08
*/
@Service
public class EqixiuDataResultServiceImpl extends ServiceImpl<EqixiuDataResultMapper, EqixiuDataResult>
    implements IEqixiuDataResultService {

}




