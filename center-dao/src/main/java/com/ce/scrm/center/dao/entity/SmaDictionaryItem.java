package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@TableName(value ="SMA_DICTIONARY_ITEM")
public class SmaDictionaryItem implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId
    private String id;

    /** 字典ID */
    private String dictionaryId;

    /** 父ID */
    private String parentId;

    /** 版本号 */
    private Integer version;

    /** 编码 */
    private String code;

    /** 名称 */
    private String name;

    /** 排序字段 */
    private Integer displayOrder;

    /** 描述字段 */
    private String description;


    private String itemValue;

    /** 状态 */
    private Integer state;

    @TableField(exist = false)
    private List<SmaDictionaryItem> children;

}