package com.ce.scrm.center.dao.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 历史线索导入到customer_leads || 53客服等来源渠道 进入customer_leads 需要传的对象
 * <AUTHOR>
 * @Date 2025-08-09 15:19
 */
@Data
public class CustomerTemplateImportDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 客户id
	 */
	private String customerId;

	/**
	 * leadsCode
	 */
	private String leadsCode;

}
