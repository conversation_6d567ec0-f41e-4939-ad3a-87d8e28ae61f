package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 潜客营销规则配置表
 * @TableName potential_customer_marketing_rules
 */
@TableName(value = "potential_customer_marketing_rules")
@Data
public class PotentialCustomerMarketingRules implements Serializable {

	/**
	 * 主键ID
	 */
	@TableId(type = IdType.AUTO)
	private Long id;

	/**
	 * 等级code A、B、C、D
	 */
	private String intentCode;

	/**
	 * 类别名称
	 */
	private String intentName;

	/**
	 * leads code (纯数字)
	 */
	private String sourceCode;

	/**
	 * 来源名称
	 */
	private String sourceName;

	/**
	 * 来源名称对应的code
	 */
	private String sourceDistinctNameCode;

	/**
	 * 来源名称筛选项是否开启 0-否 1-是
	 */
	private Integer sourceNameSelectIsOpen;

	/**
	 * 分配角色: SDR、CC、用户id、不分配
	 */
	private String allocateRole;

	/**
	 * leads来源说明
	 */
	private String sourceDesc;

	/**
	 * 字典名称
	 */
	private String dictName;

	/**
	 * 识别码类型 1-线索类型 2-线索渠道
	 */
	private String identifierType;

	/**
	 * 识别码
	 */
	private String identifierCode;

	/**
	 * 网页位置标签
	 */
	private String locateTag;

	/**
	 * leads摘要（四要素：1.联系人 2.手机号 3.注册地 4.客户名称）
	 */
	private String leadsSummaries;

	/**
	 * 一级分类标签
	 */
	private String tagCategoryOne;

	/**
	 * 一级分类标签code
	 */
	private Integer tagCategoryOneCode;

	/**
	 * 二级分类标签
	 */
	private String tagCategoryTwo;

	/**
	 * 二级分类标签code
	 */
	private Integer tagCategoryTwoCode;

	/**
	 * 三级分类标签
	 */
	private String tagCategoryThree;

	/**
	 * 三级分类标签code
	 */
	private String tagCategoryThreeCode;

	/**
	 * 更多详情
	 */
	private String moreInfo;

	/**
	 * 状态 0-暂无 1-新增 2-已有
	 */
	private Integer state;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 标签分类id（D类标签对应的分类id）
	 */
	private Long tagCategoryId;

	/**
	 * 创建人ID
	 */
	private String createBy;

	/**
	 * 修改人ID
	 */
	private String updateBy;

	/**
	 * 创建时间
	 */
	private Date createdTime;

	/**
	 * 更新时间
	 */
	private Date updatedTime;

	@TableField(exist = false)
	private static final long serialVersionUID = 1L;
}