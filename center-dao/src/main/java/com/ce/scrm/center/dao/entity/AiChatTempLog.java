package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("ai_chat_temp_log")
public class AiChatTempLog implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String channelId;
    private String pid;
    private String custId;
    private String promptId;
    private String platform;
    private String chatId;
    private String loginEmployeeId;
    private String content;
    private Date dbInsertTime;
    private Date dbUpdateTime;
}
