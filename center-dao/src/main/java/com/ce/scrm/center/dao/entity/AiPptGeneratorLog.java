package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_ppt_generator_log")
public class AiPptGeneratorLog {

    private String id;

    private String employeeId;
    private String chatId;
    private String employeeName;
    private String customerName;

    private String pptContent;

    private String pptResult;

    private Integer status;

    private Date createTime;

    private Date updateTime;
}
