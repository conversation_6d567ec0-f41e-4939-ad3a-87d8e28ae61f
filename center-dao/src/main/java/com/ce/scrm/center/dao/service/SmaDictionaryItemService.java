package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.SmaDictionaryItem;
import com.ce.scrm.center.dao.entity.query.SmaDictionaryItemQuery;
import com.ce.scrm.center.dao.entity.view.SmaDictionaryItemNewView;

import java.util.List;

/**
 * @description 字典 service
 * <AUTHOR>
 * @date 2024-04-19
 */
public interface SmaDictionaryItemService extends IService<SmaDictionaryItem> {

    List<SmaDictionaryItemNewView> selectByCondition(SmaDictionaryItemQuery condition);

}
