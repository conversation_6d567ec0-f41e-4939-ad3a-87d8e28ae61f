package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.CustomerWillCirculation;
import com.ce.scrm.center.dao.entity.CustomerWillCirculationAi;
import com.ce.scrm.center.dao.mapper.CustomerWillCirculationAiMapper;
import com.ce.scrm.center.dao.mapper.CustomerWillCirculationMapper;
import com.ce.scrm.center.dao.service.CustomerWillCirculationAiService;
import com.ce.scrm.center.dao.service.CustomerWillCirculationService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Description: 即将流转（流失）客户表
 *
 * @author: JiuDD
 * date: 2024/7/19
 */
@Service
public class CustomerWillCirculationAiServiceImpl extends ServiceImpl<CustomerWillCirculationAiMapper, CustomerWillCirculationAi> implements CustomerWillCirculationAiService {


}




