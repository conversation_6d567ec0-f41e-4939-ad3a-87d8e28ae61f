package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ai_access_auth")
public class AiAccessAuth {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String employeeId;

    private String operator;

    private Date createTime;

    private Date updateTime;
}
