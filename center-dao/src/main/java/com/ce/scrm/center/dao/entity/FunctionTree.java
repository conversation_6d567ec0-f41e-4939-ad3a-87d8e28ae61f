package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @description 功能表
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName("sma_function_tree")
@Data
public class FunctionTree implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private String id;

    /**
     * code
     */
    private String code;

    /**
     * parent_id
     */
    private String parentId;

    /**
     * view_type
     */
    private Integer viewType;

    /**
     * name
     */
    private String name;

    /**
     * desc
     */
    private String desc;

    /**
     * use_type
     */
    private String useType;

    /**
     * content
     */
    private String content;

    /**
     * icon
     */
    private String icon;

    /**
     * level
     */
    private Integer level;

    /**
     * index
     */
    private Integer index;

    /**
     * type_code
     */
    private String typeCode;

    /**
     * content_url
     */
    private String contentUrl;

    /**
     * 通知标记（1是，0否）
     */
    private Integer noticeFlag;

    /**
     * 通知接口地址
     */
    private String noticeUrl;

    public FunctionTree() {}
}
