package com.ce.scrm.center.dao.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @version 1.0
 * @Description: 区域总监查询分群信息
 * @Author: lijinpeng
 * @Date: 2025/2/27 16:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AreaSegmentQuery implements Serializable {

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 分群id
     */
    private String segmentId;

    /**
     * 省份code
     */
    private String provinceCode;

    /**
     * 市code
     */
    private String cityCode;

    /**
     * 城市筛选项list
     */
    private List<String> cityCodeList;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 客户状态
     */
    private String customerStatus;

    /**
     * 筛选项 0否 1是
     */
    private Integer visitFlag;

    /**
     * 一级国标行业编码
     */
    private List<String> firstIndustryCodeList;

    /**
     * 二级国标行业编码
     */
    private List<String> secondIndustryCodeList;

    /**
     * 三级国标行业编码
     */
    private List<String> thirdIndustryCodeList;

    /**
     * 四级国标行业编码
     */
    private List<String> fourthIndustryCodeList;

    /**
     * 注册资本范围 小
     */
    private BigDecimal registerCapitalMin;

    /**
     * 注册资本范围 大
     */
    private BigDecimal registerCapitalMax;

    /**
     * 模糊查询备注
     */
    private String likeRemark;

    /**
     * 有无域名备案 1:是 0:否
     */
    private Integer icpFlag;

    /**
     * 有无进出口信用 1:是 0:否
     */
    private Integer jingchukouFlag;

}
