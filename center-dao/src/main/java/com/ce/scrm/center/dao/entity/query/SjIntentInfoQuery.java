package com.ce.scrm.center.dao.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 商机信息表条件查询
 * @Author: lijinpeng
 * @Date: 2025/1/16 17:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SjIntentInfoQuery implements Serializable {

    /**
     * in
     */
    private List<String> busOppoCodes;

}
