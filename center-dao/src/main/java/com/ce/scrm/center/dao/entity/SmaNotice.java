package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @description 公告
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName(value ="sma_notice")
@Data
public class SmaNotice implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    private String title;

    /**
     * 
     */
    private String content;

    /**
     * 
     */
    private Date createDate;

    /**
     * 
     */
    private String createBy;

    /**
     * 
     */
    private Date updateDate;

    /**
     * 
     */
    private String updateBy;

    /**
     * 0:普通，1:重要
     */
    private Integer weightGrade;

    /**
     * 
     */
    private String type;

    /**
     * 
     */
    private Date startDate;

    /**
     * 
     */
    private Date endDate;

    /**
     * 
     */
    private String attachIds;

    /**
     * 
     */
    private Integer object;

    /**
     * 是否滚动（0否；1是）
     */
    private Integer isScroll;

    /**
     * 结束时间
     */
    private Date endTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}