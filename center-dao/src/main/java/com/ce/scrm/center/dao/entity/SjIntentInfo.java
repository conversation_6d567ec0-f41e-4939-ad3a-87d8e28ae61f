package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 *
 * @TableName sj_intent_info
 * 商机意向信息实体类
 */
@TableName(value ="sj_intent_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SjIntentInfo implements Serializable {

    /**
     * id
     */
    @TableId
    private String id;

    /** 客户id */
    private String custId;

    /** 客户名称 */
    private String custName;

    /** 商机ID */
    private String sjId;

    /** 商机单号(商机编码) */
    private String sjCode;

    /** 线索ID */
    private String clueId;

    /** 商机创建人ID */
    private String createrId;

    private String createrName;

    /** 商机创建时间 */
    private Date createTime;

    /** 属性值列表 */
    private String traffickey;

    /** 联系人 */
    private String linkmanName;

    /** 性别 */
    private String sex;

    /** 性别label */
    private String sexLabel;

    private String position;

    /** 手机 */
    private String mobile;

    /** 固话 */
    private String tel;

    /** 邮箱 */
    private String mail;

    /** 省code */
    private String provinceCode;

    /** 市code */
    private String cityCode;

    /** 区code */
    private String districtCode;

    /** 详细地址 */
    private String address;

    /** QQ号 */
    private String qq;

    /** 微信号 */
    private String wechat;

    /** 一级行业编码 */
    private String industryOneCode;

    /** 二级行业编码 */
    private String industryTwoCode;

    private String mainbusiness;

    /** 客户需求 */
    private String custrequirement;

    /** 商机备注 */
    private String remark;

    /** 商机状态 */
    private String status;

    /** 是否主商机 */
    private String isMainBusiopp;

    /** 是否新商机 */
    private String isNewBusiopp;

    /** 商务 */
    private String commerce;

    /** 创建人所属部门id */
    private String deptid;

    /** 创建人所属部门名称 */
    private String deptname;

    /** 商务所属分司名称 */
    private String branchofficename;

    /** 商务所属分司ID */
    private String branchofficeid;

    /** 商务所属区域id */
    private String regionid;

    /** 商务所属区域名称 */
    private String regionname;

    /** 是否已回访（是：YES，否：NO） */
    private String isvisited;

    /** 商务id */
    private String commerceid;

    /** 回访内容code */
    private String visitcontentcode;

    /** 回访备注 */
    private String visitremark;

    /** 回访时间 */
    private Date visittime;

    /** 商机标签 */
    private String busiOpporLabel;

    /** 放弃人 */
    private String giveUpBy;

    /** 放弃次数 */
    private Integer giveUpTimes;

    /** 超时次数 */
    private Integer overtimeNum;

    /** 是否继续轮播(0否，1是) */
    private Byte isLoop;

    /** 流转类型(1待分配，2通知) */
    private Byte lzType;

    /** 是否分配成功(0未成功需要重发，1成功) */
    private Integer isAssignSuccess;

    /** 分公司ID */
    private String subId;

    /** 商机意向客户标签，1:新，2:老 */
    private Integer sjCustMark;

    private String thisCreateBy;

    private Date thisCreateTime;

    private String thisUpdateBy;

    private Date thisUpdateTime;

    /** 意向产品 */
    private String intentionalProducts;

    /**
     * 是否计算成本
     */
    private Integer isCost;


    /** 管家id */
    private String gjid;

    /** 创建人所属部门id */
    private String gjDeptid;

    private Integer sjSource;

    /**
     * 签单日期
     */
    private Date signDate;

    /**
     * 高呈商机ID
     */
    private String gcSjId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}