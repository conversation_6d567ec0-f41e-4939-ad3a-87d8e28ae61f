package com.ce.scrm.center.dao.mapper;

import com.ce.scrm.center.dao.entity.CustomerFollow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ce.scrm.center.dao.entity.view.CustomerFollowMoneySummaryView;
import com.ce.scrm.center.dao.entity.view.CustomerFollowStageSummaryView;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @description 客户跟进表 Mapper
 * <AUTHOR>
 * @date 2024-04-19
 */
public interface CustomerFollowMapper extends BaseMapper<CustomerFollow> {

    List<CustomerFollowStageSummaryView> getZqCustomerFollowStageSummary(@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    List<CustomerFollowMoneySummaryView> getZqPerformancePredictSummary(@Param("businessMonthStartDate") Date businessMonthStartDate,
                                                                        @Param("businessMonthEndDate") Date businessMonthEndDate,
                                                                        @Param("naturalMonthStartDate") Date naturalMonthStartDate,
                                                                        @Param("naturalMonthEndDate") Date naturalMonthEndDate);

}




