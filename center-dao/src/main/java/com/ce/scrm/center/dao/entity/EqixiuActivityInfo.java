package com.ce.scrm.center.dao.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 *
 * @TableName eqixiu_activity_info
 */
@Data
public class EqixiuActivityInfo implements Serializable {
    /**
     *
     */
    private String id;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动基础地址
     */
    private String activityUrl;

    /**
     * 1 应用场景 2:分司活动
     */
    private Integer activityType;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 分享标题
     */
    private String shareTitle;

    /**
     * 分享描述
     */
    private String shareDesc;

    /**
     *
     */
    private String sharePic;

    /**
     *
     */
    private String sharePosterInfo;

    /**
     * 是否可以卡片分享 0 不可以 1 可以
     */
    private Integer cardShare;

    /**
     * 是否可以海报分享 0 不可以 1 可以
     */
    private Integer posterShare;

    /**
     * 活动状态 1 未开始 2 进行中 3 已结束
     */
    private Integer activityStatus;

    /**
     * 可见范围 1: 全部 2 部分
     */
    private Integer visibleRange;

    /**
     * 0 可用   1 不可用
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}