package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description 保护关系扩展表
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName(value ="cm_cust_protect_view")
@Data
public class CmCustProtectView implements Serializable {
    /**
     * 客户ID
     */
    @TableId
    private String custId;

    /**
     * 跟进状态
     */
    private Integer visitstatus;

    /**
     * 销售阶段
     */
    private String salesstage;

    /**
     * 
     */
    private BigDecimal expectDealAmount;

    /**
     * 预测商务月
     */
    private String forecastBusinessMonth;

    /**
     * 收藏原因
     */
    private String collectReason;

    /**
     * 保护原因
     */
    private String protectReason;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 系统模板
     */
    private String sysTemplate;

    /**
     * 自定义模板
     */
    private String customTemplate;

    /**
     * 是否跟进（1是；0否）
     */
    private Integer followOrNot;

    /**
     * 是否拜访（1是；0否）
     */
    private Integer visitOrNot;

    /**
     * 拜访有效期
     */
    private Date clueFollowExpirationDate;

    /**
     * 模板下发有效期
     */
    private Date templateIssuedExceedTime;

    /**
     * 模板下发成功日期
     */
    private Date templateIssuedSuccessTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}