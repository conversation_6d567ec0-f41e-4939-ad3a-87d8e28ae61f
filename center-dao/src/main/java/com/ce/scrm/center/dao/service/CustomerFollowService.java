package com.ce.scrm.center.dao.service;

import com.ce.scrm.center.dao.entity.CustomerFollow;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.view.CustomerFollowMoneySummaryView;
import com.ce.scrm.center.dao.entity.view.CustomerFollowStageSummaryView;

import java.util.Date;
import java.util.List;

/**
 * @description 客户跟进 service
 * <AUTHOR>
 * @date 2024-04-19
 */
public interface CustomerFollowService extends IService<CustomerFollow> {

    /**
     * 计算中企跟进记录按照销售阶段客户数汇总表
     * @param startDate
     * @param endDate
     * @return
     */
    List<CustomerFollowStageSummaryView> getZqCustomerFollowStageSummary(Date startDate, Date endDate);

    /**
     * 计算 业绩预测汇总表
     * @param startDate
     * @param endDate
     * @return
     */
    List<CustomerFollowMoneySummaryView> getZqPerformancePredictSummary(Date businessMonthStartDate, Date businessMonthEndDate,
                                                                        Date naturalMonthStartDate, Date naturalMonthEndDate);

}
