package com.ce.scrm.center.dao.service.impl;

import com.ce.scrm.center.dao.entity.CustomerCirculationSpecialSetting;
import com.ce.scrm.center.dao.mapper.CustomerCirculationSpecialSettingMapper;
import com.ce.scrm.center.dao.service.ICustomerCirculationSpecialSettingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 成交客户流转流失特例配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Service
public class CustomerCirculationSpecialSettingServiceImpl extends ServiceImpl<CustomerCirculationSpecialSettingMapper, CustomerCirculationSpecialSetting> implements ICustomerCirculationSpecialSettingService {

}
