package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户leads
 * @TableName customer_leads
 */
@TableName(value ="customer_leads")
@Data
public class CustomerLeads implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据来源 0:crm原有渠道  1:营销活动   2:手动录入
     */
    @TableField(value = "data_from_source")
    private Integer dataFromSource;

    /**
     * 客户id
     */
    @TableField(value = "customer_id")
    private String customerId;

    /**
     * 更多信息
     */
    @TableField(value = "more_info")
    private String moreInfo;

    /**
     * 客户名称
     */
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 省
     */
    @TableField(value = "province_code")
    private String provinceCode;

    /**
     * 市
     */
    @TableField(value = "city_code")
    private String cityCode;
    /**
     * 区
     */
    @TableField(value = "district_code")
    private String districtCode;


    /**
     * 详细地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 线索类型
     */
    @TableField(value = "clue_type")
    private String clueType;

    /**
     * 渠道
     */
    @TableField(value = "channel")
    private String channel;

    /**
     * 活动
     */
    @TableField(value = "activity")
    private String activity;

    /**
     * 端口
     */
    @TableField(value = "client_type")
    private String clientType;

    /**
     * 意向产品
     */
    @TableField(value = "intention_product")
    private String intentionProduct;

    /**
     * 需求备注
     */
    @TableField(value = "demand_remark")
    private String demandRemark;

    /**
     * 附件url a,b,c
     */
    @TableField(value = "attachment_urls")
    private String attachmentUrls;

    /**
     * leads类别
     */
    @TableField(value = "leads_type")
    private String leadsType;

    /**
     * leads code
     */
    @TableField(value = "leads_code")
    private String leadsCode;

    /**
     * leads 来源
     */
    @TableField(value = "leads_source")
    private String leadsSource;

    /**
     * leads url
     */
    @TableField(value = "leads_url")
    private String leadsUrl;

    /**
     * leads来源说明
     */
    @TableField(value = "leads_desc")
    private String leadsDesc;

    /**
     * 联系人
     */
    @TableField(value = "linkman_name")
    private String linkmanName;

    /**
     * 手机号
     */
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 分发时间
     */
    @TableField(value = "dispatch_time")
    private Date dispatchTime;

    /**
     * 创建者
     */
    @TableField(value = "created_id")
    private String createdId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "contact_id")
    private String contactId;

    /**
     * 性别
     */
    @TableField(value = "gender")
    private String gender;

    /**
     * 邮件
     */
    @TableField(value = "email")
    private String email;

    /**
     * 职位
     */
    @TableField(value = "position")
    private String position;

    /**
     * 微信账号
     */
    @TableField(value = "we_chat")
    private String weChat;

    /**
     * 固话
     */
    @TableField(value = "fixed_phone")
    private String fixedPhone;

    /**
     * 线索单号
     */
    @TableField(value = "clue_code")
    private String clueCode;

    /**
     * 处理人部门id
     */
    @TableField(value = "handle_dept_id")
    private String handleDeptId;

    /**
     * 处理人部门名称
     */
    @TableField(value = "handle_dept_name")
    private String handleDeptName;

    /**
     * 最新处理人id
     */
    @TableField(value = "handle_person_id")
    private String handlePersonId;

    /**
     * 最新处理人名称
     */
    @TableField(value = "handle_person_name")
    private String handlePersonName;

    /**
     * 创建人部门id
     */
    @TableField(value = "create_dept_id")
    private String createDeptId;

    /**
     * 创建人部门名称
     */
    @TableField(value = "create_dept_name")
    private String createDeptName;

    /**
     * 处理时间
     */
    @TableField(value = "handle_time")
    private Date handleTime;

    /**
     * qq号
     */
    @TableField(value = "qq")
    private String qq;

    /**
     * 一级行业code
     */
    @TableField(value = "industry_one_code")
    private String industryOneCode;

    /**
     * 二级行业code
     */
    @TableField(value = "industry_two_code")
    private String industryTwoCode;

    /**
     *
     */
    @TableField(value = "main_business")
    private String mainBusiness;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 百度url
     */
    @TableField(value = "baidu_url")
    private String baiduUrl;

    /**
     * 线索类型-子类型
     */
    @TableField(value = "baidu_source")
    private String baiduSource;

    /**
     *
     */
    @TableField(value = "clue_label")
    private String clueLabel;

    /**
     * 线索来源 400的二级渠道
     */
    @TableField(value = "child_400")
    private String child400;

    /**
     * 来源url
     */
    @TableField(value = "source_url")
    private String sourceUrl;

    /**
     * 落地url
     */
    @TableField(value = "land_url")
    private String landUrl;

    /**
     * 活动特征码
     */
    @TableField(value = "promote_slug")
    private String promoteSlug;

    /**
     * 渠道特征码
     */
    @TableField(value = "channel_slug")
    private String channelSlug;

    /**
     * 特征码
     */
    @TableField(value = "slug")
    private String slug;

    /**
     * 表单类型
     */
    @TableField(value = "form_type")
    private String formType;

    /**
     * 300咨询信息
     */
    @TableField(value = "others")
    private String others;

    /**
     * 表单提交url
     */
    @TableField(value = "form_submit_url")
    private String formSubmitUrl;

    /**
     * 二级渠道特征码
     */
    @TableField(value = "channel_child_slug")
    private String channelChildSlug;

    /**
     * 关键词1
     */
    @TableField(value = "keyword1")
    private String keyword1;

    /**
     * 关键词2
     */
    @TableField(value = "keyword2")
    private String keyword2;

    /**
     * 关键词3
     */
    @TableField(value = "utm_term")
    private String utmTerm;

    /**
     * 来源域名
     */
    @TableField(value = "from_page")
    private String fromPage;

    /**
     * 三方推送的线索id
     */
    @TableField(value = "source_data_id")
    private String sourceDataId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}