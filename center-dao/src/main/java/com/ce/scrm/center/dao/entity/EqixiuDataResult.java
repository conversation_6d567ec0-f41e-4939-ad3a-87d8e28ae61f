package com.ce.scrm.center.dao.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 按照    活动 汇总
 *
 * @TableName eqixiu_data_result
 */
@Data
public class EqixiuDataResult implements Serializable {
    /**
     *
     */
    private String id;

    /**
     *
     */
    private String activityId;

    /**
     *
     */
    private String activityName;

    /**
     * 活动链接
     */
    private String activityUrl;

    /**
     *
     */
    private Integer activityType;

    /**
     *
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     *
     */
    private String custType;

    /**
     * 分享商务ID
     */
    private String salerId;

    /**
     * 分享的商务名称
     */
    private String salerName;

    /**
     *
     */
    private String salerDeptId;

    /**
     *
     */
    private String salerDeptName;

    /**
     *
     */
    private String salerSubId;

    /**
     *
     */
    private String salerSubName;

    /**
     *
     */
    private String salerAreaId;

    /**
     *
     */
    private String salerAreaName;

    /**
     *
     */
    private String salerBuId;

    /**
     *
     */
    private String salerBuName;

    /**
     * 客户保护商务
     */
    private String protectSalerId;

    /**
     *
     */
    private String protectSalerName;

    /**
     * 客户保护商务所在的地区ID
     */
    private String protectAreaId;

    /**
     * 客户保护商务所在的地区
     */
    private String protectArea;

    /**
     * 客户保护商务所在的分公司ID
     */
    private String protectSubId;

    /**
     * 客户保护商务所在的分公司名称
     */
    private String protectSubName;

    /**
     * 客户保护商务所在的部门ID
     */
    private String protectDeptId;

    /**
     * 客户保护商务所在的部门
     */
    private String protectDeptName;

    /**
     * 客户保护商务所在的事业部
     */
    private String protectBuId;

    /**
     * 客户保护商务所在的事业部
     */
    private String protectBuName;

    /**
     *
     */
    private String contactId;

    /**
     *
     */
    private String contactName;

    /**
     *
     */
    private String contactPhone;

    /**
     * 分享链接
     */
    private String shareLink;

    /**
     * 1海报、2复制链接、3企微好友分享、4微信好友分享
     */
    private String shareType;

    /**
     * 1PC、2移动
     */
    private String operatingTerminal;

    /**
     * 源数据表ID
     */
    private String triggerId;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 最近打开时间
     */
    private Date recentOpenTime;

    /**
     * 最近抽奖时间
     */
    private Date recentLotteryTime;

    /**
     * 最近抽奖时间
     */
    private Date recentShareTime;

    /**
     * 中奖次数
     */
    private Integer isWin;

    /**
     * 中奖信息
     */
    private String winInfo;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;


}