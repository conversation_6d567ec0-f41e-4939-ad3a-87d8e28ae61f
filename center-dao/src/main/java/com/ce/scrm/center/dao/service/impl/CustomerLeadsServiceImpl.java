package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.CustomerLeads;
import com.ce.scrm.center.dao.service.CustomerLeadsService;
import com.ce.scrm.center.dao.mapper.CustomerLeadsMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer_leads(客户leads)】的数据库操作Service实现
* @createDate 2025-08-07 10:31:04
*/
@Service
public class CustomerLeadsServiceImpl extends ServiceImpl<CustomerLeadsMapper, CustomerLeads>
    implements CustomerLeadsService{

}




