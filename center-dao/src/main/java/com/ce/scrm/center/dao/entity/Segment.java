package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 分群信息表
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
public class Segment implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 分群ID
     */
    private String segmentId;

    /**
     * 分群总量
     */
    private Integer segmentCount;

    /**
     * 分群实际下发总量
     */
    private Integer segmentDistributeCount;

    /**
     * 分群名称
     */
    private String segmentName;

    /**
     * 分群描述
     */
    private String segmentDesc;

    /**
     * 分群开始时间
     */
    private Date segmentBeginTime;

    /**
     * 分群结束时间
     */
    private Date segmentEndTime;

    /**
     * 分群状态 0:未下发 1:下发中 2:已下发
     */
    private Integer segmentStatus;

    /**
     * 删除状态 0:未删除 1:已删除
     */
    private Integer deleteFlag;

    /**
     * 创建人ID
     */
    private String createId;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人ID
     */
    private String updateId;

    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateTime;

	/**
	 * 是否全员可见
	 */
    private Integer isVisibleAll;

    public Segment() {}
}
