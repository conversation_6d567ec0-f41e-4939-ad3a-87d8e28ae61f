package com.ce.scrm.center.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ce.scrm.center.dao.entity.PotentialCustomerMarketingRules;
import com.ce.scrm.center.dao.entity.query.PotentialWillingBehaviorSelectedVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【potential_customer_marketing_rules(潜客营销规则配置表)】的数据库操作Mapper
* @createDate 2025-08-04 18:18:52
* @Entity com.ce.scrm.center.dao.entity.PotentialCustomerMarketingRules
*/
public interface PotentialCustomerMarketingRulesMapper extends BaseMapper<PotentialCustomerMarketingRules> {

	/**
	 * 获取意愿行为的下拉框
	 * @return 下拉框的key和value
	 */
	List<PotentialWillingBehaviorSelectedVo> getWillingBehaviorSelected();

}
