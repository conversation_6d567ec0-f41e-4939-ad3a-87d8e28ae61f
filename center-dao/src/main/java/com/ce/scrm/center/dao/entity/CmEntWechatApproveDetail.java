package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2024/10/21 16:17
 */
@TableName(value ="cm_ent_wechat_approve_detail")
@Data
@Builder
public class CmEntWechatApproveDetail implements Serializable {

    @TableId
    private Integer id;

    /**
     * 审批的唯一id
     */
    private Long spNo;

    /**
     * 审批名
     */
    private String spName;

    /**
     * 审批发起人的userId
     */
    private String applyerUserId;

    /**
     * 模版id
     */
    private String templateId;

    /**
     * 审批申请状态变化类型：1-提单；2-同意；3-驳回；4-转审；5-催办；6-撤销；8-通过后撤销；10-添加备注；
     * 11-回退给指定审批人；12-添加审批人；13-加签并同意； 14-已办理； 15-已转交
     */
    private Integer statuChangeEvent;

    /**
     * 审批申请提交时间,Unix时间戳
     */
    private Date applyTime;

    /**
     * 申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
     */
    private Integer spStatus;

    /**
     * 审批流程详情
     */
    private String spRecord;

    /**
     * 消息发送时间
     */
    private Date createTime;

    /**
     * 事件名称：open_approval_change
     */
    private String event;

    /**
     * 接收方企业Corpid
     */
    private String toUserName;

    /**
     * 发送方：企业微信
     */
    private String fromUserName;

    /**
     * 消息类型
     */
    private String msgType;

    /**
     * 企业应用的id，整型。可在应用的设置页面查看
     */
    private Integer agentId;

}
