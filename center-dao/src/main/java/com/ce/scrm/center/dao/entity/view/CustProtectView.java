package com.ce.scrm.center.dao.entity.view;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
* @Description: 保护关系表
* @Author: lijinpeng
* @Date: 2024/12/11 15:22
* @version 1.0
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CustProtectView extends CmCustProtect implements Serializable {

}
