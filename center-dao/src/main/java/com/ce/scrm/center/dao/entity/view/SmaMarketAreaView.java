package com.ce.scrm.center.dao.entity.view;

import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 市场和区域映射表
 * @Author: lijinpeng
 * @Date: 2024/12/10 09:30
 */
@Data
public class SmaMarketAreaView implements Serializable {

    private Integer id;
    /**
     * 市场id
     */
    private String marketId;
    /**
     * 省code
     */
    private String provinceCode;
    /**
     * 市code
     */
    private String cityCode;
    /**
     *  区code
     */
    private String areaCode;
    /**
     * 省
     */
    private String provinceName;
    /**
     * 市
     */
    private String cityName;
    /**
     * 区
     */
    private String areaName;

//    /**
//     * 市场名称（实体类冗余字段，用于接受查询结果）
//     */
//    private String marketName;
//    /**
//     * 分司ID（实体类冗余字段，用于接受查询结果）
//     */
//    private String subId;

}
