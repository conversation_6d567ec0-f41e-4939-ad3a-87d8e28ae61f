package com.ce.scrm.center.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 我的保护列表查询条件拼装
 * <AUTHOR>
 * @Version 1.0.0
 * @Date 2025-01-03 16:11
 */
@Data
public class CmCustProtectQuery implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 页码
	 */
	private Integer pageNum = 1;
	/**
	 * 每页数量
	 */
	private Integer pageSize = 10;

	private String custId;
	private String custName;
	private String salerId;
	private String bussdeptId;
	private String buId;
	private String subcompanyId;
	private String areaId;
	private List<Integer> custTypes;
	private Integer custType;
	private Integer isSj;
	private Integer isIntentJudge;
	private Integer intentionalityType;
	private LocalDateTime lastVisitTime;
	private Integer visitStatus;
	private String salesStage;
	/**
	 * 销售阶段多选筛选项
	 */
	private List<String> salesStageList;
	private String forecastBusinessMonth;
	private String noForecastBusinessMonth;
	private String collectReason;
	private String protectReason;
	private String startProtectDate;
	private String endProtectDate;
	private String sjType;
	private String isTurnGc;
	private String custSourceSub;
	private List<Integer> custSources;
	private Integer isOccupy;
	private Integer isClock;
	private String clockProvince;
	private String clockCity;
	private String clockRegion;
	private String regProvince;
	private String regCity;
	private String regRegion;
	private List<String> flag7s;
	private List<String> flag8s;
	private String customTags;
	/**
	 * 绑定客户标识 0未绑定 1绑定
	 */
	private Integer bindFlag;

	/**
	 * 对应前端【类型】1未保护确认的商机、2保护确认的商机、3所有商机
	 */
	private Integer queryType;

}
