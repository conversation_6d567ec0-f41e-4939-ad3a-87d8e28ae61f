package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 分司商机自动分配规则表
 *
 * @TableName sub_sj_auto_assign_rule
 */
@TableName(value = "sub_sj_auto_assign_rule")
@Data
public class SubSjAutoAssignRule {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 是否开启自动分配 0:否 1:是
     */
    private Integer status;

    /**
     * 权重，每分配一次+1
     */
    private Integer weight;

    /**
     * 创建者
     */
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updatedId;

    /**
     * 更新时间
     */
    private Date updatedTime;
}