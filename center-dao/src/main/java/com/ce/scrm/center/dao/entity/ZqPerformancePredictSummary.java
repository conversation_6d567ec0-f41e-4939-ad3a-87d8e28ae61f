package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "zq_performance_predict_summary")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZqPerformancePredictSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 商务月 yyyy-MM
     */
    private String businessMonth;

    /**
     * 机构类型 0:全国，1:区域，2:分司）
     */
    private Integer orgType;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 当月预估业绩缺口
     */
    private BigDecimal performanceGapMonth;

    /**
     * 当月业绩目标
     */
    private BigDecimal performanceTargetMonth;

    /**
     * 当月预估签单金额
     */
    private BigDecimal signedAmtPredictMonth;

    /**
     * 当月预估签单客户数
     */
    private Integer signedCustomerCountPredictMonth;

    /**
     * 当月预估到账金额
     */
    private BigDecimal receivedAmtPredictMonth;

    /**
     * 当月预估到账客户数
     */
    private Integer receivedCustomerCountPredictMonth;

    /**
     * 下月预估签单金额
     */
    private BigDecimal signedAmtPredictNextMonth;

    /**
     * 下月预估到账金额
     */
    private BigDecimal receivedAmtPredictNextMonth;

    /**
     * 当月累计业绩
     */
    private BigDecimal performanceMonth;

    /**
     * 是否有效 1:有效 0:无效
     */
    private Integer flag;

    /**
     * 创建时间
     */
    private Date dbInsertTime;

    /**
     * 更新时间
     */
    private Date dbUpdateTime;

}
