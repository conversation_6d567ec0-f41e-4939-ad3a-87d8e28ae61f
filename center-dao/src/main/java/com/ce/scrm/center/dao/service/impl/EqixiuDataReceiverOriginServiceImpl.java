package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.EqixiuDataReceiverOrigin;
import com.ce.scrm.center.dao.mapper.EqixiuDataReceiverOriginMapper;
import com.ce.scrm.center.dao.service.IEqixiuDataReceiverOriginService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【eqixiu_data_receiver_origin(数据接收的原始消息,当进行insert 的时候触发cdp 上报通知)】的数据库操作Service实现
* @createDate 2024-11-28 18:42:07
*/
@Service
public class EqixiuDataReceiverOriginServiceImpl extends ServiceImpl<EqixiuDataReceiverOriginMapper, EqixiuDataReceiverOrigin>
    implements IEqixiuDataReceiverOriginService {

}




