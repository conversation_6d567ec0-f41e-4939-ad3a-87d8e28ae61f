package com.ce.scrm.center.dao.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.SmaMarket;
import com.ce.scrm.center.dao.entity.query.SmaMarketQuery;
import com.ce.scrm.center.dao.entity.view.SmaMarketView;
import com.ce.scrm.center.dao.mapper.SmaMarketMapper;
import com.ce.scrm.center.dao.service.SmaMarketService;
import com.ce.scrm.center.dao.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @version 1.0
 * @Description: 市场表
 * @Author: lijinpeng
 * @Date: 2024/12/10 09:54
 */
@Service
@Slf4j
public class SmaMarketServiceImpl extends ServiceImpl<SmaMarketMapper, SmaMarket>
        implements SmaMarketService {

    @Override
    public SmaMarketView selectOneByCondition(SmaMarketQuery condition) {
        List<SmaMarketView> marketList = selectByCondition(condition);
        if(CollectionUtils.isEmpty(marketList)) {
            return null;
        }else {
            return marketList.get(0);
        }
    }

    @Override
    public List<SmaMarketView> selectByCondition(SmaMarketQuery condition) {
        log.info("根据条件查询市场表,参数condition={}", JSON.toJSONString(condition));
        if(condition == null) {
            return Collections.emptyList();
        }
        LambdaQueryChainWrapper<SmaMarket> lambdaQueryChainWrapper = this.lambdaQuery();
        if(condition.getId() != null) {
            lambdaQueryChainWrapper.eq(SmaMarket::getId, condition.getId());
        }

        List<SmaMarket> smaMarketList = lambdaQueryChainWrapper.list();
        List<SmaMarketView> result = BeanCopyUtils.convertToVoList(smaMarketList, SmaMarketView.class);
//        log.info("根据条件查询市场表,结果为result={}", JSON.toJSONString(result));
        return result;
    }

}
