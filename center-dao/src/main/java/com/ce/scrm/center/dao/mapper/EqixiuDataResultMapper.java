package com.ce.scrm.center.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.EqixiuDataResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ce.scrm.center.dao.entity.EqixiuDataResultAnalyze;
import com.ce.scrm.center.dao.entity.EqixiuDataResultQuery;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【eqixiu_data_result(按照    活动 汇总 )】的数据库操作Mapper
 * @createDate 2024-11-28 18:42:08
 * @Entity com.ce.scrm.center.dao.entity.EqixiuDataResult
 */
public interface EqixiuDataResultMapper extends BaseMapper<EqixiuDataResult> {

    Page<EqixiuDataResultAnalyze> selectActivityList(Page<?> page, @Param("query") EqixiuDataResultQuery eqixiuDataResultQuery);

    Page<EqixiuDataResultAnalyze> getActivityStatistics(Page<?> page, @Param("query") EqixiuDataResultQuery eqixiuDataResultQuery);

}




