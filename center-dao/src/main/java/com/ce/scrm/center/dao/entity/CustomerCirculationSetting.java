package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 成交客户流转配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@TableName(value = "customer_circulation_setting")
@Data
public class CustomerCirculationSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分公司ID
     */
    private String subId;

    /**
     * 分公司名称
     */
    private String subName;

    /**
     * 未打卡自然月数
     */
    private Integer monthWithoutClock;

    /**
     * 流转方式：1总监待分 2经理待分
     */
    private Integer circulationType;

    /**
     * 创建用户id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改用户id
     */
    private String updateUserId;

    /**
     * 修改时间
     */
    private Date updateTime;

}
