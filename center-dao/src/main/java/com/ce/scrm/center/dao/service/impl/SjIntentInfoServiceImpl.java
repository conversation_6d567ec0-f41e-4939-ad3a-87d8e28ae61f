package com.ce.scrm.center.dao.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.SjIntentInfo;
import com.ce.scrm.center.dao.entity.query.SjIntentInfoQuery;
import com.ce.scrm.center.dao.entity.view.SjIntentInfoView;
import com.ce.scrm.center.dao.service.SjIntentInfoService;
import com.ce.scrm.center.dao.mapper.SjIntentInfoMapper;
import com.ce.scrm.center.dao.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sj_intent_info】的数据库操作Service实现
* @createDate 2025-01-16 17:13:26
*/
@Service
@Slf4j
public class SjIntentInfoServiceImpl extends ServiceImpl<SjIntentInfoMapper, SjIntentInfo>
    implements SjIntentInfoService{

    @Resource
    private SjIntentInfoService sjIntentInfoService;

    @Override
    public List<SjIntentInfoView> selectByCondition(SjIntentInfoQuery condition) {
        log.info("根据条件查询sj_intent_info表,参数condition={}", JSON.toJSONString(condition));
        if(condition == null) {
            return Collections.emptyList();
        }
        LambdaQueryChainWrapper<SjIntentInfo> lambdaQueryChainWrapper = sjIntentInfoService.lambdaQuery();
        if (CollectionUtils.isNotEmpty(condition.getBusOppoCodes())) {
            lambdaQueryChainWrapper.in(SjIntentInfo::getSjCode, condition.getBusOppoCodes());
        }
        List<SjIntentInfo> sjIntentInfoList = lambdaQueryChainWrapper.list();
        return BeanCopyUtils.convertToVoList(sjIntentInfoList, SjIntentInfoView.class);
    }

}




