package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 客户分层（商务）变化记录
 * <AUTHOR>
 * @date 2025-05-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerLayerChangeLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 原来客户分层（商务）vip-1;一般-2;低价值-3;高价值-4
     */
    private Integer oldLayer;

    /**
     * 新的客户分层（商务）vip-1;一般-2;低价值-3;高价值-4
     */
    private Integer newLayer;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
