package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName sma_role_relation
 */
@TableName(value ="sma_role_relation")
@Data
public class SmaRoleRelation implements Serializable {
    /**
     * 
     */
    @TableId
    private String id;

    /**
     * 
     */
    private Integer relationType;

    /**
     * 
     */
    private String roleId;

    /**
     * 
     */
    private String relationId;

    /**
     * 是否默认角色
     */
    private Integer isDefault;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}