package com.ce.scrm.center.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ce.scrm.center.dao.entity.CustomsTradeSummary;
import com.ce.scrm.center.dao.entity.view.CustomsTradeSummaryView;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * <AUTHOR>
 * @description 海关统计数据Mapper
 * @date 2025-05-20
 */
@Mapper
public interface CustomsTradeSummaryMapper extends BaseMapper<CustomsTradeSummary> {


    @Select({
            "<script>",
            "SELECT product_code, product_name, partner_code, partner_name, total_sum ",
            "FROM (",
            "   SELECT ",
            "       product_code, product_name, partner_code, partner_name, total_sum, ",
            "       @row_num := IF(@current_product = product_code, @row_num + 1, 1) AS row_num, ",
            "       @current_product := product_code ",
            "   FROM (",
            "       SELECT ",
            "           product_code, product_name, partner_code, partner_name, ",
            "           SUM(total) AS total_sum ",
            "       FROM customs_trade_summary  ",
            "       <where>",
            "         year IN ",
            "           <foreach item='code' collection='yearnRange' open='(' separator=',' close=')'>",
            "               #{code}",
            "           </foreach>",
            "           AND product_code IN ",
            "           <foreach item='code' collection='productCodes' open='(' separator=',' close=')'>",
            "               #{code}",
            "           </foreach>",
            "       </where>",
            "       GROUP BY product_code, product_name, partner_code, partner_name ",
            "       ORDER BY product_code, total_sum DESC ",
            "   ) AS aggregated_data, ",
            "   (SELECT @row_num := 0, @current_product := '') AS vars ",
            ") AS ranked_data ",
            "WHERE row_num &lt;= 5",
            "</script>"
    })
    List<CustomsTradeSummaryView> selectTop5ByProductCodes(@Param("productCodes") List<String> productCodes, @Param("yearnRange")  List<Integer> yearnRange);
}
