package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.SmaDictionaryItem;
import com.ce.scrm.center.dao.entity.query.SmaDictionaryItemQuery;
import com.ce.scrm.center.dao.entity.view.SmaDictionaryItemNewView;
import com.ce.scrm.center.dao.mapper.SmaDictionaryItemMapper;
import com.ce.scrm.center.dao.service.SmaDictionaryItemService;
import com.ce.scrm.center.dao.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @description 字典 service
 * <AUTHOR>
 * @date 2024-04-19
 */
@Service
@Slf4j
public class SmaDictionaryItemServiceImpl extends ServiceImpl<SmaDictionaryItemMapper, SmaDictionaryItem> implements SmaDictionaryItemService {

    @Resource
    private SmaDictionaryItemService smaDictionaryItemService;

    @Override
    public List<SmaDictionaryItemNewView> selectByCondition(SmaDictionaryItemQuery condition) {

        log.info("SmaDictionaryItemServiceImpl.selectByCondition condition:{}", condition);

        if(condition == null) {
            return Collections.emptyList();
        }

        LambdaQueryChainWrapper<SmaDictionaryItem> lambdaQueryChainWrapper = smaDictionaryItemService.lambdaQuery();

        if(CollectionUtils.isNotEmpty(condition.getIdList())) {
            lambdaQueryChainWrapper.in(SmaDictionaryItem::getId, condition.getIdList());
        }
        lambdaQueryChainWrapper.eq(StringUtils.isNotBlank(condition.getDictionaryId()),SmaDictionaryItem::getDictionaryId, condition.getDictionaryId());

        List<SmaDictionaryItem> result = lambdaQueryChainWrapper.list();
        return BeanCopyUtils.convertToVoList(result, SmaDictionaryItemNewView.class);
    }

}
