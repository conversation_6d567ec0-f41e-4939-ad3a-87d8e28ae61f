package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.query.CmCustProtectNewQuery;
import com.ce.scrm.center.dao.entity.CmCustProtectQuery;
import com.ce.scrm.center.dao.entity.view.CustProtectView;

import java.util.List;

/**
* <AUTHOR>
 * 更新方法只能有两个：updateByCustId updateBatchByCustIdList
 * 插入方法只能有一个：saveData
* @description 针对表【cm_cust_protect(客户保护关系表)】的数据库操作Service
* @createDate 2024-06-07 17:29:37
*/
public interface CmCustProtectService extends IService<CmCustProtect> {

	/**
	 * 保存保护关系统一入口
	 * @param cmCustProtect
	 * @return
	 */
	Boolean saveData(CmCustProtect cmCustProtect);

    /*
     * @Description 根据custId更新保护关系信息
     * <AUTHOR>
     * @date 2024/11/19 11:47
     * @param cmCustProtect
     * @return java.lang.Integer
     */
    Integer updateByCustId(CmCustProtect cmCustProtect);

	/**
	 * @description: 根据custId更新，可以更新空值，所以要注意【先查在更】确保查询全量字段 ！！！
	 * @author: lijinpeng
	 * @date: 2025/7/16 10:47
	 * @param: [cmCustProtect]
	 * @return: java.lang.Integer
	 **/
    Integer updateNullableByCustId(CmCustProtect cmCustProtect);

    /*
     * @Description 根据条件查询保护关系  一条数据
     * <AUTHOR>
     * @date 2024/12/11 15:13
     * @param condition
     * @return com.ce.scrm.center.dao.entity.CmCustProtectView
     */
    CustProtectView selectOneByCondition(CmCustProtect condition);

    /*
     * @Description 根据条件查询保护关系  一个集合
     * <AUTHOR>
     * @date 2024/12/11 15:13
     * @param condition
     * @return java.util.List<com.ce.scrm.center.dao.entity.CmCustProtectView>
     */
    List<CustProtectView> selectByCondition(CmCustProtect condition);

    /*
     * @Description 根据条件分页查询保护关系
     * <AUTHOR>
     * @date 2025/1/16 11:16
     * @param condition
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.center.dao.entity.view.CustProtectView>
     */
    Page<CustProtectView> selectPageByCondition(CmCustProtectNewQuery condition);

    /*
     * @Description 根据custId 批量更新 不为空的字段
     * <AUTHOR>
     * @date 2025/1/6 15:26
     * @param custIdList
     * @param cmCustProtect
     * @return java.lang.Integer
     */
    Integer updateBatchByCustIdList(List<String> custIdList, CmCustProtect cmCustProtect);

	/*
	 * @Description 根据条件查询保护关系分页，可根据条件查询保护阶段
	 * <AUTHOR>
	 * @date 2025/01/03 14:04
	 * @param cmCustProtectQuery
	 * @return java.util.List<com.ce.scrm.center.dao.entity.CmCustProtectView>
	 */
	Page<CustProtectView> selectCmCustProtectPage(CmCustProtectQuery cmCustProtectQuery);

	/**
	 * 根据客户id集合查询保护关系
	 * @param customerList 客户id集合
	 * @return 保护关系列表
	 */
	List<CustProtectView> selectByCustIds(List<String> customerList);


}
