package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 手机号查客户日志
 * @Author: lijinpeng
 * @Date: 2024/12/25 17:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value ="sma_get_customer_by_phone_log")
public class GetCustomerByPhoneLog implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;
    private String empId;
    private String empName;
    private String phone;

}
