package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 参展历史
 * @TableName tradeshow_history
 */
@TableName(value ="tradeshow_history")
@Data
public class TradeshowHistory {
    /**
     * 自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * CRM客户ID
     */
    private String customerId;

    /**
     * 展会名称
     */
    private String tradeshowName;

    /**
     * 展会年月（格式：YYYY-MM）
     */
    private String tradeshowTime;

    /**
     * CDP对应属性字段
     */
    private String cdpTagName;

    /**
     * 创建时间
     */
    private Date createTime;

}