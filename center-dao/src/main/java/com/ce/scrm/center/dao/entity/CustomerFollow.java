package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @description 客户跟进表
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName(value ="customer_follow")
@Data
public class CustomerFollow implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名
     */
    private String customerName;

    /**
     * 员工id
     */
    private String empId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 事业部id
     */
    private String buId;

    /**
     * 分公司id
     */
    private String subId;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 同步经理标识 0未同步；1已同步
     */
    private Integer syncManagerFlag;

    /**
     * 意向等级 0无；1弱；2中；3强
     */
    private Integer intentionLevel;

    /**
     * 是否vip 0否；1是
     */
    private Integer vipFlag;

    /**
     * 预计成交金额
     */
    private BigDecimal expectDealAmount;

    /**
     * 下次跟进日期
     */
    private Date nextFollowTime;

    /**
     * 网址
     */
    private String websiteUrl;

    /**
     * 网站情况
     */
    private String websiteState;

    /**
     * 通讯地址
     */
    private String address;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String region;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 
     */
    private String content;

    /**
     * 删除标记 0未删除；1商务删除 2经理删除 3总监删除  二进制位运算
     */
    private Integer deleteFlag;

    /**
     * 商务状态 0正常提交 1已删除 2已离职
     */
    private Integer empStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 预计签单日期
     */
    private Date predictSignDate;

    /**
     * 预计签单金额
     */
    private Integer predictSignMoney;

    /**
     * 预计到账日期
     */
    private Date predictAccountDate;

    /**
     * 预计到账金额
     */
    private Integer predictAccountMoney;

    /**
     * 丢单原因 code
     */
    private String loseOrderReason;

    /**
     * 销售阶段
     */
    private String salesStage;

    /**
     * 跟进时间
     */
    private Date followTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}