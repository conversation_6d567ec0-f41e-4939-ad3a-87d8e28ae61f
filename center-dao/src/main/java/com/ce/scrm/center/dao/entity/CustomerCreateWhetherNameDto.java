package com.ce.scrm.center.dao.entity;

import cn.ce.cesupport.enums.CustomerPresentStageEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 历史线索导入到customer_leads || 53客服等来源渠道 进入customer_leads 需要传的对象
 * <AUTHOR>
 * @Date 2025-08-09 15:19
 */
@Data
public class CustomerCreateWhetherNameDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 客户id
	 */
	private String customerId;

	/**
	 * 客户名称
	 */
	private String customerName;

	/**
	 * 电话
	 */
	private String phone;

	/**
	 * 客户当前阶段
	 */
	private CustomerPresentStageEnum presentStage;
}
