package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 
 * @TableName gc_business_opportunity_log
 */
@TableName(value ="gc_business_opportunity_log")
@Data
public class GcBusinessOpportunityLog implements Serializable {
    /**
     * 日志ID（主键）
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商机ID
     */
    private Long sjId;

    /**
     * 操作类型：GcSjOperateLogTypeEnum
     */
    private Integer operateType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人ID
     */
    private String operator;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}