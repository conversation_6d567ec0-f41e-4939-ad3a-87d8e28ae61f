package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.TradeshowHistory;
import com.ce.scrm.center.dao.service.TradeshowHistoryService;
import com.ce.scrm.center.dao.mapper.TradeshowHistoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【tradeshow_history(参展历史)】的数据库操作Service实现
* @createDate 2025-04-28 15:52:50
*/
@Service
@Slf4j
public class TradeshowHistoryServiceImpl extends ServiceImpl<TradeshowHistoryMapper, TradeshowHistory>
    implements TradeshowHistoryService{

}




