package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.SmaMarketSubcompany;
import com.ce.scrm.center.dao.entity.query.SmaMarketSubcompanyQuery;
import com.ce.scrm.center.dao.entity.view.SmaMarketSubcompanyView;

import java.util.List;

/**
 * @version 1.0
 * @Description: 市场分司映射表
 * @Author: lijinpeng
 * @Date: 2024/12/11 10:33
 */
public interface SmaMarketSubcompanyService extends IService<SmaMarketSubcompany> {

    /*
     * @Description 根据条件查询市场分司映射表 一条数据
     * <AUTHOR>
     * @date 2024/12/11 10:47
     * @param condition
     * @return com.ce.scrm.center.dao.entity.vo.SmaMarketSubcompanyView
     */
    SmaMarketSubcompanyView selectOneByCondition(SmaMarketSubcompanyQuery condition);

    /*
     * @Description 根据条件查询市场分司映射表 一个结果集
     * <AUTHOR>
     * @date 2024/12/10 15:26
     * @param condition
     * @return java.util.List<com.ce.scrm.center.dao.entity.vo.SmaMarketView>
     */
    List<SmaMarketSubcompanyView> selectByCondition(SmaMarketSubcompanyQuery condition);

}
