package com.ce.scrm.center.dao.service;

import com.ce.scrm.center.dao.entity.SjIntentInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.query.SjIntentInfoQuery;
import com.ce.scrm.center.dao.entity.view.SjIntentInfoView;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sj_intent_info】的数据库操作Service
* @createDate 2025-01-16 17:13:26
*/
public interface SjIntentInfoService extends IService<SjIntentInfo> {

    List<SjIntentInfoView> selectByCondition(SjIntentInfoQuery condition);

}
