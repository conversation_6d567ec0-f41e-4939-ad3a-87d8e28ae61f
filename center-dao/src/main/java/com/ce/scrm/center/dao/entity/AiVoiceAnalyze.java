package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * ai语音分析表对应的实体类
 * </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("ai_voice_analyze")
public class AiVoiceAnalyze implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 员工 ID
     */
    private String employeeId;

    private String employeeName;

    private Long groupId;

    /**
     * 是否是主 0 否 1 是
     */
    private Integer groupType;

    /**
     * 子节点顺序
     */
    private Integer groupOrder;

    /**
     * 客户 cid
     */
    private String custId;

    private String custName;

    private String subId;

    private String subName;

    private String buId;

    private String buName;

    private String orgId;

    private String areaId;

    private String areaName;

    private String mergeUrl;

    private String orgName;

    /**
     * 原始的录音 url
     */
    private String originUrl;

    /**
     * md5值
     */
    private String fileSign;

    /**
     * 分析类型
     * 0:普通分析
     * 1.跟进记录 + 普通分析
     */
    private String voiceType;

    /**
     * 1 语音识别中 2 识别成功 3 识别失败
     */
    private Integer voiceStatus;

    /**
     * 0 未开始   1 进行中 2 ai 分析完成
     */
    private Integer analyzeStatus;

    /**
     * 请求的特殊信息，透传到对应的业务
     */
    private String extralInfo;

    /**
     * 请求信息
     */
    private String reqInfo;
    private String platform;

    /**
     * 响应结果
     */
    private String respInfo;

    /**
     * 返回结果提炼信息
     */
    private String respFormat;

    /**
     * 分析结果
     */
    private String chatResponse;

    private String businessChatResponse;

    /**
     * 关联 ID
     */
    private Long parentId;

    /**
     * 是否是首次分析 0 是 1 重新分析
     */
    private Integer analyzeType;

    private Date createTime;

    private Date updateTime;
}
