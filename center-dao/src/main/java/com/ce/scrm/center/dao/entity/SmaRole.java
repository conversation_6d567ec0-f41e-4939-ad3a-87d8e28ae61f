package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @description 角色
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName(value ="sma_role")
@Data
public class SmaRole implements Serializable {
    /**
     * 
     */
    @TableId
    private String id;

    /**
     * 
     */
    private String name;

    /**
     * 
     */
    private String roleGroupId;

    /**
     * 
     */
    private String describes;

    /**
     * 
     */
    private Integer orders;

    /**
     * 
     */
    private Integer state;

    /**
     * 是否可切换
     */
    private Integer isChange;

    /**
     * 
     */
    private String deptid;

    /**
     * 
     */
    private String subid;

    /**
     * 是否总部报表权限
     */
    private Integer isZbReport;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}