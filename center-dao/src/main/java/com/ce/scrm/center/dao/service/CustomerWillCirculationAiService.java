package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.CustomerWillCirculation;
import com.ce.scrm.center.dao.entity.CustomerWillCirculationAi;

import java.util.Date;
import java.util.List;

/**
 * Description: 即将流转（流失）客户表
 *
 * @author: JiuDD
 * date: 2024/7/19
 */
public interface CustomerWillCirculationAiService extends IService<CustomerWillCirculationAi> {

}
