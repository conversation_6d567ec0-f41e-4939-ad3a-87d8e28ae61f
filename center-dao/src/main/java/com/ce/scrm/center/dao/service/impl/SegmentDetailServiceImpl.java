package com.ce.scrm.center.dao.service.impl;

import cn.ce.cesupport.enums.YesOrNoEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.SegmentDetail;
import com.ce.scrm.center.dao.entity.query.AreaSegmentQuery;
import com.ce.scrm.center.dao.mapper.SegmentDetailMapper;
import com.ce.scrm.center.dao.service.SegmentDetailService;
import com.ce.scrm.center.dao.service.SegmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/2/27 11:51
 */
@Slf4j
@Service
public class SegmentDetailServiceImpl extends ServiceImpl<SegmentDetailMapper, SegmentDetail> implements SegmentDetailService {

    @Resource
    private SegmentDetailMapper segmentDetailMapper;

    @Resource
    private SegmentService segmentService;

    @Override
    public Page<SegmentDetail> getSegmentPageByAreaId(Page<SegmentDetail> page, AreaSegmentQuery areaSegmentQuery) {
        return segmentDetailMapper.getSegmentPageByAreaId(page, areaSegmentQuery);
    }

    @Override
    public boolean setLastVisitTime(Set<String> validSegmentIds, String customerId, String salerId, Date lastVisitTime) {
        List<SegmentDetail> segmentDetails = this.list(new LambdaQueryWrapper<SegmentDetail>()
                .eq(SegmentDetail::getCustomerId, customerId)
                .eq(SegmentDetail::getSalerId, salerId)
                .isNull(SegmentDetail::getVisitLastTime)
                .in(SegmentDetail::getSegmentId, validSegmentIds));

        if (Objects.isNull(segmentDetails) || segmentDetails.isEmpty()) {
            log.warn("无有效的分群明细信息，customerId={}， salerId={}， lastVisitTime={}", customerId, salerId, lastVisitTime);
            return false;
        }

        Set<Long> segmentDetailIds = segmentDetails.stream()
                .filter(segmentDetail -> segmentDetail.getSalerId().equals(salerId))
                .map(SegmentDetail::getId)
                .collect(Collectors.toSet());

        return this.update(new LambdaUpdateWrapper<SegmentDetail>()
                .in(SegmentDetail::getId, segmentDetailIds)
                .set(SegmentDetail::getVisitLastTime, lastVisitTime));
    }

    @Override
    @Transactional
    public boolean setProtectTime(Set<String> validSegmentIds, String customerId, String salerId, Date protectTime) {
        List<SegmentDetail> segmentDetails = this.list(new LambdaQueryWrapper<SegmentDetail>()
                .eq(SegmentDetail::getCustomerId, customerId)
                .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode())
                .isNull(SegmentDetail::getProtectTime)
                .in(SegmentDetail::getSegmentId, validSegmentIds));

        if (CollectionUtils.isEmpty(segmentDetails)){
            log.warn("无有效的分群明细信息，customerId={}， protectTime={}", customerId, protectTime);
            return false;
        }

        Set<Long> currentSalerSegmentDetailIds = segmentDetails.stream()
                .filter(segmentDetail -> Objects.equals(salerId,segmentDetail.getSalerId()))
                .map(SegmentDetail::getId)
                .collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(currentSalerSegmentDetailIds)) {
            this.update(new LambdaUpdateWrapper<SegmentDetail>()
                    .in(SegmentDetail::getId, currentSalerSegmentDetailIds)
                    .set(SegmentDetail::getProtectTime, protectTime));
        }

        Set<Long> otherSalerSegmentDetailIds = segmentDetails.stream()
                .filter(segmentDetail -> !Objects.equals(salerId,segmentDetail.getSalerId()))
                .map(SegmentDetail::getId)
                .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(otherSalerSegmentDetailIds)) {
            this.update(new LambdaUpdateWrapper<SegmentDetail>()
                    .in(SegmentDetail::getId, otherSalerSegmentDetailIds)
                    .set(SegmentDetail::getDeleteFlag, YesOrNoEnum.YES.getCode())
                    .set(SegmentDetail::getDeleteReason, "分群已被其他商务抢先保护"));
        }

        return true;
    }

    @Override
    public boolean setLastSignTime(Set<String> validSegmentIds, String customerId, String salerId, Date lastSignTime) {
        List<SegmentDetail> segmentDetails = this.list(new LambdaQueryWrapper<SegmentDetail>()
                .eq(SegmentDetail::getCustomerId, customerId)
                .eq(SegmentDetail::getSalerId, salerId)
                .isNull(SegmentDetail::getSignFirstTime)
                .in(SegmentDetail::getSegmentId, validSegmentIds));

        if (Objects.isNull(segmentDetails) || segmentDetails.isEmpty()) {
            log.warn("无有效的分群明细信息，customerId={}， salerId={}， lastSignTime={}", customerId, salerId, lastSignTime);
            return false;
        }

        Set<Long> segmentDetailIds = segmentDetails.stream()
                .filter(segmentDetail -> segmentDetail.getSalerId().equals(salerId))
                .map(SegmentDetail::getId)
                .collect(Collectors.toSet());

        return this.update(new LambdaUpdateWrapper<SegmentDetail>()
                .in(SegmentDetail::getId, segmentDetailIds)
                .set(SegmentDetail::getSignFirstTime, lastSignTime));
    }
}
