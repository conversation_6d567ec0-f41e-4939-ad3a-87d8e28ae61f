package com.ce.scrm.center.dao.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 历史线索导入到customer_leads || 53客服等来源渠道 进入customer_leads 需要传的对象
 * <AUTHOR>
 * @Date 2025-08-09 15:19
 */
@Data
public class ActivityClueInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 潜客规则配置表对应的leadsCode。营销效果回收必传
	 */
	private String leadsCode;

	/**
	 * 客户id
	 */
	private String customerId;

	/**
	 * 客户名称
	 */
	private String customerName;

	/**
	 * 电话
	 */
	private String phone;

	/**
	 * 邮箱
	 */
	private String email;

}
