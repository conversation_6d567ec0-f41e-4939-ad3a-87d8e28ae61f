package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_prompt_auth_info")
public class AiPromptAuthInfo {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long aiPromptInfoId;

    private String areaId;

    private String subId;

    private String buId;

    private String deptId;

    private Date createTime;

    private Date updateTime;
}
