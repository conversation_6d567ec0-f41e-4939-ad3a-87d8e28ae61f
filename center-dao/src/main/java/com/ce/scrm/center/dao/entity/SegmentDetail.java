package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 分群客户明细表
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
public class SegmentDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 分群ID
     */
    private String segmentId;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 搜客宝PID
     */
    private String pid;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 省code
     */
    private String regProvince;

    /**
     * 市code
     */
    private String regCity;

    /**
     * 区code
     */
    private String regDistrict;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 分司ID
     */
    private String subId;

    /**
     * 事业部ID
     */
    private String buId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 商务ID
     */
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String salerId;

    /**
     * 区域总监下发时间
     */
    private Date areaDistributeTime;

    /**
     * 分司总监下发时间
     */
    private Date subDistributeTime;

    /**
     * 事业部总监下发时间
     */
    private Date buDistributeTime;

    /**
     * 部门经理下发时间
     */
    private Date deptDistributeTime;

    /**
     * 保护关系快照(JSON结构存储分司/事业部/部门/商务)
     */
    private String protectSnapshot;

    /**
     * 保护状态
     */
    private Integer protectStatus;

    /**
     * 成交客户状态 0:未成交 1:已成交
     */
    private Integer dealStatus;

    /**
     * 首次保护时间（仅初次有效）
     */
    private Date protectTime;

    /**
     * 最近一次拜访时间（根据打卡表同步）
     */
    private Date visitLastTime;

    /**
     * 首次签单时间（仅初次有效）
     */
    private Date signFirstTime;

    /**
     * 商务标注 0:未处理 1:有价值 2:无价值
     */
    private Integer salerCustomTag;

    /**
     * 首次标注时间
     */
    private Date salerCustomTagTime;

    /**
     * 删除状态 0:未删除 1:已删除
     */
    private Integer deleteFlag;

    /**
     * 删除原因
     */
    private String deleteReason;

    /**
     * 创建人ID
     */
    private String createId;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人ID
     */
    private String updateId;

    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 一级国标行业编码
     */
    private String firstIndustryCode;

    /**
     * 一级国标行业名称
     */
    private String firstIndustryName;

    /**
     * 二级国标行业编码
     */
    private String secondIndustryCode;

    /**
     * 二级国标行业名称
     */
    private String secondIndustryName;

    /**
     * 三级国标行业编码
     */
    private String thirdIndustryCode;

    /**
     * 三级国标行业名称
     */
    private String thirdIndustryName;

    /**
     * 四级国标行业编码
     */
    private String fourthIndustryCode;

    /**
     * 四级国标行业名称
     */
    private String fourthIndustryName;

    /**
     * 注册资本
     */
    private Double registerCapital;

    /**
     * 有无域名备案 1:是 0:否
     */
    private Integer icpFlag;

    /**
     * 有无进出口信用 1:是 0:否
     */
    private Integer jingchukouFlag;

    /**
     * 不参与SQL 仅查询回拼接出来
     */
    @TableField(exist = false)
    private String subIdListStr;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分配类型：1:按保护关系自动分配 2:按组织结构手动分配
     */
    private Integer assignmentType;

    public SegmentDetail() {}
}
