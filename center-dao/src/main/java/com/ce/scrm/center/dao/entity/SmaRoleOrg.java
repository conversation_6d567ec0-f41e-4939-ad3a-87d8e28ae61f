package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName sma_role_org
 */
@TableName(value ="sma_role_org")
@Data
public class SmaRoleOrg implements Serializable {
    /**
     * 
     */
    @TableId
    private String id;

    /**
     * 
     */
    private String roleId;

    /**
     * 该角色所属部门上级组织
     */
    private String orgId;

    /**
     * 该角色所属部门
     */
    private String deptId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}