package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.SmaMarketArea;
import com.ce.scrm.center.dao.entity.query.SmaMarketAreaQuery;
import com.ce.scrm.center.dao.entity.view.SmaMarketAreaView;

import java.util.List;

/**
 * @version 1.0
 * @Description: 市场地区关系 SMA_MARKET_AREA
 * @Author: lijinpeng
 * @Date: 2024/12/9 17:25
 */
public interface SmaMarketAreaService extends IService<SmaMarketArea> {


    /*
     * @Description 根据条件查询市场地区映射关系 一条数据
     * <AUTHOR>
     * @date 2024/12/10 09:53
     * @param condition
     * @return com.ce.scrm.center.dao.entity.vo.SmaMarketAreaView
     */
    SmaMarketAreaView selectOneByCondition(SmaMarketAreaQuery condition);

    /*
     * @Description 根据条件查询市场地区映射关系 一个结果集
     * <AUTHOR>
     * @date 2024/12/10 09:53
     * @param condition
     * @return java.util.List<com.ce.scrm.center.dao.entity.vo.SmaMarketAreaView>
     */
    List<SmaMarketAreaView> selectByCondition(SmaMarketAreaQuery condition);

}
