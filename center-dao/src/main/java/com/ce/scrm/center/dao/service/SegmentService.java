package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.Segment;

import java.util.Date;
import java.util.Set;

/**
 * @description 分群信息表服务层
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface SegmentService extends IService<Segment> {
    /**
     * 根据分群ID查询分群信息
     * @param segmentId
     * @return
     */
    Segment getSegmentById(String segmentId);

    /**
     * 获取有效的分群信息
     * @param referenceTime 参考时间
     * @return
     */
    Set<String> getValidSegmentIds(Date referenceTime);

    void updateDistributeCount(String segmentId, Integer distributeCount);

	/**
	 * 根据条件获取分司及对应状态下的分群列表
	 * @param subId 分司id
	 * @param pageNum 页码
	 * @param pageSize 当前页数
	 * @param empId 员工id
	 * @return 分群信息分页列表
	 */
	Page<Segment> getDiffSubIdAndStatusPage(Page<Segment> page, String subId, String empId);
}
