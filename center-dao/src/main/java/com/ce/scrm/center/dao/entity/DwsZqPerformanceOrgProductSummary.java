package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "dws_zq_performance_org_product_summary")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DwsZqPerformanceOrgProductSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 商务月 yyyy-MM
     */
    private String businessMonth;

    /**
     * 机构类型 0:全国 1:区域，2:分司，3:事业部，4:商务部，5:事业部小组，6:商务组）
     */
    private Integer orgType;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 事业部ID
     */
    private String buId;

    /**
     * 事业部名称
     */
    private String buName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 汇总类别 1:总业绩，2:门户/生态，3:门户/生态中间类，4:门户/生态小类
     */
    private Integer summaryType;

    /**
     * summary_type 对应的中文名称
     */
    private String summaryTypeName;

    /**
     * 产品分类一级编码
     */
    private String productCategoryOne;

    /**
     * 产品分类二级编码
     */
    private String productCategoryTwo;

    /**
     * 产品分类三级编码
     */
    private String productCategoryThree;

    /**
     * 产品分类三级名称
     */
    private String productCategoryThreeName;

    /**
     * 产品分类二级名称
     */
    private String productCategoryTwoName;

    /**
     * 产品分类一级编码
     */
    private String productCategoryOneName;

    /**
     * 月累计业绩
     */
    private Double performanceMonth;

    /**
     * 全国排名
     */
    private Integer performanceRankingMonthByCountry;

    /**
     * 占上一层级的比例
     */
    private Double performancePercentMonth;

    /**
     * 本日业绩
     */
    private Double performanceToday;

    /**
     * 是否有效 1:有效 0:无效
     */
    private Integer flag;

    /**
     * 创建时间
     */
    private Date dbInsertTime;

    /**
     * 更新时间
     */
    private Date dbUpdateTime;

}
