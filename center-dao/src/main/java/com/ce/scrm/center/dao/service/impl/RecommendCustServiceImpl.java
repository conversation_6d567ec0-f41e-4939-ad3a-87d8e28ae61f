package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.RecommendCust;
import com.ce.scrm.center.dao.mapper.RecommendCustMapper;
import com.ce.scrm.center.dao.service.RecommendCustService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 推荐客户
 * @createDate 2024-08-13
 */
@Service
public class RecommendCustServiceImpl extends ServiceImpl<RecommendCustMapper, RecommendCust> implements RecommendCustService {

	@Override
	public long getRecommendCustCount(String recommendedCustId) {
		if (StringUtils.isBlank(recommendedCustId)) {
			return 0;
		}
		return this.lambdaQuery().eq(RecommendCust::getRecommendedCustId, recommendedCustId).count();
	}
}



