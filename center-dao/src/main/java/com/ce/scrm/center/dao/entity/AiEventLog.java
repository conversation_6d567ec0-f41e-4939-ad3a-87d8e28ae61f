package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * Ai事件表对应的实体类，用于封装表中数据
 * </p>
 *
 * <AUTHOR> @since
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("ai_event_log")
public class AiEventLog implements Serializable {
    /**
     * 主键，自增ID
     */
    @TableId(type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    private Long id;

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 操作关联的id
     */
    private String parentEventId;

    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 员工名字
     */
    private String employeeName;

    /**
     * 客户ID
     */
    private String custId;

    /**
     * 客户PID
     */
    private String pid;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 提示词ID
     */
    private Long promptId;

    /**
     * 2025-07-10 新增
     * extraPromptId 直接分析的时候 可以多传一个行业解决方案
     */
    private Long extraPromptId;

    /**
     * 平台，取值为 WECOM/ PC
     */
    private String platform;

    /**
     * 聊天ID
     */
    private String chatId;

    /**
     * 聊天问题
     */
    private String chatQuestion;

    /**
     * 聊天推理
     */
    private String chatReasoning;

    /**
     * 聊天回答
     */
    private String chatResponse;

    /**
     * 是否需要联网，0:不联网，1:需要联网
     */
    private Integer needSearch;

    /**
     * 问题来源：0.自问，1.预置问题
     */
    private Integer questionSource;

    /**
     * 问题类型：0.默认值，1.文字提问，2.图片类型提问，3.ppt方案整合问题，4.海关数据提问，5.生成图片问题，6.分析方向直接生成,7 网站检测分析,8:新生成,9:老方案修改
     */
    private Integer questionType;

    /**
     * 操作行为：0.无操作，1.点赞，2.点low，3.复制，4.生成ppt，5.聊天，6.直接分析，7.重新分析，8.故障上报
     */
    private Integer operateType;

    /**
     * 方案整合选择的问答id
     */
    private String linkedEventIds;

    /**
     * 生成ppt的内容
     */
    private String pptGenerateContent;

    /**
     * ppt生成result
     */
    private String pptGenerateResult;

    /**
     * ppt生成结果进度：0.未开始，1.进行中，2.成功，3.失败
     */
    private Integer pptStatus;

    /**
     * 图片类型提问时的图片url
     */
    private String chatQuestionImages;

    private Long voiceId;

    /**
     * 点low原因
     */
    private String lowReason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
