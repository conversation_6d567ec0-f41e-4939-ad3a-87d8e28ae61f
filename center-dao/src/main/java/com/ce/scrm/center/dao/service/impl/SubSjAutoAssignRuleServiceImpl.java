package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.SubSjAutoAssignRule;
import com.ce.scrm.center.dao.mapper.SubSjAutoAssignRuleMapper;
import com.ce.scrm.center.dao.service.SubSjAutoAssignRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 分司商机自动分配规则Service实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
@Slf4j
@Service
public class SubSjAutoAssignRuleServiceImpl extends ServiceImpl<SubSjAutoAssignRuleMapper, SubSjAutoAssignRule> implements SubSjAutoAssignRuleService {

    @Override
    public SubSjAutoAssignRule getBySubId(String subId) {
        LambdaQueryWrapper<SubSjAutoAssignRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubSjAutoAssignRule::getSubId, subId);
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean isAutoAssignEnabled(String subId) {
        SubSjAutoAssignRule rule = getBySubId(subId);
        return Objects.nonNull(rule) && Objects.equals(rule.getStatus(), 1);
    }

    @Override
    public List<SubSjAutoAssignRule> getEnabledAutoAssignRules() {
        LambdaQueryWrapper<SubSjAutoAssignRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubSjAutoAssignRule::getStatus, 1) // 开启状态
                .orderByAsc(SubSjAutoAssignRule::getWeight) // 按权重升序
                .orderByAsc(SubSjAutoAssignRule::getId); // 按ID升序
        return this.list(queryWrapper);
    }

    @Override
    public boolean updateWeight(Long id) {
        LambdaUpdateWrapper<SubSjAutoAssignRule> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SubSjAutoAssignRule::getId, id)
                .setSql("weight = weight + 1");
        return this.update(updateWrapper);
    }

    @Override
    public boolean enableAutoAssign(String subId, String subName, String operatorId) {
        // 先查询是否已存在规则
        SubSjAutoAssignRule existingRule = getBySubId(subId);
        
        if (Objects.nonNull(existingRule)) {
            // 更新现有规则
            existingRule.setStatus(1);
            existingRule.setUpdatedId(operatorId);
            existingRule.setUpdatedTime(new Date());
            return this.updateById(existingRule);
        } else {
            // 创建新规则
            SubSjAutoAssignRule newRule = new SubSjAutoAssignRule();
            newRule.setSubId(subId);
            newRule.setSubName(subName);
            newRule.setStatus(1);
            newRule.setWeight(0);
            newRule.setCreatedId(operatorId);
            newRule.setCreateTime(new Date());
            newRule.setUpdatedId(operatorId);
            newRule.setUpdatedTime(new Date());
            return this.save(newRule);
        }
    }

    @Override
    public boolean disableAutoAssign(String subId, String operatorId) {
        SubSjAutoAssignRule rule = getBySubId(subId);
        if (Objects.isNull(rule)) {
            log.warn("分司自动分配规则不存在，分司ID={}", subId);
            return false;
        }
        
        rule.setStatus(0);
        rule.setUpdatedId(operatorId);
        rule.setUpdatedTime(new Date());
        return this.updateById(rule);
    }
}




