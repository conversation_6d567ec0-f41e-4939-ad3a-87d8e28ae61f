package com.ce.scrm.center.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ce.scrm.center.dao.entity.CustomerWillCirculation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * Description: 即将流转（流失）客户表
 * @author: JiuDD
 * date: 2024/7/19
 */
public interface CustomerWillCirculationMapper extends BaseMapper<CustomerWillCirculation> {

    /**
     * Description: 获取待流转客户的流转日期
     * @author: JiuDD
     * @param origin 16待流转
     * @return List<Date>
     * date: 2024/9/20 11:52
     */
    @Select("<script>" +
            "SELECT DISTINCT(PRE_DATE) FROM customer_will_circulation WHERE origin=#{origin} AND CREATE_DATE=#{createDate} AND delete_flag=0 AND absolute_protect_flag=0 AND cust_id IN (" +
            "    SELECT CUST_ID FROM cm_cust_protect " +
            "        <where>" +
            "            1=1" +
            "            <if test=\"subId != null and subId != ''\">" +
            "                AND SUBCOMPANY_ID = #{subId}" +
            "            </if>" +
            "            <if test=\"startTime != null and endTime != null\">" +
            "                AND first_sign_time BETWEEN #{startTime} AND #{endTime}" +
            "            </if>" +
            "            <if test=\"startTime == null or endTime == null\">" +
            "                AND first_sign_time IS NULL" +
            "            </if>" +
            "        </where>" +
            ")" +
            "</script>")
    List<Date> getPreDate(@Param("origin") Integer origin, @Param("createDate") Date createDate, @Param("subId") String subId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}




