package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @version 1.0
 * @Description: 节假日表
 * @TableName t_holiday_info
 * @Author: lijinpeng
 * @Date: 2024/10/8 16:41
 */
@TableName(value ="t_holiday_info")
@Data
public class HolidayInfo {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 是否为节假日：0否，1是，这里都是1
     */
    private Integer holiday;

    /**
     * 节假日的中文名
     */
    private String name;

    /**
     * 薪资倍数，3表示是3倍工资
     */
    private Integer wage;

    /**
     * 只在调休下有该字段。表示调休的节假日
     */
    private String target;

    /**
     * 1表示放完假后调休，0表示先调休再放假
     */
    private Integer after;

    /**
     * 节假日的日期
     */
    private Date date;

    /**
     * 表示当前时间距离目标还有多少天。比如今天是 2018-09-28，距离 2018-10-01 还有3天
     */
    private Integer rest;

}
