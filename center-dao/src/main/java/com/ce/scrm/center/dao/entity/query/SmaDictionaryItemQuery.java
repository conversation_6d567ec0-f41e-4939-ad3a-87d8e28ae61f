package com.ce.scrm.center.dao.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 字典表查询条件
 * @Author: lijinpeng
 * @Date: 2025/1/17 17:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SmaDictionaryItemQuery implements Serializable {

    private List<String> idList;

    private String dictionaryId;

}
