package com.ce.scrm.center.dao.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CustomerFollowMoneySummaryView implements Serializable {

    /**
     * 分司id
     */
    private String subId;

    /**
     * 当月预计签单金额总额（商务月）
     */
    private BigDecimal businessMoneySignSum;

    /**
     * 当月预计到账金额总额（商务月）
     */
    private BigDecimal businessMoneyAccountSum;

    /**
     * 当月预计签单客户数（商务月）
     */
    private Integer businessCustomerSignCount;

    /**
     * 当月预计到账客户数（商务月）
     */
    private Integer businessCustomerAccountCount;

    /**
     * 下月预计签单金额总额（自然月）
     */
    private BigDecimal naturalMoneySignSum;

    /**
     * 下月预计到账金额总额（自然月）
     */
    private BigDecimal naturalMoneyAccountSum;

}
