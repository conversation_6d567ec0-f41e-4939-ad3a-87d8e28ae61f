package com.ce.scrm.center.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.SegmentDetail;
import com.ce.scrm.center.dao.entity.query.AreaSegmentQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description 分群客户明细表Mapper
 * <AUTHOR>
 * @date 2025-02-27
 */
@Mapper
public interface SegmentDetailMapper extends BaseMapper<SegmentDetail> {

    Page<SegmentDetail> getSegmentPageByAreaId(Page<SegmentDetail> page, @Param("query") AreaSegmentQuery areaSegmentQuery);

}
