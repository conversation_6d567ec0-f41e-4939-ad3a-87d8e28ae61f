package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.SegmentDetail;
import com.ce.scrm.center.dao.entity.query.AreaSegmentQuery;

import java.util.Date;
import java.util.Set;

/**
 * @description 分群客户明细表服务层
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface SegmentDetailService extends IService<SegmentDetail> {

    Page<SegmentDetail> getSegmentPageByAreaId(Page<SegmentDetail> page,AreaSegmentQuery areaSegmentQuery);

    /**
     * 插入 最近一次拜访时间
     *
     * @param validSegmentIds
     * @param customerId
     * @param salerId
     * @param visitTime
     * @return
     */
    boolean setLastVisitTime(Set<String> validSegmentIds, String customerId, String salerId, Date visitTime);


    /**
     * 设置保护时间
     *
     * @param validSegmentIds
     * @param customerId
     * @param salerId
     * @param protectTime
     * @return
     */
    boolean setProtectTime(Set<String> validSegmentIds, String customerId, String salerId, Date protectTime);

    /**
     * 设置最近一次签单时间
     *
     * @param validSegmentIds
     * @param customerId
     * @param salerId
     * @param lastSignTime
     * @return
     */
    boolean setLastSignTime(Set<String> validSegmentIds, String customerId, String salerId, Date lastSignTime);
}
