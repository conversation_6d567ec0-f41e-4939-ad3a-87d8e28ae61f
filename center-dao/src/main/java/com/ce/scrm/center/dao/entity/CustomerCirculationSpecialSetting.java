package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 成交客户流转流失特例配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@TableName(value = "customer_circulation_special_setting")
@Data
public class CustomerCirculationSpecialSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 不流转类型：1不流转客户 2不流失客户
     */
    private Integer notCirculationType;

    /**
     * 客户ID
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 分公司ID
     */
    private String subId;

    /**
     * 分公司名称
     */
    private String subName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 商户Id
     */
    private String salerId;

    /**
     * 不流转添加原因：字典NOT_CIRCULATION
     */
    private String notCirculationAddReason;

    /**
     * 不流转添加原因名称
     */
    @TableField(exist = false)
    private String notCirculationAddReasonName;

    /**
     * 不流失添加原因：字典NOT_LOSE
     */
    private String notLoseAddReason;

    /**
     * 不流失添加原因：字典NOT_LOSE
     */
    @TableField(exist = false)
    private String notLoseAddReasonName;

    /**
     * 创建用户id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改用户id
     */
    private String updateUserId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标记：0否1是
     */
    private Integer deleteFlag;

}
