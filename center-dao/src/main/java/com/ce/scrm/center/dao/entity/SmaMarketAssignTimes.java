package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * @description 区总分配次数
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName(value ="sma_market_assign_times")
@Data
public class SmaMarketAssignTimes implements Serializable {
    /**
     * 
     */
    @TableId
    private String id;

    /**
     * 区域总监员工ID
     */
    private String empId;

    /**
     * 年月 格式yyyyMM
     */
    private Integer monthNum;

    /**
     * 当月已调拨客户数
     */
    private Integer times;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 修改人
     */
    private String updateBy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}