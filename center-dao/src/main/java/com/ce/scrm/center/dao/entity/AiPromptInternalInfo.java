package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("ai_prompt_internal_info")
public class AiPromptInternalInfo {

    @TableId
    private Long id;

    private String title;

    private String content;

    private Integer deleteFlag;

    private String operator;

    private Date createTime;

    private Date updateTime;
}
