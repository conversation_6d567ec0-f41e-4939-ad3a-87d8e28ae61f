package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.SmaMarketArea;
import com.ce.scrm.center.dao.entity.query.SmaMarketAreaQuery;
import com.ce.scrm.center.dao.entity.view.SmaMarketAreaView;
import com.ce.scrm.center.dao.mapper.SmaMarketAreaMapper;
import com.ce.scrm.center.dao.service.SmaMarketAreaService;
import com.ce.scrm.center.dao.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;

import java.util.Collections;
import java.util.List;

/**
 * @version 1.0
 * @Description: 市场地区关系
 * @Author: lijinpeng
 * @Date: 2024/12/9 17:26
 */
@Service
@Slf4j
public class SmaMarketAreaServiceImpl extends ServiceImpl<SmaMarketAreaMapper, SmaMarketArea>
        implements SmaMarketAreaService {

    @Override
    public SmaMarketAreaView selectOneByCondition(SmaMarketAreaQuery condition) {
        List<SmaMarketAreaView> marketAreaList = selectByCondition(condition);
        if(CollectionUtils.isEmpty(marketAreaList)) {
            return null;
        }else {
            return marketAreaList.get(0);
        }
    }

    @Override
    public List<SmaMarketAreaView> selectByCondition(SmaMarketAreaQuery condition) {
        log.info("根据条件查询市场地区映射关系,参数condition={}", JSON.toJSONString(condition));
        if(condition == null) {
            return Collections.emptyList();
        }
        LambdaQueryChainWrapper<SmaMarketArea> lambdaQueryChainWrapper = this.lambdaQuery();
        if(condition.getId() != null) {
            lambdaQueryChainWrapper.eq(SmaMarketArea::getId, condition.getId());
        }
        if(StringUtils.isNotBlank(condition.getMarketId())) {
            lambdaQueryChainWrapper.eq(SmaMarketArea::getMarketId, condition.getMarketId());
        }
        if (CollectionUtils.isNotEmpty(condition.getMarketIdList())) {
            lambdaQueryChainWrapper.in(SmaMarketArea::getMarketId, condition.getMarketIdList());
        }
        if(StringUtils.isNotBlank(condition.getProvinceCode())) {
            lambdaQueryChainWrapper.eq(SmaMarketArea::getProvinceCode, condition.getProvinceCode());
        }
        if(StringUtils.isNotBlank(condition.getCityCode())) {
            lambdaQueryChainWrapper.eq(SmaMarketArea::getCityCode, condition.getCityCode());
        }
        if(StringUtils.isNotBlank(condition.getAreaCode())) {
            lambdaQueryChainWrapper.eq(SmaMarketArea::getAreaCode, condition.getAreaCode());
        }

        List<SmaMarketArea> smaMarketAreaList = lambdaQueryChainWrapper.list();
        List<SmaMarketAreaView> result = BeanCopyUtils.convertToVoList(smaMarketAreaList, SmaMarketAreaView.class);
//        log.info("根据条件查询市场地区映射关系,结果为result={}", JSON.toJSONString(result));
        return result;
    }

}
