package com.ce.scrm.center.dao.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 数据接收的原始消息,当进行insert 的时候触发cdp 上报通知
 * @TableName eqixiu_data_receiver_origin
 */
@Data
public class EqixiuDataReceiverOrigin implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 通知消息唯一键
     */
    private String eventId;

    /**
     * 结果类型
     */
    private String eventType;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 编码解析出来的信息
     */
    private String codeDecoderInfo;

    /**
     * 原始返回数据
     */
    private String dataOrigin;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        EqixiuDataReceiverOrigin other = (EqixiuDataReceiverOrigin) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getEventId() == null ? other.getEventId() == null : this.getEventId().equals(other.getEventId()))
            && (this.getEventType() == null ? other.getEventType() == null : this.getEventType().equals(other.getEventType()))
            && (this.getActivityId() == null ? other.getActivityId() == null : this.getActivityId().equals(other.getActivityId()))
            && (this.getCodeDecoderInfo() == null ? other.getCodeDecoderInfo() == null : this.getCodeDecoderInfo().equals(other.getCodeDecoderInfo()))
            && (this.getDataOrigin() == null ? other.getDataOrigin() == null : this.getDataOrigin().equals(other.getDataOrigin()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getEventId() == null) ? 0 : getEventId().hashCode());
        result = prime * result + ((getEventType() == null) ? 0 : getEventType().hashCode());
        result = prime * result + ((getActivityId() == null) ? 0 : getActivityId().hashCode());
        result = prime * result + ((getCodeDecoderInfo() == null) ? 0 : getCodeDecoderInfo().hashCode());
        result = prime * result + ((getDataOrigin() == null) ? 0 : getDataOrigin().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", eventId=").append(eventId);
        sb.append(", eventType=").append(eventType);
        sb.append(", activityId=").append(activityId);
        sb.append(", codeDecoderInfo=").append(codeDecoderInfo);
        sb.append(", dataOrigin=").append(dataOrigin);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}