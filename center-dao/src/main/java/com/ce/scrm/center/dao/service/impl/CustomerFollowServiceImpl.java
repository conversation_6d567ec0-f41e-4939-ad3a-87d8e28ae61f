package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.CustomerFollow;
import com.ce.scrm.center.dao.entity.view.CustomerFollowMoneySummaryView;
import com.ce.scrm.center.dao.entity.view.CustomerFollowStageSummaryView;
import com.ce.scrm.center.dao.mapper.CustomerFollowMapper;
import com.ce.scrm.center.dao.service.CustomerFollowService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @description 客户跟进 service 实现
 * <AUTHOR>
 * @date 2024-04-19
 */
@Service
public class CustomerFollowServiceImpl extends ServiceImpl<CustomerFollowMapper, CustomerFollow>
    implements CustomerFollowService{

    @Resource
    private CustomerFollowMapper customerFollowMapper;


    @Override
    public List<CustomerFollowStageSummaryView> getZqCustomerFollowStageSummary(Date startDate, Date endDate) {
        return customerFollowMapper.getZqCustomerFollowStageSummary(startDate,endDate);
    }

    @Override
    public List<CustomerFollowMoneySummaryView> getZqPerformancePredictSummary(Date businessMonthStartDate, Date businessMonthEndDate,
                                                                               Date naturalMonthStartDate, Date naturalMonthEndDate) {
        return customerFollowMapper.getZqPerformancePredictSummary(businessMonthStartDate,businessMonthEndDate,naturalMonthStartDate,naturalMonthEndDate);
    }

}




