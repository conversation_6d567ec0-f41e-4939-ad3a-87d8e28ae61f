package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.SjIntentInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cm_cust_protect(客户保护关系表)】的数据库操作Service
* @createDate 2024-06-07 17:29:37
*/
public interface SmaSjIntentInfoService extends IService<SjIntentInfo> {

	/**
	 * 根据商机code列表查询商机信息列表
	 * @param sjCodeList 商机code列表
	 * @return 商机信息列表
	 */
	List<SjIntentInfo> selectListBySjCodes(List<String> sjCodeList);

}
