package com.ce.scrm.center.dao.utils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 对象复制
 * @param <T>
 * @param <V>
 */
public class BeanCopyUtils<T, V> {


	public static<T, V>  V convertToVo(T t,Class<V> clazz){
		if(null == t) return null;
		try {
			V v = clazz.newInstance();
			BeanUtils.copyProperties(t, v);
			try {
				//自定义增加字段属性写在Vo的overRideVo方法中
				clazz.getMethod("overRideVo", clazz).invoke(v, v);
			}catch (NoSuchMethodException e) {
			}
			return v;
		} catch (InstantiationException e) {
			
		} catch (IllegalAccessException e) {
			
		}catch (InvocationTargetException e) {
			
		}
		return null;
	};

	public static<T, V> List<V> convertToVoList(List<T> list,Class<V> clazz){
		if(null == list) return null;
		List<V> result = new ArrayList<V>();
		for (T t : list) {
			try {
				V v = clazz.newInstance();
				BeanUtils.copyProperties(t, v);
				try {
					//自定义增加字段属性写在Vo的overRideVo方法中
					clazz.getMethod("overRideVo", clazz).invoke(v, v);
				}catch (NoSuchMethodException e) {
				}
				result.add(v);
			} catch (InstantiationException e) {
				
			} catch (IllegalAccessException e) {
				
			}catch (InvocationTargetException e) {
				
			}
		}
		return result;
	}

	public static String[] getNullPropertyNames(Object source) {
		final BeanWrapper src = new BeanWrapperImpl(source);
		PropertyDescriptor[] pds = src.getPropertyDescriptors();
		Set<String> emptyNames = new HashSet<>();
		for (PropertyDescriptor pd : pds) {
			try {
				Object srcValue = src.getPropertyValue(pd.getName());
				if (srcValue == null) {
					emptyNames.add(pd.getName());
				}
			}catch (Exception e){
				continue;
			}
		}
		String[] result = new String[emptyNames.size()];
		return emptyNames.toArray(result);
	}
}
