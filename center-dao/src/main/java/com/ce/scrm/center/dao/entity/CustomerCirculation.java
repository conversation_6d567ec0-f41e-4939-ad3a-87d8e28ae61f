package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 流转客户表
 * <AUTHOR>
 * @date 2025-05-09
 */
@Data
@TableName(value = "customer_circulation")
public class CustomerCirculation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键
     */
    private Long id;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 事业部id
     */
    private String buId;

    /**
     * 事业部id
     */
    private String buName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 流转原因
     */
    private String reason;

    /**
     * 预计流转日期
     */
    private Date preDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建日期，yyyy-mm-dd
     */
    private Date createDate;

    /**
     * 客户分层（商务）
     */
    private Integer customerLayer;

    /**
     * 上次触达员工id
     */
    private String lastEmpId;

    /**
     * 上次触达员工名称
     */
    private String lastEmpName;

    /**
     * 上次触达方式
     */
    private Integer lastReachType;

    /**
     * 上次触达时间
     */
    private Date lastReachTime;

    /**
     * 上次流转时间
     */
    private Date lastCirculationDate;

    public CustomerCirculation() {}

}
