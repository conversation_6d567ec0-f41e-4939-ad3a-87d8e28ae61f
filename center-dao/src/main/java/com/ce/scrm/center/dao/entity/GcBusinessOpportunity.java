package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 
 * @TableName gc_business_opportunity
 */
@TableName(value ="gc_business_opportunity")
@Data
public class GcBusinessOpportunity implements Serializable {
    /**
     * 高呈商机ID（主键）
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 发起方：0、无，1、中小商务，2、高呈
     */
    private Integer initiator;

    /**
     * 等级：GcSjLevelEnum
     */
    private Integer level;

    /**
     * 来源：GcSjSourceEnum
     */
    private Integer source;

    /**
     * 高呈商机状态，GcSjStateEnum
     */
    private Integer state;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 联系人名称
     */
    private String linkmanName;

    /**
     * 联系人手机号
     */
    private String linkmanPhone;

    /**
     * 联系人邮箱
     */
    private String linkmanEmail;

    /**
     * 联系人部门
     */
    private String linkmanDept;

    /**
     * 联系人性别：0、未知，1、男，2、女
     */
    private Integer linkmanSex;

    /**
     * 联系人座机
     */
    private String linkmanLandline;

    /**
     * 联系人职务
     */
    private String linkmanJob;

    /**
     * 联系人微信
     */
    private String linkmanWechat;

    /**
     * 需求类型：GcSjRequirementEnum
     */
    private Integer requirementType;

    /**
     * 需求详情
     */
    private String requirementDetail;

    /**
     * 客户预算
     */
    private BigDecimal customerBudget;

    /**
     * 附件ID（多个使用,分隔）
     */
    private String attachmentIds;

    /**
     * 是否面谈（1、是，0、否）
     */
    private Integer interviewState;

    /**
     * 中小市场部商机code
     */
    private String telSjCode;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司ID
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务ID
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 高呈区域ID
     */
    private String gcAreaId;

    /**
     * 高呈区域名称
     */
    private String gcAreaName;

    /**
     * 高呈分司ID
     */
    private String gcSubId;

    /**
     * 高呈分司名称
     */
    private String gcSubName;

    /**
     * 高呈部门ID
     */
    private String gcDeptId;

    /**
     * 高呈部门名称
     */
    private String gcDeptName;

    /**
     * 高呈商务ID
     */
    private String gcSalerId;

    /**
     * 高呈商务名称
     */
    private String gcSalerName;

    /**
     * 期望高呈区域ID
     */
    private String expectGcAreaId;

    /**
     * 期望高呈区域名称
     */
    private String expectGcAreaName;

    /**
     * 期望高呈分司ID
     */
    private String expectGcSubId;

    /**
     * 期望高呈分司名称
     */
    private String expectGcSubName;

    /**
     * 期望高呈部门ID
     */
    private String expectGcDeptId;

    /**
     * 期望高呈部门名称
     */
    private String expectGcDeptName;

    /**
     * 期望高呈商务ID
     */
    private String expectGcSalerId;

    /**
     * 期望高呈商务名称
     */
    private String expectGcSalerName;

    /**
     * 审核总监ID
     */
    private String checkMajorId;

    /**
     * 审核总监姓名
     */
    private String checkMajorName;

    /**
     * 审核总监手机号
     */
    private String checkMajorPhone;

    /**
     * 删除标记（1、删除，0、未删除）
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 高呈市场部退回原因
     */
    private String reasonBack;

    /**
     * 高呈商务退回原因
     */
    private String reasonBusinessBack;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}