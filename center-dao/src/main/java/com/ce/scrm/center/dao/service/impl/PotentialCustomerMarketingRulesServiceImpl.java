package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.PotentialCustomerMarketingRules;
import com.ce.scrm.center.dao.entity.query.PotentialWillingBehaviorSelectedVo;
import com.ce.scrm.center.dao.mapper.PotentialCustomerMarketingRulesMapper;
import com.ce.scrm.center.dao.service.PotentialCustomerMarketingRulesService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【potential_customer_marketing_rules(潜客营销规则配置表)】的数据库操作Service实现
* @createDate 2025-08-05 20:53:35
*/
@Service
public class PotentialCustomerMarketingRulesServiceImpl extends ServiceImpl<PotentialCustomerMarketingRulesMapper, PotentialCustomerMarketingRules> implements PotentialCustomerMarketingRulesService {
	@Resource
	private PotentialCustomerMarketingRulesMapper potentialCustomerMarketingRulesMapper;

	@Override
	public List<PotentialWillingBehaviorSelectedVo> getWillingBehaviorSelected() {
		return potentialCustomerMarketingRulesMapper.getWillingBehaviorSelected();
	}
}




