package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * @description sop客户评价表
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName(value ="sma_cust_evaluate")
@Data
public class SmaCustEvaluate implements Serializable {
    /**
     * 主键id 服务单号
     */
    @TableId
    private String id;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 服务类型
     */
    private Integer serviceTypeCode;

    /**
     * 服务类型名称
     */
    private String serviceTypeName;

    /**
     * 区域名称id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司名称
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 顾问id
     */
    private String designerId;

    /**
     * 顾问名称
     */
    private String designerName;

    /**
     * 设计师 ID
     */
    private String makerEmpId;

    /**
     * 设计师名称
     */
    private String makerEmpName;

    /**
     * 助理id
     */
    private String entryClerkId;

    /**
     * 助理name
     */
    private String entryClerkName;

    /**
     * 发布日期
     */
    private Date releaseDate;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}