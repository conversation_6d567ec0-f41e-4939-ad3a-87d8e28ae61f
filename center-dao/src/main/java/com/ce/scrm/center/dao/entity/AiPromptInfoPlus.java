package com.ce.scrm.center.dao.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: ai提示词信息表
 * @Author: lijinpeng
 * @Date: 2025/2/20 09:44
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiPromptInfoPlus implements Serializable {

    private String asbd;

    private String deptIds;
    private String buIds;
    private String subIds;
    private String areaIds;

    private Long id;

    private String title;

    private String content;

    private String remark;
    private Integer promptType;


    private Integer startFlag;

    private Integer deleteFlag;

    private String createUserId;
    /**
     * ce 中企 gboss 跨境
     */
    private String company;
    private String operator;

    private Date createTime;

    private Date updateTime;

}
