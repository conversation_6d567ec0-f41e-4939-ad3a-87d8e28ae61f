package com.ce.scrm.center.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ce.scrm.center.dao.entity.CmCustProtectQuery;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【cm_cust_protect(客户保护关系表)】的数据库操作Mapper
* @createDate 2024-06-07 17:29:37
* @Entity com.ce.scrm.center.dao.entity.CmCustProtect
*/
public interface CmCustProtectMapper extends BaseMapper<CmCustProtect> {

    /**
     * @description: 非空字段更新
     * @author: lijinpeng
     * @date: 2025/7/16 10:48
     * @param: [cmCustProtect]
     * @return: java.lang.Integer
     **/
//    Integer updateByCustId(CmCustProtect cmCustProtect);

    /**
     * @description: 根据custId更新，可以更新空值，所以要注意【先查在更】确保查询全量字段 ！！！
     * @author: lijinpeng
     * @date: 2025/7/16 10:48
     * @param: [cmCustProtect]
     * @return: java.lang.Integer
     **/
    Integer updateNullableByCustId(CmCustProtect cmCustProtect);
	Page<CmCustProtect> selectCmCustProtects(Page<?> page, @Param("params") CmCustProtectQuery cmCustProtectQuery);


}




