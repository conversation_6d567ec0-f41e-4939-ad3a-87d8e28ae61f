package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;


/**
 * @description 市场和分司关系
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName(value ="sma_market_subcompay")
@Data
public class SmaMarketSubcompay implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 市场id
     */
    private String marketId;

    /**
     * 分司id
     */
    private String subCompany;

    /**
     * 类型(1专属，2周边，3共享)
     */
    private Integer type;

    /**
     * 默认省code
     */
    private String defaultProvince;

    /**
     * 默认市code
     */
    private String defaultCity;

    /**
     * 默认区code
     */
    private String defaultArea;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}