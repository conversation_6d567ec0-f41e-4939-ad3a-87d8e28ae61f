package com.ce.scrm.center.dao.service.impl;


import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CmCustProtectQuery;
import com.ce.scrm.center.dao.entity.query.CmCustProtectNewQuery;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.mapper.CmCustProtectMapper;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【cm_cust_protect(客户保护关系表)】的数据库操作Service实现
* @createDate 2024-06-07 17:29:37
*/
@Service
@Slf4j
public class CmCustProtectServiceImpl extends ServiceImpl<CmCustProtectMapper, CmCustProtect>
    implements CmCustProtectService{

    @Resource
    private CmCustProtectMapper cmCustProtectMapper;

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Override
    public Boolean saveData(CmCustProtect cmCustProtect) {
        return super.save(cmCustProtect);
    }

    /*
     * @Description 根据主键ID更新客户信息
     * <AUTHOR>
     * @date 2024/11/19 11:48
     * @param cmCustProtect
     * @return java.lang.Integer
     */
    @Override
    public Integer updateByCustId(CmCustProtect cmCustProtect) {
        UpdateWrapper<CmCustProtect> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("cust_id",cmCustProtect.getCustId());
        int update = cmCustProtectMapper.update(cmCustProtect, updateWrapper);
        return update;
    }

    /**
     * @description: 根据custId更新，可以更新空值，所以要注意【先查在更】确保查询全量字段 ！！！
     * @author: lijinpeng
     * @date: 2025/7/16 10:48
     * @param: [cmCustProtect]
     * @return: java.lang.Integer
     **/
    @Override
    public Integer updateNullableByCustId(CmCustProtect cmCustProtect) {
        return cmCustProtectMapper.updateNullableByCustId(cmCustProtect);
    }

    @Override
    public CustProtectView selectOneByCondition(CmCustProtect condition) {
        List<CustProtectView> selectByCondition = selectByCondition(condition);
        if(CollectionUtils.isEmpty(selectByCondition)) {
            return null;
        }else {
            return selectByCondition.get(0);
        }
    }

    @Override
    public List<CustProtectView> selectByCondition(CmCustProtect condition) {
        log.info("根据条件查询保护关系表,参数condition={}", JSON.toJSONString(condition));
        if(condition == null) {
            return Collections.emptyList();
        }
        LambdaQueryChainWrapper<CmCustProtect> lambdaQueryChainWrapper = cmCustProtectService.lambdaQuery();
        if(StringUtils.isNotBlank(condition.getCustId())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getCustId, condition.getCustId());
        }
        if(StringUtils.isNotBlank(condition.getCustName())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getCustName, condition.getCustName());
        }
        List<CmCustProtect> cmCustProtects = lambdaQueryChainWrapper.list();
        List<CustProtectView> result = BeanCopyUtils.convertToVoList(cmCustProtects, CustProtectView.class);
//        log.info("根据条件查询保护关系表,结果为result={}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public Page<CustProtectView> selectPageByCondition(CmCustProtectNewQuery condition) {
        log.info("根据条件分页查询保护关系,参数condition={}", JSON.toJSONString(condition));
        Page<CmCustProtect> pageParam = Page.of(condition.getCurrentPage(),condition.getPageSize());
        LambdaQueryChainWrapper<CmCustProtect> lambdaQueryChainWrapper = cmCustProtectService.lambdaQuery();

        if (StringUtils.isNotBlank(condition.getSalerId())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getSalerId, condition.getSalerId());
        }
        if (StringUtils.isNotBlank(condition.getDeptId())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getBussdeptId, condition.getDeptId());
        }
        if (StringUtils.isNotBlank(condition.getBussdeptId())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getBussdeptId, condition.getBussdeptId());
        }
        if (StringUtils.isNotBlank(condition.getSubId())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getSubcompanyId, condition.getSubId());
        }
        if (StringUtils.isNotBlank(condition.getSubcompanyId())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getSubcompanyId, condition.getSubcompanyId());
        }
        if (StringUtils.isNotBlank(condition.getAreaId())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getAreaId, condition.getAreaId());
        }
        if (condition.getStatus() != null) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getStatus, condition.getStatus());
        }
        if (condition.getSource() != null) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getSource, condition.getSource());
        }
        if (StringUtils.isNotBlank(condition.getReason())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getReason, condition.getReason());
        }
        if(StringUtils.isNotBlank(condition.getLikeCustName())) {
            lambdaQueryChainWrapper.like(CmCustProtect::getCustName, condition.getLikeCustName());
        }
        if (Objects.equals(condition.getIsClock(), YesOrNoEnum.YES.getCode())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getIsClock,YesOrNoEnum.YES.getCode());
        }
        if (Objects.equals(condition.getIsClock(), YesOrNoEnum.NO.getCode())) {
            lambdaQueryChainWrapper.and(cmCustProtectLambdaQueryWrapper -> cmCustProtectLambdaQueryWrapper.eq(CmCustProtect::getIsClock,YesOrNoEnum.NO.getCode()).or().isNull(CmCustProtect::getIsClock));
        }
        if (StringUtils.isNotBlank(condition.getClockProvince())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getClockProvince, condition.getClockProvince());
        }
        if (StringUtils.isNotBlank(condition.getClockCity())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getClockCity, condition.getClockCity());
        }
        if (StringUtils.isNotBlank(condition.getClockRegion())) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getClockRegion, condition.getClockRegion());
        }
        if (Objects.equals(condition.getIsMarketSj(), YesOrNoEnum.YES.getCode())) {
            lambdaQueryChainWrapper.isNotNull(CmCustProtect::getBusioppoCode);
        }
        if (Objects.equals(condition.getIsMarketSj(), YesOrNoEnum.NO.getCode())) {
            lambdaQueryChainWrapper.isNull(CmCustProtect::getBusioppoCode);
        }
        if (CollectionUtils.isNotEmpty(condition.getRegionList())) {
            lambdaQueryChainWrapper.in(CmCustProtect::getRegRegion, condition.getRegionList());
        }
        if (condition.getCustSourceSub() != null) {
            lambdaQueryChainWrapper.eq(CmCustProtect::getCustSourceSub, condition.getCustSourceSub());
        }
        if(condition.getStartTime() != null) {
            lambdaQueryChainWrapper.ge(CmCustProtect::getUpdateTime, condition.getStartTime());
        }
        if(condition.getEndTime() != null) {
            lambdaQueryChainWrapper.le(CmCustProtect::getUpdateTime, condition.getEndTime());
        }

        Page<CmCustProtect> pageResult = lambdaQueryChainWrapper.page(pageParam);
        Page<CustProtectView> result = new Page<>();
        BeanUtil.copyProperties(pageResult,result);
        result.setRecords(BeanCopyUtils.convertToVoList(pageResult.getRecords(), CustProtectView.class));
        return result;
    }

    @Override
    public Integer updateBatchByCustIdList(List<String> custIdList, CmCustProtect cmCustProtect) {
        if(CollectionUtils.isEmpty(custIdList) || cmCustProtect == null) {
            log.error("updateBatchByCustIdList 参数有误");
            return 0;
        }
        List<CmCustProtect> list = cmCustProtectService.lambdaQuery().in(CmCustProtect::getCustId, custIdList).list();
        if(CollectionUtils.isEmpty(list)) {
            return 0;
        }
        for (CmCustProtect item : list) {

            //拷贝 不为空的字段
             BeanUtil.copyProperties(cmCustProtect, item,BeanCopyUtils.getNullPropertyNames(cmCustProtect));

            // 处理数据
//            if(cmCustProtect.getStatus() != null) {
//                switch (cmCustProtect.getStatus()) {
//                    case 1:
//                        break;
//                    case 2:
//                        cmCustProtect.setSalerId(null);
//                        cmCustProtect.setBussdeptId(null);
//                        cmCustProtect.setBuId(null);
//                        break;
//                    case 3:
//                        cmCustProtect.setSalerId(null);
//                        break;
//                    case 4:
//                        cmCustProtect.setSalerId(null);
//                        cmCustProtect.setBussdeptId(null);
//                        cmCustProtect.setBuId(null);
//                        cmCustProtect.setSubcompanyId(null);
//                        break;
//                    case 5:
//                        cmCustProtect.setSalerId(null);
//                        cmCustProtect.setBussdeptId(null);
//                        break;
//                    default:
//                        break;
//                }
//            }

        }
        boolean b = cmCustProtectService.updateBatchById(list, 50);

        return custIdList.size();
    }

	@Override
	public Page<CustProtectView> selectCmCustProtectPage(CmCustProtectQuery cmCustProtectQuery) {
		if(cmCustProtectQuery == null) {
			return new Page<>();
		}
		if (cmCustProtectQuery.getPageNum() == null || cmCustProtectQuery.getPageSize() == null) {
			cmCustProtectQuery.setPageNum(1);
			cmCustProtectQuery.setPageSize(10);
		}
		Page<CmCustProtect> cmCustProtectPage = cmCustProtectMapper.selectCmCustProtects(
			new Page<>(cmCustProtectQuery.getPageNum(),cmCustProtectQuery.getPageSize()), cmCustProtectQuery);
		if (CollectionUtils.isEmpty(cmCustProtectPage.getRecords()) || cmCustProtectPage.getTotal() == 0) {
			return new Page<>();
		}
		List<CustProtectView> custProtectViews = BeanCopyUtils.convertToVoList(cmCustProtectPage.getRecords(), CustProtectView.class);
		Page<CustProtectView> res = new Page<>();
		BeanUtils.copyProperties(cmCustProtectPage, res);
		res.setRecords(custProtectViews);
		return res;
	}

	/**
	 * 根据客户ID列表查询保护关系集合
	 * @param customerList 客户id集合
	 * @return 客户保护关系集合
	 */
	@Override
	public List<CustProtectView> selectByCustIds(List<String> customerList) {
		if (CollectionUtils.isEmpty(customerList)) {
			return Collections.emptyList();
		}
		List<CmCustProtect> entities = cmCustProtectService.lambdaQuery().in(CmCustProtect::getCustId, customerList).list();
		if (CollectionUtils.isEmpty(entities)) {
			return Collections.emptyList();
		}
		return BeanCopyUtils.convertToVoList(entities, CustProtectView.class);
	}



}




