package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;


/**
 * @description 权限
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName(value ="sma_privilege")
@Data
public class SmaPrivilege implements Serializable {
    /**
     * 
     */
    @TableId
    private String id;

    /**
     * 
     */
    private String roleId;

    /**
     * 
     */
    private String functionId;

    /**
     * 
     */
    private Integer dataPrivilege;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}