package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 调拨表
 * @TableName transfertable
 */
@TableName(value ="transfertable")
@Data
public class Transfertable implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户id
     */
    @TableField(value = "customer_id")
    private String customerId;

    /**
     * 客户名称
     */
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 调拨原因
     */
    @TableField(value = "transfer_reason")
    private String transferReason;

    /**
     * 证明信息url
     */
    @TableField(value = "proof_info_url")
    private String proofInfoUrl;

    /**
     * 申请时间
     */
    @TableField(value = "application_time")
    private Date applicationTime;

    /**
     * 申请人
     */
    @TableField(value = "applicant")
    private String applicant;

    /**
     * 处理时间
     */
    @TableField(value = "processing_time")
    private Date processingTime;

    /**
     * 处理人
     */
    @TableField(value = "processor")
    private String processor;

    /**
     * 处理状态(1.未处理 2.通过 3.驳回 4.超时自动处理)
     */
    @TableField(value = "processing_status")
    private Integer processingStatus;

    /**
     * 备注信息
     */
    @TableField(value = "remarks")
    private String remarks;


    /**
     * 申请人id
     */
    @TableField(value = "applicant_id")
    private String applicantId;

    /**
     * 数据插入时间
     */
    @TableField(value = "db_insert_time")
    private Date dbInsertTime;

    /**
     * 数据更新时间
     */
    @TableField(value = "db_update_time")
    private Date dbUpdateTime;

    private static final long serialVersionUID = 1L;


}