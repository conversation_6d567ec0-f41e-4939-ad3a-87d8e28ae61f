package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.SubSjAutoAssignRule;

import java.util.List;

/**
 * 分司商机自动分配规则Service接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
public interface SubSjAutoAssignRuleService extends IService<SubSjAutoAssignRule> {

    /**
     * 根据分司ID获取自动分配规则
     *
     * @param subId 分司ID
     * @return 自动分配规则
     */
    SubSjAutoAssignRule getBySubId(String subId);

    /**
     * 检查分司是否开启自动分配
     *
     * @param subId 分司ID
     * @return 是否开启自动分配
     */
    boolean isAutoAssignEnabled(String subId);

    /**
     * 获取所有开启自动分配的分司规则
     *
     * @return 自动分配规则列表
     */
    List<SubSjAutoAssignRule> getEnabledAutoAssignRules();

    /**
     * 更新权重
     *
     * @param id 规则ID
     * @return 是否更新成功
     */
    boolean updateWeight(Long id);

    /**
     * 开启分司自动分配
     *
     * @param subId 分司ID
     * @param subName 分司名称
     * @param operatorId 操作人ID
     * @return 是否开启成功
     */
    boolean enableAutoAssign(String subId, String subName, String operatorId);

    /**
     * 关闭分司自动分配
     *
     * @param subId 分司ID
     * @param operatorId 操作人ID
     * @return 是否关闭成功
     */
    boolean disableAutoAssign(String subId, String operatorId);
}
