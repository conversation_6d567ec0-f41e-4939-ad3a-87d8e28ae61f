package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2024/12/9 17:49
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("SMA_MARKET")
public class SmaMarket {

    private String id;

    /** 市场名称 */
    private String name;

    /** 市场名称 */
    private String code;

    /** 策略id(1时间，2顺序) */
    private Integer tacticeId;

}
