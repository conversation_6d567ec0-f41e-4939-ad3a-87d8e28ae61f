package com.ce.scrm.center.dao.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class EqixiuDataResultQuery implements Serializable {
    private String activityId;
    private String salerId;
    private String customerName;
    private String deptId;
    private String areaId;
    private String subId;
    private Integer lotteryTimes; // 0 表示没有抽奖次数，非 0 表示有抽奖次数
    private Integer isWin; // 0 表示未中奖，非 0 表示有中奖
    private Integer shareTimesSort; // 1 表示降序，0 表示升序
    private Integer lotteryTimesSort; // 1 表示降序，0 表示升序
    private Integer winTimesSort; // 1 表示降序，0 表示升序
    private Integer openTimesSort; // 1 表示降序，0 表示升序
    private String  activityType; //活动类型

    private Integer level;
    private String buId;
}