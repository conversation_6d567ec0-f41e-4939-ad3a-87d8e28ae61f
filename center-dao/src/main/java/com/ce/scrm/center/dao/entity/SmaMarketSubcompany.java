package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @version 1.0
 * @Description: 市场分司映射表
 * @Author: lijinpeng
 * @Date: 2024/12/11 10:37
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sma_market_subcompay")
public class SmaMarketSubcompany {

    private Integer id;

    /** 市场id */
    private String marketId;

    /** 分司id */
    private String subCompany;

    /** 类型(1专属，2周边，3共享) */
    private Integer type;

    /** 分公司的默认省code（仅type=1才有值） */
    private String defaultProvince;

    /** 分公司的默认市code（仅type=1才有值） */
    private String defaultCity;

    /** 分公司的默认区code（仅type=1才有值） */
    private String defaultArea;

}
