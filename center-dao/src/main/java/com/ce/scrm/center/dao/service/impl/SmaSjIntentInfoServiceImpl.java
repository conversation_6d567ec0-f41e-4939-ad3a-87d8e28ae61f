package com.ce.scrm.center.dao.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.SjIntentInfo;
import com.ce.scrm.center.dao.mapper.SmaSjIntentInfoMapper;
import com.ce.scrm.center.dao.service.SmaSjIntentInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cm_cust_protect(客户保护关系表)】的数据库操作Service实现
* @createDate 2024-06-07 17:29:37
*/
@Service
@Slf4j
@RequiredArgsConstructor
public class SmaSjIntentInfoServiceImpl extends ServiceImpl<SmaSjIntentInfoMapper, SjIntentInfo> implements SmaSjIntentInfoService {

	private final SmaSjIntentInfoMapper smaSjIntentInfoMapper;

	@Override
	public List<SjIntentInfo> selectListBySjCodes(List<String> sjCodeList) {
		if (CollectionUtils.isEmpty(sjCodeList)) {
			return Collections.emptyList();
		}
		return this.lambdaQuery().in(SjIntentInfo::getSjCode, sjCodeList).list();
	}
}




