package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 流失客户表
 *
 * @author: liyechao
 * date: 2024/9/2
 */
@Data
@TableName(value = "customer_loss_log")
public class CustomerLossLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 流失原因
     */
    private String reason;

    /**
     * 预计流转（流失）日期
     */
    private Date preDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    public CustomerLossLog() {
    }
}