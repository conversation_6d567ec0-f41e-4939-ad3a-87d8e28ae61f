package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.RecommendCust;

/**
 * <AUTHOR>
 * @description 推荐客户
 * @createDate 2024-08-13
 */
public interface RecommendCustService extends IService<RecommendCust> {

	/**
	 * 获取推荐客户数量
	 * @param recommendCustId 推荐客户ID
	 * @return 推荐客户数量
	 */
	long getRecommendCustCount(String recommendCustId);
}