package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 市场地区关系实体类
 * @Author: lijinpeng
 * @Date: 2024/12/9 17:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("SMA_MARKET_AREA")
public class SmaMarketArea implements Serializable {

    private Integer id;
    /**
     * 市场id
     */
    private String marketId;
    /**
     * 省code
     */
    private String provinceCode;
    /**
     * 市code
     */
    private String cityCode;
    /**
     *  区code
     */
    private String areaCode;
    /**
     * 省
     */
    private String provinceName;
    /**
     * 市
     */
    private String cityName;
    /**
     * 区
     */
    private String areaName;


}
