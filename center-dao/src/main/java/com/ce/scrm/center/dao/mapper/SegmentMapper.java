package com.ce.scrm.center.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.Segment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description 分群信息表Mapper
 * <AUTHOR>
 * @date 2025-02-27
 */
@Mapper
public interface SegmentMapper extends BaseMapper<Segment> {


    void updateDistributeCount(@Param("segmentId") String segmentId, @Param("distributeCount") Integer distributeCount);

	/**
	 * 非全员可见的、获取分司及对应状态下的分群列表
	 * @param page 分页对象
	 * @param subId 分司id
	 * @param empId 员工id
	 * @return 非全员可见的分群列表
	 */
	Page<Segment> getDiffSubIdAndStatusPage(Page<?> page, @Param("loginSubId") String subId, @Param("empId") String empId);

}
