package com.ce.scrm.center.dao.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/1
 */
@Data
public class SegmentUpdateEntity implements Serializable {

    /**
     * 分群id
     */
    private String segmentId;

    /**
     * 下发数量
     */
    private Integer distributeCount=1;

    /**
     * 保护数量
     */
    Integer protectedCount =0;

    /**
     * 签单数量
     */
    Integer signedCount =0;
}
