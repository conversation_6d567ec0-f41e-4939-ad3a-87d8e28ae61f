package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @description 角色组
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName(value ="sma_role_group")
@Data
public class SmaRoleGroup implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 
     */
    private String name;

    /**
     * 
     */
    private String describes;

    /**
     * 
     */
    private Integer orders;

    /**
     * 
     */
    private Integer state;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}