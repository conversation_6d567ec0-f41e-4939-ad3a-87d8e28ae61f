package com.ce.scrm.center.dao.service;

import com.ce.scrm.center.dao.entity.SmaMarket;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.query.SmaMarketQuery;
import com.ce.scrm.center.dao.entity.view.SmaMarketView;

import java.util.List;

/**
 * @description 市场 Service
 * <AUTHOR>
 * @date 2024-04-19
 */
public interface SmaMarketService extends IService<SmaMarket> {

    /*
     * @Description 根据条件查询市场表 一条数据
     * <AUTHOR>
     * @date 2024/12/10 15:26
     * @param condition
     * @return com.ce.scrm.center.dao.entity.vo.SmaMarketView
     */
    SmaMarketView selectOneByCondition(SmaMarketQuery condition);

    /*
     * @Description 根据条件查询市场表 一个结果集
     * <AUTHOR>
     * @date 2024/12/10 15:26
     * @param condition
     * @return java.util.List<com.ce.scrm.center.dao.entity.vo.SmaMarketView>
     */
    List<SmaMarketView> selectByCondition(SmaMarketQuery condition);

}
