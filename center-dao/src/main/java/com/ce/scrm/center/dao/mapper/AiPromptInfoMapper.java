package com.ce.scrm.center.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.AiPromptInfo;
import com.ce.scrm.center.dao.entity.AiPromptInfoPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/2/20 09:45
 */
public interface AiPromptInfoMapper extends BaseMapper<AiPromptInfo> {

    Page<AiPromptInfoPlus> getAiPromptInfoList(Page<?> page,
                                               @Param("startFlag") Integer startFlag,
                                               @Param("subId") String subId,
                                               @Param("areaId") String areaId,
                                               @Param("deptId") String deptId,
                                               @Param("buId") String buId,
                                               @Param("master") boolean master,
                                               @Param("companyList") List<String> companyList,
                                               @Param("promptTypeList") List<Integer> promptTypeList
    );


}
