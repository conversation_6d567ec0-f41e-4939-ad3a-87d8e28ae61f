package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * @description 合作申请
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName(value ="t_apply_cooperation")
@Data
public class ApplyCooperation implements Serializable {
    /**
     * 
     */
    @TableId
    private String id;

    /**
     * 申请合作人
     */
    private String cooperationSalerId;

    /**
     * 客户保护人
     */
    private String protectSalerId;

    /**
     * 客户ID
     */
    private String custId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 合作状态
     */
    private Integer state;

    /**
     * 申请人区域ID
     */
    private String areaId;

    /**
     * 申请人分公司ID
     */
    private String subId;

    /**
     * 申请人部门ID
     */
    private String deptId;

    /**
     * 保护人区域ID
     */
    private String protectAreaId;

    /**
     * 保护人分司ID
     */
    private String protectSubId;

    /**
     * 保护人部门ID
     */
    private String protectDeptId;

    /**
     * 超期时间
     */
    private Date exceedTime;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 
     */
    private Date createDate;

    /**
     * 
     */
    private Date updateDate;

    /**
     * 
     */
    private String createUser;

    /**
     * 
     */
    private String updateUser;

    /**
     * 附件
     */
    private String signFileIds;

    /**
     * 申诉原因
     */
    private String appealReason;

    /**
     * 审批号
     */
    private Long spNo;

    /**
     * 申请取消合作的商务id
     */
    private String applyCancelSalerId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 合作有效期，单位天
     */
    private Integer periodValidityDay;

}