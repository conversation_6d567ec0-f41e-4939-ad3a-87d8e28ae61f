package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * @description 不流失产品
 * <AUTHOR>
 * @date 2024-04-19
 */
@TableName(value ="t_not_lose_product")
@Data
public class NotLoseProduct implements Serializable {
    /**
     * 
     */
    @TableId
    private String id;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 产品大类
     */
    private String productFirstName;

    /**
     * 产品小类
     */
    private String productSecondName;

    /**
     * 产品名称
     */
    private String productName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}