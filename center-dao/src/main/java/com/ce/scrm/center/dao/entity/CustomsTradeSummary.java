package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description 海关统计数据
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@TableName("customs_trade_summary")
public class CustomsTradeSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 格式YYYY-MM
     */
    private String dataMonth;

    /**
     * 唯一商品标识
     */
    private String productCode;

    /**
     * 商品全称
     */
    private String productName;

    /**
     * 合作伙伴编码
     */
    private String partnerCode;

    /**
     * 合作伙伴全称
     */
    private String partnerName;

    /**
     * 第一数量
     */
    private String primaryQuantity;

    /**
     * 第一计量单位(千克)
     */
    private String primaryUnit;

    /**
     * 第二数量
     */
    private String secondaryQuantity;

    /**
     * 第二计量单位
     */
    private String secondaryUnit;

    /**
     * 总交易量(单位美元)
     */
    private BigDecimal total;

    public CustomsTradeSummary() {}
}
