package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AiChannelConfig implements Serializable {


    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String channelName;

    /**
     * 调用地址
     */
    private String channelUrl;

    /**
     * 鉴权key
     */
    private String authKey;

    /**
     * 响应速度
     */
    private Integer speed;

    private Integer orderNum;

    private Integer qpsLimit;

    private Long modelTokenLimit;

    private Integer status;

    private String extraParam;

    private Integer deleteFlag;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
