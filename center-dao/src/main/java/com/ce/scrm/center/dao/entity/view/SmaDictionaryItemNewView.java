package com.ce.scrm.center.dao.entity.view;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 字典表view
 * @Author: lijinpeng
 * @Date: 2025/1/17 17:49
 */
@Data
public class SmaDictionaryItemNewView implements Serializable {

    /** id */
    @TableId
    private String id;

    /** 字典ID */
    private String dictionaryId;

    /** 父ID */
    private String parentId;

    /** 版本号 */
    private Integer version;

    /** 编码 */
    private String code;

    /** 名称 */
    private String name;

    /** 排序字段 */
    private Integer displayOrder;

    /** 描述字段 */
    private String description;


    private String itemValue;

    /** 状态 */
    private Integer state;

}
