package com.ce.scrm.center.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * @description 海关产品数据
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
public class CustomsProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 商品标识 8位
     */
    private String productCode;

    /**
     * 商品全称
     */
    private String productName;

    /**
     * 商品标识 6位
     */
    private String productCodeSix;

    /**
     * 商品标识 4位
     */
    private String productCodeFour;

    /**
     * 商品标识 2位
     */
    private String productCodeTwo;

    public CustomsProduct() {}
}
