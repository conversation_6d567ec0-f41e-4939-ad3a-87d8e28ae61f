package com.ce.scrm.center.dao.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.SmaMarketSubcompany;
import com.ce.scrm.center.dao.entity.query.SmaMarketSubcompanyQuery;
import com.ce.scrm.center.dao.entity.view.SmaMarketSubcompanyView;
import com.ce.scrm.center.dao.mapper.SmaMarketSubcompanyMapper;
import com.ce.scrm.center.dao.service.SmaMarketSubcompanyService;
import com.ce.scrm.center.dao.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @version 1.0
 * @Description: 市场分司映射
 * @Author: lijinpeng
 * @Date: 2024/12/11 10:40
 */
@Service
@Slf4j
public class SmaMarketSubcompanyServiceImpl extends ServiceImpl<SmaMarketSubcompanyMapper, SmaMarketSubcompany>
        implements SmaMarketSubcompanyService {

    @Override
    public SmaMarketSubcompanyView selectOneByCondition(SmaMarketSubcompanyQuery condition) {
        List<SmaMarketSubcompanyView> selectByCondition = selectByCondition(condition);
        if(CollectionUtils.isEmpty(selectByCondition)) {
            return null;
        }else {
            return selectByCondition.get(0);
        }
    }

    @Override
    public List<SmaMarketSubcompanyView> selectByCondition(SmaMarketSubcompanyQuery condition) {
        log.info("根据条件查询市场分司映射关系,参数condition={}", JSON.toJSONString(condition));
        if(condition == null) {
            return Collections.emptyList();
        }
        LambdaQueryChainWrapper<SmaMarketSubcompany> lambdaQueryChainWrapper = this.lambdaQuery();
        if(condition.getId() != null) {
            lambdaQueryChainWrapper.eq(SmaMarketSubcompany::getId, condition.getId());
        }
        if(StringUtils.isNotBlank(condition.getMarketId())) {
            lambdaQueryChainWrapper.eq(SmaMarketSubcompany::getMarketId, condition.getMarketId());
        }
        if(StringUtils.isNotBlank(condition.getSubCompany())) {
            lambdaQueryChainWrapper.eq(SmaMarketSubcompany::getSubCompany, condition.getSubCompany());
        }
        if (CollectionUtils.isNotEmpty(condition.getSubIdList())) {
            lambdaQueryChainWrapper.in(SmaMarketSubcompany::getSubCompany, condition.getSubIdList());
        }
        if (CollectionUtils.isNotEmpty(condition.getMarketIdList())) {
            lambdaQueryChainWrapper.in(SmaMarketSubcompany::getMarketId, condition.getMarketIdList());
        }
        List<SmaMarketSubcompany> smaMarketSubcompanyList = lambdaQueryChainWrapper.list();
        List<SmaMarketSubcompanyView> result = BeanCopyUtils.convertToVoList(smaMarketSubcompanyList, SmaMarketSubcompanyView.class);
        return result;
    }
}
