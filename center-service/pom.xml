<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>scrm-center</artifactId>
        <groupId>com.ce.scrm</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>center-service</artifactId>
    <name>center-service</name>
    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ce.scrm</groupId>
            <artifactId>center-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm</groupId>
            <artifactId>center-support</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>scrm-common-base</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-emp-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.extend</groupId>
            <artifactId>extend-dubbo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.customer</groupId>
            <artifactId>customer-dubbo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-rbac-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-contract-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-newcustclue-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.performanceCenter</groupId>
            <artifactId>performance-remoteservice-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-instance-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.extend</groupId>
            <artifactId>cdp-dubbo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-achievement-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>cn.ce.cesupport</groupId>-->
<!--            <artifactId>service-achievement-api</artifactId>-->
<!--            <version>0.0.1-SNAPSHOT</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15to18</artifactId>
            <version>1.69</version>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-sma-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ce.cecloud</groupId>
            <artifactId>appservice-business-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.ce.cloud</groupId>
            <artifactId>ce-operation-system-customer-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.ce.cloud</groupId>
                    <artifactId>spring-cloud-ce-dependencies</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ce.call.center</groupId>
            <artifactId>call-dubbo-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-newcustomer-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>
        <dependency>
            <groupId>com.ti-net</groupId>
            <artifactId>clink-serversdk</artifactId>
            <version>3.0.30</version>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-gj-api</artifactId>
        </dependency>
    </dependencies>
</project>
