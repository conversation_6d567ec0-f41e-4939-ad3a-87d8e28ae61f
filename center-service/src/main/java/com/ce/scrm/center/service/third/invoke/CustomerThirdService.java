package com.ce.scrm.center.service.third.invoke;

import cn.ce.cesupport.cma.enums.SkbTagFlag7Enum;
import cn.ce.cesupport.cma.enums.SkbTagFlag8Enum;
import cn.ce.cesupport.cma.enums.SkbTagTechCompanyEnum;
import cn.ce.cesupport.cma.vo.CustomerTag;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.service.PotentialCustomerMarketingRulesService;
import com.ce.scrm.center.service.business.ProtectBusiness;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.business.abm.CustomerLeadsBusiness;
import com.ce.scrm.center.service.business.entity.dto.ProtectBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerLeadsAddDto;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerLeadsPageDto;
import com.ce.scrm.center.service.business.entity.response.BusinessResult;
import com.ce.scrm.center.service.business.entity.view.CmCustProtectBusinessView;
import com.ce.scrm.center.service.business.entity.view.CustomerLeadsDubboView;
import com.ce.scrm.center.service.business.entity.view.CustomerLeadsView;
import com.ce.scrm.center.service.business.entity.view.HomePageCallRateWebView;
import com.ce.scrm.center.service.third.entity.dto.customer.*;
import com.ce.scrm.center.service.third.entity.view.*;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.service.yml.ThirdCustomerConfig;
import com.ce.scrm.customer.dubbo.api.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.api.ICustomerTagDubbo;
import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.dubbo.entity.dto.*;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerUpdateLeadsInfoDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户三方业务
 * <AUTHOR>
 * @date 2024/2/2 14:46
 * @version 1.0.0
 **/
@Slf4j
@Service
public class CustomerThirdService {

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private ICustomerDubbo customerDubbo;

    @Resource
    private ThirdCustomerConfig thirdCustomerConfig;

	@DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
	private ICustomerTagDubbo customerTagDubbo;
    @Autowired
    private CustomerLeadsBusiness customerLeadsBusiness;
    @Resource
    private ProtectBusiness protectBusiness;
    @Resource
    private EmployeeThirdService employeeThirdService;
    @Resource
    private SendWxMessage sendWxMessage;
    @Resource
    private PotentialCustomerMarketingRulesService potentialCustomerMarketingRulesService;
    @Resource
    private CustomerLinkmanThirdService customerLinkmanThirdService;


    /**
     * 添加客户
     * @param customerId  客户Id
     * @param customerName  客户名称
     * @param presentStage  客户阶段
     * @param createWay 客户创建方式
     * @param operator  操作人
     * <AUTHOR>
     * @date 2024/2/29 13:37
     * @return com.ce.scrm.extend.service.third.entity.view.CustomerAddThirdView
     **/
    public CustomerAddThirdView addCustomer(String customerId, String customerName, Integer presentStage, Integer createWay, String operator) {
        EnterpriseCustomerSaveDubboDto enterpriseCustomerSaveDubboDto = new EnterpriseCustomerSaveDubboDto();
        // enterpriseCustomerSaveDubboDto.setCustomerId(customerId);
        enterpriseCustomerSaveDubboDto.setCustomerName(customerName);
        enterpriseCustomerSaveDubboDto.setPresentStage(presentStage);
        enterpriseCustomerSaveDubboDto.setCreateWay(createWay);
        enterpriseCustomerSaveDubboDto.setOperator(operator);
        setSignData(enterpriseCustomerSaveDubboDto);
        DubboResult<CustomerAddDubboView> customerAddDubboViewDubboResult = customerDubbo.saveEnterpriseCustomer(enterpriseCustomerSaveDubboDto);
        CustomerAddDubboView customerAddDubboView;
        CustomerAddThirdView customerAddThirdView = new CustomerAddThirdView();
        if (!customerAddDubboViewDubboResult.checkSuccess()) {
            log.warn("添加客户失败，调用客户dubbo异常，参数为为:{}，返回数据为:{}", JSON.toJSONString(enterpriseCustomerSaveDubboDto), JSON.toJSONString(customerAddDubboViewDubboResult));
            customerAddThirdView.setErrMsg("调用客户dubbo异常");
            return customerAddThirdView;
        }
        if ((customerAddDubboView = customerAddDubboViewDubboResult.getData()) == null || StrUtil.isBlank(customerId = customerAddDubboView.getCustomerId())) {
            log.warn("添加客户失败，无法查询到当前客户，确认客户名称的真实性，参数为为:{}，返回数据为:{}", JSON.toJSONString(enterpriseCustomerSaveDubboDto), JSON.toJSONString(customerAddDubboViewDubboResult));
            customerAddThirdView.setErrMsg("无法查询到当前客户，确认客户名称的真实性");
            return customerAddThirdView;
        }
        customerAddThirdView.setCustomerId(customerId);
        return customerAddThirdView;
    }

    /**
     * 获取客户数据
     * @param customerId    客户ID
     * <AUTHOR>
     * @date 2024/2/2 15:10
     * @return java.util.Optional<com.ce.scrm.extend.service.third.entity.view.CustomerDataThirdView>
     **/
    public Optional<CustomerDataThirdView> getCustomerData(String customerId) {
        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setCustomerId(customerId);
        setSignData(customerDetailDubboDto);
        DubboResult<CustomerDubboView> customerDubboViewDubboResult = customerDubbo.detail(customerDetailDubboDto);
        CustomerDubboView customerDubboView;
        if (!customerDubboViewDubboResult.checkSuccess() || (customerDubboView = customerDubboViewDubboResult.getData()) == null) {
            log.warn("根据客户ID获取客户信息，返回数据为空，客户ID为:{}，返回数据为:{}", customerId, JSON.toJSONString(customerDubboViewDubboResult));
            return Optional.empty();
        }
        CustomerDataThirdView customerDataThirdView = CglibUtil.copy(customerDubboView, CustomerDataThirdView.class);
        if (customerDataThirdView!=null){
            List<CustomerTag> tagList= getCustomerTags(customerDubboView);
            customerDataThirdView.setCustomerTags(tagList);
        }
        return Optional.of(customerDataThirdView);
    }

    /**
     * 根据客户名称获取客户信息
     * @param customerName 客户名称
     * <AUTHOR>
     * @date 2024/2/29 09:04
     * @return java.util.Optional<com.ce.scrm.extend.service.third.entity.view.CustomerDataThirdView>
     **/
    public Optional<CustomerDataThirdView> getCustomerDataByCustomerName(String customerName) {
        CustomerConditionDubboDto customerConditionDubboDto = new CustomerConditionDubboDto();
        customerConditionDubboDto.setCustomerName(customerName);
        setSignData(customerConditionDubboDto);
        DubboResult<List<CustomerDubboView>> customerDubboByCondition = customerDubbo.findByCondition(customerConditionDubboDto);
        List<CustomerDubboView> customerDubboViewList;
        if (!customerDubboByCondition.checkSuccess()) {
            log.error("根据客户名称获取客户信息失败，客户名称为:{}，返回数据为:{}", customerName, JSON.toJSONString(customerDubboByCondition));
            throw new RuntimeException("根据客户名称获取客户信息失败");
        }
        if (CollectionUtil.isEmpty(customerDubboViewList = customerDubboByCondition.getData())) {
            log.warn("根据客户名称获取客户信息，客户不存在，客户名称为:{}，返回数据为:{}", customerName, JSON.toJSONString(customerDubboByCondition));
            return Optional.empty();
        }
        return Optional.of(CglibUtil.copy(customerDubboViewList.get(0), CustomerDataThirdView.class));
    }

    /*
     * @Description 根据条件获取客户信息
     * <AUTHOR>
     * @date 2024/12/9 11:26
     * @param customerDataThirdDto
     * @return java.util.Optional<java.util.List<com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView>>
     */
    public List<CustomerDataThirdView> findByCondition(CustomerDataThirdDto customerDataThirdDto) {
        log.info("根据条件获取客户信息，customerDataThirdDto:{}", JSON.toJSONString(customerDataThirdDto));
        if(customerDataThirdDto == null) {
            return Collections.emptyList();
        }
        CustomerConditionDubboDto customerConditionDubboDto = BeanCopyUtils.convertToVo(customerDataThirdDto, CustomerConditionDubboDto.class);
        setSignData(customerConditionDubboDto);
        DubboResult<List<CustomerDubboView>> customerDubboByCondition = customerDubbo.findByCondition(customerConditionDubboDto);
        if (!customerDubboByCondition.checkSuccess()) {
            log.error("根据条件获取客户信息，customerDataThirdDto:{}，返回数据为:{}", JSON.toJSONString(customerDataThirdDto), JSON.toJSONString(customerDubboByCondition));
            throw new RuntimeException("根据条件获取客户信息失败");
        }
        if (CollectionUtil.isEmpty(customerDubboByCondition.getData())) {
            log.warn("根据条件获取客户信息，客户不存在，customerDataThirdDto:{}，返回数据为:{}", JSON.toJSONString(customerDataThirdDto), JSON.toJSONString(customerDubboByCondition));
            return Collections.emptyList();
        }
        List<CustomerDataThirdView> customerDataThirdViews = BeanCopyUtils.convertToVoList(customerDubboByCondition.getData(), CustomerDataThirdView.class);
        log.info("根据条件获取客户信息,result={}", JSON.toJSONString(customerDataThirdViews));
        return customerDataThirdViews;
    }

    /**
     * 设置调用签名数据
     * @param signData  签名数据
     * <AUTHOR>
     * @date 2024/3/12 11:29
     **/
    public void setSignData(SignData signData) {
        signData.setSourceKey(thirdCustomerConfig.getKey());
        signData.setSourceSecret(thirdCustomerConfig.getSecret());
    }

    public HomePageCallRateWebView getCallRate(String loginPosition, String loginAreaId, String loginSubId, String loginOrgId, String loginEmployeeId) {

        LoginInfoDubboDto loginInfoDubboDto = new LoginInfoDubboDto();
        loginInfoDubboDto.setLoginPosition(loginPosition);
        loginInfoDubboDto.setLoginAreaId(loginAreaId);
        loginInfoDubboDto.setLoginSubId(loginSubId);
        loginInfoDubboDto.setLoginOrgId(loginOrgId);
        loginInfoDubboDto.setLoginEmployeeId(loginEmployeeId);

        setSignData(loginInfoDubboDto);

        DubboResult<HomePageCallRateDubboView> callRate = customerDubbo.getCallRate(loginInfoDubboDto);
        if(!callRate.checkSuccess()) {
            log.warn("获取客户拜访率失败，调用客户dubbo异常，参数为:{},返回数据位:{}", JSON.toJSONString(loginInfoDubboDto), JSON.toJSONString(callRate));
            return null;
        }
        return BeanUtil.copyProperties(callRate.getData(), HomePageCallRateWebView.class);
    }

    /**
     * 获取打卡内页客户列表
     * @param callDetailsDubboDto
     * @return
     */
    public DubboPageInfo<CustomerDubboView> getCallDetails(CallDetailsDubboDto callDetailsDubboDto) {
        setSignData(callDetailsDubboDto);
        DubboResult<DubboPageInfo<CustomerDubboView>> callDetails = customerDubbo.getCallDetails(callDetailsDubboDto);
        if(!callDetails.checkSuccess()) {
            log.warn("获取客户拜访打卡详情，调用客户dubbo异常，参数为:{},返回数据位:{}", JSON.toJSONString(callDetailsDubboDto), JSON.toJSONString(callDetails));
            return new DubboPageInfo<>();
        }
        return callDetails.getData();
    }

    public Boolean monitorProtectInfoConsumer(CustomerUpdateDubboDto customerUpdateDubboDto) {
        setSignData(customerUpdateDubboDto);
        DubboResult<Boolean> update = customerDubbo.monitorProtectInfoConsumer(customerUpdateDubboDto);
        if (!update.checkSuccess()) {
            log.warn("monitorProtectInfoConsumer 更新客户失败，调用客户dubbo异常，参数为为:{}，返回数据为:{}", JSON.toJSONString(customerUpdateDubboDto), JSON.toJSONString(update));
            return false;
        }
        return update.getData();
    }

    /**
     * 更新客户表数据 只更新CustType字段
     * @param customerUpdateDubboDto
     * @return
     */
    public Boolean updateCustomerCustType(CustomerUpdateDubboDto customerUpdateDubboDto) {
        setSignData(customerUpdateDubboDto);
        DubboResult<Boolean> update = customerDubbo.updateCustomerCustType(customerUpdateDubboDto);
        if (!update.checkSuccess()) {
            log.warn("updateCustomerCustType 更新客户失败，调用客户dubbo异常，参数为为:{}，返回数据为:{}", JSON.toJSONString(customerUpdateDubboDto), JSON.toJSONString(update));
            return false;
        }
        return update.getData();
    }

    /**
     * 获取客户身上的标签列表
     * @param customerVo
     * @return
     */
    private List<CustomerTag> getCustomerTags(CustomerDubboView customerVo){
        List<CustomerTag> customerTags = new ArrayList<>();
        // 实例相关的标
        if (Objects.equals(customerVo.getTagMenhuRelated(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("门户连带客户");
            customerTags.add(tag);
        }
        if (Objects.equals(customerVo.getTagEcoToMenhu(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("生态转门户客户");
            customerTags.add(tag);
        }
        if (Objects.equals(customerVo.getTagEcoCust(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("生态客户");
            customerTags.add(tag);
        }
        if (Objects.equals(customerVo.getTagMenhuDigital(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("数字版本门户客户");
            customerTags.add(tag);
        }
        if (Objects.equals(customerVo.getTagMenhu2023(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("2023版本门户客户");
            customerTags.add(tag);
        }
        if (Objects.equals(customerVo.getTagMenhuLowver(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("低版本门户客户");
            customerTags.add(tag);
        }
        if (Objects.equals(customerVo.getTagMenhuUpgradeableUpgrade(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("应升已升客户");
            customerTags.add(tag);
        }
        if (Objects.equals(customerVo.getTagMenhuUpgradeable(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("门户应升级客户");
            customerTags.add(tag);
        }
        if (Objects.equals(customerVo.getTagMenhuRenewableRenew(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("门户应续已续客户");
            customerTags.add(tag);
        }
        if (Objects.equals(customerVo.getTagMenhuRenewable(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("门户应续客户");
            customerTags.add(tag);
        }
        if (Objects.equals(customerVo.getTagCrossBuy(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("交叉购买客户");
            customerTags.add(tag);
        }
        if (Objects.equals(customerVo.getTagPureMenhu(),1)){
            CustomerTag tag = new CustomerTag();
            tag.setTagName("纯门户客户");
            customerTags.add(tag);
        }
        /**
         * 搜客宝 1:规模工业，2:规上服务业，3:规上建筑业，4:规上批发零售业，5:规上住宿餐饮业，6:规上房地产开发与经营业
         */
        SkbTagFlag7Enum skbTagFlag7Enum = SkbTagFlag7Enum.getSkbTagFlag7EnumByCode(customerVo.getTagFlag7());
        if (skbTagFlag7Enum!=null){
            CustomerTag tag = new CustomerTag();
            tag.setTagName(skbTagFlag7Enum.getLable());
            customerTags.add(tag);
        }
        /**
         * 搜客宝 行业 1:律师，2:学校，3:医院
         */
        SkbTagFlag8Enum skbTagFlag8Enum = SkbTagFlag8Enum.getSkbTagFlag8EnumByCode(customerVo.getTagFlag8());
        if (skbTagFlag8Enum!=null){
            CustomerTag tag = new CustomerTag();
            tag.setTagName(skbTagFlag8Enum.getLable());
            customerTags.add(tag);
        }
        /**
         * 搜客宝 科技型企业
         */
        if (StringUtils.isNotBlank(customerVo.getTagTechcompany())){
            List<String> tagTechcompanyList = Arrays.stream(customerVo.getTagTechcompany().split(",")).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tagTechcompanyList)){
                for (String tagTechcompany:tagTechcompanyList){
                    SkbTagTechCompanyEnum skbTagTechCompanyEnum = SkbTagTechCompanyEnum.getSkbTagTechCompanyEnumByCode(tagTechcompany);
                    if (skbTagTechCompanyEnum!=null){
                        CustomerTag tag = new CustomerTag();
                        tag.setTagName(skbTagTechCompanyEnum.getLable());
                        customerTags.add(tag);
                    }
                }
            }
        }
        return customerTags;
    }

    /*
     * @Description 分页查询客户信息
     * <AUTHOR>
     * @date 2025/1/10 14:36
     * @param customerPageThirdDto
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
     */
    public DubboPageInfo<CustomerDubboView> pageList(CustomerPageThirdDto customerPageThirdDto) {
        log.info("分页查询客户信息 , customerPageThirdDto={}", JSON.toJSONString(customerPageThirdDto));
        CustomerPageDubboDto customerPageDubboDto = BeanCopyUtils.convertToVo(customerPageThirdDto, CustomerPageDubboDto.class);
        setSignData(customerPageDubboDto);
        DubboResult<DubboPageInfo<CustomerDubboView>> dubboPageInfoDubboResult = customerDubbo.pageList(customerPageDubboDto);
        if (!dubboPageInfoDubboResult.checkSuccess()) {
            log.error("分页查询客户信息失败，customerPageThirdDto:{}，返回数据为:{}", JSON.toJSONString(customerPageThirdDto), JSON.toJSONString(dubboPageInfoDubboResult));
            throw new RuntimeException("分页查询客户信息失败");
        }
        if (dubboPageInfoDubboResult.getData() == null || CollectionUtil.isEmpty(dubboPageInfoDubboResult.getData().getList())) {
            log.warn("分页查询客户信息失败，customerPageThirdDto:{}，返回数据为:{}", JSON.toJSONString(customerPageThirdDto), JSON.toJSONString(dubboPageInfoDubboResult));
            return new DubboPageInfo<>();
        }
        return dubboPageInfoDubboResult.getData();
    }

    /*
     * @Description 根据条件查询客户列表  加了限制 custIdList最多200
     * <AUTHOR>
     * @date 2025/1/17 10:30
     * @param customerPageThirdDto
     * @return java.util.List<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
     */
    public List<CustomerDubboView> getListByCondition(CustomerPageThirdDto customerPageThirdDto) {
        log.info("根据条件查询客户列表 , customerPageThirdDto={}", JSON.toJSONString(customerPageThirdDto));
        CustomerPageDubboDto customerPageDubboDto = BeanCopyUtils.convertToVo(customerPageThirdDto, CustomerPageDubboDto.class);
        setSignData(customerPageDubboDto);
        customerPageDubboDto.setPageNum(1);
        customerPageDubboDto.setPageSize(200);
        DubboResult<DubboPageInfo<CustomerDubboView>> dubboPageInfoDubboResult = customerDubbo.pageList(customerPageDubboDto);
        if (!dubboPageInfoDubboResult.checkSuccess()) {
            log.error("根据条件查询客户列表，customerPageThirdDto:{}，返回数据为:{}", JSON.toJSONString(customerPageThirdDto), JSON.toJSONString(dubboPageInfoDubboResult));
            throw new RuntimeException("分页查询客户信息失败");
        }
        if (dubboPageInfoDubboResult.getData() == null || CollectionUtil.isEmpty(dubboPageInfoDubboResult.getData().getList())) {
            log.warn("根据条件查询客户列表，customerPageThirdDto:{}，返回数据为:{}", JSON.toJSONString(customerPageThirdDto), JSON.toJSONString(dubboPageInfoDubboResult));
            return Collections.emptyList();
        }
        return dubboPageInfoDubboResult.getData().getList();
    }

    /**
     * 根据客户id查询客户分享的临时信息
     * @param queryThirdDto
     * @return
     */
    public List<CustomerShareTemporaryThirdView> getCustomerShareTemporaryByCustomerId(CustomerShareQueryThirdDto queryThirdDto) {

        log.info("getCustomerShareTemporaryByCustomerId,queryThirdDto={}", JSON.toJSONString(queryThirdDto));
        CustomerShareQueryDubboDto queryDubboDto = BeanCopyUtils.convertToVo(queryThirdDto, CustomerShareQueryDubboDto.class);
        DubboResult<List<CustomerShareTemporaryDubboView>> dubboPageInfoDubboResult = customerDubbo.getCustomerShareTemporaryByCustomerId(queryDubboDto);
        if (!dubboPageInfoDubboResult.checkSuccess()) {
            throw new RuntimeException("分页查询客户分享失败");
        }
        if (dubboPageInfoDubboResult.getData() == null || CollectionUtil.isEmpty(dubboPageInfoDubboResult.getData())) {
            return Collections.emptyList();
        }
        List<CustomerShareTemporaryThirdView> result = BeanCopyUtils.convertToVoList(dubboPageInfoDubboResult.getData(), CustomerShareTemporaryThirdView.class);
        return result;

    }

	/**
	 * 添加leads
	 * @param addLeadsDtos 添加leads dubbo参数
	 * @return 是否添加成功
	 */
	public CustomerLeadsThirdView addCustomerLeads(CustomerLeadsAddThirdDto addLeadsDtos) {
		if (Objects.isNull(addLeadsDtos)) {
			log.error("addCustomerLead，参数为空, leads添加失败");
			return null;
		}
		CustomerLeadsAddDto customerLeadsAddDubboDto = BeanCopyUtils.convertToVo(addLeadsDtos, CustomerLeadsAddDto.class);
        //获取leads code
        List<CustomerLeadsAddDto> dtoList = new ArrayList<>();
        dtoList.add(customerLeadsAddDubboDto);
        customerLeadsBusiness.assembleLeadsParams(dtoList,2,4);
        //添加精准联系人
        CustomerContactPersonThirdDto customerContactPersonThirdDto = new CustomerContactPersonThirdDto();
        customerContactPersonThirdDto.setContactPersonName(customerLeadsAddDubboDto.getLinkmanName());
        customerContactPersonThirdDto.setPhone(customerLeadsAddDubboDto.getMobile());
        customerContactPersonThirdDto.setEmail(customerLeadsAddDubboDto.getEmail());
        customerContactPersonThirdDto.setOperator(customerLeadsAddDubboDto.getCreatedId());
        customerContactPersonThirdDto.setCustomerId(customerLeadsAddDubboDto.getCustomerId());
        String contactPersonId = customerLinkmanThirdService.addContractPerson(customerContactPersonThirdDto);
        log.info("添加联系人成功, contactPersonId={}", contactPersonId);
        customerLeadsAddDubboDto.setContactId(contactPersonId);

        BusinessResult<Long> longBusinessResult = customerLeadsBusiness.addCustomerLead(customerLeadsAddDubboDto);
        if (!longBusinessResult.checkSuccess()) {
            log.error("addCustomerLead，创建客户leads失败，参数为:{}，返回数据为:{}", JSON.toJSONString(addLeadsDtos), JSON.toJSONString(longBusinessResult));
            return null;
        }
        CustomerLeadsThirdView customerLeadsThirdView = BeanCopyUtils.convertToVo(customerLeadsAddDubboDto, CustomerLeadsThirdView.class);
        customerLeadsThirdView.setId(longBusinessResult.getData());
        log.info("leads添加成功,leadsId={}", longBusinessResult.getData());
        ProtectBusinessDto protectBusinessDto = new ProtectBusinessDto();
        protectBusinessDto.setCustId(customerLeadsAddDubboDto.getCustomerId());
        Optional<CmCustProtectBusinessView> custProtect = protectBusiness.getCustProtect(protectBusinessDto);
        if (custProtect.isPresent()) {
            CmCustProtectBusinessView cmCustProtectBusinessView = custProtect.get();
            if ( !cmCustProtectBusinessView.getStatus().equals(4)) {
                //发送企微给该商务
                //$客户名称$ 有新的线索数据产生：$Leads来源说明$，请及时关注
                Optional<EmployeeDataThirdView> employeeData = employeeThirdService.getEmployeeData(cmCustProtectBusinessView.getSalerId());
                if (employeeData.isPresent()) {
                    EmployeeDataThirdView employeeDataThirdView = employeeData.get();
                    customerLeadsThirdView.setSalerName(employeeDataThirdView.getName());
                    String msg = customerLeadsAddDubboDto.getCustomerName()+"有新的线索数据产生："+customerLeadsAddDubboDto.getLeadsDesc() + ",请及时关注";
                    sendWxMessage.sendMessage(employeeDataThirdView.getId(), msg);
                    customerLeadsThirdView.setIsProtect("1");
                }
            }else {
                customerLeadsBusiness.executeDistribute(customerLeadsAddDubboDto.getCustomerId(), customerLeadsAddDubboDto.getCreatedId(), Collections.singletonList(longBusinessResult.getData()));
            }
        }else {
            //创建保护关系
            customerLeadsBusiness.executeDistribute(customerLeadsAddDubboDto.getCustomerId(), customerLeadsAddDubboDto.getCreatedId(),Collections.singletonList(longBusinessResult.getData()));
        }

		return customerLeadsThirdView;
	}


	/**
	 * 获取客户leads列表
	 * @param customerId 客户id
	 * @return leads列表
	 */
	public List<CustomerLeadsThirdView> getLeadsByCustomerId(String customerId) {
		if (StringUtils.isBlank(customerId)) {
			log.error("customerId为空, leads列表失败");
			return Collections.emptyList();
		}
        List<CustomerLeadsView> listByCustomerId = customerLeadsBusiness.getListByCustomerId(customerId);
		if (CollectionUtils.isEmpty(listByCustomerId)) {
			log.info("获取客户leads列表结果为空, customerId = {}", customerId);
			return Collections.emptyList();
		}
		return BeanCopyUtils.convertToVoList(listByCustomerId, CustomerLeadsThirdView.class);
	}

	/**
	 * 获取客户leads分页列表
	 * @param customerId 客户id
	 * @return leads列表
	 */
	public Page<CustomerLeadsDubboView> getLeadsPageByCustomerId(String customerId, Integer pageNum, Integer pageSize) {
		Page<CustomerLeadsDubboView> page = Page.of(pageNum, pageSize);
		if (StringUtils.isBlank(customerId)) {
			return page;
		}
        CustomerLeadsPageDto customerLeadsPageDto = new CustomerLeadsPageDto();
		customerLeadsPageDto.setCustomerId(customerId);
		customerLeadsPageDto.setPageNum(pageNum);
		customerLeadsPageDto.setPageSize(pageSize);
        Page<CustomerLeadsView> pageByCustomerId = customerLeadsBusiness.getPageByCustomerId(customerLeadsPageDto);
        if (CollectionUtils.isEmpty(pageByCustomerId.getRecords())) {
			log.info("获取客户leads列表结果为空, customerId = {}", customerId);
			return page;
		}
		page.setTotal(pageByCustomerId.getTotal());
		page.setPages(pageByCustomerId.getPages());
		List<CustomerLeadsDubboView> customerLeadsDubboViews = BeanCopyUtils.convertToVoList(pageByCustomerId.getRecords(), CustomerLeadsDubboView.class);
		page.setRecords(customerLeadsDubboViews);
		return page;
	}

    /**
     * <AUTHOR>
     * @date 2025/8/5 20:45:03
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo<com.ce.scrm.customer.dubbo.entity.view.CustomerESDubboView>
     * @desc 从客户ES中获取客户数据
     */
    public DubboPageInfo<CustomerESDubboView> pageListFromCustomerES(CustomerESPageDubboDto customerESPageDubboDto){

        setSignData(customerESPageDubboDto);
        DubboResult<DubboPageInfo<CustomerESDubboView>> result = customerDubbo.customerESMatchByCondition(customerESPageDubboDto);
        if(!result.checkSuccess()) {
            log.error("获取客户ES中客户数据Dubbo异常，请求参数:{},返回数据:{}", JSON.toJSONString(customerESPageDubboDto), JSON.toJSONString(result));
            return new DubboPageInfo<>();
        }
        return result.getData();

    }

	/**
	 * 添加客户，无搜客宝校验
	 * @param customerAddNotValidThirdDto
	 */
	public CustomerAddDubboView addCustomerNotSkbValid(CustomerAddNotValidThirdDto customerAddNotValidThirdDto) {
		if (customerAddNotValidThirdDto == null) {
			log.error("添加客户，无搜客宝校验，入参为空");
			return null;
		}
		CustomerAddDubboDto customerAddDubboDto = BeanCopyUtils.convertToVo(customerAddNotValidThirdDto, CustomerAddDubboDto.class);
		customerAddDubboDto.setSourceKey(thirdCustomerConfig.getKey());
		customerAddDubboDto.setSourceSecret(thirdCustomerConfig.getSecret());
		try {
			DubboResult<CustomerAddDubboView> result = customerDubbo.add(customerAddDubboDto);
			if(!result.checkSuccess()) {
				log.error("添加客户，无搜客宝校验，请求参数:{},返回数据:{}", JSON.toJSONString(customerAddDubboDto), JSON.toJSONString(result));
				return null;
			}
			return result.getData();
		} catch (Exception e) {
			log.error("调用dubbo接口customerDubbo#add异常={}", e.getMessage());
		}
		return null;
	}

	/**
	 * 更新customer表的leads信息
	 * @param updateLeadsInfoThirdDto 客户表的leads信息
	 * @return 是否更新成功
	 */
	public Boolean updateCustLeadsInfoByCid(CustomerUpdateLeadsInfoThirdDto updateLeadsInfoThirdDto) {
		if (updateLeadsInfoThirdDto == null) {
			return false;
		}
		CustomerUpdateLeadsInfoDubboDto dubboReqDto = BeanCopyUtils.convertToVo(updateLeadsInfoThirdDto, CustomerUpdateLeadsInfoDubboDto.class);
		dubboReqDto.setSourceKey(thirdCustomerConfig.getKey());
		dubboReqDto.setSourceSecret(thirdCustomerConfig.getSecret());
		try {
			log.info("leads创建后更新客户表的Leads信息，请求参数:{}", JSON.toJSONString(dubboReqDto));
			DubboResult<Boolean> result = customerDubbo.updateLeadsInfoByCid(dubboReqDto);
			if(!result.checkSuccess()) {
				log.error("更新客户leads信息失败，请求参数:{},返回数据:{}", JSON.toJSONString(updateLeadsInfoThirdDto), JSON.toJSONString(result));
				return false;
			}
			return result.getData();
		} catch (Exception e) {
			log.error("调用dubbo接口customerDubbo#updateLeadsInfoByCid异常={}", e.getMessage());
		}
		return false;
	}

	/**
	 * 更新客户表的下发渠道
	 * @param updateDistributeChannelInfoThirdDto
	 * @return
	 */
	public Boolean updateCustDistributeChannelByCid(CustomerUpdateDistributeChannelInfoThirdDto updateDistributeChannelInfoThirdDto) {
		CustomerUpdateDubboDto customerUpdateDubboDto = BeanCopyUtils.convertToVo(updateDistributeChannelInfoThirdDto, CustomerUpdateDubboDto.class);
		customerUpdateDubboDto.setSourceKey(thirdCustomerConfig.getKey());
		customerUpdateDubboDto.setSourceSecret(thirdCustomerConfig.getSecret());
		try {
			log.info("更新客户表的分发渠道信息，请求参数:{}", JSON.toJSONString(updateDistributeChannelInfoThirdDto));
			DubboResult<Boolean> result = customerDubbo.update(customerUpdateDubboDto);
			if(!result.checkSuccess()) {
				log.error("更新客户表的分发渠道信息失败，请求参数:{},返回数据:{}", JSON.toJSONString(updateDistributeChannelInfoThirdDto), JSON.toJSONString(result));
				return false;
			}
			return result.getData();
		} catch (Exception e) {
			log.error("调用dubbo接口customerDubbo#update异常={}", e.getMessage());
		}
		return null;
	}

}