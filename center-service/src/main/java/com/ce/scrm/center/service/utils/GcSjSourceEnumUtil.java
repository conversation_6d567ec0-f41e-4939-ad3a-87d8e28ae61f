package com.ce.scrm.center.service.utils;

import cn.ce.cesupport.enums.GcSjSourceEnum;
import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunitySourceStateData;

import java.util.ArrayList;
import java.util.List;

/**
 * description: 高呈商机来源枚举 util
 * @author: DD.Jiu
 * date: 2024/8/9.
 */
public class GcSjSourceEnumUtil {
    /**
     * 高呈商机来源数据
     */
    private final static List<GcBusinessOpportunitySourceStateData> GC_BUSINESS_OPPORTUNITY_SOURCE_STATE_DATA_LIST = new ArrayList<>();

    /**
     * 获取高呈商机来源数据
     * <AUTHOR>
     * @date 2024/5/17 下午2:28
     * @return java.util.List<com.ce.scrm.center.dubbo.entity.view.GcBusinessOpportunitySourceStateData>
     **/
    public static List<GcBusinessOpportunitySourceStateData> list() {
        if (GC_BUSINESS_OPPORTUNITY_SOURCE_STATE_DATA_LIST.isEmpty()) {
            for (GcSjSourceEnum gcSjSourceEnum : GcSjSourceEnum.values()) {
                GcBusinessOpportunitySourceStateData gcBusinessOpportunitySourceStateData = new GcBusinessOpportunitySourceStateData();
                gcBusinessOpportunitySourceStateData.setSource(gcSjSourceEnum.getSource());
                gcBusinessOpportunitySourceStateData.setSourceName(gcSjSourceEnum.getSourceName());
                gcBusinessOpportunitySourceStateData.setSelectState(gcSjSourceEnum.getSelectState());
                gcBusinessOpportunitySourceStateData.setStateList(GcSjStateEnumUtil.listAll());
                GC_BUSINESS_OPPORTUNITY_SOURCE_STATE_DATA_LIST.add(gcBusinessOpportunitySourceStateData);
            }
        }
        return GC_BUSINESS_OPPORTUNITY_SOURCE_STATE_DATA_LIST;
    }
}