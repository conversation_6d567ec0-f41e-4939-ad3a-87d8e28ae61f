package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 卡片类型的消息
 * <AUTHOR>
 * @date 2023/12/6
 * @version 1.0.0
 */
@Data
public class TextCardMessageDto implements Serializable {

    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String description;

    /**
     * url
     */
    private String url;

    /**
     * 按钮文本
     */
    private String btntxt;

    public TextCardMessageDto() {
    }

    public TextCardMessageDto(String title, String description, String url, String btntxt) {
        this.title = title;
        this.description = description;
        this.url = url;
        this.btntxt = btntxt;
    }
}
