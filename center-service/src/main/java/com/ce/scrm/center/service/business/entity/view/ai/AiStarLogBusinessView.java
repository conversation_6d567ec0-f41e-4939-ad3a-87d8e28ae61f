package com.ce.scrm.center.service.business.entity.view.ai;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: ai点赞、点low 信息
 * @Author: lijinpeng
 * @Date: 2025/2/20 09:41
 */
@Data
public class AiStarLogBusinessView implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * pid
     */
    private String pid;

    /**
     * custId
     */
    private String custId;

    /**
     * ai基础提示词
     */
    private String aiBasePrompt;

    /**
     * ai拓展提示词
     */
    private String aiExtendPrompt;

    /**
     * ai输出的内容
     */
    private String aiOutputContent;

    /**
     * ai输出的思考过程
     */
    private String aiOutputReasoning;

    /**
     * 操作人ID
     */
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 1 点赞 0 点 low 默认 1
     */
    private Integer operatorType;

    /**
     * 点赞或者 点 low的原因
     */
    private String starReason;

}
