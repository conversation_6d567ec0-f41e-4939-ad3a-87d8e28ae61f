package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * description: 称号标签
 * @author: DD.Jiu
 * date: 2025/2/13.
 */
@Data
public class BigDataTagsDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**企业名称*/
    @JSONField(name = "企业名称", serialize = false)
    private String entname;
    /**是否是500强企业(0=否，1=是)*/
    @JSONField(name = "是否是500强企业",serialize = false)
    private Integer is_top_500;
    @JSONField(name = "是否是500强企业")
    private String is_top_500_str;
    /**是否是独角兽企业(0=否，1=是)*/
    @JSONField(name = "是否是独角兽企业",serialize = false)
    private Integer is_unicorn;
    @JSONField(name = "是否是独角兽企业")
    private String is_unicorn_str;
    /**是否是瞪羚企业(0=否，1=是)*/
    @JSONField(name = "是否是瞪羚企业",serialize = false)
    private Integer is_gazelle;
    @JSONField(name = "是否是瞪羚企业")
    private String is_gazelle_str;
}
