package com.ce.scrm.center.service.third.invoke;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.service.yml.ThirdCustomerConfig;
import com.ce.scrm.customer.dubbo.api.ICustomerTagDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerTagQueryDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerTagRelaCreateDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerTagDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 客户营销活动三方业务
 * <AUTHOR>
 * @date 2025/7/11 17:40
 * @version 1.0.0
 **/
@Slf4j
@Service
public class CustomerTagsThirdService {

	private final ThirdCustomerConfig thirdCustomerConfig;
	@DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
	private ICustomerTagDubbo customerTagDubbo;

	public CustomerTagsThirdService(ThirdCustomerConfig thirdCustomerConfig) {
		this.thirdCustomerConfig = thirdCustomerConfig;
	}


	/**
	 * 导入客户并和客户标签关系
	 * @param addThirdDtos
	 * @return
	 */
	public Boolean addCustomerTagRela(List<CustomerTagRelaCreateDubboDto> addThirdDtos, String opertor) {
		if (org.springframework.util.CollectionUtils.isEmpty(addThirdDtos) || StringUtils.isBlank(opertor)) {
			log.error("addCustomerTagRela，参数为空，addThirdDtos={}, opertor={}", JSON.toJSONString(addThirdDtos), opertor);
			return false;
		}
		DubboResult<Boolean> dubboResult;
		try {
			log.info("导入客户并和客户标签关系, 参数为:{}，opertor={}", JSON.toJSONString(addThirdDtos), opertor);
			dubboResult = customerTagDubbo.addCustomerTagRela(addThirdDtos, opertor);
			if (!dubboResult.checkSuccess()) {
				log.error("addCustomerTagRela，调用客户dubbo异常，参数为:{}，返回数据为:{}", JSON.toJSONString(addThirdDtos), JSON.toJSONString(dubboResult));
				return false;
			}
		} catch (Exception e) {
			log.error("调用Dubbo接口customerTagDubbo#addCustomerTagRela出错，异常信息={}", e.getMessage());
		}
		return false;
	}


	/**
	 * 客户的标签列表（按标签权重排序）
	 * @return key customerId value tag
	 */
	public Map<String, List<CustomerTagDubboView>> tagList(List<String> customerList) {
		if (CollectionUtils.isEmpty(customerList)) {
			return Collections.emptyMap();
		}
		DubboResult<Map<String, List<CustomerTagDubboView>>> mapDubboResult = customerTagDubbo.customerTagList(customerList);
		if(!mapDubboResult.checkSuccess()) {
			log.error("获取客户列表对应的标签信息失败，dubbo接口异常,返回值:{}", JSON.toJSONString(mapDubboResult));
			return Collections.emptyMap();
		}
		return mapDubboResult.getData();
	}

	/**
	 * 根据标签分类id，获取对应的客户的标签列表
	 * @return key customerId value tag
	 */
	public Map<String, List<CustomerTagDubboView>> getTagsByCategoryIds(List<Long> categoryIds) {
		if (CollectionUtils.isEmpty(categoryIds)) {
			return Collections.emptyMap();
		}
		DubboResult<Map<String, List<CustomerTagDubboView>>> tagsByCategoryIds = customerTagDubbo.getTagsByCategoryIds(categoryIds);
		if(!tagsByCategoryIds.checkSuccess()) {
			log.error("根据标签分类id，获取对应的客户的标签列表失败，dubbo接口异常,返回值:{}", JSON.toJSONString(tagsByCategoryIds));
			return Collections.emptyMap();
		}
		return tagsByCategoryIds.getData();
	}

	/**
	 * 根据标签code查询标签
	 * @param tagCode 标签code
	 * @return 客户标签信息
	 */
	public CustomerTagDubboView getCustomerTagByCode(String tagCode) {
		if (StringUtils.isBlank(tagCode)) {
			return null;
		}
		CustomerTagQueryDubboDto customerTagQueryDubboDto = new CustomerTagQueryDubboDto();
		customerTagQueryDubboDto.setSourceKey(thirdCustomerConfig.getKey());
		customerTagQueryDubboDto.setSourceSecret(thirdCustomerConfig.getSecret());
		customerTagQueryDubboDto.setTagCode(tagCode);
		DubboResult<CustomerTagDubboView> dubboResult = customerTagDubbo.getCustomerTags(customerTagQueryDubboDto);
		if(!dubboResult.checkSuccess()) {
			log.error("根据标签code查询标签失败，dubbo接口异常,标签code={}, 返回值:{}", tagCode, JSON.toJSONString(dubboResult));
			return null;
		}
		return dubboResult.getData();
	}


}