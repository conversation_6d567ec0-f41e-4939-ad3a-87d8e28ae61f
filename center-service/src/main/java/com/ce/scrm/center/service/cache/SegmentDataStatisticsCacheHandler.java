package com.ce.scrm.center.service.cache;

import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.ce.cesupport.enums.segment.SalerCustomTagEnum;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ce.scrm.center.cache.constant.CacheConstant;
import com.ce.scrm.center.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.center.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.center.dao.entity.SegmentDetail;
import com.ce.scrm.center.dao.service.SegmentDetailService;
import com.ce.scrm.center.dao.service.SegmentService;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.view.segment.SegmentDataStatisticsCacheView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.Objects;

/**
 * @version 1.0
 * @Description: 专项数据跟踪
 * @Author: lijinpeng
 * @Date: 2025/2/28 11:13
 */
@Slf4j
@Component
public class SegmentDataStatisticsCacheHandler extends AbstractStringCacheHandler<SegmentDataStatisticsCacheView> {

    @Resource
    private EmployeeInfoBusiness employeeInfoBusiness;

    @Resource
    private SegmentService segmentService;

    @Resource
    private SegmentDetailService segmentDetailService;

    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.SEGMENT_DATA_STATISTICS_CACHE_V2;
    }

    @Override
    protected Class<SegmentDataStatisticsCacheView> getClazz() {
        return SegmentDataStatisticsCacheView.class;
    }

    @Override
    protected long getExpire() {
		// 此处改大需要注意，改大的话一个员工会有多个角色 缓存key里并没有区分
        return CacheConstant.CacheExpire.ONE_SECOND;
    }

    @Override
    protected SegmentDataStatisticsCacheView queryDataBySource(String empIdAndSegmentId) {

        String[] split = empIdAndSegmentId.split(",");
        String empId = split[0].trim();
        String segmentId = split[1].trim();
	    String selectionStartDate = null;
	    String selectionEndDate = null;
		if (split.length > 3) {
			selectionStartDate = split[2].trim();
			selectionEndDate = split[3].trim();
		}

	    Date start = null; // 自定义开始时间
	    Date end = null; // 自定义结束时间
		if (!StringUtils.isAnyBlank(selectionStartDate, selectionEndDate)) { // 任意一个不为空字符串
			Pair<Date, Date> startEndDateForSelection = getStartEndDateForSelection(selectionStartDate, selectionEndDate);
			start = startEndDateForSelection.getLeft();
			end = startEndDateForSelection.getRight();
		}
	    boolean hasTimeCondition = Objects.nonNull(start) || Objects.nonNull(end);
		log.info("分群看板 是否有自定义时间筛选条件，若不是则默认为昨天: {}", hasTimeCondition);
        if (StringUtils.isBlank(empId) || StringUtils.isBlank(segmentId)) {
            throw new ApiException(CodeMessageEnum.INVALID_PARAM);
        }

        SegmentDataStatisticsCacheView result = SegmentDataStatisticsCacheView.builder()
				.yesterdayVisitCount(0L)
				.yesterdaySignCount(0L)
				.yesterdayProtectedCount(0L)
				.yesterdayHandleCount(0L)
				.allVisitCount(0L)
				.allSignCount(0L)
				.allProtectedCount(0L)
				.allHandleCount(0L)
				.segmentDistributeCount(0L)
				.distributeProtectedCount(0L)
				.distributeSignCount(0L)
				.build();

        EmployeeInfoBusinessDto employeeInfo = employeeInfoBusiness.getEmployeeInfoByEmpId(empId);
        if (employeeInfo == null) {
            throw new ApiException(CodeMessageEnum.NOT_USER);
        }
        String position = employeeInfo.getPosition();

        // 获取昨天的日期范围
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime startOfYesterday = LocalDateTime.of(yesterday, LocalTime.MIN);
        LocalDateTime endOfYesterday = LocalDateTime.of(yesterday, LocalTime.MAX);
        Date currentDate = new Date();

        // 昨日拜访
        LambdaQueryChainWrapper<SegmentDetail> lambdaQuery1 = segmentDetailService.lambdaQuery();
		if (Objects.equals(employeeInfo.getIsZbReport(), 1)) {

		} else if(PositionUtil.isBusinessArea(position)) {
            lambdaQuery1.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
        }else if (PositionUtil.isBusinessMajor(position)) {
            lambdaQuery1.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery1.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
        } else if (PositionUtil.isBusinessBu(position)) {
            lambdaQuery1.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery1.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
            lambdaQuery1.eq(SegmentDetail::getBuId,employeeInfo.getBuId());
        } else if (PositionUtil.isBusinessManager(position)) {
            lambdaQuery1.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery1.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
            lambdaQuery1.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
            lambdaQuery1.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
        } else if (PositionUtil.isBusinessSaler(position)) {
            return result;
        } else {
            return result;
        }
        Long yesterdayVisitCount = lambdaQuery1
                .eq(SegmentDetail::getSegmentId,segmentId)
                .ge(!hasTimeCondition, SegmentDetail::getVisitLastTime, startOfYesterday)
                .le(!hasTimeCondition, SegmentDetail::getVisitLastTime, endOfYesterday)
		        .ge(hasTimeCondition, SegmentDetail::getVisitLastTime, start)
		        .le(hasTimeCondition, SegmentDetail::getVisitLastTime, end)
                .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode())
                .count();
        result.setYesterdayVisitCount(yesterdayVisitCount == null ? 0L : yesterdayVisitCount);

        // 昨日签单量
        LambdaQueryChainWrapper<SegmentDetail> lambdaQuery2 = segmentDetailService.lambdaQuery();
		if (Objects.equals(employeeInfo.getIsZbReport(), 1)) {

		} else if(PositionUtil.isBusinessArea(position)) {
            lambdaQuery2.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
        }else if (PositionUtil.isBusinessMajor(position)) {
            lambdaQuery2.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery2.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
        } else if (PositionUtil.isBusinessBu(position)) {
            lambdaQuery2.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery2.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
            lambdaQuery2.eq(SegmentDetail::getBuId,employeeInfo.getBuId());
        } else if (PositionUtil.isBusinessManager(position)) {
            lambdaQuery2.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery2.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
            lambdaQuery2.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
            lambdaQuery2.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
        } else if (PositionUtil.isBusinessSaler(position)) {
            return result;
        } else {
            return result;
        }
        Long yesterdaySignCount = lambdaQuery2
                .eq(SegmentDetail::getSegmentId,segmentId)
                .ge(!hasTimeCondition, SegmentDetail::getSignFirstTime, startOfYesterday)
                .le(!hasTimeCondition, SegmentDetail::getSignFirstTime, endOfYesterday)
		        .ge(hasTimeCondition, SegmentDetail::getSignFirstTime, start)
		        .le(hasTimeCondition, SegmentDetail::getSignFirstTime, end)
                .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode())
                .count();
        result.setYesterdaySignCount(yesterdaySignCount == null ? 0L : yesterdaySignCount);

        // 累计拜访
        LambdaQueryChainWrapper<SegmentDetail> lambdaQuery3 = segmentDetailService.lambdaQuery();
		if (Objects.equals(employeeInfo.getIsZbReport(), 1)) {

		} else if(PositionUtil.isBusinessArea(position)) {
            lambdaQuery3.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
        }else if (PositionUtil.isBusinessMajor(position)) {
            lambdaQuery3.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery3.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
        } else if (PositionUtil.isBusinessBu(position)) {
            lambdaQuery3.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery3.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
            lambdaQuery3.eq(SegmentDetail::getBuId,employeeInfo.getBuId());
        } else if (PositionUtil.isBusinessManager(position)) {
            lambdaQuery3.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery3.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
            lambdaQuery3.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
            lambdaQuery3.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
        } else if (PositionUtil.isBusinessSaler(position)) {
            return result;
        } else {
            return result;
        }
        Long allVisitCount = lambdaQuery3
                .eq(SegmentDetail::getSegmentId,segmentId)
                .le(SegmentDetail::getVisitLastTime, currentDate)
                .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode())
                .count();
        result.setAllVisitCount(allVisitCount == null ? 0L : allVisitCount);

        // 累计签单
        LambdaQueryChainWrapper<SegmentDetail> lambdaQuery4 = segmentDetailService.lambdaQuery();
		if (Objects.equals(employeeInfo.getIsZbReport(), 1)) {

		} else if(PositionUtil.isBusinessArea(position)) {
            lambdaQuery4.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
        }else if (PositionUtil.isBusinessMajor(position)) {
            lambdaQuery4.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery4.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
        } else if (PositionUtil.isBusinessBu(position)) {
            lambdaQuery4.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery4.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
            lambdaQuery4.eq(SegmentDetail::getBuId,employeeInfo.getBuId());
        } else if (PositionUtil.isBusinessManager(position)) {
            lambdaQuery4.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
            lambdaQuery4.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
            lambdaQuery4.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
            lambdaQuery4.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
        } else if (PositionUtil.isBusinessSaler(position)) {
            return result;
        } else {
            return result;
        }
        Long allSignCount = lambdaQuery4
                .eq(SegmentDetail::getSegmentId,segmentId)
                .le(SegmentDetail::getSignFirstTime, currentDate)
                .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode())
                .count();
        result.setAllSignCount(allSignCount == null ? 0L : allSignCount);

		// 昨日保护量
	    LambdaQueryChainWrapper<SegmentDetail> lambdaQuery5 = segmentDetailService.lambdaQuery();
		if (Objects.equals(employeeInfo.getIsZbReport(), 1)) {

		} else if(PositionUtil.isBusinessArea(position)) {
		    lambdaQuery5.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
	    }else if (PositionUtil.isBusinessMajor(position)) {
		    lambdaQuery5.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery5.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
	    } else if (PositionUtil.isBusinessBu(position)) {
		    lambdaQuery5.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery5.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery5.eq(SegmentDetail::getBuId,employeeInfo.getBuId());
	    } else if (PositionUtil.isBusinessManager(position)) {
		    lambdaQuery5.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery5.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery5.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
		    lambdaQuery5.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
	    } else if (PositionUtil.isBusinessSaler(position)) {
		    return result;
	    } else {
		    return result;
	    }
	    Long yesterdayProtectedCount = lambdaQuery5
		    .eq(SegmentDetail::getSegmentId, segmentId)
		    .ge(!hasTimeCondition, SegmentDetail::getProtectTime, startOfYesterday)
		    .le(!hasTimeCondition, SegmentDetail::getProtectTime, endOfYesterday)
		    .ge(hasTimeCondition, SegmentDetail::getProtectTime, start)
		    .le(hasTimeCondition, SegmentDetail::getProtectTime, end)
		    .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode())
		    .count();
	    result.setYesterdayProtectedCount(yesterdayProtectedCount == null ? 0L : yesterdayProtectedCount);

	    // 累计保护量
	    LambdaQueryChainWrapper<SegmentDetail> lambdaQuery6 = segmentDetailService.lambdaQuery();
		if (Objects.equals(employeeInfo.getIsZbReport(), 1)) {

		} else if(PositionUtil.isBusinessArea(position)) {
		    lambdaQuery6.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
	    }else if (PositionUtil.isBusinessMajor(position)) {
		    lambdaQuery6.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery6.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
	    } else if (PositionUtil.isBusinessBu(position)) {
		    lambdaQuery6.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery6.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery6.eq(SegmentDetail::getBuId,employeeInfo.getBuId());
	    } else if (PositionUtil.isBusinessManager(position)) {
		    lambdaQuery6.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery6.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery6.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
		    lambdaQuery6.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
	    } else if (PositionUtil.isBusinessSaler(position)) {
		    return result;
	    } else {
		    return result;
	    }
	    Long allProtectedCount = lambdaQuery6
		    .eq(SegmentDetail::getSegmentId,segmentId)
		    .le(SegmentDetail::getProtectTime, currentDate)
		    .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode())
		    .count();
	    result.setAllProtectedCount(allProtectedCount ==  null ? 0L : allProtectedCount);

	    // 昨日处理量
	    LambdaQueryChainWrapper<SegmentDetail> lambdaQuery7 = segmentDetailService.lambdaQuery();
		if (Objects.equals(employeeInfo.getIsZbReport(), 1)) {

		} else if(PositionUtil.isBusinessArea(position)) {
		    lambdaQuery7.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
	    }else if (PositionUtil.isBusinessMajor(position)) {
		    lambdaQuery7.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery7.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
	    } else if (PositionUtil.isBusinessBu(position)) {
		    lambdaQuery7.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery7.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery7.eq(SegmentDetail::getBuId,employeeInfo.getBuId());
	    } else if (PositionUtil.isBusinessManager(position)) {
		    lambdaQuery7.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery7.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery7.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
		    lambdaQuery7.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
	    } else if (PositionUtil.isBusinessSaler(position)) {
		    return result;
	    } else {
		    return result;
	    }
	    Long yesterdayHandledCount = lambdaQuery7
		    .eq(SegmentDetail::getSegmentId, segmentId)
		    .ge(!hasTimeCondition, SegmentDetail::getSalerCustomTagTime, startOfYesterday)
		    .le(!hasTimeCondition, SegmentDetail::getSalerCustomTagTime, endOfYesterday)
		    .ge(hasTimeCondition, SegmentDetail::getSalerCustomTagTime, start)
		    .le(hasTimeCondition, SegmentDetail::getSalerCustomTagTime, end)
		    .ne(SegmentDetail::getSalerCustomTag, SalerCustomTagEnum.UNTREATED.getCode())
		    .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode())
		    .count();
	    result.setYesterdayHandleCount(yesterdayHandledCount == null ? 0L : yesterdayHandledCount);

	    // 累计处理量
	    LambdaQueryChainWrapper<SegmentDetail> lambdaQuery8 = segmentDetailService.lambdaQuery();
		if (Objects.equals(employeeInfo.getIsZbReport(), 1)) {

		} else if(PositionUtil.isBusinessArea(position)) {
		    lambdaQuery8.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
	    }else if (PositionUtil.isBusinessMajor(position)) {
		    lambdaQuery8.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery8.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
	    } else if (PositionUtil.isBusinessBu(position)) {
		    lambdaQuery8.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery8.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery8.eq(SegmentDetail::getBuId,employeeInfo.getBuId());
	    } else if (PositionUtil.isBusinessManager(position)) {
		    lambdaQuery8.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery8.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery8.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
		    lambdaQuery8.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
	    } else if (PositionUtil.isBusinessSaler(position)) {
		    return result;
	    } else {
		    return result;
	    }
	    Long allHandledCount = lambdaQuery8
		    .eq(SegmentDetail::getSegmentId,segmentId)
		    .le(SegmentDetail::getSalerCustomTagTime, currentDate)
		    .ne(SegmentDetail::getSalerCustomTag, SalerCustomTagEnum.UNTREATED.getCode())
		    .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode())
		    .count();
	    result.setAllHandleCount(allHandledCount == null ? 0L : allHandledCount);

		// 当前职位下发总量
	    LambdaQueryChainWrapper<SegmentDetail> lambdaQuery9 = segmentDetailService.lambdaQuery();
		if (Objects.equals(employeeInfo.getIsZbReport(), 1)) {

		} else if(PositionUtil.isBusinessArea(position)) {
		    lambdaQuery9.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
	    }else if (PositionUtil.isBusinessMajor(position)) {
		    lambdaQuery9.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery9.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
	    } else if (PositionUtil.isBusinessBu(position)) {
		    lambdaQuery9.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery9.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery9.eq(SegmentDetail::getBuId,employeeInfo.getBuId());
	    } else if (PositionUtil.isBusinessManager(position)) {
		    lambdaQuery9.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery9.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery9.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
		    lambdaQuery9.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
	    } else if (PositionUtil.isBusinessSaler(position)) {
		    return result;
	    } else {
		    return result;
	    }
		Long segmentDistributeCount = lambdaQuery9
			.eq(SegmentDetail::getSegmentId,segmentId)
		    .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode()).count();
		result.setSegmentDistributeCount(segmentDistributeCount == null ? 0L : segmentDistributeCount);

		// 当前职位下发时已保护数量
	    LambdaQueryChainWrapper<SegmentDetail> lambdaQuery10 = segmentDetailService.lambdaQuery();
		if (Objects.equals(employeeInfo.getIsZbReport(), 1)) {

		} else if(PositionUtil.isBusinessArea(position)) {
		    lambdaQuery10.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
	    }else if (PositionUtil.isBusinessMajor(position)) {
		    lambdaQuery10.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery10.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
	    } else if (PositionUtil.isBusinessBu(position)) {
		    lambdaQuery10.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery10.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery10.eq(SegmentDetail::getBuId,employeeInfo.getBuId());
	    } else if (PositionUtil.isBusinessManager(position)) {
		    lambdaQuery10.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery10.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery10.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
		    lambdaQuery10.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
	    } else if (PositionUtil.isBusinessSaler(position)) {
			lambdaQuery10.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
			lambdaQuery10.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
			lambdaQuery10.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
			lambdaQuery10.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
			lambdaQuery10.eq(SegmentDetail::getSalerId,employeeInfo.getId());
	    } else {
		    return result;
	    }
	    Long distributeProtectedCount = lambdaQuery10
		    .eq(SegmentDetail::getSegmentId,segmentId)
		    .eq(SegmentDetail::getProtectStatus, 1)
		    .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode()).count();
	    result.setDistributeProtectedCount(distributeProtectedCount == null ? 0L : distributeProtectedCount);

	    // 当前职位下发时已签单数量
	    LambdaQueryChainWrapper<SegmentDetail> lambdaQuery11 = segmentDetailService.lambdaQuery();
		if (Objects.equals(employeeInfo.getIsZbReport(), 1)) {

		} else if(PositionUtil.isBusinessArea(position)) {
		    lambdaQuery11.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
	    }else if (PositionUtil.isBusinessMajor(position)) {
		    lambdaQuery11.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery11.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
	    } else if (PositionUtil.isBusinessBu(position)) {
		    lambdaQuery11.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery11.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery11.eq(SegmentDetail::getBuId,employeeInfo.getBuId());
	    } else if (PositionUtil.isBusinessManager(position)) {
		    lambdaQuery11.eq(SegmentDetail::getAreaId,employeeInfo.getAreaId());
		    lambdaQuery11.eq(SegmentDetail::getSubId,employeeInfo.getSubId());
		    lambdaQuery11.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId,employeeInfo.getBuId());
		    lambdaQuery11.eq(SegmentDetail::getDeptId,employeeInfo.getOrgId());
	    } else if (PositionUtil.isBusinessSaler(position)) {
		    return result;
	    } else {
		    return result;
	    }
	    Long distributeSignCount = lambdaQuery11
		    .eq(SegmentDetail::getSegmentId,segmentId)
		    .eq(SegmentDetail::getDealStatus, 1)
		    .eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode()).count();
	    result.setDistributeSignCount(distributeSignCount == null ? 0L : distributeSignCount);
	    return result;
    }


	private Pair<Date, Date> getStartEndDateForSelection(String start, String end) {
		if (StringUtils.isAnyBlank(start, end)) {
			return Pair.of(null, null);
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date startTime;
		Date endTime;
		try {
			startTime = sdf.parse(start);
			endTime = sdf.parse(end);
			return Pair.of(startTime, endTime);
		} catch (ParseException e) {
			log.warn("parse date error");
		}
		return Pair.of(null, null);
	}

}
