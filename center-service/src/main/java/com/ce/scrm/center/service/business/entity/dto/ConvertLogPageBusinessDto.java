package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description: 流转日志查询参数
 * @author: JiuDD
 * date: 2024/7/11
 */
@Data
public class ConvertLogPageBusinessDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 释放时间
     */
    private Date startTime;
    /**
     * 释放时间
     */
    private Date endTime;
    /**
     * 释放原因补写状态：0-未补写，1-已补写
     */
    private String supplementStatus;
    /**
     * 原商务代表区域
     */
    private String areaOfSalerId;
    /**
     * 原商务代表分公司
     */
    private String subcompanyOfSalerId;
    /**
     * 原商务代表部门
     */
    private String deptOfSalerId;
    /**
     * 商务ID
     */
    private String salerId;
    /**
     * 职位
     */
    private String loginPosition;
    /**
     * 线索转换类型列表  ConvertRelationEnum
     */
    private List<Integer> clueConvertTypeList;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;
}