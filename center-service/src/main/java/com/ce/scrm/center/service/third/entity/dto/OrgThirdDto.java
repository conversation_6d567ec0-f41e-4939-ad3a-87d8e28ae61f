package com.ce.scrm.center.service.third.entity.dto;

import cn.ce.cesupport.emp.vo.OrgVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Description: org信息
 * @Author: lijinpeng
 * @Date: 2024/11/15 17:35
 */
@Data
public class OrgThirdDto {

    private String id;

    private String name;

    private String parentId;

    private String source;

    private Integer state;

    private String type;

    private Integer businessType;

    private Integer orgClass2;

    private Date createTime;

    private Date updateTime;

    private String gjAutoStr;

    private Integer marketCategoryId;

    private Integer isJoin;

    private List<OrgVo> children;

}
