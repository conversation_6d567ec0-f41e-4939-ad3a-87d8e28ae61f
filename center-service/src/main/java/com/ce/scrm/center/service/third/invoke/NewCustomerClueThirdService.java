package com.ce.scrm.center.service.third.invoke;

import cn.ce.cesupport.newcustclue.service.ClueRuleAppService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 新客户线索三方业务
 * <AUTHOR>
 * @date 2024/5/21 下午7:26
 * @version 1.0.0
 */
@Slf4j
@Service
public class NewCustomerClueThirdService {

    @DubboReference
    private ClueRuleAppService clueRuleAppService;

    /**
     * 获取阶段超期时间
     * <AUTHOR>
     * @date 2024/5/21 下午7:27
     * @return java.util.Date
     **/
    public Date getExceedTime() {
        return clueRuleAppService.getProtectExceedTime();
    }
}