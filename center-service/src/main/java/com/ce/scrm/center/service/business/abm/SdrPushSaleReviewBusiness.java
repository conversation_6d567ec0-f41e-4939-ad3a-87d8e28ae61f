package com.ce.scrm.center.service.business.abm;

import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerLeads;
import com.ce.scrm.center.dao.entity.SdrPushSaleReview;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.CustomerLeadsService;
import com.ce.scrm.center.dao.service.SdrPushSaleReviewService;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.SdrPushSaleReviewBusinessAddDto;
import com.ce.scrm.center.service.business.entity.dto.SdrPushSaleReviewBusinessUpdateDto;
import com.ce.scrm.center.service.business.entity.dto.SdrPushSaleReviewBusinessViewDto;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmUpdateProtectDto;
import com.ce.scrm.center.service.business.entity.mq.AbmMergeCustomerInfoMqData;
import com.ce.scrm.center.service.business.entity.view.CustomerLeadsView;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.ConvertRelationEnum;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.enums.ReviewSrcTypeEnum;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.service.yml.ThirdCustomerConfig;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.customer.dubbo.api.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerConditionDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerDetailDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerUpdateDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * SDR推送销售审核业务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
@Slf4j
@Service
public class SdrPushSaleReviewBusiness {

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private ICustomerDubbo customerDubbo;

    @Resource
    private ThirdCustomerConfig thirdCustomerConfig;

    @Resource
    private SdrPushSaleReviewService sdrPushSaleReviewService;

    @Resource
    private BizOppDistributeBusiness bizOppDistributeBusiness;

    @Resource
    private RocketMqOperate rocketMqOperate;

    @Resource
    private SubSjAutoAssignRuleBusiness subSjAutoAssignRuleBusiness;

    @Resource
    EmployeeInfoBusiness employeeInfoBusiness;

    @Resource
    EmployeeThirdService employeeThirdService;
    @Autowired
    private AbmMessageNoticeBusiness abmMessageNoticeBusiness;

    @Resource
    private CustomerLeadsService customerLeadsService;
    @Autowired
    private CmCustProtectService cmCustProtectService;

    @Resource
    private AbmUpdateProtectBusiness abmUpdateProtectBusiness;

    /***
     * 添加审核记录
     * @param reviewBusinessAddDto
     * <AUTHOR>
     * @date 2025/8/6 18:20
     * @version 1.0.0
     * @return boolean
     **/
    public Optional<String> add(SdrPushSaleReviewBusinessAddDto reviewBusinessAddDto) {
        log.info("添加审核记录={}", JSON.toJSONString(reviewBusinessAddDto));
        try {
            if (Objects.isNull(reviewBusinessAddDto)) {
                throw new ApiException(CodeMessageEnum.REQUEST_PARAM_EXCEPTION);
            }
            String customerId = reviewBusinessAddDto.getCustomerId();
            Integer reviewSrcType = reviewBusinessAddDto.getReviewSrcType();
            String newCustomerName = reviewBusinessAddDto.getCustomerName();

            // 处理名称修改导致的客户信息合并
            Optional<String> optional = dealMergeCustomerInfo(reviewBusinessAddDto, customerId, newCustomerName);
            if (optional.isPresent()) {
                log.error("处理名称修改导致的客户信息合并失败, {}", optional.get());
                return optional;
            }
            customerId = reviewBusinessAddDto.getCustomerId();
            // 构建实体对象
            SdrPushSaleReview entity = BeanCopyUtils.convertToVo(reviewBusinessAddDto, SdrPushSaleReview.class);

            if (Objects.equals(ReviewSrcTypeEnum.CC.getCode(), reviewSrcType)) {
                // 检查是否自动分配
                // 查看客户下市场，然后轮询一个分司
                Optional<String> subIdOpt = bizOppDistributeBusiness.getSubBySmaMarketArea(customerId);
                if (!subIdOpt.isPresent()) {
                    log.info("customerId={} 对应的市场下未配置分司", customerId);
                    return Optional.of("客户对应的市场下找不到分司");
                } else {
                    String subId = subIdOpt.get();
                    if (subSjAutoAssignRuleBusiness.isAutoAssignSub(subId)) {
                        entity.setReviewStatus(1);
                        // 自动分配 那么指定分配到该公司
                        entity.setIsAutoReviewPass(1);

                    } else {
                        entity.setReviewStatus(0);
                    }
                }
            } else {
                // 0:待审核
                entity.setReviewStatus(0);
            }
            String createdId = reviewBusinessAddDto.getCreatedId();
            entity.setCreatedId(createdId);
            entity.setCreateTime(new Date());
            // 保存数据
            boolean result = sdrPushSaleReviewService.save(entity);
            log.info("添加推送销售审核记录成功，customerId={}, customerName={} reviewStatus={}",
                    customerId, reviewBusinessAddDto.getCustomerName(), entity.getReviewStatus());
            if (result) {
                Integer reviewStatus = entity.getReviewStatus();
                // 更新客户表审核状态
                CustomerUpdateDubboDto customerUpdateDubboDto = new CustomerUpdateDubboDto();
                customerUpdateDubboDto.setCustomerId(reviewBusinessAddDto.getCustomerId());
                customerUpdateDubboDto.setCustomerName(reviewBusinessAddDto.getCustomerName());
                customerUpdateDubboDto.setOperator(reviewBusinessAddDto.getCreatedId());
                customerUpdateDubboDto.setSourceKey(thirdCustomerConfig.getKey());
                customerUpdateDubboDto.setSourceSecret(thirdCustomerConfig.getSecret());
                customerUpdateDubboDto.setReviewStatus(0);
                DubboResult<Boolean> update = customerDubbo.update(customerUpdateDubboDto);
                if (!update.checkSuccess()) {
                    // 不阻断，流程往下走
                    log.error("更新客户表审核状态失败 customerUpdateDubboDto={} dubboResult={}", JSON.toJSONString(customerUpdateDubboDto),JSON.toJSONString(update));
                }
                if (Objects.equals(reviewStatus, 1)) {
                    // 审核通过的，那么走审核通过的后续流程
                    optional = bizOppDistributeBusiness.bizOppDistribute(entity);
                    if (optional.isPresent()) {
                        return optional;
                    }
                } else if (Objects.equals(reviewStatus, 0)) {
                    // 待审核 >> 通知主管
                    EmployeeInfoBusinessDto employeeInfoBusinessDto = employeeInfoBusiness.getEmployeeInfoByEmpId(reviewBusinessAddDto.getCreatedId());
                    if (Objects.nonNull(employeeInfoBusinessDto)) {
                        String leaderId = employeeInfoBusinessDto.getId();
                        String createdName = reviewBusinessAddDto.getCreatedName();
                        // $员工姓名$提交了$公司名称$推送销售的申请，请尽快批准
                        String message = createdName + "提交了" + newCustomerName + "推送销售的申请，请尽快批准";
                        abmMessageNoticeBusiness.reviewNoticeMsg(leaderId, message);
                    }
                }
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("添加推送销售审核记录失败，参数：{}", JSON.toJSONString(reviewBusinessAddDto), e);
            return Optional.of("添加推送销售审核记录失败");
        }
    }

    /***
     * 处理名称修改导致的客户信息合并
     * @param reviewBusinessAddDto
     * @param customerId
     * @param newCustomerName
     * <AUTHOR>
     * @date 2025/8/15 15:06
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
    **/
    private Optional<String> dealMergeCustomerInfo(SdrPushSaleReviewBusinessAddDto reviewBusinessAddDto, String customerId, String newCustomerName) {
        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setCustomerId(customerId);
        customerDetailDubboDto.setSourceKey(thirdCustomerConfig.getKey());
        customerDetailDubboDto.setSourceSecret(thirdCustomerConfig.getSecret());
        DubboResult<CustomerDubboView> customerDubboViewDubboResult = customerDubbo.detail(customerDetailDubboDto);
        CustomerDubboView customerDubboView;
        if (!customerDubboViewDubboResult.checkSuccess() || (customerDubboView = customerDubboViewDubboResult.getData()) == null) {
            log.warn("根据客户ID获取客户信息，返回数据为空，客户ID为:{}，返回数据为:{}", customerId, JSON.toJSONString(customerDubboViewDubboResult));
            return Optional.of("客户id找不到对应的记录");
        }

        String currCustomerName = customerDubboView.getCustomerName();
        CustProtectView currProtectView = cmCustProtectService.selectOneByCondition(CmCustProtect.builder().custName(currCustomerName).build());
        String salerId = currProtectView.getSalerId();
        Integer status = currProtectView.getStatus();
        if (!(Objects.equals(status,ProtectStateEnum.PROTECT.getState()) && Objects.equals(salerId, reviewBusinessAddDto.getCreatedId()))){
            return Optional.of("无权限操作");
        }

        if (!Objects.equals(currCustomerName, newCustomerName)) {
            log.info("customerId={} newCustomerName={} currCustomerName={} 需要更改客户名称", customerId, newCustomerName, currCustomerName);
            CustomerConditionDubboDto customerConditionDubboDto = new CustomerConditionDubboDto();
            customerConditionDubboDto.setCustomerName(newCustomerName);
            customerConditionDubboDto.setSourceKey(thirdCustomerConfig.getKey());
            customerConditionDubboDto.setSourceSecret(thirdCustomerConfig.getSecret());
            DubboResult<List<CustomerDubboView>> dubboResult = customerDubbo.findByCondition(customerConditionDubboDto);
            if (dubboResult != null && dubboResult.checkSuccess() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(dubboResult.getData())) {
                // 说明 客户名称 已经存在了
                customerDubboView = dubboResult.getData().get(0);
                AbmMergeCustomerInfoMqData abmMergeCustomerInfoMqData = new AbmMergeCustomerInfoMqData();
                abmMergeCustomerInfoMqData.setFromCustomerId(customerId);
                String newCustomerId = customerDubboView.getCustomerId();
                abmMergeCustomerInfoMqData.setTargetCustomerId(newCustomerId);
                //
                CustProtectView custProtectView = cmCustProtectService.selectOneByCondition(CmCustProtect.builder().custName(newCustomerName).build());
                Integer protectStatus = null;
                if (Objects.nonNull(custProtectView)) {
                    protectStatus = custProtectView.getStatus();
                }
                String convertTypeDesc = "跨境商机下发时,客户名称重复导致信息合并,从"+ customerId +"合并到"+newCustomerId;

                if (Objects.equals(protectStatus, ProtectStateEnum.PROTECT.getState())
                        || Objects.equals(protectStatus, ProtectStateEnum.MAJOR_WILL_ASSIGN.getState())
                        || Objects.equals(protectStatus, ProtectStateEnum.MANAGER_WILL_ASSIGN.getState())
                        || Objects.equals(protectStatus, ProtectStateEnum.BU_WILL_ASSIGN.getState())
                ) {
                    // 保护中 >>> 信息合并 + 无效客户删除
                    mergeLeads(customerId, newCustomerId, newCustomerName);
                    Optional<String> optional = abmUpdateProtectBusiness.deleteProtect(customerId, reviewBusinessAddDto.getCreatedId(), ConvertRelationEnum.ABM_SJ_MEGER_CUSTOMER_INFO.getValue(), convertTypeDesc, newCustomerId);
                    if (optional.isPresent()) {
                        return Optional.of("处理失败");
                    }
                    customerDubbo.deleteCustomer(customerId);
                    rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CRM_ABM_MERGE_CUSTOMER_INFO_TOPIC, JSON.toJSONString(abmMergeCustomerInfoMqData));
                    return Optional.of("客户" + newCustomerName + "当前已被保护, 无需继续下发");
                } else if (Objects.equals(protectStatus, ProtectStateEnum.CUSTOMER_POOL.getState()) || Objects.isNull(protectStatus)) {
                    //  >>> 信息合并 + 无效客户删除 + 创建审核记录【新的客户id】
                    mergeLeads(customerId, newCustomerId, newCustomerName);
                    Optional<String> optional = abmUpdateProtectBusiness.deleteProtect(customerId, reviewBusinessAddDto.getCreatedId(), ConvertRelationEnum.ABM_SJ_MEGER_CUSTOMER_INFO.getValue(), convertTypeDesc, newCustomerId);
                    if (optional.isPresent()) {
                        return Optional.of("处理失败");
                    }
                    customerDubbo.deleteCustomer(customerId);
                    rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CRM_ABM_MERGE_CUSTOMER_INFO_TOPIC, JSON.toJSONString(abmMergeCustomerInfoMqData));
                    // 未保护的客户 新的客户id
                    reviewBusinessAddDto.setCustomerId(newCustomerId);
                } else {
                    // 正常往下走
                    // 新的客户id
                    reviewBusinessAddDto.setCustomerId(newCustomerId);
                }
                // SDR/ CC保护客户
                protectCustomerBySdr(reviewBusinessAddDto, convertTypeDesc);
            }
        }
        log.info("处理名称修改导致的客户信息合并后：{}", JSON.toJSONString(reviewBusinessAddDto));
        return Optional.empty();
    }

    /***
     * 获取客户审核列表
     * @param customerId 客户ID
     * <AUTHOR>
     * @date 2025/8/6 18:20
     * @version 1.0.0
     * @return Optional<List < SdrPushSaleReviewBusinessViewDto>>
     **/
    public Optional<List<SdrPushSaleReviewBusinessViewDto>> getCustomerReviewList(String customerId) {
        try {
            if (StringUtils.isBlank(customerId)) {
                throw new ApiException(CodeMessageEnum.REQUEST_PARAM_EXCEPTION);
            }

            LambdaQueryWrapper<SdrPushSaleReview> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SdrPushSaleReview::getCustomerId, customerId)
                    .orderByDesc(SdrPushSaleReview::getCreateTime);

            List<SdrPushSaleReview> list = sdrPushSaleReviewService.list(queryWrapper);

            if (CollectionUtils.isEmpty(list)) {
                return Optional.empty();
            }

            List<SdrPushSaleReviewBusinessViewDto> result = list.stream()
                    .map(this::convertToViewDto)
                    .collect(Collectors.toList());

            return Optional.of(result);
        } catch (Exception e) {
            log.error("获取客户审核列表失败，customerId={}", customerId, e);
            return Optional.empty();
        }
    }

    /***
     * 获取最近一次审核记录
     * @param salerId
     * @param customerId
     * <AUTHOR>
     * @date 2025/8/8 15:12
     * @version 1.0.0
     * @return java.util.Optional<com.ce.scrm.center.service.business.entity.dto.SdrPushSaleReviewBusinessViewDto>
     **/
    public Optional<SdrPushSaleReviewBusinessViewDto> getLastTimeReview(String salerId, String customerId) {
        try {
            LambdaQueryWrapper<SdrPushSaleReview> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SdrPushSaleReview::getCustomerId, customerId)
//                    .eq(SdrPushSaleReview::getCreatedId, salerId)
                    .orderByDesc(SdrPushSaleReview::getId)
                    .last("LIMIT 1");

            SdrPushSaleReview entity = sdrPushSaleReviewService.getOne(queryWrapper);

            if (Objects.isNull(entity)) {
                return Optional.empty();
            }
            log.info("entity = {}", JSON.toJSONString(entity));
            return Optional.of(convertToViewDto(entity));
        } catch (Exception e) {
            log.error("获取最近一次审核记录失败，salerId={} customerId={}", salerId, customerId, e);
            return Optional.empty();
        }
    }

    /***
     * 获取待审核记录
     * @param customerId 客户ID
     * <AUTHOR>
     * @date 2025/8/6 18:20
     * @version 1.0.0
     * @return Optional<SdrPushSaleReviewBusinessViewDto>
     **/
    public Optional<SdrPushSaleReviewBusinessViewDto> getToBeReview(String customerId) {
        try {
            if (StringUtils.isBlank(customerId)) {
                throw new ApiException(CodeMessageEnum.REQUEST_PARAM_EXCEPTION);
            }

            LambdaQueryWrapper<SdrPushSaleReview> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SdrPushSaleReview::getCustomerId, customerId)
                    .eq(SdrPushSaleReview::getReviewStatus, 0) // 0:待审核
                    .orderByDesc(SdrPushSaleReview::getCreateTime)
                    .last("LIMIT 1");

            SdrPushSaleReview entity = sdrPushSaleReviewService.getOne(queryWrapper);

            if (Objects.isNull(entity)) {
                return Optional.empty();
            }

            return Optional.of(convertToViewDto(entity));
        } catch (Exception e) {
            log.error("获取待审核记录失败，customerId={}", customerId, e);
            return Optional.empty();
        }
    }

    /***
     * 统计待审核数量
     * @param customerId 客户ID
     * <AUTHOR>
     * @date 2025/8/6 18:20
     * @version 1.0.0
     * @return Long
     **/
    public Long countToBeReview(String customerId) {
        try {
            if (StringUtils.isBlank(customerId)) {
                throw new ApiException(CodeMessageEnum.REQUEST_PARAM_EXCEPTION);
            }

            LambdaQueryWrapper<SdrPushSaleReview> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SdrPushSaleReview::getCustomerId, customerId)
                    .eq(SdrPushSaleReview::getReviewStatus, 0); // 0:待审核

            return sdrPushSaleReviewService.count(queryWrapper);
        } catch (Exception e) {
            log.error("统计待审核数量失败，customerId={}", customerId, e);
            return 0L;
        }
    }

    /***
     * 审核操作
     * @param reviewBusinessUpdateDto 审核参数
     * <AUTHOR>
     * @date 2025/8/6 18:20
     * @version 1.0.0
     * @return Optional<String>
     **/
    public Optional<String> review(SdrPushSaleReviewBusinessUpdateDto reviewBusinessUpdateDto) {
        try {
            if (Objects.isNull(reviewBusinessUpdateDto)) {
                throw new ApiException(CodeMessageEnum.REQUEST_PARAM_EXCEPTION);
            }
            if (Objects.isNull(reviewBusinessUpdateDto.getId())) {
                throw new ApiException(CodeMessageEnum.REQUEST_PARAM_EXCEPTION);
            }
            Integer reviewStatus = reviewBusinessUpdateDto.getReviewStatus();
            if (Objects.isNull(reviewStatus)) {
                throw new ApiException(CodeMessageEnum.REQUEST_PARAM_EXCEPTION);
            }

            // 查询原记录
            SdrPushSaleReview entity = sdrPushSaleReviewService.getById(reviewBusinessUpdateDto.getId());
            if (Objects.isNull(entity)) {
                return Optional.of("审核记录不存在");
            }

            // 检查审核状态
            if (entity.getReviewStatus() != 0) {
                return Optional.of("该记录已审核，不能重复审核");
            }

            // 更新审核信息
            entity.setReviewStatus(reviewBusinessUpdateDto.getReviewStatus());
            entity.setReviewId(reviewBusinessUpdateDto.getReviewId());
            entity.setReviewTime(new Date());
            entity.setUpdatedId(reviewBusinessUpdateDto.getReviewId());
            entity.setUpdatedTime(new Date());
            entity.setRemarkReason(reviewBusinessUpdateDto.getRemarkReason());
            String assignSubId = reviewBusinessUpdateDto.getAssignSubId();
            entity.setAssignSubId(assignSubId);
            if (Objects.equals(reviewStatus, 1)) {
                String customerId = entity.getCustomerId();
                if (StringUtils.isEmpty(assignSubId)) {
                    Optional<String> subBySmaMarketArea = bizOppDistributeBusiness.getSubBySmaMarketArea(customerId);
                    if (subBySmaMarketArea.isPresent()) {
                        String subId = subBySmaMarketArea.get();
                        entity.setAssignSubId(subId);
                    } else {
                        log.info("customerId={} 对应的市场下未配置分司", customerId);
                        return Optional.of("客户对应的市场下找不到分司");
                    }
                }
            } else if (Objects.equals(reviewStatus, 2)) {
                // pass
            }

            // 保存更新
            boolean result = sdrPushSaleReviewService.updateById(entity);
            if (result) {
                log.info("审核操作成功，记录ID={}，审核结果={}", reviewBusinessUpdateDto.getId(), reviewStatus);
                // 更新客户表审核状态
                CustomerUpdateDubboDto customerUpdateDubboDto = new CustomerUpdateDubboDto();
                customerUpdateDubboDto.setCustomerId(entity.getCustomerId());
                customerUpdateDubboDto.setOperator(reviewBusinessUpdateDto.getReviewId());
                customerUpdateDubboDto.setSourceKey(thirdCustomerConfig.getKey());
                customerUpdateDubboDto.setSourceSecret(thirdCustomerConfig.getSecret());
                customerUpdateDubboDto.setReviewStatus(reviewStatus);
                DubboResult<Boolean> update = customerDubbo.update(customerUpdateDubboDto);
                if (!update.checkSuccess()) {
                    // 不阻断，流程往下走
                    log.error("更新客户表审核状态失败 customerUpdateDubboDto={}", JSON.toJSONString(customerUpdateDubboDto));
                } else {
                    //  审核通过
                    if (Objects.equals(reviewStatus, 1)) {
                        // 审核通过的，那么走审核通过的后续流程 - mq 异步
                        rocketMqOperate.asyncSend(ServiceConstant.MqConstant.Topic.CRM_ABM_REVIEW_PASS_TOPIC, JSON.toJSONString(entity));
                    }
                }
            } else {
                return Optional.of("审核操作失败");
            }
        } catch (Exception e) {
            log.error("审核操作失败，参数：{}", reviewBusinessUpdateDto, e);
            return Optional.of("审核操作异常：" + e.getMessage());
        }
        return Optional.empty();
    }

    /***
     * 转换为查看DTO
     * @param entity 实体对象
     * <AUTHOR>
     * @date 2025/8/6 18:20
     * @version 1.0.0
     * @return SdrPushSaleReviewBusinessViewDto
     **/
    private SdrPushSaleReviewBusinessViewDto convertToViewDto(SdrPushSaleReview entity) {
        return BeanCopyUtils.convertToVo(entity, SdrPushSaleReviewBusinessViewDto.class);
    }

    /***
     * 查记录
     * @param reviewId
     * <AUTHOR>
     * @date 2025/8/8 17:19
     * @version 1.0.0
     * @return com.ce.scrm.center.dao.entity.SdrPushSaleReview
     **/
    public SdrPushSaleReview getByReviewId(Long reviewId) {
        return sdrPushSaleReviewService.getById(reviewId);
    }

    /**
     * 客户leads合并
     * @param fromCustomerId
     * @param targetCustomerId
     * @param targetCustomerName
     */
    public void mergeLeads(String fromCustomerId, String targetCustomerId,String targetCustomerName) {
        log.info("客户leads合并 fromCustomerId={} targetCustomerId={} targetCustomerName={}",fromCustomerId,targetCustomerId,targetCustomerName);
        List<CustomerLeads> fromList = customerLeadsService.list(new LambdaQueryWrapper<CustomerLeads>()
                .eq(CustomerLeads::getCustomerId, fromCustomerId));
        if (CollectionUtils.isEmpty(fromList)) {
            return;
        }

        List<Long> oldIdList = fromList.stream()
                .map(CustomerLeads::getId)
                .collect(Collectors.toList());

        fromList.forEach(x -> {
            x.setId(null);
            x.setCustomerId(targetCustomerId);
            x.setCustomerName(targetCustomerName);
        });

        boolean b = customerLeadsService.saveBatch(fromList);
        if (b) {
            log.info("删除 idList = {}", JSON.toJSONString(oldIdList));
            customerLeadsService.removeByIds(oldIdList);
        } else {
            log.error("保存 leads 失败, {}", JSON.toJSONString(fromList));
        }
    }

    /***
     *  SDR/CC保护客户
     * @param reviewBusinessAddDto
     * @param convertTypeDesc
     * <AUTHOR>
     * @date 2025/8/15 15:01
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
    **/
    public Optional<String> protectCustomerBySdr(SdrPushSaleReviewBusinessAddDto reviewBusinessAddDto,String convertTypeDesc){
        log.info("SDR/CC保护客户 {}", JSON.toJSONString(reviewBusinessAddDto));
        String customerId = reviewBusinessAddDto.getCustomerId();
        AbmUpdateProtectDto abmUpdateProtectDto = new AbmUpdateProtectDto();
        abmUpdateProtectDto.setCustId(customerId);
        abmUpdateProtectDto.setCustName(reviewBusinessAddDto.getCustomerName());
        Date currentDate = new Date();
        abmUpdateProtectDto.setStatus(ProtectStateEnum.PROTECT.getState());
        String createdId = reviewBusinessAddDto.getCreatedId();
        abmUpdateProtectDto.setSalerId(createdId);
        EmployeeInfoBusinessDto employeeInfoBusinessDto = employeeInfoBusiness.getEmployeeInfoByEmpId(createdId);
        abmUpdateProtectDto.setBussdeptId(employeeInfoBusinessDto.getOrgId());
        abmUpdateProtectDto.setBuId(employeeInfoBusinessDto.getBuId());
        abmUpdateProtectDto.setAreaId(employeeInfoBusinessDto.getAreaId());
        abmUpdateProtectDto.setSubcompanyId(employeeInfoBusinessDto.getSubId());
        abmUpdateProtectDto.setProtectTime(currentDate);
        abmUpdateProtectDto.setExceedTime(DateUtil.offsetDay(currentDate, 30));
        abmUpdateProtectDto.setConvertType(ConvertRelationEnum.ABM_SJ_MEGER_CUSTOMER_INFO.getValue());
        abmUpdateProtectDto.setConvertTypeDesc(convertTypeDesc);
        return abmUpdateProtectBusiness.updateProtect(abmUpdateProtectDto);
    }
}