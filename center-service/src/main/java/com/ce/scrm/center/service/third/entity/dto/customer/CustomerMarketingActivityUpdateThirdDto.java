package com.ce.scrm.center.service.third.entity.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerMarketingActivityUpdateThirdDto implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	/**
	 * 活动名称
	 */
	private String activityName;

	/**
	 * 活动类型/触达方式 1-短信 2-邮件 3-电话 4-其他
	 */
	private Integer activityType;

	/**
	 * 模板id
	 */
	private String templateId;

	/**
	 * 活动内容/触达内容
	 */
	private String activityContent;

	/**
	 * 关联活动链接
	 */
	private String activityUrl;

	/**
	 * 客群id
	 */
	private String segmentId;

	/**
	 * 客群人数
	 */
	private Integer segmentCustCount;

	/**
	 * 客群名称
	 */
	private String segmentName;

	/**
	 * 执行时间
	 */
	private Date executeTime;

	/**
	 * 修改人ID
	 */
	private String updateBy;
}
