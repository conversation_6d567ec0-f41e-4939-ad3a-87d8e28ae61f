package com.ce.scrm.center.service.third.invoke;

import cn.ce.cesupport.emp.service.OrgAppService;
import cn.ce.cesupport.emp.vo.OrgVo;
import cn.ce.cesupport.framework.base.vo.MapResultBean;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.service.third.entity.dto.OrgConditionThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.entity.view.OrgDataThirdView;
import com.ce.scrm.center.service.third.entity.view.OrgEmpOfSubCompanyDataThirdView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 组织三方业务
 * <AUTHOR>
 * @date 2024/5/21 下午3:48
 * @version 1.0.0
 */
@Slf4j
@Service
public class OrgThirdService {

    @DubboReference
    private OrgAppService orgAppService;

    /**
     * KA销售部组织ID
     */
    public final static String KA_SALE_DEPT_AREA_ID = "3949";

    /**
     * 高呈区域ID集合
     */
    private final static Set<String> GC_AREA_ID_SET = new HashSet<>();

    static {
        GC_AREA_ID_SET.add(KA_SALE_DEPT_AREA_ID);
    }

    /**
     * 是否高呈区域
     * @param areaId    区域ID
     * <AUTHOR>
     * @date 2024/5/21 下午5:32
     * @return java.lang.Boolean
     **/
    public Boolean gcAreaFlag(String areaId) {
        return GC_AREA_ID_SET.contains(areaId);
    }

    /*
     * @Description 根据机构id获取机构信息
     * <AUTHOR>
     * @date 2024/11/15 17:41
     * @param orgId
     * @return java.util.Optional<com.ce.scrm.center.service.third.entity.dto.OrgThirdDto>
     */
    public Optional<OrgThirdDto> getOrgByOrgId(String orgId){
        if(StringUtils.isBlank(orgId)) {
            log.info("根据机构ID获取机构信息，orgId为空");
            return Optional.empty();
        }
        OrgVo orgVo = orgAppService.selectOne(orgId);
        if(orgVo == null){
            log.error("根据机构ID获取机构信息，返回数据为空，机构ID为:{}", orgId);
            return Optional.empty();
        }
        OrgThirdDto orgThirdDto = CglibUtil.copy(orgVo, OrgThirdDto.class);
        return Optional.of(orgThirdDto);
    }

    public OrgThirdDto getOrgInfoByOrgId(String orgId){
        if(StringUtils.isBlank(orgId)) {
            log.info("根据机构ID获取机构信息，orgId为空");
            return null;
        }
        OrgVo orgVo = orgAppService.selectOne(orgId);
        if(orgVo == null){
            log.error("根据机构ID获取机构信息，返回数据为空，机构ID为:{}", orgId);
            return null;
        }
        OrgThirdDto orgThirdDto = CglibUtil.copy(orgVo, OrgThirdDto.class);
        return orgThirdDto;
    }

    /*
     * @Description 通过机构id（可以是部门id或者是分司id）获取所在大区
     * <AUTHOR>
     * @date 2024/11/25 16:40
     * @param orgId
     * @return com.ce.scrm.center.service.third.entity.dto.OrgThirdDto
     */
    public OrgThirdDto selectAreaByOrgId(String orgId){
        if(StringUtils.isBlank(orgId)) {
            log.info("通过机构id（可以是部门id或者是分司id）获取所在大区，orgId为空");
            return new OrgThirdDto();
        }
        OrgVo orgVo = orgAppService.selectAreaByOrgId(orgId);
        if(orgVo == null){
            log.error("通过机构id（可以是部门id或者是分司id）获取所在大区，返回数据为空，机构ID为:{}", orgId);
            return new OrgThirdDto();
        }
        OrgThirdDto orgThirdDto = CglibUtil.copy(orgVo, OrgThirdDto.class);
        return orgThirdDto;
    }

    /*
     * @Description 根据条件查询组织结构集合
     * <AUTHOR>
     * @date 2024/11/21 14:38
     * @param orgConditionThirdDto
     * @return com.ce.scrm.center.service.third.entity.dto.OrgThirdDto
     */
    public List<OrgThirdDto> getOrgChildrenByCondition(OrgConditionThirdDto orgConditionThirdDto){
        if(orgConditionThirdDto == null) {
            log.info("根据条件查询组织结构集合，orgConditionThirdDto为空");
            return Lists.newArrayList();
        }
        OrgVo orgCondition = BeanUtil.copyProperties(orgConditionThirdDto, OrgVo.class);
        List<OrgVo> orgVos = orgAppService.selectList(orgCondition);
        if(orgVos == null){
//            log.error("根据机构ID获取机构信息，返回数据为空，orgConditionThirdDto={}", orgConditionThirdDto);
            return Lists.newArrayList();
        }
        List<OrgThirdDto> orgThirdDtoList = BeanCopyUtils.convertToVoList(orgVos, OrgThirdDto.class);
        return orgThirdDtoList;
    }

    /**
     * 根据机构ID获取机构信息
     * @param orgId 机构ID
     * <AUTHOR>
     * @date 2024/5/23 上午11:07
     * @return java.util.Optional<com.ce.scrm.center.service.third.entity.view.OrgDataThirdView>
     **/
    public Optional<OrgDataThirdView> getOrgData(String orgId){
        OrgVo orgVo = orgAppService.selectOne(orgId);
        if(orgVo == null){
            log.warn("根据机构ID获取机构信息，返回数据为空，机构ID为:{}", orgId);
            return Optional.empty();
        }
        return Optional.of(CglibUtil.copy(orgVo, OrgDataThirdView.class));
    }

    /**
     * 获取机构数据映射
     * @param orgIdList 机构ID列表
     * <AUTHOR>
     * @date 2024/5/21 下午3:53
     * @return java.util.Map<java.lang.String, com.ce.scrm.center.service.third.entity.view.OrgDataThirdView>
     **/
    public Map<String, OrgDataThirdView> getOrgData(List<String> orgIdList) {
        if(orgIdList == null || orgIdList.isEmpty()) {
            return new HashMap<>();
        }
        List<OrgVo> orgVoList = orgAppService.selectListByIds(orgIdList);
        if (CollectionUtil.isEmpty(orgVoList)) {
            log.warn("根据机构ID列表获取机构信息，返回数据为空，机构ID列表为:{}", JSON.toJSONString(orgIdList));
            return Maps.newHashMap();
        }
        return CglibUtil.copyList(orgVoList, OrgDataThirdView::new).stream().collect(Collectors.toMap(OrgDataThirdView::getId, Function.identity(), (o1, o2) -> o2));
    }

    /**
     * 获取高呈所有分司数据
     * <AUTHOR>
     * @date 2024/5/22 下午1:37
     * @return java.util.List<com.ce.scrm.center.service.third.entity.view.OrgDataThirdView>
     **/
    public List<OrgDataThirdView> getGcAllSubList() {
        List<OrgVo> orgVoList = orgAppService.getAllSubCompanyByAreaCode(KA_SALE_DEPT_AREA_ID);
        if (CollectionUtil.isEmpty(orgVoList)) {
            log.warn("获取高呈所有分司数据，返回数据为空，区域ID为:{}", KA_SALE_DEPT_AREA_ID);
            return Lists.newArrayList();
        }
        return CglibUtil.copyList(orgVoList, OrgDataThirdView::new);
    }

    /**
     * Description: 获取所有分司
     * @author: JiuDD
     * @return java.util.List<com.ce.scrm.center.service.third.entity.view.OrgDataThirdView>
     * date: 2024/9/21 20:09
     */
    public List<OrgDataThirdView> getAllSubCompany() {
        List<OrgVo> orgVoList = orgAppService.getAllSubCompany();
        if (CollectionUtil.isEmpty(orgVoList)) {
            log.warn("获取所有分司，返回数据为空");
            return Lists.newArrayList();
        }
        return CglibUtil.copyList(orgVoList, OrgDataThirdView::new);
    }

    /**
     * Description: 根据分司Id获取其分司下的所有部门和商务
     * @author: JiuDD
     * @param subId 分司Id
     * @return java.util.Map<java.lang.String,com.ce.scrm.center.service.third.entity.view.OrgEmpOfSubCompanyDataThirdView> key为部门ID，value为OrgEmpOfSubCompanyDataThirdView对象
     * date: 2024/7/22 20:51
     */
    public Map<String, OrgEmpOfSubCompanyDataThirdView> getDeptAndSalers(String subId) {
        MapResultBean deptAndSalers = orgAppService.getDeptAndSalers(subId);
        Map<String, Object> data = deptAndSalers.getData();
        if (CollectionUtil.isEmpty(data)) {
            return Maps.newHashMap();
        }
        Object list = data.get("list");
        if (Objects.isNull(list)) {
            return Maps.newHashMap();
        }
        List<HashMap<String, Object>> mapList = (List<HashMap<String, Object>>) list;
        List<OrgEmpOfSubCompanyDataThirdView> listData = mapList.stream().map(m -> {
            OrgEmpOfSubCompanyDataThirdView view = new OrgEmpOfSubCompanyDataThirdView().setDeptId("" + m.get("deptId")).setDeptName("" + m.get("deptName"));
            List<HashMap<String, Object>> deptEmpList = (List<HashMap<String, Object>>) m.get("deptEmp");
            List<OrgEmpOfSubCompanyDataThirdView.DeptEmpData> deptEmpData = deptEmpList
                    .stream()
                    .map(d -> new OrgEmpOfSubCompanyDataThirdView.DeptEmpData().setSalerId("" + d.get("salerId")).setSalerName("" + d.get("salerName")))
                    .collect(Collectors.toList());
            view.setDeptEmp(deptEmpData);
            return view;
        }).collect(Collectors.toList());
        return listData.stream().collect(Collectors.toMap(OrgEmpOfSubCompanyDataThirdView::getDeptId, Function.identity(), (o1, o2) -> o2));
    }

	/**
	 * Description: 根据机构ID列表查询机构信息
	 * @param ids 机构id列表
	 * @return java.util.List<com.ce.scrm.center.service.third.entity.dto.OrgThirdDto>
	 */
	public List<OrgThirdDto> selectListByIds(List<String> ids) {
		if (CollectionUtil.isEmpty(ids)) {
			return Lists.newArrayList();
		}
		List<OrgVo> orgVos = orgAppService.selectListByIdsNoStateLimit(ids);
		if (CollectionUtils.isNotEmpty(orgVos)) {
			return BeanCopyUtils.convertToVoList(orgVos, OrgThirdDto.class);
		}
		return Lists.newArrayList();
	}

    public List<OrgThirdDto> findAllOrgs() {
        List<OrgVo> allOrgs = orgAppService.findAllOrgs();
        if (CollectionUtils.isEmpty(allOrgs)) {
            return Lists.newArrayList();
        }
        return BeanCopyUtils.convertToVoList(allOrgs, OrgThirdDto.class);
    }

}