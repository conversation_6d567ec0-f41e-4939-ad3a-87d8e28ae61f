package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 发送微信消息
 */
@Data
@ToString
public class WxMessageDto implements Serializable {
    /**
     * 经理id
     */
    private String leaderId;
    /**
     * 总监id
     */
    private String majorId;
    /**
     * 操作人id
     */
    private String operatorId;
    /**
     * 经理姓名
     */
    private String leaderName;
    /**
     * 总监姓名
     */
    private String majorName;
    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 客户名称
     */
    String custName;
    /**
     * 商机来源 1：商机 2：报价 3：转介绍 4：商机,报价 5：商机,转介绍 6：报价,转介绍 7：商机,报价,转介绍
     */
    Integer tagOpportunityOrigin;
}