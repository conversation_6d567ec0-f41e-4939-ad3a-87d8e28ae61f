package com.ce.scrm.center.service.xfppt;

import okhttp3.*;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class ApiClient {

    private static final String MEDIA_TYPE_JSON = "application/json; charset=utf-8";
    private final static OkHttpClient client = new OkHttpClient().newBuilder()
            .connectionPool(new ConnectionPool(100, 5, TimeUnit.MINUTES))
            .readTimeout(60 * 10, TimeUnit.SECONDS)
            .build();
    private static final String ERROR_MESSAGE = "Unexpected code: ";
    private final String baseUrl;

    public ApiClient(String baseUrl) {
        this.baseUrl = baseUrl;
    }


    public String createOutline(String appId, String timestamp, String signature, String query) throws IOException {
        validateParameters(appId, timestamp, signature, query);

        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        builder.addFormDataPart("query", query);
        RequestBody body = builder.build();

        Request request = buildPostRequest(baseUrl + "/createOutline", appId, timestamp, signature, body);
        return executeRequest(request);
    }

    public String createOutlineByDoc(String appId, String timestamp, String signature, File file) throws IOException {
        validateParameters(appId, timestamp, signature);

        MultipartBody.Builder body = new MultipartBody.Builder().setType(MultipartBody.FORM);
        try {
            body.addFormDataPart("file", file.getName(), RequestBody.create(null, file));
            body.addFormDataPart("fileName", file.getName());
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody requestBody = body.build();

        Request request = new Request.Builder()
                .url(baseUrl + "/createOutlineByDoc")
                .addHeader("appId", appId)
                .addHeader("timestamp", timestamp)
                .addHeader("signature", signature)
                //.addHeader("Content-Type", "multipart/form-data")
                .post(requestBody)
                .build();
        return executeRequest(request);
    }

    public String create(String appId, String timestamp, String signature, File file) throws IOException {
        validateParameters(appId, timestamp, signature);
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        builder.addFormDataPart("file", file.getName(),
                RequestBody.create(MediaType.parse("multipart/form-data"), file));
        builder.addFormDataPart("fileName", file.getName());
        builder.addFormDataPart("templateId", "202407309C7F4B8");
        RequestBody body = builder.build();
        Request request = buildPostRequest(baseUrl + "/create", appId, timestamp, signature, body);
        return executeRequest(request);
    }

    public String create(String appId, String timestamp, String signature, byte[] file) throws IOException {
        validateParameters(appId, timestamp, signature);
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        builder.addFormDataPart("file", "test.txt",
                RequestBody.create(MediaType.parse("multipart/form-data"), file));
        builder.addFormDataPart("fileName", "test.txt");
        builder.addFormDataPart("templateId", "202407309C7F4B8");
        builder.addFormDataPart("author", "中企动力");
        builder.addFormDataPart("search", "true");
        builder.addFormDataPart("isFigure", "true");
        builder.addFormDataPart("aiImage", "advanced");
        RequestBody body = builder.build();
        Request request = buildPostRequest(baseUrl + "/create", appId, timestamp, signature, body);
        return executeRequest(request);
    }

    public String checkProgress(String appId, String timestamp, String signature, String sid) throws IOException {
        validateParameters(appId, timestamp, signature, sid);

        HttpUrl url = HttpUrl.parse(baseUrl + "/progress").newBuilder()
                .addQueryParameter("sid", sid)
                .build();

        Request request = buildGetRequest(url.toString(), appId, timestamp, signature);
        return executeRequest(request);
    }


    private Request buildPostRequest(String url, String appId, String timestamp, String signature, RequestBody body) {
        return new Request.Builder()
                .url(url)
                .addHeader("appId", appId)
                .addHeader("timestamp", timestamp)
                .addHeader("signature", signature)
                .post(body)
                .build();
    }

    private Request buildGetRequest(String url, String appId, String timestamp, String signature) {
        return new Request.Builder()
                .url(url)
                .addHeader("appId", appId)
                .addHeader("timestamp", timestamp)
                .addHeader("signature", signature)
                .get()
                .build();
    }

    private String executeRequest(Request request) throws IOException {
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                System.out.println(response.body().string());
                throw new IOException(ERROR_MESSAGE + response);
            }
            return response.body().string();
        }
    }

    private void validateParameters(String... params) {
        for (String param : params) {
            if (param == null || param.isEmpty()) {
                throw new IllegalArgumentException("Parameter cannot be null or empty");
            }
        }
    }


}
