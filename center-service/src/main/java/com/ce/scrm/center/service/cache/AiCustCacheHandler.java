package com.ce.scrm.center.service.cache;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.cache.constant.CacheConstant;
import com.ce.scrm.center.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.center.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.extend.dubbo.api.AiCustCacheDubbo;
import com.ce.scrm.extend.dubbo.entity.request.AiCustCacheReq;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.AiCustCacheDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;


/**
 * description: AI客户缓存
 *
 * @author: DD.Jiu
 * date: 2025/2/12.
 */
@Slf4j
@Component
public class AiCustCacheHandler extends AbstractStringCacheHandler<AiCustCacheDubboView> {
    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private AiCustCacheDubbo aiCustCacheDubbo;

    /**
     * 获取业务缓存的key
     *
     * @return com.ce.scrm.center.cache.enumeration.CacheKeyEnum
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.AI_CUST_CACHE;
    }

    /**
     * 指定对象类型
     *
     * @return java.lang.Class<T>
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    @Override
    protected Class<AiCustCacheDubboView> getClazz() {
        return AiCustCacheDubboView.class;
    }

    /**
     * 设置缓存失效时间
     * 方便使用分布式锁，顺带每分钟发送一次redisson的心跳
     *
     * @return long
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public long getExpire() {
        return CacheConstant.CacheExpire.SEVEN_DAYS;
    }

    /**
     * 从数据源查询数据
     *
     * @return R
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    @Override
    protected AiCustCacheDubboView queryDataBySource(String key) {
        AiCustCacheReq req = new AiCustCacheReq();
        String[] values = key.split("_");
        req.setPid(values[0]);
        if (StringUtils.isBlank(req.getPid())) {
            return null;
        }
        String promptId = values[1];
        if (StringUtils.isNotBlank(promptId)) {
            req.setPromptId(Long.valueOf(promptId));
        }
        DubboResult<AiCustCacheDubboView> dubboResult = aiCustCacheDubbo.getAiCustCacheByPid(req);
        if (dubboResult.checkSuccess()) {
            return dubboResult.getData();
        }
        return null;
    }

}
