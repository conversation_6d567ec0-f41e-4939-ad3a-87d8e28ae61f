package com.ce.scrm.center.service.business.entity.dto.follow;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2025/8/8
 */
@Data
public class QueryVisitLogBusinessDto implements Serializable {

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 当前页
     */
    private Integer currentPage = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /************************当前登录员工数据 start******************************/
    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 员工名称
     */
    private String loginEmployeeName;
    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;

    /**
     * 事业部id
     */
    private String loginBuId;

    /**
     * 事业部名称
     */
    private String loginBuName;

    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;
    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;

    /************************当前登录员工数据 end******************************/
}
