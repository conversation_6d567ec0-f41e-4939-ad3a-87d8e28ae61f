package com.ce.scrm.center.service.business.entity.mq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: cdp分群下发：添加收藏
 * @Author: lijinpeng
 * @Date: 2025/3/6 11:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SegmentAddFavoriteMqData implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 收藏类型
     */
    private String wishlistHandleType;

    /**
     * 分群id
     */
    private String zqSegmentSubjectId;

    /**
     * 分群名称
     */
    private String zqSegmentSubjectName;

    private String salerId;

    private String salerName;

    private String salerDeptId;

    private String salerDeptName;

    private String salerBuId;

    private String salerBuName;

    private String salerSubId;

    private String salerSubName;

    private String salerAreaId;

    private String salerAreaName;

    /**
     * 收藏时间
     */
    private Date collectTime;

}
