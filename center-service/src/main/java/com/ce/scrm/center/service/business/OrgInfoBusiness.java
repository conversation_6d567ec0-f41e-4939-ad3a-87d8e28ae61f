package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.emp.enums.BusinessTypeEnum;
import cn.ce.cesupport.emp.enums.OrgTypeEnum;
import cn.ce.cesupport.emp.enums.StateEnum;
import cn.ce.cesupport.emp.service.EmployeeAppService;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.service.business.entity.dto.org.OrgChildrenQueryBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.org.OrgParentQueryBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.org.QueryOrgTreeBusinessDto;
import com.ce.scrm.center.service.business.entity.view.org.OrgChildrenQueryBusinessView;
import com.ce.scrm.center.service.business.entity.view.org.OrgEmployeeBusinessView;
import com.ce.scrm.center.service.business.entity.view.org.OrgParentQueryBusinessView;
import com.ce.scrm.center.service.third.entity.dto.OrgConditionThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @version 1.0
 * @Description: 组织机构信息
 * @Author: lijinpeng
 * @Date: 2024/11/21 13:49
 */
@Slf4j
@Service
public class OrgInfoBusiness {

    @Resource
    private OrgThirdService orgThirdService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public static final String ORG_EMPLOYEE_TREE_KEY = "CRM:org:employee:tree:5";

    /*
     * @Description 根据id获取其本身以及子集数据
     * <AUTHOR>
     * @date 2024/11/21 13:57
     * @param orgChildrenQueryBusinessDto
     * @return com.ce.scrm.center.service.business.entity.view.org.OrgChildrenQueryBusinessView
     */
    public OrgChildrenQueryBusinessView getChildrenList(OrgChildrenQueryBusinessDto orgChildrenQueryBusinessDto) {
        log.info("根据id获取其本身以及子集数据,orgChildrenQueryThirdDto={}", JSON.toJSONString(orgChildrenQueryBusinessDto));

        OrgChildrenQueryBusinessView result = new OrgChildrenQueryBusinessView();

        String id = orgChildrenQueryBusinessDto.getId();
        if(StringUtils.isBlank(id)) {
            return result;
        }

        // 查询所有区域
        if(Objects.equals("-1",id)) {
            OrgConditionThirdDto orgConditionThirdDto = OrgConditionThirdDto.builder()
//                    .businessType(BusinessTypeEnum.Commerce.getValue())
                    .state(StateEnum.Enable.getValue())
                    .type(OrgTypeEnum.AREA.getType())
                    .build();
            // 查询子集
            List<OrgThirdDto> orgChildrenByCondition = orgThirdService.getOrgChildrenByCondition(orgConditionThirdDto);
            result.setChildren(orgChildrenByCondition);
            log.info("根据id获取其本身以及子集数据,result={}", JSON.toJSONString(result));
            return result;
        }

        // 根据机构id查询机构信息
        Optional<OrgThirdDto> orgThirdDto = orgThirdService.getOrgByOrgId(id);
        if(!orgThirdDto.isPresent()) {
            log.error("根据id获取其本身以及子集数据，机构不存在,id={}",id);
            return result;
        }

        OrgThirdDto orgVo = orgThirdDto.get();
        result.setOrgVo(orgVo);

        String type = orgVo.getType();
        // 查询其他情况下所有子集
        OrgConditionThirdDto.OrgConditionThirdDtoBuilder orgConditionThirdDtoBuilder = OrgConditionThirdDto.builder()
                .state(StateEnum.Enable.getValue())
                .parentId(id);
        if(Objects.equals(type,OrgTypeEnum.AREA.getType())) {
            orgConditionThirdDtoBuilder.type(OrgTypeEnum.SUB.getType());
        }else if (Objects.equals(type,OrgTypeEnum.SUB.getType())) {
            Integer buFlag = orgChildrenQueryBusinessDto.getBuFlag();
            if(buFlag == 1) {
                orgConditionThirdDtoBuilder.businessType(BusinessTypeEnum.Commerce.getValue());
            }else if (buFlag == 0) {
                orgConditionThirdDtoBuilder.businessType(BusinessTypeEnum.Commerce.getValue());
                orgConditionThirdDtoBuilder.type(OrgTypeEnum.DEPT.getType());
            }
        }else if (Objects.equals(type,OrgTypeEnum.BU.getType())) {
            orgConditionThirdDtoBuilder.businessType(BusinessTypeEnum.Commerce.getValue());
            orgConditionThirdDtoBuilder.type(OrgTypeEnum.DEPT.getType());
        }else if (Objects.equals(type,OrgTypeEnum.DEPT.getType())) {
            // 想查员工，走另一个接口
            log.info("根据id获取其本身以及子集数据,result={}", JSON.toJSONString(result));
            return result;
        }

        // 查询子集
        List<OrgThirdDto> orgChildrenByCondition = orgThirdService.getOrgChildrenByCondition(orgConditionThirdDtoBuilder.build());
        result.setChildren(orgChildrenByCondition);

        log.info("根据id获取其本身以及子集数据,result={}", JSON.toJSONString(result));
        return result;
    }

    /*
     * @Description 根据orgId获取其父级数据
     * <AUTHOR>
     * @date 2024/12/2 17:34
     * @param orgParentQueryBusinessDto
     * @return com.ce.scrm.center.service.business.entity.view.org.OrgParentQueryBusinessView
     */
    @NotNull
    public OrgParentQueryBusinessView getParentOrgByOrgId(@Valid OrgParentQueryBusinessDto orgParentQueryBusinessDto) {
        String orgId = orgParentQueryBusinessDto.getOrgId();

        OrgThirdDto orgThirdDto = orgThirdService.getOrgInfoByOrgId(orgId);
        if(orgThirdDto == null) {
            throw new RuntimeException("组织找不到,orgId=" + orgId);
        }

        OrgParentQueryBusinessView builder = new OrgParentQueryBusinessView();
        if(Objects.equals(OrgTypeEnum.DEPT.getType(), orgThirdDto.getType())) {

            builder.setDeptId(orgThirdDto.getId());
            builder.setDeptName(orgThirdDto.getName());

            OrgThirdDto subInfo = orgThirdService.getOrgInfoByOrgId(orgThirdDto.getParentId());
            if(subInfo == null) {
                throw new RuntimeException("组织找不到,orgId=" + orgThirdDto.getParentId());
            }
            if(Objects.equals(OrgTypeEnum.BU.getType(), orgThirdDto.getType())) {
                builder.setBuId(subInfo.getId());
                builder.setBuName(subInfo.getName());
                OrgThirdDto subInfo2 = orgThirdService.getOrgInfoByOrgId(subInfo.getParentId());
                if(subInfo2 == null) {
                    throw new RuntimeException("组织找不到,orgId=" + subInfo.getParentId());
                }
                builder.setSubId(subInfo2.getId());
                builder.setSubName(subInfo2.getName());

                OrgThirdDto areaInfo = orgThirdService.getOrgInfoByOrgId(subInfo2.getParentId());
                if(areaInfo == null) {
                    throw new RuntimeException("组织找不到,orgId=" + subInfo2.getParentId());
                }
                builder.setAreaId(areaInfo.getId());
                builder.setAreaName(areaInfo.getName());
            }else {
                builder.setSubId(subInfo.getId());
                builder.setSubName(subInfo.getName());

                OrgThirdDto areaInfo = orgThirdService.getOrgInfoByOrgId(subInfo.getParentId());
                if(areaInfo == null) {
                    throw new RuntimeException("组织找不到,orgId=" + subInfo.getParentId());
                }
                builder.setAreaId(areaInfo.getId());
                builder.setAreaName(areaInfo.getName());
            }



        } else if (Objects.equals(OrgTypeEnum.BU.getType(), orgThirdDto.getType())) {

            builder.setBuId(orgThirdDto.getId());
            builder.setBuName(orgThirdDto.getName());

            OrgThirdDto subInfo = orgThirdService.getOrgInfoByOrgId(orgThirdDto.getParentId());
            if(subInfo == null) {
                throw new RuntimeException("组织找不到,orgId=" + orgThirdDto.getParentId());
            }
            builder.setSubId(subInfo.getId());
            builder.setSubName(subInfo.getName());

            OrgThirdDto areaInfo = orgThirdService.getOrgInfoByOrgId(subInfo.getParentId());
            if(areaInfo == null) {
                throw new RuntimeException("组织找不到,orgId=" + subInfo.getParentId());
            }
            builder.setAreaId(areaInfo.getId());
            builder.setAreaName(areaInfo.getName());

        } else if (Objects.equals(OrgTypeEnum.SUB.getType(), orgThirdDto.getType())) {

            builder.setSubId(orgThirdDto.getId());
            builder.setSubName(orgThirdDto.getName());

            OrgThirdDto areaInfo = orgThirdService.getOrgInfoByOrgId(orgThirdDto.getParentId());
            if(areaInfo == null) {
                throw new RuntimeException("组织找不到,orgId=" + orgThirdDto.getParentId());
            }
            builder.setAreaId(areaInfo.getId());
            builder.setAreaName(areaInfo.getName());

        } else if (Objects.equals(OrgTypeEnum.AREA.getType(), orgThirdDto.getType())) {

            builder.setAreaId(orgThirdDto.getId());
            builder.setAreaName(orgThirdDto.getName());

        }

        return builder;
    }

    public List<OrgEmployeeBusinessView> getOrgAndEmployeeTree(QueryOrgTreeBusinessDto queryDto) {
        Long startTime1 = System.currentTimeMillis();
        List<OrgEmployeeBusinessView> result;
        String str = stringRedisTemplate.opsForValue().get(ORG_EMPLOYEE_TREE_KEY);
        if (StringUtils.isBlank(str)) {
            result = getTreeDate(queryDto);
            stringRedisTemplate.opsForValue().set(ORG_EMPLOYEE_TREE_KEY, JSON.toJSONString(result), 6, TimeUnit.HOURS);
        }else {
            result = JSON.parseArray(str, OrgEmployeeBusinessView.class);
        }
        Long startTime2 = System.currentTimeMillis();

        if (PositionUtil.isBusinessArea(queryDto.getLoginPosition())) {
            result = queryTree(result,queryDto.getLoginAreaId());
        } else if (PositionUtil.isBusinessMajor(queryDto.getLoginPosition())) {
            result = queryTree(result,queryDto.getLoginSubId());
        } else if (PositionUtil.isBusinessBu(queryDto.getLoginPosition())) {
            result = queryTree(result,queryDto.getLoginBuId());
        } else if (PositionUtil.isBusinessManager(queryDto.getLoginPosition())) {
            result = queryTree(result,queryDto.getLoginOrgId());
        } else if (PositionUtil.isBusinessSaler(queryDto.getLoginPosition())) {
            result = Collections.emptyList();
        } else if (PositionUtil.isSystemAdministrator(queryDto.getLoginPosition())){
            // 总部角色 返回全部
        } else {
            throw new ApiException(CodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }
        Long startTime3 = System.currentTimeMillis();
        log.info("getOrgAndEmployeeTree1 耗时:{}",startTime2-startTime1);
        log.info("getOrgAndEmployeeTree2 耗时:{}",startTime3-startTime2);
        return result;
    }

    private List<OrgEmployeeBusinessView> queryTree(List<OrgEmployeeBusinessView> treeDate, String parentId) {
        if (StringUtils.isBlank(parentId) || CollectionUtil.isEmpty(treeDate)) {
            return Collections.emptyList();
        }

        for (OrgEmployeeBusinessView orgEmployeeBusinessView : treeDate) {
            // 找到匹配的节点
            if (Objects.equals(parentId, orgEmployeeBusinessView.getId()) && Objects.equals(1, orgEmployeeBusinessView.getType())) {
                return orgEmployeeBusinessView.getChildren();
            }

            // 递归搜索子节点
            if (CollectionUtil.isNotEmpty(orgEmployeeBusinessView.getChildren()) && Objects.equals(1, orgEmployeeBusinessView.getType())) {
                List<OrgEmployeeBusinessView> result = queryTree(orgEmployeeBusinessView.getChildren(), parentId);
                // 如果在子节点中找到了，就返回结果
                if (CollectionUtil.isNotEmpty(result)) {
                    return result;
                }
                // 如果没找到，继续搜索下一个兄弟节点
            }
        }

        return Collections.emptyList();
    }

    @NotNull
    public List<OrgEmployeeBusinessView> getTreeDate(QueryOrgTreeBusinessDto queryDto) {
        List<OrgThirdDto> allOrg = orgThirdService.findAllOrgs();
        List<OrgEmployeeBusinessView> areaList = new ArrayList<>();
        List<OrgEmployeeBusinessView> subList = new ArrayList<>();
        List<OrgEmployeeBusinessView> buList = new ArrayList<>();
        List<OrgEmployeeBusinessView> deptList = new ArrayList<>();
        for (OrgThirdDto orgThirdDto : allOrg) {
            if (Objects.equals(OrgTypeEnum.AREA.getType(), orgThirdDto.getType())) {
                areaList.add(OrgEmployeeBusinessView.builder()
                        .id(orgThirdDto.getId())
                        .name(orgThirdDto.getName())
                        .parentId(orgThirdDto.getParentId())
                        .type(1)
                        .build());
            } else if (Objects.equals(OrgTypeEnum.SUB.getType(), orgThirdDto.getType())) {
                subList.add(OrgEmployeeBusinessView.builder()
                        .id(orgThirdDto.getId())
                        .name(orgThirdDto.getName())
                        .parentId(orgThirdDto.getParentId())
                        .type(1)
                        .build());
            } else if (Objects.equals(OrgTypeEnum.DEPT.getType(), orgThirdDto.getType()) && Objects.equals(BusinessTypeEnum.Commerce.getValue(), orgThirdDto.getBusinessType())) {
                deptList.add(OrgEmployeeBusinessView.builder()
                        .id(orgThirdDto.getId())
                        .name(orgThirdDto.getName())
                        .parentId(orgThirdDto.getParentId())
                        .type(1)
                        .build());
            } else if (Objects.equals(OrgTypeEnum.BU.getType(), orgThirdDto.getType()) && Objects.equals(BusinessTypeEnum.Commerce.getValue(), orgThirdDto.getBusinessType())) {
                buList.add(OrgEmployeeBusinessView.builder()
                        .id(orgThirdDto.getId())
                        .name(orgThirdDto.getName())
                        .parentId(orgThirdDto.getParentId())
                        .type(1)
                        .build());
            }
        }
        List<EmployeeDataThirdView> employeeList = employeeThirdService.selectListByOrgIds(deptList.stream().map(OrgEmployeeBusinessView::getId).collect(Collectors.toList()));
        Map<String, List<EmployeeDataThirdView>> employeeMap = employeeList.stream().collect(Collectors.groupingBy(EmployeeDataThirdView::getOrgId));
        Map<String, List<OrgEmployeeBusinessView>> deptMap = deptList.stream().collect(Collectors.groupingBy(OrgEmployeeBusinessView::getParentId));
        Map<String, List<OrgEmployeeBusinessView>> buMap = buList.stream().collect(Collectors.groupingBy(OrgEmployeeBusinessView::getParentId));
        Map<String, List<OrgEmployeeBusinessView>> subMap = subList.stream().collect(Collectors.groupingBy(OrgEmployeeBusinessView::getParentId));

        for (OrgEmployeeBusinessView areaItem : areaList) {
            List<OrgEmployeeBusinessView> childrenList = subMap.get(areaItem.getId());
            Optional<EmployeeLiteThirdView> orgLeaderRe = employeeThirdService.getOrgLeader(areaItem.getId());
            orgLeaderRe.ifPresent(orgLeader -> childrenList.add(OrgEmployeeBusinessView.builder().id(orgLeader.getId()).name(orgLeader.getName()).type(3).parentId(orgLeader.getOrgId()).build()));
            areaItem.setChildren(childrenList);
        }

        for (OrgEmployeeBusinessView subItem : subList) {
            List<OrgEmployeeBusinessView> deptChildrenList = deptMap.getOrDefault(subItem.getId(), Collections.emptyList());
            List<OrgEmployeeBusinessView> buChildrenList = buMap.getOrDefault(subItem.getId(),Collections.emptyList());
            List<OrgEmployeeBusinessView> childrenList = Stream.concat(deptChildrenList.stream(), buChildrenList.stream()).collect(Collectors.toList());
            Optional<EmployeeLiteThirdView> orgLeaderRe = employeeThirdService.getOrgLeader(subItem.getId());
            orgLeaderRe.ifPresent(orgLeader -> childrenList.add(OrgEmployeeBusinessView.builder().id(orgLeader.getId()).name(orgLeader.getName()).type(3).parentId(orgLeader.getOrgId()).build()));
            subItem.setChildren(childrenList);
        }

        for (OrgEmployeeBusinessView buItem : buList) {
            List<OrgEmployeeBusinessView> childrenList = deptMap.get(buItem.getId());
            Optional<EmployeeLiteThirdView> orgLeaderRe = employeeThirdService.getOrgLeader(buItem.getId());
            orgLeaderRe.ifPresent(orgLeader -> childrenList.add(OrgEmployeeBusinessView.builder().id(orgLeader.getId()).name(orgLeader.getName()).type(3).parentId(orgLeader.getOrgId()).build()));
            buItem.setChildren(childrenList);
        }

        for (OrgEmployeeBusinessView deptItem : deptList) {
            List<EmployeeDataThirdView> employeeDataList = employeeMap.get(deptItem.getId());
            CopyOptions copyOptions = CopyOptions.create().setFieldMapping(new HashMap<String, String>() {
                {
                    put("orgId", "parentId");
                }
            });
            List<OrgEmployeeBusinessView> childrenList = BeanUtil.copyToList(employeeDataList, OrgEmployeeBusinessView.class,copyOptions);
            if (childrenList != null) {
                childrenList.forEach(item -> item.setType(2));
            }
            Optional<EmployeeLiteThirdView> orgLeaderRe = employeeThirdService.getOrgLeader(deptItem.getId());
            orgLeaderRe.ifPresent(orgLeader -> {
                if (!childrenList.stream().map(OrgEmployeeBusinessView::getId).collect(Collectors.toList()).contains(orgLeader.getId())) {
                    childrenList.add(OrgEmployeeBusinessView.builder().id(orgLeader.getId()).name(orgLeader.getName()).type(3).parentId(orgLeader.getOrgId()).build());
                }
            });
            deptItem.setChildren(childrenList);
        }
        return areaList;
    }

}
