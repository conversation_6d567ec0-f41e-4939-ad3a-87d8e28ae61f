package com.ce.scrm.center.service.eqixiu.sdk.util;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;


/**
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class JsonMapper {

    private static final Logger logger = LoggerFactory.getLogger(JsonMapper.class);
    private static final JsonMapper JSON_MAPPER = new JsonMapper();
    private final ObjectMapper mapper;
    private final ObjectMapper rawMapper = new ObjectMapper();

    private JsonMapper() {
        mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(SerializationFeature.INDENT_OUTPUT, false);

        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        //返序列化时忽略不存在的属性
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //允许控制字符
        mapper.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
        mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    }

    public static JsonMapper getInstance() {
        return JSON_MAPPER;
    }

    /**
     * Object可以是POJO，也可以是Collection或数组。
     * 如果对象为Null或集合为空集合, 则忽略.
     */
    public static String toJSON(Object object) {
        return JSON_MAPPER.toJson(object);
    }

    public static <T> T parse(String json) {
        return JSON_MAPPER.fromJson(json);
    }

    public static <T> T parse(String json, Class<T> clazz) {
        return JSON_MAPPER.fromJson(json, clazz);
    }

    public static <T> T parse(String json, TypeReference<T> tTypeReference) {
        return JSON_MAPPER.fromJson(json, tTypeReference);
    }

    public static <T> List<T> parseArray(String json, Class<T> elementClasses) {
        return JSON_MAPPER.fromJson(json, List.class, elementClasses);
    }

    public ObjectMapper getObjectMapper() {
        return this.mapper;
    }

    public ObjectMapper getRawObjectMapper() {
        return this.rawMapper;
    }


    /**
     * Object可以是POJO，也可以是Collection或数组。
     * 如果对象为Null或集合为空集合, 则忽略.
     */
    public String toJson(Object object) {

        try {
            return mapper.writeValueAsString(object);
        } catch (IOException e) {
            logger.error("write to json string error: {} : {}", object, e.getLocalizedMessage(), e);
            return "{}";
        }
    }

//    public JSONObject fromJson(String json) {
//        if (StrUtil.isEmpty(json)) {
//            return null;
//        }
//        return fromJson(json, JSONObject.class);
//    }

    /**
     * Object可以是POJO，也可以是Collection或数组。
     * 如果对象为Null, 返回"null".
     * 如果集合为空集合, 返回"[]".
     */
    public String toRawJson(Object object) {

        try {
            return rawMapper.writeValueAsString(object);
        } catch (IOException e) {
            logger.error("write to json string error: {} : {}", object, e.getLocalizedMessage(), e);
            return "{}";
        }
    }

    /**
     * 反序列化POJO或简单Collection如List<String>.
     * <p>
     * 如果JSON字符串为Null或"null"字符串, 返回Null.
     * 如果JSON字符串为"[]", 返回空集合.
     * <p>
     * 如需反序列化复杂Collection如List<MyBean>, 请使用fromJson(String,JavaType)
     */
    public <T> T fromJson(String json, Class<T> clazz) {
        if (StrUtil.isEmpty(json)) {
            return null;
        }

        try {
            return mapper.readValue(json, clazz);
        } catch (IOException e) {
            logger.warn("parse json string error: {} :{}", json, e.getLocalizedMessage(), e);
            return null;
        }
    }

    public <T> T fromJson(String json, TypeReference<T> tTypeReference) {
        if (StrUtil.isEmpty(json)) {
            return null;
        }

        try {
            return mapper.readValue(json, tTypeReference);
        } catch (IOException e) {
            logger.warn("parse json string error: {} :{}", json, e.getLocalizedMessage(), e);
            return null;
        }
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public <T> T fromJson(String json, Class collectionClass, Class elementClasses) {
        if (StrUtil.isEmpty(json)) {
            return null;
        }
        try {
            JavaType javaType = mapper.getTypeFactory().constructCollectionType(collectionClass, elementClasses);
            return mapper.readValue(json, javaType);
        } catch (IOException e) {
            logger.warn("parse json string error: {} : {}", json, e.getLocalizedMessage(), e);
            return null;
        }
    }

    public <T> T fromJson(String json) {
        if (StrUtil.isEmpty(json)) {
            return null;
        }

        try {
            return mapper.readValue(json, new TypeRef<>());
        } catch (IOException e) {
            logger.warn("parse json string error: {} :{}", json, e.getLocalizedMessage(), e);
            return null;
        }
    }

    /**
     * <AUTHOR>
     */
    public static class TypeRef<T> extends TypeReference<T> {
    }
}
