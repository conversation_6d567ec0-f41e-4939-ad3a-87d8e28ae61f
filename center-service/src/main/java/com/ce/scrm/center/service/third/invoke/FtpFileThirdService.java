package com.ce.scrm.center.service.third.invoke;

import cn.ce.cesupport.contract.entity.FtpFile;
import cn.ce.cesupport.contract.service.FtpFileAppService;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.ce.scrm.center.service.third.entity.view.FileDataThirdView;
import com.ce.scrm.center.service.yml.ThirdFileConfig;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 远程文件三方业务
 * <AUTHOR>
 * @date 2024/5/21 上午9:27
 * @version 1.0.0
 */
@Slf4j
@Service
public class FtpFileThirdService {

    @DubboReference
    private FtpFileAppService ftpFileAppService;

    @Resource
    private ThirdFileConfig thirdFileConfig;

    /**
     * 根据文件ID获取文件信息
     * @param fileId    文件ID
     * <AUTHOR>
     * @date 2024/5/21 上午9:31
     * @return java.util.Optional<com.ce.scrm.center.service.third.entity.view.FileDataThirdView>
     **/
    public Optional<FileDataThirdView> get(String fileId) {
        if (StrUtil.isBlank(fileId)) {
            log.warn("根据文件ID获取文件信息，文件ID不能为空");
            return Optional.empty();
        }
        FtpFile ftpFile = ftpFileAppService.findById(fileId);
        if (ftpFile == null) {
            log.warn("根据文件ID获取文件信息，文件不存在，文件ID为:{}", fileId);
            return Optional.empty();
        }
        FileDataThirdView fileDataThirdView = CglibUtil.copy(ftpFile, FileDataThirdView.class);
        fileDataThirdView.setFileUrl(thirdFileConfig.getUrl() + fileDataThirdView.getPath());
        return Optional.of(fileDataThirdView);
    }

    /**
     * 根据文件ID列表获取所有文件
     * @param fileIds   文件ID列表
     * <AUTHOR>
     * @date 2024/5/21 上午9:33
     * @return java.util.List<com.ce.scrm.center.service.third.entity.view.FileDataThirdView>
     **/
    public List<FileDataThirdView> get(List<String> fileIds) {
        if (ArrayUtil.isEmpty(fileIds)) {
            log.warn("根据文件ID列表获取所有文件，文件ID列表不能为空");
            return Lists.newArrayList();
        }
        return fileIds.stream().filter(StrUtil::isNotBlank).map(this::get).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
    }
}