package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.ce.cesupport.enums.CustomerPresentStageEnum;
import cn.ce.cesupport.enums.ProtectStatusEnum;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.ce.cesupport.enums.favorites.ClueCustStatusEnum;
import cn.ce.cesupport.enums.favorites.ClueRuleEnum;
import cn.ce.cesupport.enums.protect.ProtectSourceEnum;
import cn.ce.cesupport.newcustclue.service.ClueAssignAppService;
import cn.ce.cesupport.newcustclue.service.ClueRuleAppService;
import cn.ce.cesupport.newcustclue.vo.ClueAssignVo;
import cn.ce.cesupport.newcustclue.vo.ClueRuleVo;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.SmaMarketSubcompay;
import com.ce.scrm.center.dao.entity.query.CmCustProtectNewQuery;
import com.ce.scrm.center.dao.entity.query.SjIntentInfoQuery;
import com.ce.scrm.center.dao.entity.query.SmaDictionaryItemQuery;
import com.ce.scrm.center.dao.entity.query.SmaMarketQuery;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.entity.view.SjIntentInfoView;
import com.ce.scrm.center.dao.entity.view.SmaDictionaryItemNewView;
import com.ce.scrm.center.dao.entity.view.SmaMarketView;
import com.ce.scrm.center.dao.mapper.CmCustProtectMapper;
import com.ce.scrm.center.dao.service.*;
import com.ce.scrm.center.service.business.entity.dto.*;
import com.ce.scrm.center.service.business.entity.dto.org.OrgChildrenQueryBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.org.OrgParentQueryBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.protect.AssignProtectBatchBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.protect.CustPoolQueryBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.protect.ProtectAllotBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.protect.ProtectConfirmBusinessDto;
import com.ce.scrm.center.service.business.entity.view.*;
import com.ce.scrm.center.service.business.entity.view.customer.MyProtectCustomerCapacityedCountBusinessView;
import com.ce.scrm.center.service.business.entity.view.org.OrgChildrenQueryBusinessView;
import com.ce.scrm.center.service.business.entity.view.org.OrgParentQueryBusinessView;
import com.ce.scrm.center.service.business.entity.view.protect.CustPoolQueryBusinessView;
import com.ce.scrm.center.service.business.entity.view.protect.ProtectBuildCustPoolView;
import com.ce.scrm.center.service.constant.SmsTemplateConstants;
import com.ce.scrm.center.service.enums.*;
import com.ce.scrm.center.service.router.RouterContext;
import com.ce.scrm.center.service.third.entity.dto.ClockConditionQueryThirdDto;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerPageThirdDto;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmpCustSiteClockThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.entity.view.OrgDataThirdView;
import com.ce.scrm.center.service.third.invoke.*;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerUpdateDubboDto;
import com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_PRESENT_STAGE_TOPIC;

/**
 * 保护业务
 * <AUTHOR>
 * @date 2024/5/21 下午3:16
 * @version 1.0.0
 */
@Slf4j
@Service
public class ProtectBusiness {

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private SmaMarketSubcompayService smaMarketSubcompayService;

    @Resource
    private NewCustomerClueThirdService newCustomerClueThirdService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;

    @Resource
    private CmCustProtectMapper cmCustProtectMapper;

    @Resource
    private RocketMqOperate rocketMqOperate;

    @Value("${mq.prefix}" + CESUPPORT_SCRM_PRESENT_STAGE_TOPIC)
    String CESUPPORT_SCRM_CUSTOMER_PRESENT_STAGE_TOPIC;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private SendWxMessage sendWxMessage;

    @Resource
    private SmaDictionaryItemBusiness smaDictionaryItemBusiness;

    @DubboReference
    private ClueAssignAppService clueAssignAppService;

    @Resource
    private EmployeeInfoBusiness employeeInfoBusiness;

    @Resource
    private OrgInfoBusiness orgInfoBusiness;

    @DubboReference
    private ClueRuleAppService clueRuleAppService;

    @Resource
    private SmaMarketBusiness smaMarketBusiness;

    @Resource
    private SjIntentInfoService sjIntentInfoService;

    @Resource
    private SmaMarketService smaMarketService;

    @Resource
    private OrgThirdService orgThirdService;

    @Resource
    private SmaDictionaryItemService smaDictionaryItemService;

    @Resource
    private EmpCustSiteClockThirdService empCustSiteClockThirdService;

    @Resource
    private PotentialCustomerBusiness potentialCustomerBusiness;


    /**
     * 保存保护关系
     * @param protectBusinessDto    保护关系数据
     * <AUTHOR>
     * @date 2024/5/21 下午7:14
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> save(ProtectBusinessDto protectBusinessDto) {
        if (protectBusinessDto == null) {
            log.warn("保存保护关系，参数不能为空");
            return Optional.of("参数不能为空");
        }
        if (StrUtil.isBlank(protectBusinessDto.getCustId())) {
            log.warn("保存保护关系，客户ID不能为空，参数为:{}", JSONObject.toJSONString(protectBusinessDto));
            return Optional.of("客户ID不能为空");
        }
        CmCustProtect protect = CglibUtil.copy(protectBusinessDto, CmCustProtect.class);
        protect.setProtectTime(new Date());
        protect.setExceedTime(newCustomerClueThirdService.getExceedTime());
        protect.setUpdateBy(protectBusinessDto.getOperator());
        protect.setUpdateTime(new Date());
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, protectBusinessDto.getCustId()).one();
        if (cmCustProtect == null) {
            protect.setCreateBy(protectBusinessDto.getOperator());
            protect.setCreateTime(new Date());
        } else {
            protect.setId(cmCustProtect.getId());
        }
        Optional<CustomerDataThirdView> customerDataThirdViewOptional = customerThirdService.getCustomerData(protectBusinessDto.getCustId());
        if (customerDataThirdViewOptional.isPresent()) {
            CustomerDataThirdView customerDataThirdView = customerDataThirdViewOptional.get();
            protect.setUncid(customerDataThirdView.getCertificateCode());
            protect.setEntId(customerDataThirdView.getSourceDataId());
            protect.setRegProvince(customerDataThirdView.getProvinceCode());
            protect.setRegCity(customerDataThirdView.getCityCode());
            protect.setRegRegion(customerDataThirdView.getDistrictCode());
            SmaMarketSubcompay smaMarketSubcompay = smaMarketSubcompayService.lambdaQuery().eq(SmaMarketSubcompay::getSubCompany, protectBusinessDto.getSubcompanyId()).last("limit 1").one();
            if (smaMarketSubcompay != null) {
                protect.setMarkId(smaMarketSubcompay.getMarketId());
            }
        }
        if (StrUtil.isBlank(protect.getId())) {
            protect.setId(UUID.fastUUID().toString(true));
            cmCustProtectService.saveData(protect);
        } else {
            cmCustProtectService.updateByCustId(protect);
        }
        smaConvertLogThirdService.insertLog(631, "高呈市场分配客户", protectBusinessDto.getOperator(), cmCustProtect, protect);
        return Optional.empty();
    }

    /**
     * 到期或者手动流失 更新保护表
     * @param cmCustProtectBusinessDto
     * @return
     */
    public boolean updateLossCustProtect(CmCustProtectBusinessDto cmCustProtectBusinessDto, CmCustProtect cmCustProtect, String reasonName) {
        log.info("updateLossCustProtect，开始更新保护关系！{}", JSONObject.toJSONString(cmCustProtectBusinessDto));
        if (StrUtil.isBlank(cmCustProtectBusinessDto.getCustId())) {
            log.info("updateLossCustProtect, 更新保护关系custId不能为空！{}", JSONObject.toJSONString(cmCustProtectBusinessDto));
            return false;
        }
        //客户信息
        Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(cmCustProtect.getCustId());
        if (!customerData.isPresent()) {
            log.info("customer中未找到对应记录 客户信息不存在！ custId：{}", cmCustProtect.getCustId());
            return false;
        }
        CustomerDataThirdView customerDataThirdView = customerData.get();
        CmCustProtect updateCmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, cmCustProtectBusinessDto.getCustId()).one();
        updateCmCustProtect.setAssignDate(null);
        updateCmCustProtect.setProtectTime(null);
        updateCmCustProtect.setExceedTime(null);
        updateCmCustProtect.setReason(reasonName);
        updateCmCustProtect.setSource(CustProtectSourceEnum.LOSS.getValue().toString());
        updateCmCustProtect.setUpdateBy(cmCustProtectBusinessDto.getLoginEmployeeId());
        updateCmCustProtect.setUpdateTime(new Date());
        updateCmCustProtect.setStatus(ProtectStateEnum.CUSTOMER_POOL.getState());
        updateCmCustProtect.setRegProvince(customerDataThirdView.getProvinceCode());
        updateCmCustProtect.setRegCity(customerDataThirdView.getCityCode());
        updateCmCustProtect.setRegRegion(customerDataThirdView.getDistrictCode());
        Integer cmCustProtectUpdate = cmCustProtectService.updateNullableByCustId(updateCmCustProtect);
        if (cmCustProtectUpdate == null || Objects.equals(0,cmCustProtectUpdate)) {
            log.info("updateLossCustProtect，更新保护关系失败！{}", JSONObject.toJSONString(cmCustProtectBusinessDto));
            return false;
        }
        // 保存流转记录
        ConvertLogBusinessDto logBusinessDto = new ConvertLogBusinessDto();
        logBusinessDto.setSalerId(cmCustProtect.getSalerId());
        logBusinessDto.setDeptOfSalerId(cmCustProtect.getBussdeptId());
        logBusinessDto.setSubcompanyOfSalerId(cmCustProtect.getSubcompanyId());
        logBusinessDto.setAreaOfSalerId(cmCustProtect.getAreaId());
        logBusinessDto.setAreaOfCurSalerId(cmCustProtect.getAreaId());
        logBusinessDto.setCustType(cmCustProtect.getCustType());
        logBusinessDto.setCustId(cmCustProtect.getCustId());
        logBusinessDto.setCustName(cmCustProtect.getCustName());
        logBusinessDto.setSubcompanyOfCurSalerId(cmCustProtect.getSubcompanyId());
        logBusinessDto.setCreateBy(cmCustProtectBusinessDto.getLoginEmployeeId());
        logBusinessDto.setCreateTime(new Date());
        logBusinessDto.setConvertType(ConvertRelationEnum.LOSS_ZJSF.getValue());
        logBusinessDto.setReleaseReason(reasonName);
        smaConvertLogThirdService.insertLog(logBusinessDto);

        // 同步客户阶段为流失
        CustomerUpdateDubboDto customerUpdateDubboDto = new CustomerUpdateDubboDto();
        customerUpdateDubboDto.setCustomerId(cmCustProtect.getCustId());
        customerUpdateDubboDto.setPresentStage(CustomerPresentStageEnum.LOST_CUST.getCode());
        rocketMqOperate.syncSend(CESUPPORT_SCRM_CUSTOMER_PRESENT_STAGE_TOPIC, JSONObject.toJSONString(customerUpdateDubboDto));

        log.info("流失成交客户成功,客户ID={}", cmCustProtect.getCustId());
        return true;
    }

    /**
     * Description: 统计某分司下各部门的客户数量
     * @author: JiuDD
     * @param subId 分司ID
     * @return java.util.Optional<java.util.Map<java.lang.String,java.lang.Long>> key为部门ID，value为客户数量
     * date: 2024/7/22 21:37
     */
    public Optional<Map<String, Long>> countCustOfDeptBySubId(String subId) {
        List<CmCustProtect> deptIdList = cmCustProtectService.lambdaQuery()
                .select(CmCustProtect::getBussdeptId)
                .eq(CmCustProtect::getSubcompanyId, subId)
                .isNotNull(CmCustProtect::getBussdeptId)
                .groupBy(CmCustProtect::getBussdeptId)
                .list();
        List<String> bussDeptIds = deptIdList.stream()
                .map(CmCustProtect::getBussdeptId)
                .distinct()
                .collect(Collectors.toList());

        if (deptIdList.isEmpty()) {
            return Optional.empty();
        } else {
            Map<String, Long> result = new HashMap<>(deptIdList.size());

            QueryWrapper<CmCustProtect> queryWrapper = Wrappers.query();
            queryWrapper.select("bussdept_id as deptId, count(1) as totalCount");
            queryWrapper.eq("subcompany_id", subId);
            queryWrapper.in("bussdept_id", bussDeptIds);
            queryWrapper.groupBy("bussdept_id");
            List<Map<String, Object>> resultList = cmCustProtectMapper.selectMaps(queryWrapper);
            resultList.forEach(map -> {
                result.put(map.get("deptId").toString(), Long.parseLong(map.get("totalCount").toString()));
            });
            return Optional.of(result);
        }
    }

    /**
     * Description: 获取保护关系
     * @author: JiuDD
     * @param protectBusinessDto
     * @return java.util.Optional<com.ce.scrm.center.service.business.entity.view.CmCustProtectBusinessView>
     * date: 2024/7/25 13:57
     */
    public Optional<CmCustProtectBusinessView> getCustProtect(ProtectBusinessDto protectBusinessDto) {
        if (protectBusinessDto == null) {
            return Optional.empty();
        }
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery()
                .eq(StrUtil.isNotBlank(protectBusinessDto.getCustId()), CmCustProtect::getCustId, protectBusinessDto.getCustId())
                .orderByDesc(CmCustProtect::getCreateTime)
                .last("limit 1")
                .one();
        if (Objects.isNull(cmCustProtect)){
            return Optional.empty();
        }
        CmCustProtectBusinessView businessView = CglibUtil.copy(cmCustProtect, CmCustProtectBusinessView.class);
        return Optional.ofNullable(businessView);
    }

    /**
     * Description: 取该客户的保护关系，如果不存在，则删除待流转表中的记录
     * @author: JiuDD
     * @param custId  客户id
     * @return com.ce.scrm.center.dao.entity.CmCustProtect 保护关系
     * date: 2024/7/25 16:31
     */
    public CmCustProtect getCmCustProtect(String custId) {
        ProtectBusinessDto dto = new ProtectBusinessDto();
        dto.setCustId(custId);
        Optional<CmCustProtectBusinessView> view = getCustProtect(dto);
        if (!view.isPresent()) {
            return null;
        }
        return CglibUtil.copy(view.get(), CmCustProtect.class);
    }

    /**
     * Description: 给总监发送微信消息，消息内容为：今天在“总监待分配”还有一些待分配的客户，请处理。（如果已处理请忽略）
     * @author: liyechao
     * @param
     * @return
     * date: 2024/7/25 16:31
     */
    public void sendMajorWxMsg() {
        // cmCustProtect表中所有当前时间到过去24小时记录，状态为待分配的记录,根据subcompanyId分组，发送微信消息
        LambdaQueryWrapper<CmCustProtect> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .select(CmCustProtect::getSubcompanyId)
                .eq(CmCustProtect::getStatus, ProtectStateEnum.MAJOR_WILL_ASSIGN.getState())
                .isNotNull(CmCustProtect::getSubcompanyId)
                .ge(CmCustProtect::getDbUpdateTime, new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000))
                .groupBy(CmCustProtect::getSubcompanyId);
        // 总监待分配的数据
        List<CmCustProtect> listProtect = cmCustProtectService.list(queryWrapper);
        if (listProtect.isEmpty()) {
            log.info("今天在总监待分配的客户数量为0");
            return;
        }
        log.info("总监待分配的数据，数量：{}", listProtect.size());
        // 批量获取所有分公司ID
        Set<String> subcompanyIds = listProtect.stream().filter(item -> item.getSubcompanyId() != null)
                .map(CmCustProtect::getSubcompanyId)
                .collect(Collectors.toSet());

        // 查找微信模版
        Optional<SmaDictionaryItemView> smsTemplateByCode = smaDictionaryItemBusiness.findByCode(SmsTemplateConstants.SCRM_ZJ_UNDISPOSED_CUSTOMER);
        if (!smsTemplateByCode.isPresent()) {
            log.error("未找到对应模版：{}", SmsTemplateConstants.SCRM_ZJ_UNDISPOSED_CUSTOMER);
            return;
        }
        subcompanyIds.forEach(subcompanyId -> {
            log.info("发送微信消息，subcompanyId：{}", subcompanyId);
            // 获取总监
            Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(subcompanyId);
            if (!orgLeader.isPresent()) {
                log.warn("发送微信消息，获取总监失败，subcompanyId：{}", subcompanyId);
                return;
            }
            log.info("发送微信消息，总监：{}", orgLeader.get().getName());

            // 发送微信消息
            sendWxMessage.sendMessage(orgLeader.get().getId(), smsTemplateByCode.get().getName());
        });


    }

    public void mangerSendMsg() {

        List<CmCustProtect> protectList = cmCustProtectService.lambdaQuery()
                .eq(CmCustProtect::getStatus, ProtectStateEnum.MANAGER_WILL_ASSIGN.getState())
                .list();

        ClueAssignVo selectClueAssign = new ClueAssignVo();
        selectClueAssign.setStatus(ClueCustStatusEnum.MANAGER.getValue());
        List<ClueAssignVo> clueAssignVoList = clueAssignAppService.getExceedList(selectClueAssign);

        Set<String> sendMsgSet = protectList.stream().map(CmCustProtect::getBussdeptId).collect(Collectors.toSet());
        sendMsgSet.addAll(clueAssignVoList.stream().map(ClueAssignVo::getDeptId).collect(Collectors.toSet()));

        if (CollectionUtils.isEmpty(sendMsgSet)) {
            log.info("今天在经理待分配的客户数量为0");
            return;
        }

        log.info("经理待分配的数据，数量：{}", sendMsgSet.size());

        // 查找微信模版
        Optional<SmaDictionaryItemView> smsTemplateByCode = smaDictionaryItemBusiness.findByCode(SmsTemplateConstants.SCRM_JL_UNDISPOSED_CUSTOMER);
        if (!smsTemplateByCode.isPresent()) {
            log.error("未找到对应模版：{}", SmsTemplateConstants.SCRM_JL_UNDISPOSED_CUSTOMER);
            return;
        }
        sendMsgSet.forEach(deptId -> {
            log.info("发送微信消息，deptId：{}", deptId);
            // 获取经理
            Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(deptId);
            if (!orgLeader.isPresent()) {
                log.warn("发送微信消息，获取经理失败，deptId：{}", deptId);
                return;
            }
            log.info("发送微信消息，经理：{}", orgLeader.get().getName());

            // 发送微信消息
            sendWxMessage.sendMessage(orgLeader.get().getId(), smsTemplateByCode.get().getName());
        });


    }

    /*
     * @Description 根据custId更新保护关系信息，注：里面对last字段进行特殊处理，更新操作应走此方法
     * <AUTHOR>
     * @date 2024/11/19 11:47
     * @param protectUpdateBusinessDto
     * @return java.lang.Integer
     */
    public Integer updateByCustId(ProtectUpdateBusinessDto protectUpdateBusinessDto) {
        CmCustProtect cmCustProtect = BeanUtil.copyProperties(protectUpdateBusinessDto, CmCustProtect.class);
        // 商务的新旧数据问题
//        treatmentSalerData(cmCustProtect);

        Integer i = cmCustProtectService.updateByCustId(cmCustProtect);
        return i;
    }

    /**
     * @description: 根据custId更新，可以更新空值，所以要注意【先查在更】确保查询全量字段 ！！！
     * @author: lijinpeng
     * @date: 2025/7/16 10:47
     * @param: [protectUpdateBusinessDto]
     * @return: java.lang.Integer
     **/
    public Integer updateNullableByCustId(ProtectUpdateBusinessDto protectUpdateBusinessDto) {
        CmCustProtect cmCustProtect = BeanUtil.copyProperties(protectUpdateBusinessDto, CmCustProtect.class);
        // 商务的新旧数据问题
//        treatmentSalerData(cmCustProtect);
        Integer updateCount = cmCustProtectService.updateNullableByCustId(cmCustProtect);
        return updateCount;
    }

    /*
     * @Description 商务的新旧数据问题
     * <AUTHOR>
     * @date 2024/11/18 19:30
     * @param newCustProtect
     */
    private void treatmentSalerData(CmCustProtect newCustProtect) {
        String custId = newCustProtect.getCustId();
        CmCustProtect oldCustProtect = cmCustProtectService.getById(custId);
        if (oldCustProtect == null){
            log.error("修改保护关系表，根据custId查询保护关系表为空：custId={}", custId);
            return;
        }

        Integer oldStatus = oldCustProtect.getStatus();
        if(Objects.equals(ProtectStateEnum.PROTECT.getState(), oldStatus)) {
            newCustProtect.setLastSalerId(oldCustProtect.getSalerId());
            newCustProtect.setLastBussdeptId(oldCustProtect.getBussdeptId());
            newCustProtect.setLastBuId(oldCustProtect.getBuId());
            newCustProtect.setLastSubcompanyId(oldCustProtect.getSubcompanyId());
            newCustProtect.setLastAreaId(oldCustProtect.getAreaId());
        } else if (Objects.equals(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState(), oldStatus)) {
            newCustProtect.setLastSalerId(null);
            newCustProtect.setLastBussdeptId(null);
            newCustProtect.setLastBuId(null);
            newCustProtect.setLastSubcompanyId(oldCustProtect.getSubcompanyId());
            newCustProtect.setLastAreaId(oldCustProtect.getAreaId());
        } else if (Objects.equals(ProtectStateEnum.MANAGER_WILL_ASSIGN.getState(), oldStatus)) {
            newCustProtect.setLastSalerId(null);
            newCustProtect.setLastBussdeptId(oldCustProtect.getBussdeptId());
            newCustProtect.setLastBuId(oldCustProtect.getBuId());
            newCustProtect.setLastSubcompanyId(oldCustProtect.getSubcompanyId());
            newCustProtect.setLastAreaId(oldCustProtect.getAreaId());
        } else if (Objects.equals(ProtectStateEnum.CUSTOMER_POOL.getState(), oldStatus)){
            newCustProtect.setLastSalerId(null);
            newCustProtect.setLastBussdeptId(null);
            newCustProtect.setLastBuId(null);
            newCustProtect.setLastSubcompanyId(null);
            newCustProtect.setLastAreaId(null);
        } else if (Objects.equals(ProtectStateEnum.BU_WILL_ASSIGN.getState(), oldStatus)) {
            newCustProtect.setLastSalerId(null);
            newCustProtect.setLastBussdeptId(null);
            newCustProtect.setLastBuId(oldCustProtect.getBuId());
            newCustProtect.setLastSubcompanyId(oldCustProtect.getSubcompanyId());
            newCustProtect.setLastAreaId(oldCustProtect.getAreaId());
        }

        Integer newStatuus = newCustProtect.getStatus();
        if(Objects.equals(ProtectStateEnum.PROTECT.getState(), newStatuus)) {
//            newCustProtect.setSalerId(null);
//            newCustProtect.setBussdeptId(null);
//            newCustProtect.setBuId(null);
//            newCustProtect.setSubcompanyId(null);
//            newCustProtect.setAreaId(null);
        } else if (Objects.equals(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState(), newStatuus)) {
            newCustProtect.setSalerId(null);
            newCustProtect.setBussdeptId(null);
            newCustProtect.setBuId(null);
//            newCustProtect.setSubcompanyId(null);
//            newCustProtect.setAreaId(null);
        } else if (Objects.equals(ProtectStateEnum.MANAGER_WILL_ASSIGN.getState(), newStatuus)) {
            newCustProtect.setSalerId(null);
//            newCustProtect.setBussdeptId(null);
//            newCustProtect.setBuId(null);
//            newCustProtect.setSubcompanyId(null);
//            newCustProtect.setAreaId(null);
        } else if (Objects.equals(ProtectStateEnum.CUSTOMER_POOL.getState(), newStatuus)){
            newCustProtect.setSalerId(null);
            newCustProtect.setBussdeptId(null);
            newCustProtect.setBuId(null);
            newCustProtect.setSubcompanyId(null);
            newCustProtect.setAreaId(null);
        } else if (Objects.equals(ProtectStateEnum.BU_WILL_ASSIGN.getState(), newStatuus)) {
            newCustProtect.setSalerId(null);
            newCustProtect.setBussdeptId(null);
//            newCustProtect.setBuId(null);
//            newCustProtect.setSubcompanyId(null);
//            newCustProtect.setAreaId(null);
        }

    }

    public Integer getProtectFlag(String customerName,Integer myMarketFlag,String position) {

        //1、必须是我的市场
        if(Objects.equals(YesOrNoEnum.NO.getCode(),myMarketFlag) || myMarketFlag == null) {
            return YesOrNoEnum.NO.getCode();
        }

        //2、必须是商务角色
        if(!PositionUtil.checkSalerRole(position)) {
            return YesOrNoEnum.NO.getCode();
        }

        //3、保护状态必须是客户池 或者 为空
        CustProtectView cmCustProtect = cmCustProtectService.selectOneByCondition(
                CmCustProtect.builder()
                        .custName(customerName)
                        .build()
        );
        if(cmCustProtect != null && !ProtectStatusEnum.CUSTPOOL.getCode().equals(cmCustProtect.getStatus())) {
            return YesOrNoEnum.NO.getCode();
        }

        return YesOrNoEnum.YES.getCode();
    }

    public Page<TurnoverCustQueryConditionBusinessView> selectTurnoverCustByCondition(TurnoverCustQueryConditionBusinessDto turnoverCustQueryConditionBusinessDto) {

        Page<CmCustProtect> page = Page.of(turnoverCustQueryConditionBusinessDto.getPageNum(), turnoverCustQueryConditionBusinessDto.getPageSize());

        LambdaQueryWrapper<CmCustProtect> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmCustProtect::getStatus, ProtectStateEnum.PROTECT.getState());
        if(Objects.equals(turnoverCustQueryConditionBusinessDto.getCustType(), ProtectCustTypeEnum.PROTECT_FOLLOW.getValue())) {
            queryWrapper.eq(CmCustProtect::getCustType,ProtectCustTypeEnum.PROTECT_FOLLOW.getValue());
        }else if (Objects.equals(turnoverCustQueryConditionBusinessDto.getCustType(), 99)) {// 传99 查 成交+未成交 临时特殊处理
            queryWrapper.in(CmCustProtect::getCustType, Arrays.asList(2, 3, 4));
        }else {
            queryWrapper.in(CmCustProtect::getCustType, Arrays.asList(3, 4));
        }

        if(CollectionUtils.isNotEmpty(turnoverCustQueryConditionBusinessDto.getSubIdList())) {
            queryWrapper.in(CmCustProtect::getSubcompanyId, turnoverCustQueryConditionBusinessDto.getSubIdList());
        }
        if(StringUtils.isNotBlank(turnoverCustQueryConditionBusinessDto.getCustId())) {
            queryWrapper.eq(CmCustProtect::getCustId, turnoverCustQueryConditionBusinessDto.getCustId());
        }
        if(StringUtils.isNotBlank(turnoverCustQueryConditionBusinessDto.getCustName())) {
            queryWrapper.like(CmCustProtect::getCustName, turnoverCustQueryConditionBusinessDto.getCustName());
        }
        if(StringUtils.isNotBlank(turnoverCustQueryConditionBusinessDto.getSalerId())) {
            queryWrapper.eq(CmCustProtect::getSalerId, turnoverCustQueryConditionBusinessDto.getSalerId());
        }
        if(StringUtils.isNotBlank(turnoverCustQueryConditionBusinessDto.getDeptId())) {
            queryWrapper.eq(CmCustProtect::getBussdeptId, turnoverCustQueryConditionBusinessDto.getDeptId());
        }
        if(StringUtils.isNotBlank(turnoverCustQueryConditionBusinessDto.getBuId())) {
            queryWrapper.eq(CmCustProtect::getBuId, turnoverCustQueryConditionBusinessDto.getBuId());
        }
        if(StringUtils.isNotBlank(turnoverCustQueryConditionBusinessDto.getSubId())) {
            queryWrapper.eq(CmCustProtect::getSubcompanyId, turnoverCustQueryConditionBusinessDto.getSubId());
        }
        if(StringUtils.isNotBlank(turnoverCustQueryConditionBusinessDto.getAreaId())) {
            queryWrapper.eq(CmCustProtect::getAreaId, turnoverCustQueryConditionBusinessDto.getAreaId());
        }
        if(CollectionUtils.isNotEmpty(turnoverCustQueryConditionBusinessDto.getTagFlag7List())) {
            queryWrapper.in(CmCustProtect::getTagFlag7, turnoverCustQueryConditionBusinessDto.getTagFlag7List());
        }
        Page<CmCustProtect> pageList = cmCustProtectService.page(page, queryWrapper);

        Page<TurnoverCustQueryConditionBusinessView> result = new Page<>();
        BeanUtil.copyProperties(pageList, result);

        List<TurnoverCustQueryConditionBusinessView> list = new ArrayList<>();
        pageList.getRecords().forEach(item -> {
            TurnoverCustQueryConditionBusinessView turnoverCustQueryConditionBusinessView = BeanCopyUtils.convertToVo(item, TurnoverCustQueryConditionBusinessView.class);
            turnoverCustQueryConditionBusinessView.setDeptId(item.getBussdeptId());
            turnoverCustQueryConditionBusinessView.setSubId(item.getSubcompanyId());
            list.add(turnoverCustQueryConditionBusinessView);
        });
        result.setRecords(list);
        return result;
    }

    /*
     * @Description 批量分配保护关系
     * <AUTHOR>
     * @date 2025/1/6 17:03
     * @param assignProtectBatchBusinessDto
     * @return com.ce.scrm.center.service.business.entity.view.BatchResultBusinessView
     */
    @Transactional(rollbackFor = Exception.class)
    public BatchResultBusinessView assignProtectBatch(AssignProtectBatchBusinessDto assignProtectBatchBusinessDto) {
        log.info("新分配接口：assignProtect，参数为assignProtectBusinessDto={}",JSONObject.toJSONString(assignProtectBatchBusinessDto));

        EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
        if(currentUser == null) {
            throw new RuntimeException("当前登录人不存在");
        }
        Date currentData = new Date();
        String assignToSalerId = assignProtectBatchBusinessDto.getAssignToSalerId();
        String assignToDeptId = assignProtectBatchBusinessDto.getAssignToDeptId();
        String assignToBuId = assignProtectBatchBusinessDto.getAssignToBuId();
        String assignToSubId = assignProtectBatchBusinessDto.getAssignToSubId();
        String assignToAreaId = null;
        List<String> custIdList = assignProtectBatchBusinessDto.getCustIdList();

        // 根据前端触发的动作类型 构建一些塔特殊的值
        SpecialActionView specialActionView = buildSpecialAction(assignProtectBatchBusinessDto.getActionType());
        if (specialActionView == null){
            throw new RuntimeException("构建数据出错");
        }

        CmCustProtect.CmCustProtectBuilder cmCustProtectBuilder = CmCustProtect.builder();
        ClueRuleVo assignDateParam = new ClueRuleVo();
        ClueRuleVo abptParam = new ClueRuleVo();
        if (StringUtils.isNotBlank(assignToSalerId)) { //分配给商务
            EmployeeInfoBusinessDto employeeInfoBusinessDto = employeeInfoBusiness.getEmployeeCheckStateByEmpId(assignToSalerId);
            Date protectExceedTime = clueRuleAppService.getProtectExceedTime();
            if(protectExceedTime == null) {
                throw new RuntimeException("保护时间获取失败");
            }
            cmCustProtectBuilder
                    .salerId(assignToSalerId)
                    .bussdeptId((employeeInfoBusinessDto.getOrgId()))
                    .buId(employeeInfoBusinessDto.getBuId())
                    .subcompanyId(employeeInfoBusinessDto.getSubId())
                    .protectTime(currentData)
                    .exceedTime(protectExceedTime)//当前时间+30天
                    .status(ProtectStateEnum.PROTECT.getState());

            //为了后面处理起来方便
            assignToSalerId = employeeInfoBusinessDto.getId();
            assignToDeptId = employeeInfoBusinessDto.getOrgId();
            assignToBuId = employeeInfoBusinessDto.getBuId();
            assignToSubId = employeeInfoBusinessDto.getSubId();
            assignToAreaId = employeeInfoBusinessDto.getAreaId();
        } else if (StringUtils.isNotBlank(assignToDeptId)) {

            OrgParentQueryBusinessView parentOrgByOrgId = orgInfoBusiness.getParentOrgByOrgId(OrgParentQueryBusinessDto.builder().orgId(assignToDeptId).build());
            cmCustProtectBuilder
                    .bussdeptId(parentOrgByOrgId.getDeptId())
                    .buId(parentOrgByOrgId.getBuId())
                    .subcompanyId(parentOrgByOrgId.getSubId())
                    .status(ProtectStateEnum.MANAGER_WILL_ASSIGN.getState());

            //为了后面处理起来方便
            assignToSalerId = null;
            assignToDeptId = parentOrgByOrgId.getDeptId();
            assignToBuId = parentOrgByOrgId.getBuId();
            assignToSubId = parentOrgByOrgId.getSubId();
            assignToAreaId = parentOrgByOrgId.getAreaId();

        } else if (StringUtils.isNotBlank(assignToBuId)) {

            OrgParentQueryBusinessView parentOrgByOrgId = orgInfoBusiness.getParentOrgByOrgId(OrgParentQueryBusinessDto.builder().orgId(assignToBuId).build());
            cmCustProtectBuilder
                    .buId(parentOrgByOrgId.getBuId())
                    .subcompanyId(parentOrgByOrgId.getSubId())
                    .status(ProtectStateEnum.BU_WILL_ASSIGN.getState());

            //为了后面处理起来方便
            assignToSalerId = null;
            assignToDeptId = null;
            assignToBuId = parentOrgByOrgId.getBuId();
            assignToSubId = parentOrgByOrgId.getSubId();
            assignToAreaId = parentOrgByOrgId.getAreaId();

        } else if (StringUtils.isNotBlank(assignToSubId)) {
            OrgParentQueryBusinessView parentOrgByOrgId = orgInfoBusiness.getParentOrgByOrgId(OrgParentQueryBusinessDto.builder().orgId(assignToSubId).build());

            cmCustProtectBuilder
                    .subcompanyId(parentOrgByOrgId.getSubId())
                    .status(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState());

            assignDateParam.setSubId(assignToSubId);
            assignDateParam.setTypeCode(ClueRuleEnum.SJ_MAJORWILL_ASSIGN_EXCEED.getValue());

            assignToSalerId = null;
            assignToDeptId = null;
            assignToBuId = null;
            assignToSubId = parentOrgByOrgId.getSubId();
            assignToAreaId = parentOrgByOrgId.getAreaId();
        }

        ClueRuleVo assignDateResult = clueRuleAppService.getOneByClueRule(assignDateParam);
        if(assignDateResult == null || assignDateResult.getTypeValue() == null) {
            throw new RuntimeException("分配规则缺失");
        }
        ClueRuleVo abptParamResult = clueRuleAppService.getOneByClueRule(abptParam);
        if(abptParamResult == null || abptParamResult.getTypeValue() == null) {
            throw new RuntimeException("分配规则缺失");
        }
        cmCustProtectBuilder
                .assignTime(currentData)
                .assignDate(DateUtil.offsetDay(currentData,assignDateResult.getTypeValue()))// +30
                .assignCustSource(specialActionView.getAssignCustSource())
                .updateTime(currentData)
                .updateBy(currentUser.getId())
                .customTags("")//备注要清掉
                .absoluteProtectTime(DateUtil.offsetDay(currentData,abptParamResult.getTypeValue()));

        Integer i = cmCustProtectService.updateBatchByCustIdList(custIdList, cmCustProtectBuilder.build());

        //写流转日志
        List<ConvertLogBusinessDto> businessDtoList = new ArrayList<>();
        for (String custId : custIdList) {
            Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(custId);
            if(customerData.isPresent()) {
                CustomerDataThirdView customerDataThirdView = customerData.get();
                ConvertLogBusinessDto build = ConvertLogBusinessDto.builder()
                        .custId(custId)
                        .curSalerId(assignToSalerId)
                        .deptOfCurSalerId(assignToDeptId)
                        .buOfCurSalerId(assignToBuId)
                        .subcompanyOfCurSalerId(assignToSubId)
                        .areaOfCurSalerId(assignToAreaId)
                        .createBy(currentUser.getId())
                        .createTime(currentData)
                        .convertType(specialActionView.getConvertType())
                        .custName(customerDataThirdView.getCustomerName())
                        .custType(customerDataThirdView.getProtectCustType() == null || customerDataThirdView.getProtectCustType().equals("null") ?
                                null : Integer.valueOf(customerDataThirdView.getProtectCustType()))
                        .entId(customerDataThirdView.getSourceDataId())//pid
                        .build();
                businessDtoList.add(build);
            }
        }
        smaConvertLogThirdService.saveBatch(businessDtoList);
        return BatchResultBusinessView.builder().successCount(custIdList.size()).build();
    }

    /*
     * @Description 根据前端触发的动作类型 构建一些塔特殊的值
     * <AUTHOR>
     * @date 2025/1/6 18:41
     * @param actionType
     * @return com.ce.scrm.center.service.business.entity.view.SpecialActionView
     */
    private SpecialActionView buildSpecialAction(Integer actionType) {
        if(actionType == null) {
            log.error("buildSpecialAction 参数为空");
            return null;
        }

        switch (actionType) {
            case 1:
                SpecialActionView build = SpecialActionView.builder()
                        .convertType(ConvertRelationEnum.SJ_ANEW_ASSIGN.getValue())
                        .assignCustSource(AssignCustSourceSpecialEnum.SJ_ANEW_ASSIGN.getValue())
                        .build();
                return build;
            default:
                return null;
        }
    }

    public Page<CustPoolQueryBusinessView> getCustPoolListPage(CustPoolQueryBusinessDto custPoolQueryBusinessDto) {

        log.info("getCustPoolListPage,入参custPoolQueryBusinessDto={}", JSONObject.toJSONString(custPoolQueryBusinessDto));

        ProtectBuildCustPoolView protectBuildCustPoolView = getCustPoolListPageBuildData(custPoolQueryBusinessDto);

        CmCustProtectNewQuery cmCustProtectNewQuery = BeanUtil.copyProperties(protectBuildCustPoolView, CmCustProtectNewQuery.class);
        Page<CustProtectView> custProtectViewPage = cmCustProtectService.selectPageByCondition(cmCustProtectNewQuery);

        List<CustProtectView> custProtectViewList = custProtectViewPage.getRecords();

        // build缓存map的数据
        List<String> busOppoCodeList = new ArrayList<>();
        List<String> marketIdList = new ArrayList<>();
        List<String> custIdList = new ArrayList<>();
        List<String> empIdList = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        List<String> reasonIdList = new ArrayList<>();
        for (CustProtectView custProtectView : custProtectViewList) {
            busOppoCodeList.add(custProtectView.getBusioppoCode());
            marketIdList.add(custProtectView.getMarkId());
            custIdList.add(custProtectView.getCustId());
            empIdList.add(custProtectView.getSalerId());
            orgIdList.add(custProtectView.getBussdeptId());
            orgIdList.add(custProtectView.getSubcompanyId());
            reasonIdList.add(custProtectView.getReason());
        }

        // 商机缓存map key: sjCode value: sjId
        List<String> busOppoCodeListFilter = busOppoCodeList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<SjIntentInfoView> sjIntentInfoViews = CollectionUtils.isEmpty(busOppoCodeListFilter) ?
                        new ArrayList<>() :
                        sjIntentInfoService.selectByCondition(SjIntentInfoQuery.builder().busOppoCodes(busOppoCodeListFilter).build());
        Map<String, String> sjCodeAndIdMap = sjIntentInfoViews.stream().collect(Collectors.toMap(SjIntentInfoView::getSjCode, SjIntentInfoView::getSjId, (key1, key2) -> key2));

        // 市场缓存map key: id value: name
        List<String> marketIdListFilter = marketIdList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<SmaMarketView> smaMarketViews = CollectionUtils.isEmpty(marketIdListFilter) ?
                new ArrayList<>() :
                smaMarketService.selectByCondition(SmaMarketQuery.builder().idList(marketIdListFilter).build());
        Map<String, String> marketIdAndNameMap = smaMarketViews.stream().collect(Collectors.toMap(SmaMarketView::getId, SmaMarketView::getName, (key1, key2) -> key2));

        // 客户缓存map key: id value: 客户对象
        List<String> custIdListFilter = custIdList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<CustomerDubboView> customerDubboViews = CollectionUtils.isEmpty(custIdListFilter) ?
                new ArrayList<>() :
                customerThirdService.getListByCondition(CustomerPageThirdDto.builder().customerIdList(custIdListFilter).build());
        Map<String, CustomerDubboView> customerIdEntityMap = customerDubboViews.stream().collect(Collectors.toMap(CustomerDubboView::getCustomerId, item -> item, (key1, key2) -> key2));

        // 释放原因缓存map key:reasonId value name
        List<String> reasonIdListFilter = reasonIdList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<SmaDictionaryItemNewView> smaDictionaryItemViews = CollectionUtils.isEmpty(reasonIdListFilter) ?
                new ArrayList<>() :
                smaDictionaryItemService.selectByCondition(SmaDictionaryItemQuery.builder().idList(reasonIdListFilter).build());
        Map<String, String> reasonIdAndNameMap = smaDictionaryItemViews.stream().collect(Collectors.toMap(SmaDictionaryItemNewView::getId, SmaDictionaryItemNewView::getName, (key1, key2) -> key2));

        // 员工缓存map key: id value: 员工
        List<String> empIdListFilter = empIdList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, EmployeeInfoThirdDto> employeeMap = employeeThirdService.getEmployeeDataMap(empIdListFilter);

        // 机构缓存map key: id value: 机构
        List<String> orgIdListFilter = orgIdList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, OrgDataThirdView> orgMap = orgThirdService.getOrgData(orgIdListFilter);

        List<CustPoolQueryBusinessView> resultList = new ArrayList<>();
        for (CustProtectView custProtectView : custProtectViewList) {

            CustPoolQueryBusinessView item = BeanCopyUtils.convertToVo(custProtectView, CustPoolQueryBusinessView.class);

            item.setBusiOppoId(sjCodeAndIdMap.get(custProtectView.getBusioppoCode()));

            item.setMarkName(marketIdAndNameMap.get(custProtectView.getMarkId()));

            item.setReasonStr(reasonIdAndNameMap.get(custProtectView.getReason()));

            CustomerDubboView customerDubboView = customerIdEntityMap.get(custProtectView.getCustId());
            if (customerDubboView != null) {
                item.setRegisterCapital(customerDubboView.getRegisterCapital());
                item.setEstablishDate(customerDubboView.getEstablishDate());
            }

            item.setBussdeptName(orgMap.get(custProtectView.getBussdeptId()) == null ? null : orgMap.get(custProtectView.getBussdeptId()).getName());
            item.setSubcompanyName(orgMap.get(custProtectView.getSubcompanyId()) == null ? null : orgMap.get(custProtectView.getSubcompanyId()).getName());
            item.setSalerName(employeeMap.get(custProtectView.getSalerId()) == null ? null : employeeMap.get(custProtectView.getSalerId()).getName());

            resultList.add(item);
        }

        Page<CustPoolQueryBusinessView> result = new Page<>();
        BeanUtil.copyProperties(custProtectViewPage, result);
        result.setRecords(resultList);
        return result;
    }

    /*
     * @Description 构建查询条件
     * <AUTHOR>
     * @date 2025/1/18 09:53
     * @param custPoolQueryBusinessDto
     * @return com.ce.scrm.center.service.business.entity.view.protect.ProtectBuildCustPoolView
     */
    private ProtectBuildCustPoolView getCustPoolListPageBuildData(CustPoolQueryBusinessDto custPoolQueryBusinessDto) {

        log.info("getCustPoolListPageBuildData,参数为custPoolQueryBusinessDto={}", JSONObject.toJSONString(custPoolQueryBusinessDto));

        ProtectBuildCustPoolView result = new ProtectBuildCustPoolView();

        EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
        if(currentUser == null) {
            throw new ApiException(CodeMessageEnum.NOT_LOGING_USER);
        }

        // 属性copy 不能直接copy有的值不需要复制
        result.setStatus(ProtectStateEnum.CUSTOMER_POOL.getState());
        result.setSource(custPoolQueryBusinessDto.getSource());
        result.setReason(custPoolQueryBusinessDto.getReason());
        result.setIsClock(custPoolQueryBusinessDto.getIsClock());
        result.setClockProvince(custPoolQueryBusinessDto.getClockProvince());
        result.setClockCity(custPoolQueryBusinessDto.getClockCity());
        result.setClockRegion(custPoolQueryBusinessDto.getClockRegion());
        result.setCustSourceSub(custPoolQueryBusinessDto.getCustSourceSub());
        result.setDeptId(custPoolQueryBusinessDto.getDeptId());
        result.setSalerId(custPoolQueryBusinessDto.getSalerId());
        result.setLikeCustName(custPoolQueryBusinessDto.getLikeCustName());
        result.setPageSize(custPoolQueryBusinessDto.getPageSize());
        result.setCurrentPage(custPoolQueryBusinessDto.getCurrentPage());
        result.setIsMarketSj(custPoolQueryBusinessDto.getIsMarketSj());

        Date startTime = custPoolQueryBusinessDto.getStartTime();
        Date endTime = custPoolQueryBusinessDto.getEndTime();
        if (startTime != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startTime);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            result.setStartTime(calendar.getTime());
        }
        if (endTime != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endTime);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            result.setEndTime(calendar.getTime());
        }

        if(Objects.equals(ProtectSourceEnum.RUN_AWAY.getCode(),custPoolQueryBusinessDto.getSource())) {

            //查询流失客户
            // 区总
            if(PositionUtil.isBusinessArea(currentUser.getPosition())) {
                result.setAreaId(currentUser.getAreaId());
            }else {
                result.setSubId(currentUser.getSubId());
            }

            // 商务代表
            if (PositionUtil.checkSalerRole(currentUser.getPosition())){
                result.setIsClock(0);
            }

        }else {

            //释放客户走 省市区 市场匹配
            String provinceCode = custPoolQueryBusinessDto.getProvince();
            String cityCode = custPoolQueryBusinessDto.getCity();
            String regionCode = custPoolQueryBusinessDto.getRegion();

            List<String> subIds = new ArrayList<>();
            if(PositionUtil.isBusinessArea(currentUser.getPosition())) {
                OrgChildrenQueryBusinessView orgChildrenQueryBusinessView = orgInfoBusiness.getChildrenList(OrgChildrenQueryBusinessDto.builder().id(currentUser.getAreaId()).build());
                List<OrgThirdDto> children = orgChildrenQueryBusinessView.getChildren();
                if (CollectionUtils.isNotEmpty(children)) {
                    subIds.addAll(children.stream().map(OrgThirdDto::getId).collect(Collectors.toList()));
                }
            }else {
                subIds.add(currentUser.getSubId());
            }

            List<String> areaCodeList = smaMarketBusiness.getAreaCodeListByCondition(
                    AreaCodeListQueryDto.builder()
                            .subIdList(subIds)
                            .provinceCode(provinceCode)
                            .cityCode(cityCode)
                            .regionCode(regionCode)
                            .build()
            );

            result.setRegionList(areaCodeList);

        }
        return result;
    }

    public Boolean saveData(ProtectUpdateBusinessDto saveBusinessDto) {
        CmCustProtect cmCustProtect = BeanUtil.copyProperties(saveBusinessDto, CmCustProtect.class);
        return cmCustProtectService.saveData(cmCustProtect);
    }

    /**
     * @description: 添加保护时长
     * @author: lijinpeng
     * @date: 2025/7/16 14:22
     * @param: [eventDto]
     * @return: java.lang.Boolean
     **/
    public Boolean addProtectTimeByEvent(AddProtectTimeEventBusinessDto eventDto) {

        log.info("addProtectTimeByEvent , eventDto={}", JSON.toJSONString(eventDto));

        if (eventDto == null || StringUtils.isBlank(eventDto.getCustomerId()) || eventDto.getEventType() == null) {
            log.error("addProtectTimeByEvent 入参异常 , eventDto={}", JSON.toJSONString(eventDto));
            return false;
        }

        CustProtectView custProtectView = cmCustProtectService.selectOneByCondition(CmCustProtect.builder().custId(eventDto.getCustomerId()).build());
        if (custProtectView == null) {
            log.info("addProtectTimeByEvent 没有保护关系 , eventDto={}", JSON.toJSONString(eventDto));
            return false;
        }

        log.info("addProtectTimeByEvent 获取保护关系 , custProtectView={}", JSON.toJSONString(custProtectView));

        // 保护状态不为1 则返回
        if (!Objects.equals(custProtectView.getStatus(),ProtectStateEnum.PROTECT.getState())) {
            return false;
        }

        // 成交客户不需要加
        if (!Objects.equals(custProtectView.getCustType(),2)) {
            return false;
        }

        // 客户是否在三天内 （非保护期）
        if (!Objects.equals(custProtectView.getBusinessOpportunityConfirmationFlag(),0) && custProtectView.getBusinessOpportunityConfirmationFlag() != null) {
            return false;
        }

        // 保护时长达到180天
        Date exceedTime = custProtectView.getExceedTime();
        Date protectTime = custProtectView.getProtectTime();
        long diffInMillis = exceedTime.getTime() - protectTime.getTime();
        long diffInDays = TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS);
        if (diffInDays >= 180) {
            return false;
        }

        // 剩余保护时长是否大于30天
        Date currentDate = new Date();
        long diffInMillis2 = exceedTime.getTime() - currentDate.getTime();
        long diffInDays2 = TimeUnit.DAYS.convert(diffInMillis2, TimeUnit.MILLISECONDS);
        if (diffInDays2 >= 30) {
            return false;
        }

        if (Objects.equals(eventDto.getEventType(),1)) {
            // 会销签到 这里什么也不做  直接到后面可以添加保护时长
        } else if (Objects.equals(eventDto.getEventType(),2)) {
            // 如果是打卡触发

            // 判断是否是跨境
            if (!PositionUtil.isKj(custProtectView.getAreaId())) {
                // 判断是否绑定客户
                if (!Objects.equals(custProtectView.getBindFlag(),1)) {
                    return false;
                }
            }else {
                // 跨境加时间
            }
        } else if (Objects.equals(eventDto.getEventType(),3)) {
            // 绑定触发

            // 判断是否是跨境
            if (!PositionUtil.isKj(custProtectView.getAreaId())) {
                // 判断从保护到现在是否打卡
                if(!isClock(custProtectView)) {
                    return false;
                }
            }else {
                // 跨境的绑定不加时间
                return false;
            }
        } else {
            log.error("eventType错误 , eventDto={}", JSON.toJSONString(eventDto));
            return false;
        }

        // 添加保护时长
        addProtectTime(custProtectView);
        return true;
    }

    private boolean isClock(CustProtectView custProtectView) {

        Date protectTime = custProtectView.getProtectTime();
        List<EmpCustSiteClockThirdView> empCustSiteClockThirdViews = empCustSiteClockThirdService.selectByCondition(ClockConditionQueryThirdDto.builder().custId(custProtectView.getCustId()).empId(custProtectView.getSalerId()).startTime(protectTime).markClockValidFlag(2).build());
        if (CollectionUtils.isEmpty(empCustSiteClockThirdViews)) {
            return false;
        }
        return true;
    }

    /**
     * @description: 添加30天
     * @author: lijinpeng
     * @date: 2025/7/16 14:10
     * @param: [custProtectView]
     * @return: void
     **/
    private void addProtectTime(CustProtectView custProtectView) {

        Date exceedTime = custProtectView.getExceedTime();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(exceedTime);
        calendar.add(Calendar.DATE, 30); // 这里可以是正数(加)或负数(减)
        Date time = calendar.getTime();

        custProtectView.setExceedTime(time);

        CmCustProtect cmCustProtect = BeanUtil.copyProperties(custProtectView, CmCustProtect.class);
        cmCustProtectService.updateByCustId(cmCustProtect);

    }

    /**
     * @description: 商机：确认保护
     * @author: lijinpeng
     * @date: 2025/7/21 13:52
     * @param: [businessDto]
     * @return: java.lang.Boolean
     **/
    public Boolean protectConfirm(ProtectConfirmBusinessDto businessDto) {

        log.info("protectConfirmBusinessDto={}", JSON.toJSONString(businessDto));
        if (businessDto == null || StringUtils.isBlank(businessDto.getCustomerId())) {
            throw new ApiException(CodeMessageEnum.INVALID_PARAM);
        }

        MyProtectCustomerCapacityedCountBusinessView myProtectCustomerCapacityedCountBusinessView = potentialCustomerBusiness.myProtectCapacity();
        if (myProtectCustomerCapacityedCountBusinessView == null || myProtectCustomerCapacityedCountBusinessView.getNowCapacityedCount() < 1) {
            throw new ApiException(CodeMessageEnum.PROTECTION_INVENTORY_IS_FULL);
        }

        CustProtectView custProtectView = cmCustProtectService.selectOneByCondition(CmCustProtect.builder().custId(businessDto.getCustomerId()).build());
        if (custProtectView == null) {
            throw new ApiException(CodeMessageEnum.DATA_NOT_EXIST);
        }

        if (!Objects.equals(custProtectView.getStatus(),ProtectStateEnum.PROTECT.getState())) {
            return false;
        }

        if (!Objects.equals(custProtectView.getSalerId(),businessDto.getLoginEmployeeId())) {
            throw new ApiException(CodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }

        if (Objects.equals(custProtectView.getBusinessOpportunityConfirmationFlag(),0) || custProtectView.getBusinessOpportunityConfirmationFlag() == null) {
            return true;
        }

        custProtectView.setBusinessOpportunityConfirmationFlag(0);
        custProtectView.setOccupy(0);
        custProtectView.setExceedTime(DateUtil.offsetDay(custProtectView.getExceedTime(), 27));
        cmCustProtectService.updateByCustId(custProtectView);

        return true;
    }

    public Boolean allot(ProtectAllotBusinessDto businessDto) {

        log.info("allot,SDR调拨,businessDto={}",JSONObject.toJSONString(businessDto));

        String customerId = businessDto.getCustomerId();
        String oldSalerId = businessDto.getOldSalerId();
        String newSalerId = businessDto.getNewSalerId();
        Date currentDate = new Date();
        CustProtectView custProtectView = cmCustProtectService.selectOneByCondition(CmCustProtect.builder().custId(customerId).build());

        EmployeeInfoBusinessDto oldEmployee = employeeInfoBusiness.getEmployeeInfoByEmpId(oldSalerId);
        if (oldEmployee == null ||
                (!PositionUtil.isSdr(oldEmployee.getPosition()) && !PositionUtil.isCc(oldEmployee.getPosition()))){
            return false;
        }
        EmployeeInfoBusinessDto newEmployee = employeeInfoBusiness.getEmployeeInfoByEmpId(newSalerId);
        if (newEmployee == null) {
            return false;
        }

        if (custProtectView == null ||
                !Objects.equals(custProtectView.getStatus(),ProtectStateEnum.PROTECT.getState()) ||
                !Objects.equals(custProtectView.getSalerId(),oldSalerId)) {
            return false;
        }

        custProtectView.setSalerId(newEmployee.getId());
        custProtectView.setBussdeptId(newEmployee.getOrgId());
        custProtectView.setBuId(newEmployee.getBuId());
        custProtectView.setAreaId(newEmployee.getAreaId());
        custProtectView.setProtectTime(currentDate);
        custProtectView.setStatus(ProtectStateEnum.PROTECT.getState());
        custProtectView.setExceedTime(DateUtil.offsetDay(currentDate, 30));
        custProtectView.setBindFlag(0);
        cmCustProtectService.updateByCustId(custProtectView);

        ConvertLogBusinessDto convertLogBusinessDto = ConvertLogBusinessDto.builder()
                .custId(customerId)
                .createTime(new Date())
                .createBy("1024")
                .convertType(cn.ce.cesupport.enums.favorites.ConvertRelationEnum.PROTECT_FROM_SDR_TO_SALER.getValue())
                .releaseReason("客户从["+ oldEmployee.getName()
                        + "]变为["+ newEmployee.getName()
                        + "]")
                .custName(custProtectView.getCustName())
                .salerId(oldEmployee.getId())
                .deptOfSalerId(oldEmployee.getOrgId())
                .buOfSalerId(oldEmployee.getBuId())
                .subcompanyOfSalerId(oldEmployee.getSubId())
                .areaOfSalerId(oldEmployee.getAreaId())
                .curSalerId(newEmployee.getId())
                .deptOfCurSalerId(newEmployee.getOrgId())
                .buOfCurSalerId(newEmployee.getBuId())
                .subcompanyOfCurSalerId(newEmployee.getSubId())
                .areaOfCurSalerId(newEmployee.getAreaId())
                .build();
        smaConvertLogThirdService.insertLog(convertLogBusinessDto);

        return true;
    }

}