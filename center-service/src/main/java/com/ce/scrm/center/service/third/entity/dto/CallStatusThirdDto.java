package com.ce.scrm.center.service.third.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @version 1.0
 * @Description 拨打状态查询
 */
@Data
public class CallStatusThirdDto implements Serializable {

	private static final long serialVersionUID = 1L;

	Map<String, CallCenterStatusThirdDto> callcenterStatusViewMap;

	@Data
	public static class CallCenterStatusThirdDto implements Serializable {
		private static final long serialVersionUID = 1L;

		/**
		 * 0:未开启，仅可以复制
		 * 1:可以正常拨打
		 * 2:黑名单
		 * 3:超频，如果是超频 callMessage 会给出下次拨打提示，前端直接展示
		 */
		private String callStatus;

		/**
		 * 不能拨打原因
		 */
		private String callMessage;

		/**
		 * 当 callStatus =1 的时候有值
		 * 第三方线路编码
		 */
		private String lineCode;

		/**
		 * 第三方线路名称
		 */
		private String lineName;

		/**
		 * 第三方线路类型 1普通 2双呼
		 */
		private Integer lineType;

		/**
		 * 分公司ID
		 */
		private String subId;
	}
}
