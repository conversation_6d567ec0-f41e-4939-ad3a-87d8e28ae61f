/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.blacklist;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.AuthType;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

public class BlackListQuery extends BaseParam {

    /**
     * 查询关键词，例如手机号，公众号openId,自定义用户ID等
     */
    private String keywords;

    /**
     * 授权方式
     */
    private AuthType authType;


    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> map = getBaseParamsMap();
        if (StrUtil.isNotEmpty(keywords)) {
            map.put("keywords", keywords);

        }
        if (authType != null) {
            map.put("authType", String.valueOf(authType.getValue()));
        }
        return map;

    }

    @Override
    public void validate() {

    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public void setAuthType(AuthType authType) {
        this.authType = authType;
    }
}
