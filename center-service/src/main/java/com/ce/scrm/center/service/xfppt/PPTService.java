package com.ce.scrm.center.service.xfppt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.AiPptGeneratorLog;
import com.ce.scrm.center.dao.service.AiPptGeneratorLogService;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.UUID;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Level.ONE_MINUTE;
import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_TOPIC;

@Slf4j
@Service
public class PPTService {

    @Autowired
    private RocketMqOperate rocketMqOperate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public static String prefixPPt = "SCRM:PPT:AI:";


    @Autowired
    private SendWxMessage sendWxMessage;

    @Autowired
    private AiPptGeneratorLogService aiPptGeneratorLogService;

    public void generatorPPTFromAnalysisiTips(String custName, String chatId, String employeeId, String employeeName, String text) {
        try {
            String id = UUID.randomUUID().toString().replace("-", "");
            PPTRequestParam pptRequestParam = new PPTRequestParam();
            pptRequestParam.setCustName(custName);
            pptRequestParam.setChatId(chatId);
            pptRequestParam.setEmployeeId(employeeId);
            pptRequestParam.setEmployeeName(employeeName);
            pptRequestParam.setText(text);
            pptRequestParam.setId(id);
            aiPptGeneratorLogService.save(
                    new AiPptGeneratorLog(id, employeeId, chatId, employeeName, custName, text, null, 0, new Date(), new Date())
            );
            log.info("发送到 mq信息:{}", JSONObject.toJSONString(pptRequestParam));
            rocketMqOperate.syncSend(SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_TOPIC, JSONObject.toJSONString(pptRequestParam));
        } catch (Exception e) {
            log.error("生成 PPT 异常", e);
            throw new RuntimeException(e);
        }
    }


    public JSONObject getSid(String text, PPTKeyReq pptRequestParamKey) {
        try {
            long timestamp = System.currentTimeMillis() / 1000;
            String ts = String.valueOf(timestamp);
            ApiAuthAlgorithm auth = new ApiAuthAlgorithm();
            String signature = auth.getSignature(pptRequestParamKey.getAppId(), pptRequestParamKey.getApiSecret(), timestamp);
            // 建立链接
            ApiClient client = new ApiClient("https://zwapi.xfyun.cn/api/ppt/v2");
            log.info("请求生成 PPT get sid 结果内容:{}", text);
            String resp2 = client.create(pptRequestParamKey.getAppId(), ts, signature, text.getBytes(StandardCharsets.UTF_8));
            log.info("请求生成 PPT get sid :{}", resp2);
            JSONObject jsonObject = JSON.parseObject(resp2);
            return jsonObject;
        } catch (Exception e) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("result", e.getMessage());
            return jsonObject;
        }
    }


    public JSONObject getPPTUrl(String sid, PPTKeyReq pptRequestParamKey) {
        try {
            long timestamp = System.currentTimeMillis() / 1000;
            String ts = String.valueOf(timestamp);
            ApiAuthAlgorithm auth = new ApiAuthAlgorithm();
            String signature = auth.getSignature(pptRequestParamKey.getAppId(), pptRequestParamKey.getApiSecret(), timestamp);
            // 建立链接
            ApiClient client = new ApiClient("https://zwapi.xfyun.cn/api/ppt/v2");
            log.info("生产PPT 查询请求:{}", sid);
            String progressResult = client.checkProgress(pptRequestParamKey.getAppId(), ts, signature, sid);
            log.info("生成 PPT查询结果:{}", progressResult);
            JSONObject jsonObject = JSON.parseObject(progressResult);
            return jsonObject;
        } catch (IOException e) {
            log.warn("生成PPT获取 ppdurl 异常", e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("result", e.getMessage());
            return jsonObject;
        }
    }

//
//    public String getPPTUrl(PPTRequestParam pptRequestParam) {
//        try {
//            String pptKey = stringRedisTemplate.opsForValue().get(prefixPPt + pptRequestParam.getEmployeeId());
//            PPTKeyReq pptRequestParamKey = new PPTKeyReq();
//            if (StringUtils.hasText(pptKey)) {
//                pptRequestParamKey = JSONObject.parseObject(pptKey, PPTKeyReq.class);
//            } else {
//                log.error("pptkey获取异常employeeId：{}", pptRequestParam.getEmployeeId());
//                return "1";
//            }
//            // 获取请求头中需要携带的参数 appId（控制台获取）, timestamp（时间戳，单位：秒，与服务端时间相差五分钟之内）, signature（签名）
//            long timestamp = System.currentTimeMillis() / 1000;
//            String ts = String.valueOf(timestamp);
//            ApiAuthAlgorithm auth = new ApiAuthAlgorithm();
//            String signature = auth.getSignature(pptRequestParamKey.getAppId(), pptRequestParamKey.getApiSecret(), timestamp);
//            // 建立链接
//            ApiClient client = new ApiClient("https://zwapi.xfyun.cn/api/ppt/v2");
//            String progressResult = client.checkProgress(pptRequestParamKey.getAppId(), ts, signature, pptRequestParam.getSid());
//            log.info("生成 PPT查询结果:{}", progressResult);
//            JSONObject jsonObject = JSON.parseObject(progressResult);
//            String pptUrl = jsonObject.getJSONObject("data").getString("pptUrl");
//            if (StringUtils.hasText(pptUrl)) {
//                aiPptGeneratorLogService.lambdaUpdate().eq(AiPptGeneratorLog::getId, pptRequestParam.getId()).set(AiPptGeneratorLog::getStatus, 2).set(AiPptGeneratorLog::getPptResult, progressResult).update();
//                return pptUrl;
//            } else {
//                aiPptGeneratorLogService.lambdaUpdate().eq(AiPptGeneratorLog::getId, pptRequestParam.getId()).set(AiPptGeneratorLog::getStatus, 1).set(AiPptGeneratorLog::getPptResult, progressResult).update();
//                rocketMqOperate.syncSend(SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_TOPIC, JSONObject.toJSONString(pptRequestParam), ONE_MINUTE);
//            }
//        } catch (IOException e) {
//            log.warn("生成PPT获取 ppdurl 异常", e);
//            rocketMqOperate.syncSend(SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_TOPIC, JSONObject.toJSONString(pptRequestParam), ONE_MINUTE);
//        }
//        return null;
//    }


//    public String getSid(String text, String employeeId, String id) {
//        PPTKeyReq pptRequestParamKey = new PPTKeyReq();
//        try {
//            long timestamp = System.currentTimeMillis() / 1000;
//            String ts = String.valueOf(timestamp);
//            ApiAuthAlgorithm auth = new ApiAuthAlgorithm();
//            String pptKey = stringRedisTemplate.opsForValue().get(prefixPPt + employeeId);
//            if (StringUtils.hasText(pptKey)) {
//                pptRequestParamKey = JSONObject.parseObject(pptKey, PPTKeyReq.class);
//            } else {
//                log.error("pptkey获取异常employeeId：{}", employeeId);
//                return "1";
//            }
//            String signature = auth.getSignature(pptRequestParamKey.getAppId(), pptRequestParamKey.getApiSecret(), timestamp);
//            // 建立链接
//            ApiClient client = new ApiClient("https://zwapi.xfyun.cn/api/ppt/v2");
//            log.info("请求生成 PPT结果内容:{}", text);
//            String resp2 = client.create(pptRequestParamKey.getAppId(), ts, signature, text.getBytes(StandardCharsets.UTF_8));
//            log.info("生成ppt请求结果:{}", resp2);
//            JSONObject jsonObject = JSON.parseObject(resp2);
//            String sid = jsonObject.getJSONObject("data").getString("sid");
//            if (org.apache.commons.lang3.StringUtils.isNotBlank(id)) {
//                aiPptGeneratorLogService.lambdaUpdate().eq(AiPptGeneratorLog::getId, id).set(AiPptGeneratorLog::getStatus, 1).update();
//            } else {
//                aiPptGeneratorLogService.lambdaUpdate().eq(AiPptGeneratorLog::getId, id).set(AiPptGeneratorLog::getStatus, 3).update();
//            }
//            return sid;
//        } catch (Exception e) {
//            log.warn("生成 PPT获取 sid 异常,员工ID:" + employeeId + ",keyInfo:" + JSONObject.toJSONString(pptRequestParamKey), e);
//            sendWxMessage.sendMessage(employeeId, "非常抱歉，生成PPT功能的免费试用期已结束，请复制整合好的方案，在网络上自行搜索PPT生成工具。\n" +
//                    "给您带来的不便，敬请谅解，我们后续会持续寻找更合适的解决方案");
//            return "1";
////            if (null != e.getMessage() && e.getMessage().contains("code=405")) {
////                return "1";
////            }
//            // return null;
//        }
//    }

    @Data
    public static class PPTKeyReq implements Serializable {

        private String appId;

        private String apiSecret;

        private String loginEmployeeId;

    }

}
