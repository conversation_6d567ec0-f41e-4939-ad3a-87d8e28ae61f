package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 大数据结果
 * <AUTHOR>
 * @date 2024/1/15 11:14
 * @version 1.0.0
 **/
@Data
public class BigDataResult<T> implements Serializable {
    /**
     * 状态返回码
     *  成功:200
     *  失败:非200
     */
    private Integer code;
    /**
     * 返回的数据
     */
    private T data;
    /**
     * 错误信息
     */
    private String msg;
    /**
     * 状态码
     */
    private String status;
    /**
     * 当前页
     */
    private Integer currentPage;
    /**
     * 数据总条数
     */
    private Integer total;
    /**
     * 总页数
     */
    private Integer totalPage;
}