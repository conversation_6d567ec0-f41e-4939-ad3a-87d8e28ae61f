package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * description: 企业简介
 * @author: DD.Jiu
 * date: 2025/2/13.
 */
@Data
public class BigDataEntProfileDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业简介
     */
    @JSONField(name = "企业简介")
    private String ent_profile;
}
