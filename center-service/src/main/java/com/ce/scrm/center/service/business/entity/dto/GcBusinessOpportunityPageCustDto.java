package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * description: 高呈客户列表查询条件
 * @author: DD.Jiu
 * date: 2024/5/20.
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GcBusinessOpportunityPageCustDto {
    /**
     * 操作人岗位
     */
    private String operatorPosition;
    /**
     * 操作人区域ID
     */
    private String operatorAreaId;
    /**
     * 操作人分司ID
     */
    private String operatorSubId;
    /**
     * 操作人部门ID
     */
    private String operatorDeptId;
    /**
     * 操作人ID
     */
    private String operator;
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 高呈商务id
     */
    private String gcSalerId;
    /**
     * 高呈商机id
     */
    private String sjId;
    /**
     * 商务代表ID
     */
    private String salerId;
    /**
     * 分司ID
     */
    private String subId;
    /**
     * 高呈商机状态，GcSjStateEnum
     */
    private Integer state;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;
}
