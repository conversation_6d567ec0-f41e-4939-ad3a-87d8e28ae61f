package com.ce.scrm.center.service.eqixiu.sdk.service;

import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.common.Result;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Creation;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.CreationType;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.creation.*;

import com.ce.scrm.center.service.eqixiu.sdk.support.HttpClient;
import com.ce.scrm.center.service.eqixiu.sdk.support.TokenCache;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;
import com.ce.scrm.center.service.eqixiu.support.entity.UserInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 封装易企秀编辑器提供的接口
 *
 * <AUTHOR>
 */
public class CreationService extends ConnectService {

    private static final String API_CREATION_LIST = "/api/v1/editor/creation/list";

    private static final String API_CREATION_INFO = "/api/v1/editor/creation/info";

    private static final String API_CREATION_BY_CUSTOMER_LIST = "/api/v1/editor/creation/creation_by_customer/list";

    private static final String API_CREATION_BY_CUSTOMER_LIST_WITH_HD = "/api/v1/editor/creation/creation_by_customer/list/hd";
    private static final String API_CREATION_CREATE = "/api/v1/editor/creation/create";
    private static final String API_CREATION_PUBLISH = "/api/v1/editor/creation/publish";
    private static final String API_CREATION_COPY = "/api/v1/editor/creation/copy";
    private static final String API_CREATION_DELETE = "/api/v1/editor/creation/delete";
    private static final String API_CREATION_PREVIEW_TOKEN = "/api/v1/editor/creation/preview/token";
    private static final String API_CREATION_EDIT_SHARE_INFO = "/api/v1/editor/creation/share/setting";
    private static final String API_CREATION_DATA_SAVE = "/api/v1/editor/creation/dyn/data/save";
    private static final String API_CREATION_IMAGE_GET = "/api/v1/editor/design/pic";
    private static final String API_GIFTCODE_MY_GET = "/api/v1/editor/hd/giftcode/list/my";
    private static final String API_GIFTCODE_HD_GET = "/api/v1/editor/hd/giftcode/list";


    public CreationService(Secret secret) {
        super(secret);
    }

    public CreationService(Secret secret, TokenCache tokenCache) {
        super(secret, tokenCache);
    }

    public CreationService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        super(secret, tokenCache, httpClient);
    }

    /**
     * 查询指定条件的作品列表
     * 用于展示指定员工的作品列表
     *
     * @param query
     * @return
     */
    public Result<Creation> findCreationList(CreationQuery query) {
        paramValidate(query);
        JSONObject obj = httpClient.httpGet(getApiURL(API_CREATION_LIST), null, query.getParamsMap());
        return getResult(obj, Creation.class);
    }


    public String addDynParam(String dataId, Map<String, String> dynParams, String creationId) {
        HashMap<String, String> params = createParamMapWithToken();
        params.put("dataId", dataId);
        params.put("creationId", String.valueOf(creationId));
        params.putAll(dynParams);
        JSONObject obj = httpClient.httpPost(getApiURL(API_CREATION_DATA_SAVE, params), null, params);
        return obj.getJSONObject("obj").getStr("shareUrl");
    }

    public List<UserInfo> getGiftCodeHdGet(String pageNo, String pageSize) {
        HashMap<String, String> params = createParamMapWithToken();
        params.put("status", "1");
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);
        JSONObject obj = httpClient.httpGet(getApiURL(API_GIFTCODE_HD_GET, params), null, params);
        logger.info("请求易企秀返回信息:{}", obj.toString());
        return obj.getList(UserInfo.class);
    }

    public List<UserInfo> getGiftCodeMyGet(String pageNo, String pageSize) {
        HashMap<String, String> params = createParamMapWithToken();
        params.put("status", "1");
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);
        JSONObject obj = httpClient.httpGet(getApiURL(API_GIFTCODE_MY_GET, params), null, params);
        logger.info("请求易企秀返回信息:{}", obj.toString());
        return obj.getList(UserInfo.class);
    }

    /**
     * 获取指定访客参与的所有作品列表
     *
     * @param query
     * @return
     */
    public Result<Creation> findCreationByCustomerList(CreationByCustomerQuery query) {
        return getCreationResult(query, API_CREATION_BY_CUSTOMER_LIST);

    }

    /**
     * 互动作品list
     *
     * @param query
     * @return
     */
    public Result<Creation> findCreationByCustomerWithHd(CreationByCustomerQuery query) {
        query.setCreationType(CreationType.HD);
        return getCreationResult(query, API_CREATION_BY_CUSTOMER_LIST_WITH_HD);
    }

    /**
     * 根据作品ID获取作品信息。
     *
     * @param id 作品的ID
     * @return 返回作品信息
     */
    public Creation getCreationInfoById(Long id) {
        if (id == null) {
            throw new KnownException("作品ID不能为空");
        }
        HashMap<String, String> map = createParamMapWithToken();
        map.put("id", String.valueOf(id));

        JSONObject obj = httpClient.httpGet(getApiURL(API_CREATION_INFO), null, map);
        printLog(obj, "获取作品信息失败，{}");
        return obj.getObj(Creation.class);

    }

    /**
     * 创建作品
     *
     * @param cmd
     * @return
     */
    public CreationCreateVO create(CreateCreationCmd cmd) {
        paramValidate(cmd);
        JSONObject obj = httpClient.httpPost(getApiURL(API_CREATION_CREATE, cmd.getParamsMap()), null, cmd.getParamsMap());
        if (!obj.getSuccess()) {
            logger.warn("创建作品失败，{}", obj.getMsg());
            return null;
        }
        Map result = obj.getObj(Map.class);
        return new CreationCreateVO(Long.valueOf(result.get("creationId").toString()), (String) result.get("url"));
    }

    /**
     * 发布作品
     *
     * @param openId
     * @param creationId
     * @return
     */
    public boolean pushCreation(String openId, Long creationId) {
        if (openId == null || creationId == null) {
            throw new KnownException("openId和creationId不能为空");
        }
        HashMap<String, String> params = createParamMapWithToken();
        params.put("openId", openId);
        params.put("creationId", String.valueOf(creationId));
        JSONObject obj = httpClient.httpPost(getApiURL(API_CREATION_PUBLISH, params), null, params);
        printLog(obj, "发布作品失败，{}");
        return obj.getSuccess();
    }

    /**
     * 复制作品
     *
     * @param creationId   复制作品 ID
     * @param openId       操作员工 ID
     * @param renamePrefix 作品名前缀，如原作品为“易企秀作品”，复制后为“复制-易企秀作品”，即默认前缀为“复制-”
     * @return
     */
    public Long copyCreation(Long creationId, String openId, String renamePrefix) {
        Map<String, String> params = createParamMapWithToken();
        if (openId == null || creationId == null) {
            throw new KnownException("openId和creationId不能为空");
        }
        params.put("openId", openId);
        params.put("id", String.valueOf(creationId));
        if (StrUtil.isNotEmpty(renamePrefix)) {
            params.put("renamePrefix", renamePrefix);
        }
        JSONObject obj = httpClient.httpPost(getApiURL(API_CREATION_COPY, params), null, params);
        printLog(obj, "复制作品失败，{}");
        return obj.getLong("obj");
    }

    /**
     * 删除作品
     *
     * @param creationId
     * @param openId
     * @return
     */
    public boolean deleteCreation(Long creationId, String openId) {
        if (creationId == null || openId == null) {
            throw new KnownException("openId和creationId不能为空");
        }
        HashMap<String, String> params = createParamMapWithToken();
        params.put("id", String.valueOf(creationId));
        params.put("openId", openId);
        JSONObject obj = httpClient.httpPost(getApiURL(API_CREATION_DELETE, params), null, params);
        printLog(obj, "删除作品失败，{}");
        return obj.getSuccess();
    }

    /**
     * 获取预览 Token
     *
     * @param creationId
     * @return 作品的预览token，绑定作品，有效期为30分钟
     * 拿到token后将token放到预览链接上即可无需授权预览活动。活动链接：https://{domain}.eqxiu.cn/pv/10086?token={token}
     */
    public String getPreviewToken(Long creationId) {
        if (creationId == null) {
            throw new KnownException("creationId不能为空");
        }
        HashMap<String, String> params = createParamMapWithToken();
        params.put("creationId", String.valueOf(creationId));
        JSONObject obj = httpClient.httpPost(getApiURL(API_CREATION_PREVIEW_TOKEN, params), null, params);
        printLog(obj, "获取预览 Token 失败,{}");
        return obj.getStr("obj");
    }

    /**
     * 修改作品分享信息
     *
     * @param cmd
     * @return
     */
    public boolean editCreationShareInfo(EditCreationShareCmd cmd) {
        paramValidate(cmd);
        JSONObject obj = httpClient.httpPost(getApiURL(API_CREATION_EDIT_SHARE_INFO, cmd.getParamsMap()), null, cmd.getParamsMap());
        printLog(obj, "修改作品分享信息失败，{}");
        return obj.getSuccess();
    }

    private Result<Creation> getCreationResult(CreationByCustomerQuery query, String apiUrl) {
        paramValidate(query);
        JSONObject obj = httpClient.httpGet(getApiURL(apiUrl), null, query.getParamsMap());
        return getResult(obj, Creation.class);
    }
}
