package com.ce.scrm.center.service.business;

import cn.ce.cesupport.framework.base.enums.CommonYesNoEnum;
import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.service.business.entity.dto.openapi.SaveCallRecordKjBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.openapi.SaveRecordKjBusinessDto;
import com.ce.scrm.center.service.utils.FileUploadUtil;
import com.ce.scrm.extend.dubbo.api.CloudCustomerServiceCallLogDubbo;
import com.ce.scrm.extend.dubbo.entity.dto.CloudCustomerServiceCallLogDubboDto;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.CloudCustomerServiceCallLogDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;

/**
 * @version 1.0
 * @Description: 录音推送跨境
 * @Author: lijinpeng
 * @Date: 2025/3/19 09:45
 */
@Slf4j
@Service
public class RecordKjBusiness {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private CloudCustomerServiceCallLogDubbo cloudCustomerServiceCallLogDubbo;

    @Value("${file.upload.dir}")
    private String uploadDir;

    @Value("${file.upload.url}")
    private String uploadUrl;

    public Boolean saveRecordKj(@Valid SaveRecordKjBusinessDto saveRecordKjBusinessDto) {

        DubboResult<CloudCustomerServiceCallLogDubboView> detail = cloudCustomerServiceCallLogDubbo.detail(saveRecordKjBusinessDto.getMainUniqueId());
        if (detail == null || !detail.checkSuccess()) {
            return false;
        }
        if (detail.getData() == null) {
            log.info("没有找到此通话记录，callId={}",saveRecordKjBusinessDto.getMainUniqueId());
            return Boolean.TRUE;
        }

        CloudCustomerServiceCallLogDubboView data = detail.getData();
        CloudCustomerServiceCallLogDubboDto cloudCustomerServiceCallLogDubboDto = BeanUtil.copyProperties(data, CloudCustomerServiceCallLogDubboDto.class);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String format = sdf.format(new Date());
        String filePath = "/recordKj/" + format + "/";
        String fileName = saveRecordKjBusinessDto.getRecordFileIndex()+".mp3";
        String localUrlPath = uploadUrl + filePath + fileName;
        FileUploadUtil.saveUrlAs(saveRecordKjBusinessDto.getPreSignUrl(),uploadDir+filePath,fileName,"GET");

        cloudCustomerServiceCallLogDubboDto.setRecordUrl(localUrlPath);
        cloudCustomerServiceCallLogDubbo.updateByCallId(cloudCustomerServiceCallLogDubboDto);

        return Boolean.TRUE;
    }

    public Boolean saveCallRecordKj(SaveCallRecordKjBusinessDto saveDto) {

        CloudCustomerServiceCallLogDubboDto cloudCustomerServiceCallLogDubboDto = new CloudCustomerServiceCallLogDubboDto();
        cloudCustomerServiceCallLogDubboDto.setCallId(saveDto.getMainUniqueId());
        cloudCustomerServiceCallLogDubboDto.setCallType(saveDto.getCallType());

        if(saveDto.getStartTime() != null) {
            Instant instant1 = Instant.ofEpochSecond(saveDto.getStartTime());
            LocalDateTime startTime = LocalDateTime.ofInstant(instant1, ZoneId.systemDefault());
            cloudCustomerServiceCallLogDubboDto.setStartTime(startTime);
        }

        if(saveDto.getEndTime() != null) {
            Instant instant2 = Instant.ofEpochSecond(saveDto.getEndTime());
            LocalDateTime endTime = LocalDateTime.ofInstant(instant2, ZoneId.systemDefault());
            cloudCustomerServiceCallLogDubboDto.setEndTime(endTime);
        }

        if(saveDto.getAnswerTime() != null) {
            Instant instant3 = Instant.ofEpochSecond(saveDto.getAnswerTime());
            LocalDateTime answerTime = LocalDateTime.ofInstant(instant3, ZoneId.systemDefault());
            cloudCustomerServiceCallLogDubboDto.setAnswerTime(answerTime);
        }

        cloudCustomerServiceCallLogDubboDto.setCustomerNumber(saveDto.getCustomerNumber());
        cloudCustomerServiceCallLogDubboDto.setCustomerProvince(saveDto.getCustomerProvince());
        cloudCustomerServiceCallLogDubboDto.setCustomerCity(saveDto.getCustomerCity());
        cloudCustomerServiceCallLogDubboDto.setHangUpParty(saveDto.getOnHookSource());
        if( saveDto.getEndTime() != null && saveDto.getAnswerTime() != null) {
            long callDuration = saveDto.getEndTime()- saveDto.getAnswerTime();
            cloudCustomerServiceCallLogDubboDto.setCallDuration(Integer.valueOf(Long.toString(callDuration)));
        }
        cloudCustomerServiceCallLogDubboDto.setSourceType(2);
        if(Objects.equals(saveDto.getCallType(),"1")) {
            cloudCustomerServiceCallLogDubboDto.setServiceNumber(saveDto.getHotline());
        }else if(Objects.equals(saveDto.getCallType(),"4")) {
            cloudCustomerServiceCallLogDubboDto.setDisNumber(saveDto.getHotline());
        }
        DubboResult<Boolean> add = cloudCustomerServiceCallLogDubbo.add(cloudCustomerServiceCallLogDubboDto);

        return add.checkSuccess();
    }

}
