package com.ce.scrm.center.service.enums;

import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityRequirementData;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 高呈商机需求枚举
 * <AUTHOR>
 * @date 2024/5/13 下午2:16
 * @version 1.0.0
 **/
@Getter
public enum GcSjRequirementEnum {
    OFFICIAL_WEBSITE(1, "官网"),
    CE_PLATFORM_DEV(2, "平台开发"),
    CE_ONLINE_SALES(3, "电商"),
    CE_APP(4, "APP"),
    CE_PHONE_WEBSITE(5, "手机站"),
    CE_MINI_PROGRAMS(6, "小程序"),
    CE_WX_DEV(7, "微信开发"),
    CE_H5(8, "H5"),
    CE_WEBSITE_CLUSTER(9, "站群"),
    ;

    /**
     * 等级
     */
    private final Integer requirementType;

    /**
     * 等级名称
     */
    private final String requirementName;

    GcSjRequirementEnum(Integer requirementType, String requirementName) {
        this.requirementType = requirementType;
        this.requirementName = requirementName;
    }

    /**
     * 需求数据映射
     */
    private final static Map<Integer, GcSjRequirementEnum> REQUIREMENT_ENUM_MAP = new HashMap<>();

    /**
     * 需求数据列表
     */
    private final static List<GcBusinessOpportunityRequirementData> GC_BUSINESS_OPPORTUNITY_REQUIREMENT_DATA_LIST = new ArrayList<>();

    /**
     * 获取高呈商机需求枚举
     * @param requirementType 需求类型
     * <AUTHOR>
     * @date 2024/5/17 下午1:55
     * @return com.ce.scrm.center.dubbo.enums.REQUIREMENT_ENUM_MAP
     **/
    public static GcSjRequirementEnum get(Integer requirementType) {
        if (REQUIREMENT_ENUM_MAP.isEmpty()) {
            for (GcSjRequirementEnum gcSjRequirementEnum : GcSjRequirementEnum.values()) {
                REQUIREMENT_ENUM_MAP.put(gcSjRequirementEnum.getRequirementType(), gcSjRequirementEnum);
            }
        }
        return REQUIREMENT_ENUM_MAP.get(requirementType);
    }

    /**
     * 获取所有高呈商机需求枚举数据
     * <AUTHOR>
     * @date 2024/5/17 下午1:57
     * @return java.util.List<com.ce.scrm.center.dubbo.entity.view.GcBusinessOpportunityRequirementData>
     **/
    public static List<GcBusinessOpportunityRequirementData> list() {
        if (GC_BUSINESS_OPPORTUNITY_REQUIREMENT_DATA_LIST.isEmpty()) {
            for (GcSjRequirementEnum gcSjRequirementEnum : GcSjRequirementEnum.values()) {
                GcBusinessOpportunityRequirementData gcBusinessOpportunityRequirementData = new GcBusinessOpportunityRequirementData();
                gcBusinessOpportunityRequirementData.setRequirementType(gcSjRequirementEnum.getRequirementType());
                gcBusinessOpportunityRequirementData.setRequirementName(gcSjRequirementEnum.getRequirementName());
                GC_BUSINESS_OPPORTUNITY_REQUIREMENT_DATA_LIST.add(gcBusinessOpportunityRequirementData);
            }
        }
        return GC_BUSINESS_OPPORTUNITY_REQUIREMENT_DATA_LIST;
    }
}
