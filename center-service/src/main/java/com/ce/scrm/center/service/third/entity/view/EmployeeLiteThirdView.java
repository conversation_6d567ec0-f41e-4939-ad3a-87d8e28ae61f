package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: 员工Vo
 * @author: JiuDD
 * date: 2024/7/11
 */
@Data
public class EmployeeLiteThirdView implements Serializable {
    private static final long serialVersionUID = -5817726904141693190L;
    /**
     * 员工ID
     */
    private String id;
    /**
     * 员工姓名
     */
    private String name;
    /**
     * 职级
     */
    private String jobGrade;
    /**
     * 工作邮箱
     */
    private String workMail;
    /**
     * 工作电话
     */
    private String officePhone;
    /**
     * ehr的手机号
     */
    private String mobile;
    /**
     * 绑定的手机号
     */
    private String bindMobile;
    /**
     * 职位
     */
    private String position;

    /**
     * 分司名称
     */
    private String subName;
    /**
     * 所属机构
     */
    private String orgId;
    /**
     * 分司id
     */
    private String subId;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 区域id
     */
    private String areaId;




}