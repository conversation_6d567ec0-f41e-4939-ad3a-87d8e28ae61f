package com.ce.scrm.center.service.business.entity.view.segment;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 区域查询分群信息
 * @Author: lijinpeng
 * @Date: 2025/2/27 14:25
 */
@Data
public class ClueSegmentBusinessView implements Serializable {

	/**
	 * 主键id
	 */
	private Integer id;

	/**
	 * 分群id
	 */
	private String segmentId;

	/**
	 * 分群名称
	 */
	private String segmentName;

	/**
	 * 下发总量
	 */
	private Integer segmentCount;

	/**
	 * 分群实际下发总量
	 */
	private Integer segmentDistributeCount;

	/**
	 * 下发状态 0:未下发 1:下发中 2:已下发
	 */
	private Integer segmentStatus;

	/**
	 * 下发状态描述 0:未下发 1:下发中 2:已下发
	 */
	private String segmentStatusName;

	/**
	 * 专项线索名称（分司可见）
	 */
	private String segmentDesc;

	/**
	 * 分群开始时间
	 */
	private Date segmentBeginTime;

	/**
	 * 分群结束时间
	 */
	private Date segmentEndTime;

}
