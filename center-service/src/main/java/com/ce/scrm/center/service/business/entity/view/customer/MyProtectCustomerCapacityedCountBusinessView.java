package com.ce.scrm.center.service.business.entity.view.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 我的保护列表
 * <AUTHOR>
 * @Version 1.0.0
 * @Date 2025-01-03 11:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyProtectCustomerCapacityedCountBusinessView implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 现有库容
	 */
	private Integer nowCapacityedCount;

	/**
	 * 总库容
	 * */
	private Integer totalCapacityedCount;
}
