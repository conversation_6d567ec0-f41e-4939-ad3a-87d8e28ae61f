package com.ce.scrm.center.service.enums;

import lombok.Getter;

@Getter
public enum ReleaseBussinessEnum {

    SD("sd", "手动"),
    CS("cs", "超时"),
    SJ("sj","商机"),
    BJ("bj", "报价"),
    ZJS("zjs", "转介绍");


    private final String value;

    private final String label;

    private ReleaseBussinessEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
