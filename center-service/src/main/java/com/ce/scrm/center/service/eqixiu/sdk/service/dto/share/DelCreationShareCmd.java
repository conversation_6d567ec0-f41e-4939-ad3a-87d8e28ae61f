/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.share;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.domain.CreationShare;

import java.util.List;
import java.util.Map;

/**
 * 删除分享
 *
 * <AUTHOR>
 */
public class DelCreationShareCmd extends BaseParam {

    private List<CreationShare> shareList;

    public DelCreationShareCmd(String openId) {
        setOpenId(openId);
    }

    @Override
    public Map<String, String> getParamsMap() {
        return getBaseParamsMap();
    }

    @Override
    public void validate() {
        validateOpenId();
        if (shareList == null || shareList.isEmpty()) {
            throw new KnownException("shareList 不能为空");
        }
        for (CreationShare share : shareList) {
            if (share.getCreationId() == null) {
                throw new KnownException("creationId 不能为空");
            }
            if (share.getRelId() == null) {
                throw new KnownException("relId 不能为空");
            }
            if (share.getRelType() == null) {
                throw new KnownException("relType 不能为空");
            }
        }
    }

    public List<CreationShare> getShareList() {
        return shareList;
    }

    public DelCreationShareCmd setShareList(List<CreationShare> shareList) {
        this.shareList = shareList;
        return this;
    }

    public DelCreationShareCmd addShare(CreationShare share) {
        if (shareList == null) {
            shareList = java.util.Collections.singletonList(share);
        } else {
            shareList.add(share);
        }
        return this;
    }
}
