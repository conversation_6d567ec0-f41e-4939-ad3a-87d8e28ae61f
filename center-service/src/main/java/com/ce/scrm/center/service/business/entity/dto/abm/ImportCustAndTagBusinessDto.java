package com.ce.scrm.center.service.business.entity.dto.abm;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description 导入客户和标签信息
 * <AUTHOR>
 * @Date 2025-07-14 09:40
 */
@Data
public class ImportCustAndTagBusinessDto {
	/**
	 * 客户名称
	 */
	private String customerName;

	/**
	 * 标签列表
	 */
	private List<CustTagInfoBusinessDto> custTagInfoBusinessDtoList;

	@Data
	public static class CustTagInfoBusinessDto {
		/**
		 * 标签名
		 */
		private String tagName;

		/**
		 * tag积分
		 */
		private BigDecimal weight;
	}
}
