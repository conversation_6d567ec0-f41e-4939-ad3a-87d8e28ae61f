package com.ce.scrm.center.service.eqixiu.sdk.service;


import com.ce.scrm.center.service.eqixiu.sdk.common.Result;
import com.ce.scrm.center.service.eqixiu.sdk.domain.CreationDistribute;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.disbribute.DelDistributeCmd;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.disbribute.DistributeQuery;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.disbribute.ModifyDistributeCmd;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.disbribute.SaveDistributeCmd;
import com.ce.scrm.center.service.eqixiu.sdk.support.HttpClient;
import com.ce.scrm.center.service.eqixiu.sdk.support.TokenCache;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;


/**
 * 作品多人分发服务
 * 多次保存时，若成员与已存在的成员重复，则添加时跳过重复的人。
 * 若渠道名称与当前列表重复，则添加时跳过重复的渠道。
 * https://hc.eqxiu.cn/doc/639/
 *
 * <AUTHOR>
 */
public class CreationDistributeService extends ConnectService {

    public static final String API_DISTRIBUTE_SAVE = "/api/v1/editor/creation/distribute/save/";
    public static final String API_DISTRIBUTE_LIST = "/api/v1/editor/creation/distribute/list";
    public static final String API_DISTRIBUTE_DELETE = "/api/v1/editor/creation/distribute/delete";
    public static final String API_DISTRIBUTE_UPDATE = "/api/v1/editor/creation/distribute/update";

    CreationDistributeService(Secret secret) {
        super(secret);
    }

    public CreationDistributeService(Secret secret, TokenCache tokenCache) {
        super(secret, tokenCache);
    }

    public CreationDistributeService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        super(secret, tokenCache, httpClient);
    }

    /**
     * 创建作品分发
     *
     * @param cmd
     * @return
     */
    public boolean saveDistribute(SaveDistributeCmd cmd) {
        paramValidate(cmd);
        JSONObject object = httpClient.httpPostObj(getApiURL(API_DISTRIBUTE_SAVE + cmd.getCreationId(), cmd.getParamsMap()), cmd.getDistributes());
        printLog(object, "添加作品分发失败:{}");
        return object.getSuccess();
    }

    /**
     * 查询作品分发数据列表
     *
     * @param query
     * @return
     */
    public Result<CreationDistribute> findCreationDistributeList(DistributeQuery query) {
        paramValidate(query);
        JSONObject object = httpClient.httpPostObj(getApiURL(API_DISTRIBUTE_LIST, query.getParamsMap()), query);
        printLog(object, "查询作品分发数据列表失败:{}");
        return getResult(object, CreationDistribute.class);
    }


    /**
     * 修改作品分发渠道名称
     *
     * @param cmd
     * @return
     */
    public boolean modifyDistributeName(ModifyDistributeCmd cmd) {
        paramValidate(cmd);
        JSONObject object = httpClient.httpPostObj(getApiURL(API_DISTRIBUTE_UPDATE, cmd.getParamsMap()), cmd);
        printLog(object, "修改作品分发渠道名称失败:{}");
        return object.getSuccess();
    }

    /**
     * 删除作品分发
     *
     * @param cmd
     * @return
     */
    public boolean deleteDistribute(DelDistributeCmd cmd) {
        paramValidate(cmd);
        JSONObject object = httpClient.httpPostObj(getApiURL(API_DISTRIBUTE_DELETE, cmd.getParamsMap()), cmd.getIds());
        printLog(object, "删除作品分发失败:{}");
        return object.getSuccess();
    }

}
