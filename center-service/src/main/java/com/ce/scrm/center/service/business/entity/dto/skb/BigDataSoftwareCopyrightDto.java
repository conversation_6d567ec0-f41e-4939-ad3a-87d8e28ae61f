package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description:软件著作权
 * @author: JiuDD
 * date: 2025/2/13
 */
@Data
public class BigDataSoftwareCopyrightDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**企业名称*/
    @JSONField(serialize = false)
	private String entname;
    /**统一信用代码*/
    @JSONField(serialize = false)
	private String uncid;
    /**总数量*/
    @JSONField(serialize = false)
	private int total_count;
    /**总页数*/
    @JSONField(serialize = false)
    private int total_page;
    /**当前页*/
    @JSONField(serialize = false)
	private int page;

    @JSONField(name = "软件著作权列表")
    private List<ListSoftwareCopyright> list;

    @NoArgsConstructor
    @Data
    public static class ListSoftwareCopyright implements Serializable {
        private static final long serialVersionUID = 1L;
        /**登记号*/
        @JSONField(serialize = false)
		private String regist_no;
        /**版本号*/
        @JSONField(name = "版本号",serialize = false)
		private String version;
        /**发布日期*/
        @JSONField(name = "发布日期",serialize = false)
		private String release_date;
        /**软件名*/
        @JSONField(name = "软件著作权软件名")
		private String software_name;
        /**简称*/
        @JSONField(name = "软件著作权简称")
		private String short_name;
        /**登记批准日期*/
        @JSONField(serialize = false)
		private String regist_date;
    }
}
