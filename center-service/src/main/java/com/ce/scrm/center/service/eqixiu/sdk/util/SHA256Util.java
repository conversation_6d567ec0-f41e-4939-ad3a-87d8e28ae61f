package com.ce.scrm.center.service.eqixiu.sdk.util;


import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.StringJoiner;

/**
 * SHA256加密工具类，用于开放 API 请求时参数签名
 */

public class SHA256Util {

    public static String getSHA256Str(List<String> paramList) {
        Collections.sort(paramList);
        String signatureStr = arrayToDelimitedString(paramList.toArray(), "");
        return getSHA256Str(signatureStr);
    }

    public static String getSHA256Str(String... params) {
        Arrays.sort(params);
        String signatureStr = arrayToDelimitedString(params, "");
        return getSHA256Str(signatureStr);
    }

    private static String getSHA256Str(String str) {
        MessageDigest messageDigest;
        String encodedStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            byte[] hash = messageDigest.digest(str.getBytes(StandardCharsets.UTF_8));
            encodedStr = bytesToHexString(hash);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return encodedStr;
    }

    /**
     * Convert byte[] to hex string
     *
     * @param src byte[] data
     * @return hex string
     */
    private static String bytesToHexString(byte[] src) {
        if (src == null || src.length <= 0) {
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    private static String arrayToDelimitedString(Object[] arr, String delim) {
        if (arr == null || arr.length == 0) {
            return "";
        }
        if (arr.length == 1) {
            return String.valueOf(arr[0]);
        }

        StringJoiner sj = new StringJoiner(delim);
        for (Object elem : arr) {
            sj.add(String.valueOf(elem));
        }
        return sj.toString();
    }
}
