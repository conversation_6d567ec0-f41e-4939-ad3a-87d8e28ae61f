package com.ce.scrm.center.service.business.entity.view;

import com.ce.scrm.center.service.third.entity.view.OrgEmpOfSubCompanyDataThirdView;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

/**
 * description: 分司的部门及员工数据
 * @author: DD.Jiu
 * date: 2024/7/22.
 */
@Data
@Accessors(chain = true)
public class DeptAndSalersCacheListView implements Serializable {

    private Map<String, OrgEmpOfSubCompanyDataThirdView> deptAndSalers;
}
