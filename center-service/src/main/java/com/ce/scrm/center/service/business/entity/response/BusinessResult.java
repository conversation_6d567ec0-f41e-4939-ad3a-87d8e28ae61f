package com.ce.scrm.center.service.business.entity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 返回包装体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/05/28 下午3:38
 */
@Data
public class BusinessResult<T> implements Serializable {
    private static final long serialVersionUID = -2156057946333564975L;
    private String traceId;
    private String msg;
    private String code;
    private T data;

    private BusinessResult() {
    }

    private BusinessResult(T data) {
        this.code = BusinessCodeMessageEnum.REQUEST_SUCCESS.getCode();
        this.msg = BusinessCodeMessageEnum.REQUEST_SUCCESS.getMsg();
        this.data = data;
    }

    private BusinessResult(T data, String msg) {
        this.code = BusinessCodeMessageEnum.REQUEST_SUCCESS.getCode();
        this.msg = msg;
        this.data = data;
    }

    private BusinessResult(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private BusinessResult(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    private BusinessResult(BusinessCodeMessageEnum cm) {
        if (cm != null) {
            this.code = cm.getCode();
            this.msg = cm.getMsg();
        }
    }

    public static <T> BusinessResult<T> success(T data) {
        return new BusinessResult<>(data);
    }

    public static <T> BusinessResult<T> success(T data, String msg) {
        return new BusinessResult<>(data, msg);
    }

    public static <T> BusinessResult<T> success() {
        return success(null);
    }

    public static <T> BusinessResult<T> error(BusinessCodeMessageEnum cm) {
        return new BusinessResult<>(cm);
    }

    public static <T> BusinessResult<T> error(BusinessCodeMessageEnum cm, String msg) {
        return new BusinessResult<>(cm.getCode(), msg);
    }

    public static <T> BusinessResult<T> error(String code, String msg) {
        return new BusinessResult<>(code, msg);
    }

    public static <T> BusinessResult<T> error(BusinessResult<?> resultEntity) {
        return new BusinessResult<>(resultEntity.getCode(), resultEntity.getMsg());
    }

    public Boolean checkSuccess() {
        return BusinessCodeMessageEnum.REQUEST_SUCCESS.getCode().equals(this.code);
    }
}
