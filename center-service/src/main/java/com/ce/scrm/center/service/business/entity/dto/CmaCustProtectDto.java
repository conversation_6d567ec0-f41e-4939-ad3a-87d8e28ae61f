package com.ce.scrm.center.service.business.entity.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2024/12/5 16:10
 */
@Data
@Builder
public class CmaCustProtectDto implements Serializable {

    /** 客户id */
    private String custId;

    /** 客户所在位置阶段 */
    private String stage;

    /** 客户/企业名称 */
    private String custName;

    /** 商务代表ID */
    private String salerId;

    /** 商务代表名称 */
    private String salerName;

    /** 分公司ID */
    private String subcompanyId;

    /** 分公司名称 */
    private String subcompanyName;

    private String buId;

    private String buName;

    /** 部门ID */
    private String bussdeptId;

    /** 部门名称 */
    private String bussdeptName;

}
