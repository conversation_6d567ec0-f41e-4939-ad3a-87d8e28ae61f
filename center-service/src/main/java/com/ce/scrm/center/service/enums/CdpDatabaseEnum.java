package com.ce.scrm.center.service.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/25 19:18
 */
public enum CdpDatabaseEnum {
    /**
     * 测试项目
     */
    test("3","horizon_default_3","测试项目"),
    /**
     * 正式项目
     */
    prod("2","horizon_production_2","正式项目");

    /**
     * 属性名
     */
    private String projectId;
    /**
     * 描述
     */
    private String database;

    /**
     * 描述
     */
    private String description;

    CdpDatabaseEnum(String projectId, String database, String description) {
        this.projectId = projectId;
        this.database = database;
        this.description = description;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getDatabase() {
        return database;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public static CdpDatabaseEnum getEnumByProjectId(String projectId){
        if (StringUtils.isBlank(projectId)){
            return null;
        }
        for (CdpDatabaseEnum databaseEnum : CdpDatabaseEnum.values()) {
            if (projectId.equals(databaseEnum.getProjectId())){
                return databaseEnum;
            }
        }
        return null;
    }
}
