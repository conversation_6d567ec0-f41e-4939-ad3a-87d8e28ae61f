package com.ce.scrm.center.service.business.entity.dto.abm;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:申请调度参数
 */

@Data
public class BizOppDistributeInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    private String customerId;

    private String customerName;

    /**
     * 审核记录id
     */
    private Long reviewId;

    /**
     * 审核来源类型 1：SDR 2：CC 必填非空
     */
    private Integer reviewSrcType;

    /**
     * 指定的保护关系状态
     */
    private Integer designatedProtectStatus;

    /**
     * 指定的人员
     */
    private String designatedProtectEmpId;

    /**
     * 指定的人员机构
     */
    private String designatedProtectOrg;


    /**
     * 当前保护关系表Id
     */
    private String cmCustProtectId;

    /**
     *  企微通知类型： 0：不通知 1：通知商务 2：通知总监/经理
     */
    private Integer wxNoticeType;
}