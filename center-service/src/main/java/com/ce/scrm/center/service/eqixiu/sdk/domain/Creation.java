package com.ce.scrm.center.service.eqixiu.sdk.domain;

import java.util.Date;

/**
 * 易企秀作品
 *
 * <AUTHOR>
 */
public class Creation {
    protected Long id;
    /**
     * checkStatus
     * 作品分类
     */
    protected String type;
    protected String code;
    protected String title;
    protected String cover;
    protected String description;

    /**
     * 作者ID = StaffId
     */
    protected String createUser;
    /**
     * 作者所属企业ID
     */
    protected String corpId;
    protected Date createTime;
    protected Date updateTime;
    protected Date publishTime;
    protected Date startTime;
    protected Date endTime;
    /**
     * 来源作品或模板ID,
     * 作品A 基于 模板 T生成，
     * A.sourceId = T.id
     * A.originTemplateId = T.id
     * 作品 B 基于 作品A复制生成
     * B.sourceId = A.id
     * B.originTemplateId = T.id
     */
    protected Long sourceId;
    protected Long templateId;
    protected Long originTemplateId;
    protected Long pv;
    protected Long uv;
    protected Integer activityStatus;
    /**
     * 作品发布状态
     */
    protected Integer status;
    private String createUserName;
    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 审批状态
     */
    private Integer approveStatus;
    /**
     * 审批的消息
     */
    private String labelDetail;
    /**
     * 作品语言
     */
    private String lang;
    /**
     * 当前是哪个版本，对应creation_snapshot表的id
     */
    private Long versionId;

    /**
     * 分享url
     */
    private String shareUrl;

    /**
     * 编辑url
     */
    private String editUrl;

    private String previewUrl;

    private String managerPreviewUrl;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Long getOriginTemplateId() {
        return originTemplateId;
    }

    public void setOriginTemplateId(Long originTemplateId) {
        this.originTemplateId = originTemplateId;
    }

    public Long getPv() {
        return pv;
    }

    public void setPv(Long pv) {
        this.pv = pv;
    }

    public Long getUv() {
        return uv;
    }

    public void setUv(Long uv) {
        this.uv = uv;
    }

    public Integer getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(Integer activityStatus) {
        this.activityStatus = activityStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getLabelDetail() {
        return labelDetail;
    }

    public void setLabelDetail(String labelDetail) {
        this.labelDetail = labelDetail;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    public String getEditUrl() {
        return editUrl;
    }

    public void setEditUrl(String editUrl) {
        this.editUrl = editUrl;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    public String getManagerPreviewUrl() {
        return managerPreviewUrl;
    }

    public void setManagerPreviewUrl(String managerPreviewUrl) {
        this.managerPreviewUrl = managerPreviewUrl;
    }

    @Override
    public String toString() {
        return "Creation{" +
                "id=" + id +
                ", type='" + type + '\'' +
                ", code='" + code + '\'' +
                ", title='" + title + '\'' +
                ", cover='" + cover + '\'' +
                ", description='" + description + '\'' +
                ", createUser='" + createUser + '\'' +
                ", createUserName='" + createUserName + '\'' +
                ", corpId='" + corpId + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", publishTime=" + publishTime +
                '}';
    }
}
