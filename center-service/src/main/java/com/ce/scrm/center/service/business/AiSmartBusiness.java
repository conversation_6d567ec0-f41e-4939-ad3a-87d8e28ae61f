package com.ce.scrm.center.service.business;

import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import cn.ce.cesupport.emp.service.OrgAppService;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.*;
import com.ce.scrm.center.dao.mapper.AiPromptInfoMapper;
import com.ce.scrm.center.dao.service.AiChatInfoService;
import com.ce.scrm.center.dao.service.AiPromptInfoService;
import com.ce.scrm.center.dao.service.AiStarInfoService;
import com.ce.scrm.center.dao.service.IAiPromptAuthInfoService;
import com.ce.scrm.center.service.business.entity.ai.*;
import com.ce.scrm.center.service.business.entity.view.ai.AiChatLogBusinessView;
import com.ce.scrm.center.service.business.entity.view.ai.AiPromptInfoBusinessView;
import com.ce.scrm.center.service.business.entity.view.ai.AiStarLogBusinessView;
import com.ce.scrm.center.service.cache.AiPromptCacheHandler;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description: ai业务层
 * @Author: lijinpeng
 * @Date: 2025/2/20 09:37
 */
@Slf4j
@Service
public class AiSmartBusiness {

    @Resource
    private AiPromptInfoService aiPromptInfoService;

    @Resource
    private AiPromptCacheHandler aiPromptCacheHandler;

    @Resource
    private AiStarInfoService aiStarInfoService;

    @Autowired
    private OrgThirdService orgThirdService;
    @Resource
    private AiChatInfoService aiChatInfoService;

    @Autowired
    private IAiPromptAuthInfoService aiPromptAuthInfoService;

    @Autowired
    private AiPromptInfoMapper aiPromptInfoMapper;

    @DubboReference
    private OrgAppService orgAppService;


    @Transactional(rollbackFor = Exception.class)
    public Page<AiPromptInfoBusinessView> getAiPromptInfoPage(QueryAiPromptInfoBusinessDto queryAiPromptInfoBusinessDto, boolean master) {
        log.info("getAiPromptInfoPage queryAiPromptInfoBusinessDto={}", JSON.toJSONString(queryAiPromptInfoBusinessDto));
        Page<AiPromptInfoPlus> aiPromptInfoPage = aiPromptInfoMapper.getAiPromptInfoList(Page.of(queryAiPromptInfoBusinessDto.getPageNum(), queryAiPromptInfoBusinessDto.getPageSize()),
                queryAiPromptInfoBusinessDto.getStartFlag(),
                queryAiPromptInfoBusinessDto.getSubId(),
                queryAiPromptInfoBusinessDto.getAreaId(),
                queryAiPromptInfoBusinessDto.getDeptId(),
                queryAiPromptInfoBusinessDto.getBuId(),
                master,
                queryAiPromptInfoBusinessDto.getCompany(),
                queryAiPromptInfoBusinessDto.getPromptTypeList()
        );
        Page<AiPromptInfoBusinessView> result = BeanUtil.copyProperties(aiPromptInfoPage, Page.class);
        List<AiPromptInfoBusinessView> aiPromptInfoBusinessViews = BeanUtil.copyToList(aiPromptInfoPage.getRecords(), AiPromptInfoBusinessView.class);
        aiPromptInfoBusinessViews = aiPromptInfoBusinessViews.stream().peek(aiPromptInfoBusinessView -> {
            List<AiPromptAuthInfo> authList = aiPromptAuthInfoService.lambdaQuery().eq(AiPromptAuthInfo::getAiPromptInfoId, aiPromptInfoBusinessView.getId()).list();
            if (!CollectionUtils.isEmpty(authList)) {
                List<String> deptIds = new ArrayList<>();
                List<String> buIds = new ArrayList<>();
                List<String> subIds = new ArrayList<>();
                List<String> areaIds = new ArrayList<>();
                for (AiPromptAuthInfo auth : authList) {
                    boolean hasDept = StringUtils.isNotBlank(auth.getDeptId());
                    boolean hasBu = StringUtils.isNotBlank(auth.getBuId());
                    boolean hasSub = StringUtils.isNotBlank(auth.getSubId());
                    boolean hasArea = StringUtils.isNotBlank(auth.getAreaId());
                    if (hasDept) {
                        deptIds.add(auth.getDeptId());
                    } else if (hasBu) {
                        buIds.add(auth.getBuId());
                    } else if (hasSub) {
                        subIds.add(auth.getSubId());
                    } else if (hasArea) {
                        areaIds.add(auth.getAreaId());
                    }
                }
                aiPromptInfoBusinessView.setDeptIdsList(deptIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                aiPromptInfoBusinessView.setBuIdsList(buIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                aiPromptInfoBusinessView.setSubIdsList(subIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                aiPromptInfoBusinessView.setAreaIdsList(areaIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
        }).sorted((t, e) -> e.getStartFlag() - t.getStartFlag()).collect(Collectors.toList());
        result.setRecords(aiPromptInfoBusinessViews);
        return result;
    }

    private String getOrgName(Optional<OrgThirdDto> orgOptional) {
        return orgOptional.map(OrgThirdDto::getName).orElse("");
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean addAiPromptInfo(AddAiPromptInfoBusinessDto addAiPromptInfoBusinessDto) {
        log.info("addAiPromptInfo addAiPromptInfoBusinessDto={}", addAiPromptInfoBusinessDto);
        Date currentData = new Date();
        AiPromptInfo aiPromptInfo = AiPromptInfo.builder()
                .title(addAiPromptInfoBusinessDto.getTitle())
                .content(addAiPromptInfoBusinessDto.getContent())
                .createUserId(addAiPromptInfoBusinessDto.getLoginEmployeeId())
                .company(StringUtils.isNotBlank(addAiPromptInfoBusinessDto.getCompany()) ? addAiPromptInfoBusinessDto.getCompany() : null)
                .deleteFlag(YesOrNoEnum.NO.getCode())
                .startFlag(YesOrNoEnum.NO.getCode())
                .promptType(addAiPromptInfoBusinessDto.getPromptType())
                .createTime(currentData)
                .updateTime(currentData)
                .remark(addAiPromptInfoBusinessDto.getRemark())
                .operator(addAiPromptInfoBusinessDto.getLoginEmployeeId())
                .build();
        boolean result = aiPromptInfoService.save(aiPromptInfo);
        // 通过id 查询
        if (!CollectionUtils.isEmpty(addAiPromptInfoBusinessDto.getAiPromptAuthInfos())) {
            List<AiPromptAuthInfo> aiPromptAuthInfos = addAiPromptInfoBusinessDto.getAiPromptAuthInfos().stream()
                    .map(aiPromptAuthInfo ->
                            AiPromptAuthInfo.builder()
                                    .aiPromptInfoId(aiPromptInfo.getId())
                                    .deptId(StringUtils.isNotBlank(aiPromptAuthInfo.getDeptId()) ? aiPromptAuthInfo.getDeptId() : null)
                                    .subId(StringUtils.isNotBlank(aiPromptAuthInfo.getSubId()) ? aiPromptAuthInfo.getSubId() : null)
                                    .areaId(StringUtils.isNotBlank(aiPromptAuthInfo.getAreaId()) ? aiPromptAuthInfo.getAreaId() : null)
                                    .buId(StringUtils.isNotBlank(aiPromptAuthInfo.getBuId()) ? aiPromptAuthInfo.getBuId() : null)
                                    .build()).collect(Collectors.toList());
            aiPromptAuthInfoService.saveBatch(aiPromptAuthInfos);
        }
        log.info("addAiPromptInfo result={}", result);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAiPromptInfoById(UpdateAiPromptInfoBusinessDto updateAiPromptInfoBusinessDto) {
        log.info("updateAiPromptInfoById,updateAiPromptInfoBusinessDto={}", updateAiPromptInfoBusinessDto);
        boolean result = aiPromptInfoService.lambdaUpdate()
                .set(AiPromptInfo::getTitle, StringUtils.isNotBlank(updateAiPromptInfoBusinessDto.getTitle()) ? updateAiPromptInfoBusinessDto.getTitle() : null)
                .set(AiPromptInfo::getContent, StringUtils.isNotBlank(updateAiPromptInfoBusinessDto.getContent()) ? updateAiPromptInfoBusinessDto.getContent() : null)
                .set(AiPromptInfo::getUpdateTime, new Date())
                .set(AiPromptInfo::getRemark, updateAiPromptInfoBusinessDto.getRemark())
                .set(AiPromptInfo::getPromptType, updateAiPromptInfoBusinessDto.getPromptType())
                .set(AiPromptInfo::getOperator, updateAiPromptInfoBusinessDto.getLoginEmployeeId())
                .set(AiPromptInfo::getStartFlag, null != updateAiPromptInfoBusinessDto.getStartFlag() ? updateAiPromptInfoBusinessDto.getStartFlag() : 1)
                .eq(AiPromptInfo::getId, updateAiPromptInfoBusinessDto.getId())
                .update();
        aiPromptAuthInfoService.lambdaUpdate().eq(AiPromptAuthInfo::getAiPromptInfoId, updateAiPromptInfoBusinessDto.getId()).remove();
        // 通过id 查询
        if (!CollectionUtils.isEmpty(updateAiPromptInfoBusinessDto.getAiPromptAuthInfos())) {
            List<AiPromptAuthInfo> aiPromptAuthInfos = updateAiPromptInfoBusinessDto.getAiPromptAuthInfos().stream()
                    .map(aiPromptAuthInfo ->
                            AiPromptAuthInfo.builder()
                                    .aiPromptInfoId(updateAiPromptInfoBusinessDto.getId())
                                    .deptId(StringUtils.isNotBlank(aiPromptAuthInfo.getDeptId()) ? aiPromptAuthInfo.getDeptId() : null)
                                    .subId(StringUtils.isNotBlank(aiPromptAuthInfo.getSubId()) ? aiPromptAuthInfo.getSubId() : null)
                                    .areaId(StringUtils.isNotBlank(aiPromptAuthInfo.getAreaId()) ? aiPromptAuthInfo.getAreaId() : null)
                                    .buId(StringUtils.isNotBlank(aiPromptAuthInfo.getBuId()) ? aiPromptAuthInfo.getBuId() : null)
                                    .build()).collect(Collectors.toList());
            aiPromptAuthInfoService.saveBatch(aiPromptAuthInfos);
        }
        aiPromptCacheHandler.del(String.valueOf(updateAiPromptInfoBusinessDto.getId()));
        log.info("updateAiPromptInfoById,result={}", result);
        return result;
    }


    public Boolean updateAiPromptStatus(UpdateAiPromptInfoBusinessDto updateAiPromptInfoBusinessDto) {
        log.info("updateAiPromptStatus,updateAiPromptInfoBusinessDto={}", updateAiPromptInfoBusinessDto);
        boolean result = aiPromptInfoService.lambdaUpdate()
                .set(null != updateAiPromptInfoBusinessDto.getStartFlag(), AiPromptInfo::getStartFlag, updateAiPromptInfoBusinessDto.getStartFlag())
                .set(null != updateAiPromptInfoBusinessDto.getDeleteFlag(), AiPromptInfo::getDeleteFlag, updateAiPromptInfoBusinessDto.getDeleteFlag())
                .eq(AiPromptInfo::getId, updateAiPromptInfoBusinessDto.getId()).update();
        aiPromptCacheHandler.del(String.valueOf(updateAiPromptInfoBusinessDto.getId()));
        log.info("updateAiPromptStatus,result={}", result);
        return result;
    }


    public Boolean saveAiStarLog(AiStarLogBusinessDto aiStarLogBusinessDto) {
        if (aiStarLogBusinessDto == null) {
            return false;
        }
        AiStarLog aiStarLog = BeanUtil.copyProperties(aiStarLogBusinessDto, AiStarLog.class);
        aiStarLog.setOperator(aiStarLogBusinessDto.getLoginEmployeeId());
        aiStarLog.setCreateTime(new Date());
        AiPromptInfo aiPromptInfo = aiPromptInfoService.lambdaQuery()
                .eq(AiPromptInfo::getId, Long.parseLong(aiStarLogBusinessDto.getPromptId()))
                .eq(AiPromptInfo::getStartFlag, YesOrNoEnum.YES.getCode())
                .eq(AiPromptInfo::getDeleteFlag, YesOrNoEnum.NO.getCode())
                .last("limit 1")
                .one();
        aiStarLog.setAiBasePrompt(aiPromptInfo.getContent());
        return aiStarInfoService.save(aiStarLog);
    }


    public Page<AiStarLogBusinessView> analysisStarLogList(AiStarLogBusinessDto aiStarInfoBusinessDto) {
        log.info("getAiPromptInfoPage analysisStarLogList={}", JSON.toJSONString(aiStarInfoBusinessDto));
        Page<AiStarLog> page = Page.of(aiStarInfoBusinessDto.getPageNum(), aiStarInfoBusinessDto.getPageSize());
        Page<AiStarLog> pageResult = aiStarInfoService.lambdaQuery()
                .eq(Objects.nonNull(aiStarInfoBusinessDto.getOperatorType()), AiStarLog::getOperatorType, aiStarInfoBusinessDto.getOperatorType())
                .eq(StringUtils.isNotBlank(aiStarInfoBusinessDto.getPid()), AiStarLog::getPid, aiStarInfoBusinessDto.getPid())
                .eq(StringUtils.isNotBlank(aiStarInfoBusinessDto.getCustId()), AiStarLog::getCustId, aiStarInfoBusinessDto.getCustId())
                .orderByDesc(AiStarLog::getId)
                .page(page);
        Page<AiStarLogBusinessView> result = BeanUtil.copyProperties(pageResult, Page.class);
        List<AiStarLogBusinessView> aiStarLogBusinessViews = BeanUtil.copyToList(pageResult.getRecords(), AiStarLogBusinessView.class);
        result.setRecords(aiStarLogBusinessViews);
        return result;
    }

    public Page<AiChatLogBusinessView> getChatLogList(AiChatLogBusinessDto aiChatLogBusinessDto) {
        log.info("getChatLogList aiChatLogBusinessDto={}", JSON.toJSONString(aiChatLogBusinessDto));
        Page<AiChatLog> page = Page.of(aiChatLogBusinessDto.getPageNum(), aiChatLogBusinessDto.getPageSize());
        Page<AiChatLog> pageResult = aiChatInfoService.lambdaQuery()
                .eq(StringUtils.isNotBlank(aiChatLogBusinessDto.getPid()), AiChatLog::getPid, aiChatLogBusinessDto.getPid())
                .eq(StringUtils.isNotBlank(aiChatLogBusinessDto.getCustId()), AiChatLog::getCustId, aiChatLogBusinessDto.getCustId())
                .orderByDesc(AiChatLog::getId)
                .page(page);
        Page<AiChatLogBusinessView> result = BeanUtil.copyProperties(pageResult, Page.class);
        List<AiChatLogBusinessView> aiChatRecordBusinessViews = BeanUtil.copyToList(pageResult.getRecords(), AiChatLogBusinessView.class);
        result.setRecords(aiChatRecordBusinessViews);
        return result;
    }

    public List<AiPromptInfoBusinessView> getAnalysisPromptIdAndNameList(QueryAiPromptInfoBusinessDto queryAiPromptInfoBusinessDto, String position, List<Long> promptList, Set<Long> excludeIds) {
        log.info("getAiPromptInfoPage getAnalysisPromptIdAndNameList={}", JSON.toJSONString(queryAiPromptInfoBusinessDto));
        // TODO 特殊判断 胡明,高美晶
        if (Objects.equals("86602", queryAiPromptInfoBusinessDto.getEmployeeId()) || Objects.equals("87116", queryAiPromptInfoBusinessDto.getEmployeeId())) {
            Page<AiPromptInfo> aiPromptInfoPage = aiPromptInfoService.lambdaQuery().select(AiPromptInfo::getId, AiPromptInfo::getTitle)
                    .eq(AiPromptInfo::getDeleteFlag, YesOrNoEnum.NO.getCode())
                    .eq(AiPromptInfo::getPromptType, null != queryAiPromptInfoBusinessDto.getPromptType() ? queryAiPromptInfoBusinessDto.getPromptType() : 1)
                    .eq(null != queryAiPromptInfoBusinessDto.getStartFlag(), AiPromptInfo::getStartFlag, queryAiPromptInfoBusinessDto.getStartFlag())
                    .orderByDesc(AiPromptInfo::getCreateTime)
                    .page(Page.of(1, 100));
            return BeanUtil.copyToList(aiPromptInfoPage.getRecords(), AiPromptInfoBusinessView.class);
        }
        //限制下返回条数
        List<AiPromptAuthInfo> aiPromptInfos = new ArrayList<>();
        if (StringUtils.isNotBlank(queryAiPromptInfoBusinessDto.getDeptId())) {
            List<AiPromptAuthInfo> aiPromptAuthInfoPage = aiPromptAuthInfoService.lambdaQuery().select(AiPromptAuthInfo::getAiPromptInfoId)
                    .eq(AiPromptAuthInfo::getDeptId, queryAiPromptInfoBusinessDto.getDeptId()).groupBy(AiPromptAuthInfo::getAiPromptInfoId).list();
            aiPromptInfos.addAll(aiPromptAuthInfoPage);
        }
        if (EmpPositionConstant.BUSINESS_AREA.equals(position) && StringUtils.isNotBlank(queryAiPromptInfoBusinessDto.getAreaId())) {
            List<AiPromptAuthInfo> aiPromptInfoPage2 = aiPromptAuthInfoService.lambdaQuery().select(AiPromptAuthInfo::getAiPromptInfoId)
                    .eq(AiPromptAuthInfo::getAreaId, queryAiPromptInfoBusinessDto.getAreaId()).groupBy(AiPromptAuthInfo::getAiPromptInfoId)
                    .list();
            aiPromptInfos.addAll(aiPromptInfoPage2);
        }
        if (EmpPositionConstant.BUSINESS_MAJOR.equals(position) && StringUtils.isNotBlank(queryAiPromptInfoBusinessDto.getSubId())) {
            List<AiPromptAuthInfo> aiPromptInfoPage2 = aiPromptAuthInfoService.lambdaQuery().select(AiPromptAuthInfo::getAiPromptInfoId)
                    .eq(AiPromptAuthInfo::getSubId, queryAiPromptInfoBusinessDto.getSubId()).groupBy(AiPromptAuthInfo::getAiPromptInfoId)
                    .list();
            aiPromptInfos.addAll(aiPromptInfoPage2);
        }
        if (EmpPositionConstant.BUSINESS_MAJOR_BU.equals(position) && StringUtils.isNotBlank(queryAiPromptInfoBusinessDto.getBuId())) {
            List<AiPromptAuthInfo> aiPromptInfoPage2 = aiPromptAuthInfoService.lambdaQuery().select(AiPromptAuthInfo::getAiPromptInfoId)
                    .eq(AiPromptAuthInfo::getBuId, queryAiPromptInfoBusinessDto.getBuId()).groupBy(AiPromptAuthInfo::getAiPromptInfoId)
                    .list();
            aiPromptInfos.addAll(aiPromptInfoPage2);
        }
        aiPromptInfos = aiPromptInfos.stream().distinct().collect(Collectors.toList());
        /////*****************/
        // 过滤出 有权限 且符合过滤条件的 ID
        List<Long> promptIdsHas = aiPromptInfos.stream()
                .map(AiPromptAuthInfo::getAiPromptInfoId)
                .filter(promptList::contains)
                .collect(Collectors.toList());
        // 移除掉所有的 特殊提示词 ID
        List<Long> promptIds = aiPromptInfos.stream()
                .map(AiPromptAuthInfo::getAiPromptInfoId)
                .filter(id -> !excludeIds.contains(id))
                .collect(Collectors.toList());
        // promptList
        if (!CollectionUtils.isEmpty(promptIdsHas)) {
            promptIds.addAll(promptIdsHas);
        }
        /////*****************/
        if (CollectionUtils.isEmpty(promptIds)) {
            return new ArrayList<>();
        } else {
            Page<AiPromptInfo> aiPromptInfoPage = aiPromptInfoService.lambdaQuery().select(AiPromptInfo::getId, AiPromptInfo::getTitle, AiPromptInfo::getCreateTime)
                    .eq(AiPromptInfo::getDeleteFlag, YesOrNoEnum.NO.getCode())
                    .eq(null != queryAiPromptInfoBusinessDto.getStartFlag(), AiPromptInfo::getStartFlag, queryAiPromptInfoBusinessDto.getStartFlag())
                    .eq(AiPromptInfo::getPromptType, null != queryAiPromptInfoBusinessDto.getPromptType() ? queryAiPromptInfoBusinessDto.getPromptType() : 1)
                    .in(AiPromptInfo::getId, promptIds.stream().distinct().collect(Collectors.toList()))
                    .orderByDesc(AiPromptInfo::getCreateTime)
                    .page(Page.of(1, 100));
            return BeanUtil.copyToList(aiPromptInfoPage.getRecords(), AiPromptInfoBusinessView.class);
        }
    }

}
