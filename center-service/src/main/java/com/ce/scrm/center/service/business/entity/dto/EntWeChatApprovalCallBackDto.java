package com.ce.scrm.center.service.business.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @version 1.0
 * @Description: 企业微信审批回调实体类
 * @Author: lijinpeng
 * @Date: 2024/10/17 10:28
 */
@Data
public class EntWeChatApprovalCallBackDto {

    /**
     * 审批信息
     */
    @JsonProperty("ApprovalInfo")
    private ApprovalInfo approvalInfo;

    /**
     * 消息发送时间
     */
    @JsonProperty("CreateTime")
    private Long createTime;

    /**
     * 事件名称：open_approval_change
     */
    @JsonProperty("Event")
    private String event;

    /**
     * 接收方企业Corpid
     */
    @JsonProperty("ToUserName")
    private String toUserName;

    /**
     * 发送方：企业微信
     */
    @JsonProperty("FromUserName")
    private String fromUserName;

    /**
     * 消息类型
     */
    @JsonProperty("MsgType")
    private String msgType;

    /**
     * 企业应用的id，整型。可在应用的设置页面查看
     */
    @JsonProperty("AgentID")
    private Integer agentId;

    @Data
    public static class ApprovalInfo {

        /**
         * 申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
         */
        @JsonProperty("SpStatus")
        private Integer spStatus;

        /**
         * 审批编号
         */
        @JsonProperty("SpNo")
        private Long spNo;

        /**
         * 审批模板id
         */
        @JsonProperty("TemplateId")
        private String templateId;

        /**
         * 审批模板名称
         */
        @JsonProperty("SpName")
        private String spName;

        /**
         * 审批申请提交时间,Unix时间戳
         */
        @JsonProperty("ApplyTime")
        private Long applyTime;

        /**
         * 审批申请状态变化类型：1-提单；2-同意；3-驳回；4-转审；5-催办；6-撤销；8-通过后撤销；10-添加备注；
         * 11-回退给指定审批人；12-添加审批人；13-加签并同意； 14-已办理； 15-已转交
         */
        @JsonProperty("StatuChangeEvent")
        private Integer statuChangeEvent;

        /**
         * 申请人信息
         */
        @JsonProperty("Applyer")
        private Applyer applyer;

        /**
         * 审批流程信息，可能有多个审批节点
         */
        @JsonProperty("SpRecord")
        private SpRecord spRecord;

        /**
         * 审批流程列表
         */
        @JsonProperty("ProcessList")
        private Process processList;

    }

    @Data
    public static class Process {

        /**
         * 流程节点
         */
        @JsonProperty("NodeList")
        private Node nodeList;

    }

    @Data
    public static class Node {

        /**
         * 子节点列表
         */
        @JsonProperty("SubNodeList")
        private List<SubNode> subNodeList;

        /**
         * 节点状态 1-审批中；2-同意；3-驳回；4-转审；11-退回给指定审批人；12-加签；13-同意并加签；14-办理；15-转交
         */
        @JsonProperty("SpStatus")
        private Integer spStatus;

        /**
         * 节点类型 1 审批人 2 抄送人 3办理人
         */
        @JsonProperty("NodeType")
        private Integer nodeType;

        /**
         * 多人办理方式 1-会签；2-或签 3-依次审批
         */
        @JsonProperty("ApvRel")
        private Integer apvRel;

    }

    @Data
    public static class SubNode {

        /**
         * 操作时间
         */
        @JsonProperty("Sptime")
        private Long spTime;

        /**
         * 审批/办理意见
         */
        @JsonProperty("Speech")
        private String speech;

        /**
         * 子节点状态 1-审批中；2-同意；3-驳回；4-转审；11-退回给指定审批人；12-加签；13-同意并加签；14-办理；15-转交
         */
        @JsonProperty("SpYj")
        private Integer spYj;

        /**
         * 处理人信息
         */
        @JsonProperty("UserInfo")
        private UserInfo userInfo;

    }

    @Data
    public static class UserInfo {

        /**
         * 	处理人userid
         */
        @JsonProperty("UserId")
        private String userId;

    }

    @Data
    public static class SpRecord {

        /**
         * 审批节点状态：1-审批中；2-已同意；3-已驳回；4-已转审
         */
        @JsonProperty("SpStatus")
        private String spStatus;

        /**
         * 节点审批方式：1-或签；2-会签
         */
        @JsonProperty("ApproverAttr")
        private String approverAttr;

        /**
         * 审批节点详情。当节点为标签或上级时，一个节点可能有多个分支
         */
        @JsonProperty("Details")
        private List<Details> details;

    }

    @Data
    public static class Details {

        /**
         * 分支审批人审批状态：1-审批中；2-已同意；3-已驳回；4-已转审
         */
        @JsonProperty("SpStatus")
        private String spStatus;

        /**
         * 审批意见字段
         */
        @JsonProperty("Speech")
        private String speech;

        /**
         * 分支审批人
         */
        @JsonProperty("Approver")
        private Applyer approver;

        /**
         * 节点分支审批人审批操作时间，0为尚未操作
         */
        @JsonProperty("SpTime")
        private Long spTime;

    }

    @Data
    public static class Applyer {
        /**
         * 申请人userid
         */
        @JsonProperty("UserId")
        private String userId;
        /**
         * 申请人所在部门pid
         */
        @JsonProperty("Party")
        private String party;

    }

}
