package com.ce.scrm.center.service.eqixiu.sdk.domain.type;

/**
 * 事件类型
 *
 * doc:<a href="https://hc.eqxiu.cn/doc/16/">...</a>
 *
 * <AUTHOR>
 */

public enum EventType {
    CREATION_CREATE("creation_create", "作品创建"),
    CREATION_COPY("creation_copy", "作品复制"),
    CREATION_BASE_CHANGE("creation_base_change", "作品基础数据变动"),
    CREATION_APPROVE("creation_approve", "作品审批"),
    CREATION_PUBLISH("creation_publish", "作品发布"),
    CREATION_STOP("creation_stop", "作品停用"),
    CREATION_DELETE("creation_delete", "作品删除"),
    PREVIEW_VIEW("preview_view", "页面预览"),
    PREVIEW_EXIT("preview_exit", "预览页面退出"),
    PREVIEW_SHARE("preview_share", "预览分享"),
    PV_LIMIT_ALARM("pv_limit_alarm", "PV限制预警"),
    SHARE_SUCCESS("share_success", "分享增加权益"),
    FORM_SUBMIT_ALL("form_submit_all", "表单提交"),
    HELP_OTHER_SUCCESS("help_other_success", "成功帮助他人助力"),
    HELP_PLAYER_SUCCESS("help_player_success", "成功达成目标-完成助力"),
    // 省略其他事件以保持简洁，您可以根据需要添加更多事件

    // 互动事件
    ACTIVITY_PAUSE("activity_pause", "活动暂停"),
    ACTIVITY_RESUME("activity_resume", "活动恢复"),
    ACTIVITY_STOP("activity_stop", "活动结束"),
    PREVIEW_LOTTERY("preview_lottery", "活动中奖"),
    JOIN_LOTTERY("join_lottery", "参与抽奖"),
    FORM_SUBMIT("form_submit", "表单提交"),
    GAME_RANK("game_rank", "游戏分数上榜"),
    EXCHANGE_PRIZE("exchange_prize", "奖品核销"),
    SIGN_SUCCESS("sign_success", "签到成功"),
    JOIN_GAME("join_game", "参与游戏"),
    // 省略其他互动事件

    // H5事件
    LOTTERY_ENROLL("lottery_enroll", "定时开奖-报名"),
    LOSING_LOTTERY("losing_lottery", "定时开奖-未中奖"),
    // 省略其他H5事件

    // 其他事件
    HTTP_INVOKE_FAIL("http_invoke_fail", "自定义抽奖错误或服务请求失败");

    private final String value;
    private final String title;

    EventType(String value, String title) {
        this.value = value;
        this.title = title;
    }

    public static EventType of(String value){
        for(EventType type : EventType.values()){
            if(type.getValue().equals(value)){
                return type;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }

    public String getTitle() {
        return title;
    }
}
