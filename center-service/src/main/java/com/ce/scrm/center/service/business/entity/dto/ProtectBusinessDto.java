package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * @description 保护关系表
 * <AUTHOR>
 * @date 2024-04-19
 */
@Data
public class ProtectBusinessDto implements Serializable {

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户阶段。(2：保护跟进；3：网站客户；4：非网站客户)
     */
    private Integer custType;

    /**
     * 商务代表ID
     */
    private String salerId;

    /**
     * 部门ID
     */
    private String bussdeptId;

    /**
     * 事业部id
     */
    private String buId;

    /**
     * 分公司ID
     */
    private String subcompanyId;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 客户来源
     */
    private Integer custSource;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 二级来源
     */
    private Integer custSourceSub;

    /**
     * 是否计算库容（1不计算，0计算）
     */
    private Integer occupy;

    /**
     * 保护阶段（1、保护；2、总监；3、经理；4、客户池）
     */
    private Integer status;

    /** 阶段性保护时间 */
    private Date protectTime;

    /** 阶段性超期时间 */
    private Date exceedTime;

    /** 绝对保护期 */
    private Date absoluteProtectTime;

}