package com.ce.scrm.center.service.business.entity.view.protect;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Description: 保护关系构建查询
 * @Author: lijinpeng
 * @Date: 2025/1/16 10:33
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProtectBuildCustPoolView implements Serializable {

    private Integer status;

    private Integer source;

    private String reason;

    private Integer isClock;

    private String clockProvince;

    private String clockCity;

    private String clockRegion;

    private Integer isMarketSj;

    private List<String> regionList;

    private String areaId;

    private String subId;

    private String deptId;

    private String salerId;

    private String likeCustName;

    /**
     * 二级来源
     */
    private Integer custSourceSub;

    private Date startTime;

    private Date endTime;

    /**
     * 当前页
     */
    private Integer currentPage = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

}
