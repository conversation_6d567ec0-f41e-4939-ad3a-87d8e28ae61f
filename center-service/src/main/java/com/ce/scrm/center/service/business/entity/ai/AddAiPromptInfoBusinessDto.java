package com.ce.scrm.center.service.business.entity.ai;

import com.ce.scrm.center.dao.entity.AiPromptAuthInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 添加提示词信息
 * @Author: lijinpeng
 * @Date: 2025/2/20 10:00
 */
@Data
public class AddAiPromptInfoBusinessDto implements Serializable {

    private String title;

    private String content;

    /**
     * 员工ID
     */
    private String loginEmployeeId;

    /**
     * ce 中企 gboss 跨境
     */
    private String company;

    private String remark;

    private Integer promptType;


    private List<AiPromptAuthInfo> aiPromptAuthInfos;


}
