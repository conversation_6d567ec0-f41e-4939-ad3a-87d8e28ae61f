package com.ce.scrm.center.service.third.invoke;

import cn.ce.performance.jx.remoteservice.JxCeoInfoRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * description: 绩效
 * @author: DD.Jiu
 * date: 2024/7/18.
 */
@Slf4j
@Service
public class PerformanceThirdService {
    @DubboReference
    private JxCeoInfoRemoteService jxCeoInfoRemoteService;


    public String getPerformanceInfo(String ceoCode) {
        return jxCeoInfoRemoteService.getCeoInfo(new HashMap<String, Object>(), "");
    }
}
