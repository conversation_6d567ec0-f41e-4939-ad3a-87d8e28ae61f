package com.ce.scrm.center.service.router;

import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * @version 1.0
 * @Description: 线程上下文
 * @Author: lijinpeng
 * @Date: 2024/12/11 14:20
 */
@Slf4j
public class RouterContext {

    private static final ThreadLocal<Map<String, Object>> CONTEXT = ThreadLocal.withInitial(HashMap::new);

    private static final String CURRENT_USER_KEY = "currentUser";
    private static final String CONSUME_TIME_KEY = "consumeTimeKey";
    private static final String CONSUME_STEP_SIZE_KEY = "consumeStepSizeKey";

    public static void setCurrentUser(EmployeeInfoBusinessDto currentUser) {
        CONTEXT.get().put(CURRENT_USER_KEY, currentUser);
    }

    public static EmployeeInfoBusinessDto getCurrentUser() {
        Object o = CONTEXT.get().get(CURRENT_USER_KEY);
        if (o == null) {
            return null;
        }
        if (!(o instanceof EmployeeInfoBusinessDto)) {
            return null;
        }
        return (EmployeeInfoBusinessDto) o;
    }

    public static void printConsumeTime() {
        Long startTime = (Long)CONTEXT.get().get(CONSUME_TIME_KEY);
        Integer stepSize = (Integer)CONTEXT.get().get(CONSUME_STEP_SIZE_KEY);
        if(stepSize == null) {
            stepSize = 1;
        }
        long currentTime = System.currentTimeMillis();
        if (startTime != null) {
            Long endTime = currentTime - startTime;
            log.info("李金澎测试代码耗时，第{}步 Consume time is {} ms",stepSize++, endTime);
        }
        CONTEXT.get().put(CONSUME_TIME_KEY, currentTime);
        CONTEXT.get().put(CONSUME_STEP_SIZE_KEY, stepSize);
    }

    public static void clear() {
        CONTEXT.remove();
    }

}
