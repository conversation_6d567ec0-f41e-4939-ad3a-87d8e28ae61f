package com.ce.scrm.center.service.eqixiu.sdk.domain.event;

import com.ce.scrm.center.service.eqixiu.sdk.domain.type.EventType;
import com.ce.scrm.center.service.eqixiu.sdk.util.JsonMapper;

/**
 * 推送事件基类
 *
 * <AUTHOR>
 */
public class Event {

    /**
     * 事件唯一ID
     */
    protected String eventId;

    /**
     * 事件类型
     */
    protected String eventType;

    /**
     * 事件创建时间
     */
    protected String createTime;

    /**
     * 企业ID,客户则的企业唯一ID
     */
    protected String corpOpenId;

    /**
     * 业务数据
     */
    protected String eventData;

    public <T> T getEventData(Class<T> clazz) {
        return JsonMapper.getInstance().fromJson(this.eventData, clazz);
    }

    public String getEventId() {
        return eventId;
    }

    public String getEventType() {
        return eventType;
    }

    public EventType getEventTypeEnum() {
        return EventType.of(eventType);
    }

    public String getCreateTime() {
        return createTime;
    }

    public String getCorpOpenId() {
        return corpOpenId;
    }

    public String getEventData() {
        return eventData;
    }

    @Override
    public String toString() {
        return "Event{" +
                "eventId='" + eventId + '\'' +
                ", eventType='" + eventType + '\'' +
                ", createTime='" + createTime + '\'' +
                ", corpOpenId='" + corpOpenId + '\'' +
                '}';
    }
}
