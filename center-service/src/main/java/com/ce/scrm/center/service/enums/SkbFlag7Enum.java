package com.ce.scrm.center.service.enums;

/**
 * description: 搜客宝的flag7枚举类（规模以上）
 * https://wiki.300.cn/pages/viewpage.action?pageId=60269431
 * @author: DD.Jiu
 * date: 2025/2/18.
 */
public enum SkbFlag7Enum {
    flag7_1("flag7_1", "1", "工业"),
    flag7_2("flag7_2", "2", "服务业"),
    flag7_3("flag7_3", "3", "建筑业"),
    flag7_4("flag7_4", "4", "批发零售业"),
    flag7_5("flag7_5", "5", "住宿餐饮业"),
    flag7_6("flag7_6", "6", "房地产开发与经营业"),
    ;

    private String field;
    private String value;
    private String description;

    SkbFlag7Enum(String field, String value, String description) {
        this.field = field;
        this.value = value;
        this.description = description;
    }

    public String getField() {
        return field;
    }

    public String getDescription() {
        return description;
    }

    public String getValue() {
        return value;
    }

    public static SkbFlag7Enum getByValue(String value) {
        for (SkbFlag7Enum e : SkbFlag7Enum.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }
}

