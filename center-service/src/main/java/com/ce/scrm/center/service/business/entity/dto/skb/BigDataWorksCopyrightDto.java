package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 作品著作权
 * @Author: lijinpeng
 * @Date: 2025/2/18 11:31
 */
@Data
public class BigDataWorksCopyrightDto implements Serializable {

    private int total_count;
    private int total_page;
    private int page;
    @JSONField(name = "作品著作列表")
    private List<ListWorksCopyright> list;

    @NoArgsConstructor
    @Data
    public static class ListWorksCopyright {
        /**
         * 作品名称
         */
        @JSONField(name = "作品名称")
        private String works_name;
        /**
         * 作品类别
         */
        @JSONField(serialize = false)
        private String works_category;
        /**
         * 登记号
         */
        @JSONField(serialize = false)
        private String regist_no;
        /**
         * 登记时间
         */
        @JSONField(serialize = false)
        private String regist_date;
        /**
         * 首次发表日期
         */
        @JSONField(serialize = false)
        private String release_date;
        /**
         * 创作完成日期
         */
        @JSONField(serialize = false)
        private String finish_date;
    }

}
