package com.ce.scrm.center.service.business.abm;

import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.enums.CodeMessageEnum;
import com.ce.scrm.center.dao.entity.SubSjAutoAssignRule;
import com.ce.scrm.center.dao.service.SubSjAutoAssignRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 分司商机自动分配规则业务处理类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
@Slf4j
@Service
public class SubSjAutoAssignRuleBusiness {

    @Resource
    private SubSjAutoAssignRuleService subSjAutoAssignRuleService;

    /***
     * 分司是否自动分配
     * @param subId
     * <AUTHOR>
     * @date 2025/8/7 21:42
     * @version 1.0.0
     * @return boolean
    **/
    public boolean isAutoAssignSub(String subId){
        if (StringUtils.isEmpty(subId)){
            return false;
        }
        SubSjAutoAssignRule bySubId = subSjAutoAssignRuleService.getBySubId(subId);
        if (Objects.isNull(bySubId)){
            return false;
        }
        // 是否开启自动分配 0:否 1:是
        Integer status = bySubId.getStatus();
        if (Objects.equals(status,1)){
            return true;
        }
        return false;
    }
    /***
     * 更新权重
     * @param ruleId 规则ID
     * <AUTHOR>
     * @date 2024/12/19 10:00:00
     * @version 1.0.0
     * @return boolean
     **/
    public boolean updateWeight(Long ruleId) {
        log.info("更新分司自动分配规则权重，规则ID={}", ruleId);
        try {
            // 参数校验
            if (Objects.isNull(ruleId)) {
                throw new ApiException(CodeMessageEnum.REQUEST_PARAM_EXCEPTION);
            }

            boolean result = subSjAutoAssignRuleService.updateWeight(ruleId);
            if (result) {
                log.info("更新分司自动分配规则权重成功，规则ID={}", ruleId);
            } else {
                log.error("更新分司自动分配规则权重失败，规则ID={}", ruleId);
            }
            return result;
        } catch (Exception e) {
            log.error("更新分司自动分配规则权重异常，规则ID={}", ruleId, e);
            return false;
        }
    }
} 