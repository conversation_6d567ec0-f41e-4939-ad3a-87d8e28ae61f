package com.ce.scrm.center.service.enums;

/**
 * description: 同步到ai的user字段枚举
 * @author: DD.Jiu
 * date: 2025/2/17.
 */
public enum SyncCrmFieldEnum {
    digital_menhu_latest_end_time_20250101("digital_menhu_latest_end_time_20250101", "数字门户_最晚到期时间_20250101", CdpTypeEnum.Datetime),
    menhu_latest_end_time_20250101("menhu_latest_end_time_20250101", "门户产品_最晚到期时间_20250101", CdpTypeEnum.Datetime),
    ali_international_urls_404_2024("ali_international_urls_404_2024", "2024年ali_国际站地址_404", CdpTypeEnum.String),
    ali_international_urls_count_404_2024("ali_international_urls_count_404_2024", "2024年ali_国际站地址_404_数量", CdpTypeEnum.Number),
    domain_2024_PV("domain_2024_PV", "2024年产生流量的域名", CdpTypeEnum.String),
    china_PV_2024("china_PV_2024", "2024年国内流量(PV)", CdpTypeEnum.Number),
    china_PV_2024_festival("china_PV_2024_festival", "2024年春节国内流量(PV)", CdpTypeEnum.Number),
    top5_overseas_countries_PV_2024_festival("top5_overseas_countries_PV_2024_festival", "2024年春节海外Top5国家与流量(PV)", CdpTypeEnum.Number),
    top5_overseas_countries_sum_PV_2024_festival("top5_overseas_countries_sum_PV_2024_festival", "2024年春节海外Top5国家与流量(PV)总和", CdpTypeEnum.Number),
    top5_overseas_countries_pv_2024("top5_overseas_countries_pv_2024", "2024年海外Top5国家与流量(PV)", CdpTypeEnum.Number),
    top5_overseas_countries_sum_PV_2024("top5_overseas_countries_sum_PV_2024", "2024年海外Top5国家与流量(PV)总和", CdpTypeEnum.Number),
    //搜客宝工商信息已有
    //third_industry("third_industry", "国标三级行业code", CdpTypeEnum.String),
    //搜客宝工商信息已有
    //fourth_industry("fourth_industry", "国标四级行业code", CdpTypeEnum.String),
    ali_international_urls_2024("ali_international_urls_2024", "2024年ali_国际站_地址", CdpTypeEnum.String),
    ali_international_transactionamount_sum_2024("ali_international_transactionamount_sum_2024", "2024年ali_国际站_交易金额_汇总", CdpTypeEnum.Number),
    ali_international_bond_sum_2024("ali_international_bond_sum_2024", "2024年ali_国际站_信保金额_汇总", CdpTypeEnum.Number),
    ali_international_years_max_2024("ali_international_years_max_2024", "2024年ali_国际站_年限_最大值", CdpTypeEnum.Number),
    ali_international_orderNum_sum_2024("ali_international_orderNum_sum_2024", "2024年ali_国际站_订单数量_汇总", CdpTypeEnum.Number),
    ali_international_url_count_2024("ali_international_url_count_2024", "2024年ali_国际站_地址_数量", CdpTypeEnum.Number),
    bu_name("bu_name", "客户所属事业部名称", CdpTypeEnum.String),
    //bu_id("bu_id", "客户所属事业部ID", CdpTypeEnum.String),
    sub_name("sub_name", "客户所属分司名称", CdpTypeEnum.String),
    area_name("area_name", "客户所属区域名称", CdpTypeEnum.String),
    saler_name("saler_name", "客户所属商务姓名", CdpTypeEnum.String),
    dept_name("dept_name", "客户所属部门名称", CdpTypeEnum.String),
    //listed_company_flag("listed_company_flag", "是否上市公司（数据来源于2024.6.28）", CdpTypeEnum.Bool),
    highTech_flag("highTech_flag", "是否是高新企业", CdpTypeEnum.Bool),
    //搜客宝工商信息已有
    //patent_name("patent_name", "专利名称", CdpTypeEnum.String),
    //patent_type("patent_type", "专利类型", CdpTypeEnum.String),
    //contact_address("contact_address", "企业通讯地址", CdpTypeEnum.String),
    //trade_show_name("trade_show_name", "参展名称", CdpTypeEnum.String),
    //brand_product("brand_product", "品牌产品", CdpTypeEnum.String),
    //搜客宝工商信息已有
    //brand_name("brand_name", "品牌名字", CdpTypeEnum.String),
    //e_product_name("e_product_name", "商品名称", CdpTypeEnum.String),   字段太长了
    //搜客宝工商信息已有
    //trademark_name("trademark_name", "商标名称", CdpTypeEnum.String),
    //trademark_type("trademark_type", "商标类型", CdpTypeEnum.String),
    wechat_name("wechat_name", "微信公众号名称", CdpTypeEnum.String),
    //搜客宝工商信息已有
    //sem_keyword("sem_keyword", "推广关键词", CdpTypeEnum.String),
    //搜客宝工商信息已有
    //sem_title("sem_title", "推广标题", CdpTypeEnum.String),
    //搜客宝工商信息已有
    //legal_person_name("legal_person_name", "法人姓名", CdpTypeEnum.String),
    //uncid("uncid", "统一信用代码", CdpTypeEnum.String),
    //搜客宝工商信息已有
    //cus_credit_level("cus_credit_level", "进出口信用等级", CdpTypeEnum.String),
    //搜客宝工商信息已有
    //is_unicorn("is_unicorn", "是否独角兽企业", CdpTypeEnum.Bool),
    //搜客宝工商信息已有
    //is_gazelle("is_gazelle", "是否瞪羚企业", CdpTypeEnum.Bool),
    //legal_person_phone("legal_person_phone", "法人手机（数据更新截至2023.03）", CdpTypeEnum.String),
    //legal_person_position("legal_person_position", "法人职位（数据更新截至2023.03）", CdpTypeEnum.String),
    tags_flag8("tags_flag8", "现代服务业类型", CdpTypeEnum.String),
    tags_flag7("tags_flag7", "规上行业类型", CdpTypeEnum.String),
    tags_menhu_2023("tags_menhu_2023", "是否2023版本门户", CdpTypeEnum.Bool),
    tags_cross_buy("tags_cross_buy", "是否2024交叉购买客户", CdpTypeEnum.Bool),
    tags_pure_menhu("tags_pure_menhu", "是否2024纯门户客户", CdpTypeEnum.Bool),
    tags_menhu_related("tags_menhu_related", "是否2024门户连带客户", CdpTypeEnum.Bool),
    tags_menhu_lowver("tags_menhu_lowver", "是否低版本门户", CdpTypeEnum.Bool),
    tags_menhu_digital("tags_menhu_digital", "是否数字版本门户", CdpTypeEnum.Bool),
    tags_eco_to_menhu("tags_eco_to_menhu", "是否生态转门户", CdpTypeEnum.Bool),
    tags_menhu_upgradeable_upgrade("tags_menhu_upgradeable_upgrade", "是否门户应升已升客户", CdpTypeEnum.Bool),
    tags_menhu_upgradeable("tags_menhu_upgradeable", "是否门户应升级客户", CdpTypeEnum.Bool),
    tags_menhu_renewable("tags_menhu_renewable", "是否门户应续客户-（有效期至2025.6.30）", CdpTypeEnum.Bool),
    tags_menhu_renewable_renew("tags_menhu_renewable_renew", "是否门户应续已续客户-（有效期至2025.6.30）", CdpTypeEnum.Bool),
    tags_eco_cust("tags_eco_cust", "是否首购生态客户", CdpTypeEnum.Bool),
    pay_amount("pay_amount", "客户支付成功总金额", CdpTypeEnum.Number),
    //搜客宝工商信息已有
    //first_industry("first_industry", "国标一级行业code", CdpTypeEnum.String),
    //搜客宝工商信息已有
    //second_industry("second_industry", "国标二级行业code", CdpTypeEnum.String),
    sem_platform_360("sem_platform_360", "是否有360竞价推广（SEM）", CdpTypeEnum.Bool),
    //has_patent("has_patent", "是否有专利", CdpTypeEnum.Bool),
    //has_trademark("has_trademark", "是否有商标", CdpTypeEnum.Bool),
    sem_platform_sougou("sem_platform_sougou", "是否有搜狗竞价推广（SEM）", CdpTypeEnum.Bool),
    sem_platform_baidu("sem_platform_baidu", "是否有百度竞价推广（SEM）", CdpTypeEnum.Bool),
    //搜客宝工商信息已有
    //establish_date("establish_date", "成立日期", CdpTypeEnum.Datetime),
    //has_import_export_credit("has_import_export_credit", "是否含进出口信用", CdpTypeEnum.Bool),
    has_wechat("has_wechat", "是否有公众号", CdpTypeEnum.Bool)
    //搜客宝工商信息已有
    //reg_cap("reg_cap", "注册资本", CdpTypeEnum.String),
    //reg_caps_unit("reg_caps_unit", "注册资本单位", CdpTypeEnum.String),
    //ent_status("ent_status", "营业状态code", CdpTypeEnum.String),
    //cust_name("cust_name", "客户名称", CdpTypeEnum.String);
    ;

    private String field;
    private String description;
    private CdpTypeEnum fieldType;

    SyncCrmFieldEnum(String field, String description, CdpTypeEnum fieldType) {
        this.field = field;
        this.description = description;
        this.fieldType = fieldType;
    }

    public String getField() {
        return field;
    }

    public String getDescription() {
        return description;
    }

    public CdpTypeEnum getFieldType() {
        return fieldType;
    }
}

