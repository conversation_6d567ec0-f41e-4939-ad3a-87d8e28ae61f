package com.ce.scrm.center.service.business.entity.dto;

import cn.ce.cesupport.enums.GcSjSourceEnum;
import com.ce.scrm.center.service.enums.GcSjLevelEnum;
import com.ce.scrm.center.service.enums.GcSjRequirementEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 高呈商机添加业务参数
 * <AUTHOR>
 * @date 2024/5/16 下午4:45
 * @version 1.0.0
 **/
@Data
public class GcBusinessOpportunitySaveBusinessDto implements Serializable {

    /**
     * 高呈商机ID（更新必传）
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 等级：1、A（高），2、B（中），3、C（低），4、D（其他）
     */
    private GcSjLevelEnum level;

    /**
     * 来源
     */
    private GcSjSourceEnum source;

    /**
     * 区域ID
     */
    private String gcAreaId;

    /**
     * 区域名称
     */
    private String gcAreaName;

    /**
     * 分司ID
     */
    private String gcSubId;

    /**
     * 分司名称
     */
    private String gcSubName;

    /**
     * 联系人名称
     */
    private String linkmanName;

    /**
     * 联系人手机号
     */
    private String linkmanPhone;

    /**
     * 联系人邮箱
     */
    private String linkmanEmail;

    /**
     * 联系人部门
     */
    private String linkmanDept;

    /**
     * 联系人性别：0、未知，1、男，2、女
     */
    private Integer linkmanSex;

    /**
     * 联系人座机
     */
    private String linkmanLandline;

    /**
     * 联系人职务
     */
    private String linkmanJob;

    /**
     * 联系人微信
     */
    private String linkmanWechat;

    /**
     * 需求类型
     */
    private GcSjRequirementEnum requirementType;

    /**
     * 需求详情
     */
    private String requirementDetail;

    /**
     * 客户预算
     */
    private BigDecimal customerBudget;

    /**
     * 附件ID（多个使用,分隔）
     */
    private String attachmentIds;

    /**
     * 操作人ID
     */
    private String operator;

    /**
     * 操作人名称
     */
    private String operatorName;
}