/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.template;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 将作品转为企业模板
 *
 * <AUTHOR>
 */
public class MakeCorpTemplateCmd extends BaseParam {

    /**
     * 处理模板数据的方式
     * true：异步；false：同步,默认为同步false
     */
    private boolean async = false;

    /**
     * 要转成模板的作品 ID，单次不超过 10个
     */
    private String creationIds;


    public MakeCorpTemplateCmd(Long creationId, String openId) {
        this.creationIds = String.valueOf(creationId);
        setOpenId(openId);
    }

    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> paramsMap = getBaseParamsMap();
        paramsMap.put("async", String.valueOf(async));
        paramsMap.put("creationIds", creationIds);
        return paramsMap;
    }

    @Override
    public void validate() {
        if (StrUtil.isEmpty(getOpenId())) {
            throw new KnownException("openId不能为空");
        }
        if (StrUtil.isEmpty(creationIds)) {
            throw new KnownException("creationId不能为空");
        }
    }

    public void setAsync(boolean async) {
        this.async = async;
    }

    public void setCreationIds(Long creationId) {
        this.creationIds = String.valueOf(creationId);
    }

    public void setCreationIds(List<Long> creationIds) {
        this.creationIds = creationIds.stream().map(Object::toString).collect(Collectors.joining(","));
    }

    public void setCreationId(Long... creationId) {
        this.creationIds = Arrays.stream(creationId).map(Object::toString).collect(Collectors.joining(","));
    }
}
