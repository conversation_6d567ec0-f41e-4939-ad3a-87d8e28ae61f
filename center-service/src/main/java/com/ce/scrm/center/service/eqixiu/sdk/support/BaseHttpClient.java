/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.support;

import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * httpClient实现封装
 */
public class BaseHttpClient {

    private final HttpClient httpClient;

    public BaseHttpClient(HttpClient httpClient) {
        this.httpClient = httpClient;
    }

    public JSONObject httpGet(String url, Map<String, String> header, Map<String, String> params) {
        return httpClient.httpGet(url, addDefaultHeader(header), params);
    }

    public JSONObject httpGet(String url, Map<String, String> params) {
        return httpClient.httpGet(url, addDefaultHeader(null), params);
    }

    public JSONObject httpPost(String url, Map<String, String> header, Map<String, String> params) {
        return httpClient.httpPost(url, addDefaultHeader(header), params);
    }

    public JSONObject httpPostObj(String url, Map<String, String> header, Object param) {
        return httpClient.httpPostObj(url, addDefaultHeader(header), param);
    }

    public JSONObject httpPostObj(String url, Object param) {
        return httpClient.httpPostObj(url, addDefaultHeader(null), param);
    }

    private Map<String, String> addDefaultHeader(Map<String, String> header) {
        if (header == null) {
            header = new HashMap<>();
        }
        header.put("Referer", "eqxiu-open-sdk v1.0");
        return header;
    }
}
