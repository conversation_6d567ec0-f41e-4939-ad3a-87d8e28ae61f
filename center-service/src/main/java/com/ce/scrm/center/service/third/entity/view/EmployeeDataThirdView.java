package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

/**
 * 员工三方数据封装
 * <AUTHOR>
 * @date 2024/5/20 上午10:21
 * @version 1.0.0
 **/
@Data
public class EmployeeDataThirdView {
    /**
     * 员工ID
     */
    private String id;
    /**
     * 员工名称
     */
    private String name;
    /**
     * 分司ID
     */
    private String subId;
    /**
     * 分司名称
     */
    private String subName;
    /**
     * 部门ID
     */
    private String orgId;
    /**
     * 部门名称
     */
    private String orgName;
    /**
     * 区域ID
     */
    private String areaId;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 工作邮箱
     */
    private String workMail;
    /**
     * 员工职位
     */
    private String position;
    /**
     * 绑定手机号
     */
    private String bindMobile;
    /**
     * 职级
     */
    private String jobGrade;
    /**
     * 高呈员工标记
     */
    private Boolean gcEmployeeFlag = false;

    /**
     * 事业部id
     */
    private String buId;

    private String buName;

}