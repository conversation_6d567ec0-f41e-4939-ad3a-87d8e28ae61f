package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 根据手机号查询客户信息 （搜客宝数据源）
 * @Author: lijinpeng
 * @Date: 2024/12/12 13:42
 */
@Data
public class BigDataSkbCompanyByPhoneView implements Serializable {

    private String pid;

    /**
     * 企业名称
     */
    private String entName;

    /**
     * 联系地址
     */
    private String contactAddress;

    /**
     * 企业经营状态
     */
    private String entStatus;

    /**
     * 省市区
     */
    private String location;

    /**
     * 企业类型
     */
    private String entType;

    /**
     * 注册资本
     */
    private String regcap;

    /**
     * 成立年限
     */
    private String opFrom;

    /**
     * 企业类型
     */
    private String industryphy;

}
