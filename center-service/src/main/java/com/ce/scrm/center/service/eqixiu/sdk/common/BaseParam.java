/*
 *   Copyright (c) 2021-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.common;


import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 封装通用请求参数
 *
 * <AUTHOR>
 */
public abstract class BaseParam {
    private String token;

    /**
     * 企业中对应的员工ID,或企业在平台侧的唯一ID
     */
    private String openId;

    private int pageNo = 1;

    private int pageSize = 10;

    public abstract Map<String, String> getParamsMap();

    public abstract void validate();

    protected Map<String, String> getBaseParamsMap() {
        HashMap<String, String> map = new HashMap<>();
        if (StrUtil.isNotEmpty(token)) {
            map.put("token", token);
        }
        if (StrUtil.isNotEmpty(openId)) {
            map.put("openId", openId);
        }
        map.put("pageNo", String.valueOf(pageNo));
        map.put("pageSize", String.valueOf(pageSize));
        return map;
    }

    /**
     * 验证 openId 是否为空
     */
    protected void validateOpenId() {
        if (StrUtil.isEmpty(openId)) {
            throw new KnownException("openId 不可以为空");
        }
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}
