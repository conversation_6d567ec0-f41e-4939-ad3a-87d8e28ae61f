package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 地理打卡查询参数
 * @author: JiuDD
 * date: 2024/7/24
 */
@Data
@Accessors(chain = true)
public class EmpCustSiteClockBusinessDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 员工id
     */
    private String empId;

    /**
     * 打卡地址
     */
    private String clockAddress;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 跟踪记录id
     */
    private String followRecordId;

    /**
     * 附件id
     */
    private String attachIds;

    /**
     * 打卡所在省
     */
    private String clockProvince;

    /**
     * 打卡所在市
     */
    private String clockCity;

    /**
     * 打卡所在区
     */
    private String clockRegion;

    /**
     * 警告1
     */
    private Integer warning1;

    /**
     * 警告2
     */
    private Integer warning2;

    /**
     * 警告3
     */
    private Integer warning3;

    //search
    private String createDateStart;
    private String createDateEnd;

    /**
     * 客户阶段：1保护客户 2成交客户 3其他
     */
    private Integer customerStage;

    /**
     * 客户注册地址
     */
    private String regAddress;
}