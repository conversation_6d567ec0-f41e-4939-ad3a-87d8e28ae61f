/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.creation;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.CreationType;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

public class CreateCreationCmd extends BaseParam {

    /**
     * 作品类型
     */
    private CreationType type;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 参数：表单或海报空白创建时需要指定，用于指定子品类，后缓转成枚举值
     * <a href="https://hc.eqxiu.cn/doc/35/#h2-u521Bu5EFAu4F5Cu54C1">参数说明</a>
     */
    private String extra;


    @Override
    public Map<String, String> getParamsMap() {
        Map param = getBaseParamsMap();
        param.put("type", type.getValue());
        if (templateId != null) {
            param.put("templateId", templateId.toString());
        }
        if (StrUtil.isNotEmpty(extra)) {
            param.put("extra", extra);
        }
        return param;
    }

    /**
     * <a href="https://hc.eqxiu.cn/doc/35/#h2-u521Bu5EFAu4F5Cu54C1">参数说明</a>
     */
    @Override
    public void validate() {
        if (type == null) {
            throw new KnownException("type 参数不能为空");
        }
        if (type == CreationType.HD && templateId == null) {
            throw new KnownException("创建互动作品必须指定 templateId");
        }
        if ((type == CreationType.FORM || type == CreationType.DESIGN) && extra == null) {
            throw new KnownException("创建表单或海报作品必须指定 extra");
        }
        if (StrUtil.isEmpty(getOpenId())) {
            throw new KnownException("openId 参数不能为空");
        }
    }

    public void setType(CreationType type) {
        this.type = type;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }
}
