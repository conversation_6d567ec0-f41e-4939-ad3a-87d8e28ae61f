package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import com.ce.scrm.extend.dubbo.entity.view.ContactsSourceView;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 企业联系方式
 * @Author: lijinpeng
 * @Date: 2025/2/18 09:45
 */
@Data
public class BigDataContactInformationDto implements Serializable {

    /**
     * 是否精准联系人
     */
    @JSONField(name = "是否精准联系人")
    private Boolean accurate_ent;
    /**
     * 联系人
     */
    @JSONField(name = "联系人名称")
    private String contact;
    /**
     * 号码
     */
    @JSONField(name = "联系人号码")
    private String content;
    /**
     * 外部序号
     */
    @JSONField(name = "外部序号",serialize = false)
    private String outSort;
    /**
     * 联系人职位
     */
    @JSONField(name = "联系人职位")
    private String position;
    /**
     * 号码重复数
     */
    @JSONField(name = "号码重复数",serialize = false)
    private int repeat_amount;
    /**
     * 来源
     */
    @JSONField(name = "来源列表")
    private List<ContactsSourceView> sources;
    /**
     * 手机号状态 : 1000实号、2000沉默号、2001停机、2002空号、2003风险号、3000库无、3001非法号码、3002未验证
     */
    @JSONField(name = "手机号状态",serialize = false)
    private String status;
    /**
     * 号码类型 【0:未知 1:手机; 2:固话; 3:QQ; 4:邮箱】
     */
    @JSONField(name = "号码类型",serialize = false)
    private Integer type;

}
