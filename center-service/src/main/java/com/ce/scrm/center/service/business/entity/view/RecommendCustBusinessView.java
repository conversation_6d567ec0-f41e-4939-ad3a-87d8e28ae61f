package com.ce.scrm.center.service.business.entity.view;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 推荐客户
 * @createDate 2024-08-13
 */
@Data
public class RecommendCustBusinessView {

    private String id;

    /**
     * 被推荐客户id
     */
    private String recommendedCustId;
    /**
     * 被推荐客户所在分司id
     */
    private String subId;
    /**
     * 被推荐客户所在部门id
     */
    private String deptId;
    /**
     * 被推荐客户所在区域id
     */
    private String areaId;
    /**
     * 被推荐客户所在商务id
     */
    private String salerId;
    /**
     * 推荐客户id
     */
    private String recommendCustId;
    /**
     * 推荐客户名称
     */
    private String recommendCustName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 是否签单
     */
    private Integer isSign;
    /**
     * 首次签单时间
     */
    private LocalDateTime firstSignTime;
}