package com.ce.scrm.center.service.cache;

import cn.hutool.core.util.ObjectUtil;
import com.ce.scrm.center.cache.constant.CacheConstant;
import com.ce.scrm.center.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.center.cache.handler.AbstractStringCacheHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * @version 1.0
 * @Description: 企业微信登录code
 * @Author: lijinpeng
 * @Date: 2025/2/7 13:57
 */
@Slf4j
@Component
public class EntVxCodeCacheHandler extends AbstractStringCacheHandler<String> {

    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.ENT_WX_CODE_CACHE;
    }

    @Override
    protected Class<String> getClazz() {
        return String.class;
    }

    @Override
    protected String queryDataBySource(String customerKey) {
        return null;
    }

    @Override
    protected long getExpire() {
        return CacheConstant.CacheExpire.TEN_HOURS;
    }

    public void setCacheData(String key, String cacheValue) {
        String cacheKey = getKey(key);
        if (ObjectUtil.isEmpty(cacheValue)) {
            cacheValue = getNullData();
        } else {
            cacheValue = packageCacheValue(cacheValue);
        }
        super.setCacheData(cacheKey, cacheValue, getExpire());
    }

}
