package com.ce.scrm.center.service.business.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 流转流失客户
 * @author: JiuDD
 * date: 2024/7/22
 */
@Data
public class CirculationLossPageBusinessView implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 事业部id
     */
    private String buId;

    /**
     * 事业部名称
     */
    private String buName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 流转（流失）原因
     */
    private String reason;

    /**
     * 来源（流转、流失）
     */
    private Integer origin;

    /**
     * 状态 总监是否已经分配 0：未分配 1：已分配
     */
    private Integer status;

    /**
     * 预计流转（流失）日期
     */
    private Date preDate;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;
    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 客户分层（商务）
     */
    private Integer customerLayer;

    /**
     * 客户分层（商务） 字符版
     */
    private String customerLayerStr;

    /**
     * 上次触达员工id
     */
    private String lastEmpId;

    /**
     * 上次触达员工名称
     */
    private String lastEmpName;

    /**
     * 上次触达方式
     */
    private Integer lastReachType;

    /**
     * 上次触达方式 字符版
     */
    private String lastReachTypeStr;

    /**
     * 上次触达时间
     */
    private Date lastReachTime;

}