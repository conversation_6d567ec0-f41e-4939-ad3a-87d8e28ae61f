package com.ce.scrm.center.service.business.entity.view.org;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 根据orgId获取上级信息
 * @Author: lijinpeng
 * @Date: 2024/12/2 12:02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrgParentQueryBusinessView implements Serializable {

    private String deptId;

    private String deptName;

    private String buId;

    private String buName;

    private String subId;

    private String subName;

    private String areaId;

    private String areaName;

}
