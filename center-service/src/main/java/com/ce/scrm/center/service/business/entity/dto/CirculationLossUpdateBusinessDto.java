package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 流转流失客户update参数
 * @author: JiuDD
 * date: 2024/7/22
 */
@Data
@Accessors(chain = true)
public class CirculationLossUpdateBusinessDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 流转（流失）原因
     */
    private String reason;

    /**
     * 来源（流转、流失）
     * @see com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum
     */
    private Integer origin;

    /**
     * 状态 总监是否已经分配 0：未分配 1：已分配
     */
    private Integer status;

    /**
     * 删除标记：1删除，0未删除
     */
    private Integer deleteFlag;

    /**
     * 预计流转（流失）日期
     */
    private Date preDate;

}