package com.ce.scrm.center.service.business.entity.ai;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/3/21 17:23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerInfoByNameBusinessView implements Serializable {

    private String customerName;

    private String customerId;

    private String pid;

}
