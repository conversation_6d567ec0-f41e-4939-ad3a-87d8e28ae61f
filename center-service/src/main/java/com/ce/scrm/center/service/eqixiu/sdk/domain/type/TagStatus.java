package com.ce.scrm.center.service.eqixiu.sdk.domain.type;

/**
 * 标签状态：正常、删除
 *
 * <AUTHOR>
 */
public enum TagStatus {
    NORMAL(1, "正常"),
    DISABLE(2,"停用"),
    DELETED(-1, "删除"),
    UNKNOWN(0, "未知");

    private int value;
    private String title;

    TagStatus(int value, String title) {
        this.value = value;
        this.title = title;
    }

    public int value() {
        return this.value;
    }
    public String title() {return this.title;}

    public static TagStatus of(Integer value) {
        if (1 == value) {
            return NORMAL;
        }
        if(2 == value){
            return DISABLE;
        }
        if (-1 == value) {
            return DELETED;
        }
        return UNKNOWN;
    }

}
