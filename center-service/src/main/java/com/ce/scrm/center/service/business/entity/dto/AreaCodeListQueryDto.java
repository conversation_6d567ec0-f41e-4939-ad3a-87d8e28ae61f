package com.ce.scrm.center.service.business.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 区域code查询参数
 * @Author: lijinpeng
 * @Date: 2025/1/15 11:48
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AreaCodeListQueryDto implements Serializable {

    /**
     * 分司集合
     */
    @NotEmpty
    private List<String> subIdList;

    /**
     * 省code
     */
    private String provinceCode;

    /**
     * 市code
     */
    private String cityCode;

    /**
     * 区code
     */
    private String regionCode;

}
