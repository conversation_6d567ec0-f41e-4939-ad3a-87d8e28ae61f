package com.ce.scrm.center.service.third.entity.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerTagRelaAddThirdDto implements Serializable {

	/**
	 * tagId
	 */
	private Long tagId;

	/**
	 * 标签名称
	 */
	private String tagName;

	/**
	 * 标签积分
	 */
	private BigDecimal weight;

	/**
	 * 客户id
	 */
	private String customerId;

	/**
	 * 创建人
	 */
	private String createBy;

}
