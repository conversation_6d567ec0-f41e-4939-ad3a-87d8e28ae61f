package com.ce.scrm.center.service.business.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Description: 请求企业微信提交审批实体类 https://developer.work.weixin.qq.com/document/path/91853
 * @Author: lijinpeng
 * @Date: 2024/10/15 13:50
 */
@Data
@Builder
public class EntWeChatApprovalDto {

    /**
     * 调用接口凭证，见获取access_token
     */
    private String accessToken;

    /**
     * 申请人userid，此审批申请将以此员工身份提交，申请人需在应用可见范围内
     */
    private String creatorUserId;

    /**
     * 模板id。可在“获取审批申请详情”、“审批状态变化回调通知”中获得，也可在审批模板的模板编辑页面链接中获得。
     * 暂不支持通过接口提交[打卡补卡][调班]模板审批单。
     */
    private String templateId;

    /**
     * 审批人模式：
     * 0-通过接口指定审批人、抄送人（此时process参数必填）;
     * 1-使用此模板在管理后台设置的审批流程(需要保证审批流程中没有“申请人自选”节点)，支持条件审批。默认为0
     */
    private Integer useTemplateApprover;

    /**
     * 提单者提单部门id，不填默认为主部门
     */
    private Integer chooseDepartment;

    /**
     * 审批申请数据，可定义审批申请中各个控件的值，其中必填项必须有值，选填项可为空，
     * 数据结构同“获取审批申请详情”接口返回值中同名参数“apply_data”
     */
    private ApplyData applyData;

    /**
     *  保护客户名称
     */
    private String protectEmtName;

    /**
     * 情况说明
     */
    private String description;

    /**
     * 客户保护商务
     */
    private String protectSalerName;

    /**
     * 合作发起商务
     */
    private String approvalSalerName;

    /**
     * 新版流程列表
     */
    private Process process;

    /**
     * 摘要信息，用于显示在审批通知卡片、审批列表的摘要信息，最多3行
     */
    @JsonProperty("summary_list")
    private List<Object> summaryList;

    @Data
    @Builder
    public static class SummaryInfo {
        /**
         * 摘要行显示文字，用于记录列表和消息通知的显示，不要超过20个字符
         */
        private String text;
        /**
         * 摘要行显示语言，中文：zh_CN（注意不是zh-CN），英文：en。
         */
        private String lang;
    }

    @Data
    @Builder
    public static class Process {

        @JsonProperty("node_list")
        private List<Node> nodeList;

    }

    @Data
    @Builder
    public static class Node {
        /**
         * 节点类型 1:审批人 2:抄送人 3:办理人
         */
        private Integer type;
        /**
         * type为1、2时必填,多人审批方式 1-会签；2-或签 3-依次审批
         */
        @JsonProperty("apv_rel")
        private Integer apvRel;
        /**
         * 用户id
         */
        @JsonProperty("userid")
        private List<String> userId;
    }

    @Data
    @Builder
    public static class ApplyData {

        /**
         * 审批申请详情，由多个表单控件及其内容组成，其中包含需要对控件赋值的信息
         */
        private List<Content> contents;

    }

    @Data
    @Builder
    public static class Content {

        /**
         * 控件类型：
         * ext-文本；Textarea-多行文本；Number-数字；Money-金额；Date-日期/日期+时间；
         * Selector-单选/多选；；Contact-成员/部门；Tips-说明文字；File-附件；Table-明细；
         * Location-位置；RelatedApproval-关联审批单；Formula-公式；DateRange-时长；
         */
        private String control;
        /**
         * 控件id：控件的唯一id，可通过“获取审批模板详情”接口获取
         */
        private String id;
        /**
         * 控件值 ，需在此为申请人在各个控件中填写内容不同控件有不同的赋值参数，
         * 具体说明详见附录。模板配置的控件属性为必填时，对应value值需要有值。
         */
        private Map<String,String> value;

    }

}
