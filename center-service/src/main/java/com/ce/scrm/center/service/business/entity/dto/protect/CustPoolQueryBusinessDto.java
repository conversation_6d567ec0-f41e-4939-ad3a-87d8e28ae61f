package com.ce.scrm.center.service.business.entity.dto.protect;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 客户池查询
 * @Author: lijinpeng
 * @Date: 2025/1/15 10:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustPoolQueryBusinessDto implements Serializable {

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 客户名称 like查询 %custName%
     */
    private String likeCustName;

    /**
     * 省code
     */
    private String province;

    /**
     * 市code
     */
    private String city;

    /**
     * 区code
     */
    private String region;

    /**
     * 客户来源（1=释放、2=流失）
     * @see cn.ce.cesupport.enums.protect.ProtectSourceEnum
     */
    @NotNull
    private Integer source;

    /**
     * 释放原因
     */
    private String reason;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 是否是市场商机（1是，0否）
     */
    private Integer isMarketSj;

    /**
     * 二级来源
     */
    private Integer custSourceSub;

    /**
     * 是否已打卡（1是，0否）
     */
    private Integer isClock;

    /**
     * 打卡 省code
     */
    private String clockProvince;

    /**
     * 打卡 市code
     */
    private String clockCity;

    /**
     * 打卡 区code
     */
    private String clockRegion;

    /**
     * 当前页
     */
    private Integer currentPage = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

}
