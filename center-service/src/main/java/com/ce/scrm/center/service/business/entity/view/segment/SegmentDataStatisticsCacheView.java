package com.ce.scrm.center.service.business.entity.view.segment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 专项数据跟踪缓存视图
 * @Author: lijinpeng
 * @Date: 2025/2/28 11:14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SegmentDataStatisticsCacheView implements Serializable {

    /**
     * 昨日拜访量
     */
    private Long yesterdayVisitCount;

    /**
     * 昨日签单量
     */
    private Long yesterdaySignCount;

	/**
	 * 昨日保护量
	 */
    private Long yesterdayProtectedCount;


	/**
	 * 昨日处理量
	 */
	private Long yesterdayHandleCount;


    /**
     * 累计拜访
     */
    private Long allVisitCount;

    /**
     * 累计签单
     */
    private Long allSignCount;

	/**
	 * 累计保护量
	 */
	private Long allProtectedCount;

	/**
	 * 累计处理量
	 */
	private Long allHandleCount;

	/**
	 * 当前角色的下发总量
	 */
	private Long segmentDistributeCount;

	/**
	 * 当前角色下发时已保护数量
	 */
	private Long distributeProtectedCount;

	/**
	 * 当前角色下发时已签单数量
	 */
	private Long distributeSignCount;
}
