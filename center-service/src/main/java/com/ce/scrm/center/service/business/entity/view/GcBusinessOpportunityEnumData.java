package com.ce.scrm.center.service.business.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 高呈商机枚举数据
 * <AUTHOR>
 * @date 2024/5/17 下午1:41
 * @version 1.0.0
 */
@Data
public class GcBusinessOpportunityEnumData implements Serializable {
    /**
     * 高呈商机来源状态数据
     */
    private List<GcBusinessOpportunitySourceStateData> gcBusinessOpportunitySourceStateDataList;

    /**
     * 高呈商机等级数据
     */
    private List<GcBusinessOpportunityLevelData> gcBusinessOpportunityLevelDataList;

    /**
     * 需求类型
     */
    private List<GcBusinessOpportunityRequirementData> gcBusinessOpportunityRequirementDataList;

    /**
     * 客户阶段数据
     */
    private List<GcBusinessOpportunityCustomerStageData> gcBusinessOpportunityCustomerStageDataList;
}