package com.ce.scrm.center.service.business;

import cn.ce.cecloud.base.utils.DateUtil;
import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.base.utils.MyStringUtils;
import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.ce.cesupport.enums.ProtectStatusEnum;
import cn.ce.cesupport.enums.UserActionTypeEnum;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.ce.cesupport.enums.favorites.*;
import cn.ce.cesupport.newcustclue.service.ClueAssignAppService;
import cn.ce.cesupport.newcustclue.service.ClueRuleAppService;
import cn.ce.cesupport.newcustclue.vo.ClueAssignVo;
import cn.ce.cesupport.newcustclue.vo.ClueRuleVo;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.business.entity.dto.AddFavoriteBatchBuildDataDto;
import com.ce.scrm.center.service.business.entity.dto.AddFavoriteBatchBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.org.OrgParentQueryBusinessDto;
import com.ce.scrm.center.service.business.entity.view.BatchResultBusinessView;
import com.ce.scrm.center.service.business.entity.view.org.OrgParentQueryBusinessView;
import com.ce.scrm.center.service.config.UniqueIdService;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.router.RouterContext;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerPageThirdDto;
import com.ce.scrm.center.service.third.entity.view.BigDataCompanyDetailByNameView;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.BigDataThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.SmaConvertLogThirdService;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView;
import com.ce.scrm.extend.dubbo.entity.dto.BigDataCustomerFlagDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_BIGDATA_FLAG_TOPIC;
import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_CONTACTPERSON_TO_REDIS_TOPIC;

/**
 * @version 1.0
 * @Description: 收藏业务
 * @Author: lijinpeng
 * @Date: 2024/12/11 09:21
 */
@Service
@Slf4j
public class FavoriteBusiness {

    @DubboReference
    private ClueAssignAppService clueAssignAppService;

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private OrgInfoBusiness orgInfoBusiness;

    @Resource
    private EmployeeInfoBusiness employeeInfoBusiness;

    @DubboReference
    private ClueRuleAppService clueRuleAppService;

    @Resource
    private BigDataThirdService bigDataThirdService;

    @Resource
    private CmCustProtectService custProtectService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Autowired
    private UniqueIdService uniqueIdService;

    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;

    @Resource
    private RocketMqOperate rocketMqOperate;

    /*
     * @Description 获取收藏标识  1可以收藏 0不可以收藏
     * <AUTHOR>
     * @date 2024/12/11 09:32
     * @param uncid
     * @param customerName
     * @return java.lang.Integer
     */
    public Integer getFavoriteFlag(String uncid,String customerName,Integer myMarketFlag,String position) {

        //1、必须是我的市场
        if(Objects.equals(YesOrNoEnum.NO.getCode(),myMarketFlag) || myMarketFlag == null) {
            return YesOrNoEnum.NO.getCode();
        }

        //2、必须是商务角色
        if(!PositionUtil.checkSalerRole(position)) {
            return YesOrNoEnum.NO.getCode();
        }

        //3、保护状态必须是客户池 或者 为空
        CustProtectView cmCustProtect = cmCustProtectService.selectOneByCondition(
                CmCustProtect.builder()
                        .custName(customerName)
                        .build()
        );
        if(cmCustProtect != null && !ProtectStatusEnum.CUSTPOOL.getCode().equals(cmCustProtect.getStatus())) {
            return YesOrNoEnum.NO.getCode();
        }

        //4、收藏夹表不能有此名称
        ClueAssignVo clueAssignVo = clueAssignAppService.getOneByCustName(customerName);
        if(clueAssignVo != null) {
            return YesOrNoEnum.NO.getCode();
        }

        //5、收藏夹表不能有此uncid
        List<ClueAssignVo> byUncid = clueAssignAppService.getByUncid(uncid);
        if(CollectionUtil.isNotEmpty(byUncid)) {
            return YesOrNoEnum.NO.getCode();
        }

        return YesOrNoEnum.YES.getCode();
    }

    /*
     * @Description 添加收藏批量
     * <AUTHOR>
     * @date 2025/1/13 09:15
     * @param addFavoriteBatchBusinessDto
     * @return com.ce.scrm.center.service.business.entity.view.BatchResultBusinessView
     */
    public BatchResultBusinessView addFavoriteBatch(AddFavoriteBatchBusinessDto addFavoriteBatchBusinessDto) {

        log.info("addFavoriteBatch,入参为addFavoriteBatchBusinessDto={}", JSON.toJSONString(addFavoriteBatchBusinessDto));

        // 构建通用数据集
        AddFavoriteBatchBuildDataDto buildDataDto = buildAddFavoriteBatchData(addFavoriteBatchBusinessDto);

        List<String> pidList = addFavoriteBatchBusinessDto.getPidList();
        List<String> customerIdList = addFavoriteBatchBusinessDto.getCustomerIdList();

        int successCount = 0;
        int failCount = 0;
        List<String> fPidList = new ArrayList<>();
        List<String> successPidList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(customerIdList)) {
            for (String customerId : customerIdList) {

                Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(customerId);
                if (!customerData.isPresent()) {
                    continue;
                }
                CustomerDataThirdView customerDataThirdView = customerData.get();
                try {

                    // 库存判断
                    if(buildDataDto.getAvailableCapacity() != null) {
                        checkRepertoryCapacity(successCount,buildDataDto.getAvailableCapacity());
                    }

                    String companyName = customerDataThirdView.getCustomerName();
                    if(StringUtils.isBlank(companyName)) {
                        throw new ApiException(CodeMessageEnum.PARAM_EMPTY);
                    }
                    String otherParenthesisName = MyStringUtils.getOtherParenthesisName(companyName);
                    // 判断是否在线索库存在
                    ClueAssignVo custIdOrCustName = clueAssignAppService.getOneByCustIdOrCustNameOrUncId(customerId,companyName,otherParenthesisName,customerDataThirdView.getCertificateCode());
                    if(custIdOrCustName != null) {
                        throw new ApiException(CodeMessageEnum.CLUE_CUSTOMER_IS_EXIST);
                    }
                    // 判断是否在 保护表中存在 不等于4的数据
                    CmCustProtect cmCustProtectByName = custProtectService.lambdaQuery()
                            .ne(CmCustProtect::getStatus, ProtectStateEnum.CUSTOMER_POOL.getState())
                            .in(CmCustProtect::getCustName, ListUtil.of(companyName, otherParenthesisName))
                            .one();
                    if (cmCustProtectByName != null) {
                        throw new ApiException(CodeMessageEnum.PROTECT_CUSTOMER_IS_EXIST);
                    }

                    // 保存收藏夹
                    ClueAssignVo clueAssignSaveVo = BeanUtil.copyProperties(buildDataDto.getClueAssignSaveVo(), ClueAssignVo.class);
                    clueAssignSaveVo.setCustId(customerId);
                    clueAssignSaveVo.setCustName(companyName);
                    clueAssignSaveVo.setEntId(customerDataThirdView.getSourceDataId());
                    clueAssignSaveVo.setProvinceCode(customerDataThirdView.getProvinceCode());
                    clueAssignSaveVo.setCityCode(customerDataThirdView.getCityCode());
                    clueAssignSaveVo.setDistrictCode(customerDataThirdView.getDistrictCode());
                    clueAssignSaveVo.setUncid(customerDataThirdView.getCertificateCode());
                    clueAssignSaveVo.setAddress(customerDataThirdView.getRegisterAddress());
                    clueAssignAppService.save(clueAssignSaveVo);

                    // 保存流转日志
                    ConvertLogBusinessDto convertLogBusinessDto = BeanUtil.copyProperties(buildDataDto.getConvertLogBusinessDto(), ConvertLogBusinessDto.class);
                    convertLogBusinessDto.setCustId(customerId);
                    convertLogBusinessDto.setCustName(companyName);
                    smaConvertLogThirdService.insertLog(convertLogBusinessDto);

                    successCount++;
                    successPidList.add(customerDataThirdView.getSourceDataId());
                }catch (Exception e) {
                    if(e instanceof ApiException) {
                        CodeMessageEnum codeMessageEnum = ((ApiException) e).getApiResult();
                        if(CodeMessageEnum.INSUFFICIENT_STORAGE.equals(codeMessageEnum)) {
                            return BatchResultBusinessView.builder().successCount(successCount).failCount(pidList.size()-successCount).build();
                        }
                        if(CodeMessageEnum.PROTECT_CUSTOMER_IS_EXIST.equals(codeMessageEnum) || CodeMessageEnum.CLUE_CUSTOMER_IS_EXIST.equals(codeMessageEnum)) {
                            fPidList.add(customerDataThirdView.getSourceDataId());
                        }
                    }
                    failCount++;
                }
            }
        }

        if (CollectionUtils.isNotEmpty(pidList)) {
            for (String pid : pidList) {

                try {

                    // 库存判断
                    if(buildDataDto.getAvailableCapacity() != null) {
                        checkRepertoryCapacity(successCount,buildDataDto.getAvailableCapacity());
                    }

                    BigDataCompanyDetailByNameView companyDetailByPid = bigDataThirdService.getCompanyDetailByPid(pid);

                    // 省市区 code处理
                    String provinceCode = companyDetailByPid.getReg_province_code();
                    String cityCode  = companyDetailByPid.getReg_city_code();
                    String districtCode = companyDetailByPid.getReg_district_code();
                    String uncId = companyDetailByPid.getUncid();
                    String opLocation = companyDetailByPid.getOp_location();
                    if(StringUtils.isEmpty(cityCode)){
                        throw new ApiException(CodeMessageEnum.CITYCODE_NOT_EXIST);
                    }
                    // 兼容错误数据
                    if("810000".equals(provinceCode)){
                        cityCode = "810100";
                        districtCode = "810101";
                    }else if("820000".equals(provinceCode)){
                        cityCode = "820100";
                        districtCode = "820101";
                    }
                    if("441900".equals(cityCode)){//东莞
                        districtCode = "441901";
                    }else if("442000".equals(cityCode)){//中山
                        districtCode = "442001";
                    }

                    String companyName = companyDetailByPid.getEntname();
                    if(StringUtils.isBlank(companyName)) {
                        throw new ApiException(CodeMessageEnum.PARAM_EMPTY);
                    }
                    String otherParenthesisName = MyStringUtils.getOtherParenthesisName(companyName);
                    // 获取custId
                    String custId = getCustId(uncId, companyName);
                    // 判断是否在线索库存在
                    ClueAssignVo custIdOrCustName = clueAssignAppService.getOneByCustIdOrCustNameOrUncId(custId,companyName,otherParenthesisName,uncId);
                    if(custIdOrCustName != null) {
                        throw new ApiException(CodeMessageEnum.CLUE_CUSTOMER_IS_EXIST);
                    }
                    // 判断是否在 保护表中存在 不等于4的数据
                    CmCustProtect cmCustProtectByName = custProtectService.lambdaQuery()
                            .ne(CmCustProtect::getStatus, ProtectStateEnum.CUSTOMER_POOL.getState())
                            .in(CmCustProtect::getCustName, ListUtil.of(companyName, otherParenthesisName))
                            .one();
                    if (cmCustProtectByName != null) {
                        throw new ApiException(CodeMessageEnum.PROTECT_CUSTOMER_IS_EXIST);
                    }

                    // 保存收藏夹
                    ClueAssignVo clueAssignSaveVo = BeanUtil.copyProperties(buildDataDto.getClueAssignSaveVo(), ClueAssignVo.class);
                    clueAssignSaveVo.setCustId(custId);
                    clueAssignSaveVo.setCustName(companyName);
                    clueAssignSaveVo.setEntId(pid);
                    clueAssignSaveVo.setProvinceCode(provinceCode);
                    clueAssignSaveVo.setCityCode(cityCode);
                    clueAssignSaveVo.setDistrictCode(districtCode);
                    clueAssignSaveVo.setUncid(uncId);
                    if(opLocation != null) {
                        clueAssignSaveVo.setAddress(opLocation.length() >= 200 ? opLocation.substring(0, 199) : opLocation);
                    }
                    clueAssignAppService.save(clueAssignSaveVo);

                    // 保存流转日志
                    ConvertLogBusinessDto convertLogBusinessDto = BeanUtil.copyProperties(buildDataDto.getConvertLogBusinessDto(), ConvertLogBusinessDto.class);
                    convertLogBusinessDto.setCustId(custId);
                    convertLogBusinessDto.setCustName(companyName);
                    smaConvertLogThirdService.insertLog(convertLogBusinessDto);

                    successCount++;
                    successPidList.add(pid);
                }catch (Exception e) {
                    if(e instanceof ApiException) {
                        CodeMessageEnum codeMessageEnum = ((ApiException) e).getApiResult();
                        if(CodeMessageEnum.INSUFFICIENT_STORAGE.equals(codeMessageEnum)) {
                            return BatchResultBusinessView.builder().successCount(successCount).failCount(pidList.size()-successCount).build();
                        }
                        if(CodeMessageEnum.PROTECT_CUSTOMER_IS_EXIST.equals(codeMessageEnum) || CodeMessageEnum.CLUE_CUSTOMER_IS_EXIST.equals(codeMessageEnum)) {
                            fPidList.add(pid);
                        }
                    }
                    failCount++;
                }
            }
        }

        // 处理flag状态
        if (CollectionUtils.isNotEmpty(successPidList)) {
            // 将收藏成功的客户的flag1设置为收藏中
            try {
                rocketMqOperate.syncSend(CESUPPORT_SCRM_BIGDATA_FLAG_TOPIC,JSON.toJSONString(new BigDataCustomerFlagDto(successPidList, UserActionTypeEnum.FLAG_1.getId(), HighSearchProtectStatusEnum.COLLECTING.getValue() + "")));
            } catch (Exception e) {
                log.error("收藏线索客户时pId为：{}的数据给大数据发kafka消息是异常" , StringUtils.join(successPidList));
            }
            // 将收藏成功的客户的联系人放入缓存中
            try {
                rocketMqOperate.syncSend(CESUPPORT_SCRM_CONTACTPERSON_TO_REDIS_TOPIC, JSON.toJSONString(successPidList));
            } catch (Exception e) {
                log.error("收藏线索客户时pId为：{}的客户联系人放入redis异常" , StringUtils.join(successPidList));
            }
        }
        if (CollectionUtils.isNotEmpty(fPidList)) {
            // 将flag1设置为收藏中-保护过
            try {
                rocketMqOperate.syncSend(CESUPPORT_SCRM_BIGDATA_FLAG_TOPIC, JSON.toJSONString(new BigDataCustomerFlagDto(fPidList, UserActionTypeEnum.FLAG_1.getId(), HighSearchProtectStatusEnum.COLLECTING_PROTECTED.getValue() + "")));
            } catch (Exception e) {
                log.error("收藏线索客户时pId为：{}的数据给大数据发kafka消息是异常", StringUtils.join(fPidList));
            }
            // 将flag5置空
            try {
                rocketMqOperate.syncSend(CESUPPORT_SCRM_BIGDATA_FLAG_TOPIC, JSON.toJSONString(new BigDataCustomerFlagDto(fPidList, UserActionTypeEnum.FLAG_5.getId(), null)));
            } catch (Exception e) {
                log.error("收藏线索客户时pId为：{}的数据给大数据发kafka消息是异常", StringUtils.join(fPidList));
            }
        }

        return BatchResultBusinessView.builder().successCount(successCount).failCount(failCount).build();
    }

    private void checkRepertoryCapacity(int successCount, Integer availableCapacity) {
        if (successCount > availableCapacity) {
            throw new ApiException(CodeMessageEnum.INSUFFICIENT_STORAGE);
        }
    }

    private String getCustId(String uncId, String companyName) {
        DubboPageInfo<CustomerDubboView> customerDubboViewDubboPageInfo = customerThirdService.pageList(CustomerPageThirdDto.builder().orCertificateCode(uncId).customerName(companyName).build());
        if (customerDubboViewDubboPageInfo == null || CollectionUtil.isEmpty(customerDubboViewDubboPageInfo.getList())) {
            // 发号器
            return uniqueIdService.getId();
        }else {
            for (CustomerDubboView customerDubboView : customerDubboViewDubboPageInfo.getList()) {
                if(Objects.equals(customerDubboView.getCustomerName(), companyName)) {
                    return customerDubboView.getCustomerId();
                }
            }
            return customerDubboViewDubboPageInfo.getList().get(0).getCustomerId();
        }
    }

    private AddFavoriteBatchBuildDataDto buildAddFavoriteBatchData(AddFavoriteBatchBusinessDto addFavoriteBatchBusinessDto) {

        AddFavoriteBatchBuildDataDto  result = new AddFavoriteBatchBuildDataDto();

        EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
        if(currentUser == null) {
            throw new ApiException(CodeMessageEnum.NOT_LOGING_USER);
        }

        // 商务角色 查客户的时候批量收藏的情况下 是没有分配的商务id的
        String assignToSalerId = PositionUtil.isBusinessSaler(currentUser.getPosition()) ?
                currentUser.getId() : addFavoriteBatchBusinessDto.getAssignToSalerId();
        String assignToDeptId = addFavoriteBatchBusinessDto.getAssignToDeptId();
        String assignToBuId = addFavoriteBatchBusinessDto.getAssignToBuId();
        String assignToSubId = addFavoriteBatchBusinessDto.getAssignToSubId();
        String assignToAreaId = null;

        Date currentDate = new Date();

        ClueAssignVo clueAssignSaveVo = new ClueAssignVo();
        String clueRuleType = null;
        if (StringUtils.isNotBlank(assignToSalerId)) { //分配给商务

            // 根据商务id查询人员信息
            EmployeeInfoBusinessDto employeeInfoBusinessDto = employeeInfoBusiness.getEmployeeCheckStateByEmpId(assignToSalerId);
            if(employeeInfoBusinessDto == null) {
                throw new ApiException(CodeMessageEnum.NOT_USER);
            }

            //分配给商务  需要判断商务的容量是否够用
            // 查询销售已用的库容数
            ClueAssignVo vo = new ClueAssignVo();
            vo.setEmpId(assignToSalerId);
            vo.setStatus(ClueCustStatusEnum.SALER.getValue());
            int salerHadClueNum = clueAssignAppService.getCount(vo);
            // 查询系统分配的容量
            ClueRuleVo clueRuleVo = new ClueRuleVo();
            clueRuleVo.setSubId(employeeInfoBusinessDto.getSubId());
            clueRuleVo.setTypeCode(ClueRuleEnum.CLUE_SALERMAXNUM.getValue());
            clueRuleVo.setJobGrade(employeeInfoBusinessDto.getJobGrade());
            ClueRuleVo ruleVo = clueRuleAppService.getOneByClueRule(clueRuleVo);
            if(ruleVo == null) {
                throw new ApiException(CodeMessageEnum.SALER_REPERTORY_NOT_EXIST);
            }
            result.setAvailableCapacity(ruleVo.getTypeValue() - salerHadClueNum);

            clueRuleType = ClueRuleEnum.SALEREXCEED.getValue();
            clueAssignSaveVo.setStatus(ClueCustStatusEnum.SALER.getValue());

            assignToSalerId = employeeInfoBusinessDto.getId();
            assignToDeptId = employeeInfoBusinessDto.getOrgId();
            assignToBuId = employeeInfoBusinessDto.getBuId();
            assignToSubId = employeeInfoBusinessDto.getSubId();
            assignToAreaId = employeeInfoBusinessDto.getAreaId();
        } else if (StringUtils.isNotBlank(assignToDeptId)) {

            OrgParentQueryBusinessView parentOrgByOrgId = orgInfoBusiness.getParentOrgByOrgId(OrgParentQueryBusinessDto.builder().orgId(assignToDeptId).build());

            clueAssignSaveVo.setStatus(ClueCustStatusEnum.MANAGER.getValue());
            clueRuleType = ClueRuleEnum.MANAGEREXCEED.getValue();

            assignToSalerId = null;
            assignToDeptId = parentOrgByOrgId.getDeptId();
            assignToBuId = parentOrgByOrgId.getBuId();
            assignToSubId = parentOrgByOrgId.getSubId();
            assignToAreaId = parentOrgByOrgId.getAreaId();

        } else if (StringUtils.isNotBlank(assignToBuId)) {

            OrgParentQueryBusinessView parentOrgByOrgId = orgInfoBusiness.getParentOrgByOrgId(OrgParentQueryBusinessDto.builder().orgId(assignToBuId).build());

            clueAssignSaveVo.setStatus(ClueCustStatusEnum.BU.getValue());
            clueRuleType = ClueRuleEnum.MANAGEREXCEED.getValue();

            assignToSalerId = null;
            assignToDeptId = null;
            assignToBuId = parentOrgByOrgId.getBuId();
            assignToSubId = parentOrgByOrgId.getSubId();
            assignToAreaId = parentOrgByOrgId.getAreaId();

        } else if (StringUtils.isNotBlank(assignToSubId)) {

            OrgParentQueryBusinessView parentOrgByOrgId = orgInfoBusiness.getParentOrgByOrgId(OrgParentQueryBusinessDto.builder().orgId(assignToSubId).build());

            clueAssignSaveVo.setStatus(ClueCustStatusEnum.MAJORDOMO.getValue());
            clueRuleType = ClueRuleEnum.MAJOREXCEED.getValue();

            assignToSalerId = null;
            assignToDeptId = null;
            assignToBuId = null;
            assignToSubId = parentOrgByOrgId.getSubId();
            assignToAreaId = parentOrgByOrgId.getAreaId();
        } else {
            throw new ApiException(CodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }

        // convertLogType 字段
        Integer convertLogType = null;
        if(PositionUtil.isBusinessMajor(currentUser.getPosition())) {// 总监
            if(StringUtils.isNotBlank(assignToSalerId)) {
                convertLogType = ConvertRelationEnum.CLUECUST_MAJOR_ASSIGN_HAND_TO_SALER.getValue();
            }else {
                convertLogType = ConvertRelationEnum.CLUECUST_MAJOR_ASSIGN_HAND_TO_DEPT.getValue();
            }
        }else if (PositionUtil.isBusinessBu(currentUser.getPosition())) { // 事业部
//                convertLogType = ConvertRelationEnum.CLUECUST_MANAGER_ASSIGN_SYS_TO_SALER.getValue();
        }else if (PositionUtil.checkIsManagerOrSupervisor(currentUser.getPosition())) { // 经理
            convertLogType = ConvertRelationEnum.CLUECUST_MANAGER_ASSIGN_SYS_TO_SALER.getValue();
        }else if (PositionUtil.checkSalerRole(currentUser.getPosition())) { // 商务代表
            convertLogType = ConvertRelationEnum.SYSCLUECUST_SALER_ASSIGN.getValue();
        }

        //线索超期时间
        ClueRuleVo clueRuleVo = new ClueRuleVo();
        clueRuleVo.setSubId(assignToSubId);
        clueRuleVo.setTypeCode(clueRuleType);
        ClueRuleVo ruleVo = clueRuleAppService.getOneByClueRule(clueRuleVo);
        if(ruleVo == null) {
            throw new ApiException(CodeMessageEnum.DATA_NOT_EXIST);
        }
        Date exceedTime = DateUtil.getDateAddDate(new Date(), 5, ruleVo.getTypeValue() / 24);

        // 线索数据build
        clueAssignSaveVo.setEmpId(assignToSalerId);
        clueAssignSaveVo.setDeptId(assignToDeptId);
        clueAssignSaveVo.setBuId(assignToBuId);
        clueAssignSaveVo.setSubCompanyId(assignToSubId);
        clueAssignSaveVo.setAreaId(assignToAreaId);
        clueAssignSaveVo.setExceedTime(exceedTime);
        clueAssignSaveVo.setCreateTime(currentDate);
        clueAssignSaveVo.setCreateUser(currentUser.getId());
        clueAssignSaveVo.setIsReaded(IsReadEnum.NOREADED.getValue());
        clueAssignSaveVo.setSalerGetclueFrom(SalerGetclueFromEnum.SKB_PULL.getValue());
        clueAssignSaveVo.setClueVisitStage("CLUEVISITSTAGE_WCL");
        clueAssignSaveVo.setOptType(convertLogType);

        // 流转日志build
        ConvertLogBusinessDto convertLogBusinessDto = ConvertLogBusinessDto.builder()
                .curSalerId(assignToSalerId)
                .deptOfCurSalerId(assignToDeptId)
                .buOfCurSalerId(assignToBuId)
                .subcompanyOfCurSalerId(assignToSubId)
                .areaOfCurSalerId(assignToAreaId)
                .createTime(currentDate)
                .createBy(currentUser.getId())
                .convertType(convertLogType)
                .build();

        result.setAssignToSalerId(assignToSalerId);
        result.setAssignToDeptId(assignToDeptId);
        result.setAssignToBuId(assignToBuId);
        result.setAssignToSubId(assignToSubId);
        result.setAssignToAreaId(assignToAreaId);
        result.setClueAssignSaveVo(clueAssignSaveVo);
        result.setConvertLogBusinessDto(convertLogBusinessDto);
        return result;
    }


}
