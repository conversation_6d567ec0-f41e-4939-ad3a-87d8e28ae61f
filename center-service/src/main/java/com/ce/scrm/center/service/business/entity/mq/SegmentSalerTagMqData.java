package com.ce.scrm.center.service.business.entity.mq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: cdp分群下发：商务打标
 * @Author: lijinpeng
 * @Date: 2025/3/6 11:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SegmentSalerTagMqData implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 分群id
     */
    private String zqSegmentSubjectId;

    /**
     * 分群名称
     */
    private String zqSegmentSubjectName;

    private String salerId;

    private String salerName;

    private String salerDeptId;

    private String salerDeptName;

    private String salerBuId;

    private String salerBuName;

    private String salerSubId;

    private String salerSubName;

    private String salerAreaId;

    private String salerAreaName;

    /**
     * 保护状态
     */
    private String protectStatus;

    /**
     * 成交状态
     */
    private String dealStatus;

    /**
     * 处理结果
     */
    private String handleResult;

    /**
     * 处理时间
     */
    private Date handleTime;

}
