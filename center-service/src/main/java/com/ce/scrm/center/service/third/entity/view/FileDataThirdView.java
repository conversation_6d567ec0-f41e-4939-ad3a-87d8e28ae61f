package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.util.Date;

/**
 * 文件数据
 * <AUTHOR>
 * @date 2024/5/21 上午9:28
 * @version 1.0.0
 */
@Data
public class FileDataThirdView {
    /**
     * 文件ID
     */
    private String id;
    /**
     * 文件路径
     */
    private String path;
    /**
     * 文件地址
     */
    private String fileUrl;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件大小
     */
    private Float size;
    /**
     * 文件类型
     */
    private String type;
    /**
     * 文件上传日期
     */
    private Date upDate;
    /**
     * 文件上传人
     */
    private String upPerson;
    /**
     * 系统标记
     */
    private String sysFlag;
    /**
     * 桶名称
     */
    private String bucketName;
    /**
     * 文件密钥
     */
    private String fileKey;
}