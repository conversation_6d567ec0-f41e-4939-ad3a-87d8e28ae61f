package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * description: 流失客户记录表
 * @author: DD.Jiu
 * date: 2024/10/21.
 */
@Data
public class CustomerLossLogBusinessDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 流失原因
     */
    private String reason;

    /**
     * 预计流转（流失）日期
     */
    private Date preDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
