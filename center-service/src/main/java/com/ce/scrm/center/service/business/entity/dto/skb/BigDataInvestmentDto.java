package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 对外投资
 * @author: JiuDD
 * date: 2025/2/13
 */
@Data
public class BigDataInvestmentDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**总数量*/
    private int total_count;
    /**总页数*/
    private int total_page;
    /**当前页*/
    private int  page;

    @JSONField(name = "对外投资企业列表")
    private List<ListInvestment> list;

    @Data
    public static class ListInvestment implements Serializable {
        private static final long serialVersionUID = 1L;
        /**企业名称*/
        @JSONField(name = "投资的企业名称")
        private String ent_name;
        /**统一信用代码*/
        @JSONField(serialize = false)
        private String uncid;
        /**企业状态*/
        @JSONField(name = "企业状态")
        private String ent_status;
        /**公司法人*/
        @JSONField(name = "公司法人",serialize = false)
        private String legal_person;
        /**成立日期(毫秒，如果为0表示数据为空)*/
        @JSONField(name = "成立日期",serialize = false)
        private Long es_date;
        /**注册资金*/
        @JSONField(name = "注册资金",serialize = false)
        private Integer reg_cap;
        /**注册资金单位*/
        @JSONField(name = "注册资金单位",serialize = false)
        private String reg_cap_cur;
        /**持股比例*/
        @JSONField(serialize = false)
        private String in_sto;
        /**认缴额 默认单位：万元*/
        @JSONField(serialize = false)
        private String lis_sub_con_am;
        /**是否是历史投资,是否退出 0:否,1:是*/
        @JSONField(serialize = false)
        private Integer is_history;
        /**退出时间*/
        @JSONField(serialize = false)
        private Long outgoing_date;
        /**股东名称*/
        @JSONField(serialize = false)
        private String inv_name;
    }
}
