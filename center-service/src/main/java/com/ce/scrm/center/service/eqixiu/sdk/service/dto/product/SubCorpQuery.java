/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.product;

import com.ce.scrm.center.service.eqixiu.sdk.common.ProductParam;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.CorpAccountType;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.CorpStatus;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平台子企业查询参数
 */
public class SubCorpQuery extends ProductParam {

    private int pageNo = 1;
    private int pageSize = 10;

    /**
     * 指定企业名称
     */
    private String name;

    /**
     * 企业状态
     */
    private CorpStatus status;

    /**
     * 企业账号类型
     */
    private CorpAccountType accountType;

    @Override
    protected Map<String, String> getParams() {
        Map<String, String> params = new HashMap<>();
        params.put("pageNo", String.valueOf(pageNo));
        params.put("pageSize", String.valueOf(pageSize));
        if (name != null) {
            params.put("name", name);
        }
        if (status != null) {
            params.put("status", String.valueOf(status.getValue()));
        }
        if (accountType != null) {
            params.put("accountType", String.valueOf(accountType.getValue()));
        }
        return params;
    }

    @Override
    protected void validateParam() {

    }

    @Override
    public List<String> getSignatureParams() {
        List<String> params = new ArrayList<>();
        params.add(String.valueOf(getAppId()));
        params.add(getTimestamp());
        if (StrUtil.isNotEmpty(getOpenId())) {
            params.add(getOpenId());
        }
        if (StrUtil.isNotEmpty(name)) {
            params.add(name);
        }
        if (status != null) {
            params.add(String.valueOf(status.getValue()));
        }
        if (accountType != null) {
            params.add(String.valueOf(accountType.getValue()));
        }
        return params;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public CorpAccountType getAccountType() {
        return accountType;
    }

    public void setAccountType(CorpAccountType accountType) {
        this.accountType = accountType;
    }

    public CorpStatus getStatus() {
        return status;
    }

    public void setStatus(CorpStatus status) {
        this.status = status;
    }
}
