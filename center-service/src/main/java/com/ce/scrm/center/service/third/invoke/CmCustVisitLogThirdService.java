package com.ce.scrm.center.service.third.invoke;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.third.entity.dto.QueryVisitLogOneThirdDto;
import com.ce.scrm.center.service.third.entity.view.CmCustVisitLogThirdView;
import com.ce.scrm.extend.dubbo.api.CmCustVisitLogDubboService;
import com.ce.scrm.extend.dubbo.entity.request.CmCustVisitLogReq;
import com.ce.scrm.extend.dubbo.entity.response.CmCustVisitLogRes;
import com.ce.scrm.extend.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @version 1.0
 * @Description: 客户拜访和电联记录
 * @Author: lijinpeng
 * @Date: 2025/2/17 15:56
 */
@Slf4j
@Service
public class CmCustVisitLogThirdService {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private CmCustVisitLogDubboService cmCustVisitLogDubboService;

    /*
     * @Description 根据打卡记录id集合查询拜访记录集合
     * <AUTHOR>
     * @date 2025/2/17 16:03
     * @param idList
     * @return java.util.List<com.ce.scrm.center.service.third.entity.view.CmCustVisitLogThirdView>
     */
    public List<CmCustVisitLogThirdView> getVisitLogsBySiteClockRecordIdList(List<String> idList) {

        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }

        DubboResult<List<CmCustVisitLogRes>> dubboResult = cmCustVisitLogDubboService.getVisitLogsBySiteClockRecordIdList(idList);

        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            log.error("getVisitLogsBySiteClockRecordIdList，idList={}", idList);
            return Collections.emptyList();
        }

        return BeanUtil.copyToList(dubboResult.getData(), CmCustVisitLogThirdView.class);

    }


    public List<CmCustVisitLogThirdView> getVisitLogsBySiteClockRecordIdListByCustId(String custId) {
        CmCustVisitLogReq custVisitLogReq = new CmCustVisitLogReq();
        custVisitLogReq.setCustId(custId);
        DubboResult<List<CmCustVisitLogRes>> dubboResult = cmCustVisitLogDubboService.getVisitedCust(custVisitLogReq, 1, 100);

        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            log.error("getVisitLogsBySiteClockRecordIdList，custId={}", custId);
            return Collections.emptyList();
        }

        return BeanUtil.copyToList(dubboResult.getData(), CmCustVisitLogThirdView.class);

    }

    /**
     *  根据 客户id 和 商务id查询最新的一条跟进记录
     * @param queryDto
     * @return
     */
    public CmCustVisitLogThirdView queryVisitLogOne(QueryVisitLogOneThirdDto queryDto) {
        log.info("queryVisitLog queryDto={}", queryDto);
        CmCustVisitLogReq req = BeanUtil.copyProperties(queryDto, CmCustVisitLogReq.class);
        DubboPageInfo<CmCustVisitLogRes> dubboResult = cmCustVisitLogDubboService.queryVisitLogsPage(req, 1, 1);
        if (CollectionUtils.isEmpty(dubboResult.getList())) {
            return null;
        }else {
            CmCustVisitLogRes cmCustVisitLogRes = dubboResult.getList().get(0);
            CmCustVisitLogThirdView cmCustVisitLogThirdView = BeanUtil.copyProperties(cmCustVisitLogRes, CmCustVisitLogThirdView.class);
            return cmCustVisitLogThirdView;
        }
    }


    public String create(CmCustVisitLogReq cmCustVisitLogReq) {
        log.info("cm_cust_visit_log save param={}", JSONObject.toJSONString(cmCustVisitLogReq));
        CmCustVisitLogReq save = cmCustVisitLogDubboService.save(cmCustVisitLogReq);
        return save.getId();
    }
}
