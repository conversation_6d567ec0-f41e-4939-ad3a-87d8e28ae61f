package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.ce.cloud.common.exception.BusinessException;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.ZqCustomerFollowStageSummary;
import com.ce.scrm.center.dao.entity.ZqPerformancePredictSummary;
import com.ce.scrm.center.dao.service.ZqCustomerFollowStageSummaryService;
import com.ce.scrm.center.dao.service.ZqPerformancePredictSummaryService;
import com.ce.scrm.center.service.business.entity.dto.BusinessMajorBoardBusinessDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@Slf4j
public class BusinessMajorBoardBusiness {

    @Resource
    private ZqCustomerFollowStageSummaryService zqCustomerFollowStageSummaryService;

    @Resource
    private ZqPerformancePredictSummaryService zqPerformancePredictSummaryService;

    public Page<ZqPerformancePredictSummary> board(BusinessMajorBoardBusinessDto queryDto) {


        LambdaQueryChainWrapper<ZqPerformancePredictSummary> queryChainWrapper = zqPerformancePredictSummaryService.lambdaQuery();

        if (Objects.equals(queryDto.getLoginPosition(),"系统管理员")) {
            queryChainWrapper.eq(StringUtils.isNotBlank(queryDto.getAreaId()), ZqPerformancePredictSummary::getAreaId, queryDto.getAreaId());
            queryChainWrapper.eq(StringUtils.isNotBlank(queryDto.getSubId()), ZqPerformancePredictSummary::getSubId, queryDto.getSubId());
        } else if (PositionUtil.isBusinessArea(queryDto.getLoginPosition())) {
            queryChainWrapper.eq(ZqPerformancePredictSummary::getAreaId, queryDto.getLoginAreaId());
            queryChainWrapper.eq(StringUtils.isNotBlank(queryDto.getSubId()), ZqPerformancePredictSummary::getSubId, queryDto.getSubId());
        } else if (PositionUtil.isBusinessMajor(queryDto.getLoginPosition())) {
            queryChainWrapper.eq(ZqPerformancePredictSummary::getAreaId, queryDto.getLoginAreaId());
            queryChainWrapper.eq(ZqPerformancePredictSummary::getSubId, queryDto.getLoginSubId());
        } else {
            throw new ApiException(CodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }

        queryChainWrapper.eq(StringUtils.isNotBlank(queryDto.getBusinessMonth()), ZqPerformancePredictSummary::getBusinessMonth, queryDto.getBusinessMonth());
        queryChainWrapper.eq(ZqPerformancePredictSummary::getOrgType,2);
//        queryChainWrapper.eq()
        



        Page<ZqPerformancePredictSummary> page = Page.of(queryDto.getPageNum(), queryDto.getPageSize());
        return queryChainWrapper.page(page);
    }

    public Page<ZqCustomerFollowStageSummary> stageBoard(BusinessMajorBoardBusinessDto queryDto) {

        LambdaQueryChainWrapper<ZqCustomerFollowStageSummary> queryChainWrapper = zqCustomerFollowStageSummaryService.lambdaQuery();

        if (Objects.equals(queryDto.getLoginPosition(),"系统管理员")) {
            queryChainWrapper.eq(StringUtils.isNotBlank(queryDto.getAreaId()), ZqCustomerFollowStageSummary::getAreaId, queryDto.getAreaId());
            queryChainWrapper.eq(StringUtils.isNotBlank(queryDto.getSubId()), ZqCustomerFollowStageSummary::getSubId, queryDto.getSubId());
        } else if (PositionUtil.isBusinessArea(queryDto.getLoginPosition())) {
            queryChainWrapper.eq(ZqCustomerFollowStageSummary::getAreaId, queryDto.getLoginAreaId());
            queryChainWrapper.eq(StringUtils.isNotBlank(queryDto.getSubId()), ZqCustomerFollowStageSummary::getSubId, queryDto.getSubId());
        } else if (PositionUtil.isBusinessMajor(queryDto.getLoginPosition())) {
            queryChainWrapper.eq(ZqCustomerFollowStageSummary::getAreaId, queryDto.getLoginAreaId());
            queryChainWrapper.eq(ZqCustomerFollowStageSummary::getSubId, queryDto.getLoginSubId());
        } else {
            throw new ApiException(CodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }

        queryChainWrapper.eq(StringUtils.isNotBlank(queryDto.getBusinessMonth()), ZqCustomerFollowStageSummary::getBusinessMonth, queryDto.getBusinessMonth());
        queryChainWrapper.eq(ZqCustomerFollowStageSummary::getOrgType,2);
//        queryChainWrapper.eq()




        Page<ZqCustomerFollowStageSummary> page = Page.of(queryDto.getPageNum(), queryDto.getPageSize());
        return queryChainWrapper.page(page);
    }
}
