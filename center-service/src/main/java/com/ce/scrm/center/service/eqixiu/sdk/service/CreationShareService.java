package com.ce.scrm.center.service.eqixiu.sdk.service;

import com.ce.scrm.center.service.eqixiu.sdk.domain.CreationShare;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.share.CreationShareCmd;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.share.CreationShareQuery;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.share.DelCreationShareCmd;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.share.ModifyCreationShareCmd;
import com.ce.scrm.center.service.eqixiu.sdk.support.HttpClient;
import com.ce.scrm.center.service.eqixiu.sdk.support.TokenCache;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;

import java.util.List;


/**
 * 作品共享接口
 * 添加共享成员后，默认作品就共享给这个成员，拥有查看作品数据的权限。
 * 删除共享数据时会删除对应的分发数据。删除分发数据时不会删除对应的共享数据权限。
 *
 * <AUTHOR>
 */
public class CreationShareService extends ConnectService {

    public static final String API_CREATION_SHARE_SAVE = "/api/v1/editor/creation/share/save";
    public static final String API_CREATION_SHARE_LIST = "/api/v1/editor/creation/share/list";
    public static final String API_CREATION_SHARE_UPDATE = "/api/v1/editor/creation/share/update";
    public static final String API_CREATION_SHARE_DELETE = "/api/v1/editor/creation/share/delete";

    public CreationShareService(Secret secret) {
        super(secret);
    }

    public CreationShareService(Secret secret, TokenCache tokenCache) {
        super(secret, tokenCache);
    }

    public CreationShareService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        super(secret, tokenCache, httpClient);
    }

    /**
     * 添加作品共享
     *
     * @param cmd
     * @return
     */
    public boolean saveShareCreation(CreationShareCmd cmd) {
        paramValidate(cmd);
        JSONObject object = httpClient.httpPostObj(getApiURL(API_CREATION_SHARE_SAVE, cmd.getParamsMap()), cmd);
        printLog(object, "添加作品共享失败:{}");
        return object.getSuccess();

    }

    /**
     * 查询作品共享
     *
     * @param query
     * @return
     */
    public List<CreationShare> listCreationShare(CreationShareQuery query) {
        paramValidate(query);
        JSONObject object = httpClient.httpPostObj(getApiURL(API_CREATION_SHARE_LIST, query.getParamsMap()), query);
        printLog(object, "查询作品共享失败:{}");
        return object.getList(CreationShare.class);
    }

    /**
     * 修改共享权限
     *
     * @param cmd
     * @return
     */
    public boolean modifyCreationShare(ModifyCreationShareCmd cmd) {
        paramValidate(cmd);
        JSONObject object = httpClient.httpPostObj(getApiURL(API_CREATION_SHARE_UPDATE, cmd.getParamsMap()), cmd);
        printLog(object, "修改共享权限失败:{}");
        return object.getSuccess();
    }

    /**
     * 删除作品共享
     *
     * @param cmd
     * @return
     */
    public boolean deleteCreationShare(DelCreationShareCmd cmd) {
        paramValidate(cmd);
        JSONObject object = httpClient.httpPostObj(getApiURL(API_CREATION_SHARE_DELETE, cmd.getParamsMap()), cmd.getShareList());
        printLog(object, "删除共享数据失败:{}");
        return object.getSuccess();
    }


}
