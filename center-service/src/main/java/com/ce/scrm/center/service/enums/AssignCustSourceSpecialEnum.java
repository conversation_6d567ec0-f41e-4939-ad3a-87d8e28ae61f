package com.ce.scrm.center.service.enums;

import cn.ce.cesupport.framework.base.enums.BaseCustomEnum;

/**
 * Description: 客户进待分配表的来源
 * 				只摘取了源枚举类{@link cn.ce.cesupport.sma.enums.AssignCustSourceEnum}的部分枚举值
 * @author: JiuDD
 * date: 2024/7/19
 */
public enum AssignCustSourceSpecialEnum implements BaseCustomEnum<Integer> {
	/** 流转客户 */
	CIRCULATION("流转客户", 16),
	/** 流失客户 */
	LOSS("流失客户（首次分配）", 17),
	SJ_ANEW_ASSIGN("商机重新分配", 18),
	;

	private String lable;
	private Integer value;

	AssignCustSourceSpecialEnum(String lable, Integer value) {
		this.lable = lable;
		this.value = value;
	}

	public String getLable() {
		return lable;
	}

	public void setLable(String lable) {
		this.lable = lable;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	public static AssignCustSourceSpecialEnum getAssignCustSourceEnumByValue(Integer value) {
		AssignCustSourceSpecialEnum[] enums = AssignCustSourceSpecialEnum.values();
		for (AssignCustSourceSpecialEnum assignCustSourceEnum : enums) {
			if (assignCustSourceEnum.getValue().intValue() == value.intValue()) {
				return assignCustSourceEnum;
			}
		}
		return null;
	}

	public String getDesc() {
		return "";
	}
}
