package com.ce.scrm.center.service.business.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CdpSegmentDistributeMqData implements Serializable {

    /**
     * 分群id
     */
    private String segmentId;

    /**
     * 分配类型 0 系统分 1手动 2按照组织
     */
    private Integer assignmentType;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 下发时间
     */
    private Date distributeTime;

    /**
     * 接受商务id receivedType = 1
     */
    private String receivedId;

    /**
     * 接受组织id receivedType in 2、3、4
     */
    private String receivedOrgId;

    /**
     * 1、商务  2、总监 3、事业部 4、部门 5、区域
     */
    private Integer receivedType;

    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;

    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;

    /**
     * 事业部ID
     */
    private String loginBuId;
    /**
     * 事业部名称
     */
    private String loginBuName;
    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;

    /**
     * 角色
     */
    private String loginPosition;

}
