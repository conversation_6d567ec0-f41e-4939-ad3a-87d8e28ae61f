package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Description: 市场部人员组织
 * @author: JiuDD
 * date: 2024/11/5 10:01
 */
@Data
@Accessors(chain = true)
public class EmployeeIntentClueWhiteListBusinessDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;
}