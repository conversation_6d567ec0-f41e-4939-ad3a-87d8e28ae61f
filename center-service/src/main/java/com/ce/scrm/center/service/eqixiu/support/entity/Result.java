package com.ce.scrm.center.service.eqixiu.support.entity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 结果包装类，用于跨层或跨系统交互
 *
 * @param
 * <AUTHOR> (<EMAIL>)
 */
public class Result implements java.io.Serializable {

    /**
     * 调用结果
     */
    private boolean success;

    /**
     * 错误或成功代码
     */
    private String code;

    /**
     * 服务器端返回消息
     */
    private String msg;

    /**
     * 数据对象
     */
    private Object obj;

    private Map<String, Object> map;

    /**
     * 集合数据对象
     */
    private List list;

    private Object[] messageArgs;

    private Result() {
        //处理语言环境
    }

    public boolean success() {
        return success;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getCode() {
        return code;
    }


    public String getMsg() {
        //处理通配 TODO
        return this.msg;
    }

    public Object getObj() {
        return obj;
    }

    public Result setObj(Object data) {
        this.obj = data;
        return this;
    }

    public List getList() {
        return list;
    }

    public Result setList(List list) {
        this.list = list;
        return this;
    }

    public Map getMap() {
        return map;
    }

    public Result setMap(Map<String, Object> map) {
        this.map = map;
        return this;
    }

    public Result addAttribute(String key, Object value) {
        if (this.map == null) {
            this.map = new HashMap<>();
        }
        this.map.put(key, value);
        return this;
    }


    public Result setPage(List pageList, int pageNo, int pageSize, long totalCount) {
        this.list = pageList;
        if (this.map == null) {
            this.map = new HashMap<>();
        }
        this.map.put("pageNo", pageNo);
        this.map.put("pageSize", pageSize);
        this.map.put("count", totalCount);
        boolean end = (long) pageSize * pageNo >= totalCount;
        this.map.put("end", end);
        return this;
    }

    public static Result ofSuccess() {
        return new Result().setSuccess(true).setCode("200");
    }
    public static Result ofError() {
        return new Result().setSuccess(false).setCode("400");
    }

    private Result setSuccess(boolean success) {
        this.success = success;
        return this;
    }

    private Result setMsg(String msg) {
        this.msg = msg;
        return this;
    }


    private Result setCode(String code) {
        this.code = code;
        return this;
    }

    private Result setMessageArgs(Object[] messageArgs) {
        this.messageArgs = messageArgs;
        return this;
    }
}
