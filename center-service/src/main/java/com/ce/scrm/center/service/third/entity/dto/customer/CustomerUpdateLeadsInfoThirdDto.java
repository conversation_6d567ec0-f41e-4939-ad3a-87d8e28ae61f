package com.ce.scrm.center.service.third.entity.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2024/12/9 11:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerUpdateLeadsInfoThirdDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * leadsCode
	 */
	private String leadsCode;

	/**
	 * 意向类型, 也叫等级code A、B、C、D
	 */
	private String leadsIntentCode;

	/**
	 * 客户id
	 */
	private String customerId;
}
