package com.ce.scrm.center.service.business.entity.dto.segment;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @version 1.0
 * @Description: 根据角色查询分群入参条件
 * @Author: lijinpeng
 * @Date: 2025/2/27 14:24
 */
@Data
public class QueryRoleSegmentBusinessDto implements Serializable {

    /**
     * 分群id
     */
    @NotNull
    private String segmentId;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 事业部id
     */
    private String buId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 处理情况标注
     */
    private String salerCustomTag;

    /**
     * 客户状态
     */
    private String customerStatus;

    /**
     * 筛选项 0否 1是
     */
    private Integer visitFlag;

    /**
     * 城市筛选项list
     */
    private List<String> cityCodeList;


    /**
     * 一级国标行业编码
     */
    private List<String> firstIndustryCodeList;

    /**
     * 二级国标行业编码
     */
    private List<String> secondIndustryCodeList;

    /**
     * 三级国标行业编码
     */
    private List<String> thirdIndustryCodeList;

    /**
     * 四级国标行业编码
     */
    private List<String> fourthIndustryCodeList;

    /**
     * 注册资本范围 小
     */
    private BigDecimal registerCapitalMin;

    /**
     * 注册资本范围 大
     */
    private BigDecimal registerCapitalMax;

    /**
     * 有无域名备案 1:是 0:否
     */
    private Integer icpFlag;

    /**
     * 有无进出口信用 1:是 0:否
     */
    private Integer jingchukouFlag;

    /**
     * 模糊查询备注
     */
    private String likeRemark;

    /**
     * 页码
     * 默认 1
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     * 默认 10
     */
    private Integer pageSize = 10;

    //----------------以下  登录人信息

    /**
     * 登录人id
     */
    private String loginEmployeeId;

    /**
     * 登录人分司ID
     */
    private String loginSubId;

    /**
     * 登录人事业部id
     */
    private String loginBuId;

    /**
     * 部门ID
     */
    private String loginOrgId;

    /**
     * 登录人职位
     */
    private String loginPosition;

}
