package com.ce.scrm.center.service.eqixiu.sdk.domain;

public class Corp {

    private String id;           // 企业ID，易企秀内部id
    private String openId;       // 企业openId
    private String name;         // 企业名称
    private int staffCount;      // 员工账号个数
    private int creationCount;   // 企业作品数量
    private int pvCount;         // 企业消耗流量
    private int pvTotal;         // 企业总流量
    private String pvLimit;         // pv限制值
    private int pvAvailable;     // 可用pv数量
    private String createTime;      // 开通时间
    private String expireTime;      // 到期时间
    private int smsCount;        // 短信消耗数量
    private int status;          // 企业状态，0停用，1正常，2关闭，3已过期
    private int accountType;


    public int getAccountType() {
        return accountType;
    }

    public void setAccountType(int accountType) {
        this.accountType = accountType;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getCreationCount() {
        return creationCount;
    }

    public void setCreationCount(int creationCount) {
        this.creationCount = creationCount;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public int getPvAvailable() {
        return pvAvailable;
    }

    public void setPvAvailable(int pvAvailable) {
        this.pvAvailable = pvAvailable;
    }

    public int getPvCount() {
        return pvCount;
    }

    public void setPvCount(int pvCount) {
        this.pvCount = pvCount;
    }

    public String getPvLimit() {
        return pvLimit;
    }

    public void setPvLimit(String pvLimit) {
        this.pvLimit = pvLimit;
    }

    public int getPvTotal() {
        return pvTotal;
    }

    public void setPvTotal(int pvTotal) {
        this.pvTotal = pvTotal;
    }

    public int getSmsCount() {
        return smsCount;
    }

    public void setSmsCount(int smsCount) {
        this.smsCount = smsCount;
    }

    public int getStaffCount() {
        return staffCount;
    }

    public void setStaffCount(int staffCount) {
        this.staffCount = staffCount;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "Corp{" +
                "accountType=" + accountType +
                ", id='" + id + '\'' +
                ", openId='" + openId + '\'' +
                ", name='" + name + '\'' +
                ", staffCount=" + staffCount +
                ", creationCount=" + creationCount +
                ", pvCount=" + pvCount +
                ", pvTotal=" + pvTotal +
                ", pvLimit=" + pvLimit +
                ", pvAvailable=" + pvAvailable +
                ", createTime='" + createTime + '\'' +
                ", expireTime='" + expireTime + '\'' +
                ", smsCount=" + smsCount +
                ", status=" + status +
                '}';
    }
}
