package com.ce.scrm.center.service.business.entity.dto.segment;

import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 根据角色查询分群入参条件
 * @Author: lijinpeng
 * @Date: 2025/2/27 14:24
 */
@Data
public class PullGroupSegmentBusinessDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	private Integer id;

	/**
	 * cdp分群code
	 */
	private String segmentId;

	/**
	 * 专项线索名称（分司可见）
	 */
	private String segmentName;

	/**
	 * 专项线索名称（分司可见）
	 */
	private String segmentDesc;

	/**
	 * 跟踪开始时间
	 */
	private String segmentStartDate;

	/**
	 * 跟踪结束时间
	 */
	private String segmentEndDate;

	/**
	 * 员工ID
	 */
	private String loginEmployeeId;
}
