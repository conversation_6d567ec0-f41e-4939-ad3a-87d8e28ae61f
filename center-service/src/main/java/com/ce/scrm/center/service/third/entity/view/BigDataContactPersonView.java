package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 根据pid获取联系方式
 * @Author: lijinpeng
 * @Date: 2024/12/12 14:16
 */
@Data
public class BigDataContactPersonView implements Serializable {

    /**
     * 企业唯一编码
     */
    private String pid;
    /**
     * 企业名称
     */
    private String entname;
    /**
     * 统一信用代码
     */
    private String uncid;

    /**
     * 联系方式
     */
    private List<BigDataContactsView> list;

}
