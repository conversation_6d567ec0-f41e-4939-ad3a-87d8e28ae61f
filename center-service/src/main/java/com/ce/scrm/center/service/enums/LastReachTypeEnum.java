package com.ce.scrm.center.service.enums;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/15 11:43
 */
public enum LastReachTypeEnum {

    /**
     * 分司总监拜访
     */
    BRANCH_DIRECTOR_VISIT(1, "分司总监拜访"),

    /**
     * 经理拜访
     */
    MANAGER_VISIT(2, "经理拜访"),

    /**
     * 事业部总监拜访
     */
    DIVISION_DIRECTOR_VISIT(3, "事业部总监拜访"),

    /**
     * 商务拜访
     */
    BUSINESS_VISIT(4, "商务拜访"),

    /**
     * 分司总监发送内容
     */
    BRANCH_DIRECTOR_SEND(5, "分司总监发送内容"),

    /**
     * 经理发送内容
     */
    MANAGER_SEND(6, "经理发送内容"),

    /**
     * 事业部总监发送内容
     */
    DIVISION_DIRECTOR_SEND(7, "事业部总监发送内容"),

    /**
     * 商务发送内容
     */
    BUSINESS_SEND(8, "商务发送内容"),
    ;

    private final Integer code;
    private final String description;

    /**
     * 构造函数
     * @param code 层级编码（自然数序号，1开始）
     * @param description 层级描述
     */
    LastReachTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取层级编码
     * @return 编码值
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取层级描述
     * @return 描述文本
     */
    public String getDescription() {
        return description;
    }

    public static String getDescriptionByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (LastReachTypeEnum lastReachTypeEnum : values()) {
            if (lastReachTypeEnum.getCode().equals(code)) {
                return lastReachTypeEnum.description;
            }
        }
        return null;
    }

    public static Integer getCodeByDescription(String description) {
        if (description == null) {
            return null;
        }
        for (LastReachTypeEnum lastReachTypeEnum : values()) {
            if (lastReachTypeEnum.getDescription().equals(description)) {
                return lastReachTypeEnum.code;
            }
        }
        return null;
    }

}
