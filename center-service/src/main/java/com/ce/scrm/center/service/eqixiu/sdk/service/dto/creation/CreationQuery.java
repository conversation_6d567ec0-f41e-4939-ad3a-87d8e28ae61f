/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.creation;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.*;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

/**
 * 作品列表查询参数封装
 *
 * <AUTHOR>
 */
public class CreationQuery extends BaseParam {

    /**
     * 查询指定员工的作品信息，逗号分隔
     */
    private String openIds;

    /**
     * 作品名称，支持左匹配模糊查询
     */
    private String name;
    private CreationStatus status;
    private CreationType type;

    private ActivityStatus activityStatus;

    private AuditStatus auditStatus;

    private ApproveStatus approveStatus;

    private AuthType authType;


    public void validate() {

    }

    public Map<String, String> getParamsMap() {
        Map<String, String> map = getBaseParamsMap();
        if (StrUtil.isNotEmpty(openIds)) {
            map.put("openIds", openIds);
        }
        if (StrUtil.isNotEmpty(name)) {
            map.put("name", name);
        }
        if (status != null) {
            map.put("status", String.valueOf(status.getValue()));
        }
        if (type != null) {
            map.put("type", type.getValue());
        }
        if (activityStatus != null) {
            map.put("activityStatus", String.valueOf(activityStatus.getValue()));
        }
        if (auditStatus != null) {
            map.put("auditStatus", String.valueOf(auditStatus.getValue()));
        }
        if (approveStatus != null) {
            map.put("approveStatus", String.valueOf(approveStatus.getValue()));
        }
        if (authType != null) {
            map.put("authType", String.valueOf(authType.getValue()));
        }
        return map;
    }

    public CreationQuery setOpenIds(String openIds) {
        this.openIds = openIds;
        return this;
    }

    public CreationQuery setName(String name) {
        this.name = name;
        return this;
    }

    public CreationQuery setStatus(CreationStatus status) {
        this.status = status;
        return this;
    }

    public CreationQuery setType(CreationType type) {
        this.type = type;
        return this;
    }

    public CreationQuery setActivityStatus(ActivityStatus activityStatus) {
        this.activityStatus = activityStatus;
        return this;
    }

    public CreationQuery setAuditStatus(AuditStatus auditStatus) {
        this.auditStatus = auditStatus;
        return this;
    }

    public CreationQuery setApproveStatus(ApproveStatus approveStatus) {
        this.approveStatus = approveStatus;
        return this;
    }

    public CreationQuery setAuthType(AuthType authType) {
        this.authType = authType;
        return this;
    }

}
