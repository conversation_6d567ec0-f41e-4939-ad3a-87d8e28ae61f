package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description:商标
 * @author: JiuDD
 * date: 2025/2/13
 */
@Data
public class BigDataTrademarkDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**统一信用代码*/
    @JSONField(serialize = false)
	private String uncid;
    /**总数量*/
    @JSONField(serialize = false)
	private int total_count;
    /**总页数*/
    @JSONField(serialize = false)
	private int total_page;
    /**当前页*/
    @JSONField(serialize = false)
	private int page;
    /**商标列表*/
    @JSONField(name = "商标列表")
    private List<ListTrademark> list;

    @NoArgsConstructor
    @Data
    public static class ListTrademark implements Serializable {
        private static final long serialVersionUID = 1L;
        /**商标注册号（申请号）*/
        @JSONField(serialize = false)
		private String apply_no;
        /**商标类别*/
        @JSONField(name = "商标类别",serialize = false)
		private String mark_type;
        /**商标名称*/
        @JSONField(name = "商标名称")
		private String mark_name;
        /**申请日期*/
        @JSONField(serialize = false)
		private String apply_date;
        /**申请人（中文）*/
        @JSONField(serialize = false)
		private String company_name;
        /**申请人地址*/
        @JSONField(serialize = false)
		private String apply_addr;
        /**商标共有人(多个商标共有人, 用英文的;号隔开)*/
        @JSONField(serialize = false)
        private String mark_coowner;
        /**商标共有人的pid(多个商标共有人的pid, 用英文的;号隔开)*/
        @JSONField(serialize = false)
		private String mark_coowner_pid;
        /**申请人（英文）*/
        @JSONField(serialize = false)
		private String company_en_name;
        /**申请人地址（英文）*/
        @JSONField(serialize = false)
		private String apply_en_addr;
        /**初审公告期号*/
        @JSONField(serialize = false)
		private String exam_pub_no;
        /**初审公告日期*/
        @JSONField(serialize = false)
		private String exam_pub_date;
        /**注册公告期号*/
        @JSONField(name = "注册公告期号",serialize = false)
		private String register_no;
        /**注册公告日期*/
        @JSONField(name = "注册公告日期",format = "yyyy-MM-dd HH:mm:ss",serialize = false)
		private Date register_date;
        /**专用权开始日期*/
        @JSONField(name = "专用权开始日期",serialize = false)
		private String use_right_date_begin;
        /**专用权结束日期*/
        @JSONField(name = "专用权结束日期",serialize = false)
		private String use_right_date_end;
        /**专用权期限*/
        @JSONField(serialize = false)
		private String deadline;
        /**是否共有商标（否0，是1）*/
        @JSONField(name = "是否共有商标",serialize = false)
		private Integer is_common_mark;
        @JSONField(name = "是否共有商标")
        private String is_common_mark_str;
        /**商标代理机构名称*/
        @JSONField(serialize = false)
		private String agent_name;
        /**商标代理机构pid*/
        @JSONField(serialize = false)
		private String agent_name_pid;
        /**商标类型 0-普通商标 1-特殊商标 2-集体商标 3-证明商标 4-立体商标 5-声音商标*/
        @JSONField(name = "商标类型",serialize = false)
		private String mark_type_str;
        /**后期指定日期*/
        @JSONField(serialize = false)
        private String specify_date;
        /**国际注册日期*/
        @JSONField(serialize = false)
		private String inter_regi_date;
        /**优先权日期*/
        @JSONField(name = "优先权日期",serialize = false)
		private String priority_date;
        /**商标类似群*/
        @JSONField(serialize = false)
		private String mark_class_code;
        /**商标状态*/
        @JSONField(name = "商标状态")
		private String status;
        /**类型编码*/
        @JSONField(name = "商标类型",serialize = false)
		private String inventory;
    }
}
