package com.ce.scrm.center.service.eqixiu.sdk.domain.type;

/**
 * 活动状态
 * 1	未开始
 * 2	进行中
 * 3	暂停中
 * 4	已结束
 * -1	未发布未开始(无实际意义)
 *
 * 只有互动，表单这些可设置开始时间、结束时间作品，此状态才有意义，其他作品返回-1
 */
public enum ActivityStatus {
    NOT_START(1, "未开始"),
    UNDERWAY(2, "进行中"),
    PAUSE(3, "暂停中"),
    END(4, "已结束"),
    NOT_PUBLISH(1, "未发布未开始(无实际意义)")
    ;
    private int value;
    private String title;

    ActivityStatus(int value, String title) {
        this.value = value;
        this.title = title;
    }

    public int getValue() {
        return value;
    }

    public String getTitle() {
        return title;
    }
}
