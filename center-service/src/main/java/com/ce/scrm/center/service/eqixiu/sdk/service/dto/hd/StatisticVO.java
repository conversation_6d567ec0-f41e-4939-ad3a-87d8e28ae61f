/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.hd;

import java.util.Date;
import java.util.List;

public class StatisticVO {
    List<Long> shareList;
    private Long creationId;
    private Date hourBeLongDay;
    private Long pvTotal = 0L;
    private Long uvTotal = 0L;
    private Long likeTotal = 0L;
    private Long shareTotal = 0L;
    private Long appJumpTotal = 0L;
    private Long gameJoinPersonTotal;
    private Long gameJoinPersonTimeTotal;
    private Long lotteryJoinPersonTotal;
    private Long lotteryJoinPersonTimeTotal;
    private Long lotteryWinPersonTotal;
    private Long lotteryWinPersonTimeTotal;
    private Long winTotal;
    private Long cashTotal;
    private Long lotterySendOutPrizeTotal;
    private Long pvDur = 0L;
    private Long uvDur = 0L;
    private Long likeDur = 0L;
    private Long shareDur = 0L;
    private Long appJumpDur = 0L;
    private Long formTotal = 0L;
    private Long formDur = 0L;
    private Double avgScoreDur = 0D;
    private Long forwardTotal = 0L;
    private Long gameJoinPersonDur;
    private Long gameJoinPersonTimeDur;
    private Long lotteryJoinPersonDur;
    private Long lotteryJoinPersonTimeDur;
    private Long lotteryWinPersonDur;
    private Long lotteryWinPersonTimeDur;
    private Long winDur;
    private Long cashDur;
    private Long lotterySendOutPrizeDur;
    private List<Long> pvList;
    private List<Long> uvList;
    private List<Long> formList;
    private List<Long> likeList;
    private List<String> timeList;
    private List<Long> appJumpList;
    private List<Long> gameJoinPersonList;
    private List<Long> gameJoinPersonTimeList;
    private List<Long> lotteryJoinPersonList;
    private List<Long> lotteryJoinPersonTimeList;
    private List<Long> lotteryWinPersonList;
    private List<Long> lotteryWinPersonTimeList;
    private List<Long> winList;
    private List<Long> cashList;
    private List<Long> lotterySendOutPrizeList;
    private List<Double> catchAvgScoreList;
    private List<Long> catchPtCountList;
    private String authTitle;

    public static StatisticVO empty() {
        return new StatisticVO();
    }

    public Date getHourBeLongDay() {
        return hourBeLongDay;
    }

    public void setHourBeLongDay(Date hourBeLongDay) {
        this.hourBeLongDay = hourBeLongDay;
    }

    public Long getFormDur() {
        return formDur;
    }

    public void setFormDur(Long formDur) {
        this.formDur = formDur;
    }

    public String getAuthTitle() {
        return authTitle;
    }

    public void setAuthTitle(String authTitle) {
        this.authTitle = authTitle;
    }

    public List<Long> getFormList() {
        return formList;
    }

    public void setFormList(List<Long> formList) {
        this.formList = formList;
    }

    public List<Double> getCatchAvgScoreList() {
        return catchAvgScoreList;
    }

    public void setCatchAvgScoreList(List<Double> catchAvgScoreList) {
        this.catchAvgScoreList = catchAvgScoreList;
    }

    public List<Long> getCatchPtCountList() {
        return catchPtCountList;
    }

    public void setCatchPtCountList(List<Long> catchPtCountList) {
        this.catchPtCountList = catchPtCountList;
    }

    public Long getPvTotal() {
        return pvTotal;
    }

    public void setPvTotal(Long pvTotal) {
        this.pvTotal = pvTotal;
    }

    public Long getUvTotal() {
        return uvTotal;
    }

    public void setUvTotal(Long uvTotal) {
        this.uvTotal = uvTotal;
    }

    public Long getShareTotal() {
        return shareTotal;
    }

    public void setShareTotal(Long shareTotal) {
        this.shareTotal = shareTotal;
    }

    public Long getShareDur() {
        return shareDur;
    }

    public void setShareDur(Long shareDur) {
        this.shareDur = shareDur;
    }

    public Long getLikeTotal() {
        return likeTotal;
    }

    public void setLikeTotal(Long likeTotal) {
        this.likeTotal = likeTotal;
    }

    public Long getAppJumpTotal() {
        return appJumpTotal;
    }

    public void setAppJumpTotal(Long appJumpTotal) {
        this.appJumpTotal = appJumpTotal;
    }

    public Long getPvDur() {
        return pvDur;
    }

    public void setPvDur(Long pvDur) {
        this.pvDur = pvDur;
    }

    public Long getUvDur() {
        return uvDur;
    }

    public void setUvDur(Long uvDur) {
        this.uvDur = uvDur;
    }

    public Long getLikeDur() {
        return likeDur;
    }

    public void setLikeDur(Long likeDur) {
        this.likeDur = likeDur;
    }

    public Long getAppJumpDur() {
        return appJumpDur;
    }

    public void setAppJumpDur(Long appJumpDur) {
        this.appJumpDur = appJumpDur;
    }

    public Double getAvgScoreDur() {
        return avgScoreDur;
    }

    public void setAvgScoreDur(Double avgScoreDur) {
        this.avgScoreDur = avgScoreDur;
    }

    public List<Long> getPvList() {
        return pvList;
    }

    public void setPvList(List<Long> pvList) {
        this.pvList = pvList;
    }

    public List<Long> getUvList() {
        return uvList;
    }

    public void setUvList(List<Long> uvList) {
        this.uvList = uvList;
    }

    public List<Long> getLikeList() {
        return likeList;
    }

    public void setLikeList(List<Long> likeList) {
        this.likeList = likeList;
    }

    public List<String> getTimeList() {
        return timeList;
    }

    public void setTimeList(List<String> timeList) {
        this.timeList = timeList;
    }

    public List<Long> getAppJumpList() {
        return appJumpList;
    }

    public void setAppJumpList(List<Long> appJumpList) {
        this.appJumpList = appJumpList;
    }

    public List<Long> getWinList() {
        return winList;
    }

    public void setWinList(List<Long> winList) {
        this.winList = winList;
    }

    public List<Long> getCashList() {
        return cashList;
    }

    public void setCashList(List<Long> cashList) {
        this.cashList = cashList;
    }

    public Long getCreationId() {
        return creationId;
    }

    public void setCreationId(Long creationId) {
        this.creationId = creationId;
    }

    public Long getFormTotal() {
        return formTotal;
    }

    public void setFormTotal(Long formTotal) {
        this.formTotal = formTotal;
    }

    public Long getForwardTotal() {
        return forwardTotal;
    }

    public void setForwardTotal(Long forwardTotal) {
        this.forwardTotal = forwardTotal;
    }

    public List<Long> getShareList() {
        return shareList;
    }

    public void setShareList(List<Long> shareList) {
        this.shareList = shareList;
    }

    public Long getWinTotal() {
        return winTotal == null ? 0L : winTotal;
    }

    public void setWinTotal(Long winTotal) {
        this.winTotal = winTotal;
    }

    public Long getCashTotal() {
        return cashTotal == null ? 0L : cashTotal;
    }

    public void setCashTotal(Long cashTotal) {
        this.cashTotal = cashTotal;
    }

    public Long getWinDur() {
        return winDur;
    }

    public void setWinDur(Long winDur) {
        this.winDur = winDur;
    }

    public Long getCashDur() {
        return cashDur;
    }

    public void setCashDur(Long cashDur) {
        this.cashDur = cashDur;
    }

    public Long getGameJoinPersonTotal() {
        return gameJoinPersonTotal == null ? 0L : gameJoinPersonTotal;
    }

    public void setGameJoinPersonTotal(Long gameJoinPersonTotal) {
        this.gameJoinPersonTotal = gameJoinPersonTotal;
    }

    public Long getGameJoinPersonTimeTotal() {
        return gameJoinPersonTimeTotal == null ? 0L : gameJoinPersonTimeTotal;
    }

    public void setGameJoinPersonTimeTotal(Long gameJoinPersonTimeTotal) {
        this.gameJoinPersonTimeTotal = gameJoinPersonTimeTotal;
    }

    public Long getLotteryJoinPersonTotal() {
        return lotteryJoinPersonTotal == null ? 0L : lotteryJoinPersonTotal;
    }

    public void setLotteryJoinPersonTotal(Long lotteryJoinPersonTotal) {
        this.lotteryJoinPersonTotal = lotteryJoinPersonTotal;
    }

    public Long getLotteryJoinPersonTimeTotal() {
        return lotteryJoinPersonTimeTotal == null ? 0L : lotteryJoinPersonTimeTotal;
    }

    public void setLotteryJoinPersonTimeTotal(Long lotteryJoinPersonTimeTotal) {
        this.lotteryJoinPersonTimeTotal = lotteryJoinPersonTimeTotal;
    }

    public Long getLotteryWinPersonTotal() {
        return lotteryWinPersonTotal == null ? 0L : lotteryWinPersonTotal;
    }

    public void setLotteryWinPersonTotal(Long lotteryWinPersonTotal) {
        this.lotteryWinPersonTotal = lotteryWinPersonTotal;
    }

    public Long getLotterySendOutPrizeTotal() {
        return lotterySendOutPrizeTotal == null ? 0L : lotterySendOutPrizeTotal;
    }

    public void setLotterySendOutPrizeTotal(Long lotterySendOutPrizeTotal) {
        this.lotterySendOutPrizeTotal = lotterySendOutPrizeTotal;
    }

    public Long getGameJoinPersonDur() {
        return gameJoinPersonDur;
    }

    public void setGameJoinPersonDur(Long gameJoinPersonDur) {
        this.gameJoinPersonDur = gameJoinPersonDur;
    }

    public Long getGameJoinPersonTimeDur() {
        return gameJoinPersonTimeDur;
    }

    public void setGameJoinPersonTimeDur(Long gameJoinPersonTimeDur) {
        this.gameJoinPersonTimeDur = gameJoinPersonTimeDur;
    }

    public Long getLotteryJoinPersonDur() {
        return lotteryJoinPersonDur;
    }

    public void setLotteryJoinPersonDur(Long lotteryJoinPersonDur) {
        this.lotteryJoinPersonDur = lotteryJoinPersonDur;
    }

    public Long getLotteryJoinPersonTimeDur() {
        return lotteryJoinPersonTimeDur;
    }

    public void setLotteryJoinPersonTimeDur(Long lotteryJoinPersonTimeDur) {
        this.lotteryJoinPersonTimeDur = lotteryJoinPersonTimeDur;
    }

    public Long getLotteryWinPersonDur() {
        return lotteryWinPersonDur;
    }

    public void setLotteryWinPersonDur(Long lotteryWinPersonDur) {
        this.lotteryWinPersonDur = lotteryWinPersonDur;
    }

    public Long getLotterySendOutPrizeDur() {
        return lotterySendOutPrizeDur;
    }

    public void setLotterySendOutPrizeDur(Long lotterySendOutPrizeDur) {
        this.lotterySendOutPrizeDur = lotterySendOutPrizeDur;
    }

    public List<Long> getGameJoinPersonList() {
        return gameJoinPersonList;
    }

    public void setGameJoinPersonList(List<Long> gameJoinPersonList) {
        this.gameJoinPersonList = gameJoinPersonList;
    }

    public List<Long> getGameJoinPersonTimeList() {
        return gameJoinPersonTimeList;
    }

    public void setGameJoinPersonTimeList(List<Long> gameJoinPersonTimeList) {
        this.gameJoinPersonTimeList = gameJoinPersonTimeList;
    }

    public List<Long> getLotteryJoinPersonList() {
        return lotteryJoinPersonList;
    }

    public void setLotteryJoinPersonList(List<Long> lotteryJoinPersonList) {
        this.lotteryJoinPersonList = lotteryJoinPersonList;
    }

    public List<Long> getLotteryJoinPersonTimeList() {
        return lotteryJoinPersonTimeList;
    }

    public void setLotteryJoinPersonTimeList(List<Long> lotteryJoinPersonTimeList) {
        this.lotteryJoinPersonTimeList = lotteryJoinPersonTimeList;
    }

    public List<Long> getLotteryWinPersonList() {
        return lotteryWinPersonList;
    }

    public void setLotteryWinPersonList(List<Long> lotteryWinPersonList) {
        this.lotteryWinPersonList = lotteryWinPersonList;
    }

    public List<Long> getLotterySendOutPrizeList() {
        return lotterySendOutPrizeList;
    }

    public void setLotterySendOutPrizeList(List<Long> lotterySendOutPrizeList) {
        this.lotterySendOutPrizeList = lotterySendOutPrizeList;
    }

    public Long getLotteryWinPersonTimeTotal() {
        return lotteryWinPersonTimeTotal == null ? 0L : lotteryWinPersonTimeTotal;
    }

    public void setLotteryWinPersonTimeTotal(Long lotteryWinPersonTimeTotal) {
        this.lotteryWinPersonTimeTotal = lotteryWinPersonTimeTotal;
    }

    public Long getLotteryWinPersonTimeDur() {
        return lotteryWinPersonTimeDur;
    }

    public void setLotteryWinPersonTimeDur(Long lotteryWinPersonTimeDur) {
        this.lotteryWinPersonTimeDur = lotteryWinPersonTimeDur;
    }

    public List<Long> getLotteryWinPersonTimeList() {
        return lotteryWinPersonTimeList;
    }

    public void setLotteryWinPersonTimeList(List<Long> lotteryWinPersonTimeList) {
        this.lotteryWinPersonTimeList = lotteryWinPersonTimeList;
    }

    @Override
    public String toString() {
        return "StatisticPageViewVo{" +
                "creationId=" + creationId +
                ", pvTotal=" + pvTotal +
                ", uvTotal=" + uvTotal +
                ", likeTotal=" + likeTotal +
                ", appJumpTotal=" + appJumpTotal +
                ", pvDur=" + pvDur +
                ", uvDur=" + uvDur +
                ", likeDur=" + likeDur +
                ", appJumpDur=" + appJumpDur +
                ", pvList=" + pvList +
                ", uvList=" + uvList +
                ", likeList=" + likeList +
                ", timeList=" + timeList +
                ", appJumpList=" + appJumpList +
                ", winList=" + winList +
                ", cashList=" + cashList +
                '}';
    }
}
