package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 企业联系方式
 * @Author: lijinpeng
 * @Date: 2024/12/12 14:17
 */
@Data
public class BigDataContactsView implements Serializable {

    /**
     * 是否精准联系人
     */
    private Boolean accurate_ent;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 号码
     */
    private String content;
    /**
     * 外部序号
     */
    private String outSort;
    /**
     * 联系人职位
     */
    private String position;
    /**
     * 号码重复数
     */
    private int repeat_amount;
    /**
     * 来源
     */
//    private List<ContactsSourceView> sources;
    /**
     * 手机号状态 : 1000实号、2000沉默号、2001停机、2002空号、2003风险号、3000库无、3001非法号码、3002未验证
     */
    private String status;
    /**
     * 号码类型 【0:未知 1:手机; 2:固话; 3:QQ; 4:邮箱】
     */
    private Integer type;

}
