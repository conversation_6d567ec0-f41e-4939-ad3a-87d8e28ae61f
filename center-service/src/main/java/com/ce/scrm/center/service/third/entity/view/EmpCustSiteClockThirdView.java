package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EmpCustSiteClockThirdView implements Serializable {

    /**
     * id
     */
    private String id;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 员工id
     */
    private String empId;

    /**
     * 打卡地址
     */
    private String clockAddress;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 事业部id
     */
    private String buId;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 跟踪记录id
     */
    private String followRecordId;

    /**
     * 附件id
     */
    private String attachIds;

    /**
     * 打卡所在省
     */
    private String clockProvince;

    /**
     * 打卡所在市
     */
    private String clockCity;

    /**
     * 打卡所在区
     */
    private String clockRegion;

    /**
     * 警告1
     */
    private Integer warning1;

    /**
     * 警告2
     */
    private Integer warning2;

    /**
     * 警告3
     */
    private Integer warning3;

    /**
     * 客户注册地址
     */
    private String regAddress;

    /**
     * 替打卡人员id
     */
    private String replaceClockEmpId;

    /**
     * 是否是代打卡
     */
    private Integer replaceClockFlag;

    /**
     * 保护商务id
     */
    private String protectSalerId;


    /**
     * 标记打卡是否有效
     */
    private Integer markClockValidFlag;

}
