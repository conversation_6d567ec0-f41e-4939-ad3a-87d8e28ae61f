package com.ce.scrm.center.service.business.entity.dto.abm;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: 下发商机明细对象
 */

@Data
public class SjAssignDetailAddDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    private String customerId;

    private String customerName;
    /**
     * 审核记录id
     */
    private Long reviewId;
    /**
     * 审核来源类型 1：SDR 2：CC 必填非空
     */
    private Integer reviewSrcType;

    /**
     * 商务代表ID
     */
    private String salerId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 事业部ID
     */
    private String buId;

    /**
     * 分公司ID
     */
    private String subId;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 市场
     */
    private String marketId;

    /**
     * 创建者
     */
    private String createdId;

}