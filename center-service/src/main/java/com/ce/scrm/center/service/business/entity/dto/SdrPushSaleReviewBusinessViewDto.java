package com.ce.scrm.center.service.business.entity.dto;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * SDR推送销售审核业务查看DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
@Data
public class SdrPushSaleReviewBusinessViewDto implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 审核来源类型 1：SDR 2：CC 必填非空
     */
    private Integer reviewSrcType;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 联系人名称
     */
    private String contactPersonName;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 通讯地址
     */
    private String address;

    /**
     * 意向产品 支持多个
     */
    private String intentProduct;

    /**
     * 推荐理由
     */
    private String recommendedReason;

    /**
     * 附件信息
     */
    private String attachment;
//    private List<Map<String, Object>> attachment;

    /**
     * 审核状态 0:待审核 1:审核通过
     */
    private Integer reviewStatus;

    /**
     *  是否自动审核通过 0: 否 1:是
     */
    private Integer isAutoReviewPass;

    /**
     * 审核人id
     */
    private String reviewId;

    /**
     * 审核时间
     */
    private Date reviewTime;

    /**
     * 调配分司
     */
    private String assignSubId;

    /**
     * 备注原因
     */
    private String remarkReason;

    /**
     * 创建者
     */
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updatedId;

    /**
     * 更新时间
     */
    private Date updatedTime;
} 