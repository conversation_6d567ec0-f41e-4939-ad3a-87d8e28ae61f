package com.ce.scrm.center.service.business.entity.view;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 首页拜访率进入内页显示界面
 * @Author: 李金澎
 * @Date: 2024/9/26 10:55
 * @Version: 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CallDetailsWebView implements Serializable  {

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司ID
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务ID
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 保护开始日期
     */
    private Date protectProtectTime;

    /**
     * 已保护天数
     */
    private Integer cdpProtectDay;

    /**
     * 本月打卡次数
     */
    private Integer cdpCurrentMonthClockCount;

    /**
     * 累计打卡次数
     */
    private Integer cdpClockCount;

    /**
     * 是否成交
     */
    private Boolean bargainFlag;

}
