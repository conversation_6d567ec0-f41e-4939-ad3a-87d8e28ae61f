/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.corp;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

/**
 * 变更员工部门
 */
public class ChangeStaffDeptCmd extends BaseParam {

    /**
     * 部门id，对应部门管理接口“查询部门”返回的id
     * 这里可以批量设置多个部门，用逗号隔开，如："1,2,3"
     */
    private String departmentIds;

    public ChangeStaffDeptCmd(String openId, String... departmentIds) {
        setOpenId(openId);
        this.departmentIds = String.join(",", departmentIds);
    }

    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> paramMap = getBaseParamsMap();
        paramMap.put("departmentIds", departmentIds);
        return paramMap;
    }

    @Override
    public void validate() {
        if (StrUtil.isEmpty(getOpenId())) {
            throw new KnownException("openId参数不能为空");
        }
        if (StrUtil.isEmpty(departmentIds)) {
            throw new KnownException("departmentIds参数不能为空");
        }

    }

    public String getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(String departmentIds) {
        this.departmentIds = departmentIds;
    }
}
