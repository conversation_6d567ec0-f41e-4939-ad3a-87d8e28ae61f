package com.ce.scrm.center.service.business.entity.dto;

import cn.ce.cesupport.enums.GcSjSourceEnum;
import cn.ce.cesupport.enums.GcSjStateEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 高呈商机分页参数
 * <AUTHOR>
 * @date 2024/5/15 上午10:53
 * @version 1.0.0
 */
@Data
public class GcBusinessOpportunityPageBusinessDto implements Serializable {

    /**
     * 高呈商机查询开始时间
     */
    private LocalDateTime startTime;

    /**
     * 高呈商机查询结束时间
     */
    private LocalDateTime endTime;

    /**
     * 查询字符串（客户名称、联系人姓名、手机号）
     */
    private String queryStr;

    /**
     * 商机来源
     */
    private GcSjSourceEnum gcSjSourceEnum;

    /**
     * 商机状态和来源
     */
    private List<GcSjStateEnum> gcSjStateEnumList;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 10;
}