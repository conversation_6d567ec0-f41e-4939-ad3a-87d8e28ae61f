/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.template;

import java.util.Date;

/**
 * 模板使用明细，基于模板生成的作品信息
 */
public class TemplateUsedCreation {

    /**
     * 作品封面
     */
    private String cover;

    /**
     * 作品名称
     */
    private String creationName;

    /**
     * 累计浏览数（pv）
     */
    private int pvTotal;

    /**
     * 累计访客数（uv）
     */
    private int uvTotal;

    /**
     * 累计表单数
     */
    private int formTotal;

    /**
     * 分享数（此字段数据仅供参考）
     */
    private int shareTotal;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 作品ID
     */
    private Long creationId;

    /**
     * 发布状态
     */
    private int publishStatus;

    /**
     * 员工ID
     */
    private String staffId;

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getCreationName() {
        return creationName;
    }

    public void setCreationName(String creationName) {
        this.creationName = creationName;
    }

    public int getPvTotal() {
        return pvTotal;
    }

    public void setPvTotal(int pvTotal) {
        this.pvTotal = pvTotal;
    }

    public int getUvTotal() {
        return uvTotal;
    }

    public void setUvTotal(int uvTotal) {
        this.uvTotal = uvTotal;
    }

    public int getFormTotal() {
        return formTotal;
    }

    public void setFormTotal(int formTotal) {
        this.formTotal = formTotal;
    }

    public int getShareTotal() {
        return shareTotal;
    }

    public void setShareTotal(int shareTotal) {
        this.shareTotal = shareTotal;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getCreationId() {
        return creationId;
    }

    public void setCreationId(Long creationId) {
        this.creationId = creationId;
    }

    public int getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(int publishStatus) {
        this.publishStatus = publishStatus;
    }

    public String getStaffId() {
        return staffId;
    }

    public void setStaffId(String staffId) {
        this.staffId = staffId;
    }

    @Override
    public String toString() {
        return "{" +
                "cover='" + cover + '\'' +
                ", creationName='" + creationName + '\'' +
                ", pvTotal=" + pvTotal +
                ", uvTotal=" + uvTotal +
                ", formTotal=" + formTotal +
                ", shareTotal=" + shareTotal +
                ", createDate=" + createDate +
                ", createUser='" + createUser + '\'' +
                ", deptName='" + deptName + '\'' +
                ", creationId=" + creationId +
                ", publishStatus=" + publishStatus +
                ", staffId='" + staffId + '\'' +
                '}';
    }
}
