package com.ce.scrm.center.service.third.entity.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerMarketingActivityQueryThirdDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 页号
	 */
	private Integer pageNum = 1;

	/**
	 * 页码
	 */
	private Integer pageSize = 10;


	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 活动编码
	 */
	private String activityCode;

	/**
	 * 活动名称
	 */
	private String activityName;

	/**
	 * 创建人id
	 */
	private String createBy;

	/**
	 * 执行状态
	 */
	private Integer executeState;

	/**
	 * 创建时间开始
	 */
	private Date createTimeStart;

	/**
	 * 创建时间结束
	 */
	private Date createTimeEnd;
}
