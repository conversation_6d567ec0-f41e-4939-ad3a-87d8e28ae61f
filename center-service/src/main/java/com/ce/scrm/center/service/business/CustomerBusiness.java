package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.base.utils.protect.CmCustProtectUtil;
import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import cn.ce.cesupport.enums.CustomerRegisterStageEnum;
import cn.ce.cesupport.enums.ProtectStatusEnum;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.ce.cesupport.newcustclue.service.ClueAssignAppService;
import cn.ce.cesupport.sma.dto.CustomerStageInfoDto;
import cn.ce.cesupport.sma.service.CustomerStageAppService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerLeads;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.business.abm.CustomerLeadsBusiness;
import com.ce.scrm.center.service.business.entity.dto.*;
import com.ce.scrm.center.service.business.entity.dto.abm.ContactPersonCallStatusDto;
import com.ce.scrm.center.service.business.entity.view.*;
import com.ce.scrm.center.service.business.entity.view.customer.*;
import com.ce.scrm.center.service.enums.CustomerStageEnum;
import com.ce.scrm.center.service.enums.ProtectCustTypeEnum;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.router.RouterContext;
import com.ce.scrm.center.service.third.entity.dto.CallStatusThirdDto;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.view.*;
import com.ce.scrm.center.service.third.invoke.*;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.customer.dubbo.entity.dto.CallDetailsDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerDetailDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerESPageDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView;
import com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView;
import com.ce.scrm.customer.dubbo.entity.view.CustomerESDubboView;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerTagDubboView;
import com.ce.scrm.extend.dubbo.api.IBigDataDubbo;
import com.ce.scrm.extend.dubbo.api.ICompanyInfoEsDubbo;
import com.ce.scrm.extend.dubbo.entity.request.NearbyCustomersFromESRequest;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.BigDataCompanyDetailView;
import com.ce.scrm.extend.dubbo.entity.view.CompanyInfoDubboView;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户业务
 * <AUTHOR>
 * @date 2024/5/21 下午3:24
 * @version 1.0.0
 */
@Slf4j
@Service
public class CustomerBusiness {

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private OrgThirdService orgThirdService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private CustomerThirdService customerThirdService;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private IBigDataDubbo iBigDataDubbo;

    @Resource
    private BigDataThirdService bigDataThirdService;

    @Resource
    private SmaMarketBusiness smaMarketBusiness;

    @DubboReference
    private CustomerStageAppService customerStageAppService;

    @Resource
    private FavoriteBusiness favoriteBusiness;

    @Resource
    private ProtectBusiness protectBusiness;

    @Resource
    private IndustryThirdService industryThirdService;

    @Resource
    private CustomerLinkmanThirdService customerLinkmanThirdService;

	@DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
	private ICompanyInfoEsDubbo iCompanyInfoEsDubbo;

    @DubboReference
    private ClueAssignAppService clueAssignAppService;

    @Resource
    private CustomerLeadsBusiness customerLeadsBusiness;

    @Resource
    private CallCenterThirdService callCenterThirdService;

    @Resource
    private CustomerTagsThirdService customerTagsThirdService;

    /**
     * 获取客户阶段数据
     * @param customerId    客户ID
     * <AUTHOR>
     * @date 2024/5/21 下午3:53
     * @return com.ce.scrm.center.service.business.entity.view.CustomerStageDataBusinessView
     **/
    public CustomerStageDataBusinessView getCustomerStageData(String customerId) {
        CustomerStageDataBusinessView customerStageDataBusinessView = new CustomerStageDataBusinessView();
        customerStageDataBusinessView.setCustomerId(customerId);
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, customerId).one();
        CustomerStageEnum customerStageEnum = CustomerStageEnum.OPEN_SEA;
        if (cmCustProtect != null) {
            List<String> orgIdList = Arrays.asList(cmCustProtect.getAreaId(), cmCustProtect.getSubcompanyId(), cmCustProtect.getBussdeptId());
            Map<String, OrgDataThirdView> orgDataThirdViewMap = orgThirdService.getOrgData(orgIdList);
            customerStageDataBusinessView.setCustomerName(cmCustProtect.getCustName());
            customerStageDataBusinessView.setAreaId(cmCustProtect.getAreaId());
            customerStageDataBusinessView.setAreaName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getAreaId(), new OrgDataThirdView()).getName());
            customerStageDataBusinessView.setSubId(cmCustProtect.getSubcompanyId());
            customerStageDataBusinessView.setSubName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getSubcompanyId(), new OrgDataThirdView()).getName());
            customerStageDataBusinessView.setDeptId(cmCustProtect.getBussdeptId());
            customerStageDataBusinessView.setDeptName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBussdeptId(), new OrgDataThirdView()).getName());
            customerStageDataBusinessView.setSalerId(cmCustProtect.getSalerId());
            customerStageDataBusinessView.setSalerName(employeeThirdService.getEmployeeData(cmCustProtect.getSalerId()).orElse(new EmployeeDataThirdView()).getName());
            customerStageEnum = getCustomerStageEnum(cmCustProtect);
        }
        customerStageDataBusinessView.setCustomerStageEnum(customerStageEnum);
        return customerStageDataBusinessView;
    }

    /**
     * 获取客户阶段枚举
     * @param cmCustProtect 保护关系数据
     * <AUTHOR>
     * @date 2024/5/21 下午4:02
     * @return com.ce.scrm.center.service.enums.CustomerStageEnum
     **/
    private @NotNull CustomerStageEnum getCustomerStageEnum(CmCustProtect cmCustProtect) {
        if (ProtectStateEnum.CUSTOMER_POOL.getState().equals(cmCustProtect.getStatus())) {
            return CustomerStageEnum.OPEN_SEA;
        }
        if (orgThirdService.gcAreaFlag(cmCustProtect.getAreaId())) {
            return CustomerStageEnum.GC_PROTECT;
        }
        return CustomerStageEnum.CE_PROTECT;
    }


    /***
     * 获取客户拜访率
     * @param loginPosition
     * @param loginAreaId
     * @param loginSubId
     * @param loginOrgId
     * @param loginEmployeeId
     * @return
     */
    public HomePageCallRateWebView getCallRate(String loginPosition, String loginAreaId, String loginSubId, String loginOrgId, String loginEmployeeId) {
        if(EmpPositionConstant.BUSINESS_SYS_MANAGER.equals(loginPosition)) {
            return new HomePageCallRateWebView();
        }
        return customerThirdService.getCallRate(loginPosition, loginAreaId, loginSubId, loginOrgId, loginEmployeeId);
    }

    /**
     * 获取打卡内页列表
     * @param callDetailsDubboDto
     * @return
     */
    public DubboPageInfo<CallDetailsWebView> getCallDetails(CallDetailsDubboDto callDetailsDubboDto) {

        // 调用第三方接口获取客户分页数据
        DubboPageInfo<CustomerDubboView> callDetails = customerThirdService.getCallDetails(callDetailsDubboDto);
        if(callDetails == null || CollectionUtils.isEmpty(callDetails.getList())) {
            return new DubboPageInfo<>();
        }

        // 获取区域map
        List<String> areaIdList = callDetails.getList().stream().map(CustomerDubboView::getProtectAreaId).collect(Collectors.toList());
        Map<String, OrgDataThirdView> areaMap = orgThirdService.getOrgData(areaIdList);

        // 获取分司map
        List<String> subIdList = callDetails.getList().stream().map(CustomerDubboView::getProtectSubcompanyId).collect(Collectors.toList());
        Map<String, OrgDataThirdView> subIdMap = orgThirdService.getOrgData(subIdList);

        // 获取部门map
        List<String> deptIdList = callDetails.getList().stream().map(CustomerDubboView::getProtectBussdeptId).collect(Collectors.toList());
        Map<String, OrgDataThirdView> deptIdMap = orgThirdService.getOrgData(deptIdList);

        // 获取员工map
        List<String> salerIdList = callDetails.getList().stream().map(CustomerDubboView::getProtectSalerId).collect(Collectors.toList());
        Map<String, EmployeeInfoThirdDto> salerIdMap = employeeThirdService.getEmployeeDataMap(salerIdList);

        DubboPageInfo<CallDetailsWebView> result = BeanUtil.copyProperties(callDetails, DubboPageInfo.class);

        // 处理结果集
        List<CallDetailsWebView> callDetailsWebViewList = callDetails.getList().stream().map(item -> {

            // 成交/未成交显示处理
            String protectCustType = item.getProtectCustType();
            Boolean bargainFlag = null;
            if("2".equals(protectCustType)) {
                bargainFlag = false;
            }else if("3".equals(protectCustType) || "4".equals(protectCustType)) {
                bargainFlag = true;
            }

            return CallDetailsWebView.builder()
                    .areaId(item.getProtectAreaId())
                    // 获取 areaMap  key = item.getProtectAreaId() 对象name  两层判空
                    .areaName(areaMap == null ? "" :
                            areaMap.get(item.getProtectAreaId()) == null ? "" : areaMap.get(item.getProtectAreaId()).getName())
                    .subId(item.getProtectSubcompanyId())
                    .subName(subIdMap == null ? "" :
                            subIdMap.get(item.getProtectSubcompanyId()) == null ? "" : subIdMap.get(item.getProtectSubcompanyId()).getName())
                    .deptId(item.getProtectBussdeptId())
                    .deptName(deptIdMap == null ? "" :
                            deptIdMap.get(item.getProtectBussdeptId()) == null ? "" : deptIdMap.get(item.getProtectBussdeptId()).getName())
                    .salerId(item.getProtectSalerId())
                    .salerName(salerIdMap == null ? "" :
                            salerIdMap.get(item.getProtectSalerId()) == null ? "" : salerIdMap.get(item.getProtectSalerId()).getName())
                    .custId(item.getCustomerId())
                    .custName(item.getCustomerName())
                    .protectProtectTime(item.getProtectProtectTime())
                    .cdpProtectDay(item.getCdpProtectDay())
                    .cdpCurrentMonthClockCount(item.getCdpCurrentMonthClockCount())
                    .cdpClockCount(item.getCdpClockCount())
                    .bargainFlag(bargainFlag)
                    .build();
        }).collect(Collectors.toList());

        result.setList(callDetailsWebViewList);
        return result;
    }

    public CustomerTwoNameBusinessView getCustomerTwoNameByName(String name) {

        DubboResult<BigDataCompanyDetailView> dubboResult =  iBigDataDubbo.getCompanyDetailByName(name);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            log.info("根据名字查询大数据接口，返回数据失败,name={}" , name);
            return CustomerTwoNameBusinessView.builder().build();
        }

        BigDataCompanyDetailView companyDetailView = dubboResult.getData();
        String entName = companyDetailView.getEntname();
        CustomerTwoNameBusinessView.CustomerTwoNameBusinessViewBuilder builder = CustomerTwoNameBusinessView.builder();
        if(!Objects.equals(entName,name)) {
            //如果现用名和参数name不相等，那么就是有new并且使用entName查询新的
            CmCustProtect newProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustName, entName).one();
            if(newProtect != null && Objects.equals(newProtect.getStatus(),ProtectStateEnum.PROTECT.getState())) {
                Optional<EmployeeDataThirdView> employeeData = employeeThirdService.getEmployeeData(newProtect.getSalerId());
                if(employeeData.isPresent()) {
                    EmployeeDataThirdView employee = employeeData.get();
                    CmaCustProtectDto newCustProtect = CmaCustProtectDto.builder()
                            .custId(newProtect.getCustId())
                            .custName(newProtect.getCustName())
                            .salerId(employee.getId())
                            .salerName(employee.getName())
                            .bussdeptId(employee.getOrgId())
                            .bussdeptName(employee.getOrgName())
                            .buId(employee.getBuId())
                            .buName(employee.getBuName())
                            .subcompanyId(employee.getSubId())
                            .subcompanyName(employee.getSubName())
                            .stage(CmCustProtectUtil.getCustLocation(newProtect.getStatus(),newProtect.getCustType(),newProtect.getSource()))
                            .build();
                    builder.newCustomer(newCustProtect);
                }
            }else {
                CmaCustProtectDto newCustProtect = CmaCustProtectDto.builder()
                        .custName(entName).build();
                builder.newCustomer(newCustProtect);
            }

        }

        CmCustProtect oldProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustName, name).one();
        if(oldProtect != null && Objects.equals(oldProtect.getStatus(),ProtectStateEnum.PROTECT.getState())) {
            Optional<EmployeeDataThirdView> employeeData = employeeThirdService.getEmployeeData(oldProtect.getSalerId());
            if(employeeData.isPresent()) {
                EmployeeDataThirdView employee = employeeData.get();
                CmaCustProtectDto oldCustProtect = CmaCustProtectDto.builder()
                        .custId(oldProtect.getCustId())
                        .custName(oldProtect.getCustName())
                        .salerId(employee.getId())
                        .salerName(employee.getName())
                        .bussdeptId(employee.getOrgId())
                        .bussdeptName(employee.getOrgName())
                        .buId(employee.getBuId())
                        .buName(employee.getBuName())
                        .subcompanyId(employee.getSubId())
                        .subcompanyName(employee.getSubName())
                        .stage(CmCustProtectUtil.getCustLocation(oldProtect.getStatus(),oldProtect.getCustType(),oldProtect.getSource()))
                        .build();
                builder.oldCustomer(oldCustProtect);
            }
        }else {
            CmaCustProtectDto oldCustProtect = CmaCustProtectDto.builder()
                    .custName(entName).build();
            builder.oldCustomer(oldCustProtect);
        }
        return builder.build();
    }

    public List<CustomerByPhoneBusinessView> getCustomerByPhone(CustomerByPhoneBusinessDto customerByPhoneBusinessDto) {
        log.info("根据联系方式查询客户信息,参数customerByPhoneBusinessDto={}" , JSONObject.toJSONString(customerByPhoneBusinessDto));

        String phone = customerByPhoneBusinessDto.getPhone();

        EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
        if(currentUser == null) {
            log.error("没有登录人");
            throw new RuntimeException("没有登录人");
        }
        String loginEmployeeId = currentUser.getId();

        // 根据手机号查询搜客宝
        List<BigDataSkbCompanyByPhoneView> companyByPhoneListByPhone = bigDataThirdService.getCompanyByPhoneListByPhone(phone);
        if(CollectionUtil.isEmpty(companyByPhoneListByPhone)) {
            return Collections.emptyList();
        }
        List<String> pidList = companyByPhoneListByPhone.stream().map(BigDataSkbCompanyByPhoneView::getPid).collect(Collectors.toList());
        List<BigDataSkbCompanyView> customerList = bigDataThirdService.getSkbCompanyDetailByPidList(pidList);

        // 获取所有行业
        List<IndustryThirdView> allIndustry = industryThirdService.getAllData();
        if(CollectionUtil.isEmpty(allIndustry)) {
            throw new RuntimeException("获取所有行业失败");
        }
        Map<String, String> industryMap = allIndustry.stream().collect(Collectors.toMap(IndustryThirdView::getCode, IndustryThirdView::getName, (o1, o2) -> o2));

        List<CustomerByPhoneBusinessView> result = new ArrayList<>();
        customerList.forEach(customer -> {

            //build联系人数据
            BigDataContactPersonView contactPersonDetailByPid = bigDataThirdService.getContactPersonDetailByPid(customer.getPid());
            if(contactPersonDetailByPid == null) {
                log.error("通过pid反查询联系人数据数据异常，pid={}", customer.getPid());
                return;
            }
            List<BigDataContactsView> list = contactPersonDetailByPid.getList();
            if (CollectionUtil.isEmpty(list)) {
                log.error("通过pid反查询联系人数据数据异常，pid={}", customer.getPid());
                return;
            }
            List<BigDataContactsView> contactsViews = list.stream().filter(l -> phone.equals(l.getContent())).collect(Collectors.toList());
            contactsViews.forEach( contactsView -> {

                CustomerByPhoneBusinessView item = BeanCopyUtils.convertToVo(customer, CustomerByPhoneBusinessView.class);

                //build客户状态
                item.setEntStatus(CustomerRegisterStageEnum.getNameByCode(item.getEntStatus()));

                //build成立日期
                Date date = customer.getEsdate()==null?null:new Date(customer.getEsdate());
                item.setCreateData(date);

                //build联系人信息
                item.setContactPersonName(contactsView.getContact());
                item.setPositionName(contactsView.getPosition());
                item.setContactWay(contactsView.getContent());

                //build是否是精准联系人
                item.setExactContactPersonFlag(contactsView.getAccurate_ent() ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());

                //build市场
                CustomerMarketDto customerMarket = smaMarketBusiness.getCustomerMarket(customer.getSecdistrict(),customer.getDistrict() ,currentUser.getSubId());
                item.setMarketName(customerMarket.getMarketName());

                //build所属行业
                // 全查回来匹配
                item.setFirstIndustry(industryMap.get(item.getFirstIndustry()));

                //build客户状态
                try {
                    CustomerStageInfoDto customerStageByCustomerName = customerStageAppService.getCustomerStageByCustomerName(item.getEntName(), loginEmployeeId);
                    if(customerStageByCustomerName != null) {
                        cn.ce.cesupport.enums.CustomerStageEnum customerStageEnum = customerStageByCustomerName.getCustomerStageEnum();
                        item.setCustomerState(customerStageEnum.getName());
                    }
                }catch (Exception e){
                    log.info("处理失败，查找客户位置返回空，customerName={},loginEmployeeId={}", item.getEntName(),loginEmployeeId);
                    throw new RuntimeException(e);
                }

                //build可收藏
                item.setFavoriteFlag(
                        favoriteBusiness.getFavoriteFlag(
                                item.getSocialCreditCode(),
                                item.getEntName(),
                                customerMarket.getMyMarketFlag(),
                                currentUser.getPosition()
                        )
                );

                //build可保护
                item.setProtectFlag(
                        protectBusiness.getProtectFlag(
                                item.getEntName(),
                                customerMarket.getMyMarketFlag(),
                                currentUser.getPosition()
                        )
                );

                result.add(item);
            });
        });
        log.info("根据联系方式查询客户信息,结果result={}" , JSONObject.toJSONString(result));
        return result;

    }

	/**
	 * 查询主要人员关联的企业信息
	 * @param affiliatedEnterpriseBusinessDto pid name type
	 */
	public CustomerAffiliatedEnterpriseBusinessView getAffiliatedEnterprise(CustomerAffiliatedEnterpriseBusinessDto affiliatedEnterpriseBusinessDto) {
		EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
		Assert.notNull(currentUser, "登录人获取失败");
		CustomerAffiliatedEnterpriseBusinessView businessView = new CustomerAffiliatedEnterpriseBusinessView();
		try {
            // 大客户
            List<String> subIds = Lists.newArrayList("4192", "4195", "4197");
            boolean dakehu = subIds.contains(currentUser.getSubId());
            // condition1：当前登录人是大客户(subIds是北京、上海、广州)
            BigDataAffiliatedEnterpriseView affiliatedEnterprise = bigDataThirdService.getAffiliatedEnterprise(affiliatedEnterpriseBusinessDto.getPid(), affiliatedEnterpriseBusinessDto.getName());
            Assert.notNull(affiliatedEnterprise, "查询主要人员关联的企业信息为空");
            businessView.setTotal(affiliatedEnterprise.getTotal());
            if (YesOrNoEnum.NO.getCode().equals(affiliatedEnterpriseBusinessDto.getType())) {
                businessView.setItems(Collections.emptyList());
                return businessView;
            }
            if (!CollectionUtils.isEmpty(affiliatedEnterprise.getItems())) {
                List<CustomerAffiliatedEnterpriseBusinessView.CustomerAffiliatedEnterpriseDetailBusinessView> copyResList = BeanCopyUtils.convertToVoList(
                        affiliatedEnterprise.getItems(),
                        CustomerAffiliatedEnterpriseBusinessView.CustomerAffiliatedEnterpriseDetailBusinessView.class);
                    // 填充字段 市场、客户保护状态、是否可保护
                    businessView.setItems(copyResList);
            }
			log.info("主要人员关联的企业信息: type1");
            Optional.ofNullable(businessView.getItems()).orElse(Lists.newArrayList()).forEach(item->{
                //客户阶段
                CustomerStageInfoDto customerStageByCustomerName = customerStageAppService.getCustomerStageByCustomerName(item.getEntName(), currentUser.getId());
                cn.ce.cesupport.enums.CustomerStageEnum customerStageEnum = customerStageByCustomerName.getCustomerStageEnum();
                BigDataCompanyDetailByNameView companyDetailByPid = bigDataThirdService.getCompanyDetailByName(item.getEntName());

                //所属市场
                CustomerMarketDto customerMarket = smaMarketBusiness.getCustomerMarket(
                        "", companyDetailByPid.getCity_code(), currentUser.getSubId());

                item.setUncid(companyDetailByPid.getUncid());
                item.setPid(companyDetailByPid.getPid());
                item.setCustId(customerStageByCustomerName.getCustomerId());
                item.setMarketName(customerMarket.getMarketName());
                item.setCustomerState(customerStageEnum.getName());

                List<String> interfaceType1 = Lists.newArrayList(cn.ce.cesupport.enums.CustomerStageEnum.NO_CUST.getName(),
                        cn.ce.cesupport.enums.CustomerStageEnum.CLUE_POOL.getName(),
                        cn.ce.cesupport.enums.CustomerStageEnum.MY_CLUECUST.getName());

                List<String> interfaceType2 = Lists.newArrayList(cn.ce.cesupport.enums.CustomerStageEnum.CUST_POOL.getName(),
                        cn.ce.cesupport.enums.CustomerStageEnum.OPEN_SEA.getName());

                if (interfaceType1.contains(item.getCustomerState())) {
                    item.setInterfaceType(1);
                } else if (interfaceType2.contains(item.getCustomerState())) {
                    item.setInterfaceType(2);
                }
                if (dakehu){
                    // 大客户逻辑
                    // 判断 主 客户是否是成交客户
                    if (checkChengJiaoByCustomerName(affiliatedEnterpriseBusinessDto.getCustomerName())){
                        if (customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.CLUE_POOL)
                                || customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.OPEN_SEA)
                                || customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.NO_CUST)
                                || customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.CUST_POOL)
                        ) {
                            item.setProtectFlag(YesOrNoEnum.YES.getCode());
                        }else{
                            item.setProtectFlag(YesOrNoEnum.NO.getCode());
                        }
                    }else{
                        // 非成交 需要看市场 和 状态
                        if (Objects.equals(customerMarket.getMyMarketFlag(),1)){
                            if (customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.CLUE_POOL)
                                    || customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.OPEN_SEA)
                                    || customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.NO_CUST)
                                    || customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.CUST_POOL)
                            ) {
                                item.setProtectFlag(YesOrNoEnum.YES.getCode());
                            }else{
                                item.setProtectFlag(YesOrNoEnum.NO.getCode());
                            }
                        }else{
                            item.setProtectFlag(YesOrNoEnum.NO.getCode());
                        }
                    }
                }else{
                    // 非大客户逻辑
                    if (Objects.equals(customerMarket.getMyMarketFlag(),1)){
                        if (customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.CLUE_POOL)
                                || customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.OPEN_SEA)
                                || customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.NO_CUST)
                                || customerStageEnum.equals(cn.ce.cesupport.enums.CustomerStageEnum.CUST_POOL)
                        ) {
                            item.setProtectFlag(YesOrNoEnum.YES.getCode());
                        }else{
                            item.setProtectFlag(YesOrNoEnum.NO.getCode());
                        }
                    }else{
                        item.setProtectFlag(YesOrNoEnum.NO.getCode());
                    }
                }
            });
		} catch (Exception e) {
			log.error("CustomerBusiness#getAffiliatedEnterprise call bigDataThirdService#getAffiliatedEnterprise method error: {}", e.getMessage());
		}
		return businessView;
	}


    /***
     * 判断主客户是否是成交客户
     * @param customerName
     * <AUTHOR>
     * @date 2024/12/20 22:02
     * @version 1.0.0
     * @return boolean
    **/
    public boolean checkChengJiaoByCustomerName(String customerName) {
        // 查询保护状态为线索池或客户池
        CustProtectView cmCustProtect = cmCustProtectService.selectOneByCondition(
                CmCustProtect.builder()
                        .custName(customerName)
                        .build()
        );
        if (cmCustProtect == null) {
            return false;
        }
        List<Integer> custTypes = Lists.newArrayList(ProtectCustTypeEnum.ORDERED.getValue(), ProtectCustTypeEnum.SECOND_DEVELOPMENT.getValue());
        if (Objects.nonNull(cmCustProtect)
                && ProtectStatusEnum.PROTECT.getCode().equals(cmCustProtect.getStatus())
                && custTypes.contains(cmCustProtect.getCustType())
        ) {
            return true;
        }
        return false;
    }

	/**
	 * 该客户是否为历史名称
	 * @param customerName 客户名称
	 * @return 是否为历史名称
	 */
	public Boolean hasHistoryNameFlag(String customerName) {
		try {
			return bigDataThirdService.getHistoryNameFlag(customerName);
		} catch (Exception e) {
			log.warn("CustomerBusiness#hashistoryNameFlag call getHistoryNameFlag method error: {}", e.getMessage());
		}
		return false;
	}

	/**
	 * 附近20km中的成交客户
	 * @param dto
	 * @return 客户列表
	 */
	public Page<CustomerNearByTwentyBusinessView> nearbyTwentyKmCustomerPage(CustomerNearByTwentyBusinessDto dto) {
		// step1: 获取客户的pid
		String pid = dto.getPid();
		String secondIndustryCode;
		Optional<CustomerDataThirdView> customerDataThirdView = customerThirdService.getCustomerData(dto.getCustId());
		if (customerDataThirdView.isPresent()) {
			secondIndustryCode = customerDataThirdView.get().getSecondIndustryCode();
			if (StringUtils.isBlank(pid)) {
				pid = customerDataThirdView.get().getSourceDataId();
			}
		} else {
			secondIndustryCode = null;
		}
		// step2: 获取当前客户经纬度信息
		CompanyInfoDubboView view = iCompanyInfoEsDubbo.getByPid(pid);
		if (Objects.isNull(view) || StringUtil.isBlank(view.getLocation())) {
			return Page.of(dto.getPageNum(), dto.getPageSize(), 0L);
		}
		// 经纬度
		JSONObject location = JSON.parseObject(view.getLocation());
		String lon = location.getString("lon");
		String lat = location.getString("lat");
		// step3: 从ES查询附近20km的客户 过滤同二级行业的客户 & 附近20km的成交客户
		NearbyCustomersFromESRequest esRequest = new NearbyCustomersFromESRequest();
		esRequest.setPage(dto.getPageNum());
		esRequest.setSize(dto.getPageSize());
		esRequest.setDistanceRange(dto.getDistanceRangeMeter());
		esRequest.setIndustry(Lists.newArrayList(secondIndustryCode).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
		esRequest.setLongitude(lon);
		esRequest.setLatitude(lat);
		esRequest.setFlag1In(Lists.newArrayList("6"));
		DubboResult<com.ce.scrm.extend.dubbo.entity.response.DubboPageInfo<CompanyInfoDubboView>> dubboResult = iCompanyInfoEsDubbo.getNearbyCustomersFromES(esRequest);
		if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
			log.error("从ES查询附近20km的客户失败，pid={}", pid);
			return Page.of(dto.getPageNum(), dto.getPageSize(), 0L);
		}
		List<CompanyInfoDubboView> list = dubboResult.getData().getList();
		if (CollectionUtils.isEmpty(list)) {
			return Page.of(dto.getPageNum(), dto.getPageSize(), 0L);
		}
		log.info("从ES查询附近的客户成功，过滤前，入参：{}, 结果条数：{}, 客户名列表: {}", JSON.toJSONString(esRequest), list.size(), JSON.toJSONString(Lists.transform(list, CompanyInfoDubboView::getEntName)));
		List<CustomerNearByTwentyBusinessView> busViews = new ArrayList<>();
		list.forEach(item -> {
			CustomerNearByTwentyBusinessView busView = new CustomerNearByTwentyBusinessView();
			busView.setCustName(item.getEntName());
			Optional<CustomerDataThirdView> customerDataByCustomerName = customerThirdService.getCustomerDataByCustomerName(item.getEntName());
			customerDataByCustomerName.ifPresent(dataThirdView -> busView.setCustId(dataThirdView.getCustomerId()));
			busViews.add(busView);
		});
		Page<CustomerNearByTwentyBusinessView> page = Page.of(dto.getPageNum(), dto.getPageSize(), dubboResult.getData().getTotal());
		page.setPages(dubboResult.getData().getPages());
		page.setRecords(busViews);
		return page;
	}

	/**
	 * 附近20km中的成交客户 - 不分页
	 * @return 客户列表
	 */
	public List<CustomerNearByTwentyBusinessView> nearbyTwentyKmCustomerList(CustomerNearByTwentyBusinessDto dto) {
		// step1: 获取客户的pid
		String pid = dto.getPid();
		String secondIndustryCode;
		Optional<CustomerDataThirdView> customerDataThirdView = customerThirdService.getCustomerData(dto.getCustId());
		if (customerDataThirdView.isPresent()) {
			secondIndustryCode = customerDataThirdView.get().getSecondIndustryCode();
			if (StringUtils.isBlank(pid)) {
				pid = customerDataThirdView.get().getSourceDataId();
			}
		} else {
			secondIndustryCode = null;
		}
		// step2: 获取当前客户经纬度信息
		CompanyInfoDubboView view = iCompanyInfoEsDubbo.getByPid(pid);
		if (Objects.isNull(view) || StringUtil.isBlank(view.getLocation())) {
			return Collections.emptyList();
		}
		// 经纬度
		JSONObject location = JSON.parseObject(view.getLocation());
		String lon = location.getString("lon");
		String lat = location.getString("lat");
		// step3: 从ES查询附近20km的客户 过滤同二级行业的客户 & 附近20km的成交客户
		NearbyCustomersFromESRequest esRequest = new NearbyCustomersFromESRequest();
		esRequest.setPage(1);
		esRequest.setSize(1000); // 默认最多1000条
		esRequest.setDistanceRange(dto.getDistanceRangeMeter());
		esRequest.setIndustry(Lists.newArrayList(secondIndustryCode).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
		esRequest.setLongitude(lon);
		esRequest.setLatitude(lat);
		esRequest.setFlag1In(Lists.newArrayList("6"));
		DubboResult<com.ce.scrm.extend.dubbo.entity.response.DubboPageInfo<CompanyInfoDubboView>> dubboResult = iCompanyInfoEsDubbo.getNearbyCustomersFromES(esRequest);
		if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
			log.error("从ES查询附近20km的客户列表失败，pid={}", pid);
			return Collections.emptyList();
		}
		List<CompanyInfoDubboView> list = dubboResult.getData().getList();
		if (CollectionUtils.isEmpty(list)) {
			return Collections.emptyList();
		}
		List<CmCustProtect> cmCustProtectList = cmCustProtectService.lambdaQuery()
			.in(CmCustProtect::getCustType, Lists.newArrayList(ProtectCustTypeEnum.ORDERED.getValue(), ProtectCustTypeEnum.SECOND_DEVELOPMENT.getValue()))
			.in(CmCustProtect::getCustName, Lists.transform(list, CompanyInfoDubboView::getEntName))
			.list();
		return BeanCopyUtils.convertToVoList(cmCustProtectList, CustomerNearByTwentyBusinessView.class);
	}

    public CustomerLocationBusinessView getCustomerLocationInfo(@Valid QueryCustomerLocationBusinessDto queryDto) {

        CustomerLocationBusinessView result = new CustomerLocationBusinessView();

        // 如果登录人是商务角色  查询客户位置信息
        if (PositionUtil.isBusinessSaler(queryDto.getLoginPosition())) {
            try {
                CustomerStageInfoDto customerStageByCustomerName = customerStageAppService.getCustomerStageByCustomerName(queryDto.getCustomerName(), queryDto.getLoginEmployeeId());
                if(customerStageByCustomerName != null) {
                    cn.ce.cesupport.enums.CustomerStageEnum customerStageEnum = customerStageByCustomerName.getCustomerStageEnum();
                    result.setCustomerLocationStr(customerStageEnum.getName());
                }
            }catch (Exception e){
                log.info("处理失败，查找客户位置返回空，customerName={},loginEmployeeId={}", queryDto.getCustomerName(), queryDto.getLoginEmployeeId());
//                throw new RuntimeException(e);
            }
        }
        Optional<CustomerDataThirdView> customerDataByCustomerName = customerThirdService.getCustomerDataByCustomerName(queryDto.getCustomerName());
        if (customerDataByCustomerName.isPresent()) {
            CustomerDataThirdView customerDataThirdView = customerDataByCustomerName.get();
            result.setCustomerId(customerDataThirdView.getCustomerId());
        }
        return result;
    }

    public CustomerBusinessView getCustomerByCustomerName(@Valid QueryCustomerBusinessDto queryCustomerBusinessDto) {
        Optional<CustomerDataThirdView> customerDataByCustomerName = customerThirdService.getCustomerDataByCustomerName(queryCustomerBusinessDto.getCustomerName());
        if (customerDataByCustomerName.isPresent()) {
            CustomerDataThirdView customerDataThirdView = customerDataByCustomerName.get();
            CustomerBusinessView customerBusinessView = BeanCopyUtils.convertToVo(customerDataThirdView, CustomerBusinessView.class);
            return customerBusinessView;
        }
        return null;
    }

    /***
     * 根据搜客宝id获取客户信息
     * @param pid
     * <AUTHOR>
     * @date 2025/6/12 14:54
     * @version 1.0.0
     * @return com.ce.scrm.center.service.business.entity.view.customer.CustomerBusinessView
    **/
    public CustomerBusinessView getCustomerFromSkbByPid(String pid) {
        List<CompanyInfoDubboView> companyInfoDubboViewList = iCompanyInfoEsDubbo.listByPid(pid);
        if (CollectionUtils.isEmpty(companyInfoDubboViewList)){
            return null;
        }
        CompanyInfoDubboView companyInfoDubboView = companyInfoDubboViewList.get(0);
        CustomerBusinessView customerBusinessView = new CustomerBusinessView();
        customerBusinessView.setSourceDataId(pid);
        customerBusinessView.setCustomerName(companyInfoDubboView.getEntName());
        customerBusinessView.setCertificateCode(companyInfoDubboView.getUncid());
        customerBusinessView.setProvinceCode(companyInfoDubboView.getProvince());
        customerBusinessView.setProvinceName(companyInfoDubboView.getProvinceName());
        customerBusinessView.setCityCode(companyInfoDubboView.getCity());
        customerBusinessView.setCityName(companyInfoDubboView.getCityName());
        customerBusinessView.setDistrictCode(companyInfoDubboView.getDistrict());
        customerBusinessView.setDistrictName(companyInfoDubboView.getDistrictName());
        customerBusinessView.setB2bProduct(companyInfoDubboView.getB2bProduct());
        return customerBusinessView;
    }

    /**
     * <AUTHOR>
     * @date 2025/7/16 10:18:52
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo<com.ce.scrm.customer.dubbo.entity.view.CustomerESDubboView>
     * @desc 从客户ES中获取客户数据
     */
    public DubboPageInfo<CustomerESBusinessView> pageListFromCustomerES(CustomerESPageBusinessDto dto){
        CustomerESPageDubboDto customerESPageDubboDto = BeanUtil.copyProperties(dto, CustomerESPageDubboDto.class);
        // 调用第三方接口从客户ES中获取客户分页数据
        DubboPageInfo<CustomerESDubboView> result = customerThirdService.pageListFromCustomerES(customerESPageDubboDto);
        if (result == null || CollectionUtils.isEmpty(result.getList())) {
            return new DubboPageInfo<>();
        }
        DubboPageInfo<CustomerESBusinessView> dubboPageInfo = new DubboPageInfo<>();
        BeanUtil.copyProperties(result, dubboPageInfo);
        dubboPageInfo.setList(BeanCopyUtils.convertToVoList(Optional.ofNullable(result.getList())
                .orElse(Collections.emptyList()), CustomerESBusinessView.class));

        List<CustomerESBusinessView> listDubboView = dubboPageInfo.getList();
        List<String> listCustomerIds = listDubboView.stream().map(CustomerESBusinessView::getCustomerId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, List<CustomerLeads>> mapLeads = customerLeadsBusiness.getCustIdLeadsMap(listCustomerIds);
        Map<String, List<CustomerTagDubboView>> tagList = customerTagsThirdService.tagList(listCustomerIds);
        // 将 leads 设置到每个 record 中
        listDubboView.forEach(view -> {
            String customerId = view.getCustomerId();
            if (customerId != null && mapLeads.containsKey(customerId)) {
                List<CustomerLeadsDubboView> customerLeadsDubboViews = BeanUtil.copyToList(mapLeads.get(customerId), CustomerLeadsDubboView.class);
                view.setCustomerLeadsList(customerLeadsDubboViews);
            } else {
                // 如果没有 leads，可以设置为空列表
                view.setCustomerLeadsList(Collections.emptyList());
            }
            if (customerId != null && tagList.containsKey(customerId)) {
                List<CustomerTagDubboView> customerTagsDubboViews = BeanUtil.copyToList(tagList.get(customerId), CustomerTagDubboView.class);
                view.setCustomerTagList(customerTagsDubboViews);
            } else {
                // 如果没有tags，可以设置为空列表
                view.setCustomerTagList(Collections.emptyList());
            }
            CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
            customerDetailDubboDto.setCustomerId(customerId);
            customerDetailDubboDto.setSourceKey(customerESPageDubboDto.getSourceKey());
            customerDetailDubboDto.setSourceSecret(customerESPageDubboDto.getSourceSecret());
            List<ContactPersonDataDubboView> listResult = customerLinkmanThirdService.getCustomerData(customerDetailDubboDto);
            List<ContactPersonBusinessView> listView = BeanCopyUtils.convertToVoList(Optional.ofNullable(listResult).orElse(Collections.emptyList()), ContactPersonBusinessView.class);
            //首个联系人增加能否拨打状态判断
            if (CollectionUtil.isNotEmpty(listView)) {
                ContactPersonBusinessView contactView = listView.get(0);

                CallStatusBusinessDto callStatusBusinessDto = CallStatusBusinessDto.builder()
                        .empId(dto.getLoginEmployeeId())
                        .subId(dto.getLoginSubId())
                        .phones(Collections.singletonList(contactView.getPhone()))
                        .build();
                CallStatusThirdDto callStatus = callCenterThirdService.getCallStatus(callStatusBusinessDto);
                if (callStatus != null) {
                    Map<String, CallStatusThirdDto.CallCenterStatusThirdDto> callStatusMap = callStatus.getCallcenterStatusViewMap();
                    if (MapUtils.isNotEmpty(callStatusMap)) {
                        CallStatusThirdDto.CallCenterStatusThirdDto callCenterStatusThirdDto = callStatusMap.get(contactView.getPhone());
                        if (callCenterStatusThirdDto != null) {
                            contactView.setCallStatus(callCenterStatusThirdDto.getCallStatus());
                            contactView.setCallMessage(callCenterStatusThirdDto.getCallMessage());
                            contactView.setLineCode(callCenterStatusThirdDto.getLineCode());
                            contactView.setLineName(callCenterStatusThirdDto.getLineName());
                            contactView.setLineType(callCenterStatusThirdDto.getLineType());
                            contactView.setCallSubId(callCenterStatusThirdDto.getSubId());
                        }
                    }
                }
            }
            view.setContactPersonList(listView);
        });

        return dubboPageInfo;
    }


    /**
     * <AUTHOR>
     * @date 2025/8/12 09:44:15
     * @desc 获取更多电话号码的拨打状态
     */
    public List<ContactPersonBusinessView> getMoreContactWithCallStatus(ContactPersonCallStatusDto dto, boolean isSkipFirst) {

        List<String> listCustomerIds = Collections.singletonList(dto.getCustomerId());
        String customerId = dto.getCustomerId();
        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setCustomerId(customerId);
        customerDetailDubboDto.setSourceKey(dto.getSourceKey());
        customerDetailDubboDto.setSourceSecret(dto.getSourceSecret());
        List<ContactPersonDataDubboView> listResult = customerLinkmanThirdService.getCustomerData(customerDetailDubboDto);
        List<ContactPersonBusinessView> listView = BeanCopyUtils.convertToVoList(Optional.ofNullable(listResult).orElse(Collections.emptyList()), ContactPersonBusinessView.class);

        //跳过第一条记录
        if (!CollectionUtils.isEmpty(listView) && isSkipFirst) {
            listView.remove(0);
        }

        List<String> phones = listView.stream().map(ContactPersonBusinessView::getPhone).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        //联系人增加能否拨打状态判断
        if (!CollectionUtil.isEmpty(phones)) {

            CallStatusBusinessDto callStatusBusinessDto = CallStatusBusinessDto.builder()
                    .empId(dto.getLoginEmployeeId())
                    .subId(dto.getLoginSubId())
                    .phones(phones)
                    .build();
            CallStatusThirdDto callStatus = callCenterThirdService.getCallStatus(callStatusBusinessDto);
            if (callStatus != null && CollectionUtil.isNotEmpty(listView)) {
                Map<String, CallStatusThirdDto.CallCenterStatusThirdDto> callStatusMap = callStatus.getCallcenterStatusViewMap();
                listView.forEach(item -> {
                    if (MapUtils.isNotEmpty(callStatusMap)) {
                        CallStatusThirdDto.CallCenterStatusThirdDto callCenterStatusThirdDto = callStatusMap.get(item.getPhone());
                        if (callCenterStatusThirdDto != null) {
                            item.setCallStatus(callCenterStatusThirdDto.getCallStatus());
                            item.setCallMessage(callCenterStatusThirdDto.getCallMessage());
                            item.setLineCode(callCenterStatusThirdDto.getLineCode());
                            item.setLineName(callCenterStatusThirdDto.getLineName());
                            item.setLineType(callCenterStatusThirdDto.getLineType());
                            item.setCallSubId(callCenterStatusThirdDto.getSubId());
                        }
                    }
                });
            }
        }
        return listView;
    }

    /***
     * 只查客户表
     * @param custId
     * <AUTHOR>
     * @date 2025/8/7 19:03
     * @version 1.0.0
     * @return java.util.Optional<com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView>
    **/
    public Optional<CustomerDataThirdView> getCustomerDataByCustId(String custId) {
        return customerThirdService.getCustomerData(custId);
    }

	/**
	 * 获取abm客户标签列表
	 * @param customerId 客户id
	 * @return 客户标签列表
	 */
	public List<CustomerTagsBusinessView> getCustomerTags(String customerId) {
		Map<String, List<CustomerTagDubboView>> custIdTagsMap = customerTagsThirdService.tagList(Collections.singletonList(customerId));
		if (!CollectionUtils.isEmpty(custIdTagsMap)) {
			List<CustomerTagDubboView> tags = custIdTagsMap.get(customerId);
			return BeanCopyUtils.convertToVoList(tags, CustomerTagsBusinessView.class);
		}
		return Collections.emptyList();
	}
}