package com.ce.scrm.center.service.business.entity.dto.segment;

import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 根据角色查询分群入参条件
 * @Author: lijinpeng
 * @Date: 2025/2/27 14:24
 */
@Data
public class RemoveGroupSegmentBusinessDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	private Integer id;

	/**
	 * cdp分群code
	 */
	private String segmentId;

	/**
	 * 员工ID
	 */
	private String loginEmployeeId;

}
