package com.ce.scrm.center.service.xfppt;



import java.io.File;

/**
 * 智能PPT生成（新版）demo，注意：新版的接口和旧版的不能混用~
 * 正式调用前，请先阅读接口文档中的调用流程图！！！
 * 接口文档地址：https://www.xfyun.cn/doc/spark/PPTv2.html
 * 控制台地址：https://console.xfyun.cn/services/zwapi
 * 产品页地址：https://www.xfyun.cn/services/aippt
 */
public class Main {
    public static final String APPID = "e1fbdf40";
    public static final String APISecret = "NmFmYjNiYzdjNWU2ODc5MDMwZWVkMzQy";

    public static void main(String[] args) throws Exception {
        // 获取请求头中需要携带的参数 appId（控制台获取）, timestamp（时间戳，单位：秒，与服务端时间相差五分钟之内）, signature（签名）
        long timestamp = System.currentTimeMillis() / 1000;
        String ts = String.valueOf(timestamp);
        ApiAuthAlgorithm auth = new ApiAuthAlgorithm();
        String signature = auth.getSignature(APPID, APISecret, timestamp);
        // 建立链接
        ApiClient client = new ApiClient("https://zwapi.xfyun.cn/api/ppt/v2");
        File pptFile = new File("test.txt");
        String resp2 = client.create(APPID, ts, signature, pptFile);
        System.out.println(resp2);

    }


    public static void main2(String[] args) throws Exception {
        // 获取请求头中需要携带的参数 appId（控制台获取）, timestamp（时间戳，单位：秒，与服务端时间相差五分钟之内）, signature（签名）
        long timestamp = System.currentTimeMillis() / 1000;
        String ts = String.valueOf(timestamp);
        ApiAuthAlgorithm auth = new ApiAuthAlgorithm();
        String signature = auth.getSignature(APPID, APISecret, timestamp);
        // 建立链接
        ApiClient client = new ApiClient("https://zwapi.xfyun.cn/api/ppt/v2");
        String sid = "bf60a501c88e4a969b128f65ee37eb84";
        String progressResult = client.checkProgress(APPID, ts, signature, sid);
        System.out.println(progressResult);
    }
}
