package com.ce.scrm.center.service.business.entity.view.org;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @program: scrm-center
 * @ClassName OrgEmployeeBusinessView
 * @description:
 * @author: lijinpeng
 * @create: 2025-08-11 10:15
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrgEmployeeBusinessView implements Serializable {

    private String id;
    /**
     * 名称
     */
    private String name;
    /**
     * 父id
     */
    private String parentId;

    /**
     * 类型  1 组织机构 2 人员 3 部门领导
     */
    private Integer type;

    private List<OrgEmployeeBusinessView> children;

}
