package com.ce.scrm.center.service.business.entity.dto.org;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 查询组织机构子集及本身
 * @Author: lijinpeng
 * @Date: 2024/11/21 13:51
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrgChildrenQueryBusinessDto implements Serializable {

    /**
     * 前端传参 areaId、subId、buId、deptId
     * 如果为 -1 则查所有区域
     */
    @NotNull
    private String id;

    /**
     * 事业部筛选项（只有id为subId才有效） 默认1 查询带事业部 0不带事业部
     */
    private Integer buFlag = 1;

}
