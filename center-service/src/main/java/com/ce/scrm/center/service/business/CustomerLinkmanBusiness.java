package com.ce.scrm.center.service.business;

import cn.ce.cesupport.enums.ContactTypeEnum;
import com.ce.scrm.center.service.business.entity.dto.CustomerLinkmanBusinessByCustomerIdDto;
import com.ce.scrm.center.service.business.entity.view.customer.CustomerLinkmanBusinessView;
import com.ce.scrm.center.service.third.invoke.CustomerLinkmanThirdService;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.ContactInfoDubboView;
import com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboView;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.*;

/**
 * @classname CustomerLinkmanBusiness
 * @description TODO
 * @date 2025/1/21 11:03
 * @create by gaomeijing
 */

@Slf4j
@Service
public class CustomerLinkmanBusiness {
    @Resource
    private CustomerLinkmanThirdService customerLinkmanThirdService;

    //标签显示顺序：签单-会员-服务-商务
    private final Map<String, Integer> linkManSortMap = ImmutableMap.<String, Integer>builder()
            .put("法人", 10)
            .put("会员联系人", 20)
            .put("签单联系人", 30)
            .put("网站负责人", 40)
            .put("服务联系人", 50)
            .put("商务联系人", 60)
            .build();

    public List<CustomerLinkmanBusinessView> getCustomerLinkmanBusinessViewList(CustomerLinkmanBusinessByCustomerIdDto customerLinkmanBusinessByCustomerIdDto) {
        List<CustomerLinkmanBusinessView> linkmanList = new ArrayList<>();
        String customerId = customerLinkmanBusinessByCustomerIdDto.getCustomerId();
        if(StringUtils.isBlank(customerId)) {
            log.error("获取业务联系人列表失败，请求参数customerId为空");
            return linkmanList;
        }
        List<ContactPersonDubboView> contactPersonDubboViewList = customerLinkmanThirdService.getCustomerContactListByCustomerId(customerId);

        // 循环客户联系人列表 用客户联系人ID查询联系方式详情
        for (ContactPersonDubboView person: contactPersonDubboViewList) {
            CustomerLinkmanBusinessView linkman = new CustomerLinkmanBusinessView();
            String linkManId = person.getContactPersonId();
            ContactPersonDubboView personFromDetail = customerLinkmanThirdService.getContactListByLinkmanId(linkManId);
            if (Objects.isNull(personFromDetail)) {
                continue;
            }
            // 联系方式列表
            List<ContactInfoDubboView> contactInfoDubboViews = personFromDetail.getContactInfoList();
            if (CollectionUtils.isEmpty(contactInfoDubboViews) || contactInfoDubboViews.size() <= 0) {
                continue;
            }

            linkman.setLinkManId(linkManId);
            linkman.setManNameCn(personFromDetail.getContactPersonName());
            if (personFromDetail.getGender() != null) {
                linkman.setManSex(personFromDetail.getGender() == 0 ? 3 : personFromDetail.getGender());
            }
            linkman.setManPost(personFromDetail.getPositionName());
            linkman.setRemarks(personFromDetail.getRemarks());
            linkman.setCreator(personFromDetail.getCreator());
            linkman.setLegalPersonFlag(personFromDetail.getLegalPersonFlag());
            try {
                if (personFromDetail.getCreateTime() != null) {
                    linkman.setCreateDate(Date.from(personFromDetail.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
                }
            } catch (Exception e) {
                log.info("linkman.setCreateDate报错: {}, 报错值：{}", e, personFromDetail.getCreateTime());
            }

            // 联系方式集合 电话、邮箱、微信
            Set<String> telSet = new HashSet<>();
            Set<String> emailSet = new HashSet<>();
            Set<String> wxSet = new HashSet<>();
            for (ContactInfoDubboView contact: contactInfoDubboViews) {
                String contactWay = contact.getContactWay();
                Integer contactType = getContactTypeFromMysql(contact.getContactType());
                if (StringUtils.isNotEmpty(contactWay) && StringUtils.isNotEmpty(StringUtils.stripToNull(contactWay))) {
                    if (ContactTypeEnum.TYPE_EMAIL.getValue().equals(contactType)) {
                        emailSet.add(contactWay);
                    } else if (ContactTypeEnum.TYPE_MOBILE.getValue().equals(contactType)) {
                        if(StringUtils.isNotEmpty(StringUtils.stripToNull(contactWay))) {
                            telSet.add(contactWay);
                        }
                        linkman.setPhoneVerifiedFlag(contact.getPhoneVerifiedFlag());
                    } else if (ContactTypeEnum.TYPE_WEIXIN.getValue().equals(contactType)) {
                        wxSet.add(contactWay);
                    } else if (ContactTypeEnum.TYPE_FAX.getValue().equals(contactType)) {
                        telSet.add(contactWay);
                    }
                }
            }
            boolean isExistTel = false;
            if (telSet.size() > 0) {
                isExistTel = true;
            }
            if (!isExistTel) {
                continue;
            }
            linkman.setTelSet(telSet);
            linkman.setEmailSet(emailSet);
            linkman.setWxSet(wxSet);

            // 根据标签拆分为多个联系人
            String sourceTag = personFromDetail.getSourceTag();
            if(StringUtils.isNotBlank(sourceTag)) {
                String[] tags = sourceTag.split(",");
                for (String tag: tags) {
                    CustomerLinkmanBusinessView newLinkman = new CustomerLinkmanBusinessView();
                    BeanUtils.copyProperties(linkman, newLinkman);
                    newLinkman.setSourceTag(tag);
                    linkmanList.add(newLinkman);
                }
            } else {
                linkmanList.add(linkman);
            }
        }
        // 排序
        linkmanList.sort(new Comparator<CustomerLinkmanBusinessView>() {
            @Override
            public int compare(CustomerLinkmanBusinessView o1, CustomerLinkmanBusinessView o2) {
                Integer sortOne = linkManSortMap.getOrDefault(o1.getSourceTag(), 100);
                Integer sortTwo = linkManSortMap.getOrDefault(o2.getSourceTag(), 100);
                if(sortOne.equals(sortTwo)){
                    return 0;
                }
                return sortOne > sortTwo ? 1 : -1;
            }
        });
        return linkmanList;
    }

    /**
     * 根据Mysql中的联系方式类型转换为Dubbo中的联系方式类型
     * @param contactType
     * @return
     */
    private Integer getContactTypeFromMysql(Integer contactType) {
        if (contactType == null) {
            return null;
        }

        switch (contactType) {
            case 4:
                return 1;
            case 1:
                return 2;
            case 5:
                return 3;
            case 2:
                return 5;
            case 3:
                return 6;
            case 6:
                return 10;
            default:
                return 0;
        }
    }
}
