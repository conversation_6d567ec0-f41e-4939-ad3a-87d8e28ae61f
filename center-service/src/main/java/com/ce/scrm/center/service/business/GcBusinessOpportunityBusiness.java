package com.ce.scrm.center.service.business;

import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import cn.ce.cesupport.enums.*;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.GcBusinessOpportunity;
import com.ce.scrm.center.dao.entity.GcBusinessOpportunityLog;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.GcBusinessOpportunityLogService;
import com.ce.scrm.center.dao.service.GcBusinessOpportunityService;
import com.ce.scrm.center.service.business.entity.dto.*;
import com.ce.scrm.center.service.business.entity.view.CustomerStageDataBusinessView;
import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityAssignDataBusinessView;
import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityBusinessView;
import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityEnumData;
import com.ce.scrm.center.service.enums.*;
import com.ce.scrm.center.service.enums.CustomerStageEnum;
import com.ce.scrm.center.service.third.entity.view.CustomerAddThirdView;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.entity.view.OrgDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.service.utils.GcSjSourceEnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 高呈商机业务实现
 * <AUTHOR>
 * @date 2024/5/20 下午4:55
 * @version 1.0.0
 */
@Slf4j
@Service
public class GcBusinessOpportunityBusiness {

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private GcBusinessOpportunityService gcBusinessOpportunityService;

    @Resource
    private GcBusinessOpportunityLogService gcBusinessOpportunityLogService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private CustomerBusiness customerBusiness;

    @Resource
    private ProtectBusiness protectBusiness;

    @Resource
    private OrgThirdService orgThirdService;

    @Resource
    private CmCustProtectService cmCustProtectService;


    /**
     * 获取高呈商机相关枚举数据
     * <AUTHOR>
     * @date 2024/5/20 下午6:43
     * @return com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityEnumData
     **/
    public GcBusinessOpportunityEnumData enums() {
        GcBusinessOpportunityEnumData gcBusinessOpportunityEnumData = new GcBusinessOpportunityEnumData();
        gcBusinessOpportunityEnumData.setGcBusinessOpportunitySourceStateDataList(GcSjSourceEnumUtil.list());
        gcBusinessOpportunityEnumData.setGcBusinessOpportunityLevelDataList(GcSjLevelEnum.list());
        gcBusinessOpportunityEnumData.setGcBusinessOpportunityRequirementDataList(GcSjRequirementEnum.list());
        gcBusinessOpportunityEnumData.setGcBusinessOpportunityCustomerStageDataList(CustomerStageEnum.list());
        return gcBusinessOpportunityEnumData;
    }


    /**
     * 高呈商机分页列表
     *
     * @param gcBusinessOpportunityPageBusinessDto 分页参数
     * <AUTHOR>
     * @date 2024/5/20 下午4:53
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityBusinessView>
     **/
    public Page<GcBusinessOpportunityBusinessView> pageList(GcBusinessOpportunityPageBusinessDto gcBusinessOpportunityPageBusinessDto) {
        Page<GcBusinessOpportunity> page = Page.of(gcBusinessOpportunityPageBusinessDto.getPageNum(), gcBusinessOpportunityPageBusinessDto.getPageSize());
        LambdaQueryChainWrapper<GcBusinessOpportunity> lambdaQueryChainWrapper = gcBusinessOpportunityService.lambdaQuery()
                .between(gcBusinessOpportunityPageBusinessDto.getStartTime() != null && gcBusinessOpportunityPageBusinessDto.getEndTime() != null,
                        GcBusinessOpportunity::getUpdateTime, gcBusinessOpportunityPageBusinessDto.getStartTime(), gcBusinessOpportunityPageBusinessDto.getEndTime())
                .ge(GcBusinessOpportunity::getState, GcSjStateEnum.NON_HANDLE.getState())
                .eq(GcBusinessOpportunity::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .in(!CollectionUtils.isEmpty(gcBusinessOpportunityPageBusinessDto.getGcSjStateEnumList()), GcBusinessOpportunity::getState, gcBusinessOpportunityPageBusinessDto.getGcSjStateEnumList().stream().map(GcSjStateEnum::getState).collect(Collectors.toList()))
                ;
        if (Objects.nonNull(gcBusinessOpportunityPageBusinessDto.getGcSjSourceEnum())) {
            lambdaQueryChainWrapper.eq(GcBusinessOpportunity::getSource, gcBusinessOpportunityPageBusinessDto.getGcSjSourceEnum().getSource());
        }
        if (StrUtil.isNotBlank(gcBusinessOpportunityPageBusinessDto.getQueryStr())) {
            lambdaQueryChainWrapper.and(wrapper -> wrapper
                    .like(GcBusinessOpportunity::getCustomerName, gcBusinessOpportunityPageBusinessDto.getQueryStr())
                    .or()
                    .like(GcBusinessOpportunity::getLinkmanName, gcBusinessOpportunityPageBusinessDto.getQueryStr())
                    .or()
                    .like(GcBusinessOpportunity::getLinkmanPhone, gcBusinessOpportunityPageBusinessDto.getQueryStr())
            );
        }
        lambdaQueryChainWrapper.orderByDesc(GcBusinessOpportunity::getUpdateTime);
        Page<GcBusinessOpportunity> gcBusinessOpportunityPage = lambdaQueryChainWrapper.page(page);
        Page<GcBusinessOpportunityBusinessView> gcBusinessOpportunityBusinessViewPage = BeanUtil.copyProperties(gcBusinessOpportunityPage, Page.class);
        if (gcBusinessOpportunityBusinessViewPage.getTotal() < 1) {
            return gcBusinessOpportunityBusinessViewPage;
        }
        gcBusinessOpportunityBusinessViewPage.setRecords(CglibUtil.copyList(gcBusinessOpportunityPage.getRecords(), GcBusinessOpportunityBusinessView::new));
        return gcBusinessOpportunityBusinessViewPage;
    }

    /**
     * 获取商机详情数据
     * @param gcBusinessOpportunityOperateBusinessDto 商机详情数据
     * <AUTHOR>
     * @date 2024/5/27 下午4:42
     * @return java.util.Optional<com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityBusinessView>
     **/
    public Optional<GcBusinessOpportunityBusinessView> detail(GcBusinessOpportunityOperateBusinessDto gcBusinessOpportunityOperateBusinessDto) {
        GcBusinessOpportunity gcBusinessOpportunity = gcBusinessOpportunityService.getById(gcBusinessOpportunityOperateBusinessDto.getId());
        if (gcBusinessOpportunity == null || gcBusinessOpportunity.getDeleteFlag().equals(DeleteFlagEnum.DELETE.getCode())) {
            log.warn("获取商机详情数据, 商机数据不存在，参数为:{}", JSON.toJSON(gcBusinessOpportunityOperateBusinessDto));
            return Optional.empty();
        }
        return Optional.ofNullable(CglibUtil.copy(gcBusinessOpportunity, GcBusinessOpportunityBusinessView.class));
    }

    /**
     * 保存高呈商机数据
     *
     * @param gcBusinessOpportunitySaveBusinessDto 高呈商机添加参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:02
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> add(GcBusinessOpportunitySaveBusinessDto gcBusinessOpportunitySaveBusinessDto) {
        Optional<String> checkParam = checkParam(false, "保存高呈商机", gcBusinessOpportunitySaveBusinessDto);
        if (checkParam.isPresent()) {
            return checkParam;
        }
        GcBusinessOpportunity gcBusinessOpportunity = CglibUtil.copy(gcBusinessOpportunitySaveBusinessDto, GcBusinessOpportunity.class);
        gcBusinessOpportunity.setId(null);
        gcBusinessOpportunity.setLevel(gcBusinessOpportunitySaveBusinessDto.getLevel().getLevel());
        gcBusinessOpportunity.setSource(gcBusinessOpportunitySaveBusinessDto.getSource().getSource());
        gcBusinessOpportunity.setState(GcSjStateEnum.NON_HANDLE.getState());
        gcBusinessOpportunity.setRequirementType(gcBusinessOpportunitySaveBusinessDto.getRequirementType().getRequirementType());
        Optional<String> sjAddOptional = gcBusinessOpportunityService.save(gcBusinessOpportunity) ? Optional.empty() : Optional.of("商机新增失败");
        if (!sjAddOptional.isPresent()) {
            saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.HIGH_PRESENTATION_MARKET_CREATION, gcBusinessOpportunitySaveBusinessDto.getOperator(), gcBusinessOpportunitySaveBusinessDto.getOperatorName(), null);
        }
        return sjAddOptional;
    }

    /**
     * 校验高呈商机保存参数
     * @param updateFlag 更新标记
     * @param logPrefix 日志前缀
     * @param gcBusinessOpportunitySaveBusinessDto 高呈商机保存参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:03
     * @return java.util.Optional<java.lang.String>
     **/
    private Optional<String> checkParam(boolean updateFlag, String logPrefix, GcBusinessOpportunitySaveBusinessDto gcBusinessOpportunitySaveBusinessDto) {
        if (gcBusinessOpportunitySaveBusinessDto == null) {
            log.warn("{}，参数不能为空", logPrefix);
            return Optional.of("参数不能为空");
        }
        if (updateFlag && gcBusinessOpportunitySaveBusinessDto.getId() == null) {
            log.warn("{}，高呈商机ID不能为空，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunitySaveBusinessDto));
            return Optional.of("高呈商机ID不能为空");
        }
        if (StrUtil.isBlank(gcBusinessOpportunitySaveBusinessDto.getCustomerName())) {
            log.warn("{}，客户名称不能为空，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunitySaveBusinessDto));
            return Optional.of("客户名称不能为空");
        }
        if (StrUtil.isBlank(gcBusinessOpportunitySaveBusinessDto.getGcAreaId()) || StrUtil.isBlank(gcBusinessOpportunitySaveBusinessDto.getGcAreaName())) {
            log.warn("{}，区域信息不能为空，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunitySaveBusinessDto));
            return Optional.of("区域信息不能为空");
        }
        if (gcBusinessOpportunitySaveBusinessDto.getSource() == null) {
            log.warn("{}，商机来源不能为空，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunitySaveBusinessDto));
            return Optional.of("商机来源不能为空");
        }
        if (StrUtil.isBlank(gcBusinessOpportunitySaveBusinessDto.getLinkmanName())) {
            log.warn("{}，联系人姓名不能为空，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunitySaveBusinessDto));
            return Optional.of("联系人姓名不能为空");
        }
        if (StrUtil.isBlank(gcBusinessOpportunitySaveBusinessDto.getLinkmanPhone())) {
            log.warn("{}，联系人手机不能为空，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunitySaveBusinessDto));
            return Optional.of("联系人手机不能为空");
        }
        if (gcBusinessOpportunitySaveBusinessDto.getRequirementType() == null) {
            log.warn("{}，需求类型不能为空，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunitySaveBusinessDto));
            return Optional.of("需求类型不能为空");
        }
        if (StrUtil.isBlank(gcBusinessOpportunitySaveBusinessDto.getRequirementDetail())) {
            log.warn("{}，需求不能为空，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunitySaveBusinessDto));
            return Optional.of("需求不能为空");
        }
        //获取客户信息
        Optional<CustomerDataThirdView> customerDataThirdViewOptional = customerThirdService.getCustomerDataByCustomerName(gcBusinessOpportunitySaveBusinessDto.getCustomerName());
        //客户不存在，新增客户
        if (!customerDataThirdViewOptional.isPresent()) {
            CustomerAddThirdView customerAddThirdView = customerThirdService.addCustomer(null, gcBusinessOpportunitySaveBusinessDto.getCustomerName(), 2, 5, gcBusinessOpportunitySaveBusinessDto.getOperator());
            if (StrUtil.isNotBlank(customerAddThirdView.getErrMsg())) {
                log.warn("{}，{}，参数为:{}", logPrefix, customerAddThirdView.getErrMsg(), JSON.toJSONString(gcBusinessOpportunitySaveBusinessDto));
                return Optional.of(customerAddThirdView.getErrMsg());
            }
            gcBusinessOpportunitySaveBusinessDto.setCustomerId(customerAddThirdView.getCustomerId());
            /*去除客户再次验证是否存在 上一步已经验证过
            customerDataThirdViewOptional = customerThirdService.getCustomerData(customerAddThirdView.getCustomerId());
            if (!customerDataThirdViewOptional.isPresent()) {
                log.warn("{}，获取客户失败，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunitySaveBusinessDto));
                return Optional.of("获取客户失败");
            }
             */
        }
        return Optional.empty();
    }

    /**
     * 更新高呈商机
     *
     * @param gcBusinessOpportunitySaveBusinessDto 高呈商机更新参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:10
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> update(GcBusinessOpportunitySaveBusinessDto gcBusinessOpportunitySaveBusinessDto) {
        Optional<String> checkParam = checkParam(true, "修改高呈商机", gcBusinessOpportunitySaveBusinessDto);
        if (checkParam.isPresent()) {
            return checkParam;
        }
        Optional<String> updateCheck = updateCheck("修改高呈商机", gcBusinessOpportunitySaveBusinessDto.getId(), false);
        if (updateCheck.isPresent()) {
            return updateCheck;
        }
        GcBusinessOpportunity gcBusinessOpportunity = CglibUtil.copy(gcBusinessOpportunitySaveBusinessDto, GcBusinessOpportunity.class);
        gcBusinessOpportunity.setLevel(gcBusinessOpportunitySaveBusinessDto.getLevel().getLevel());
        gcBusinessOpportunity.setSource(gcBusinessOpportunitySaveBusinessDto.getSource().getSource());
        gcBusinessOpportunity.setRequirementType(gcBusinessOpportunitySaveBusinessDto.getRequirementType().getRequirementType());
        saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.HIGH_PRESENTATION_MARKET_UPDATE, gcBusinessOpportunitySaveBusinessDto.getOperator(), gcBusinessOpportunitySaveBusinessDto.getOperatorName(), null);
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunity) ? Optional.empty() : Optional.of("商机更新失败");
    }

    /**
     * 更新来源状态校验
     * @param logPrefix 日志前缀
     * @param id    高呈商机ID
     * @param selectState 选中状态 更新、删除传入false、受理、退回传入true
     * <AUTHOR>
     * @date 2024/5/20 下午7:11
     * @return java.util.Optional<java.lang.String>
     **/
    private Optional<String> updateCheck(String logPrefix, Long id, boolean selectState) {
        GcBusinessOpportunity gcBusinessOpportunity = gcBusinessOpportunityService.getById(id);
        if (gcBusinessOpportunity == null || Objects.equals(DeleteFlagEnum.DELETE.getCode(), gcBusinessOpportunity.getDeleteFlag())) {
            log.warn("{}，商机不存在，商机ID为:{}", logPrefix, id);
            return Optional.of("商机不存在");
        }
        GcSjSourceEnum gcSjSourceEnum = GcSjSourceEnum.get(gcBusinessOpportunity.getSource());
        if (gcSjSourceEnum == null || gcSjSourceEnum.getSelectState() == selectState) {
            log.warn("{}，当前来源商机不能操作，商机ID为:{}", logPrefix, id);
            return Optional.of("当前来源商机不能操作");
        }
        if (logPrefix != null && logPrefix.contains("退回")) {
            Set<GcSjStateEnum> allowedStates = EnumSet.of(
                    GcSjStateEnum.NON_HANDLE,
                    GcSjStateEnum.FOLLOW,
                    GcSjStateEnum.HIGH_PRESENTATION_BUSINESS_RETURN,
                    GcSjStateEnum.SEND_BACK
            );
            // 检查状态是否在允许集合中 不在集合中的不许操作
            if (!allowedStates.contains(GcSjStateEnum.get(gcBusinessOpportunity.getState()))) {
                log.warn("{}，当前状态下商机不能操作，商机ID为:{}", logPrefix, id);
                return Optional.of("当前状态下商机不能操作");
            }
        } else{
            if (GcSjStateEnum.NON_HANDLE != GcSjStateEnum.get(gcBusinessOpportunity.getState())) {
                log.warn("{}，当前状态下商机不能操作，商机ID为:{}", logPrefix, id);
                return Optional.of("当前状态下商机不能操作");
            }
        }
        return Optional.empty();
    }

    /**
     * 删除高呈商机
     *
     * @param gcBusinessOpportunityOperateBusinessDto 高呈商机删除参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:13
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> delete(GcBusinessOpportunityOperateBusinessDto gcBusinessOpportunityOperateBusinessDto) {
        Optional<String> updateCheck = updateCheck("删除高呈商机", gcBusinessOpportunityOperateBusinessDto.getId(), false);
        if (updateCheck.isPresent()) {
            return updateCheck;
        }
        GcBusinessOpportunity gcBusinessOpportunity = new GcBusinessOpportunity();
        gcBusinessOpportunity.setId(gcBusinessOpportunityOperateBusinessDto.getId());
        gcBusinessOpportunity.setDeleteFlag(DeleteFlagEnum.DELETE.getCode());
        saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.HIGH_PRESENTATION_MARKET_DELETION, gcBusinessOpportunityOperateBusinessDto.getOperator(), gcBusinessOpportunityOperateBusinessDto.getOperatorName(), null);
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunity) ? Optional.empty() : Optional.of("商机删除失败");
    }

    /**
     * 受理高呈商机
     *
     * @param gcBusinessOpportunityOperateBusinessDto 高呈商机受理参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:13
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> accept(GcBusinessOpportunityOperateBusinessDto gcBusinessOpportunityOperateBusinessDto) {
        Optional<String> updateCheck = updateCheck("受理高呈商机", gcBusinessOpportunityOperateBusinessDto.getId(), true);
        if (updateCheck.isPresent()) {
            return updateCheck;
        }
        GcBusinessOpportunity gcBusinessOpportunity = new GcBusinessOpportunity();
        gcBusinessOpportunity.setId(gcBusinessOpportunityOperateBusinessDto.getId());
        gcBusinessOpportunity.setState(GcSjStateEnum.ACCEPT.getState());
        saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.HIGH_PRESENTATION_MARKET_ACCEPTANCE, gcBusinessOpportunityOperateBusinessDto.getOperator(), gcBusinessOpportunityOperateBusinessDto.getOperatorName(), null);
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunity) ? Optional.empty() : Optional.of("商机受理失败");
    }

    /**
     * 退回高呈商机
     *
     * @param gcBusinessOpportunitySendBackDto 高呈商机退回参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:13
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> sendBack(GcBusinessOpportunitySendBackDto gcBusinessOpportunitySendBackDto) {
        Optional<String> updateCheck;
        if (Objects.equals(gcBusinessOpportunitySendBackDto.getType(), GcSjSendBackEnum.GC_MARKET_DEPARTMENT_BACK.getState())){
            updateCheck = updateCheck("高呈市场部退回", gcBusinessOpportunitySendBackDto.getId(), true);
        } else {
            updateCheck = updateCheck("高呈商务退回", gcBusinessOpportunitySendBackDto.getId(), true);
        }
        if (updateCheck.isPresent()) {
            return updateCheck;
        }
        GcBusinessOpportunity gcBusinessOpportunity = new GcBusinessOpportunity();
        gcBusinessOpportunity.setId(gcBusinessOpportunitySendBackDto.getId());
        if (Objects.equals(gcBusinessOpportunitySendBackDto.getType(), GcSjSendBackEnum.GC_MARKET_DEPARTMENT_BACK.getState())){
            //高呈市场部退回
            gcBusinessOpportunity.setState(GcSjStateEnum.SEND_BACK.getState());
            gcBusinessOpportunity.setReasonBack(gcBusinessOpportunitySendBackDto.getReasonBack());
            saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.HIGH_PRESENTATION_MARKET_RETURN, gcBusinessOpportunitySendBackDto.getOperator(), gcBusinessOpportunitySendBackDto.getOperatorName(), gcBusinessOpportunitySendBackDto.getReasonBack());
        }else{
            //高呈商务退回
            gcBusinessOpportunity.setState(GcSjStateEnum.HIGH_PRESENTATION_BUSINESS_RETURN.getState());
            gcBusinessOpportunity.setReasonBusinessBack(gcBusinessOpportunitySendBackDto.getReasonBack());
            gcBusinessOpportunity.setGcAreaId("");
            gcBusinessOpportunity.setGcAreaName("");
            gcBusinessOpportunity.setGcSubId("");
            gcBusinessOpportunity.setGcSubName("");
            gcBusinessOpportunity.setGcDeptId("");
            gcBusinessOpportunity.setGcDeptName("");
            gcBusinessOpportunity.setGcSalerId("");
            gcBusinessOpportunity.setGcSalerName("");
            saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.HIGH_PRESENTATION_BUSINESS_RETURN, gcBusinessOpportunitySendBackDto.getOperator(), gcBusinessOpportunitySendBackDto.getOperatorName(), gcBusinessOpportunitySendBackDto.getReasonBack());
        }
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunity) ? Optional.empty() : Optional.of("商机退回失败");
    }

    /**
     * 获取高呈商机分配数据
     * @param gcBusinessOpportunityOperateBusinessDto   分配数据获取参数
     * <AUTHOR>
     * @date 2024/5/22 下午2:03
     * @return com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityAssignDataBusinessView
     **/
    public GcBusinessOpportunityAssignDataBusinessView assignData(GcBusinessOpportunityOperateBusinessDto gcBusinessOpportunityOperateBusinessDto) {
        GcBusinessOpportunity gcBusinessOpportunity = gcBusinessOpportunityService.getById(gcBusinessOpportunityOperateBusinessDto.getId());
        if (gcBusinessOpportunity == null || Objects.equals(DeleteFlagEnum.DELETE.getCode(), gcBusinessOpportunity.getDeleteFlag())) {
            log.error("获取高呈商机分配数据，商机不存在，参数为:{}", JSON.toJSONString(gcBusinessOpportunityOperateBusinessDto));
            return null;
        }
        GcBusinessOpportunityAssignDataBusinessView gcBusinessOpportunityAssignDataBusinessView = CglibUtil.copy(gcBusinessOpportunity, GcBusinessOpportunityAssignDataBusinessView.class);
        CustomerStageDataBusinessView customerStageDataBusinessView = customerBusiness.getCustomerStageData(gcBusinessOpportunity.getCustomerId());
        CustomerStageEnum customerStageEnum = customerStageDataBusinessView.getCustomerStageEnum();
        BeanUtil.copyProperties(customerStageDataBusinessView, gcBusinessOpportunityAssignDataBusinessView);
        gcBusinessOpportunityAssignDataBusinessView.setCustomerStage(customerStageEnum.getStage());
        gcBusinessOpportunityAssignDataBusinessView.setCustomerStageName(customerStageEnum.getStageName());
        List<EmployeeDataThirdView> employeeDataThirdViewList = new ArrayList<>();
        if (CustomerStageEnum.GC_PROTECT == customerStageEnum && StrUtil.isNotBlank(gcBusinessOpportunity.getGcSalerId())) {
            Optional<EmployeeDataThirdView> employeeDataThirdViewOptional = employeeThirdService.getEmployeeData(customerStageDataBusinessView.getSalerId());
            employeeDataThirdViewOptional.ifPresent(employeeDataThirdViewList::add);
        } else {
            employeeDataThirdViewList = employeeThirdService.getGcAllSalerData();
        }
        gcBusinessOpportunityAssignDataBusinessView.setGcEmployeeDataList(employeeDataThirdViewList.stream().map(employeeDataThirdView -> {
            GcBusinessOpportunityAssignDataBusinessView.GcEmployeeData gcEmployeeData = new GcBusinessOpportunityAssignDataBusinessView.GcEmployeeData();
            gcEmployeeData.setEmployeeId(employeeDataThirdView.getId());
            gcEmployeeData.setEmployeeName(employeeDataThirdView.getName());
            gcEmployeeData.setEmail(employeeDataThirdView.getWorkMail());
            gcEmployeeData.setDeptId(employeeDataThirdView.getOrgId());
            gcEmployeeData.setDeptName(employeeDataThirdView.getOrgName());
            gcEmployeeData.setPosition(employeeDataThirdView.getPosition());
            return gcEmployeeData;
        }).collect(Collectors.toList()));
        return gcBusinessOpportunityAssignDataBusinessView;
    }

    /**
     * 分配高呈商机
     *
     * @param gcBusinessOpportunityAssignBusinessDto 高呈商机分配参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:15
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> assign(GcBusinessOpportunityAssignBusinessDto gcBusinessOpportunityAssignBusinessDto) {
        GcBusinessOpportunity gcBusinessOpportunity = gcBusinessOpportunityService.getById(gcBusinessOpportunityAssignBusinessDto.getId());
        if (gcBusinessOpportunity == null || Objects.equals(DeleteFlagEnum.DELETE.getCode(), gcBusinessOpportunity.getDeleteFlag())) {
            log.warn("分配高呈商机，商机不存在，参数为:{}", JSON.toJSONString(gcBusinessOpportunityAssignBusinessDto));
            return Optional.of("商机不存在");
        }
        GcSjSourceEnum gcSjSourceEnum = GcSjSourceEnum.get(gcBusinessOpportunity.getSource());
        if (gcSjSourceEnum == null) {
            log.warn("分配高呈商机，商机来源异常，参数为:{}", JSON.toJSONString(gcBusinessOpportunityAssignBusinessDto));
            return Optional.of("商机来源异常");
        }
        GcSjStateEnum gcSjStateEnum = GcSjStateEnum.get(gcBusinessOpportunity.getState());
        if (gcSjStateEnum == null) {
            log.warn("分配高呈商机，商机状态异常，参数为:{}", JSON.toJSONString(gcBusinessOpportunityAssignBusinessDto));
            return Optional.of("商机状态异常");
        }
        if (gcSjStateEnum != GcSjStateEnum.NON_HANDLE && gcSjStateEnum != GcSjStateEnum.ACCEPT && gcSjStateEnum != GcSjStateEnum.FOLLOW && gcSjStateEnum != GcSjStateEnum.HIGH_PRESENTATION_BUSINESS_RETURN) {
            log.warn("分配高呈商机，当前商机不能进行分配，参数为:{}", JSON.toJSONString(gcBusinessOpportunityAssignBusinessDto));
            return Optional.of("当前商机不能进行分配");
        }
        Optional<EmployeeDataThirdView> salerDataOptional = employeeThirdService.getEmployeeData(gcBusinessOpportunityAssignBusinessDto.getSalerId());
        if (!salerDataOptional.isPresent()) {
            log.warn("分配高呈商机，分配商务不存在，参数为:{}", JSON.toJSONString(gcBusinessOpportunityAssignBusinessDto));
            return Optional.of("分配商务不存在");
        }
        EmployeeDataThirdView salerData = salerDataOptional.get();
        if (!salerData.getGcEmployeeFlag()) {
            log.warn("分配高呈商机，当前商务（非高呈）不能被分配，参数为:{}", JSON.toJSONString(gcBusinessOpportunityAssignBusinessDto));
            return Optional.of("当前商务不能被分配");
        }
        CustomerStageDataBusinessView customerStageDataBusinessView = customerBusiness.getCustomerStageData(gcBusinessOpportunity.getCustomerId());
        if (CustomerStageEnum.GC_PROTECT == customerStageDataBusinessView.getCustomerStageEnum() && StrUtil.isNotBlank(gcBusinessOpportunity.getGcSalerId())) {
            log.warn("分配高呈商机，当前客户被其他商务保护，不能分配，参数为:{}", JSON.toJSONString(gcBusinessOpportunityAssignBusinessDto));
            return Optional.of("当前客户被其他商务保护");
        }
        if (CustomerStageEnum.OPEN_SEA == customerStageDataBusinessView.getCustomerStageEnum() || CustomerStageEnum.GC_PROTECT == customerStageDataBusinessView.getCustomerStageEnum()) {
            ProtectBusinessDto protectBusinessDto = new ProtectBusinessDto();
            protectBusinessDto.setCustId(gcBusinessOpportunity.getCustomerId());
            protectBusinessDto.setCustType(ProtectCustTypeEnum.PROTECT_FOLLOW.getValue());
            protectBusinessDto.setSalerId(salerData.getId());
            protectBusinessDto.setBussdeptId(salerData.getOrgId());
            protectBusinessDto.setBuId(salerData.getBuId());
            protectBusinessDto.setSubcompanyId(salerData.getSubId());
            protectBusinessDto.setAreaId(salerData.getAreaId());
            protectBusinessDto.setOperator(gcBusinessOpportunityAssignBusinessDto.getOperator());
            protectBusinessDto.setCustSource(CustSourceEnum.GAOCHENG.getValue());
            protectBusinessDto.setCustName(customerStageDataBusinessView.getCustomerName());
            protectBusinessDto.setCustSourceSub(6);
            protectBusinessDto.setOccupy(1);
            protectBusinessDto.setStatus(ProtectStateEnum.PROTECT.getState());
            protectBusinessDto.setProtectTime(new Date());
            protectBusinessDto.setExceedTime(DateUtil.offsetDay(new Date(), 30));
            Optional<String> saveOptional = protectBusiness.save(protectBusinessDto);
            if (saveOptional.isPresent()) {
                log.warn("分配高呈商机，保护关系保存失败，参数为:{}", JSON.toJSONString(protectBusinessDto));
                return Optional.of("保护关系保存失败");
            }
        }
        GcBusinessOpportunity gcBusinessOpportunityUpdate = new GcBusinessOpportunity();
        gcBusinessOpportunityUpdate.setId(gcBusinessOpportunity.getId());
        if (CustomerStageEnum.CE_PROTECT == customerStageDataBusinessView.getCustomerStageEnum()) {
            gcBusinessOpportunityUpdate.setAreaId(customerStageDataBusinessView.getAreaId());
            gcBusinessOpportunityUpdate.setAreaName(customerStageDataBusinessView.getAreaName());
            gcBusinessOpportunityUpdate.setSubId(customerStageDataBusinessView.getSubId());
            gcBusinessOpportunityUpdate.setSubName(customerStageDataBusinessView.getSubName());
            gcBusinessOpportunityUpdate.setDeptId(customerStageDataBusinessView.getDeptId());
            gcBusinessOpportunityUpdate.setDeptName(customerStageDataBusinessView.getDeptName());
            gcBusinessOpportunityUpdate.setSalerId(customerStageDataBusinessView.getSalerId());
            gcBusinessOpportunityUpdate.setSalerName(customerStageDataBusinessView.getSalerName());
        }
        gcBusinessOpportunityUpdate.setGcAreaId(salerData.getAreaId());
        gcBusinessOpportunityUpdate.setGcAreaName(salerData.getAreaName());
        gcBusinessOpportunityUpdate.setGcSubId(salerData.getSubId());
        gcBusinessOpportunityUpdate.setGcSubName(salerData.getSubName());
        gcBusinessOpportunityUpdate.setGcDeptId(salerData.getOrgId());
        gcBusinessOpportunityUpdate.setGcDeptName(salerData.getOrgName());
        gcBusinessOpportunityUpdate.setGcSalerId(salerData.getId());
        gcBusinessOpportunityUpdate.setGcSalerName(salerData.getName());
        gcBusinessOpportunityUpdate.setState(GcSjStateEnum.FOLLOW.getState());
        saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.HIGH_PRESENTATION_MARKET_ALLOCATION, gcBusinessOpportunityAssignBusinessDto.getOperator(), gcBusinessOpportunityAssignBusinessDto.getOperatorName(), null);
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunityUpdate) ? Optional.empty() : Optional.of("商机分配失败");
    }

    /**
     * 提交高呈商机
     * @param gcBusinessOpportunityOperateBusinessDto   商机提交参数
     * <AUTHOR>
     * @date 2024/5/22 下午3:14
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> submit(GcBusinessOpportunityOperateBusinessDto gcBusinessOpportunityOperateBusinessDto) {
        Optional<String> ceCheckOperateParam = ceCheckOperateParam("提交高呈商机", gcBusinessOpportunityOperateBusinessDto, GcSjStateEnum.UNCOMMIT);
        if (ceCheckOperateParam.isPresent()) {
            return ceCheckOperateParam;
        }
        GcBusinessOpportunity gcBusinessOpportunity = new GcBusinessOpportunity();
        gcBusinessOpportunity.setId(gcBusinessOpportunityOperateBusinessDto.getId());
        gcBusinessOpportunity.setState(GcSjStateEnum.WAIT_MAJOR_CHECK.getState());
        saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.SME_BUSINESS_SUBMISSION, gcBusinessOpportunityOperateBusinessDto.getOperator(), gcBusinessOpportunityOperateBusinessDto.getOperatorName(), null);
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunity) ? Optional.empty() : Optional.of("提交商机失败");
    }

    /**
     * 中小商务操作参数校验
     * @param logPrefix 日志前缀
     * @param gcBusinessOpportunityOperateBusinessDto   操作参数
     * @param gcSjStateEnum 允许操作的状态
     * <AUTHOR>
     * @date 2024/5/22 下午3:17
     * @return java.util.Optional<java.lang.String>
     **/
    private Optional<String> ceCheckOperateParam(String logPrefix, GcBusinessOpportunityOperateBusinessDto gcBusinessOpportunityOperateBusinessDto, GcSjStateEnum gcSjStateEnum) {
        GcBusinessOpportunity gcBusinessOpportunity = gcBusinessOpportunityService.getById(gcBusinessOpportunityOperateBusinessDto.getId());
        if (gcBusinessOpportunity == null || Objects.equals(DeleteFlagEnum.DELETE.getCode(), gcBusinessOpportunity.getDeleteFlag())) {
            log.warn("{}，商机不存在，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunityOperateBusinessDto));
            return Optional.of("商机不存在");
        }
        if (!gcBusinessOpportunityOperateBusinessDto.getOperator().equals(gcBusinessOpportunity.getSalerId())) {
            log.warn("{}，不能操作他人的商机数据，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunityOperateBusinessDto));
            return Optional.of("不能操作他人的商机数据");
        }
        GcSjSourceEnum gcSjSourceEnum = GcSjSourceEnum.get(gcBusinessOpportunity.getSource());
        if (GcSjSourceEnum.SUB != gcSjSourceEnum) {
            log.warn("{}，当前来源商机不能操作，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunityOperateBusinessDto));
            return Optional.of("当前来源商机不能操作");
        }
        if (gcSjStateEnum != GcSjStateEnum.get(gcBusinessOpportunity.getState())) {
            log.warn("{}，当前状态下商机不能操作，参数为:{}", logPrefix, JSON.toJSONString(gcBusinessOpportunityOperateBusinessDto));
            return Optional.of("当前状态下商机不能操作");
        }
        return Optional.empty();
    }

    /**
     * 撤回高呈商机
     * @param gcBusinessOpportunityOperateBusinessDto   商机撤回参数
     * <AUTHOR>
     * @date 2024/5/22 下午3:14
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> revocation(GcBusinessOpportunityOperateBusinessDto gcBusinessOpportunityOperateBusinessDto) {
        Optional<String> ceCheckOperateParam = ceCheckOperateParam("撤回高呈商机", gcBusinessOpportunityOperateBusinessDto, GcSjStateEnum.WAIT_MAJOR_CHECK);
        if (ceCheckOperateParam.isPresent()) {
            return ceCheckOperateParam;
        }
        GcBusinessOpportunity gcBusinessOpportunity = new GcBusinessOpportunity();
        gcBusinessOpportunity.setId(gcBusinessOpportunityOperateBusinessDto.getId());
        gcBusinessOpportunity.setState(GcSjStateEnum.UNCOMMIT.getState());
        saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.SME_BUSINESS_WITHDRAWAL, gcBusinessOpportunityOperateBusinessDto.getOperator(), gcBusinessOpportunityOperateBusinessDto.getOperatorName(), null);
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunity) ? Optional.empty() : Optional.of("撤回商机失败");
    }

    /**
     * 中小商务删除高呈商机
     * @param gcBusinessOpportunityOperateBusinessDto   商机删除参数
     * <AUTHOR>
     * @date 2024/5/22 下午3:14
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> ceDelete(GcBusinessOpportunityOperateBusinessDto gcBusinessOpportunityOperateBusinessDto) {
        Optional<String> ceCheckOperateParam = ceCheckOperateParam("中小商务删除高呈商机", gcBusinessOpportunityOperateBusinessDto, GcSjStateEnum.UNCOMMIT);
        if (ceCheckOperateParam.isPresent()) {
            return ceCheckOperateParam;
        }
        GcBusinessOpportunity gcBusinessOpportunity = new GcBusinessOpportunity();
        gcBusinessOpportunity.setId(gcBusinessOpportunityOperateBusinessDto.getId());
        gcBusinessOpportunity.setDeleteFlag(DeleteFlagEnum.DELETE.getCode());
        saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.SME_BUSINESS_DELETION, gcBusinessOpportunityOperateBusinessDto.getOperator(), gcBusinessOpportunityOperateBusinessDto.getOperatorName(), null);
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunity) ? Optional.empty() : Optional.of("删除商机失败");
    }

    /**
     * Description: 获取高呈商机客户列表
     * @author: JiuDD
     * @param dto 高呈商机客户查询参数
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityBusinessView>
     * date: 2024/5/20 11:07
     */
    public Page<GcBusinessOpportunityBusinessView> getGcCustList(GcBusinessOpportunityPageCustDto dto) {
        String operator = dto.getOperator();
        if (StrUtil.isBlank(operator)) {
            log.warn("获取高呈商机客户列表，操作人ID不能为空，参数为:{}", JSON.toJSONString(dto));
            return new Page<>();
        }
        Page<GcBusinessOpportunity> page = Page.of(dto.getPageNum(), dto.getPageSize());
        LambdaQueryChainWrapper<GcBusinessOpportunity> lambdaQueryChainWrapper = gcBusinessOpportunityService.lambdaQuery()
                .eq(GcBusinessOpportunity::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        if (EmpPositionConstant.BUSINESS_AREA.equals(dto.getOperatorPosition())) {
            lambdaQueryChainWrapper.eq(GcBusinessOpportunity::getGcAreaId, dto.getOperatorAreaId());
        } else if (EmpPositionConstant.BUSINESS_MAJOR.equals(dto.getOperatorPosition())) {
            lambdaQueryChainWrapper.eq(GcBusinessOpportunity::getGcSubId, dto.getOperatorSubId());
        } else if (EmpPositionConstant.BUSINESS_MANAGER.equals(dto.getOperatorPosition())) {
            lambdaQueryChainWrapper.eq(GcBusinessOpportunity::getGcDeptId, dto.getOperatorDeptId());
        } else if (EmpPositionConstant.BUSINESS_GCKHDB.equals(dto.getOperatorPosition())) {
            lambdaQueryChainWrapper.eq(GcBusinessOpportunity::getGcSalerId, operator);
        } else {
            log.warn("获取高呈商机客户列表，操作人职位异常，参数为:{}", JSON.toJSONString(dto));
            return new Page<>();
        }
        if (StrUtil.isNotBlank(dto.getCustomerName())) {
            lambdaQueryChainWrapper.like(GcBusinessOpportunity::getCustomerName, dto.getCustomerName());
        }
        lambdaQueryChainWrapper
                .groupBy(GcBusinessOpportunity::getCustomerId)
                .orderByDesc(GcBusinessOpportunity::getUpdateTime);

        Page<GcBusinessOpportunity> gcBusinessOpportunityPage = lambdaQueryChainWrapper.page(page);
        Page<GcBusinessOpportunityBusinessView> dubboViewDubboPageInfo = BeanUtil.copyProperties(gcBusinessOpportunityPage, Page.class);
        if (dubboViewDubboPageInfo.getTotal() < 1) {
            return dubboViewDubboPageInfo;
        }
        List<GcBusinessOpportunity> gcBusinessOpportunityList = gcBusinessOpportunityPage.getRecords();
        dubboViewDubboPageInfo.setRecords(CglibUtil.copyList(gcBusinessOpportunityList, GcBusinessOpportunityBusinessView::new));
        return dubboViewDubboPageInfo;
    }

    /**
     * Description: 获取高呈商机客户的项目列表
     * @author: JiuDD
     * @param dto 客户id必传
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityBusinessView>
     * date: 2024/5/20 14:14
     */
    public Page<GcBusinessOpportunityBusinessView> getGcCustProjectList(GcBusinessOpportunityPageCustDto dto) {
        String customerId = dto.getCustomerId();
        if (StrUtil.isBlank(customerId)) {
            log.warn("获取高呈商机客户的项目列表，客户ID不能为空");
            return new Page<>();
        }
        String operator = dto.getOperator();
        if (StrUtil.isBlank(operator)) {
            log.warn("获取高呈商机客户的项目列表，操作人ID不能为空，参数为:{}", JSON.toJSONString(dto));
            return new Page<>();
        }
        Page<GcBusinessOpportunity> page = Page.of(dto.getPageNum(), dto.getPageSize());
        LambdaQueryChainWrapper<GcBusinessOpportunity> lambdaQueryChainWrapper = gcBusinessOpportunityService.lambdaQuery()
                .eq(StrUtil.isNotBlank(customerId), GcBusinessOpportunity::getCustomerId, customerId)
                .eq(GcBusinessOpportunity::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        if (EmpPositionConstant.BUSINESS_AREA.equals(dto.getOperatorPosition())) {
            lambdaQueryChainWrapper.eq(GcBusinessOpportunity::getGcAreaId, dto.getOperatorAreaId());
        } else if (EmpPositionConstant.BUSINESS_MAJOR.equals(dto.getOperatorPosition())) {
            lambdaQueryChainWrapper.eq(GcBusinessOpportunity::getGcSubId, dto.getOperatorSubId());
        } else if (EmpPositionConstant.BUSINESS_MANAGER.equals(dto.getOperatorPosition())) {
            lambdaQueryChainWrapper.eq(GcBusinessOpportunity::getGcDeptId, dto.getOperatorDeptId());
        } else if (EmpPositionConstant.BUSINESS_GCKHDB.equals(dto.getOperatorPosition())) {
            lambdaQueryChainWrapper.eq(GcBusinessOpportunity::getGcSalerId, operator);
        } else {
            log.warn("获取高呈商机客户的项目列表，操作人职位异常，参数为:{}", JSON.toJSONString(dto));
            return new Page<>();
        }
        lambdaQueryChainWrapper.orderByDesc(GcBusinessOpportunity::getUpdateTime);

        Page<GcBusinessOpportunity> gcBusinessOpportunityPage = lambdaQueryChainWrapper.page(page);
        Page<GcBusinessOpportunityBusinessView> dubboViewDubboPageInfo = BeanUtil.copyProperties(gcBusinessOpportunityPage, Page.class);
        if (dubboViewDubboPageInfo.getTotal() < 1) {
            return dubboViewDubboPageInfo;
        }
        List<GcBusinessOpportunity> gcBusinessOpportunityList = gcBusinessOpportunityPage.getRecords();
        dubboViewDubboPageInfo.setRecords(CglibUtil.copyList(gcBusinessOpportunityList, GcBusinessOpportunityBusinessView::new));
        return dubboViewDubboPageInfo;
    }

    /**
     * Description: 转高呈（中小商务）
     * @author: JiuDD
     * @param businessDto  商机添加参数
     * @return java.util.Optional<java.lang.String>
     * date: 2024/5/21 18:51
     */
    public Optional<String> addGcBusinessOpportunityCe(GcSJCooperationSaveBusinessDto businessDto) {
        Optional<String> checkParam = checkAddGcBusinessOpportunityParam("转高呈", businessDto);
        if (checkParam.isPresent()) {
            return checkParam;
        }
        GcBusinessOpportunity gcBusinessOpportunity = CglibUtil.copy(businessDto, GcBusinessOpportunity.class);
        gcBusinessOpportunity.setId(null);
        gcBusinessOpportunity.setSource(GcSjSourceEnum.SUB.getSource());
        gcBusinessOpportunity.setState(GcSjStateEnum.WAIT_MAJOR_CHECK.getState());
        gcBusinessOpportunity.setInitiator(GcSjInitiatorEnum.INITIATOR_ZX.getInitiatorType());
        String customerId = businessDto.getCustomerId();
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, customerId).one();
        if (cmCustProtect != null) {
            List<String> orgIdList = Arrays.asList(cmCustProtect.getAreaId(), cmCustProtect.getSubcompanyId(), cmCustProtect.getBussdeptId());
            Map<String, OrgDataThirdView> orgDataThirdViewMap = orgThirdService.getOrgData(orgIdList);
            //中小市场部商机code
            gcBusinessOpportunity.setTelSjCode(cmCustProtect.getBusioppoCode());
            //中小当前保护的商务、部门、分司、区域
            gcBusinessOpportunity.setCustomerId(customerId);
            gcBusinessOpportunity.setCustomerName(cmCustProtect.getCustName());
            gcBusinessOpportunity.setAreaId(cmCustProtect.getAreaId());
            gcBusinessOpportunity.setAreaName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getAreaId(), new OrgDataThirdView()).getName());
            gcBusinessOpportunity.setSubId(cmCustProtect.getSubcompanyId());
            gcBusinessOpportunity.setSubName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getSubcompanyId(), new OrgDataThirdView()).getName());
            gcBusinessOpportunity.setDeptId(cmCustProtect.getBussdeptId());
            gcBusinessOpportunity.setDeptName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBussdeptId(), new OrgDataThirdView()).getName());
            gcBusinessOpportunity.setSalerId(cmCustProtect.getSalerId());
            gcBusinessOpportunity.setSalerName(employeeThirdService.getEmployeeData(cmCustProtect.getSalerId()).orElse(new EmployeeDataThirdView()).getName());
        } else{
            Optional<EmployeeDataThirdView> loginEmployee = employeeThirdService.getEmployeeData(businessDto.getOperator());
            if (loginEmployee.isPresent()){
                EmployeeDataThirdView loginEmployeeView = loginEmployee.get();
                gcBusinessOpportunity.setAreaId(loginEmployeeView.getAreaId());
                gcBusinessOpportunity.setAreaName(loginEmployeeView.getAreaName());
                gcBusinessOpportunity.setSubId(loginEmployeeView.getSubId());
                gcBusinessOpportunity.setSubName(loginEmployeeView.getSubName());
                gcBusinessOpportunity.setDeptId(loginEmployeeView.getOrgId());
                gcBusinessOpportunity.setDeptName(loginEmployeeView.getOrgName());
            }
        }
        //期望高呈商务信息
        if(businessDto.getExpectGcSalerId() != null && !businessDto.getExpectGcSalerId().isEmpty()){
            Optional<EmployeeDataThirdView> gcEmployeeData = employeeThirdService.getEmployeeData(businessDto.getExpectGcSalerId());
            if (gcEmployeeData.isPresent()) {
                EmployeeDataThirdView gcEmployeeThirdView = gcEmployeeData.get();
                gcBusinessOpportunity.setExpectGcSalerId(businessDto.getExpectGcSalerId());
                gcBusinessOpportunity.setExpectGcSalerName(gcEmployeeThirdView.getName());
                gcBusinessOpportunity.setExpectGcDeptId(gcEmployeeThirdView.getOrgId());
                gcBusinessOpportunity.setExpectGcDeptName(gcEmployeeThirdView.getOrgName());
                gcBusinessOpportunity.setExpectGcSubId(gcEmployeeThirdView.getSubId());
                gcBusinessOpportunity.setExpectGcSubName(gcEmployeeThirdView.getSubName());
                gcBusinessOpportunity.setExpectGcAreaId(gcEmployeeThirdView.getAreaId());
                gcBusinessOpportunity.setExpectGcAreaName(gcEmployeeThirdView.getAreaName());
            }
        }
        Optional<String> sjAddFlag = gcBusinessOpportunityService.save(gcBusinessOpportunity) ? Optional.empty() : Optional.of("转高呈新增商机失败");
        if (!sjAddFlag.isPresent()) {
            saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.SME_BUSINESS_TO_HIGH_PRESENTATION, businessDto.getOperator(), businessDto.getOperatorName(), null);
        }
        return sjAddFlag;
    }

    /**
     * Description: 申请合作（高呈商务）
     * @author: JiuDD
     * @param businessDto  商机添加参数
     * @return java.util.Optional<java.lang.String>
     * date: 2024/5/21 18:51
     */
    public Optional<String> addGcBusinessOpportunity(GcSJCooperationSaveBusinessDto businessDto) {
        Optional<String> checkParam = checkAddGcBusinessOpportunityParam("申请合作", businessDto);
        if (checkParam.isPresent()) {
            return checkParam;
        }
        GcBusinessOpportunity gcBusinessOpportunity = CglibUtil.copy(businessDto, GcBusinessOpportunity.class);
        gcBusinessOpportunity.setId(null);
        gcBusinessOpportunity.setSource(GcSjSourceEnum.SUB.getSource());
        gcBusinessOpportunity.setState(GcSjStateEnum.WAIT_CE_CONFIRM.getState());
        gcBusinessOpportunity.setInitiator(GcSjInitiatorEnum.INITIATOR_GC.getInitiatorType());
        String customerId = businessDto.getCustomerId();
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, customerId).one();
        if (cmCustProtect != null) {
            List<String> orgIdList = Arrays.asList(cmCustProtect.getAreaId(), cmCustProtect.getSubcompanyId(), cmCustProtect.getBussdeptId());
            Map<String, OrgDataThirdView> orgDataThirdViewMap = orgThirdService.getOrgData(orgIdList);
            //中小市场部商机code
            gcBusinessOpportunity.setTelSjCode(cmCustProtect.getBusioppoCode());
            //中小当前保护的商务、部门、分司、区域
            gcBusinessOpportunity.setCustomerName(cmCustProtect.getCustName());
            gcBusinessOpportunity.setAreaId(cmCustProtect.getAreaId());
            gcBusinessOpportunity.setAreaName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getAreaId(), new OrgDataThirdView()).getName());
            gcBusinessOpportunity.setSubId(cmCustProtect.getSubcompanyId());
            gcBusinessOpportunity.setSubName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getSubcompanyId(), new OrgDataThirdView()).getName());
            gcBusinessOpportunity.setDeptId(cmCustProtect.getBussdeptId());
            gcBusinessOpportunity.setDeptName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBussdeptId(), new OrgDataThirdView()).getName());
            gcBusinessOpportunity.setSalerId(cmCustProtect.getSalerId());
            gcBusinessOpportunity.setSalerName(employeeThirdService.getEmployeeData(cmCustProtect.getSalerId()).orElse(new EmployeeDataThirdView()).getName());
        }
        //高呈商务信息
        Optional<EmployeeDataThirdView> gcEmployeeData = employeeThirdService.getEmployeeData(businessDto.getGcSalerId());
        if (gcEmployeeData.isPresent()) {
            EmployeeDataThirdView gcEmployeeThirdView = gcEmployeeData.get();
            gcBusinessOpportunity.setGcSalerName(gcEmployeeThirdView.getName());
            gcBusinessOpportunity.setGcDeptId(gcEmployeeThirdView.getOrgId());
            gcBusinessOpportunity.setGcDeptName(gcEmployeeThirdView.getOrgName());
            gcBusinessOpportunity.setGcSubId(gcEmployeeThirdView.getSubId());
            gcBusinessOpportunity.setGcSubName(gcEmployeeThirdView.getSubName());
            gcBusinessOpportunity.setGcAreaId(gcEmployeeThirdView.getAreaId());
            gcBusinessOpportunity.setGcAreaName(gcEmployeeThirdView.getAreaName());
        }
        Optional<String> sjAddFlag = gcBusinessOpportunityService.save(gcBusinessOpportunity) ? Optional.empty() : Optional.of("申请合作新增商机失败");
        if (!sjAddFlag.isPresent()) {
            saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.HIGH_PRESENTATION_BUSINESS_COOPERATION_REQUEST, businessDto.getOperator(), businessDto.getOperatorName(), null);
        }
        return sjAddFlag;
    }

    /**
     * 校验 申请合作（高呈商务） / 转商机（中小商务） 保存参数
     * @author: JiuDD
     * @param businessDto 高呈商机保存参数
     * @return java.util.Optional<java.lang.String>
     * date: 2024/5/21 18:51
     */
    private Optional<String> checkAddGcBusinessOpportunityParam(String logPrefix, GcSJCooperationSaveBusinessDto businessDto) {
        if (businessDto == null) {
            log.warn("{}，参数不能为空", logPrefix);
            return Optional.of("参数不能为空");
        }
        if (StrUtil.isBlank(businessDto.getCustomerId())) {
            log.warn("{}，客户id不能为空，参数为:{}", logPrefix, JSON.toJSONString(businessDto));
            return Optional.of("客户名称不能为空");
        }
        if (StrUtil.isBlank(businessDto.getRequirementDetail())) {
            log.warn("{}，需求不能为空，参数为:{}", logPrefix, JSON.toJSONString(businessDto));
            return Optional.of("需求不能为空");
        }
        if (businessDto.getRequirementType() == null) {
            log.warn("{}，项目类型不能为空，参数为:{}", logPrefix, JSON.toJSONString(businessDto));
            return Optional.of("需求类型不能为空");
        }
        if (businessDto.getCustomerBudget() == null) {
            log.warn("{}，预算不能为空，参数为:{}", logPrefix, JSON.toJSONString(businessDto));
            return Optional.of("预算不能为空");
        }
        Optional<CustomerDataThirdView> customerDataThirdViewOptional = customerThirdService.getCustomerData(businessDto.getCustomerId());
        if (!customerDataThirdViewOptional.isPresent()) {
            //获取客户信息
            customerDataThirdViewOptional = customerThirdService.getCustomerDataByCustomerName(businessDto.getCustomerName());
            //客户不存在，新增客户
            if (!customerDataThirdViewOptional.isPresent()) {
                CustomerAddThirdView customerAddThirdView = customerThirdService.addCustomer(null, businessDto.getCustomerName(), 2, 5, businessDto.getOperator());
                if (StrUtil.isNotBlank(customerAddThirdView.getErrMsg())) {
                    log.warn("{}，{}，参数为:{}", customerAddThirdView.getErrMsg(), logPrefix, JSON.toJSONString(businessDto));
                    return Optional.of(customerAddThirdView.getErrMsg());
                }
                businessDto.setCustomerId(customerAddThirdView.getCustomerId());
                /*去除客户再次验证是否存在 上一步已经验证过
                customerDataThirdViewOptional = customerThirdService.getCustomerData(customerAddThirdView.getCustomerId());
                if (!customerDataThirdViewOptional.isPresent()) {
                    log.warn("{}，获取客户失败，参数为:{}", logPrefix, JSON.toJSONString(businessDto));
                    return Optional.of("获取客户失败");
                }
                 */
            }
        }
        return Optional.empty();
    }

    /**
     * Description: 中小商务编辑转高呈的商机
     * @author: JiuDD
     * @param businessDto  商机添加参数
     * @return java.util.Optional<java.lang.String>
     * date: 2024/5/21 18:51
     */
    public Optional<String> updateGcBusinessOpportunity(GcSJCooperationSaveBusinessDto businessDto) {
        GcBusinessOpportunityOperateBusinessDto operateBusinessDto = CglibUtil.copy(businessDto, GcBusinessOpportunityOperateBusinessDto.class);
        Optional<String> ceCheckOperateParam = ceCheckOperateParam("中小商务编辑转高呈的商机", operateBusinessDto, GcSjStateEnum.UNCOMMIT);
        if (ceCheckOperateParam.isPresent()) {
            return ceCheckOperateParam;
        }
        Optional<String> checkUpdateGcBusinessOpportunityParam = checkUpdateGcBusinessOpportunityParam(businessDto);
        if (checkUpdateGcBusinessOpportunityParam.isPresent()) {
            return checkUpdateGcBusinessOpportunityParam;
        }
        //组装db对象。   中小商务的姓名、电话、邮箱不保存数据库，需要展示时从接口查
        GcBusinessOpportunity gcBusinessOpportunity = assembleGcBusinessOpportunity(businessDto);
        saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.SME_BUSINESS_EDIT_TO_HIGH_PRESENTATION, businessDto.getOperator(), businessDto.getOperatorName(), null);
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunity) ? Optional.empty() : Optional.of("编辑转高呈的商机失败");
    }

    /**
     * Description: 校验 中小商务编辑转高呈的商机 update参数
     * @author: JiuDD
     * @param businessDto
     * @return java.util.Optional<java.lang.String>
     * date: 2024/5/22 16:50
     */
    private Optional<String> checkUpdateGcBusinessOpportunityParam(GcSJCooperationSaveBusinessDto businessDto) {
        String customerName = businessDto.getCustomerName();
        if (StrUtil.isBlank(customerName)) {
            log.warn("中小商务编辑转高呈的商机，客户名称不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("客户名称不能为空");
        }
        String requirementDetail = businessDto.getRequirementDetail();
        if (StrUtil.isBlank(requirementDetail)) {
            log.warn("中小商务编辑转高呈的商机，需求不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("需求不能为空");
        }
        Integer requirementType = businessDto.getRequirementType();
        if (requirementType == null) {
            log.warn("中小商务编辑转高呈的商机，项目类型不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("需求类型不能为空");
        }
        String linkmanName = businessDto.getLinkmanName();
        if (StrUtil.isBlank(linkmanName)) {
            log.warn("中小商务编辑转高呈的商机，联系人不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("联系人不能为空");
        }
        String linkmanPhone = businessDto.getLinkmanPhone();
        if (StrUtil.isBlank(linkmanPhone)) {
            log.warn("中小商务编辑转高呈的商机，联系电话不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("联系电话不能为空");
        }
        return Optional.empty();
    }

    /**
     * Description: 组装db对象：中小商务编辑转高呈的商机
     * @author: JiuDD
     * @param businessDto
     * @return com.ce.scrm.center.dao.entity.GcBusinessOpportunity
     * date: 2024/5/22 17:07
     */
    private GcBusinessOpportunity assembleGcBusinessOpportunity(GcSJCooperationSaveBusinessDto businessDto) {
        GcBusinessOpportunity gcBusinessOpportunity = new GcBusinessOpportunity();
        // 必填项
        gcBusinessOpportunity.setId(businessDto.getId());
        gcBusinessOpportunity.setRequirementType(businessDto.getRequirementType());
        gcBusinessOpportunity.setRequirementDetail(businessDto.getRequirementDetail());
        gcBusinessOpportunity.setLinkmanName(businessDto.getLinkmanName());
        gcBusinessOpportunity.setLinkmanPhone(businessDto.getLinkmanPhone());
        // 非必填项
        String attachmentIds = businessDto.getAttachmentIds();
        gcBusinessOpportunity.setAttachmentIds(attachmentIds);
        Integer interviewState = businessDto.getInterviewState();
        if (interviewState != null) {
            gcBusinessOpportunity.setInterviewState(interviewState);
        }
        Integer requirementType = businessDto.getRequirementType();
        if (requirementType != null) {
            gcBusinessOpportunity.setRequirementType(requirementType);
        }
        BigDecimal customerBudget = businessDto.getCustomerBudget();
        if (customerBudget != null) {
            gcBusinessOpportunity.setCustomerBudget(customerBudget);
        }
        String expectGcSalerId = businessDto.getExpectGcSalerId();
        if (StrUtil.isNotBlank(expectGcSalerId)) {
            employeeThirdService.getEmployeeData(expectGcSalerId).ifPresent(gcSaler -> {
                gcBusinessOpportunity.setExpectGcSalerId(expectGcSalerId);
                gcBusinessOpportunity.setExpectGcSalerName(gcSaler.getName());
                gcBusinessOpportunity.setExpectGcDeptId(gcSaler.getOrgId());
                gcBusinessOpportunity.setExpectGcDeptName(gcSaler.getOrgName());
                gcBusinessOpportunity.setExpectGcSubId(gcSaler.getSubId());
                gcBusinessOpportunity.setExpectGcSubName(gcSaler.getSubName());
                gcBusinessOpportunity.setExpectGcAreaId(gcSaler.getAreaId());
                gcBusinessOpportunity.setExpectGcAreaName(gcSaler.getAreaName());
            });
        }
        String expectGcAreaId = businessDto.getExpectGcAreaId();
        if (StrUtil.isNotBlank(expectGcAreaId)) {
            gcBusinessOpportunity.setExpectGcAreaId(expectGcAreaId);
        }
        String expectGcAreaName = businessDto.getExpectGcAreaName();
        if (StrUtil.isNotBlank(expectGcAreaName)) {
            gcBusinessOpportunity.setExpectGcAreaName(expectGcAreaName);
        }
        String expectGcSubId = businessDto.getExpectGcSubId();
        if (StrUtil.isNotBlank(expectGcSubId)) {
            gcBusinessOpportunity.setExpectGcSubId(expectGcSubId);
        }
        String expectGcSubName = businessDto.getExpectGcSubName();
        if (StrUtil.isNotBlank(expectGcSubName)) {
            gcBusinessOpportunity.setExpectGcSubName(expectGcSubName);
        }
        String expectGcDeptId = businessDto.getExpectGcDeptId();
        if (StrUtil.isNotBlank(expectGcDeptId)) {
            gcBusinessOpportunity.setExpectGcDeptId(expectGcDeptId);
        }
        String expectGcDeptName = businessDto.getExpectGcDeptName();
        if (StrUtil.isNotBlank(expectGcDeptName)) {
            gcBusinessOpportunity.setExpectGcDeptName(expectGcDeptName);
        }
        String expectGcSalerName = businessDto.getExpectGcSalerName();
        if (StrUtil.isNotBlank(expectGcSalerName)) {
            gcBusinessOpportunity.setExpectGcSalerName(expectGcSalerName);
        }
        String linkmanEmail = businessDto.getLinkmanEmail();
        if (StrUtil.isNotBlank(linkmanEmail)) {
            gcBusinessOpportunity.setLinkmanEmail(linkmanEmail);
        }
        return gcBusinessOpportunity;
    }

    /**
     * Description: 市场部手动分配商机到高呈分司或部门
     * @author: JiuDD
     * @param businessDto  商机添加参数
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     * date: 2024/5/21 21:15
     */
    public Optional<String> assignBusinessOpportunityToGc(GcSJCooperationSaveBusinessDto businessDto) {
        if (Objects.isNull(businessDto.getCustomerBudget())) {
            businessDto.setCustomerBudget(BigDecimal.ZERO);
        }
        Optional<String> checkParam = checkAddGcBusinessOpportunityParam("市场部手动分配转高呈", businessDto);
        if (checkParam.isPresent()) {
            return checkParam;
        }
        Optional<String> checkParamMarketAssign = checkAssignBusinessOpportunityToGcParam(businessDto);
        if (checkParamMarketAssign.isPresent()) {
            return checkParamMarketAssign;
        }
        GcBusinessOpportunity gcBusinessOpportunity = assembleMarketAssignBusinessOpportunity(businessDto);
        Optional<String> sjAddFlag = gcBusinessOpportunityService.save(gcBusinessOpportunity) ? Optional.empty() : Optional.of("市场部分配商机新增商机失败");
        if (!sjAddFlag.isPresent()) {
            saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.CHINA_ENTERPRISE_MARKET_ALLOCATION, businessDto.getOperator(), businessDto.getOperatorName(), null);
        }
        return sjAddFlag;
    }

    private Optional<String> checkAssignBusinessOpportunityToGcParam(GcSJCooperationSaveBusinessDto businessDto) {
        if (businessDto == null) {
            log.warn("市场部手动分配转高呈，参数不能为空");
            return Optional.of("参数不能为空");
        }
        if (StrUtil.isBlank(businessDto.getGcAreaId())) {
            log.warn("市场部手动分配转高呈，高呈区域id不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("高呈区域id不能为空");
        }
        if (StrUtil.isBlank(businessDto.getGcSubId())) {
            log.warn("市场部手动分配转高呈，高呈分司id不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("高呈分司id不能为空");
        }
        return Optional.empty();
    }

    /**
     * Description: 市场部手动分配商机到高呈的商机对象封装
     * @author: JiuDD
     * @param businessDto
     * @return com.ce.scrm.center.dao.entity.GcBusinessOpportunity
     * date: 2024/5/29 14:07
     */
    private GcBusinessOpportunity assembleMarketAssignBusinessOpportunity(GcSJCooperationSaveBusinessDto businessDto) {
        GcBusinessOpportunity gcBusinessOpportunity = CglibUtil.copy(businessDto, GcBusinessOpportunity.class);
        gcBusinessOpportunity.setId(null);
        gcBusinessOpportunity.setSource(GcSjSourceEnum.CE_MARKET.getSource());
        //商机来源市场部，商机状态置为：未处理
        gcBusinessOpportunity.setState(GcSjStateEnum.NON_HANDLE.getState());
        gcBusinessOpportunity.setInitiator(GcSjInitiatorEnum.INITIATOR_NULL.getInitiatorType());
        String customerId = businessDto.getCustomerId();
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, customerId).one();
        if (cmCustProtect != null) {
            List<String> orgIdList = Arrays.asList(cmCustProtect.getAreaId(), cmCustProtect.getSubcompanyId(), cmCustProtect.getBussdeptId());
            Map<String, OrgDataThirdView> orgDataThirdViewMap = orgThirdService.getOrgData(orgIdList);
            //中小市场部商机code
            gcBusinessOpportunity.setTelSjCode(cmCustProtect.getBusioppoCode());
            //中小当前保护的商务、部门、分司、区域
            gcBusinessOpportunity.setCustomerId(customerId);
            gcBusinessOpportunity.setCustomerName(cmCustProtect.getCustName());
            // 当前是高呈保护
            if (Objects.equals(OrgThirdService.KA_SALE_DEPT_AREA_ID, cmCustProtect.getAreaId())) {
                gcBusinessOpportunity.setGcAreaId(cmCustProtect.getAreaId());
                gcBusinessOpportunity.setGcAreaName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getAreaId(), new OrgDataThirdView()).getName());
                gcBusinessOpportunity.setGcSubId(cmCustProtect.getSubcompanyId());
                gcBusinessOpportunity.setGcSubName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getSubcompanyId(), new OrgDataThirdView()).getName());
                gcBusinessOpportunity.setGcDeptId(cmCustProtect.getBussdeptId());
                gcBusinessOpportunity.setGcDeptName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBussdeptId(), new OrgDataThirdView()).getName());
                gcBusinessOpportunity.setGcSalerId(cmCustProtect.getSalerId());
                gcBusinessOpportunity.setGcSalerName(employeeThirdService.getEmployeeData(cmCustProtect.getSalerId()).orElse(new EmployeeDataThirdView()).getName());
            } else {
                // 当前是中小保护
                gcBusinessOpportunity.setAreaId(cmCustProtect.getAreaId());
                gcBusinessOpportunity.setAreaName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getAreaId(), new OrgDataThirdView()).getName());
                gcBusinessOpportunity.setSubId(cmCustProtect.getSubcompanyId());
                gcBusinessOpportunity.setSubName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getSubcompanyId(), new OrgDataThirdView()).getName());
                gcBusinessOpportunity.setDeptId(cmCustProtect.getBussdeptId());
                gcBusinessOpportunity.setDeptName(orgDataThirdViewMap.getOrDefault(cmCustProtect.getBussdeptId(), new OrgDataThirdView()).getName());
                gcBusinessOpportunity.setSalerId(cmCustProtect.getSalerId());
                gcBusinessOpportunity.setSalerName(employeeThirdService.getEmployeeData(cmCustProtect.getSalerId()).orElse(new EmployeeDataThirdView()).getName());
            }
        }
        //期望高呈商务信息
        String expectGcSalerId = businessDto.getExpectGcSalerId();
        if (StrUtil.isNotBlank(expectGcSalerId)) {
            Optional<EmployeeDataThirdView> gcEmployeeData = employeeThirdService.getEmployeeData(expectGcSalerId);
            if (gcEmployeeData.isPresent()) {
                EmployeeDataThirdView gcEmployeeThirdView = gcEmployeeData.get();
                gcBusinessOpportunity.setExpectGcSalerId(expectGcSalerId);
                gcBusinessOpportunity.setExpectGcSalerName(gcEmployeeThirdView.getName());
                gcBusinessOpportunity.setExpectGcDeptId(gcEmployeeThirdView.getOrgId());
                gcBusinessOpportunity.setExpectGcDeptName(gcEmployeeThirdView.getOrgName());
                gcBusinessOpportunity.setExpectGcSubId(gcEmployeeThirdView.getSubId());
                gcBusinessOpportunity.setExpectGcSubName(gcEmployeeThirdView.getSubName());
                gcBusinessOpportunity.setExpectGcAreaId(gcEmployeeThirdView.getAreaId());
                gcBusinessOpportunity.setExpectGcAreaName(gcEmployeeThirdView.getAreaName());
            }
        }
        //分配到高呈的 部门（部门可选）
        String gcDeptId = businessDto.getGcDeptId();
        if (StrUtil.isNotBlank(gcDeptId)) {
            //部门信息
            Optional<OrgDataThirdView> orgData = orgThirdService.getOrgData(gcDeptId);
            if (orgData.isPresent()) {
                OrgDataThirdView orgDataThirdView = orgData.get();
                gcBusinessOpportunity.setGcDeptId(gcDeptId);
                gcBusinessOpportunity.setGcDeptName(orgDataThirdView.getName());
            }
        }
        //分配到高呈的 分司信息、区域信息（区域、分司必选）
        String gcAreaId = businessDto.getGcAreaId();
        String gcSubId = businessDto.getGcSubId();
        Map<String, OrgDataThirdView> orgData = orgThirdService.getOrgData(Arrays.asList(gcAreaId, gcSubId));
        gcBusinessOpportunity.setGcAreaId(gcAreaId);
        gcBusinessOpportunity.setGcAreaName(orgData.getOrDefault(gcAreaId, new OrgDataThirdView()).getName());
        gcBusinessOpportunity.setGcSubId(gcSubId);
        gcBusinessOpportunity.setGcSubName(orgData.getOrDefault(gcSubId, new OrgDataThirdView()).getName());
        return gcBusinessOpportunity;
    }

    /**
     * Description: 转高呈待总监审批客户列表
     * @author: JiuDD
     * @param dto
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityBusinessView>
     * date: 2024/5/22 11:31
     */
    public Page<GcBusinessOpportunityBusinessView> getWaitMajorCheckCustList(GcBusinessOpportunityPageCustDto dto) {
        String subId = dto.getSubId();
        if (StrUtil.isBlank(subId)) {
            log.warn("获取待总监审批客户列表，分司ID不能为空，参数为:{}", JSON.toJSONString(dto));
            return new Page<>();
        }
        Page<GcBusinessOpportunity> page = Page.of(dto.getPageNum(), dto.getPageSize());
        LambdaQueryChainWrapper<GcBusinessOpportunity> lambdaQueryChainWrapper = gcBusinessOpportunityService.lambdaQuery()
                .eq(GcBusinessOpportunity::getSubId, subId)
                .eq(GcBusinessOpportunity::getSource, GcSjSourceEnum.SUB.getSource())
                .eq(Objects.nonNull(dto.getState()), GcBusinessOpportunity::getState, dto.getState())
                .ge(GcBusinessOpportunity::getState, GcSjStateEnum.WAIT_MAJOR_CHECK.getState())
                .eq(GcBusinessOpportunity::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .like(StrUtil.isNotBlank(dto.getCustomerName()), GcBusinessOpportunity::getCustomerName, dto.getCustomerName())
                .orderByDesc(GcBusinessOpportunity::getUpdateTime);
        Page<GcBusinessOpportunity> gcBusinessOpportunityPage = lambdaQueryChainWrapper.page(page);
        Page<GcBusinessOpportunityBusinessView> dubboViewDubboPageInfo = BeanUtil.copyProperties(gcBusinessOpportunityPage, Page.class);
        if (dubboViewDubboPageInfo.getTotal() < 1) {
            return dubboViewDubboPageInfo;
        }
        List<GcBusinessOpportunity> gcBusinessOpportunityList = gcBusinessOpportunityPage.getRecords();
        dubboViewDubboPageInfo.setRecords(CglibUtil.copyList(gcBusinessOpportunityList, GcBusinessOpportunityBusinessView::new));
        return dubboViewDubboPageInfo;
    }

    /**
     * Description: 审核转高呈商机（中小总监）
     * @author: JiuDD
     * @param businessDto
     * @return java.util.Optional<java.lang.String>
     * date: 2024/5/22 14:56
     */
    public Optional<String> majorCheckCust(MajorCheckCustBusinessDto businessDto) {
        Optional<String> checkParams = checkMajorCheckCustParams(businessDto, GcSjStateEnum.WAIT_MAJOR_CHECK);
        if (checkParams.isPresent()) {
            return checkParams;
        }
        GcBusinessOpportunity gcBusinessOpportunity = new GcBusinessOpportunity();
        gcBusinessOpportunity.setId(businessDto.getId());
        gcBusinessOpportunity.setCheckMajorId(businessDto.getCheckMajorId());
        gcBusinessOpportunity.setCheckMajorName(businessDto.getCheckMajorName());
        gcBusinessOpportunity.setCheckMajorPhone(businessDto.getCheckMajorPhone());
        gcBusinessOpportunity.setRefuseReason(businessDto.getRefuseReason());
        Integer approvalState = businessDto.getApprovalState();
        if (1 == approvalState) {
            gcBusinessOpportunity.setState(GcSjStateEnum.NON_HANDLE.getState());
            saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.SME_DIRECTOR_APPROVAL, businessDto.getOperator(), businessDto.getOperatorName(), null);
        }
        if (2 == approvalState) {
            gcBusinessOpportunity.setState(GcSjStateEnum.MAJOR_CHECK_REFUSE.getState());
            saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.SME_DIRECTOR_REJECTION, businessDto.getOperator(), businessDto.getOperatorName(), null);
        }
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunity) ? Optional.empty() : Optional.of("审核转高呈商机失败");
    }

    /**
     * Description: 校验 中小总监审核转高呈商机 参数
     * @author: JiuDD
     * @param businessDto
     * @return java.util.Optional<java.lang.String>
     * date: 2024/5/22 15:08
     */
    private Optional<String> checkMajorCheckCustParams(MajorCheckCustBusinessDto businessDto, GcSjStateEnum gcSjStateEnum) {
        if (businessDto == null) {
            log.warn("中小总监审核转高呈商机，参数不能为空");
            return Optional.of("参数不能为空");
        }
        Long sjId = businessDto.getId();
        if (sjId == null) {
            log.warn("中小总监审核转高呈商机，商机ID不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("商机ID不能为空");
        }
        String checkMajorId = businessDto.getCheckMajorId();
        if (StrUtil.isBlank(checkMajorId)) {
            log.warn("中小总监审核转高呈商机，中小总监ID不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("总监ID不能为空");
        }
        String checkMajorName = businessDto.getCheckMajorName();
        if (StrUtil.isBlank(checkMajorName)) {
            log.warn("中小总监审核转高呈商机，中小总监姓名不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("总监姓名不能为空");
        }
        String checkMajorPhone = businessDto.getCheckMajorPhone();
        if (StrUtil.isBlank(checkMajorPhone)) {
            log.warn("中小总监审核转高呈商机，中小总监手机号不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("总监手机号不能为空");
        }
        Integer approvalState = businessDto.getApprovalState();
        if (approvalState == null) {
            log.warn("中小总监审核转高呈商机，审批状态不能为空，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("审批状态不能为空");
        }
        GcBusinessOpportunity gcBusinessOpportunity = gcBusinessOpportunityService.getById(sjId);
        if (gcBusinessOpportunity == null || Objects.equals(DeleteFlagEnum.DELETE.getCode(), gcBusinessOpportunity.getDeleteFlag())) {
            log.warn("中小总监审核转高呈商机，商机不存在，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("商机不存在");
        }
        if (!businessDto.getSubId().equals(gcBusinessOpportunity.getSubId())) {
            log.warn("中小总监审核转高呈商机，不能操作其它分司的商机数据，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("不能操作其它分司的商机数据");
        }
        GcSjSourceEnum gcSjSourceEnum = GcSjSourceEnum.get(gcBusinessOpportunity.getSource());
        if (GcSjSourceEnum.SUB != gcSjSourceEnum) {
            log.warn("中小总监审核转高呈商机，当前来源商机不能操作，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("当前来源商机不能操作");
        }
        if (gcSjStateEnum != GcSjStateEnum.get(gcBusinessOpportunity.getState())) {
            log.warn("中小总监审核转高呈商机，当前状态下商机不能操作，参数为:{}", JSON.toJSONString(businessDto));
            return Optional.of("当前状态下商机不能操作");
        }
        return Optional.empty();
    }

    /**
     * 转高呈数据分页列表
     * @param ceQueryGcBusinessOpportunityPageBusinessDto 转高呈分页参数
     * <AUTHOR>
     * @date 2024/5/28 下午3:55
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityBusinessView>
     **/
    public Page<GcBusinessOpportunityBusinessView> sendGcList(CeQueryGcBusinessOpportunityPageBusinessDto ceQueryGcBusinessOpportunityPageBusinessDto) {
        Page<GcBusinessOpportunity> page = Page.of(ceQueryGcBusinessOpportunityPageBusinessDto.getPageNum(), ceQueryGcBusinessOpportunityPageBusinessDto.getPageSize());
        LambdaQueryChainWrapper<GcBusinessOpportunity> gcBusinessOpportunityLambdaQueryChainWrapper = gcBusinessOpportunityService.lambdaQuery()
                .eq(GcBusinessOpportunity::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(GcBusinessOpportunity::getSource, GcSjSourceEnum.SUB.getSource())
                .eq(GcBusinessOpportunity::getInitiator, GcSjInitiatorEnum.INITIATOR_ZX.getInitiatorType())
                .like(StrUtil.isNotBlank(ceQueryGcBusinessOpportunityPageBusinessDto.getCustomerName()), GcBusinessOpportunity::getCustomerName, ceQueryGcBusinessOpportunityPageBusinessDto.getCustomerName());
        if (EmpPositionConstant.BUSINESS_MAJOR.equals(ceQueryGcBusinessOpportunityPageBusinessDto.getOperatorPosition())) {
            gcBusinessOpportunityLambdaQueryChainWrapper.eq(GcBusinessOpportunity::getSubId, ceQueryGcBusinessOpportunityPageBusinessDto.getOperatorSubId());
        }
        if (EmpPositionConstant.managerPositonList.contains(ceQueryGcBusinessOpportunityPageBusinessDto.getOperatorPosition())) {
            gcBusinessOpportunityLambdaQueryChainWrapper.eq(GcBusinessOpportunity::getDeptId, ceQueryGcBusinessOpportunityPageBusinessDto.getOperatorDeptId());
        }
        if (EmpPositionConstant.salerPositonNoManagerList.contains(ceQueryGcBusinessOpportunityPageBusinessDto.getOperatorPosition())) {
            gcBusinessOpportunityLambdaQueryChainWrapper.eq(GcBusinessOpportunity::getSalerId, ceQueryGcBusinessOpportunityPageBusinessDto.getOperator());
        }
        Page<GcBusinessOpportunity> gcBusinessOpportunityPage = gcBusinessOpportunityLambdaQueryChainWrapper.orderByDesc(GcBusinessOpportunity::getUpdateTime).page(page);
        Page<GcBusinessOpportunityBusinessView> gcBusinessOpportunityBusinessViewPage = BeanUtil.copyProperties(gcBusinessOpportunityPage, Page.class);
        if (gcBusinessOpportunityBusinessViewPage.getTotal() < 1) {
            return gcBusinessOpportunityBusinessViewPage;
        }
        gcBusinessOpportunityBusinessViewPage.setRecords(CglibUtil.copyList(gcBusinessOpportunityPage.getRecords(), GcBusinessOpportunityBusinessView::new));
        return gcBusinessOpportunityBusinessViewPage;
    }

    /**
     * 申请合作数据分页列表
     * @param ceQueryGcBusinessOpportunityPageBusinessDto 申请合作分页参数
     * <AUTHOR>
     * @date 2024/5/28 下午3:55
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityBusinessView>
     **/
    public Page<GcBusinessOpportunityBusinessView> applyCooperationPageList(CeQueryGcBusinessOpportunityPageBusinessDto ceQueryGcBusinessOpportunityPageBusinessDto) {
        Page<GcBusinessOpportunity> page = Page.of(ceQueryGcBusinessOpportunityPageBusinessDto.getPageNum(), ceQueryGcBusinessOpportunityPageBusinessDto.getPageSize());
        LambdaQueryChainWrapper<GcBusinessOpportunity> gcBusinessOpportunityLambdaQueryChainWrapper = gcBusinessOpportunityService.lambdaQuery()
                .eq(GcBusinessOpportunity::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(GcBusinessOpportunity::getSource, GcSjSourceEnum.SUB.getSource())
                .eq(GcBusinessOpportunity::getInitiator, GcSjInitiatorEnum.INITIATOR_GC.getInitiatorType())
                .like(StrUtil.isNotBlank(ceQueryGcBusinessOpportunityPageBusinessDto.getCustomerName()), GcBusinessOpportunity::getCustomerName, ceQueryGcBusinessOpportunityPageBusinessDto.getCustomerName())
                .eq(GcBusinessOpportunity::getSalerId, ceQueryGcBusinessOpportunityPageBusinessDto.getOperator());
        Page<GcBusinessOpportunity> gcBusinessOpportunityPage = gcBusinessOpportunityLambdaQueryChainWrapper.orderByDesc(GcBusinessOpportunity::getUpdateTime).page(page);
        Page<GcBusinessOpportunityBusinessView> gcBusinessOpportunityBusinessViewPage = BeanUtil.copyProperties(gcBusinessOpportunityPage, Page.class);
        if (gcBusinessOpportunityBusinessViewPage.getTotal() < 1) {
            return gcBusinessOpportunityBusinessViewPage;
        }
        gcBusinessOpportunityBusinessViewPage.setRecords(CglibUtil.copyList(gcBusinessOpportunityPage.getRecords(), GcBusinessOpportunityBusinessView::new));
        return gcBusinessOpportunityBusinessViewPage;
    }

    /**
     * 确认高呈商机合作
     * @param gcBusinessOpportunityOperateBusinessDto   确认合作参数
     * <AUTHOR>
     * @date 2024/5/28 下午5:06
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> confirmCooperation(GcBusinessOpportunityOperateBusinessDto gcBusinessOpportunityOperateBusinessDto) {
        GcBusinessOpportunity gcBusinessOpportunity = gcBusinessOpportunityService.getById(gcBusinessOpportunityOperateBusinessDto.getId());
        if (gcBusinessOpportunity == null || Objects.equals(DeleteFlagEnum.DELETE.getCode(), gcBusinessOpportunity.getDeleteFlag())) {
            log.warn("确认高呈商机合作，商机不存在，参数为:{}", JSON.toJSONString(gcBusinessOpportunityOperateBusinessDto));
            return Optional.of("商机不存在");
        }
        if (GcSjSourceEnum.SUB != GcSjSourceEnum.get(gcBusinessOpportunity.getSource())) {
            log.warn("确认高呈商机合作，当前来源商机不能操作，参数为:{}", JSON.toJSONString(gcBusinessOpportunityOperateBusinessDto));
            return Optional.of("当前来源商机不能操作");
        }
        if (GcSjStateEnum.WAIT_CE_CONFIRM != GcSjStateEnum.get(gcBusinessOpportunity.getState())) {
            log.warn("确认高呈商机合作，当前状态下商机不能操作，参数为:{}", JSON.toJSONString(gcBusinessOpportunityOperateBusinessDto));
            return Optional.of("当前状态下商机不能操作");
        }
        GcBusinessOpportunity gcBusinessOpportunityUpdate = new GcBusinessOpportunity();
        gcBusinessOpportunityUpdate.setId(gcBusinessOpportunityOperateBusinessDto.getId());
        gcBusinessOpportunityUpdate.setState(GcSjStateEnum.WAIT_MAJOR_CHECK.getState());
        saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.SME_BUSINESS_CONFIRM_COOPERATION_REQUEST, gcBusinessOpportunityOperateBusinessDto.getOperator(), gcBusinessOpportunityOperateBusinessDto.getOperatorName(), null);
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunityUpdate) ? Optional.empty() : Optional.of("确认高呈商机合作失败");
    }

    /**
     * 拒绝高呈商机合作
     * @param gcBusinessOpportunityOperateBusinessDto   拒绝合作参数
     * <AUTHOR>
     * @date 2024/5/28 下午5:06
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> refuseCooperation(GcBusinessOpportunityOperateBusinessDto gcBusinessOpportunityOperateBusinessDto) {
        GcBusinessOpportunity gcBusinessOpportunity = gcBusinessOpportunityService.getById(gcBusinessOpportunityOperateBusinessDto.getId());
        if (gcBusinessOpportunity == null || Objects.equals(DeleteFlagEnum.DELETE.getCode(), gcBusinessOpportunity.getDeleteFlag())) {
            log.warn("拒绝高呈商机合作，商机不存在，参数为:{}", JSON.toJSONString(gcBusinessOpportunityOperateBusinessDto));
            return Optional.of("商机不存在");
        }
        if (GcSjSourceEnum.SUB != GcSjSourceEnum.get(gcBusinessOpportunity.getSource())) {
            log.warn("拒绝高呈商机合作，当前来源商机不能操作，参数为:{}", JSON.toJSONString(gcBusinessOpportunityOperateBusinessDto));
            return Optional.of("当前来源商机不能操作");
        }
        if (GcSjStateEnum.WAIT_CE_CONFIRM != GcSjStateEnum.get(gcBusinessOpportunity.getState())) {
            log.warn("拒绝高呈商机合作，当前状态下商机不能操作，参数为:{}", JSON.toJSONString(gcBusinessOpportunityOperateBusinessDto));
            return Optional.of("当前状态下商机不能操作");
        }
        GcBusinessOpportunity gcBusinessOpportunityUpdate = new GcBusinessOpportunity();
        gcBusinessOpportunityUpdate.setId(gcBusinessOpportunityOperateBusinessDto.getId());
        gcBusinessOpportunityUpdate.setState(GcSjStateEnum.CE_REFUSE.getState());
        saveSjLog(gcBusinessOpportunity.getId(), GcSjOperateLogTypeEnum.SME_BUSINESS_CONFIRM_COOPERATION_REQUEST, gcBusinessOpportunityOperateBusinessDto.getOperator(), gcBusinessOpportunityOperateBusinessDto.getOperatorName(), null);
        return gcBusinessOpportunityService.updateById(gcBusinessOpportunityUpdate) ? Optional.empty() : Optional.of("拒绝高呈商机合作失败");
    }

    /**
     * 所有数据分页列表查询
     * @param ceQueryGcBusinessOpportunityPageBusinessDto  分页参数
     * <AUTHOR>
     * @date 2024/5/28 下午7:19
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityBusinessView>
     **/
    public Page<GcBusinessOpportunityBusinessView> allPageList(CeQueryGcBusinessOpportunityPageBusinessDto ceQueryGcBusinessOpportunityPageBusinessDto) {
        Page<GcBusinessOpportunity> gcBusinessOpportunityPage = gcBusinessOpportunityService.lambdaQuery()
                .eq(GcBusinessOpportunity::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .like(StrUtil.isNotBlank(ceQueryGcBusinessOpportunityPageBusinessDto.getCustomerName()), GcBusinessOpportunity::getCustomerName, ceQueryGcBusinessOpportunityPageBusinessDto.getCustomerName())
                .orderByDesc(GcBusinessOpportunity::getUpdateTime)
                .page(Page.of(ceQueryGcBusinessOpportunityPageBusinessDto.getPageNum(), ceQueryGcBusinessOpportunityPageBusinessDto.getPageSize()));
        Page<GcBusinessOpportunityBusinessView> gcBusinessOpportunityBusinessViewPage = BeanUtil.copyProperties(gcBusinessOpportunityPage, Page.class);
        if (gcBusinessOpportunityBusinessViewPage.getTotal() < 1) {
            return gcBusinessOpportunityBusinessViewPage;
        }
        gcBusinessOpportunityBusinessViewPage.setRecords(CglibUtil.copyList(gcBusinessOpportunityPage.getRecords(), GcBusinessOpportunityBusinessView::new));
        return gcBusinessOpportunityBusinessViewPage;
    }

    /**
     * 添加高呈商机操作日志
     * @param sjId  商机ID
     * @param gcSjOperateLogTypeEnum    操作类型
     * @param operator  操作人ID
     * @param operatorName  操作人名称
     * <AUTHOR>
     * @date 2024/5/31 上午10:41
     **/
    private void saveSjLog(Long sjId, GcSjOperateLogTypeEnum gcSjOperateLogTypeEnum, String operator, String operatorName, String remark) {
        GcBusinessOpportunityLog gcBusinessOpportunityLog = new GcBusinessOpportunityLog();
        gcBusinessOpportunityLog.setSjId(sjId);
        gcBusinessOpportunityLog.setOperateType(gcSjOperateLogTypeEnum.getOperateType());
        gcBusinessOpportunityLog.setOperator(operator);
        gcBusinessOpportunityLog.setOperatorName(operatorName);
        gcBusinessOpportunityLog.setRemark(remark);
        gcBusinessOpportunityLogService.save(gcBusinessOpportunityLog);
    }

    /**
     * 获取高呈商机操作日志
     * @param sjId  商机ID
     * <AUTHOR>
     * @date 2024/5/31 下午5:08
     * @return java.util.List<com.ce.scrm.center.dao.entity.GcBusinessOpportunityLog>
     **/
    public List<GcBusinessOpportunityLog> getOperateLog(Long sjId) {
        return Optional.ofNullable(gcBusinessOpportunityLogService.lambdaQuery()
                .eq(GcBusinessOpportunityLog::getSjId, sjId)
                .orderByDesc(GcBusinessOpportunityLog::getOperateTime)
                .list()).orElse(new ArrayList<>());
    }

    /**
     * 获取状态
     *
     * @param custId 客户id
     * @return
     */
    public Optional<Integer> getGcBusinessState(String custId) {
        if (StrUtil.isEmpty(custId)) {
            log.error("[getGcBusinessState]--custId is null");
            return Optional.empty();
        }
        LambdaQueryWrapper<GcBusinessOpportunity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //客户id
        lambdaQueryWrapper.eq(GcBusinessOpportunity::getCustomerId, custId);
        //未删除状态
        lambdaQueryWrapper.eq(GcBusinessOpportunity::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        //根据更新时间倒序
        lambdaQueryWrapper.orderByDesc(GcBusinessOpportunity::getUpdateTime);
        // 只取第一条数据
        lambdaQueryWrapper.last("LIMIT 1");
        GcBusinessOpportunity gcBusinessOpportunity = gcBusinessOpportunityService.getOne(lambdaQueryWrapper);
        if (gcBusinessOpportunity != null && gcBusinessOpportunity.getState() != null) {
            return Optional.of(gcBusinessOpportunity.getState());
        }
        return Optional.empty();
    }
}