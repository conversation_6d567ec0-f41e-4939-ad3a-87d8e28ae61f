package com.ce.scrm.center.service.business.entity.ai.crm;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Description: 商机
 * @Author: lijinpeng
 * @Date: 2025/2/17 17:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessOpportunityView implements Serializable {

    /**
     * 客户需求
     */
    @JSONField(name = "客户需求")
    private String custRequirement;

    /**
     * 资讯产品名称
     */
    @JSONField(name = "资讯产品名称列表")
    private List<String> oppoReferProductNameList;

    /**
     * 商机等级
     */
    @JSONField(name = "商机等级")
    private String intentTag;

    /**
     * 成为商机的时间
     */
    @JSONField(name = "成为商机的时间" , format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
