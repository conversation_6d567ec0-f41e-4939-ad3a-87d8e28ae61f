package com.ce.scrm.center.service.business.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2024/12/9 10:57
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerAffiliatedEnterpriseBusinessDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 是否获取详情 1-是 0-否
	 */
	private Integer type;

	/**
	 * 企业唯一编码
	 */
	private String pid;

	/**
	 * 姓名
	 */
	private String name;

	/**
	 * 保护关系表 cm_cust_protect.cust_name
	 */
	private String customerName;

}
