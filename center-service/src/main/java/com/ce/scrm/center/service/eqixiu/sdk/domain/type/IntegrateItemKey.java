package com.ce.scrm.center.service.eqixiu.sdk.domain.type;

public enum IntegrateItemKey {

    AUTONOMOUS_USER_SYSTEM("1002", "自主用户系统"),
    INTERACTIVE_CUSTOM_RULES("1006", "互动-自定义规则"),
    INTERACTIVE_CUSTOM_DRAW_TIMES("1012", "互动-自定义抽奖次数"),
    PAGE_INTEGRATION("1004", "页面集成"),
    CUSTOM_SCRIPT_JS("1504", "自定义脚本（JS）");

    // 成员变量
    private final String value;
    private final String title;

    // 构造函数
    IntegrateItemKey(String value, String title) {
        this.value = value;
        this.title = title;
    }

    // 通过key获取对应的枚举值
    public static boolean isValidKey(String key) {
        for (IntegrateItemKey param : IntegrateItemKey.values()) {
            if (param.getValue().equals(key)) {
                return true;
            }
        }
        return false;
    }

    // 获取key
    public String getValue() {
        return value;
    }

    // 获取描述
    public String getTitle() {
        return title;
    }

}


