package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.constants.ScrmAppConstants;
import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import cn.ce.cesupport.enums.CustomerPresentStageEnum;
import cn.ce.cesupport.enums.DeleteFlagEnum;
import cn.ce.cesupport.enums.SortEnum;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.ce.cesupport.enums.favorites.HighSearchProtectStatusEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerCirculationSetting;
import com.ce.scrm.center.dao.entity.CustomerCirculationSpecialSetting;
import com.ce.scrm.center.dao.entity.CustomerWillCirculation;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.CustomerWillCirculationService;
import com.ce.scrm.center.dao.service.ICustomerCirculationSettingService;
import com.ce.scrm.center.dao.service.ICustomerCirculationSpecialSettingService;
import com.ce.scrm.center.service.business.entity.dto.CirculationLossPageBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.CirculationLossUpdateBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.EmpCustSiteClockBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.CustomerLossLogBusinessDto;
import com.ce.scrm.center.service.business.entity.view.CirculationLossPageBusinessView;
import com.ce.scrm.center.service.business.entity.view.SmaDictionaryItemView;
import com.ce.scrm.center.service.constant.LeadDayConstants;
import com.ce.scrm.center.service.constant.SmsTemplateConstants;
import com.ce.scrm.center.service.enums.*;
import com.ce.scrm.center.service.third.entity.view.*;
import com.ce.scrm.center.service.third.invoke.*;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.util.date.DateUtils;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerUpdateDubboDto;
import com.dianping.cat.util.StringUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_PRESENT_STAGE_TOPIC;


/**
 * description: 流转流失客户业务类
 * @author: DD.Jiu
 * date: 2024/7/22.
 */
@Slf4j
@Service
public class CirculationLossBusiness {
    @Resource
    private CustomerWillCirculationService customerWillCirculationService;
    @Resource
    private SendWxMessage sendWxMessage;
    @Resource
    private CmCustProtectService cmCustProtectService;
    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;
    @Resource
    private OrgThirdService orgThirdService;
    @Resource
    private EmployeeThirdService employeeThirdService;
    @Resource
    private SmaDictionaryItemBusiness smaDictionaryItemBusiness;
    @Resource
    private CustomerThirdService customerThirdService;
    @Resource
    private RocketMqOperate rocketMqOperate;
    @Resource
    private ICustomerCirculationSpecialSettingService customerCirculationSpecialSettingService;
    @Resource
    private CirculationLossBusiness circulationLossBusiness;
    @Resource
    private ICustomerCirculationSettingService customerCirculationSettingService;
    @Resource
    private CustomerCirculationSettingBusiness customerCirculationSettingBusiness;
    @Resource
    private EmpCustSiteClockThirdService empCustSiteClockThirdService;


    @Resource
    private ProtectBusiness protectBusiness;
    @Resource
    private CustomerLossLogBusiness customerLossLogBusiness;
    @Resource
    private BigDataThirdService bigDataThirdService;

    @Value("${mq.prefix}" + CESUPPORT_SCRM_PRESENT_STAGE_TOPIC)
    String CESUPPORT_SCRM_CUSTOMER_PRESENT_STAGE_TOPIC;

    /**
     * Description: 获取待流转（流失）记录
     * @author: JiuDD
     * @param businessDto
     * @return com.ce.scrm.center.dao.entity.CustomerWillCirculation
     * date: 2024/7/24 17:01
     */
    public CustomerWillCirculation getOneCirculationLoss(CirculationLossPageBusinessDto businessDto) {
        return customerWillCirculationService.lambdaQuery()
                .eq(Objects.nonNull(businessDto.getDeleteFlag()), CustomerWillCirculation::getDeleteFlag, businessDto.getDeleteFlag())
                .eq(Objects.nonNull(businessDto.getOrigin()), CustomerWillCirculation::getOrigin, businessDto.getOrigin())
                .eq(StrUtil.isNotBlank(businessDto.getCustId()), CustomerWillCirculation::getCustId, businessDto.getCustId())
                .eq(StrUtil.isNotBlank(businessDto.getCustName()), CustomerWillCirculation::getCustName, businessDto.getCustName())
                .eq(StrUtil.isNotBlank(businessDto.getSalerId()), CustomerWillCirculation::getSalerId, businessDto.getSalerId())
                .orderByDesc(CustomerWillCirculation::getUpdateTime)
                .last("LIMIT 1")
                .one();
    }

    public Page<CirculationLossPageBusinessView> getCirculationList(CirculationLossPageBusinessDto businessDto) {
        if (Objects.isNull(businessDto)) {
            return new Page<>();
        }
        Page<CustomerWillCirculation> page = Page.of(businessDto.getPageNum(), businessDto.getPageSize());
        LambdaQueryChainWrapper<CustomerWillCirculation> lambdaQueryChainWrapper = customerWillCirculationService.lambdaQuery();
        if (Objects.equals(businessDto.getOrigin(), AssignCustSourceSpecialEnum.LOSS.getValue())) {
            //待流失客户
            lambdaQueryChainWrapper.eq(CustomerWillCirculation::getOrigin, AssignCustSourceSpecialEnum.LOSS.getValue());
        } else {
            //待流转客户
            lambdaQueryChainWrapper.eq(CustomerWillCirculation::getOrigin, AssignCustSourceSpecialEnum.CIRCULATION.getValue());
        }
        lambdaQueryChainWrapper
                .eq(StrUtil.isNotBlank(businessDto.getAreaId()), CustomerWillCirculation::getAreaId, businessDto.getAreaId())
                .eq(StrUtil.isNotBlank(businessDto.getSubId()), CustomerWillCirculation::getSubId, businessDto.getSubId())
                .eq(StrUtil.isNotBlank(businessDto.getBuId()), CustomerWillCirculation::getBuId, businessDto.getBuId())
                .eq(StrUtil.isNotBlank(businessDto.getDeptId()), CustomerWillCirculation::getDeptId, businessDto.getDeptId())
                .eq(businessDto.getCustomerLayer() != null, CustomerWillCirculation::getCustomerLayer, businessDto.getCustomerLayer())
                .eq(StrUtil.isNotBlank(businessDto.getSalerId()), CustomerWillCirculation::getSalerId, businessDto.getSalerId());
        if (Objects.equals(businessDto.getOrigin(), AssignCustSourceSpecialEnum.LOSS.getValue())) {
            //待流失客户查询天数
            DateTime beginDate = DateUtil.beginOfDay(new Date());
            DateTime endDate = DateUtil.offsetDay(beginDate, LeadDayConstants.WILL_LOSS_DAY_NUM);
            lambdaQueryChainWrapper.between(CustomerWillCirculation::getPreDate, beginDate, endDate);
        } else {
            lambdaQueryChainWrapper.eq(CustomerWillCirculation::getCreateDate, getEffectiveCreateDate());
        }
        lambdaQueryChainWrapper
                .eq(StrUtil.isNotBlank(businessDto.getCustId()), CustomerWillCirculation::getCustId, businessDto.getCustId())
                .like(StrUtil.isNotBlank(businessDto.getCustName()), CustomerWillCirculation::getCustName, businessDto.getCustName())
                .eq(CustomerWillCirculation::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        if (Objects.nonNull(businessDto.getOrderBy())) {
            if (1 == businessDto.getOrderBy()) {
                if (SortEnum.ASC.getValue().equalsIgnoreCase(businessDto.getOrderByType())) {
                    lambdaQueryChainWrapper.orderByAsc(CustomerWillCirculation::getPreDate);
                } else {
                    lambdaQueryChainWrapper.orderByDesc(CustomerWillCirculation::getPreDate);
                }
            }
        } else {
            lambdaQueryChainWrapper.orderByAsc(CustomerWillCirculation::getPreDate);
        }
        Page<CustomerWillCirculation> dbResultPage = lambdaQueryChainWrapper.page(page);
        Page<CirculationLossPageBusinessView> businessViewPage = BeanUtil.copyProperties(dbResultPage, Page.class);
        if (businessViewPage.getTotal() < 1) {
            return businessViewPage;
        }
        businessViewPage.setRecords(CglibUtil.copyList(dbResultPage.getRecords(), CirculationLossPageBusinessView::new));
        return businessViewPage;
    }

    /**
     * Description: 获取待流转列表过滤条件的 创建日期 参数。
     *              CustomerKeepCirculationConsumer 每天会全量insert待流转客户，因此页面获取待流转列表的数据时，需要从每天新insert的数据中过滤。
     *              但是，每天insert待流转客户任务执行之前，页面查询不到数据，因此，采用兜底方案，暂时查询昨天的数据。
     * @author: JiuDD
     * @return java.util.Date
     * date: 2024/9/7 18:42
     */
    public Date getEffectiveCreateDate() {
        CustomerWillCirculation one = customerWillCirculationService.lambdaQuery()
                .eq(CustomerWillCirculation::getOrigin, AssignCustSourceSpecialEnum.CIRCULATION.getValue())
                .orderByDesc(CustomerWillCirculation::getId)
                .last("LIMIT 1")
                .one();
        if (Objects.isNull(one)) {
            return DateUtil.beginOfDay(DateUtil.yesterday());
        } else {
            return one.getCreateDate();
        }
    }

    /**
     * Description: 逻辑删除待流转、流失客户
     * @author: JiuDD
     * @param businessDto 待流转、流失 更新参数
     * @return boolean 删除成功返回true，否则返回false
     * date: 2024/7/22 15:11
     */
    public boolean deleteCirculationLoss(CirculationLossUpdateBusinessDto businessDto) {
        if (StrUtil.isBlank(businessDto.getCustId())) {
            return false;
        }
        return customerWillCirculationService.lambdaUpdate()
                .eq(CustomerWillCirculation::getCustId, businessDto.getCustId())
                .eq(Objects.nonNull(businessDto.getOrigin()), CustomerWillCirculation::getOrigin, businessDto.getOrigin())
                .set(CustomerWillCirculation::getDeleteFlag, DeleteFlagEnum.DELETE.getCode())
                .set(CustomerWillCirculation::getUpdateTime, new Date())
                .update();
    }

    /**
     * 更新流失流转表
     *
     * @param businessDto
     * @return
     */
    public boolean updateCirculationLoss(CirculationLossUpdateBusinessDto businessDto) {
        if (StrUtil.isBlank(businessDto.getCustId())) {
            log.error("updateCirculationLoss方法请求，custId不存在！{}", JSONObject.toJSONString(businessDto));
            return false;
        }
        CustomerWillCirculation customerWillCirculation = customerWillCirculationService.lambdaQuery().eq(CustomerWillCirculation::getCustId, businessDto.getCustId())
                .eq(CustomerWillCirculation::getOrigin, AssignCustSourceSpecialEnum.LOSS.getValue()).one();
        if (Objects.isNull(customerWillCirculation)) {
            log.info("未找到customerWillCirculation流失流转记录！{}", JSONObject.toJSONString(businessDto));
            return false;
        }
        //更新理由
        if (StringUtils.isNotEmpty(businessDto.getReason())) {
            customerWillCirculation.setReason(businessDto.getReason());
        }

        if (Objects.nonNull(businessDto.getDeleteFlag())) {
            customerWillCirculation.setDeleteFlag(businessDto.getDeleteFlag());
        } else {
            if (Objects.isNull(customerWillCirculation.getDeleteFlag())) {
                //默认未删除
                customerWillCirculation.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
            }
        }

        if (Objects.nonNull(businessDto.getStatus())) {
            //如果在前180天内的，则更新为已分配
            if (businessDto.getStatus().equals(YesOrNoEnum.YES.getCode()) && customerWillCirculation.getPreDate() != null &&
                    customerWillCirculation.getPreDate().after(DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -LeadDayConstants.POOL_LOSS_DAY_NUM))
                     && customerWillCirculation.getPreDate().before(DateUtil.endOfDay(new Date()))) {
                log.info("更新流失流转表，更新为已分配！custId为：{}", customerWillCirculation.getCustId());
                customerWillCirculation.setStatus(YesOrNoEnum.YES.getCode());
            }
        } else {
            if (Objects.isNull(customerWillCirculation.getStatus())) {
                //默认未分配
                customerWillCirculation.setStatus(YesOrNoEnum.NO.getCode());
            }
        }
        customerWillCirculation.setUpdateTime(new Date());

        if (StringUtils.isNotEmpty(businessDto.getCustName())) {
            customerWillCirculation.setCustName(businessDto.getCustName());
        }

        if (StringUtils.isNotEmpty(businessDto.getAreaId()) && StringUtils.isNotEmpty(businessDto.getSubId()) && StringUtils.isNotEmpty(businessDto.getDeptId())) {
            List<String> orgIdList = Arrays.asList(businessDto.getAreaId(), businessDto.getSubId(), businessDto.getDeptId());
            Map<String, OrgDataThirdView> orgDataThirdViewMap = orgThirdService.getOrgData(orgIdList);

            customerWillCirculation.setAreaId(businessDto.getAreaId());
            customerWillCirculation.setAreaName(orgDataThirdViewMap.getOrDefault(businessDto.getAreaId(), new OrgDataThirdView()).getName());
            customerWillCirculation.setSubId(businessDto.getSubId());
            customerWillCirculation.setSubName(orgDataThirdViewMap.getOrDefault(businessDto.getSubId(), new OrgDataThirdView()).getName());
            customerWillCirculation.setDeptId(businessDto.getDeptId());
            customerWillCirculation.setDeptName(orgDataThirdViewMap.getOrDefault(businessDto.getDeptId(), new OrgDataThirdView()).getName());
        }

        if (StringUtils.isNotEmpty(businessDto.getSalerId())) {
            customerWillCirculation.setSalerId(businessDto.getSalerId());
            customerWillCirculation.setSalerName(employeeThirdService.getEmployeeData(businessDto.getSalerId()).orElse(new EmployeeDataThirdView()).getName());
        }

        log.info("开始更新customerWillCirculation表，数据为：{}", JSONObject.toJSONString(customerWillCirculation));
        return customerWillCirculationService.updateById(customerWillCirculation);
    }

    /**
     * 判断是否是流失客户
     *
     * @param custId 客户id
     * @return
     */
    public Optional<Boolean> isLossCust(String custId) {
        if (StringUtils.isEmpty(custId)) {
            log.error("[isLossCust]--custId is null");
            return Optional.empty();
        }
        LambdaQueryWrapper<CustomerWillCirculation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //客户id
        lambdaQueryWrapper.eq(CustomerWillCirculation::getCustId, custId);
        //未删除状态
        lambdaQueryWrapper.eq(CustomerWillCirculation::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        //流失客户
        lambdaQueryWrapper.eq(CustomerWillCirculation::getOrigin, AssignCustSourceSpecialEnum.LOSS.getValue());
        //已分配
        lambdaQueryWrapper.eq(CustomerWillCirculation::getStatus, YesOrNoEnum.YES.getCode());
        if (customerWillCirculationService.count(lambdaQueryWrapper) > 0) {
            return Optional.of(true);
        }
        return Optional.of(false);
    }

    /**
     * 检查将要流失客户 提前发送微信消息
     */
    public void willCirculationLossSendMsg(AssignCustSourceSpecialEnum sourceEnum) {
        Integer leadDayConstants;
        String smsTemplate;
        if (AssignCustSourceSpecialEnum.LOSS.equals(sourceEnum)) {
            leadDayConstants = LeadDayConstants.PROTECT_LOSS_DAY_NUM;
            smsTemplate = SmsTemplateConstants.SCRM_CUSTOMER_LOSS;
        } else if (AssignCustSourceSpecialEnum.CIRCULATION.equals(sourceEnum)) {
            leadDayConstants = LeadDayConstants.CIRCULATION_DAY_NUM;
            smsTemplate = SmsTemplateConstants.SCRM_CUSTOMER_CIRCULATION;
        } else {
            log.warn("willLossSendMsg方法参数sourceEnum错误！{}", sourceEnum);
            return;
        }
        //查找微信模版
        Optional<SmaDictionaryItemView> smsTemplateByCode = smaDictionaryItemBusiness.findByCode(smsTemplate);
        if (!smsTemplateByCode.isPresent()) {
            log.error("未找到对应模版：{}", smsTemplate);
            return;
        }
        SmaDictionaryItemView smaDictionaryItemView = smsTemplateByCode.get();

        //查询流失流转表 提前发送企微消息
        DateTime earlyBegin = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), leadDayConstants);
        DateTime earlyEnd = DateUtil.endOfDay(earlyBegin);
        List<CustomerWillCirculation> customerWillCirculationList = customerWillCirculationService.lambdaQuery()
                .eq(CustomerWillCirculation::getOrigin, sourceEnum.getValue())
                .eq(Objects.equals(AssignCustSourceSpecialEnum.CIRCULATION, sourceEnum), CustomerWillCirculation::getCreateDate, DateUtil.format(DateUtil.date(), "yyyy-MM-dd"))
                .between(CustomerWillCirculation::getPreDate, earlyBegin, earlyEnd)
                .eq(CustomerWillCirculation::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
        //按商务、经理、总监进行分组，分别给商务发送消息 过滤和分组
        Map<String, List<CustomerWillCirculation>> groupedBySubId = customerWillCirculationList.stream()
                .filter(c -> c.getSubId() != null && !c.getSubId().isEmpty())
                .collect(Collectors.groupingBy(CustomerWillCirculation::getSubId));

        Map<String, List<CustomerWillCirculation>> groupedByDeptId = customerWillCirculationList.stream()
                .filter(c -> c.getDeptId() != null && !c.getDeptId().isEmpty())
                .collect(Collectors.groupingBy(CustomerWillCirculation::getDeptId));

        Map<String, List<CustomerWillCirculation>> groupedBySalerId = customerWillCirculationList.stream()
                .filter(c -> c.getSalerId() != null && !c.getSalerId().isEmpty())
                .collect(Collectors.groupingBy(CustomerWillCirculation::getSalerId));
        // 发送企微消息
        sendWillCirculationMsg(EmpPositionConstant.BUSINESS_MAJOR, sourceEnum, groupedBySubId, smaDictionaryItemView, leadDayConstants);
        sendWillCirculationMsg(EmpPositionConstant.BUSINESS_MANAGER, sourceEnum, groupedByDeptId, smaDictionaryItemView, leadDayConstants);
        sendWillCirculationMsg(EmpPositionConstant.BUSINESS_SALER, sourceEnum, groupedBySalerId, smaDictionaryItemView, leadDayConstants);
    }

    private void sendWillCirculationMsg(String position, AssignCustSourceSpecialEnum sourceEnum, Map<String, List<CustomerWillCirculation>> willCirculationList, SmaDictionaryItemView smaDictionaryItemView, Integer leadDayConstants) {
        if (MapUtils.isEmpty(willCirculationList)) {
            return;
        }
        //分别给每个总监发送微信消息
        for (Map.Entry<String, List<CustomerWillCirculation>> entry : willCirculationList.entrySet()) {
            String id = entry.getKey();
            List<CustomerWillCirculation> recordList = entry.getValue();
            if (CollectionUtils.isEmpty(recordList)) {
                continue;
            }
            // 每50个客户发送一条企微消息
            List<List<CustomerWillCirculation>> split = CollUtil.split(recordList, SmsTemplateConstants.CUST_NUM_PER_MSG);
            for (List<CustomerWillCirculation> list : split) {
                log.info("willLossSendMsg准备发送企微消息，sourceEnum={},  id={}, position={}", sourceEnum.getLable(), id, position);
                List<String> listCustName = new ArrayList<>();
                list.forEach(customerWillCirculation ->
                        listCustName.add(customerWillCirculation.getCustName()));
                String format;
                //发送微信消息
                if (AssignCustSourceSpecialEnum.LOSS.equals(sourceEnum)) {
                    format = MessageFormat.format(smaDictionaryItemView.getName(), leadDayConstants, Joiner.on("\n").join(listCustName));
                } else if (AssignCustSourceSpecialEnum.CIRCULATION.equals(sourceEnum)) {
                    format = MessageFormat.format(smaDictionaryItemView.getName(), Joiner.on("\n").join(listCustName));
                } else {
                    format = "";
                }
                if (format.isEmpty()){
                    log.info("willLossSendMsg 发送微信消息为空，直接处理下一条");
                    return;
                }
                log.info("willLossSendMsg 发送微信消息为：{}，sourceEnum={}", format, sourceEnum.getLable());
                //分别给商务、经理、总监发送微信消息
                try {
                    if (EmpPositionConstant.BUSINESS_MAJOR.equals(position) || EmpPositionConstant.BUSINESS_MANAGER.equals(position)) {
                        employeeThirdService.getOrgLeader(id).ifPresent(employeeLiteThirdView -> sendWxMessage.sendMessage(employeeLiteThirdView.getId(), format));
                    } else if (EmpPositionConstant.BUSINESS_SALER.equals(position)) {
                        sendWxMessage.sendMessage(id, format);
                    }
                } catch (Exception e) {
                    log.error("willLossSendMsg 发送微信消息失败！id={}, position={}, error={}", id, position, e.getMessage());
                }
            }
        }
    }

    /**
     * 过期放到客户池
     */
    public void lossPool(Date beginDay, Date endDay) {
        //总时长180天,180天前没有签单则直接放入到客户池（未删除状态,放到客户池后修改为删除状态）
        if (endDay.after(DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -LeadDayConstants.POOL_LOSS_DAY_NUM))) {
            log.info("lossPool endDay必须在{}天之前未到过期时间，无需执行！",LeadDayConstants.POOL_LOSS_DAY_NUM);
            return;
        }
        log.info("开始执行，lossPool！beginDay={}, endDay={}", beginDay, endDay);
        List<CustomerWillCirculation> customerWillCirculationList = customerWillCirculationService.lambdaQuery()
                .eq(CustomerWillCirculation::getOrigin, AssignCustSourceSpecialEnum.LOSS.getValue())
                .between(CustomerWillCirculation::getPreDate, beginDay, endDay)
                .eq(CustomerWillCirculation::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .list();
        log.info("lossPool 查询到的数据个数为：{}", customerWillCirculationList.size());
        customerWillCirculationList.forEach(customerWillCirculation -> {
            circulationLossBusiness.lossPoolSave(customerWillCirculation);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void lossPoolSave(CustomerWillCirculation customerWillCirculation){
        log.info("lossPool准备释放的客户id为：{}", customerWillCirculation.getCustId());
        //将此客户放回到客户池
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, customerWillCirculation.getCustId()).one();
        // 保护关系表 不存在该客户，则不进行流转
        if (Objects.isNull(cmCustProtect)) {
            log.info("客户放回到客户池，cmCustProtect为空！{}", JSONObject.toJSONString(customerWillCirculation));
            return;
        }
        //已经在客户池中的不处理
        if (Objects.equals(cmCustProtect.getStatus(), ProtectStateEnum.CUSTOMER_POOL.getState())) {
            log.info("lossPool客户已经在客户池 无需再次更新！{}", JSONObject.toJSONString(customerWillCirculation));
            return;
        }
        //不流失表中存在则不流失
        if (checkSpecialSetting(customerWillCirculation.getCustId())) {
            log.info("lossPool客户不流失表中存在则不流失! {}", JSONObject.toJSONString(customerWillCirculation));
            return;
        }
        customerWillCirculation.setDeleteFlag(DeleteFlagEnum.DELETE.getCode());
        customerWillCirculation.setUpdateTime(new Date());
        customerWillCirculation.setReason("180天自动流失！");
        boolean customerWillCirculationUpdate = customerWillCirculationService.updateById(customerWillCirculation);
        if (!customerWillCirculationUpdate) {
            log.error("customerWillCirculationUpdate更新失败！{}", JSONObject.toJSONString(customerWillCirculation));
            throw new RuntimeException("customerWillCirculationUpdate更新失败！");
        }
        //客户信息
        Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(customerWillCirculation.getCustId());
        if (!customerData.isPresent()) {
            log.warn("lossPool-customer中未找到对应记录 客户信息不存在！ {}", JSONObject.toJSONString(customerWillCirculation));
            throw new RuntimeException("lossPool-customer中未找到对应记录 客户信息不存在！");
        }
        CustomerDataThirdView customerDataThirdView = customerData.get();
        CmCustProtect updateCmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, customerWillCirculation.getCustId()).one();
        updateCmCustProtect.setCustId(customerWillCirculation.getCustId());
        updateCmCustProtect.setAssignDate(null);
        updateCmCustProtect.setProtectTime(null);
        updateCmCustProtect.setExceedTime(null);
        updateCmCustProtect.setReason(ConvertRelationEnum.LOSS_KHC.getLable());
        updateCmCustProtect.setStatus(ProtectStateEnum.CUSTOMER_POOL.getState());
        updateCmCustProtect.setSource(CustProtectSourceEnum.LOSS.getValue().toString());
        updateCmCustProtect.setRegProvince(customerDataThirdView.getProvinceCode());
        updateCmCustProtect.setRegCity(customerDataThirdView.getCityCode());
        updateCmCustProtect.setRegRegion(customerDataThirdView.getDistrictCode());
        updateCmCustProtect.setCustType(ProtectCustTypeEnum.PROTECT_FOLLOW.getValue());
        //更新保护关系表
        Integer cmCustProtectUpdate = cmCustProtectService.updateNullableByCustId(updateCmCustProtect);
        if (cmCustProtectUpdate == null || Objects.equals(0,cmCustProtectUpdate)) {
            log.error("cmCustProtectUpdate更新失败！{}", JSONObject.toJSONString(customerWillCirculation));
            throw new RuntimeException("cmCustProtectUpdate更新失败！");
        }
        // 保存 流失记录表
        CustomerLossLogBusinessDto customerLoss = new CustomerLossLogBusinessDto();
        customerLoss.setCustId(cmCustProtect.getCustId());
        customerLoss.setReason("SCRM_LOSS_REASON_5");//产品到期系统自动流失, 来自: SMA_DICTIONARY_ITEM表, DICTIONARY_ID='CRM_LOSS_REASON'
        boolean customerLossSaveBoolean = customerLossLogBusiness.saveCustomerLossLog(customerLoss);
        if (!customerLossSaveBoolean){
            log.error("customerLoss保存失败！{}", JSONObject.toJSONString(customerLoss));
            throw new RuntimeException("customerLoss保存失败！");
        }
        //删除待流转状态
        circulationLossBusiness.deleteCirculationLoss(new CirculationLossUpdateBusinessDto().setCustId(customerWillCirculation.getCustId()).setOrigin(AssignCustSourceSpecialEnum.CIRCULATION.getValue()));

        //更新客户信息表
        // 保存流转记录
        ConvertLogBusinessDto logBusinessDto = new ConvertLogBusinessDto();
        logBusinessDto.setSalerId(cmCustProtect.getSalerId());
        logBusinessDto.setDeptOfSalerId(cmCustProtect.getBussdeptId());
        logBusinessDto.setSubcompanyOfSalerId(cmCustProtect.getSubcompanyId());
        logBusinessDto.setAreaOfSalerId(cmCustProtect.getAreaId());
        logBusinessDto.setAreaOfCurSalerId(cmCustProtect.getAreaId());
        logBusinessDto.setCustType(ProtectCustTypeEnum.PROTECT_FOLLOW.getValue());
        logBusinessDto.setCustId(cmCustProtect.getCustId());
        logBusinessDto.setCustName(cmCustProtect.getCustName());
        logBusinessDto.setSubcompanyOfCurSalerId(cmCustProtect.getSubcompanyId());
        logBusinessDto.setCreateBy(ScrmAppConstants.SYSTEM_ADMIN);
        logBusinessDto.setCreateTime(new Date());
        logBusinessDto.setConvertType(ConvertRelationEnum.LOSS_KHC.getValue());
        logBusinessDto.setReleaseReason(ConvertRelationEnum.LOSS_KHC.getLable());
        smaConvertLogThirdService.insertLog(logBusinessDto);
        // 同步客户阶段为流失
        CustomerUpdateDubboDto customerUpdateDubboDto = new CustomerUpdateDubboDto();
        customerUpdateDubboDto.setCustomerId(customerWillCirculation.getCustId());
        customerUpdateDubboDto.setPresentStage(CustomerPresentStageEnum.LOST_CUST.getCode());
        rocketMqOperate.syncSend(CESUPPORT_SCRM_CUSTOMER_PRESENT_STAGE_TOPIC, JSON.toJSONString(customerUpdateDubboDto));
        //搜客宝es的flag1从 6(保护中已成交) 改为 4(保护过)
        synFlag1From6to4(cmCustProtect);
        log.info("流失成交客户成功,客户ID={}", cmCustProtect.getCustId());
    }

    /**
     * 过去十天未进行分配的客户进入总监待分配
     * 只查当天的，可能会有失败的情况，固多查几天
     */
    public void generalDistribute(Date beginDay, Date endDay) {
        log.info("开始执行，generalDistribute！ beginDay={}, endDay={}", beginDay, endDay);
        List<CustomerWillCirculation> customerWillCirculationList = customerWillCirculationService.lambdaQuery()
                .eq(CustomerWillCirculation::getOrigin, AssignCustSourceSpecialEnum.LOSS.getValue())
                .between(CustomerWillCirculation::getPreDate, beginDay, endDay)
                .eq(CustomerWillCirculation::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(CustomerWillCirculation::getStatus, YesOrNoEnum.NO.getCode())
                .list();

        log.info("generalDistribute 查询到的数据个数为：{}", customerWillCirculationList.size());
        customerWillCirculationList.forEach(customerWillCirculation -> {
                circulationLossBusiness.generalDistributeSave(customerWillCirculation);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void generalDistributeSave(CustomerWillCirculation customerWillCirculation){
        log.info("generalDistribute 准备将客户放到总监待分配 客户id为：{}", customerWillCirculation.getCustId());
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, customerWillCirculation.getCustId()).one();
        if (Objects.isNull(cmCustProtect)) {
            log.info("generalDistribute 根据custId未找到保护关系！{}", JSONObject.toJSONString(customerWillCirculation));
            return;
        }

        //此客户当前不在总监待分、客户池 将此客户放到总监待分配 更新保护关系表
        if (!ProtectStateEnum.MAJOR_WILL_ASSIGN.getState().equals(cmCustProtect.getStatus())
                && !ProtectStateEnum.CUSTOMER_POOL.getState().equals(cmCustProtect.getStatus())) {
            log.info("generalDistribute 开始更新！CustId:{}", customerWillCirculation.getCustId());
            //客户信息
            Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(customerWillCirculation.getCustId());
            if (!customerData.isPresent()) {
                log.warn("[generalDistribute] customer中未找到对应记录 客户信息不存在！ {}", JSONObject.toJSONString(customerWillCirculation));
                return;
            }
            CustomerDataThirdView customerDataThirdView = customerData.get();
            CmCustProtect updateCmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, customerWillCirculation.getCustId()).one();
            updateCmCustProtect.setAssignDate(null);
            updateCmCustProtect.setProtectTime(null);
            updateCmCustProtect.setExceedTime(null);
            updateCmCustProtect.setReason(ConvertRelationEnum.LOSS_ZJDFP.getLable());
            updateCmCustProtect.setRegProvince(customerDataThirdView.getProvinceCode());
            updateCmCustProtect.setRegCity(customerDataThirdView.getCityCode());
            updateCmCustProtect.setRegRegion(customerDataThirdView.getDistrictCode());
            updateCmCustProtect.setStatus(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState());
            updateCmCustProtect.setAssignCustSource(AssignCustSourceSpecialEnum.LOSS.getValue());
            Integer cmCustProtectUpdate = cmCustProtectService.updateNullableByCustId(updateCmCustProtect);
            if (cmCustProtectUpdate == null || Objects.equals(0,cmCustProtectUpdate)) {
                log.error("[generalDistribute] cmCustProtectUpdate更新失败！{}", JSONObject.toJSONString(customerWillCirculation));
                throw new RuntimeException("[generalDistribute] cmCustProtectUpdate更新失败！");
            }

            boolean customerWillCirculationUpdate = customerWillCirculationService.lambdaUpdate().eq(CustomerWillCirculation::getId, customerWillCirculation.getId())
                    .set(CustomerWillCirculation::getUpdateTime, new Date())
                    .set(CustomerWillCirculation::getReason, ConvertRelationEnum.LOSS_ZJDFP.getValue())
                    .update();
            if (!customerWillCirculationUpdate){
                log.error("[generalDistribute] customerWillCirculationUpdate更新失败！{}", JSONObject.toJSONString(customerWillCirculation));
                throw new RuntimeException("[generalDistribute] customerWillCirculationUpdate更新失败！");
            }

            // 保存流转记录
            ConvertLogBusinessDto logBusinessDto = new ConvertLogBusinessDto();
            logBusinessDto.setSalerId(cmCustProtect.getSalerId());
            logBusinessDto.setDeptOfSalerId(cmCustProtect.getBussdeptId());
            logBusinessDto.setSubcompanyOfSalerId(cmCustProtect.getSubcompanyId());
            logBusinessDto.setAreaOfSalerId(cmCustProtect.getAreaId());
            logBusinessDto.setAreaOfCurSalerId(cmCustProtect.getAreaId());
            logBusinessDto.setCustType(cmCustProtect.getCustType());
            logBusinessDto.setCustId(cmCustProtect.getCustId());
            logBusinessDto.setCustName(cmCustProtect.getCustName());
            logBusinessDto.setSubcompanyOfCurSalerId(cmCustProtect.getSubcompanyId());
            logBusinessDto.setCreateBy(ScrmAppConstants.SYSTEM_ADMIN);
            logBusinessDto.setCreateTime(new Date());
            logBusinessDto.setConvertType(ConvertRelationEnum.LOSS_ZJDFP.getValue());
            logBusinessDto.setReleaseReason(ConvertRelationEnum.LOSS_ZJDFP.getLable());
            smaConvertLogThirdService.insertLog(logBusinessDto);
            log.info("generalDistribute 到总监待分配成功,客户ID={}", cmCustProtect.getCustId());
        } else{
            log.info("generalDistribute 已经在总监待分或客户池！客户ID={}", cmCustProtect.getCustId());
        }
    }

    /**
     * Description: 不流失流转控制表中存在该客户，则不流失
     * @author: lyc
     * @param custId 客户id
     * @return boolean true存在，false不存在
     * date: 2024/7/25 16:18
     */
    private boolean checkSpecialSetting(String custId) {
        List<CustomerCirculationSpecialSetting> specialSettingList = customerCirculationSpecialSettingService.lambdaQuery()
                .eq(CustomerCirculationSpecialSetting::getNotCirculationType, 2)
                .eq(CustomerCirculationSpecialSetting::getCustId, custId)
                .eq(CustomerCirculationSpecialSetting::getDeleteFlag, 0)
                .list();
        if (!CollectionUtils.isEmpty(specialSettingList)) {
            log.warn("当前客户在不流转不流失控制表中存在，不进行流失，custId:{}", custId);
            return true;
        }
        return false;
    }

    /**
     * Description: 根据待流转客户表的id检查待流转客户是否可被流转
     * 1.是否是商务保护中 并且 是成交客户
     * 2.是否在不流转客户白名单中
     * 3.是否在考核期
     * 4.是否在绝对保护期
     * 5.分司的流转配置是否合理
     * @author: JiuDD
     * @param customerWillCirculation 待流转信息
     * @return boolean true可流转，false不可流转
     * date: 2024/9/3 19:07
     */
    public boolean checkCustomerProtect(CustomerWillCirculation customerWillCirculation) {
        if (Objects.equals(customerWillCirculation.getAbsoluteProtectFlag(), 1)) {
            log.warn("当前客户绝对保护标记为1，不进行流转，custId:{}", customerWillCirculation.getCustId());
            return false;
        }
        String custId = customerWillCirculation.getCustId();
        if (StringUtils.isEmpty(custId)) {
            log.warn("checkWillCirculationData客户id为空！customerWillCirculation={}", JSONObject.toJSONString(customerWillCirculation));
            return false;
        }
        // 从保护关系查询是否是成交客户
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, custId).one();
        if (Objects.isNull(cmCustProtect)) {
            log.warn("checkWillCirculationData根据custId未找到保护关系！custId={}", custId);
            return false;
        }
        // 当前客户为总监待分、经理待分，则不进行流转，不入待流转表
        Integer status = cmCustProtect.getStatus();
        if (Objects.equals(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState(), status) || Objects.equals(ProtectStateEnum.MANAGER_WILL_ASSIGN.getState(), status) || Objects.equals(ProtectStateEnum.BU_WILL_ASSIGN.getState(), status)) {
            String position = "";
            if (Objects.equals(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState(), status)) {
                position = "总监";
            } else if (Objects.equals(ProtectStateEnum.MANAGER_WILL_ASSIGN.getState(), status)) {
                position = "经理";
            } else if (Objects.equals(ProtectStateEnum.BU_WILL_ASSIGN.getState(), status)) {
                position = "事业部总监";
            }
            log.warn("当前客户为{}待分，不进行流转，custId:{}", position, custId);
            return false;
        }
        if (Objects.equals(ProtectStateEnum.CUSTOMER_POOL.getState(), status)) {
            log.warn("当前客户在客户池，不进行流转， custId:{}", custId);
            return false;
        }
        if (Objects.nonNull(cmCustProtect.getAbsoluteProtectTime()) && cmCustProtect.getAbsoluteProtectTime().after(DateUtil.date())) {
            log.warn("当前客户绝对保护期未结束，则不进行流转，custId:{}, absoluteProtectTime:{}", custId, cmCustProtect.getAbsoluteProtectTime());
            return false;
        }
        if (!Objects.equals(cmCustProtect.getCustType(), ProtectCustTypeEnum.ORDERED.getValue()) && !Objects.equals(cmCustProtect.getCustType(), ProtectCustTypeEnum.SECOND_DEVELOPMENT.getValue())) {
            log.warn("checkWillCirculationData该客户不属于成交客户！custId={}", custId);
            return false;
        }
        String subId = cmCustProtect.getSubcompanyId();
        if (StringUtils.isEmpty(subId)) {
            log.warn("checkWillCirculationData分司id为空！cmCustProtect={}", JSONObject.toJSONString(cmCustProtect));
            return false;
        }
        CustomerCirculationSetting circulationSetting = customerCirculationSettingService.getOne(new LambdaQueryWrapper<CustomerCirculationSetting>().eq(CustomerCirculationSetting::getSubId, subId));
        if (Objects.isNull(circulationSetting)) {
            log.warn("checkWillCirculationData根据subId未找到保有流失配置！subId={}", subId);
            return false;
        }
        Integer monthWithoutClock = circulationSetting.getMonthWithoutClock();
        if (Objects.isNull(monthWithoutClock)) {
            log.warn("checkWillCirculationData考核周期（月份）配置为空！circulationSetting={}", JSON.toJSONString(circulationSetting));
            return false;
        }
        Integer circulationType = circulationSetting.getCirculationType();
        if (Objects.isNull(circulationType) || (circulationType != 1 && circulationType != 2)) {
            log.warn("checkWillCirculationData流失类型配置不合法，可选值：1、2.  circulationSetting={}", JSON.toJSONString(circulationSetting));
            return false;
        }
        // 当前客户是否在考核期
        DateUtils.CirculationLimitDateResult visitLimitDate = DateUtils.getVisitCirculationLimitDate(cmCustProtect.getFirstSignTime(), monthWithoutClock);
        log.info("客户[{} {}]流转的初始考核日期:{}, 考核周期{}个月, 当前考核周期的起止日期:{}~{}", custId, cmCustProtect.getCustName(), visitLimitDate.getStartLimitDay(), monthWithoutClock, visitLimitDate.getPreviousLimitDate(), visitLimitDate.getNextLimitDate());
        if (!DateUtils.isInAppraisalPeriod(visitLimitDate)) {
            log.warn("当前客户不在考核期，不进行流转，custId:{}", custId);
            return false;
        }
        return true;
    }


    /***
     * 不流失白名单校验
     * @param customerId
     * <AUTHOR>
     * @date 2024/9/4 20:04
     * @version 1.0.0
     * @return java.lang.Boolean
    **/
    public Boolean checkSpecialSettingWhiteList(String customerId){
        return customerCirculationSettingBusiness.checkSpecialSetting(customerId);
    }

    /**
     * Description: 流转客户到总监待分配------
     * @author: JiuDD
     * @param customerId 保护关系
     * @return void
     * date: 2024/9/4 19:00
     */

    @Transactional
    public void circulationCust(String customerId) {
        CmCustProtect cmCustProtect = protectBusiness.getCmCustProtect(customerId);
        if (Objects.isNull(cmCustProtect)){
            log.error("流转的时候保护关系查询为空，customerId={}",customerId);
        }
        // 1 [真正的流转动作] 更新保护关系表
        circulation(cmCustProtect);
        // 2 流转完成之后，将 待流转客户表 delete flag 置为 1
        circulationLossBusiness.deleteCirculationLoss(new CirculationLossUpdateBusinessDto().setCustId(cmCustProtect.getCustId()).setOrigin(AssignCustSourceSpecialEnum.CIRCULATION.getValue()));
        // 3 添加流转记录
        insertSmaConvertLog(cmCustProtect);
    }

    /**
     * Description: [真正的流转动作] 更新保护关系表
     * @author: JiuDD
     * @param cmCustProtect 保护关系
     * @return void
     * date: 2024/7/25 17:06
     */
    public void circulation(CmCustProtect cmCustProtect) {
        // 目前 只流转到 总监待分配，将来根据需求再扩展到经理待分配
        Integer circulationType = 1;
        CmCustProtect updateCmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, cmCustProtect.getCustId()).one();
        updateCmCustProtect.setId(cmCustProtect.getId());
        updateCmCustProtect.setCustId(cmCustProtect.getCustId());
        updateCmCustProtect.setAssignCustSource(AssignCustSourceSpecialEnum.CIRCULATION.getValue());
        updateCmCustProtect.setStatus((1 == circulationType) ? ProtectStateEnum.MAJOR_WILL_ASSIGN.getState() : ProtectStateEnum.MANAGER_WILL_ASSIGN.getState());
        updateCmCustProtect.setUpdateTime(new Date());
        updateCmCustProtect.setUpdateBy(ScrmAppConstants.SYSTEM_ADMIN);
        cmCustProtectService.updateByCustId(updateCmCustProtect);
    }

    /**
     * Description: insert流转记录
     * @author: JiuDD
     * @param cmCustProtect 保护关系
     * @return void
     * date: 2024/7/25 17:27
     */
    public void insertSmaConvertLog(CmCustProtect cmCustProtect) {
        ConvertLogBusinessDto logBusinessDto = new ConvertLogBusinessDto();
        logBusinessDto.setSalerId(cmCustProtect.getSalerId());
        logBusinessDto.setDeptOfSalerId(cmCustProtect.getBussdeptId());
        logBusinessDto.setBuOfSalerId(cmCustProtect.getBuId());
        logBusinessDto.setSubcompanyOfSalerId(cmCustProtect.getSubcompanyId());
        logBusinessDto.setAreaOfSalerId(cmCustProtect.getAreaId());
        logBusinessDto.setAreaOfCurSalerId(cmCustProtect.getAreaId());
        logBusinessDto.setCustType(cmCustProtect.getCustType());
        logBusinessDto.setCustId(cmCustProtect.getCustId());
        logBusinessDto.setCustName(cmCustProtect.getCustName());
        logBusinessDto.setSubcompanyOfCurSalerId(cmCustProtect.getSubcompanyId());
        logBusinessDto.setDeptOfCurSalerId(null);
        logBusinessDto.setBuOfCurSalerId(null);
        logBusinessDto.setCreateBy(ScrmAppConstants.SYSTEM_ADMIN);
        logBusinessDto.setCreateTime(new Date());
        logBusinessDto.setConvertType(ConvertRelationEnum.CIRCULATION.getValue());
        logBusinessDto.setReleaseReason(ConvertRelationEnum.CIRCULATION.getLable());
        smaConvertLogThirdService.insertLog(logBusinessDto);
    }

    /**
     * Description: 实际打卡次数
     * @author: JiuDD
     * @param cmCustProtect 保护关系
     * @param visitLimitDate  打卡时限
     * @return java.lang.Long 实际打卡次数
     * date: 2024/7/25 16:10
     */
    public Long getCustSiteClockCount(CmCustProtect cmCustProtect, DateUtils.CirculationLimitDateResult visitLimitDate) {
        EmpCustSiteClockBusinessDto req = new EmpCustSiteClockBusinessDto()
                .setCustId(cmCustProtect.getCustId())
                //.setEmpId(cmCustProtect.getSalerId())//非商务本人打卡客户，也算作有效打卡，同样不流转(张国玲邮件通知的)
                .setCreateDateStart(DateUtil.formatDateTime(visitLimitDate.getPreviousLimitDate()))
                .setCreateDateEnd(DateUtil.formatDateTime(visitLimitDate.getNextLimitDate()));
        return empCustSiteClockThirdService.getCountByParam(req);
    }

    /**
     * Description: 获取考核周期内的打卡次数
     * @author: JiuDD
     * @param circulation  待流转信息
     * @return java.lang.Long 考核周期内的打卡次数
     * date: 2024/9/5 10:44
     */
    public Long getCustSiteClockCount(CustomerWillCirculation circulation) {
        String subId = circulation.getSubId();
        /* 2.打卡指标考核：考核周期内打卡次数>0，则不流转，否则进行流转 */
        CustomerCirculationSetting circulationSetting = customerCirculationSettingService.getOne(new LambdaQueryWrapper<CustomerCirculationSetting>().eq(CustomerCirculationSetting::getSubId, subId));
        Integer monthWithoutClock = circulationSetting.getMonthWithoutClock();
        CmCustProtect cmCustProtect = protectBusiness.getCmCustProtect(circulation.getCustId());
        DateUtils.CirculationLimitDateResult visitLimitDate = DateUtils.getVisitCirculationLimitDate(cmCustProtect.getFirstSignTime(), monthWithoutClock);
        log.info("客户[{} {}]流转的初始考核日期:{}, 考核周期{}个月, 当前考核周期的起止日期:{}~{}", circulation.getCustId(), cmCustProtect.getCustName(), visitLimitDate.getStartLimitDay(), monthWithoutClock, visitLimitDate.getPreviousLimitDate(), visitLimitDate.getNextLimitDate());
        Long custSiteClockCount = getCustSiteClockCount(cmCustProtect, visitLimitDate);
        log.info("当前客户在考核周期内 打卡次数=0，需进行流转，custId:{}, custSiteClockCount:{}, 考核周期: {}~{}", circulation.getCustId(), custSiteClockCount, visitLimitDate.getPreviousLimitDate(), visitLimitDate.getNextLimitDate());
        return custSiteClockCount;
    }

    /**
     * Description: 根据创建日期物理删除待流转客户。
     *              注意：该方法删除的待流转客户包含创建日期为给定参数“当天”insert的待流转客户，及“之前”insert的待流转客户，调用该方法前，请确认该方法是否适用
     * @author: JiuDD
     * @param createDay  创建日期
     * @return boolean 是否删除成功，true：成功，false：失败
     * date: 2024/9/7 16:33
     */
    public boolean deleteByOriginAndCreateDate(Date createDay) {
        QueryWrapper<CustomerWillCirculation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("origin", AssignCustSourceSpecialEnum.CIRCULATION.getValue());
        queryWrapper.le("create_date", createDay);
        boolean remove = customerWillCirculationService.remove(queryWrapper);
        return remove;
    }

    /**
     * Description: 根据创建日期集合逐日物理删除待流转客户
     *              注意：
     *                  1.该方法不含事务（仅1天的删除量就为30W+，如果使用事务，会导致数据库性能显著下降。且删除失败，并不影响正常业务），调用该方法前，请确认该方法是否适用
     *                  2.建议日期数量尽量少
     * @author: JiuDD
     * @param createDateList 创建日期集合，可指定多个，以英文逗号分割，格式为yyyy-MM-dd的字符串，例如：2024-09-01,2024-09-02,2024-09-03
     * @return void
     * date: 2024/9/7 16:37
     */
    public void deleteByOriginAndCreateDateList(List<String> createDateList) {
        if (CollectionUtils.isEmpty(createDateList)) {
            log.warn("待流转客户物理删除失败，参数createDateList为空");
            return;
        }
        for (String createDate : createDateList) {
            log.info("开始物理删除 [create_date={}] insert的待流转客户", createDate);
            QueryWrapper<CustomerWillCirculation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("origin", AssignCustSourceSpecialEnum.CIRCULATION.getValue());
            queryWrapper.eq("create_date", createDate);
            boolean remove = customerWillCirculationService.remove(queryWrapper);
            if (remove) {
                log.info("待流转客户删除成功：{}", createDate);
            } else {
                log.error("待流转客户删除失败：{}", createDate);
            }
        }
    }


    /**
     * Description: 从待流转表获取有流转客户的分公司
     * @author: JiuDD
     * @return java.util.List<com.ce.scrm.entity.OrgDataThirdView>
     * date: 2024/9/22 18:42
     */
    public List<OrgDataThirdView> getHasCirculationSubList() {
        List<CustomerWillCirculation> circulationList = customerWillCirculationService.lambdaQuery()
                .select(CustomerWillCirculation::getSubId)
                .eq(CustomerWillCirculation::getOrigin, AssignCustSourceSpecialEnum.CIRCULATION.getValue())
                .eq(CustomerWillCirculation::getCreateDate, DateUtil.format(DateUtil.date(), "yyyy-MM-dd"))
                .eq(CustomerWillCirculation::getDeleteFlag, 0)
                .eq(CustomerWillCirculation::getAbsoluteProtectFlag, 0)
                .list();
        if (CollectionUtil.isEmpty(circulationList)) {
            return Lists.newArrayList();
        }
        List<String> subIdList = circulationList.stream().map(CustomerWillCirculation::getSubId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(subIdList)) {
            return Lists.newArrayList();
        }
        Map<String, OrgDataThirdView> orgData = orgThirdService.getOrgData(subIdList);
        if (CollectionUtil.isEmpty(orgData)) {
            return Lists.newArrayList();
        }
        return orgData.values().stream().filter(org -> Objects.nonNull(org.getId())).collect(Collectors.toList());
    }


    /**
     * Description: 检查 CustomerKeepCirculationConsumer 插入到待流转表的流转日期是否正确, 如果不正确，则删除该id
     * @author: JiuDD
     * @return 正确返回true, 错误返回false
     * date: 2024/09/22 9:27
     */
    public List<String> checkCirculationDateAndDeleteInvalidId(List<String> idList) {
        try {
            if (CollectionUtil.isEmpty(idList)) {
                log.info("待流转流失表的id为空.");
                return Lists.newArrayList();
            }
            Iterator<String> iterator = idList.iterator();
            while (iterator.hasNext()) {
                String id = iterator.next();
                CustomerWillCirculation circulation = customerWillCirculationService.getById(id);
                if (Objects.isNull(circulation)) {
                    log.error("待流转流失表的id[{}]不存在,请检查.", id);
                    iterator.remove();
                    continue;
                }
                if (!Objects.equals(circulation.getOrigin(), AssignCustSourceSpecialEnum.CIRCULATION.getValue())) {
                    log.error("待流转流失表的id[{}]的来源不是流转客户,请检查.", id);
                    iterator.remove();
                    continue;
                }
                if (Objects.equals(circulation.getAbsoluteProtectFlag(), 1)) {
                    log.error("待流转流失表id=[{}]的客户[{}-{}]绝对保护标记为1,不流转,请检查.", id, circulation.getCustId(), circulation.getCustName());
                    iterator.remove();
                    continue;
                }
                Integer monthWithoutClock = getMonthWithoutClock(circulation.getSubId());
                if (monthWithoutClock == null) {
                    log.error("待流转流失表的id[{}]的分公司[{}]没有设置考核周期,请检查.", id, circulation.getSubId());
                    iterator.remove();
                    continue;
                }
                CmCustProtect cmCustProtect = cmCustProtectService.getOne(new LambdaQueryWrapper<CmCustProtect>().eq(CmCustProtect::getCustId, circulation.getCustId()));
                if (Objects.isNull(cmCustProtect)) {
                    log.error("待流转流失表的id[{}]的客户[{}-{}]没有保护关系,请检查.", id, circulation.getCustId(), circulation.getCustName());
                    iterator.remove();
                    continue;
                }
                if (Objects.nonNull(cmCustProtect.getAbsoluteProtectTime()) && cmCustProtect.getAbsoluteProtectTime().after(DateUtil.date())) {
                    log.error("待流转流失表的id[{}]的客户[{}-{}]绝对保护期[{}]未结束,请检查.", id, circulation.getCustId(), circulation.getCustName(), DateUtil.formatDateTime(cmCustProtect.getAbsoluteProtectTime()));
                    iterator.remove();
                    continue;
                }
                DateUtils.CirculationLimitDateResult circulationLimitDate = DateUtils.getVisitCirculationLimitDate(cmCustProtect.getFirstSignTime(), monthWithoutClock);
                if (!DateUtil.isSameDay(circulation.getPreDate(), circulationLimitDate.getNextLimitDate())) {
                    log.error("待流转流失表的id[{}]的客户[{}-{}]的预计流转日期[{}]与实时计算的考核周期[{}]不匹配,请检查.", id, circulation.getCustId(), circulation.getCustName(), DateUtil.format(circulation.getPreDate(), "yyyy-MM-dd"), DateUtil.format(circulationLimitDate.getNextLimitDate(), "yyyy-MM-dd"));
                    iterator.remove();
                }
            }
            return idList;
        } catch (Exception e) {
            log.error("检查待流转客户[指定idList]的流转日期是否正确失败.", e);
            return Lists.newArrayList();
        }
    }

    /**
     * Description: 获取分公司的考核周期
     * @author: JiuDD
     * @param subId 分司id
     * @return java.lang.Integer
     * date: 2024/10/1 12:38
     */
    public Integer getMonthWithoutClock(String subId) {
        CustomerCirculationSetting circulationSetting = customerCirculationSettingService.getOne(new LambdaQueryWrapper<CustomerCirculationSetting>().eq(CustomerCirculationSetting::getSubId, subId));
        if (Objects.isNull(circulationSetting)) {
            log.warn("分公司没有设置考核规则,请先设置考核规则后再执行该job. 分公司ID：{}", subId);
            return null;
        }
        Integer monthWithoutClock = circulationSetting.getMonthWithoutClock();
        if (Objects.isNull(monthWithoutClock)) {
            log.warn("分公司没有设置考核周期,请先设置考核周期后再执行该job. 分公司设置的考核规则：{}", JSON.toJSONString(circulationSetting));
            return null;
        }
        return monthWithoutClock;
    }


    /**
     * Description: 检查上个月及以前(但是在2024-08-02上线之后)的新成交客户的流转日期是否正确(以自然月为维度来进行检查)
     * @author: JiuDD
     * @param monthWithoutClock 考核周期
     * @param org  分司
     * @return boolean  正确返回true, 错误返回false
     * date: 2024/9/22 11:26
     */
    public boolean checkPreviousMonthNewDealCustomerCirculationDateIsOk(Integer monthWithoutClock, OrgDataThirdView org) {
        String subId = org.getId();
        String subName = org.getName();
        DateTime startTime = DateUtil.beginOfMonth(DateUtil.date());
        for (int i = 0; i <= monthWithoutClock; i++) {
            // 考核期内某自然月的开始时间, 结束时间
            startTime = DateUtil.offsetMonth(startTime, -1);
            String startTimeStr = DateUtil.format(startTime, "yyyy-MM");
            Date endTime = DateUtil.offsetMonth(startTime, 1);
            if (startTime.isBefore(DateUtils.START_LIMIT_DAY)) {
                log.info("分公司[{}-{}]的{}月新成交客户的预计流转日期检查: 已早于系统考核起始日期[2024-08-01], 退出.", subName, subId, startTimeStr);
                break;
            }

            // 实时计算出新成交客户的流转日期
            DateUtils.CirculationLimitDateResult visitLimitDate = DateUtils.getVisitCirculationLimitDate(startTime, monthWithoutClock);
            log.info("分公司[{}-{}]的{}月新成交客户的预计流转日期检查: 考核日期为[{}]", subName, subId, startTimeStr, visitLimitDate);
            String nextLimitDateStr = DateUtil.format(visitLimitDate.getNextLimitDate(), DatePattern.NORM_DATE_PATTERN);
            // 待流转表实际 insert 的新成交客户的流转日期
            List<Date> preDateListNewCust = customerWillCirculationService.getPreDate(AssignCustSourceSpecialEnum.CIRCULATION.getValue(), DateUtil.beginOfDay(DateUtil.date()), subId, startTime, endTime);
            if (CollectionUtil.isEmpty(preDateListNewCust)) {
                log.info("分公司[{}-{}]的{}月新成交客户的预计流转日期检查: 无待流转客户.", subName, subId, startTimeStr);
                continue;
            }
            if (preDateListNewCust.size() > 1) {
                log.error("分公司[{}-{}]的{}月新成交客户的预计流转日期检查: 待流转日期应该为 {}, 实际查出的待流转日期为：{}, 请检查.", subName, subId, startTimeStr, nextLimitDateStr, preDateListNewCust.stream().map(Date::toString).collect(Collectors.joining(",")));
                return false;
            }
            Date preDate = preDateListNewCust.iterator().next();
            if (!DateUtil.isSameDay(preDate, visitLimitDate.getNextLimitDate())) {
                log.error("分公司[{}-{}]的{}月新成交客户的预计流转日期错误: 待流转表的流转日期[{}], 实时计算的流转日期[{}], 请检查.", subName, subId, startTimeStr, DateUtil.format(preDate, DatePattern.NORM_DATE_PATTERN), nextLimitDateStr);
                return false;
            }
            log.info("分公司[{}-{}]的{}月新成交客户的预计流转日期检查无误, 流转日期为[{}]", subName, subId, startTimeStr, DateUtil.format(preDate, DatePattern.NORM_DATE_PATTERN));
        }
        return true;
    }

    /**
     * Description: 检查当月的新签单客户是否进入待流转表. 结果应该是不进入待流转表,因为当月的新签单客户到次月1日才开始考核
     * @author: JiuDD
     * @param org  分司
     * @return boolean  正确返回true, 错误返回false
     * date: 2024/9/22 11:11
     */
    public boolean checkCurrentMonthNewDealCustomerCirculationDateIsOk(OrgDataThirdView org) {
        String subId = org.getId();
        String subName = org.getName();
        DateTime startDate = DateUtil.beginOfMonth(DateUtil.date());
        DateTime endDate = DateUtil.offsetMonth(startDate, 1);
        List<Date> preDateListNewCust = customerWillCirculationService.getPreDate(AssignCustSourceSpecialEnum.CIRCULATION.getValue(), DateUtil.beginOfDay(DateUtil.date()), subId, startDate, endDate);
        if (CollectionUtil.isNotEmpty(preDateListNewCust)) {
            log.error("分公司[{}-{}]的{}月新签单客户不应该进入待流转表, 但是实际查出的待流转日期为：{}", subName, subId, DateUtil.format(startDate, "yyyy-MM"), preDateListNewCust.stream().map(Date::toString).collect(Collectors.joining(",")));
            return false;
        }
        log.info("分公司[{}-{}]的{}月新签单客户不应该进入待流转表, 实际检查结果也是没有, 校验OK", subName, subId, DateUtil.format(startDate, "yyyy-MM"));
        return true;
    }

    /**
     * Description: 检查 2024年8月2日自动流转功能上线之前的老成交客户(smadb.cm_cust_protect表的 first_sign_time 字段空)的流转日期是否正确: 应该全部为'2024-11-01' + 考核周期*N (注: N>=1)
     * @author: JiuDD
     * @param monthWithoutClock 考核周期
     * @param org              分司
     * @return boolean        正确返回true, 错误返回false
     * date: 2024/9/22 11:02
     */
    public boolean checkOldDealCustomerCirculationDateIsOk(Integer monthWithoutClock, OrgDataThirdView org) {
        String subId = org.getId();
        String subName = org.getName();
        DateUtils.CirculationLimitDateResult limitDate = DateUtils.getVisitCirculationLimitDate(null, monthWithoutClock);
        log.info("分公司[{}-{}]的2024年8月2日自动流转功能上线之前的老成交客户的预计流转日期检查开始, 考核日期为[{}]", subName, subId, limitDate);
        Date nextLimitDateOldCust = limitDate.getNextLimitDate();
        List<Date> preDateListOldCust = customerWillCirculationService.getPreDate(AssignCustSourceSpecialEnum.CIRCULATION.getValue(), DateUtil.beginOfDay(DateUtil.date()), subId, null, null);
        if (CollectionUtil.isEmpty(preDateListOldCust)) {
            log.info("分公司[{}-{}]的2024年8月2日自动流转功能上线之前的老成交客户的预计流转日期检查: 待流转表无待流转客户。", subName, subId);
            return true;
        }
        if (preDateListOldCust.size() > 1) {
            log.error("分公司[{}-{}]的2024年8月2日自动流转功能上线之前的老成交客户的预计流转日期应该全部为[{}], 实际查出的待流转日期为：{}, 请检查。", subName, subId, DateUtil.format(nextLimitDateOldCust, DatePattern.NORM_DATE_PATTERN), preDateListOldCust.stream().map(Date::toString).collect(Collectors.joining(",")));
            return false;
        }
        Date preDate = preDateListOldCust.iterator().next();
        if (!DateUtil.isSameDay(preDate, nextLimitDateOldCust)) {
            log.error("分公司[{}-{}]的2024年8月2日自动流转功能上线之前的老成交客户的预计流转日期错误: 待流转表的流转日期[{}], 实时计算的流转日期[{}], 请检查。", subName, subId, DateUtil.format(preDate, DatePattern.NORM_DATE_PATTERN), DateUtil.format(nextLimitDateOldCust, DatePattern.NORM_DATE_PATTERN));
            return false;
        }
        log.info("分公司[{}-{}]的2024年8月2日自动流转功能上线之前的老成交客户的预计流转日期检查无误, 流转日期为[{}]", subName, subId, DateUtil.format(preDate, DatePattern.NORM_DATE_PATTERN));
        return true;
    }

    /**
     * Description: 客户流失时，flag1从 6(保护中已成交) 改为 4(保护过)，同步到搜客宝es
     * @author: JiuDD
     * @param cmCustProtect
     * @return void
     * date: 2025/3/25 16:46
     */
    private void synFlag1From6to4(CmCustProtect cmCustProtect) {
        try {
            BigDataCompanyDetailByNameView companyDetailByPid = bigDataThirdService.getCompanyDetailByName(cmCustProtect.getCustName());
            if (Objects.isNull(companyDetailByPid) || org.apache.commons.lang3.StringUtils.isBlank(companyDetailByPid.getPid())) {
                log.warn("skb无此企业，无需更改flag！{}", cmCustProtect.getCustName());
                return;
            }
            SendBigDataView sendBigDataView = new SendBigDataView();
            sendBigDataView.setEntIdList(Lists.newArrayList(companyDetailByPid.getPid()));
            sendBigDataView.setStatus(HighSearchProtectStatusEnum.PROTECTED.getValue());
            log.info("客户流失，同步大数据flag1=4，sendBigDataView={}", JSON.toJSONString(sendBigDataView));
            bigDataThirdService.sendCustStatusToBigData(sendBigDataView);
        } catch (Exception e) {
            log.error("客户流失，同步大数据flag1=4失败，客户id={}", cmCustProtect.getCustId(), e);
        }
    }
}
