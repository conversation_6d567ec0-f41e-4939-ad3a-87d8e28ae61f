package com.ce.scrm.center.service.enums;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/15 11:43
 */
public enum CustomerGradeEnum {

    /**
     * S级客户（最高级别）
     */
    S_LEVEL(1, "S级客户"),

    /**
     * A级客户
     */
    A_LEVEL(2, "A级客户"),

    /**
     * B级客户
     */
    B_LEVEL(3, "B级客户"),

    /**
     * C级客户
     */
    C_LEVEL(4, "C级客户");

    private final Integer code;
    private final String description;

    /**
     * 构造函数
     * @param code 类型编码（自然数序号）
     * @param description 类型描述
     */
    CustomerGradeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取类型编码
     * @return 类型编码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取类型描述
     * @return 类型描述
     */
    public String getDescription() {
        return description;
    }

    public static String getDescriptionByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomerGradeEnum csutomerGradeEnum : values()) {
            if (csutomerGradeEnum.getCode().equals(code)) {
                return csutomerGradeEnum.description;
            }
        }
        return null;
    }

    public static Integer getCodeByDescription(String description) {
        if (description == null) {
            return null;
        }
        for (CustomerGradeEnum csutomerGradeEnum : values()) {
            if (csutomerGradeEnum.getDescription().equals(description)) {
                return csutomerGradeEnum.code;
            }
        }
        return null;
    }

}
