package com.ce.scrm.center.service.eqixiu.sdk.service;

import com.ce.scrm.center.service.eqixiu.sdk.common.Result;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Customer;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.AuthCustomerQuery;
import com.ce.scrm.center.service.eqixiu.sdk.support.HttpClient;
import com.ce.scrm.center.service.eqixiu.sdk.support.TokenCache;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.HashMap;

/**
 * 访客信息接口
 * <a href="https://hc.eqxiu.cn/doc/2765/">...</a>
 *
 * <AUTHOR>
 */
public class CustomerService extends ConnectService {

    private static final String API_CUSTOMER_REGISTER = "/api/v1/biz/customer/register";
    private static final String API_CUSTOMER_INFO = "/api/v1/biz/customer/info";
    private static final String API_CUSTOMER_ID = "/api/v1/biz/customer/encodeId";
    private static final String API_CUSTOMER_AUTH_LIST = "/api/v1/editor/statistic/auth/visitor/list";


    public CustomerService(Secret secret) {
        super(secret);
    }

    public CustomerService(Secret secret, TokenCache tokenCache) {
        super(secret, tokenCache);
    }

    public CustomerService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        super(secret, tokenCache, httpClient);
    }

    /**
     * 注册访客
     * 访客还没登录易企秀平台的情况下可以通过接口提前注册。
     *
     * @param thirdUserId 三方用户id，最长支持64位
     * @return
     */
    public boolean registerCustomer(String thirdUserId) {
        if (StrUtil.isEmpty(thirdUserId)) {
            logger.warn("thirdUserId 不能为空");
            return false;
        }
        if (thirdUserId.length() > 64) {
            logger.warn("thirdUserId 长度不能超过64位");
            return false;
        }
        HashMap<String, String> map = createParamMapWithToken();
        map.put("thirdUserId", thirdUserId);
        JSONObject result = httpClient.httpPost(getApiURL(API_CUSTOMER_REGISTER, map), null, map);
        printLog(result, "注册访客失败:{}");
        return result.getSuccess();

    }

    /**
     * 获取访客信息
     *
     * @param encodeId
     * @return
     */
    public Customer getCustomerInfo(String encodeId) {
        if (StrUtil.isEmpty(encodeId)) {
            logger.warn("encodeId 不能为空");
            return null;
        }
        HashMap<String, String> map = createParamMapWithToken();
        map.put("encodeId", encodeId);
        JSONObject result = httpClient.httpGet(getApiURL(API_CUSTOMER_INFO), null, map);
        printLog(result, "获取访客信息失败:{}");
        return result.getObj(Customer.class);
    }


    /**
     * 根据三方用户id获取易企秀访客的唯一 ID
     *
     * @param userId
     * @return
     */
    public String getEncodeId(String userId) {
        if (StrUtil.isEmpty(userId)) {
            logger.warn("userId 不能为空");
            return null;
        }
        HashMap<String, String> map = createParamMapWithToken();
        map.put("thirdUserId", userId);
        JSONObject result = httpClient.httpGet(getApiURL(API_CUSTOMER_ID), null, map);
        printLog(result, "获取访客encodeId失败:{}");
        return (String) result.getObj(HashMap.class).get("encodeId");
    }

    /**
     * 查询作品授权访客列表
     *
     * @param query
     * @return
     */
    public Result<Customer> findCreationAuthCustomer(AuthCustomerQuery query) {
        paramValidate(query);
        JSONObject object = httpClient.httpGet(getApiURL(API_CUSTOMER_AUTH_LIST), null, query.getParamsMap());
        printLog(object, "查询作品授权访客列表失败:{}");
        return getResult(object, Customer.class);
    }

}
