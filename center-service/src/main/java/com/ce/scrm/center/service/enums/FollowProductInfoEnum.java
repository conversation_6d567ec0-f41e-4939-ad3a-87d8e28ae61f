package com.ce.scrm.center.service.enums;

import com.ce.scrm.center.service.business.entity.view.FollowProductInfoBusinessView;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
public enum FollowProductInfoEnum {

    DIGITAL_PORTAL("1","门户类",
            new HashMap<String,String>() {{
                put("1_1","数字门户");
                put("1_1_2","数字门户KA");
                put("1_1_3","数字门户行业解决方案");
                put("1_3","中小定制");
                put("1_2","KA定制");
                put("1_4_1","其他");
            }}),
    ECOLOGY("2","生态类",
            new HashMap<String,String>() {{
                put("2_1","安全证书");
                put("2_6","域名");
                put("2_5","全球邮");
                put("2_7","SEO推广");
                put("2_4","营销推广");
                put("2_2","广告投放");
                put("2_8","生态其他");
            }}),
    KUAJING("99","跨境类",
            new HashMap<String,String>() {{
                put("99_1","建站");
                put("99_2","推广");
                put("99_3","社媒");
                put("99_4","CRM");
            }}),
    ;

    private final String code;
    private final String name;
    private final Map<String,String> map;

    FollowProductInfoEnum(String code, String name, Map<String, String> map) {
        this.code = code;
        this.name = name;
        this.map = map;
    }

    public static List<FollowProductInfoBusinessView> getFollowProductInfoBusinessViewList(){
        List<FollowProductInfoBusinessView> result = new ArrayList<>();
        for (FollowProductInfoEnum followProductInfoEnum : FollowProductInfoEnum.values()) {
            FollowProductInfoBusinessView item = new FollowProductInfoBusinessView();
            item.setCode(followProductInfoEnum.getCode());
            item.setName(followProductInfoEnum.getName());
            List<FollowProductInfoBusinessView> children = new ArrayList<>();
            followProductInfoEnum.getMap().forEach((k, v) -> {
                FollowProductInfoBusinessView child = new FollowProductInfoBusinessView();
                child.setCode(k);
                child.setName(v);
                children.add(child);
            });
            item.setChildren(children);
            result.add(item);
        };
        return result;
    }

}
