package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.framework.core.utils.UUIDGenerator;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CurrentBusinessMonthTemporaryTable;
import com.ce.scrm.center.dao.entity.CustomerFollow;
import com.ce.scrm.center.dao.entity.query.SmaDictionaryItemQuery;
import com.ce.scrm.center.dao.entity.view.SmaDictionaryItemNewView;
import com.ce.scrm.center.dao.service.CurrentBusinessMonthTemporaryTableService;
import com.ce.scrm.center.dao.service.CustomerFollowService;
import com.ce.scrm.center.dao.service.SmaDictionaryItemService;
import com.ce.scrm.center.service.business.entity.dto.follow.CustomerFollowBusinessCreateDto;
import com.ce.scrm.center.service.business.entity.dto.follow.QueryBusinessMonthBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.follow.QueryFollowDetailListBusinessDto;
import com.ce.scrm.center.service.business.entity.response.BusinessCodeMessageEnum;
import com.ce.scrm.center.service.business.entity.response.BusinessResult;
import com.ce.scrm.center.service.business.entity.view.BusinessMonthBusinessView;
import com.ce.scrm.center.service.business.entity.view.FollowProductInfoBusinessView;
import com.ce.scrm.center.service.business.entity.view.follow.FollowDetailBusinessView;
import com.ce.scrm.center.service.enums.FollowProductInfoEnum;
import com.ce.scrm.center.service.enums.ProtectCustTypeEnum;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.entity.dto.QueryVisitLogOneThirdDto;
import com.ce.scrm.center.service.third.entity.view.CmCustVisitLogThirdView;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CmCustVisitLogThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.extend.dubbo.entity.request.CmCustVisitLogReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FollowBusiness {

    @Resource
    private CustomerFollowService customerFollowService;

    @Resource
    private CmCustVisitLogThirdService cmCustVisitLogThirdService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private OrgThirdService orgThirdService;

    @Resource
    private SmaDictionaryItemService smaDictionaryItemService;

    @Resource
    private CurrentBusinessMonthTemporaryTableService currentBusinessMonthTemporaryTableService;

    @Resource
    private CustomerThirdService customerThirdService;

    public List<FollowProductInfoBusinessView> getProductInfoEnum() {
        return FollowProductInfoEnum.getFollowProductInfoBusinessViewList();
    }

    public Page<FollowDetailBusinessView> getFollowDetailList(QueryFollowDetailListBusinessDto queryBusinessDto) {

        // 兼容前端 同步异步问题
        if (StringUtils.isNotBlank(queryBusinessDto.getBusinessMonth())) {
            CurrentBusinessMonthTemporaryTable currentBusinessMonthTemporaryTable = currentBusinessMonthTemporaryTableService.lambdaQuery().eq(CurrentBusinessMonthTemporaryTable::getBussinessMonth, queryBusinessDto.getBusinessMonth()).last("limit 1").one();
            if (currentBusinessMonthTemporaryTable == null) {
                throw new RuntimeException("商务月不存在");
            }
            if (Objects.equals(queryBusinessDto.getType(),"signedAmtPredictMonth")) {
                queryBusinessDto.setPredictSignDateStart(currentBusinessMonthTemporaryTable.getBeginDate());
                queryBusinessDto.setPredictSignDateEnd(currentBusinessMonthTemporaryTable.getEndDate());
            }
            if (Objects.equals(queryBusinessDto.getType(),"receivedAmtPredictMonth")) {
                queryBusinessDto.setPredictAccountDateStart(currentBusinessMonthTemporaryTable.getBeginDate());
                queryBusinessDto.setPredictAccountDateEnd(currentBusinessMonthTemporaryTable.getEndDate());
            }
        }

        LambdaQueryChainWrapper<CustomerFollow> lambdaQuery = customerFollowService.lambdaQuery();
        if (PositionUtil.isBusinessArea(queryBusinessDto.getLoginPosition())) {
            lambdaQuery.eq(CustomerFollow::getAreaId, queryBusinessDto.getLoginAreaId());
        }else if (PositionUtil.isBusinessMajor(queryBusinessDto.getLoginPosition())) {
            lambdaQuery.eq(CustomerFollow::getAreaId, queryBusinessDto.getLoginAreaId());
            lambdaQuery.eq(CustomerFollow::getSubId, queryBusinessDto.getLoginSubId());
        }else if (PositionUtil.isBusinessBu(queryBusinessDto.getLoginPosition())) {
            lambdaQuery.eq(CustomerFollow::getAreaId, queryBusinessDto.getLoginAreaId());
            lambdaQuery.eq(CustomerFollow::getSubId, queryBusinessDto.getLoginSubId());
            lambdaQuery.eq(StringUtils.isNoneBlank(queryBusinessDto.getLoginBuId()),CustomerFollow::getBuId, queryBusinessDto.getLoginBuId());
        }else if (PositionUtil.isBusinessManager(queryBusinessDto.getLoginPosition())) {
            lambdaQuery.eq(CustomerFollow::getAreaId, queryBusinessDto.getLoginAreaId());
            lambdaQuery.eq(CustomerFollow::getSubId, queryBusinessDto.getLoginSubId());
            lambdaQuery.eq(StringUtils.isNoneBlank(queryBusinessDto.getLoginBuId()),CustomerFollow::getBuId, queryBusinessDto.getLoginBuId());
            lambdaQuery.eq(CustomerFollow::getDeptId, queryBusinessDto.getLoginOrgId());
        }else if (PositionUtil.isBusinessSaler(queryBusinessDto.getLoginPosition())) {
            lambdaQuery.eq(CustomerFollow::getAreaId, queryBusinessDto.getLoginAreaId());
            lambdaQuery.eq(CustomerFollow::getSubId, queryBusinessDto.getLoginSubId());
            lambdaQuery.eq(StringUtils.isNoneBlank(queryBusinessDto.getLoginBuId()),CustomerFollow::getBuId, queryBusinessDto.getLoginBuId());
            lambdaQuery.eq(CustomerFollow::getDeptId, queryBusinessDto.getLoginOrgId());
            lambdaQuery.eq(CustomerFollow::getEmpId, queryBusinessDto.getLoginEmployeeId());
        }

        lambdaQuery.eq(StringUtils.isNoneBlank(queryBusinessDto.getSubId()), CustomerFollow::getSubId, queryBusinessDto.getSubId());
        lambdaQuery.eq(StringUtils.isNoneBlank(queryBusinessDto.getBuId()), CustomerFollow::getBuId, queryBusinessDto.getBuId());
        lambdaQuery.eq(StringUtils.isNoneBlank(queryBusinessDto.getDeptId()), CustomerFollow::getDeptId, queryBusinessDto.getDeptId());
        lambdaQuery.eq(StringUtils.isNoneBlank(queryBusinessDto.getSalerId()), CustomerFollow::getEmpId, queryBusinessDto.getSalerId());

        lambdaQuery.between(queryBusinessDto.getFollowTimeStart() != null && queryBusinessDto.getFollowTimeEnd() != null,
                CustomerFollow::getFollowTime, queryBusinessDto.getFollowTimeStart(), queryBusinessDto.getFollowTimeEnd());
        lambdaQuery.eq(StringUtils.isNoneBlank(queryBusinessDto.getSalesStage()), CustomerFollow::getSalesStage, queryBusinessDto.getSalesStage());
        lambdaQuery.in(CollectionUtils.isNotEmpty(queryBusinessDto.getSalesStageList()), CustomerFollow::getSalesStage, queryBusinessDto.getSalesStageList());
        lambdaQuery.between(queryBusinessDto.getPredictSignDateStart() != null && queryBusinessDto.getPredictSignDateEnd() != null,
                CustomerFollow::getPredictSignDate,queryBusinessDto.getPredictSignDateStart(), queryBusinessDto.getPredictSignDateEnd());
        lambdaQuery.between(queryBusinessDto.getPredictAccountDateStart() != null && queryBusinessDto.getPredictAccountDateEnd() != null,
                CustomerFollow::getPredictAccountDate,queryBusinessDto.getPredictAccountDateStart(),queryBusinessDto.getPredictAccountDateEnd());

        Page<CustomerFollow> page = new Page<>(queryBusinessDto.getCurrentPage(), queryBusinessDto.getPageSize());
        Page<CustomerFollow> customerFollowPage = lambdaQuery.page(page);

        List<CustomerFollow> records = customerFollowPage.getRecords();

        // 组装
        Set<String> orgIdSet = new HashSet<>();
        Set<String> empIdSet = new HashSet<>();
        Set<String> salerStateIdSet = new HashSet<>();
        for (CustomerFollow customerFollow : records) {
            orgIdSet.add(customerFollow.getDeptId());
            orgIdSet.add(customerFollow.getBuId());
            orgIdSet.add(customerFollow.getSubId());
            orgIdSet.add(customerFollow.getAreaId());
            empIdSet.add(customerFollow.getEmpId());
            salerStateIdSet.add(customerFollow.getSalesStage());
        }
        orgIdSet = orgIdSet.stream().filter(StringUtils::isNoneBlank).collect(Collectors.toSet());
        empIdSet = empIdSet.stream().filter(StringUtils::isNoneBlank).collect(Collectors.toSet());
        salerStateIdSet = salerStateIdSet.stream().filter(StringUtils::isNoneBlank).collect(Collectors.toSet());
        Map<String,String> orgMap;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orgIdSet)) {
            List<OrgThirdDto> orgThirdDtoList = orgThirdService.selectListByIds(new ArrayList<>(orgIdSet));
            orgMap =orgThirdDtoList.stream().collect(Collectors.toMap(OrgThirdDto::getId,OrgThirdDto::getName,(key1,key2) -> key2));
        }else {
            orgMap = Collections.emptyMap();
        }

        Map<String, EmployeeInfoThirdDto> employeeMap;
        if (CollectionUtils.isNotEmpty(empIdSet)) {
            employeeMap = employeeThirdService.getEmployeeDataMap(new ArrayList<>(empIdSet));
        }else {
            employeeMap = Collections.emptyMap();
        }

        Map<String, String> salerStateIdAndNameMap;
        if (CollectionUtils.isNotEmpty(salerStateIdSet)) {
            List<SmaDictionaryItemNewView> smaDictionaryItemNewViews = smaDictionaryItemService.selectByCondition(SmaDictionaryItemQuery.builder().idList(new ArrayList<>(salerStateIdSet)).build());
            salerStateIdAndNameMap = smaDictionaryItemNewViews.stream().collect(Collectors.toMap(SmaDictionaryItemNewView::getId, SmaDictionaryItemNewView::getName, (key1, key2) -> key2));
        }else {
            salerStateIdAndNameMap = Collections.emptyMap();
        }

        List<FollowDetailBusinessView> resultList = new ArrayList<>();
        for (CustomerFollow customerFollow : records) {

            FollowDetailBusinessView businessView = BeanUtil.copyProperties(customerFollow, FollowDetailBusinessView.class);

            EmployeeInfoThirdDto employeeInfoThirdDto = employeeMap.get(businessView.getEmpId());
            businessView.setEmpName(employeeInfoThirdDto == null ? "" : employeeInfoThirdDto.getName());
            businessView.setDeptName(orgMap.get(businessView.getDeptId()));
            businessView.setBuName(orgMap.get(businessView.getBuId()));
            businessView.setSubName(orgMap.get(businessView.getSubId()));
            businessView.setAreaName(orgMap.get(businessView.getAreaId()));
            businessView.setSalesStageStr(salerStateIdAndNameMap.get(customerFollow.getSalesStage()));

            CmCustVisitLogThirdView custVisitLog = cmCustVisitLogThirdService.queryVisitLogOne(
                    QueryVisitLogOneThirdDto.builder()
                            .custId(customerFollow.getCustomerId())
                            .salerId(customerFollow.getEmpId())
                            .build()
            );

            if (custVisitLog != null) {
                businessView.setCustTypeStr(
                        Objects.equals(custVisitLog.getCustType(), ProtectCustTypeEnum.ORDERED.getValue()) || Objects.equals(custVisitLog.getCustType(),ProtectCustTypeEnum.SECOND_DEVELOPMENT.getValue())
                        ? "成交" : "未成交");
            }

            resultList.add(businessView);
        }

        Page<FollowDetailBusinessView> result = new Page<>();
        BeanUtil.copyProperties(customerFollowPage,result);
        result.setRecords(resultList);
        return result;
    }

    public BusinessMonthBusinessView getBusinessMonthInfoByName(QueryBusinessMonthBusinessDto queryBusinessDto) {

        if (StringUtils.isBlank(queryBusinessDto.getBusinessMonth())) {
            return null;
        }

        CurrentBusinessMonthTemporaryTable currentBusinessMonthTemporaryTable = currentBusinessMonthTemporaryTableService.lambdaQuery().eq(CurrentBusinessMonthTemporaryTable::getBussinessMonth, queryBusinessDto.getBusinessMonth()).last("limit 1").one();

        if (currentBusinessMonthTemporaryTable == null) {
            return null;
        }

        return BeanUtil.copyProperties(currentBusinessMonthTemporaryTable, BusinessMonthBusinessView.class);
    }


    /***
     * 1.同一个客户ID和empID的记录，只有一条
     * 2.写 cm_cust_visit_log 表
     * @param customerFollowBusinessCreateDto
     * <AUTHOR>
     * @date 2025/8/11 09:48
     * @version 1.0.0
     * @return com.ce.scrm.center.service.business.entity.response.BusinessResult<java.lang.Boolean>
    **/
    public BusinessResult<Boolean> create(CustomerFollowBusinessCreateDto customerFollowBusinessCreateDto) {
        Date current = new Date();
        // 查询 customerId是否存在
        Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(customerFollowBusinessCreateDto.getCustomerId());
        if (!customerData.isPresent()){
            return BusinessResult.error(BusinessCodeMessageEnum.DATA_NOT_EXIST);
        }
        //构造customerFollow对象
        CustomerFollow customerFollow = new CustomerFollow();
        customerFollow.setCustomerId(customerFollowBusinessCreateDto.getCustomerId());
        customerFollow.setCustomerName(customerData.get().getCustomerName());
        customerFollow.setEmpId(customerFollowBusinessCreateDto.getLoginEmployeeId());
        customerFollow.setDeptId(customerFollowBusinessCreateDto.getLoginOrgId());
        customerFollow.setBuId(customerFollowBusinessCreateDto.getLoginBuId());
        customerFollow.setSubId(customerFollowBusinessCreateDto.getLoginSubId());
        customerFollow.setAreaId(customerFollowBusinessCreateDto.getLoginAreaId());
        customerFollow.setSyncManagerFlag(1);
        //customerFollow.setIntentionLevel();
        //customerFollow.setVipFlag();
        //customerFollow.setExpectDealAmount();
        //customerFollow.setNextFollowTime();
        //customerFollow.setWebsiteUrl();
        //customerFollow.setWebsiteState();
        customerFollow.setAddress(customerFollowBusinessCreateDto.getAddress());
        customerFollow.setProvince(customerFollowBusinessCreateDto.getProvinceCode());
        customerFollow.setCity(customerFollowBusinessCreateDto.getCityCode());
        customerFollow.setRegion(customerFollowBusinessCreateDto.getDistrictCode());
        //customerFollow.setRemarks();
        customerFollow.setContent(customerFollowBusinessCreateDto.getContent());
        customerFollow.setDeleteFlag(0);
        customerFollow.setEmpStatus(0);
        customerFollow.setCreateTime(current);
        customerFollow.setUpdateTime(current);
        //sdr 写跟进 默认是需求确认 阶段
        customerFollow.setSalesStage("DICT_SALES_STAGE_002");
        customerFollow.setFollowTime(current);

        //同一个客户ID和empID的记录，只有一条
        LambdaQueryChainWrapper<CustomerFollow> lambdaQuery = customerFollowService.lambdaQuery();
        lambdaQuery.eq(CustomerFollow::getCustomerId, customerFollowBusinessCreateDto.getCustomerId());
        lambdaQuery.eq(CustomerFollow::getEmpId, customerFollowBusinessCreateDto.getLoginEmployeeId());
        List<CustomerFollow> list = lambdaQuery.list();
        if (CollectionUtils.isEmpty(list)) {
            //新增
            customerFollowService.save(customerFollow);
            Long id = customerFollow.getId();
            customerFollow.setId(id);
        }else{
            //更新
            Long id = list.get(0).getId();
            customerFollow.setId(id);
            customerFollowService.updateById(customerFollow);
        }
        // 写cm_cust_visit_log表

        CmCustVisitLogReq cmCustVisitLogReq = new CmCustVisitLogReq();
        cmCustVisitLogReq.setCustId(customerFollowBusinessCreateDto.getCustomerId());
        cmCustVisitLogReq.setCustName(customerData.get().getCustomerName());
        // 生成一个uuId作为唯一主键，用于记录的更新操作
        String visitLogUuId = UUIDGenerator.generator();
        cmCustVisitLogReq.setUuId(visitLogUuId);
        // 自然时间
        Calendar c = Calendar.getInstance();
        c.setTime(current);
        // 自然年
        cmCustVisitLogReq.setNatureYear(c.get(Calendar.YEAR));
        // 自然月
        cmCustVisitLogReq.setNatureMonth(c.get(Calendar.MONTH) + 1);
        // 【181226】不用商务年月，使用自然年月
        // 商务年
        cmCustVisitLogReq.setYear(c.get(Calendar.YEAR));
        // 商务月
        cmCustVisitLogReq.setMonth(c.get(Calendar.MONTH) + 1);
        // 商务天
        Integer day = c.get(Calendar.DAY_OF_MONTH);
        cmCustVisitLogReq.setDay(day);
        String protectCustType = customerData.get().getProtectCustType();
        if (protectCustType != null){
            cmCustVisitLogReq.setCustType(Integer.valueOf(protectCustType));
            cmCustVisitLogReq.setCustTypeLable(ProtectCustTypeEnum.of(cmCustVisitLogReq.getCustType()).getLable());
        }else{
            cmCustVisitLogReq.setCustType(ProtectCustTypeEnum.PROTECT_FOLLOW.getValue());
            cmCustVisitLogReq.setCustTypeLable(ProtectCustTypeEnum.PROTECT_FOLLOW.getLable());
        }
        cmCustVisitLogReq.setLinkManId(customerFollowBusinessCreateDto.getCustomerId());
        cmCustVisitLogReq.setLinkManName(customerFollowBusinessCreateDto.getContactPersonName());
        cmCustVisitLogReq.setLinkManMobile(customerFollowBusinessCreateDto.getContactPersonMobile());
        cmCustVisitLogReq.setSalerId(customerFollowBusinessCreateDto.getLoginEmployeeId());
        cmCustVisitLogReq.setBussdeptId(customerFollowBusinessCreateDto.getLoginOrgId());
        cmCustVisitLogReq.setSubcompanyId(customerFollowBusinessCreateDto.getLoginSubId());
        cmCustVisitLogReq.setAreaId(customerFollowBusinessCreateDto.getLoginAreaId());
        cmCustVisitLogReq.setVisitType(customerFollowBusinessCreateDto.getVisitType());
        cmCustVisitLogReq.setVisitTime(current);
        cmCustVisitLogReq.setFollowRecordId(String.valueOf(customerFollow.getId()));
        // 手动录入
        cmCustVisitLogReq.setRecordWay("DICT_VISIT_RECORDWAY_01");
        cmCustVisitLogReq.setContent(customerFollowBusinessCreateDto.getContent());
        cmCustVisitLogReq.setSalesStage("DICT_SALES_STAGE_002");
        cmCustVisitLogReq.setUpdateNums(0);
        cmCustVisitLogReq.setBudget(new BigDecimal("0"));
        cmCustVisitLogReq.setAttachmentsList(StringUtils.join(customerFollowBusinessCreateDto.getFileIds(), ","));
        cmCustVisitLogReq.setIsAutoFlag(0);
        cmCustVisitLogReq.setIntentionProductList(StringUtils.join(customerFollowBusinessCreateDto.getIntentionProductList(), ","));
        cmCustVisitLogReq.setCreateBy(customerFollowBusinessCreateDto.getLoginEmployeeId());
        cmCustVisitLogReq.setUpdateBy(customerFollowBusinessCreateDto.getLoginEmployeeId());
        cmCustVisitLogReq.setCreateTime(current);
        cmCustVisitLogReq.setUpdateTime(current);
        cmCustVisitLogReq.setRoleName("SDR");
        cmCustVisitLogThirdService.create(cmCustVisitLogReq);
        return BusinessResult.success( true);
    }
}
