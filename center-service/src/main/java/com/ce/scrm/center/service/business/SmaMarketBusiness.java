package com.ce.scrm.center.service.business;

import cn.ce.cesupport.enums.YesOrNoEnum;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ce.scrm.center.dao.entity.SmaMarket;
import com.ce.scrm.center.dao.entity.SmaMarketArea;
import com.ce.scrm.center.dao.entity.query.SmaMarketAreaQuery;
import com.ce.scrm.center.dao.entity.query.SmaMarketQuery;
import com.ce.scrm.center.dao.entity.query.SmaMarketSubcompanyQuery;
import com.ce.scrm.center.dao.entity.view.SmaMarketAreaView;
import com.ce.scrm.center.dao.entity.view.SmaMarketSubcompanyView;
import com.ce.scrm.center.dao.entity.view.SmaMarketView;
import com.ce.scrm.center.dao.service.SmaMarketAreaService;
import com.ce.scrm.center.dao.service.SmaMarketService;
import com.ce.scrm.center.dao.service.SmaMarketSubcompanyService;
import com.ce.scrm.center.service.business.entity.dto.AreaCodeListQueryDto;
import com.ce.scrm.center.service.business.entity.dto.CustomerMarketDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description: 市场
 * @Author: lijinpeng
 * @Date: 2024/12/10 09:25
 */
@Service
@Slf4j
public class SmaMarketBusiness {

    @Resource
    private SmaMarketAreaService smaMarketAreaService;

    @Resource
    private SmaMarketService smaMarketService;

    @Resource
    private SmaMarketSubcompanyService smaMarketSubcompanyService;

    /*
     * @Description 根据areaCode cityCode subId查询客户市场中 是否是我的市场 市场名称  =-= 老项目搬过来的
     * <AUTHOR>
     * @date 2024/12/11 10:55
     * @param areaCode
     * @param cityCode
     * @param subId
     * @return com.ce.scrm.center.service.business.entity.dto.CustomerMarketDto
     */
    public CustomerMarketDto getCustomerMarket(String areaCode, String cityCode,String subId) {
        CustomerMarketDto customerMarketDto = new CustomerMarketDto();
        if (StringUtils.isEmpty(areaCode)) {
            if (!StringUtils.isEmpty(cityCode)) {
                try {
                    areaCode = cityCode.substring(0, 5) + "1";
                } catch (Exception e) {
                    log.error("客户的城市编码有误，城市编码={}", cityCode);
                }
            }
        }

        if(StringUtils.isEmpty(areaCode)){
            // 如果判断客户所对应的员工市场失败，则认为客户不在员工市场
            customerMarketDto.setMyMarketFlag(YesOrNoEnum.NO.getCode());
            return customerMarketDto;
        }
        // 根据区域id判断该客户是否有权限收藏
        SmaMarketAreaView smaMarketArea = smaMarketAreaService.selectOneByCondition(
                SmaMarketAreaQuery.builder()
                        .areaCode(areaCode)
                        .build());
        if(smaMarketArea == null){
            customerMarketDto.setMyMarketFlag(YesOrNoEnum.NO.getCode());
            return customerMarketDto;
        }
        String marketId = smaMarketArea.getMarketId();
        List<SmaMarketSubcompanyView> smaMarketSubcompanyViewList =
                smaMarketSubcompanyService.selectByCondition(
                        SmaMarketSubcompanyQuery.builder().subCompany(subId).build()
                );
        List<String> marketIdsList = smaMarketSubcompanyViewList.stream().map(SmaMarketSubcompanyView::getMarketId).collect(Collectors.toList());
        if (marketIdsList.contains(marketId)) {
            customerMarketDto.setMyMarketFlag(YesOrNoEnum.YES.getCode());
        } else {
	        customerMarketDto.setMyMarketFlag(YesOrNoEnum.NO.getCode());
        }
        SmaMarketView smaMarket = smaMarketService.selectOneByCondition(
                SmaMarketQuery.builder()
                        .id(smaMarketArea.getMarketId())
                        .build());
        if (smaMarket != null) {
            customerMarketDto.setMarketName(smaMarket.getName());
        }
        return customerMarketDto;
    }

    /*
     * @Description 根据条件查询区域code
     * <AUTHOR>
     * @date 2025/1/16 10:22
     * @param areaCodeListQueryDto
     * @return java.util.List<java.lang.String>
     */
    public List<String> getAreaCodeListByCondition(AreaCodeListQueryDto areaCodeListQueryDto) {
        log.info("getAreaCodeListByCondition, 入参为areaCodeListQueryDto={}", JSON.toJSONString(areaCodeListQueryDto));

        String provinceCode = areaCodeListQueryDto.getProvinceCode();
        String cityCode = areaCodeListQueryDto.getCityCode();
        String regionCode = areaCodeListQueryDto.getRegionCode();
        List<String> subIdList = areaCodeListQueryDto.getSubIdList();

        if(StringUtils.isNotBlank(regionCode)) {
            return Collections.singletonList(regionCode);
        }

        List<SmaMarketSubcompanyView> smaMarketSubcompanyViews = smaMarketSubcompanyService.selectByCondition(
                SmaMarketSubcompanyQuery.builder()
                        .subIdList(subIdList)
                        .build()
        );
        if (CollectionUtils.isEmpty(smaMarketSubcompanyViews)) {
            return Collections.emptyList();
        }
        List<SmaMarketAreaView> smaMarketAreaViews = smaMarketAreaService.selectByCondition(
                SmaMarketAreaQuery.builder()
                        .marketIdList(smaMarketSubcompanyViews.stream().map(SmaMarketSubcompanyView::getMarketId).collect(Collectors.toList()))
                        .provinceCode(provinceCode)
                        .cityCode(cityCode)
                        .areaCode(regionCode)
                        .build()
        );

        return smaMarketAreaViews.stream().map(SmaMarketAreaView::getAreaCode).collect(Collectors.toList());
    }


}
