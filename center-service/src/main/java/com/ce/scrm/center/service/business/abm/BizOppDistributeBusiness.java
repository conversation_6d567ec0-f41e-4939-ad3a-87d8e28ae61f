package com.ce.scrm.center.service.business.abm;

import cn.ce.cesupport.emp.service.OrgAppService;
import cn.ce.cesupport.newcustclue.service.ClueAssignAppService;
import cn.ce.cesupport.newcustclue.vo.ClueAssignVo;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.center.dao.entity.*;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.*;
import com.ce.scrm.center.service.business.CustomerBusiness;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.QueryCustomerBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmUpdateProtectDto;
import com.ce.scrm.center.service.business.entity.dto.abm.BizOppDistributeInfoDto;
import com.ce.scrm.center.service.business.entity.dto.abm.ReceiptConfirmDto;
import com.ce.scrm.center.service.business.entity.dto.abm.SjAssignDetailAddDto;
import com.ce.scrm.center.service.business.entity.view.customer.CustomerBusinessView;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.enums.ConvertRelationEnum;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.yml.ThirdCustomerConfig;
import com.ce.scrm.center.support.xxl.JobTemplate;
import com.ce.scrm.center.util.date.DateUtils;
import com.ce.scrm.customer.dubbo.api.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerUpdateDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 转商机下发 业务处理类
 */
@Slf4j
@Service
public class BizOppDistributeBusiness {

    @DubboReference
    private ClueAssignAppService clueAssignAppService;

    @Resource
    CustomerBusiness customerBusiness;

    @Resource
    CmCustProtectService cmCustProtectService;

    @Resource
    SjAssignSalerConfigService sjAssignSalerConfigService;

    @Resource
    EmployeeInfoBusiness employeeInfoBusiness;

    @Resource
    private SmaMarketAreaService smaMarketAreaService;

    @Resource
    SjAssignSubConfigBusiness sjAssignSubConfigBusiness;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private SjAssignDetailService sjAssignDetailService;

    @Resource
    private JobTemplate jobTemplate;

    @Resource
    private AbmUpdateProtectBusiness abmUpdateProtectBusiness;

    @Resource
    private ThirdCustomerConfig thirdCustomerConfig;

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private ICustomerDubbo customerDubbo;

    @Resource
    private AbmMessageNoticeBusiness abmMessageNoticeBusiness;


    /***
     * 转商机
     * @param sdrPushSaleReview
     * <AUTHOR>
     * @date 2025/8/7 14:27
     * @version 1.0.0
     * @return void
     **/
    public Optional<String> bizOppDistribute(SdrPushSaleReview sdrPushSaleReview) {
        log.info("转商机下发,参数= {}", JSON.toJSONString(sdrPushSaleReview));
        String customerId = sdrPushSaleReview.getCustomerId();
        String customerName = sdrPushSaleReview.getCustomerName();
        Long reviewId = sdrPushSaleReview.getId();
        Integer reviewStatus = sdrPushSaleReview.getReviewStatus();
        // 创建人是CC、SDR
        String createdId = sdrPushSaleReview.getCreatedId();
        if (!Objects.equals(reviewStatus, 1)) {
            log.info("状态异常，当前审核记录的状态 {}不是：审核通过 ", reviewStatus);
            return Optional.of("异常审核状态,无法分配");
        }

        CustProtectView cmCustProtect = cmCustProtectService.selectOneByCondition(
                CmCustProtect.builder()
                        .custId(customerId)
                        .build()
        );

        // 当前客户是否是保护中
        boolean isProtected = false;
        if (Objects.nonNull(cmCustProtect)) {
            Integer status = cmCustProtect.getStatus();

            if (!Objects.equals(status, ProtectStateEnum.CUSTOMER_POOL.getState())) {
                isProtected = true;
                String salerId = cmCustProtect.getSalerId();
                log.info("当前customerName={} 保护中 status={} salerId={}",customerName, status, salerId);
                if (Objects.equals(salerId, createdId)) {
                    // 当前是商务保护中
                    // 且保护商务是：SDR/CC
                    // 这种需要给商务发送消息
                    isProtected = false;
                }
            }
        }

        if (isProtected) {
            /**
             *  保护中 处理逻辑
             */
            BizOppDistributeInfoDto bizOppDistributeInfoDto = new BizOppDistributeInfoDto();
            bizOppDistributeInfoDto.setCustomerId(customerId);
            bizOppDistributeInfoDto.setCustomerName(customerName);
            bizOppDistributeInfoDto.setReviewId(reviewId);
            bizOppDistributeInfoDto.setReviewSrcType(sdrPushSaleReview.getReviewSrcType());
            bizOppDistributeInfoDto.setCmCustProtectId(cmCustProtect.getId());

            // 保护中、待分配
            Integer status = cmCustProtect.getStatus();
            String salerId = cmCustProtect.getSalerId();
            String bussdeptId = cmCustProtect.getBussdeptId();
            String buId = cmCustProtect.getBuId();
//            String areaId = cmCustProtect.getAreaId();
            String subcompanyId = cmCustProtect.getSubcompanyId();
            if (Objects.equals(status, ProtectStateEnum.PROTECT.getState())) {
                bizOppDistributeInfoDto.setDesignatedProtectEmpId(salerId);
                bizOppDistributeInfoDto.setDesignatedProtectOrg(bussdeptId);
                bizOppDistributeInfoDto.setDesignatedProtectStatus(ProtectStateEnum.PROTECT.getState());
            } else if (Objects.equals(status, ProtectStateEnum.MAJOR_WILL_ASSIGN.getState())) {
                // 分配给分司总监
                bizOppDistributeInfoDto.setDesignatedProtectOrg(subcompanyId);
                bizOppDistributeInfoDto.setDesignatedProtectStatus(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState());
            } else if (Objects.equals(status, ProtectStateEnum.MANAGER_WILL_ASSIGN.getState())) {
                // 分配给商务经理
                bizOppDistributeInfoDto.setDesignatedProtectOrg(bussdeptId);
                bizOppDistributeInfoDto.setDesignatedProtectStatus(ProtectStateEnum.MANAGER_WILL_ASSIGN.getState());
            } else if (Objects.equals(status, ProtectStateEnum.BU_WILL_ASSIGN.getState())) {
                // 分配给BU经理
                bizOppDistributeInfoDto.setDesignatedProtectOrg(buId);
                bizOppDistributeInfoDto.setDesignatedProtectStatus(ProtectStateEnum.BU_WILL_ASSIGN.getState());
            } else {
                log.error("客户保护状态异常，客户ID：{}，客户名称：{}，客户保护状态：{}", customerId, customerName, status);
                return Optional.of("客户保护状态异常");
            }

            return distributeAction(bizOppDistributeInfoDto);
        } else {
            /**
             *  非保护中，是否在收藏夹 处理逻辑
             */
            QueryCustomerBusinessDto queryCustomerBusinessDto = new QueryCustomerBusinessDto();
            queryCustomerBusinessDto.setCustomerName(customerName);
            CustomerBusinessView customerBusinessView = customerBusiness.getCustomerByCustomerName(queryCustomerBusinessDto);
            String sourceDataId;

            if (Objects.isNull(customerBusinessView)) {
                Optional<CustomerDataThirdView> customerDataByCustId = customerBusiness.getCustomerDataByCustId(customerId);
                if (!customerDataByCustId.isPresent()) {
                    log.error("在客户表中，找不到对应的客户信息 customerName={} customerId={}", customerName, customerId);
                    return Optional.of("找不到对应的客户信息");
                }else {
                    log.error("在客户表中customerName={} customerId={}  审核传入名称：{}", customerDataByCustId.get().getCustomerName(), customerId, customerName);
                }
                CustomerDataThirdView customerDataThirdView = customerDataByCustId.get();
                sourceDataId = customerDataThirdView.getSourceDataId();
            }else {
                sourceDataId = customerBusinessView.getSourceDataId();
            }

            ClueAssignVo clueAssignVoByName = null;
            List<ClueAssignVo> byUncid = null;

            //4、收藏夹表不能有此名称
            if (StringUtils.hasText(customerName)){
                clueAssignVoByName = clueAssignAppService.getOneByCustName(customerName);
            }

            //5、收藏夹表不能有此uncid
            if (StringUtils.hasText(sourceDataId)) {
                byUncid = clueAssignAppService.getByUncid(sourceDataId);
            }
            if (Objects.isNull(clueAssignVoByName) && CollectionUtil.isEmpty(byUncid)) {
                // 非收藏中、非收藏、非待分配
                // 按规则分配
                log.info("客户={} 非收藏、非待分配,按规则分配",customerId);
                String assignSubId = sdrPushSaleReview.getAssignSubId();
                log.info("客户={} 将会被分配到={}", customerId, assignSubId);
                SjAssignSalerConfig nextSalerBySubId = sjAssignSalerConfigService.getNextSalerBySubId(assignSubId);
                if (Objects.isNull(nextSalerBySubId)) {
                    log.info("客户={}在分司={}未获取到下发的销售", customerName, assignSubId);
                    return Optional.of("下发失败，未找到对应的商务");
                }
                String salerId = nextSalerBySubId.getSalerId();
                BizOppDistributeInfoDto bizOppDistributeInfoDto = new BizOppDistributeInfoDto();
                bizOppDistributeInfoDto.setCustomerId(customerId);
                bizOppDistributeInfoDto.setCustomerName(customerName);
                bizOppDistributeInfoDto.setReviewId(reviewId);
                bizOppDistributeInfoDto.setReviewSrcType(sdrPushSaleReview.getReviewSrcType());
                EmployeeInfoBusinessDto employeeInfoByEmpId = employeeInfoBusiness.getEmployeeInfoByEmpId(salerId);
                if (Objects.isNull(employeeInfoByEmpId)) {
                    log.error("未找到下发商务={}对应的信息", salerId);
                    return Optional.of("未找到下发商务对应的信息");
                }
                bizOppDistributeInfoDto.setDesignatedProtectStatus(ProtectStateEnum.PROTECT.getState());
                bizOppDistributeInfoDto.setDesignatedProtectEmpId(salerId);
                bizOppDistributeInfoDto.setWxNoticeType(1);
                return distributeAction(bizOppDistributeInfoDto);
            } else {
                /**
                 * 客户在收藏夹中
                 */
                BizOppDistributeInfoDto bizOppDistributeInfoDto = new BizOppDistributeInfoDto();
                bizOppDistributeInfoDto.setCustomerId(customerId);
                bizOppDistributeInfoDto.setCustomerName(customerName);
                bizOppDistributeInfoDto.setReviewId(reviewId);
                bizOppDistributeInfoDto.setReviewSrcType(sdrPushSaleReview.getReviewSrcType());
                // 收藏中
                Integer status = null;
                ClueAssignVo clueAssignVo = null;
                String createUser = null;
                if (Objects.nonNull(clueAssignVoByName)) {
                    clueAssignVo = clueAssignVoByName;
                    status = clueAssignVoByName.getStatus();
                    createUser = clueAssignVoByName.getCreateUser();
                } else {
                    if (!CollectionUtils.isEmpty(byUncid)) {
                        clueAssignVo = byUncid.get(0);
                        status = clueAssignVo.getStatus();
                        createUser = clueAssignVo.getCreateUser();
                    }
                }
                if (Objects.isNull(status)) {
                    log.error("客户被收藏，但是状态有点问题....{}  {} 收藏状态为空", customerId, customerName);
                    return Optional.of("客户被收藏中，但是收藏状态为空");
                } else {
                    log.info("客户={} 被收藏",customerId);
                    //  STATUS  '1总监2经理3商务',
                    if (Objects.equals(status, 1)) {
                        bizOppDistributeInfoDto.setDesignatedProtectOrg(clueAssignVo.getAreaId());
                        bizOppDistributeInfoDto.setDesignatedProtectStatus(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState());
                    } else if (Objects.equals(status, 2)) {
                        bizOppDistributeInfoDto.setDesignatedProtectOrg(clueAssignVo.getBuId());
                        bizOppDistributeInfoDto.setDesignatedProtectStatus(ProtectStateEnum.MANAGER_WILL_ASSIGN.getState());
                    } else if (Objects.equals(status, 3)) {
                        bizOppDistributeInfoDto.setDesignatedProtectStatus(ProtectStateEnum.PROTECT.getState());
                        bizOppDistributeInfoDto.setWxNoticeType(1);
                        if (Objects.equals(createdId, createUser)) {
                            // 说明是SDR、CC收藏的 ==》 直接按照规则下发
                            String assignSubId = sdrPushSaleReview.getAssignSubId();
                            log.info("客户={} 将会被分配到={}", customerId, assignSubId);
                            SjAssignSalerConfig nextSalerBySubId = sjAssignSalerConfigService.getNextSalerBySubId(assignSubId);
                            if (Objects.isNull(nextSalerBySubId)) {
                                log.info("客户={}在分司={}未获取到下发的销售", customerName, assignSubId);
                                return Optional.of("下发失败，未找到对应的商务");
                            }
                            String salerId = nextSalerBySubId.getSalerId();
                            bizOppDistributeInfoDto.setCustomerId(customerId);
                            bizOppDistributeInfoDto.setCustomerName(customerName);
                            bizOppDistributeInfoDto.setReviewId(reviewId);
                            EmployeeInfoBusinessDto employeeInfoByEmpId = employeeInfoBusiness.getEmployeeInfoByEmpId(salerId);
                            if (Objects.isNull(employeeInfoByEmpId)) {
                                log.error("customerName={} 未找到下发商务={}对应的信息", customerName, salerId);
                                return Optional.of("未找到下发商务对应的信息");
                            }
                            bizOppDistributeInfoDto.setDesignatedProtectEmpId(salerId);
                        } else {
                            bizOppDistributeInfoDto.setDesignatedProtectEmpId(createUser);
                            bizOppDistributeInfoDto.setDesignatedProtectStatus(ProtectStateEnum.PROTECT.getState());
                        }
                    } else {
                        log.error("客户被收藏，但是状态有点问题....{}  {} 收藏状态为{}", customerId, customerName, status);
                        return Optional.of("客户被收藏中，但是状态有点问题");
                    }
                }
                return distributeAction(bizOppDistributeInfoDto);
            }
        }
    }

    /***
     * 找到客户所属的市场下的某一个分司
     * @param customerId
     * <AUTHOR>
     * @date 2025/8/7 20:11
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> getSubBySmaMarketArea(String customerId) {
        Optional<CustomerDataThirdView> customerDataByCustId = customerBusiness.getCustomerDataByCustId(customerId);
        if (customerDataByCustId.isPresent()) {
            CustomerDataThirdView customerDataThirdView = customerDataByCustId.get();
            String provinceCode = customerDataThirdView.getProvinceCode();
            String cityCode = customerDataThirdView.getCityCode();
            if (StringUtils.isEmpty(provinceCode) && StringUtils.isEmpty(cityCode)) {
                log.error("客户{}省市信息不全 provinceCode={} cityCode={} 找不到对应的市场", customerId, provinceCode, cityCode);
                return Optional.empty();
            } else {
                // 找到市场
                List<SmaMarketArea> list = smaMarketAreaService.lambdaQuery()
                        .eq(SmaMarketArea::getProvinceCode, provinceCode)
                        .eq(SmaMarketArea::getCityCode, cityCode)
                        .select(SmaMarketArea::getMarketId)
                        .list();
                if (CollectionUtils.isEmpty(list)) {
                    log.error("customerId = {} provinceCode={} cityCode={} 获取不到对应市场有问题", customerId, provinceCode, cityCode);
                    return Optional.empty();
                } else {
                    // 市场下分司 -轮询获取
                    Set<String> markIdSet = list.stream().map(SmaMarketArea::getMarketId).collect(Collectors.toSet());
                    Optional<String> nextSubId = sjAssignSubConfigBusiness.getNextSubId(Lists.newLinkedList(markIdSet));
                    if (nextSubId.isPresent()) {
                        return nextSubId;
                    } else {
                        log.error("customerId = {} markIdSet={}下未找到有用的分司", customerId, JSON.toJSONString(markIdSet));
                        return Optional.empty();
                    }
                }
            }
        } else {
            log.error("为找到客户={}", customerId);
            return Optional.empty();
        }
    }

    /***
     * 下发商机到人/机构
     * @param bizOppDistributeInfoDto
     * <AUTHOR>
     * @date 2025/8/7 17:11
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> distributeAction(BizOppDistributeInfoDto bizOppDistributeInfoDto) {
        log.info("下发商机到人/机构 {}", JSON.toJSONString(bizOppDistributeInfoDto));
        // 执行分配
        String customerId = bizOppDistributeInfoDto.getCustomerId();
        String customerName = bizOppDistributeInfoDto.getCustomerName();
        Long reviewId = bizOppDistributeInfoDto.getReviewId();
        Integer designatedProtectStatus = bizOppDistributeInfoDto.getDesignatedProtectStatus();
        String designatedProtectEmpId = bizOppDistributeInfoDto.getDesignatedProtectEmpId();
        String designatedProtectOrg = bizOppDistributeInfoDto.getDesignatedProtectOrg();

        SjAssignDetailAddDto sjAssignDetailAddDto = new SjAssignDetailAddDto();
        sjAssignDetailAddDto.setCustomerId(customerId);
        sjAssignDetailAddDto.setCustomerName(customerName);
        sjAssignDetailAddDto.setReviewId(reviewId);
        sjAssignDetailAddDto.setReviewSrcType(bizOppDistributeInfoDto.getReviewSrcType());

        if (Objects.equals(designatedProtectStatus, ProtectStateEnum.PROTECT.getState())) {
            sjAssignDetailAddDto.setSalerId(designatedProtectEmpId);
            sjAssignDetailAddDto.setCreatedId(designatedProtectEmpId);

            // 获取完整结构信息
            buildOrgInfoByEmpId(designatedProtectEmpId, sjAssignDetailAddDto);
            // 产生明细记录
            log.info("sjAssignDetailAddDto = {}", JSON.toJSONString(sjAssignDetailAddDto));

            SjAssignDetail entity = BeanUtil.copyProperties(sjAssignDetailAddDto, SjAssignDetail.class);
            log.info("产生明细记录 entity = {}", JSON.toJSONString(entity));
            saveDistributeDetail(entity);
            // 分配给销售 - update 保护关系
            Optional<String> optional = updateProtectStatus(bizOppDistributeInfoDto);
            if (optional.isPresent()) {
                return optional;
            }

            LocalDateTime nextExecutionTime = LocalDateTime.now();
            nextExecutionTime = nextExecutionTime.plusMinutes(90);
            Date date = Date.from(nextExecutionTime.atZone(ZoneId.systemDefault()).toInstant());
            entity.setReceiptEndTime(date);

            // 更新客户表-回执到期日期
            CustomerUpdateDubboDto customerUpdateDubboDto = new CustomerUpdateDubboDto();
            customerUpdateDubboDto.setCustomerId(entity.getCustomerId());
//            customerUpdateDubboDto.setCustomerName(entity.getCustomerName());
            customerUpdateDubboDto.setOperator(bizOppDistributeInfoDto.getDesignatedProtectEmpId());
            customerUpdateDubboDto.setSourceKey(thirdCustomerConfig.getKey());
            customerUpdateDubboDto.setSourceSecret(thirdCustomerConfig.getSecret());
            customerUpdateDubboDto.setReceiptEndTime(date);
            customerUpdateDubboDto.setReceiptSalerId(bizOppDistributeInfoDto.getDesignatedProtectEmpId());
            customerUpdateDubboDto.setReceiptFlag(0);
            DubboResult<Boolean> update = customerDubbo.update(customerUpdateDubboDto);
            if (!update.checkSuccess()) {
                // 不阻断，流程往下走
                log.error("更新客户表-回执到期日期失败 customerUpdateDubboDto={}", JSON.toJSONString(customerUpdateDubboDto));
                return Optional.of("更新客户表-回执到期日期失败 customerUpdateDubboDto=" + JSON.toJSONString(customerUpdateDubboDto));
            }

            addReceiptXxlTask(entity);
            // 剩余半个小时时，提醒方式：短信提醒
            addReceiptRemindXxlTask(entity, 1, 30);

            boolean succeed = sjAssignDetailService.lambdaUpdate()
                    .eq(SjAssignDetail::getId, entity.getId())
                    .set(SjAssignDetail::getReceiptEndTime, entity.getReceiptEndTime())
                    .set(SjAssignDetail::getUpdatedId, entity.getSalerId())
                    .set(SjAssignDetail::getUpdatedTime, new Date())
                    .update();
            if (!succeed) {
                log.error("更新回执到期时间失败");
            }
        } else {
            // 分配总监 、经理
            Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(designatedProtectOrg);
            if (orgLeader.isPresent()) {

                EmployeeLiteThirdView employeeLiteThirdView = orgLeader.get();
                // 获取完整结构信息
                buildOrgInfoByEmpId(employeeLiteThirdView.getId(), sjAssignDetailAddDto);
                sjAssignDetailAddDto.setCreatedId(employeeLiteThirdView.getId());

                // 产生明细记录
                SjAssignDetail entity = BeanUtil.copyProperties(sjAssignDetailAddDto, SjAssignDetail.class);
                saveDistributeDetail(entity);

                // 总监 、经理 - update 保护关系
                Optional<String> optional = updateProtectStatus(bizOppDistributeInfoDto);
                if (optional.isPresent()) {
                    return optional;
                }

            } else {
                log.error("下发 到org={} 未找到leader 流转中断", designatedProtectOrg);
                return Optional.of("商机下发到org" + designatedProtectOrg + " 未找到leader 流转中断");
            }
        }

        // 企微通知类型： 0：不通知 1：通知商务 2：通知总监/ 经理
        Integer wxNoticeType = bizOppDistributeInfoDto.getWxNoticeType();
        if (Objects.equals(wxNoticeType, 1)) {
            abmMessageNoticeBusiness.wxNoticeMsg(bizOppDistributeInfoDto.getCustomerName(),bizOppDistributeInfoDto.getDesignatedProtectEmpId());
        } else if (Objects.equals(wxNoticeType, 2)) {
            // 这些待分配的先不通知
        }
        return Optional.empty();
    }

    /***
     * 回执到期xxl到期任务
     * @param entity
     * <AUTHOR>
     * @date 2025/8/8 17:03
     * @version 1.0.0
     * @return void
     **/
    public Integer addReceiptXxlTask(SjAssignDetail entity) {
        log.info("回执到期xxl到期任务 {}", JSON.toJSONString(entity));
        LocalDateTime receiptEndTime = LocalDateTime.ofInstant(entity.getReceiptEndTime().toInstant(), ZoneId.systemDefault());
        String execCron = receiptEndTime.format(DateUtils.CUSTOM_CRON_FORMATTER);
        // 下发明细的id
        Long id = entity.getId();
        String jobDesc = ServiceConstant.JobConstant.JobDescConstant.SCRM_ABM_RECEIPT_CHECK_JOB_DESC + id;
        log.info("回执到期xxl到期任务参数: jobDesc={} execCron={} id={}", jobDesc, execCron, id);
        int jobId = jobTemplate.addJob(jobDesc
                , execCron
                , ServiceConstant.JobConstant.JobHandlerConstant.SCRM_ABM_RECEIPT_CHECK_JOB_HANDLER
                , String.valueOf(id));

        if (jobId > 0) {
            log.info("创建 {} 任务成功: jobId={}", ServiceConstant.JobConstant.JobDescConstant.SCRM_ABM_RECEIPT_CHECK_JOB_DESC, jobId);
        } else {
            log.error("创建{} 任务失败，返回的jobId无效: jobId={}", ServiceConstant.JobConstant.JobDescConstant.SCRM_ABM_RECEIPT_CHECK_JOB_DESC, jobId);
        }
        return jobId;
    }

    /***
     * 回执提醒任务
     * @param entity
     * @param remindType  提醒类型 1：短信 2：AI电话
     * @param minutes
     * <AUTHOR>
     * @date 2025/8/8 17:03
     * @version 1.0.0
     * @return void
     **/
    public Integer addReceiptRemindXxlTask(SjAssignDetail entity, Integer remindType, Integer minutes) {
        log.info("回执-提醒-xxl到期任务 {} minutes={}", JSON.toJSONString(entity), minutes);
        LocalDateTime receiptEndTime = LocalDateTime.ofInstant(entity.getReceiptEndTime().toInstant(), ZoneId.systemDefault());
        LocalDateTime remindTime = receiptEndTime.plusMinutes(-minutes);
        String execCron = remindTime.format(DateUtils.CUSTOM_CRON_FORMATTER);
        // 下发明细的id
        Long id = entity.getId();
        String jobDesc = ServiceConstant.JobConstant.JobDescConstant.SCRM_ABM_RECEIPT_REMIND_JOB_DESC + id;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("remindType", remindType);
        jsonObject.put("id", id);
        String params = JSON.toJSONString(jsonObject);
        log.info("回执-提醒-xxl到期任务参数: jobDesc={} execCron={} params={}", jobDesc, execCron, params);
        int jobId = jobTemplate.addJob(jobDesc
                , execCron
                , ServiceConstant.JobConstant.JobHandlerConstant.SCRM_ABM_RECEIPT_REMIND_JOB_HANDLER
                , params);

        if (jobId > 0) {
            log.info("创建 {} 任务成功: jobId={}", ServiceConstant.JobConstant.JobDescConstant.SCRM_ABM_RECEIPT_REMIND_JOB_DESC, jobId);
        } else {
            log.error("创建{} 任务失败，返回的jobId无效: jobId={}", ServiceConstant.JobConstant.JobDescConstant.SCRM_ABM_RECEIPT_REMIND_JOB_DESC, jobId);
        }
        return jobId;
    }

    /***
     * 跟进任务-到期xxl到期任务
     * @param sjAssignDetail
     * <AUTHOR>
     * @date 2025/8/10 00:08
     * @version 1.0.0
     * @return void
     **/
    public Integer addCustFollowXxlTask(SjAssignDetail sjAssignDetail) {
        log.info("跟进任务-到期xxl到期任务 {}", JSON.toJSONString(sjAssignDetail));
        LocalDateTime receiptEndTime = LocalDateTime.ofInstant(sjAssignDetail.getFollowEndTime().toInstant(), ZoneId.systemDefault());
        String execCron = receiptEndTime.format(DateUtils.CUSTOM_CRON_FORMATTER);
        // 下发明细的id
        Long id = sjAssignDetail.getId();
        String jobDesc = ServiceConstant.JobConstant.JobDescConstant.SCRM_ABM_CUST_FOLLOW_CHECK_JOB_DESC + id;
        log.info("跟进任务-到期xxl到期任务参数: jobDesc={} execCron={} id={}", jobDesc, execCron, id);
        int jobId = jobTemplate.addJob(jobDesc
                , execCron
                , ServiceConstant.JobConstant.JobHandlerConstant.SCRM_ABM_CUST_FOLLOW_CHECK_JOB_HANDLER
                , String.valueOf(id));

        if (jobId > 0) {
            log.info("创建 {} 任务成功: jobId={}", ServiceConstant.JobConstant.JobDescConstant.SCRM_ABM_RECEIPT_CHECK_JOB_DESC, jobId);
        } else {
            log.error("创建{} 任务失败，返回的jobId无效: jobId={}", ServiceConstant.JobConstant.JobDescConstant.SCRM_ABM_RECEIPT_CHECK_JOB_DESC, jobId);
        }
        return jobId;
    }

    /***
     * 获取完整结构信息
     * @param empId
     * @param sjAssignDetailAddDto
     * <AUTHOR>
     * @date 2025/8/8 15:07
     * @version 1.0.0
     * @return void
     **/
    private void buildOrgInfoByEmpId(String empId, SjAssignDetailAddDto sjAssignDetailAddDto) {
        EmployeeInfoBusinessDto employeeInfoByEmpId = employeeInfoBusiness.getEmployeeInfoByEmpId(empId);
        log.info("获取完整结构信息 ={}", JSON.toJSONString(employeeInfoByEmpId));
        sjAssignDetailAddDto.setSalerId(employeeInfoByEmpId.getId());
        sjAssignDetailAddDto.setDeptId(employeeInfoByEmpId.getOrgId());
        sjAssignDetailAddDto.setSubId(employeeInfoByEmpId.getSubId());
        sjAssignDetailAddDto.setBuId(employeeInfoByEmpId.getBuId());
        sjAssignDetailAddDto.setAreaId(employeeInfoByEmpId.getAreaId());
    }

    /***
     * 保存下发记录
     * @param entity
     * <AUTHOR>
     * @date 2025/8/8 14:08
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> saveDistributeDetail(SjAssignDetail entity) {
        entity.setCreateTime(new Date());
        boolean save = sjAssignDetailService.save(entity);
        if (save) {
            String subId = entity.getSubId();
            // 更新分司轮询配置表权重
            sjAssignSubConfigBusiness.updateWeight(subId);

            //  更新分司-商务轮询配置表权重
            String salerId = entity.getSalerId();
            sjAssignSalerConfigService.updateWeight(salerId);

            return Optional.empty();
        } else {
            log.error("保存商机下发明细记录失败 {}", JSON.toJSONString(entity));
            return Optional.of("保存商机下发明细记录失败");
        }
    }


    /***
     * 回执确认
     * @param receiptConfirmDto
     * <AUTHOR>
     * @date 2025/8/8 16:44
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> doReceipt(ReceiptConfirmDto receiptConfirmDto) {
        log.info("回执确认参数={} ", JSON.toJSONString(receiptConfirmDto));
        Date currentTime = new Date();

        String customerId = receiptConfirmDto.getCustomerId();
        String confirmDtoSalerId = receiptConfirmDto.getSalerId();
        SjAssignDetail sjAssignDetail = sjAssignDetailService.getOne(new LambdaQueryWrapper<SjAssignDetail>()
                .eq(SjAssignDetail::getCustomerId, customerId)
                .eq(SjAssignDetail::getReceiptFlag, 0)
                .orderByDesc(SjAssignDetail::getId)
        );

        if (Objects.isNull(sjAssignDetail)) {
            log.info("客户无待回执的记录 {}", customerId);
            return Optional.of("客户无待回执的记录");
        } else {
            String salerId = sjAssignDetail.getSalerId();
            if (!Objects.equals(confirmDtoSalerId, salerId)) {
                log.info("商务={} 无权限操作客户{}的回执", confirmDtoSalerId, customerId);
                return Optional.of("无权限操作该客户的回执");
            } else {
                Date receiptEndTime = sjAssignDetail.getReceiptEndTime();
                Integer receiptFlag = sjAssignDetail.getReceiptFlag();
                if (Objects.equals(receiptFlag, 1)) {
                    log.info("客户不能重复回执 {}", customerId);
                    return Optional.of("不能重复回执");
                }

                if (Objects.isNull(receiptEndTime)) {
                    log.error("客户商机下发记录有问题，找不到回执到期时间，不能回执 {}", customerId);
                    return Optional.of("回执失败");
                } else {
                    if (receiptEndTime.getTime() < currentTime.getTime()) {
                        log.info("已经过了回执时间段，不能操作回执");
                        return Optional.of("已经过了回执时间段，不能操作回执");
                    }
                }

                LocalDateTime nextExecutionTime = LocalDateTime.now();
                nextExecutionTime = nextExecutionTime.plusHours(48);
                Date followEndTime = Date.from(nextExecutionTime.atZone(ZoneId.systemDefault()).toInstant());

                boolean update = sjAssignDetailService.lambdaUpdate()
                        .eq(SjAssignDetail::getId, sjAssignDetail.getId())
                        .eq(SjAssignDetail::getSalerId, confirmDtoSalerId)
                        .set(SjAssignDetail::getReceiptFlag, 1)
                        .set(SjAssignDetail::getUpdatedId, confirmDtoSalerId)
                        .set(SjAssignDetail::getFollowStartTime, nextExecutionTime)
                        .set(SjAssignDetail::getFollowEndTime, followEndTime)
                        .set(SjAssignDetail::getUpdatedTime, currentTime)
                        .update();
                if (!update) {
                    log.error("商务{}对{}回执确认失败", confirmDtoSalerId, customerId);
                    return Optional.of("回执确认失败");
                } else {
                    Optional<String> cleared = abmUpdateProtectBusiness.clearRequireReceiptFlag(customerId, confirmDtoSalerId, null);
                    cleared.ifPresent(s -> log.error("更新客户表-是否需要回执失败,{}", s));

                    // 添加48h内跟进 xxljob
                    addCustFollowXxlTask(sjAssignDetailService.getById(sjAssignDetail.getId()));
                }
            }
        }
        return Optional.empty();
    }

    /***
     * 统计一个审核通过记录下下发的次数
     * @param reviewId
     * <AUTHOR>
     * @date 2025/8/8 17:12
     * @version 1.0.0
     * @return java.lang.Long
     **/
    public Long countDistributeTimesByReviewId(Long reviewId) {
        long count = sjAssignDetailService.count(new LambdaQueryWrapper<SjAssignDetail>()
                .eq(SjAssignDetail::getReviewId, reviewId));
        log.info("统计一个审核通过记录 {} 下下发的次数 {}", reviewId, count);
        return count;
    }


    /***
     * 统计一个审核通过记录下-已经跟进检查的 次数
     * @param reviewId
     * <AUTHOR>
     * @date 2025/8/11 18:57
     * @version 1.0.0
     * @return java.lang.Long
     **/
    public Long countCheckFollowTimesByReviewId(Long reviewId) {
        long count = sjAssignDetailService.count(new LambdaQueryWrapper<SjAssignDetail>()
                .eq(SjAssignDetail::getReviewId, reviewId)
                .eq(SjAssignDetail::getFollowCheck, 1)
        );
        log.info("统计一个审核通过记录 {} 下-跟进检查的 次数 {}", reviewId, count);
        return count;
    }


    /***
     * 商机下发，更改保护关系、添加流转日志
     * @param bizOppDistributeInfoDto
     * <AUTHOR>
     * @date 2025/8/9 17:27
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> updateProtectStatus(BizOppDistributeInfoDto bizOppDistributeInfoDto) {
        String customerId = bizOppDistributeInfoDto.getCustomerId();
        String customerName = bizOppDistributeInfoDto.getCustomerName();
        Long reviewId = bizOppDistributeInfoDto.getReviewId();
        Integer reviewSrcType = bizOppDistributeInfoDto.getReviewSrcType();
        Integer designatedProtectStatus = bizOppDistributeInfoDto.getDesignatedProtectStatus();
        String designatedProtectEmpId = bizOppDistributeInfoDto.getDesignatedProtectEmpId();
        String designatedProtectOrg = bizOppDistributeInfoDto.getDesignatedProtectOrg();

        AbmUpdateProtectDto abmUpdateProtectDto = new AbmUpdateProtectDto();
        abmUpdateProtectDto.setCustId(customerId);
        abmUpdateProtectDto.setCustName(customerName);
        Date currentDate = new Date();
        if (Objects.equals(ProtectStateEnum.PROTECT.getState(), designatedProtectStatus)) {
            abmUpdateProtectDto.setStatus(designatedProtectStatus);
            abmUpdateProtectDto.setSalerId(designatedProtectEmpId);
            EmployeeInfoBusinessDto employeeInfoBusinessDto = employeeInfoBusiness.getEmployeeInfoByEmpId(designatedProtectEmpId);
            abmUpdateProtectDto.setBussdeptId(employeeInfoBusinessDto.getOrgId());
            abmUpdateProtectDto.setBuId(employeeInfoBusinessDto.getBuId());
            abmUpdateProtectDto.setAreaId(employeeInfoBusinessDto.getAreaId());
            abmUpdateProtectDto.setSubcompanyId(employeeInfoBusinessDto.getSubId());
            abmUpdateProtectDto.setProtectTime(currentDate);
            abmUpdateProtectDto.setExceedTime(DateUtil.offsetDay(currentDate, 30));

//            abmUpdateProtectDto.setConvertType(703);
//            abmUpdateProtectDto.setConvertTypeDesc("abm-商机下发到销售");
            abmUpdateProtectDto.setConvertType(ConvertRelationEnum.ABM_CROSS_BORDER_PUSH_BUSINESS.getValue());
            abmUpdateProtectDto.setConvertTypeDesc(ConvertRelationEnum.ABM_CROSS_BORDER_PUSH_BUSINESS.getLable());
        } else {
            Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(designatedProtectOrg);
            if (orgLeader.isPresent()) {
                EmployeeLiteThirdView employeeLiteThirdView = orgLeader.get();
                String id = employeeLiteThirdView.getId();
                EmployeeInfoBusinessDto employeeInfoBusinessDto = employeeInfoBusiness.getEmployeeInfoByEmpId(id);
                abmUpdateProtectDto.setStatus(designatedProtectStatus);
                abmUpdateProtectDto.setBussdeptId(employeeInfoBusinessDto.getOrgId());
                abmUpdateProtectDto.setBuId(employeeInfoBusinessDto.getBuId());
                abmUpdateProtectDto.setSubcompanyId(employeeInfoBusinessDto.getSubId());
                abmUpdateProtectDto.setAreaId(employeeInfoBusinessDto.getAreaId());
                abmUpdateProtectDto.setProtectTime(currentDate);
                abmUpdateProtectDto.setExceedTime(DateUtil.offsetDay(currentDate, 30));
                if (Objects.equals(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState(), designatedProtectStatus)) {
                    abmUpdateProtectDto.setConvertType(ConvertRelationEnum.ABM_CROSS_BORDER_PUSH_BUSINESS.getValue());
                    abmUpdateProtectDto.setConvertTypeDesc(ConvertRelationEnum.ABM_CROSS_BORDER_PUSH_BUSINESS.getLable());
//                    abmUpdateProtectDto.setConvertTypeDesc("abm-商机下发到总监");
                } else if (Objects.equals(ProtectStateEnum.MANAGER_WILL_ASSIGN.getState(), designatedProtectStatus)) {
//                    abmUpdateProtectDto.setConvertType(701);
//                    abmUpdateProtectDto.setConvertTypeDesc("abm-商机下发到经理");
                    abmUpdateProtectDto.setConvertType(ConvertRelationEnum.ABM_CROSS_BORDER_PUSH_BUSINESS.getValue());
                    abmUpdateProtectDto.setConvertTypeDesc(ConvertRelationEnum.ABM_CROSS_BORDER_PUSH_BUSINESS.getLable());
                } else if (Objects.equals(ProtectStateEnum.BU_WILL_ASSIGN.getState(), designatedProtectStatus)) {
//                    abmUpdateProtectDto.setConvertType(702);
//                    abmUpdateProtectDto.setConvertTypeDesc("abm-商机下发到事业部总监");
                    abmUpdateProtectDto.setConvertType(ConvertRelationEnum.ABM_CROSS_BORDER_PUSH_BUSINESS.getValue());
                    abmUpdateProtectDto.setConvertTypeDesc(ConvertRelationEnum.ABM_CROSS_BORDER_PUSH_BUSINESS.getLable());
                } else {
                    log.error("designatedProtectStatus={}异常状态,无法下发", designatedProtectStatus);
                    return Optional.of("商机下发失败");
                }
            } else {
                log.error("designatedProtectOrg={}异常机构id,无法下发", designatedProtectOrg);
                return Optional.of("商机下发失败,找不到机构领导人");
            }
        }
        return abmUpdateProtectBusiness.updateProtect(abmUpdateProtectDto);
    }


}
