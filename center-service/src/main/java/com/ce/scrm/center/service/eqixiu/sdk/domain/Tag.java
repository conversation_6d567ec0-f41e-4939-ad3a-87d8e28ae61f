package com.ce.scrm.center.service.eqixiu.sdk.domain;


import com.ce.scrm.center.service.eqixiu.sdk.domain.type.TagStatus;

import java.util.List;

/**
 * 标签dto
 *
 * @date 2021-03-04 10:54:54
 * @since 1.0.0
 */
public class Tag {

    /**
     * 顶级标签 parentId = 0
     */
    public static final Long PARENT_ID_TOP = 0L;
    /**
     * 排序默认值 0
     */
    private static final int DEFAULT_SORT = 0;
    private Long id;

    /**
     * 名称
     */
    private String title;

    /**
     * 关联对象类型
     */
    private Integer type;

    private String relId;

    /**
     * 上级id
     */
    private Long parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 1:有效 -1:删除
     */
    private TagStatus status;

    private List<Tag> children;

    private String bizCode;


    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        //TOTO 长度校验
        this.title = title;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public int getStatus() {
        if (status == null) {
            status = TagStatus.UNKNOWN;
        }
        return status.value();
    }

    public void setStatus(TagStatus status) {
        this.status = status;
    }

    public void setStatus(int status) {
        this.status = TagStatus.of(status);
    }

    public String getName() {
        return getTitle();
    }

    public void delete() {
        this.status = TagStatus.DELETED;
    }

    public List<Tag> getChildren() {
        return children;
    }

    public void setChildren(List<Tag> children) {
        this.children = children;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRelId() {
        return relId;
    }

    public void setRelId(String relId) {
        this.relId = relId;
    }

    @Override
    public String toString() {
        return "{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", parentId=" + parentId +
                ", sort=" + sort +
                ", children=" + children +
                '}';
    }
}
