package com.ce.scrm.center.service.business;

import cn.ce.cecloud.business.entity.BusiIntentionalProduct;
import cn.ce.cecloud.business.entity.BusinessOpportunity;
import cn.ce.cecloud.business.service.BusinessAppService;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.ce.cesupport.enums.IntentionLevelEnum;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.CustomerFollow;
import com.ce.scrm.center.dao.service.CustomerFollowService;
import com.ce.scrm.center.service.business.entity.ai.crm.CustomerFollowView;
import com.ce.scrm.center.service.business.entity.dto.ai.AiBusinessDto;
import com.ce.scrm.center.service.business.entity.view.SmaDictionaryItemView;
import com.ce.scrm.center.service.third.entity.view.CmCustVisitLogThirdView;
import com.ce.scrm.center.service.third.invoke.CmCustVisitLogThirdService;
import com.ce.scrm.extend.dubbo.api.IBigDataDubbo;
import com.ce.scrm.extend.dubbo.entity.response.CesupportBossInstanceRes;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.enums.VisitTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description: ai crm 业务
 * @Author: lijinpeng
 * @Date: 2025/2/17 14:45
 */
@Slf4j
@Service
public class AiCrmBusiness {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private IBigDataDubbo bigDataDubbo;

    @Resource
    private CustomerFollowService customerFollowService;

    @Resource
    private CmCustVisitLogThirdService cmCustVisitLogThirdService;

    @Resource
    private SmaDictionaryItemBusiness smaDictionaryItemBusiness;

    @DubboReference
    private BusinessAppService businessAppService;

    public List<CustomerFollowView> getCustomerFollow(AiBusinessDto aiBusinessDto) {

        if (StringUtils.isBlank(aiBusinessDto.getCustomerId())) {
            throw new ApiException(CodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }

        List<CustomerFollow> list = customerFollowService.lambdaQuery()
                .eq(CustomerFollow::getCustomerId, aiBusinessDto.getCustomerId()).last("limit 3").orderByDesc(CustomerFollow::getCreateTime)
                .list();

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

//        List<String> idList = list.stream().map(customerFollow -> String.valueOf(customerFollow.getId())).collect(Collectors.toList());

        List<CmCustVisitLogThirdView> visitLogThirdViewList = cmCustVisitLogThirdService.getVisitLogsBySiteClockRecordIdListByCustId(aiBusinessDto.getCustomerId());

        Map<String, CmCustVisitLogThirdView> visitLogMap = visitLogThirdViewList.stream().collect(
                Collectors.toMap(
                        CmCustVisitLogThirdView::getFollowRecordId,
                        visitLogThirdView -> visitLogThirdView,
                        (v1, v2) -> v1
                )
        );

        Set<String> dictionaryIdSet = new HashSet<>();
        visitLogThirdViewList.forEach(visitLog -> {
            String custDemandsList = visitLog.getCustDemandsList();
            if (StringUtils.isNotBlank(custDemandsList)) {
                String[] split = custDemandsList.split(",");
                dictionaryIdSet.addAll(Arrays.asList(split));
            }
        });

        List<SmaDictionaryItemView> smaDictionaryItemViewList = smaDictionaryItemBusiness.getByIdList(new ArrayList<>(dictionaryIdSet));
        Map<String, String> dictionaryMap = smaDictionaryItemViewList.stream()
                .collect(
                        Collectors.toMap(
                                SmaDictionaryItemView::getId,
                                SmaDictionaryItemView::getName,
                                (v1, v2) -> v2)
                );

        List<CustomerFollowView> customerFollowViewList = new ArrayList<>();
        for (CustomerFollow customerFollow : list) {


            CmCustVisitLogThirdView cmCustVisitLogThirdView = visitLogMap.get(String.valueOf(customerFollow.getId()));
            VisitTypeEnum visitTypeEnumByValue = null;
            List<String> custDemandsList = new ArrayList<>();
            if (cmCustVisitLogThirdView != null) {
                // 拜访类型
                visitTypeEnumByValue = VisitTypeEnum.getVisitTypeEnumByValue(cmCustVisitLogThirdView.getVisitType());

                // 意向产品类型集合
                String custDemandsListItem = cmCustVisitLogThirdView.getCustDemandsList();
                if (StringUtils.isNotBlank(custDemandsListItem)) {
                    String[] split = custDemandsListItem.split(",");
                    for (String item : split) {
                        custDemandsList.add(dictionaryMap.get(item));
                    }
                }

            }

            CustomerFollowView customerFollowView = CustomerFollowView.builder()
                    .intentionLevel(IntentionLevelEnum.getNameByCode(customerFollow.getIntentionLevel()))
                    .vipFlag(Objects.equals(1, customerFollow.getVipFlag()) ? "是" : "否")
                    .expectDealAmount(customerFollow.getExpectDealAmount())
                    .content(customerFollow.getContent())
                    .followTime(customerFollow.getCreateTime())
                    .visitType(visitTypeEnumByValue == null ? null : visitTypeEnumByValue.getLable())
                    .custDemandsList(custDemandsList)
                    .build();

            customerFollowViewList.add(customerFollowView);
        }

        log.info("getCustomerFollow=,result={}", JSONObject.toJSONString(customerFollowViewList));
        return customerFollowViewList;
    }

    /**
     * 获取成交客户的交易数据-已购产品信息
     */
    public String getCustomerDealings(AiBusinessDto aiBusinessDto) {
        if (StringUtils.isBlank(aiBusinessDto.getCustomerId())) {
            return StringUtils.EMPTY;
        }

        try {
            String custId = aiBusinessDto.getCustomerId();
            DubboResult<List<CesupportBossInstanceRes>> dubboResult = bigDataDubbo.findProductsByCustId(custId);
            if (dubboResult == null || !dubboResult.checkSuccess()) {
                log.info("获取大数据已购列表findProductsByCustId失败, custId={}", custId);
                return StringUtils.EMPTY;
            }
            List<CesupportBossInstanceRes> resultList = dubboResult.getData();
            Map<String, Object> dealingsProducts = new HashMap<>();
            if (CollectionUtils.isEmpty(resultList)) {
                log.info("获取大数据已购列表数据findProductsByCustId失败, resultList is emptyList, custId={}", custId);
                return StringUtils.EMPTY;
            }
            //log.info("*******返回数据**********:{}",JSONObject.toJSONString(resultList));
            List<String> statusList = new ArrayList<>(Arrays.asList("正常,运营中,已过期,待上线,生产中".split(",")));
            resultList = resultList.stream().filter(T -> statusList.contains(T.getStatusName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(resultList)) {
                log.info("获取大数据已购列表数据findProductsByCustId失败, resultList is emptyList, custId={}", custId);
                return StringUtils.EMPTY;
            }
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (CesupportBossInstanceRes cesupportBossInstanceRes : resultList) {
                // 产品名称
                dealingsProducts.put("产品名称", cesupportBossInstanceRes.getProductName());
                dealingsProducts.put("产品标识", cesupportBossInstanceRes.getInstanceTrade());
//                // 产品分类
//                dealingsProducts.put("产品分类", cesupportBossInstanceRes.getCategoryName());
//                dealingsProducts.put("产品开通时间", cesupportBossInstanceRes.getBeginTimeStr());
//                // 购买时长
//                Long daysBetween = DateUtils.getDaysBetween(cesupportBossInstanceRes.getBeginTimeStr(), cesupportBossInstanceRes.getEndTimeStr());
//                dealingsProducts.put("产品购买时长", daysBetween);
//                // 购买时长单位
//                dealingsProducts.put("产品购买时长单位", "天");
                // 产品到期时间
                dealingsProducts.put("到期时间", cesupportBossInstanceRes.getEndTimeStr());
                // 产品状态
                dealingsProducts.put("产品状态", cesupportBossInstanceRes.getStatusName());
                jsonObjectList.add(JSONObject.parseObject(JSON.toJSONString(dealingsProducts)));
            }
            jsonObjectList = jsonObjectList.stream().limit(10).collect(Collectors.toList());
            return JSONObject.toJSONString(jsonObjectList);
        } catch (Exception ex) {
            log.error("获取成交客户的交易数据失败: {}", ex.getMessage());
        }
        return StringUtils.EMPTY;
    }

//    public BusinessOpportunityView getBusinessOpportunity(AiBusinessDto aiBusinessDto) {
//        String busiOppoCode = aiBusinessDto.getBusiOppoCode();
//        if (StringUtils.isBlank(busiOppoCode)) {
//            return null;
//        }
//
//        BusinessOpportunity businessOpportunity = new BusinessOpportunity();
//        businessOpportunity.setBusiOppoCode(busiOppoCode);
//        List<BusinessOpportunity> busiOppList = businessAppService.findBusiOppList(businessOpportunity);
//        if (CollectionUtils.isEmpty(busiOppList)) {
//            return null;
//        }
//        BusinessOpportunity businessOpportunityItem = busiOppList.get(0);
//        if (businessOpportunityItem == null) {
//            return null;
//        }
//        Map<String, String> map = new HashMap<>();
//        map.put("busiOppoId", businessOpportunityItem.getId());
//        List<BusiIntentionalProduct> busiIntentionalProductByPage = businessAppService.findBusiIntentionalProductByPage(map);
//        List<String> productNameList = busiIntentionalProductByPage.stream().map(BusiIntentionalProduct::getProductCodeLabel).collect(Collectors.toList());
//        return BusinessOpportunityView.builder()
//                .custRequirement(businessOpportunityItem.getCustRequirement())
//                .oppoReferProductNameList(productNameList)
//                .intentTag(businessOpportunityItem.getIntentTag())
//                .createTime(businessOpportunityItem.getCreateTime())
//                .build();
//    }

    public JSONObject getBusinessOpportunityNew(AiBusinessDto aiBusinessDto) {
        String busiOppoCode = aiBusinessDto.getBusiOppoCode();
        if (StringUtils.isBlank(busiOppoCode)) {
            return null;
        }

        BusinessOpportunity businessOpportunity = new BusinessOpportunity();
        businessOpportunity.setBusiOppoCode(busiOppoCode);
        List<BusinessOpportunity> busiOppList = businessAppService.findBusiOppList(businessOpportunity);
        if (CollectionUtils.isEmpty(busiOppList)) {
            return null;
        }
        BusinessOpportunity businessOpportunityItem = busiOppList.get(0);
        if (businessOpportunityItem == null) {
            return null;
        }
        Map<String, String> map = new HashMap<>();
        map.put("busiOppoId", businessOpportunityItem.getId());
        List<BusiIntentionalProduct> busiIntentionalProductByPage = businessAppService.findBusiIntentionalProductByPage(map);
        List<String> productStrList = new ArrayList<>();
        Set<String> orderStrSet = new HashSet<>();
        for (BusiIntentionalProduct product : busiIntentionalProductByPage) {

            productStrList.add(product.getProductCodeLabel());
            orderStrSet.add(product.getExtend());

        }

        JSONObject result = new JSONObject(new LinkedHashMap<>());
        result.put("资讯产品名称列表",productStrList);
        result.put("需求描述",businessOpportunityItem.getCustRequirement());
        result.put("商机等级",businessOpportunityItem.getIntentTag());
        try {
            // 定义日期格式
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 将 Date 转换为格式化字符串
            String formattedDate = dateFormat.format(businessOpportunityItem.getCreateTime());
            result.put("成为商机的时间", formattedDate);
        }catch (Exception e){
            log.error("商机创建时间为空,busiOppoCode={}",busiOppoCode);
        }
        if (Objects.nonNull(businessOpportunityItem.getBudget())) {
            result.put("预算",businessOpportunityItem.getBudget()+"万元");
        }
        for (String orderStr : orderStrSet) {
            if (StringUtils.isBlank(orderStr)) {
                continue;
            }
            //业务类型: 推广渠道: yandex、baidu、douyin
            String[] split = orderStr.split(":");
            if (split.length == 2) {
                result.put(split[0], split[1]);
            }

        }

        return result;
    }
}
