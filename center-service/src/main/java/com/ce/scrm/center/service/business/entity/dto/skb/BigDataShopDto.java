package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * description:网店(电商数据)
 * @author: DD.Jiu
 * date: 2025/2/13.
 */
@Data
public class BigDataShopDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**统一信用代码*/
    @JSONField(serialize = false)
    private String uncid;
    /**总数量*/
    private int total_count;
    /**总页数*/
    private int total_page;
    /**当前页*/
    private int page;
    /**品牌信息列表*/
    @JSONField(name = "店铺列表")
    private List<ListShop> list;

    @NoArgsConstructor
    @Data
    public static class ListShop implements Serializable {
        private static final long serialVersionUID = 1L;
        /**网店店铺外链*/
        @JSONField(name = "网店店铺外链", serialize = false)
        private String shop_url;
        /**网店id*/
        @JSONField(name = "网店id", serialize = false)
        private String shop_id;
        /**网店名称*/
        @JSONField(name = "网店名称")
        private String shop_name;
        /**店铺创建时间*/
        @JSONField(name = "网店创建时间",format = "yyyy-MM-dd HH:mm:ss")
        private Date shop_create_date;
        /**上架平台*/
        @JSONField(name = "网店上架平台")
        private String platform_source;
        /**店铺的主营产品*/
        @JSONField(name = "店铺的主营产品", serialize = false)
        private String shop_product;
        /**店铺的主营品牌*/
        @JSONField(name = "店铺的主营品牌", serialize = false)
        private String shop_brand;
        /**店铺的评分*/
        @JSONField(name = "店铺的评分")
        private ShopRating shop_rating;
    }

    @NoArgsConstructor
    @Data
    public static class ShopRating implements Serializable {
        private static final long serialVersionUID = 1L;
        /**用户评分*/
        @JSONField(name = "用户评分", serialize = false)
        private String user_rating;
        /**物流时效*/
        @JSONField(name = "物流时效", serialize = false)
        private String logistics_rating;
        /**售后服务*/
        @JSONField(name = "售后服务", serialize = false)
        private String after_sale_rating;
        /**综合评分*/
        @JSONField(name = "店铺综合评分")
        private String syn_rating;
    }
}
