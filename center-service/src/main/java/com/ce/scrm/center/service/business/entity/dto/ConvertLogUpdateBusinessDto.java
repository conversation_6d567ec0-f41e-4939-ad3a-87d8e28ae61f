package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 流转日志更新参数
 * @author: JiuDD
 * date: 2024/7/11
 */
@Data
public class ConvertLogUpdateBusinessDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private String id;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 经理补充原因
     */
    private String jlReasonText;
    /**
     * 经理补充原因时间
     */
    private Date jlReleaseTime;
    /**
     * 经理姓名
     */
    private String jlName;
    /**
     * 总监id
     */
    private String zjId;
}