package com.ce.scrm.center.service.business.entity.view.segment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 专项线索追踪
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TrackClueSegmentBusinessView implements Serializable {

	/**
	 * 主键id
	 */
	private Integer id;

	/**
	 * 分群id
	 */
	private String segmentId;

	/**
	 * 分群名称
	 */
	private String segmentName;

	/**
	 * 下发总量
	 */
	private Long segmentCount;

	/**
	 * 分群下发日期
	 */
	private Date segmentBeginTime;

	/**
	 * 昨日拜访量
	 */
	private Long yesterdayVisitCount;

	/**
	 * 昨日签单量
	 */
	private Long yesterdaySignCount;

	/**
	 * 累计拜访
	 */
	private Long allVisitCount;

	/**
	 * 累计签单
	 */
	private Long allSignCount;

}
