/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.corp;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

public class AddStaffCmd extends BaseParam {

    /**
     * 角色id，默认为3(员工)
     */
    private int roleId = 3;
    private String name;
    private String phone;

    /**
     * 性别1男，2女
     */
    private int sex = 1;

    public AddStaffCmd(String openId, String name) {
        this.name = name;
        this.setOpenId(openId);
    }

    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> params = getBaseParamsMap();
        params.put("name", name);
        params.put("roleId", String.valueOf(roleId));
        if (StrUtil.isNotEmpty(phone)) {
            params.put("phone", phone);
        }
        params.put("sex", String.valueOf(sex));
        return params;
    }

    @Override
    public void validate() {
        if (StrUtil.isEmpty(getOpenId())) {
            throw new KnownException("openId 不能为空");
        }
        if (StrUtil.isEmpty(name)) {
            throw new KnownException("name 不能为空");
        }
    }

    public int getRoleId() {
        return roleId;
    }

    public void setRoleId(int roleId) {
        this.roleId = roleId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }
}
