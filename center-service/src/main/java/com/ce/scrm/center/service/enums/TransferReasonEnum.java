package com.ce.scrm.center.service.enums;

import lombok.Getter;

@Getter
public enum TransferReasonEnum {
    //已于客户沟通
    ALREADY_CONTACT_CUSTOMER(1, "已与客户沟通"),
    //已线下拜访
    ALREADY_OFFLINE_VISIT(2, "已线下拜访"),
    ;

    private final Integer code;
    private final String name;

    TransferReasonEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getBy(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransferReasonEnum Enum : values()) {
            if (code.equals(Enum.getCode())) {
                return Enum.getName();
            }
        }
        return null;
    }

}
