package com.ce.scrm.center.service.business.entity.view;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 区域商机
 * @Author: lijinpeng
 * @Date: 2024/12/26 15:10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaBusinessOpportunityBusinessView implements Serializable {

    /**
     * 商机表id
     */
    private String id;

    /**
     * 商机单号
     */
    private String busiOppoCode;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 联系人姓名
     */
    private String linkmanName;

    /**
     * 联系人职位
     */
    private String position;

    /**
     * 手机号
     */
    private String mobile;


    /**
     * 客户状态id
     */
    private String customerStateId;

    /**
     * 客户状态
     */
    private String customerState;

    /**
     * 客户需求
     */
    private String custRequirement;


	/**
	 * 末次释放时间
	 */
	private Date lastReleaseTime;

	/**
	 * 末次保护商务名称
	 */
	private String lastProtectSalerName;

	/**
	 * 末次保护分司名称
	 */
	private String lastProtectSubName;
}
