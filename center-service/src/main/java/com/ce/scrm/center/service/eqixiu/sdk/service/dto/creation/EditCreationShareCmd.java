/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.creation;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

/**
 * 修改作品分享信息
 */
public class EditCreationShareCmd extends BaseParam {

    /**
     * 作品id
     */
    private Long id;

    /**
     * 分享标题
     */
    private String title;

    /**
     * 分享封面
     */
    private String cover;

    /**
     * 分享描述
     */
    private String description;

    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> paramsMap = getBaseParamsMap();
        paramsMap.put("id", String.valueOf(id));
        if (StrUtil.isNotEmpty(title)) {
            paramsMap.put("title", title);
        }
        if (StrUtil.isNotEmpty(cover)) {
            paramsMap.put("cover", cover);
        }
        if (StrUtil.isNotEmpty(description)) {
            paramsMap.put("description", description);
        }
        return paramsMap;
    }

    @Override
    public void validate() {
        if (id == null) {
            throw new KnownException("id must not be null");
        }
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
