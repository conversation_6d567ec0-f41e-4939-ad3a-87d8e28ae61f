package com.ce.scrm.center.service.business.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SmaDictionaryItemView implements Serializable {

    private static final long serialVersionUID = 1;

    /** id */
    private String id;

    /** 字典ID */
    private String dictionaryId;

    /** 父ID */
    private String parentId;

    /** 版本号 */
    private Integer version;

    /** 编码 */
    private String code;

    /** 名称 */
    private String name;

    /** 排序字段 */
    private Integer displayOrder;

    /** 描述字段 */
    private String description;

    /** 状态 */
    private Integer state;

    private List<SmaDictionaryItemView> children;

}
