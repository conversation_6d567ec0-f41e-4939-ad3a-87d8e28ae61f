package com.ce.scrm.center.service.enums;

import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * 保护关系客户类型枚举
 * <AUTHOR>
 * @date 2024/5/21 下午8:22
 * @version 1.0.0
 **/
@Getter
public enum ProtectCustTypeEnum {

    /** 公海 */
    COMMON(-1, "公海"),
    /** 预保护库 */
    COLLECTION(1, "收藏夹"),
    /** 保护跟进的 */
    PROTECT_FOLLOW(2, "保护跟进"),
    /** 网站客户 */
    ORDERED(3, "网站客户"),
    /** 非网站客户 */
    SECOND_DEVELOPMENT(4, "非网站客户"),
    /** 释放 */
    RELEASE(5, "释放"),
    /** 经理分配的已合作客户 */
    MANAGER_ASSIGN(6, "分配的已合作客户"),
    /** 流失客户 */
    LOST(7, "流失客户"),
    /** 意向客户 */
    INTENT(10, "意向客户跟进");

    private final String lable;
    private final Integer value;

    ProtectCustTypeEnum(Integer value, String lable) {
        this.lable = lable;
        this.value = value;
    }
	public static ProtectCustTypeEnum of(Integer val) {
		return Stream.of(values())
			.filter(C -> C.getValue().equals(Objects.requireNonNull(val, "ProtectCustTypeEnum#of value not allow null")))
			.findAny()
			.orElse(null);
	}

}