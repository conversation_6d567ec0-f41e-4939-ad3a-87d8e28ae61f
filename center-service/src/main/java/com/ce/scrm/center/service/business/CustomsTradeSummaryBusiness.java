package com.ce.scrm.center.service.business;

import com.ce.scrm.center.dao.entity.CustomsTradeSummary;
import com.ce.scrm.center.dao.entity.view.CustomsTradeSummaryView;
import com.ce.scrm.center.dao.mapper.CustomsTradeSummaryMapper;
import com.ce.scrm.center.dao.service.CustomsTradeSummaryService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/20
 */
@Slf4j
@Service
public class CustomsTradeSummaryBusiness {

    @Autowired
    CustomsTradeSummaryService customsTradeSummaryService;

    @Autowired
    CustomsTradeSummaryMapper customsTradeSummaryMapper;

    /***
     * 根据产品code编码列表获取产品交易数据
     * @param productCodes
     * <AUTHOR>
     * @date 2025/5/20 10:29
     * @version 1.0.0
     * @return java.util.List<com.ce.scrm.center.dao.entity.CustomsTradeSummary>
    **/
    public List<CustomsTradeSummary> getCustomsTradeSummaryByCodes(List<String> productCodes) {
        if (CollectionUtils.isEmpty(productCodes)){
            return Lists.newArrayList();
        }
        return customsTradeSummaryService.lambdaQuery().in(CustomsTradeSummary::getProductCode, productCodes).list();
    }

    /***
     * productCode top 5
     * @param productCodes
     * <AUTHOR>
     * @date 2025/5/21 13:47
     * @version 1.0.0
     * @return java.util.List<com.ce.scrm.center.dao.entity.CustomsTradeSummary>
    **/
    public List<CustomsTradeSummaryView> selectTop5ByProductCodes(List<String> productCodes, List<Integer> yearnRange) {
        if (CollectionUtils.isEmpty(productCodes)){
            return Lists.newArrayList();
        }
        return customsTradeSummaryMapper.selectTop5ByProductCodes(productCodes,yearnRange);
    }
}
