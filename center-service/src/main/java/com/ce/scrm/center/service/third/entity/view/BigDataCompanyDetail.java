package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 大数据公司详情
 * <AUTHOR>
 * @date 2024/1/15 11:16
 * @version 1.0.0
 **/
@Data
public class BigDataCompanyDetail implements Serializable {
    /**
     * 核准日期
     */
    private String appr_date;
    /**
     * 城市名称
     */
    private String city;
    /**
     * 城市编码
     */
    private String city_code;
    /**
     * 地区
     */
    private String district;
    /**
     * 地区编码
     */
    private String district_code;
    /**
     * 企业状态:
     * 1、在营/存续
     * 2、迁入/迁出
     * 3、吊销/撤销
     * 4、注销
     * 5、停业
     * 6、其他
     */
    private String ent_status;
    /**
     * 企业(机构)类型
     */
    private String ent_type;
    /**
     * 企业名称
     */
    private String entname;
    /**
     * 成立日期(毫秒，如果为0表示数据为空)
     */
    private String establish_date;
    /**
     * 企业通讯地址
     */
    private String geo_address;
    /**
     * 企业历史名称
     */
    private List<String> hisotry_names;
    /**
     * 一级行业
     */
    private String industryL1_desc;
    /**
     * 二级行业
     */
    private List<String> industryL2_desc;
    /**
     * 法人、负责人
     */
    private String legal_person;
    /**
     * 营(驻在)期限至
     */
    private String op_end;
    /**
     * 经营(驻在)期限自
     */
    private String op_from;
    /**
     * 经营场所
     */
    private String op_location;
    /**
     * 经营(业务)范围
     */
    private String op_scope;
    /**
     * 企业唯一编码
     */
    private String pid;
    /**
     * 省份
     */
    private String province;
    /**
     * 省份代码
     */
    private String province_code;
    /**
     * 企业注册地址
     */
    private String reg_address;
    /**
     * 注册资金
     */
    private String reg_cap;
    /**
     * 注册资金单位
     */
    private String reg_cap_cur;
    /**
     * 注册省份代码
     */
    private String reg_province_code;
    /**
     * 注册地址市代码
     */
    private String reg_city_code;
    /**
     * 注册地址地区代码
     */
    private String reg_district_code;
    /**
     * 注册号
     */
    private String reg_no;
    /**
     * 登记机关
     */
    private String reg_org;
    /**
     * 注销日期/吊销日期
     */
    private String revoke_date;
    /**
     * 统一信用代码
     */
    private String uncid;
}