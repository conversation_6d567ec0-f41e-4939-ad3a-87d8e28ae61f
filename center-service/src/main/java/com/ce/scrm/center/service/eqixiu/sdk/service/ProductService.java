package com.ce.scrm.center.service.eqixiu.sdk.service;

import com.ce.scrm.center.service.eqixiu.sdk.common.ProductParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.Result;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Corp;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.product.*;
import com.ce.scrm.center.service.eqixiu.sdk.util.HttpClientUtils;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;
import com.ce.scrm.center.service.eqixiu.sdk.util.SHA256Util;

import java.util.List;
import java.util.Map;

/**
 * 平台服务接口
 * https://hc.eqxiu.cn/doc/6/
 *
 * <AUTHOR>
 */
public class ProductService extends ConnectService {

    private static final String API_PLATFORM_PV_REMAINING = "/api/v1/base/product/corp/pv/remaining";

    private static final String API_PLATFORM_CREATE_CORP = "/api/v1/base/corp/create";

    private static final String API_PLATFORM_GET_SECRET_ID = "/api/v1/base/secret/detail";

    private static final String API_PLATFORM_GET_ALL_CORP = "/api/v1/base/product/corpList";

    private static final String API_PLATFORM_PV_ADD = "/api/v1/base/product/corp/pv/add";

    public ProductService(Secret secret) {
        super(secret);
    }

    /**
     * 用于生成内容中台与平台对应的企业组织
     *
     * @param query
     * @return
     */

    public CorpSecret createCorp(CreateCorpCmd query) {
        paramValidate(query);
        JSONObject object = HttpClientUtils.httpPost(getApiURL(API_PLATFORM_CREATE_CORP), null, getParamMapWithSignature(query));
        printLog(object, "创建子企业失败 fail:{}");
        if (!object.getSuccess()) {
            return null;
        }
        Map<String, String> resultMap = object.getResultMap();
        return new CorpSecret(resultMap.get("corpId"), resultMap.get("secretId"), resultMap.get("secretKey"));
    }


    /**
     * 当密钥（授权凭证）丢失时可用该接口来获取
     *
     * @param query
     * @return
     */

    public CorpSecret getCorpSecretId(CorpSecretQuery query) {
        paramValidate(query);
        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_PLATFORM_GET_SECRET_ID), null, getParamMapWithSignature(query));
        printLog(object, "获取子企业密钥失败 fail:{}");
        Map<String, String> resultMap = object.getResultMap();
        return new CorpSecret(resultMap.get("corpId"), resultMap.get("secretId"), resultMap.get("secretKey"));
    }


    /**
     * 通过接口查询平台下所有的企业
     *
     * @param query
     * @return
     */

    public Result<Corp> findSubCorp(SubCorpQuery query) {
        paramValidate(query);
        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_PLATFORM_GET_ALL_CORP), null, getParamMapWithSignature(query));
        printLog(object, "获取子企业失败 fail:{}");
        return getResult(object, Corp.class);
    }


    /**
     * 平台为平台下创建的企业增加pv流量 仅限先结算型企业
     *
     * @param cmd
     * @return
     */

    public boolean addCorpPv(AddCorpPvCmd cmd) {
        paramValidate(cmd);
        JSONObject object = HttpClientUtils.httpPost(getApiURL(API_PLATFORM_PV_ADD), null, getParamMapWithSignature(cmd));
        printLog(object, "增加子企业pv失败 fail:{}");
        return object.getSuccess();
    }


    /**
     * 获取子企业剩余pv
     * 这个接口只限于先结算型企业
     *
     * @param query
     * @return 成功状态码200
     */
    public Integer getCorpPvRemaining(CorpPvQuery query) {
        paramValidate(query);
        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_PLATFORM_PV_REMAINING), null, getParamMapWithSignature(query));
        printLog(object, "获取子企业剩余pv失败 fail:{}");

        return object.getInt("obj");


    }

    private Map<String, String> getParamMapWithSignature(ProductParam param) {
        Map<String, String> paramMap = param.getParamsMap();
        List<String> paramList = param.getSignatureParams();
        paramList.add(getSecret().getSecretKey());

        String sign = SHA256Util.getSHA256Str(paramList);
        paramMap.put("signature", sign);
        return paramMap;
    }

    private void paramValidate(ProductParam param) {
        param.setAppId(getSecret().getAppId());
        param.validate();
    }

}
