/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.disbribute;


import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.domain.CreationDistribute;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 保存或修改分发信息
 */
public class SaveDistributeCmd extends BaseParam {

    /**
     * 作品唯一标识
     */
    private Long creationId;
    private List<CreationDistribute> distributes;

    public SaveDistributeCmd(Long creationId, String openId) {
        this.creationId = creationId;
        setOpenId(openId);
    }

    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> map = getBaseParamsMap();
        map.put("creationId", String.valueOf(creationId));
        return map;
    }

    @Override
    public void validate() {
        validateOpenId();
        if (creationId == null) {
            throw new KnownException("creationId 参数不能为空");
        }
        if (distributes == null || distributes.isEmpty()) {
            throw new KnownException("distributes 参数不能为空");
        }
        for (CreationDistribute distribute : distributes) {
            if (StrUtil.isEmpty(distribute.getName()) || distribute.getName().length() > 256) {
                throw new KnownException("name 参数不正确");
            }
            if (distribute.getCreationId() == null) {
                throw new KnownException("creationId 参数不能为空");
            }
            if (distribute.getRelType() != 1 && distribute.getRelType() != 2) {
                throw new KnownException("relType 参数不正确");
            }
            if (distribute.getRelType() == 2 && StrUtil.isEmpty(distribute.getRelId())) {
                throw new KnownException("relId 参数不能为空");
            }
        }
    }

    public List<CreationDistribute> getDistributes() {
        return distributes;
    }

    public void setDistributes(CreationDistribute distribute) {
        distributes = new ArrayList<>();
        distributes.add(distribute);
    }

    public void setDistributes(List<CreationDistribute> distributes) {
        this.distributes = distributes;
    }

    public Long getCreationId() {
        return creationId;
    }

    public void setCreationId(Long creationId) {
        this.creationId = creationId;
    }
}
