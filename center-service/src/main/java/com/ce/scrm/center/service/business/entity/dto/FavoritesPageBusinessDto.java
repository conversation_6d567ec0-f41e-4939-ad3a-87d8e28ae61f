package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * FavoritesPageBusinessDto
 *
 * <AUTHOR>
 * @since 2024/12/2 15:41
 */
@Data
public class FavoritesPageBusinessDto {

	/**
	 * 页码
	 */
	private Integer pageNum = 1;

	/**
	 * 每页数量
	 */
	private Integer pageSize = 10;

	/**
	 * 来源 0-收藏夹列表  1-待办任务-分配任务-收藏夹列表
	 */
	private Integer from = 0;

	/**
	 * 来源
	 */
	private String lablFrom;

	/**
	 * 运营行业编码
	 */
	private String industryZhongqi;

	/**
	 * 开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	private LocalDateTime endTime;


	/**
	 * 分司id
	 */
	private String subId;

	/**
	 * 事业部id
	 */
	private String buId;

	/**
	 * 部门id
	 */
	private String deptId;

	/**
	 * 商务id
	 */
	private String salerId;

	/**
	 * 商务操作类型
	 */
	private String bussOppType;

	/**
	 * 客户id
	 */
	private String custId;

	/**
	 * 客户名称
	 */
	private String custName;

	/**
	 * 地址
	 */
	private String address;

	/**
	 * 任务
	 */
	private String clueMission;

	/**
	 * 二级国标行业
	 */
	private String secondIndustry;

	/**
	 * 二级国标行业
	 */
	private String salerClueFrom;


	/**
	 * 二级获客来源
	 */
	private String salerClueFromSub;

	/**
	 * 跟进状态
	 */
	private String clueVisitStage;

	/**
	 * 下次跟进日期
	 */
	private String nextVisitDate;
}
