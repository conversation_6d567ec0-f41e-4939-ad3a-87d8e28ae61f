package com.ce.scrm.center.service.eqixiu.sdk.domain;

/**
 * 易企秀接口调用密钥
 *
 * <AUTHOR>
 */
public class Secret {

    /**
     * 用于请求易企秀接口的SecretId
     */
    private final String secretId;

    /**
     * 用于请求易企秀接口的SecretKey
     */
    private final String secretKey;

    public Secret(String secretId, String secretKey) {
        this.secretId = secretId;
        this.secretKey = secretKey;
    }

    public String getSecretId() {
        return secretId;
    }

    public String getSecretKey() {
        return secretKey;
    }

    /**
     * 平台密钥为平台ID
     *
     * @return
     */
    public Long getAppId() {
        return Long.valueOf(secretId);
    }
}
