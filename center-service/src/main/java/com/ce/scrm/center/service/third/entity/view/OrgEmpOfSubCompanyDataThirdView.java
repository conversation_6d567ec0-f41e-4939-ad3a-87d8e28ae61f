package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Description: 分司的部门及员工数据
 * @author: JiuDD
 * date: 2024/7/22
 */
@Data
@Accessors(chain = true)
public class OrgEmpOfSubCompanyDataThirdView {
    /**
     * 机构ID
     */
    private String deptId;
    /**
     * 机构名称
     */
    private String deptName;
    /**
     * 员工
     */
    private List<DeptEmpData> deptEmp;



    /**
     * Description: 员工
     * @author: JiuDD
     * date: 2024/7/22
     */
    @Data
    @Accessors(chain = true)
    public static class DeptEmpData {
        /**
         * 员工ID
         */
        private String salerId;
        /**
         * 员工姓名
         */
        private String salerName;

    }
}