/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.share;


import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.domain.CreationShare;

import java.util.List;
import java.util.Map;

public class CreationShareCmd extends BaseParam {

    private Long creationId;

    private List<CreationShare> shareList;


    public CreationShareCmd(Long creationId, String openId) {
        this.creationId = creationId;
        setOpenId(openId);
    }

    @Override
    public Map<String, String> getParamsMap() {
        return getBaseParamsMap();
    }

    @Override
    public void validate() {
        validateOpenId();
        if (creationId == null) {
            throw new KnownException("creationId 不能为空");
        }
        if (shareList == null || shareList.isEmpty()) {
            throw new KnownException("共享信息不能为空");
        }
    }

    public Long getCreationId() {
        return creationId;
    }

    public void setCreationId(Long creationId) {
        this.creationId = creationId;
    }

    public List<CreationShare> getShareList() {
        return shareList;
    }

    public CreationShareCmd setShareList(List<CreationShare> shareList) {
        this.shareList = shareList;
        return this;
    }

    public CreationShareCmd addShare(CreationShare share) {
        if (shareList == null) {
            shareList = java.util.Collections.singletonList(share);
        } else {
            shareList.add(share);
        }
        return this;
    }
}
