package com.ce.scrm.center.service.third.entity.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerContactPersonThirdDto implements Serializable {

	/**
	 * 客户id
	 */
	private String customerId;

	/**
	 * 联系人姓名
	 */
	private String contactPersonName;

	/**
	 * 手机号(多个手机号，逗号分隔)
	 */
	private String phone;

	/**
	 * 操作人id
	 */
	private String operator;

	/**
	 * 主联系人手机号（必填）
	 */
	private String phoneNumber;

	/**
	 * 其他手机号 (多个 手机号, 用英文分号隔开)
	 */
	private String otherPhoneNumbers;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 其他邮箱 (多个邮箱, 用英文分号隔开)
	 */
	private String otherEmails;

}
