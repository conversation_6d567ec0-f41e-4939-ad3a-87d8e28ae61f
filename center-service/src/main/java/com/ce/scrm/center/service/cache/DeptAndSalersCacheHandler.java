package com.ce.scrm.center.service.cache;

import com.ce.scrm.center.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.center.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.center.service.business.entity.view.DeptAndSalersCacheListView;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * description: 分司的部门及员工数据
 * @author: DD.Jiu
 * date: 2024/7/22.
 */
@Slf4j
@Component
public class DeptAndSalersCacheHandler extends AbstractStringCacheHandler<DeptAndSalersCacheListView> {
    @Resource
    private OrgThirdService orgThirdService;


    /**
     * 获取业务缓存的key
     *
     * @return com.ce.scrm.center.cache.enumeration.CacheKeyEnum
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.SMA_DEPT_SALERS_CACHE;
    }

    /**
     * 指定对象类型
     *
     * @return java.lang.Class<T>
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    @Override
    protected Class<DeptAndSalersCacheListView> getClazz() {
        return DeptAndSalersCacheListView.class;
    }

    /**
     * 从数据源查询数据
     *
     * @param subId 分司id
     * @return R
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    @Override
    protected DeptAndSalersCacheListView queryDataBySource(String subId) {
        return new DeptAndSalersCacheListView().setDeptAndSalers(orgThirdService.getDeptAndSalers(subId));
    }
}
