package com.ce.scrm.center.service.cache;

import com.ce.scrm.center.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.center.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.center.service.business.SmaDictionaryItemBusiness;
import com.ce.scrm.center.service.business.entity.view.SmaDictionaryItemView;
import com.ce.scrm.center.service.business.entity.view.SmaDictionaryItemListView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * description: 字典缓存
 * @author: DD.Jiu
 * date: 2024/7/12.
 */
@Slf4j
@Component
public class SmaDictionaryCacheHandler extends AbstractStringCacheHandler<SmaDictionaryItemListView> {
    @Resource
    private SmaDictionaryItemBusiness smaDictionaryItemBusiness;

    /**
     * 获取业务缓存的key
     * @return com.ce.scrm.center.cache.enumeration.CacheKeyEnum
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.SMA_DICTIONARY_CACHE;
    }

    /**
     * 指定对象类型
     * @return java.lang.Class<T>
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    @Override
    protected Class<SmaDictionaryItemListView> getClazz() {
        return SmaDictionaryItemListView.class;
    }

    /**
     * 从数据源查询数据
     * @param dictionaryId 字典id
     * @return R
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    @Override
    protected SmaDictionaryItemListView queryDataBySource(String dictionaryId) {
        Optional<List<SmaDictionaryItemView>> dictionaryItemValue = smaDictionaryItemBusiness.findByDictionaryId(dictionaryId);
        if (dictionaryItemValue.isPresent()) {
            SmaDictionaryItemListView listThirdView = new SmaDictionaryItemListView();
            listThirdView.setList(dictionaryItemValue.get());
            return listThirdView;
        }
        return null;
    }

}
