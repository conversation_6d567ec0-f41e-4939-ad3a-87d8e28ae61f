package com.ce.scrm.center.service.business.abm;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.SmaRoleRelation;
import com.ce.scrm.center.dao.entity.Transfertable;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.SmaRoleRelationService;
import com.ce.scrm.center.dao.service.TransfertableService;
import com.ce.scrm.center.service.business.ProtectBusiness;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.business.entity.dto.abm.TransferDto;
import com.ce.scrm.center.service.business.entity.dto.abm.TransferWebDto;
import com.ce.scrm.center.service.business.entity.dto.protect.ProtectAllotBusinessDto;
import com.ce.scrm.center.service.business.entity.view.TransferWebDtoView;
import com.ce.scrm.center.service.enums.TransferProcessingStatusEnum;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TransferBusiness {
    @Resource
    private TransfertableService transfertableService;
    @Resource
    private SendWxMessage sendWxMessage;
    @Resource
    private SmaRoleRelationService smaRoleRelationService;
    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private ProtectBusiness protectBusiness;

    /**
     * @param applicationTransferDto
     * @return
     * <AUTHOR>
     * 插入申请调度信息
     */
    public Boolean insertApplicationTransfer(TransferDto applicationTransferDto) {
        //插入申请调度信息
        Transfertable transfertable = new Transfertable();
        transfertable.setCustomerId(applicationTransferDto.getCustomerId());
        transfertable.setCustomerName(applicationTransferDto.getCustomerName());
        transfertable.setTransferReason(applicationTransferDto.getTransferReason());
        transfertable.setProofInfoUrl(applicationTransferDto.getProofInfoUrl());
        transfertable.setApplicationTime(new Date());
        transfertable.setApplicantId(applicationTransferDto.getLoginEmployeeId());
        Optional<EmployeeDataThirdView> employeeData = employeeThirdService.getEmployeeData(applicationTransferDto.getLoginEmployeeId());
        if (!employeeData.isPresent()) {
            log.warn("获取员工信息为空，员工ID为：{}", applicationTransferDto.getLoginEmployeeId());
            return false;
        }
        String applicant = "";
        if (StringUtils.isNotBlank(employeeData.get().getAreaName())) {
            applicant += employeeData.get().getAreaName() + "/";
        }
        if (StringUtils.isNotBlank(employeeData.get().getSubName())) {
            applicant += employeeData.get().getSubName() + "/";
        }
        if (StringUtils.isNotBlank(employeeData.get().getOrgName())) {
            applicant += employeeData.get().getOrgName() + "/";
        }
        if (StringUtils.isNotBlank(employeeData.get().getBuName())) {
            applicant += employeeData.get().getBuName() + "/";
        }
        applicant += employeeData.get().getName();

        transfertable.setApplicant(applicant);
        transfertable.setProcessingStatus(TransferProcessingStatusEnum.UNPROCESSED.getCode());
        boolean save = transfertableService.save(transfertable);
        try {
            //申请调度发送企微通知
            String msg = applicant + ",申请调拨" + transfertable.getCustomerName() + "客户,请处理";
            //SDR主管的roleId
            String roleId = "caf151a2-7dac-401f-bbd8-8112186c93b9";
            List<String> relationIds = Optional.ofNullable(smaRoleRelationService.lambdaQuery().select(SmaRoleRelation::getRelationId).eq(SmaRoleRelation::getRoleId, roleId).list())
                    .orElse(Collections.emptyList()).stream().map(SmaRoleRelation::getRelationId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(relationIds)) {
                log.error("SDR主管调拨处理，未找到对应角色信息");
                // 如果未找到sdr主管，默认发给 范博义 107213
                sendWxMessage.sendMessage("107213", "你有调拨申请需要处理");
            } else {
                Map<String, EmployeeInfoThirdDto> employeeDataMap = employeeThirdService.getEmployeeDataMap(relationIds);
                if (CollectionUtils.isEmpty(employeeDataMap)) {
                    log.error("SDR主管调拨处理，未找到对应员工信息, relationIds={}", relationIds);
                } else {
                    //发送信息给SDR调拨处理角色的人员，msg：区域/分司/部门/组/商务A，申请调拨$客户名称$客户，请处理
                    List<String> idList = employeeDataMap.values().stream().map(EmployeeInfoThirdDto::getId).collect(Collectors.toList());
                    idList.forEach(id -> {
                        try {
//                            sendWxMessage.sendMessage("105590", msg);
                            sendWxMessage.sendMessage(id, msg);
                        } catch (Exception e) {
                            log.error("发送信息给SDR调拨处理角色的人员失败，msg：{}，userId：{}", msg, id, e);
                        }
                    });
                    log.info("发送信息给SDR调拨处理角色的人员完成，msg：{}", msg);
                }
            }
        } catch (Exception e) {
            log.error("发送信息给SDR调拨处理角色的人员失败,参数={}", JSON.toJSONString(applicationTransferDto), e);
        }
        return save;

    }


    public Page<TransferWebDtoView> page(TransferWebDto thirdQueryWebDto) {
        //分页
        Page<Transfertable> page = new Page<>(thirdQueryWebDto.getPageNum(), thirdQueryWebDto.getPageSize());

        LambdaQueryWrapper<Transfertable> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(thirdQueryWebDto.getCustomerName())) {
            queryWrapper.like(Transfertable::getCustomerName, thirdQueryWebDto.getCustomerName());
        }
        if (thirdQueryWebDto.getProcessingStatus() != null) {
            queryWrapper.eq(Transfertable::getProcessingStatus, thirdQueryWebDto.getProcessingStatus());
        }
        if (thirdQueryWebDto.getStartTime() != null) {
            queryWrapper.ge(Transfertable::getApplicationTime, thirdQueryWebDto.getStartTime());
        }
        if (thirdQueryWebDto.getEndTime() != null) {
            queryWrapper.le(Transfertable::getApplicationTime, thirdQueryWebDto.getEndTime());
        }
        queryWrapper.orderByDesc(Transfertable::getApplicationTime);

        IPage<Transfertable> result = transfertableService.page(page, queryWrapper);
        Page<TransferWebDtoView> pageResult = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        pageResult.setRecords(result.getRecords().stream().map(record -> {
            return BeanCopyUtils.convertToVo(record, TransferWebDtoView.class);
        }).collect(Collectors.toList()));
        return pageResult;
    }


    public TransferWebDtoView detail(Long id) {
        Transfertable transfertable = transfertableService.getById(id);
        return BeanCopyUtils.convertToVo(transfertable, TransferWebDtoView.class);
    }

    /**
     * 处理申请调度信息
     *
     * @param id
     * @param remarks
     * @param processingResult
     * @return
     */
    public Boolean deal(Long id, String remarks, Integer processingResult, String loginEmployeeName) {
        Transfertable byId = transfertableService.getById(id);
        Transfertable transfertable = new Transfertable();
        transfertable.setId(id);
        transfertable.setRemarks(remarks);
        transfertable.setProcessingTime(new Date());
        transfertable.setProcessor(loginEmployeeName);
        transfertable.setProcessingStatus(processingResult);
        boolean b = transfertableService.updateById(transfertable);
        if (b) {
            try {
                String msg = null;
                if (TransferProcessingStatusEnum.PASS.getCode().equals(processingResult)) {
                    //通过处理，发送信息:您调拨$客户名称$客户的申请已通过，原因：XXXXXXXXXXXXXXX
                    msg = "您调拨" + byId.getCustomerName() + "客户的申请已通过,原因：" + remarks;
                } else {
                    //驳回处理，发送信息:您调拨$客户名称$客户的申请已驳回，原因：XXXXXXXXXXXXXXX
                    msg = "您调拨" + byId.getCustomerName() + "客户的申请已驳回,原因：" + remarks;
                }
                log.info("发送申请处理信息给商务{}成功，msg：{}", byId.getApplicantId(), msg);
//                sendWxMessage.sendMessage("105590", msg);
                sendWxMessage.sendMessage(byId.getApplicantId(), msg);
            } catch (Exception e) {
                log.error("处理调拨发送消息失败,参数={}", JSON.toJSONString(byId), e);
            }
            //处理调拨之后的保护关系  SDR->普通商务
            if (transfertable.getProcessingStatus().equals(TransferProcessingStatusEnum.PASS.getCode()) ) {
                CustProtectView custProtectView = cmCustProtectService.selectOneByCondition(CmCustProtect.builder().custId(byId.getCustomerId()).build());
                if (Objects.nonNull(custProtectView)) {
                    ProtectAllotBusinessDto protectAllotBusinessDto = new ProtectAllotBusinessDto();
                    protectAllotBusinessDto.setCustomerId(byId.getCustomerId());
                    protectAllotBusinessDto.setOldSalerId(custProtectView.getSalerId());
                    protectAllotBusinessDto.setNewSalerId(byId.getApplicantId());
                    Boolean allot = protectBusiness.allot(protectAllotBusinessDto);
                    if (!allot) {
                        log.error("处理调拨之后的保护关系失败,参数={}", JSON.toJSONString(protectAllotBusinessDto));
                    } else {
                        log.info("处理调拨之后的保护关系成功,参数={}", JSON.toJSONString(protectAllotBusinessDto));
                    }
                } else {
                    log.error("调拨处理后查询客户的保护关系失败，客户id={}", byId.getCustomerId());
                }
            }
        }


        return b;
    }

}
