package com.ce.scrm.center.service.eqixiu.sdk.service;


import com.ce.scrm.center.service.eqixiu.sdk.domain.IntegratedConfig;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.SaveConfigCmd;
import com.ce.scrm.center.service.eqixiu.sdk.support.HttpClient;
import com.ce.scrm.center.service.eqixiu.sdk.support.TokenCache;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;

import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 集成配置服务类
 * <a href="https://hc.eqxiu.cn/doc/32/?highlight=/api/v1/base/param/params">...</a>
 *
 * <AUTHOR>
 */
public class IntegrateService extends ConnectService {

    private static final String API_INTEGRATED_CONFIG_ADD = "/api/v1/base/open/app/config/save";

    private static final String API_INTEGRATED_CONFIG_DELETE = "/api/v1/base/open/app/config/delete";

    private static final String API_INTEGRATED_CONFIG_LIST = "/api/v1/base/param/params";


    public IntegrateService(Secret secret) {
        super(secret);
    }

    public IntegrateService(Secret secret, TokenCache tokenCache) {
        super(secret, tokenCache);
    }

    public IntegrateService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        super(secret, tokenCache, httpClient);
    }

    /**
     * 新增或修改集成配置
     *
     * @param cmd
     * @return
     */
    public boolean saveConfig(SaveConfigCmd cmd) {
        paramValidate(cmd);
        JSONObject result = httpClient.httpPost(getApiURL(API_INTEGRATED_CONFIG_ADD, cmd.getParamsMap()), null, cmd.getParamsMap());
        printLog(result, "保存配置失败:{}");
        return result.getSuccess();

    }


    /**
     * 查询配置列表
     *
     * @param key 集成管理业务key 1002:自主用户系统 1006:互动-自定义规则 1012：互动-自定义抽奖次数 1004: 页面集成，等
     * @return
     */
    public List<IntegratedConfig> listConfigs(String key) {
        if (key == null) {
            logger.error("key 参数不能为空");
            return Collections.emptyList();
        }
        Map<String, String> map = createParamMapWithToken();
        map.put("key", key);
        JSONObject result = httpClient.httpGet(getApiURL(API_INTEGRATED_CONFIG_LIST), null, map);
        printLog(result, "查询配置列表失败:{}");
        return result.getList(IntegratedConfig.class);


    }

    /**
     * 删除配置
     *
     * @param productId
     * @param cfgId
     * @return
     */
    public boolean deleteConfig(String productId, Long cfgId) {
        if (productId == null || cfgId == null) {
            logger.error("productId or cfgId is empty");
            return false;
        }
        Map<String, String> map = createParamMapWithToken();
        map.put("productId", productId);
        map.put("cfgId", String.valueOf(cfgId));
        JSONObject result = httpClient.httpPost(getApiURL(API_INTEGRATED_CONFIG_DELETE, map), null, map);
        printLog(result, "删除配置失败:{}");
        return result.getSuccess();
    }

}
