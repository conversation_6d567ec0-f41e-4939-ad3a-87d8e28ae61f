package com.ce.scrm.center.service.eqixiu.sdk.domain;

import com.ce.scrm.center.service.eqixiu.sdk.domain.type.DistributeType;

/**
 * 作品分发
 *
 * <AUTHOR>
 */
public class CreationDistribute {

    // "主键id")
    private String id;
    // "关联类型：1.渠道 2.成员 3.外部成员（暂时没有）")
    private DistributeType relType;
    // "名称")
    private String name;
    // "部门名称")

    // value = "关联id")
    private String relId;
    // value = "员工code")

    private Long creationId;

    /**
     * 创建渠道分发
     *
     * @param creationId
     * @param name
     * @return
     */
    public static CreationDistribute createChannelDistribute(Long creationId, String name) {
        CreationDistribute creationDistribute = new CreationDistribute();
        creationDistribute.setName(name);
        creationDistribute.setCreationId(creationId);
        creationDistribute.setRelType(DistributeType.CHANNEL);
        return creationDistribute;
    }

    /**
     * 创建员工分发
     * 注意，这里的参数是 易企秀员工 ID，不是员工 openId
     *
     * @param creationId
     * @param name
     * @param staffId
     * @return
     */
    public static CreationDistribute createStaffDistribute(Long creationId, String name, String staffId) {
        CreationDistribute creationDistribute = new CreationDistribute();
        creationDistribute.setName(name);
        creationDistribute.setCreationId(creationId);
        creationDistribute.setRelType(DistributeType.STAFF);
        creationDistribute.setRelId(staffId);
        return creationDistribute;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getRelType() {
        return relType.getValue();
    }

    public void setRelType(DistributeType relType) {
        this.relType = relType;
    }

    public void setRelType(int relType) {
        this.relType = DistributeType.of(relType);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRelId() {
        return relId;
    }

    public void setRelId(String relId) {
        this.relId = relId;
    }

    public Long getCreationId() {
        return creationId;
    }

    public void setCreationId(Long creationId) {
        this.creationId = creationId;
    }

    @Override
    public String toString() {
        return "{" +
                "id='" + id + '\'' +
                ", relType=" + relType.getValue() +
                ", name='" + name + '\'' +
                ", relId='" + relId + '\'' +
                ", creationId=" + creationId +
                '}';
    }
}
