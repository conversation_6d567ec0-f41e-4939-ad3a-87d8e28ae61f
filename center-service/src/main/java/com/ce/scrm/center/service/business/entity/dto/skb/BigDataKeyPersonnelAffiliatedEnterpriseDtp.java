package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import com.ce.scrm.extend.dubbo.entity.view.BigDataAffiliatedEnterpriseDubboView;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 主要人员关联企业
 * @Author: lijinpeng
 * @Date: 2025/2/14 15:13
 */
@Data
public class BigDataKeyPersonnelAffiliatedEnterpriseDtp implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 关联企业数量
     */
    @JSONField(serialize = false)
    private Integer total;

    /**
     * 关联企业详情列表
     */
    @JSONField(name = "关联企业详情列表",serialize = false)
    private List<CustomerAffiliatedEnterpriseDetailWebView> items;

    /**
     * 关联企业详情列表
     */
    @JSONField(name = "关联企业信息列表")
    private String item;


    /**
     * 详情信息
     */
    @Data
    public static class CustomerAffiliatedEnterpriseDetailWebView implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 企业唯一编码
         */
        @JSONField(serialize = false)
        private String pid;

        /**
         * 企业名称
         */
        @JSONField(name = "关联企业的名称")
        private String entName;

        /**
         * 关联企业的企业标签
         */
        @JSONField(name = "关联企业的标签")
        private String entTagName;


        /**
         * 职务/职位
         */
        @JSONField(serialize = false)
        private String position;

        /**
         * 任职状态
         */
        @JSONField(serialize = false)
        private String positionStatus;

        /**
         * 注册资本
         */
        @JSONField(serialize = false)
        private String  regCapital;


        /**
         * 注册地区
         */
        @JSONField(serialize = false)
        private String address;

        /**
         * 持股比例
         */
        @JSONField(serialize = false)
        private String shareholdingRatio;
    }

}
