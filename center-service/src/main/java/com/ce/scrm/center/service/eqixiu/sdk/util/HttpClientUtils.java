package com.ce.scrm.center.service.eqixiu.sdk.util;

import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * HttpClient工具类封装
 *
 * <AUTHOR>
 */
public class HttpClientUtils {

    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtils.class);

    /**
     * 发送post请求
     *
     * @param url
     * @param header
     * @param params
     * @return
     */
    public static JSONObject httpPost(String url, Map<String, String> header, Map<String, String> params) {
        logger.info("httpPost请求参数, url:{},header:{},params:{}", url, header, params);
        HttpPost httpPost = null;
        try {
            httpPost = getHttpPost(url, header, params);
            return sendRequest(httpPost);
        } catch (IOException e) {
            logger.error("httpPost请求提交失败, url:{}, msg:{}", url, e.getMessage());
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
        return JSONObject.empty();
    }

    /**
     * 发送post请求，适用于 Body 传递对象参数
     *
     * @param url
     * @param param
     * @return
     */
    public static JSONObject httpPost(String url, Map<String, String> header, Object param) {
        logger.debug("httpPost请求参数, url:{},params:{}", url, param);
        HttpPost httpPost = null;
        try {
            httpPost = getHttpPost(url, header, param);
            return sendRequest(httpPost);
        } catch (IOException e) {
            logger.error("httpPost请求提交失败, url:{}, msg:{}", url, e.getMessage());
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
        return JSONObject.empty();
    }

    /**
     * 发送get请求
     *
     * @param url
     * @param header
     * @param params
     * @return
     */
    public static JSONObject httpGet(String url, Map<String, String> header, Map<String, String> params) {
        logger.debug("httpGet请求参数, url:{},header:{},params:{}", url, header, params);
        HttpGet httpGet = null;
        try {
            httpGet = getHttpGet(url, header, params);
            return sendRequest(httpGet);
        } catch (Exception e) {
            logger.error("httpGet 请求提交失败, url:{} msg:{}", url, e.getMessage());
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
        }
        return JSONObject.empty();
    }

    /**
     * 根据Content-Type，判断是否使用json格式传参
     *
     * @param header
     * @return
     */
    private static boolean isJsonContent(Map<String, String> header) {
        if (header == null) {
            return false;
        }
        String contentType = header.get("Content-Type");
        if (StrUtil.isEmpty(contentType)) {
            return false;
        }
        return contentType.contains("json");
    }

    /**
     * 根据参数组装HttpPost请求
     *
     * @param url
     * @param header
     * @param params
     * @return
     * @throws UnsupportedEncodingException
     */
    private static HttpPost getHttpPost(String url, Map<String, String> header, Map<String, String> params) throws UnsupportedEncodingException {
        HttpPost httpPost = new HttpPost(url);
        setRequestHeader(httpPost, header);
        setRequestConfig(httpPost);

        if (params != null && !params.isEmpty()) {
            if (isJsonContent(header)) {
                StringEntity entity = new StringEntity(JsonMapper.toJSON(params), "UTF-8");
                httpPost.setEntity(entity);
            } else {
                List<BasicNameValuePair> formParams = new ArrayList<>();
                for (Map.Entry<String, String> p : params.entrySet()) {
                    formParams.add(new BasicNameValuePair(p.getKey(), p.getValue()));
                }
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(formParams, "UTF-8");
                httpPost.setEntity(entity);
            }
        }

        return httpPost;
    }

    private static HttpPost getHttpPost(String url, Map<String, String> header, Object param) {
        HttpPost httpPost = new HttpPost(url);
        header.put("Content-Type", "application/json");
        setRequestHeader(httpPost, header);
        setRequestConfig(httpPost);

        if (param != null) {
            StringEntity entity = new StringEntity(JsonMapper.toJSON(param), "UTF-8");
            httpPost.setEntity(entity);
        }

        return httpPost;
    }


    /**
     * 根据参数组装HttpGet请求
     *
     * @param url
     * @param header
     * @param params
     * @return
     * @throws URISyntaxException
     */
    private static HttpGet getHttpGet(String url, Map<String, String> header, Map<String, String> params) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder(url);
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                uriBuilder.addParameter(entry.getKey(), entry.getValue());
            }
        }
        HttpGet httpGet = new HttpGet(uriBuilder.build());
        setRequestHeader(httpGet, header);
        setRequestConfig(httpGet);

        return httpGet;
    }

    /**
     * 发送请求
     *
     * @param request
     * @return
     * @throws IOException
     */
    private static JSONObject sendRequest(HttpUriRequest request) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            CloseableHttpResponse response = httpClient.execute(request);
            //请求发送成功，并得到响应
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                //读取服务器返回过来的json字符串数据
                String str = EntityUtils.toString(response.getEntity(), "utf-8");
                logger.info("requestId:{}", response.getFirstHeader("X-Request-Id"));
                //把json字符串转换成json对象
                return JsonMapper.parse(str, JSONObject.class);
            }
            logger.error("请求失败：requestId:{},status:{},conetnt:{}", response.getFirstHeader("X-Request-Id"), response.getStatusLine(), response.getEntity());
            return new JSONObject("error", response.getEntity());
        }
    }

    /**
     * 设置请求头
     *
     * @param header
     * @param request
     */
    private static void setRequestHeader(HttpUriRequest request, Map<String, String> header) {
        if (null != header && !header.isEmpty()) {
            for (Map.Entry<String, String> entry : header.entrySet()) {
                request.setHeader(entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * 设置请求超时时间
     *
     * @param request
     */
    private static void setRequestConfig(HttpRequestBase request) {
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(5000).setConnectTimeout(5000).build();
        request.setConfig(requestConfig);
    }
}
