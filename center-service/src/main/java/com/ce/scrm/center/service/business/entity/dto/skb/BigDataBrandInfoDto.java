package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import com.ce.scrm.extend.dubbo.entity.view.BigDataBrandInfoDubboView;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * description: 品牌信息
 * @author: DD.Jiu
 * date: 2025/2/13.
 */
@Data
public class BigDataBrandInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**总数量*/
    private int total_count;
    /**总页数*/
    private int total_page;
    /**当前页*/
    private int page;
    /**品牌信息列表*/
    @JSONField(name = "品牌信息列表")
    private List<ListBrandInfo> list;

    @Data
    public static class ListBrandInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        /**品牌名字*/
        @JSONField(name = "品牌名字")
        private String brand_name;
        /**品牌成立时间*/
        @JSONField(name = "品牌成立时间",serialize = false)
        private String es_date;
        /**品牌所属地*/
        @JSONField(name = "品牌所属地",serialize = false)
        private String brand_from;
        /**品牌类型*/
        @JSONField(name = "品牌类型")
        private List<String> brand_type;
        /**品牌介绍*/
        @JSONField(name = "品牌介绍", serialize = false)
        private String brand_info;
        /**品牌产品*/
        @JSONField(name = "品牌产品", serialize = false)
        private String brand_product;
    }
}
