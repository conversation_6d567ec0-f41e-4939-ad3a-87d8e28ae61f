package com.ce.scrm.center.service.business.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 流转日志
 * @author: JiuDD
 * date: 2024/7/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConvertLogBusinessDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_UUID)
    /**
     * id
     */
    private String id;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 原商务代表id
     */
    private String salerId;

    /**
     * 原商务代表部门
     */
    private String deptOfSalerId;

    /**
     * 创建人id
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 转换类型
     */
    private Integer convertType;

    /**
     * 发布原因
     */
    private String releaseReason;

    /**
     * 当前商务代表所属公司
     */
    private String subcompanyOfCurSalerId;

    /**
     * 当前商务代表所属区域
     */
    private String areaOfCurSalerId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 当前商务代表id
     */
    private String curSalerId;

    /**
     * 当前商务代表所属部门
     */
    private String deptOfCurSalerId;

    /**
     * 原客户类型
     */
    private Integer custType;

    /**
     * 原商务代表分公司
     */
    private String subcompanyOfSalerId;

    /**
     *  原商务代表区域
     */
    private String areaOfSalerId;

    /**
     * 目标
     */
    private Integer dest;

    /**
     * 企业id
     */
    private String entId;

    /**
     * 意向级别
     */
    private Integer intentionType;

    /**
     * 商机来源 sj:商机 bj:报价客户 zjs:转介绍客户。如果是多个来源，则用,分割。如：sj,bj,zjs
     */
    private Integer tagOpportunityOrigin;

    /**
     * 商务释放理由
     */
    private String swReasonText;

    /**
     * 经理补充理由
     */
    private String jlReasonText;

    /**
     * 经理补充时间
     */
    private Date jlReleaseTime;

    /**
     * 当前商务代表事业部id
     */
    private String buOfCurSalerId;

    /**
     * 原商务代表事业部id
     */
    private String buOfSalerId;

}