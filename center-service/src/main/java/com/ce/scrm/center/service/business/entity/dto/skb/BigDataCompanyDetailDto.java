package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * description: 大数据企业基本信息
 * @author: DD.Jiu
 * date: 2025/2/13.
 */
@Data
public class BigDataCompanyDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 核准日期
     */
    @JSONField(name = "核准日期", serialize = false)
    private String appr_date;
    /**
     * 城市名称
     */
    @JSONField(name = "通讯地址城市",serialize = false)
    private String city;
    /**
     * 城市编码
     */
    @JSONField(name = "城市编码", serialize = false)
    private String city_code;
    /**
     * 地区
     */
    @JSONField(name = "通讯地址地区",serialize = false)
    private String district;
    /**
     * 地区编码
     */
    @JSONField(name = "地区编码", serialize = false)
    private String district_code;
    /**
     * 企业状态:
     * 1、在营/存续
     * 2、迁入/迁出
     * 3、吊销/撤销
     * 4、注销
     * 5、停业
     * 6、其他
     */
    @JSONField(name = "公司经营状态")
    private String ent_status;
    /**
     * 企业(机构)类型
     */
    @JSONField(name = "企业类型")
    private String ent_type;
    /**
     * 企业名称
     */
    @JSONField(name = "企业名称")
    private String entname;
    /**
     * 成立日期(毫秒，如果为0表示数据为空)
     */
    @JSONField(name = "成立日期")
    private String establish_date;

    public String getEstablish_date() {
        if (Objects.equals("0", establish_date) || Objects.equals("", establish_date) || Objects.isNull(establish_date)) {
            return null;
        }
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.parseLong(establish_date)));
        } catch (NumberFormatException e) {
            return establish_date;
        }
    }

    /**
     * 企业通讯地址
     */
    @JSONField(name = "企业通讯地址", serialize = false)
    private String geo_address;
    /**
     * 企业历史名称
     */
    @JSONField(name = "企业历史名称", serialize = false)
    private List<String> hisotry_names;
    /**
     * 一级行业
     */
    @JSONField(name = "一级行业")
    private String industryL1_desc;
    /**
     * 二级行业
     */
    @JSONField(name = "二级行业")
    private List<String> industryL2_desc;
    /**
     * 法人、负责人
     */
    // 特殊情况 后面要用这个字段查询
    @JSONField(name = "法人、负责人",serialize = false)
    private String legal_person;
    /**
     * 经营期限至
     */
    @JSONField(name = "经营期限至", serialize = false)
    private String op_end;
    /**
     * 经营期限自
     */
    @JSONField(name = "经营期限自", serialize = false)
    private String op_from;
    /**
     * 经营场所
     */
    @JSONField(name = "经营场所", serialize = false)
    private String op_location;
    /**
     * 经营(业务)范围
     */
    @JSONField(name = "经营(业务)范围",serialize = false)
    private String op_scope;
    /**
     * 企业唯一编码
     */
    @JSONField(name = "企业唯一编码", serialize = false)
    private String pid;
    /**
     * 省份
     */
    @JSONField(name = "省份", serialize = false)
    private String province;
    /**
     * 省份代码
     */
    @JSONField(name = "省份代码", serialize = false)
    private String province_code;
    /**
     * 企业注册地址
     */
    @JSONField(name = "企业注册地址")
    private String reg_address;
    /**
     * 注册资金
     */
    @JSONField(name = "注册资金")
    private String reg_cap;
    /**
     * 注册资金单位
     */
    @JSONField(name = "注册资金单位",serialize = false)
    private String reg_cap_cur;
    /**
     * 注册省份代码
     */
    @JSONField(name = "注册省份代码", serialize = false)
    private String reg_province_code;
    /**
     * 注册地址市代码
     */
    @JSONField(name = "注册地址市代码", serialize = false)
    private String reg_city_code;
    /**
     * 注册地址地区代码
     */
    @JSONField(name = "注册地址地区代码", serialize = false)
    private String reg_district_code;
    /**
     * 注册号
     */
    @JSONField(name = "注册号", serialize = false)
    private String reg_no;
    /**
     * 登记机关
     */
    @JSONField(name = "登记机关", serialize = false)
    private String reg_org;
    /**
     * 注销日期/吊销日期
     */
    @JSONField(name = "注销日期/吊销日期", serialize = false)
    private String revoke_date;
    /**
     * 统一信用代码
     */
    @JSONField(name = "统一信用代码", serialize = false)
    private String uncid;
    /**
     * 企业英文名称
     */
    @JSONField(name = "企业英文名称")
    private String entname_eng;
    /**
     * 三级行业
     */
    @JSONField(name = "三级行业")
    private String industryL3_desc;
    /**
     * 四级行业
     */
    @JSONField(name = "四级行业")
    private String industryL4_desc;
    /**
     * 1=现有企业匹配，2=企业历史名称匹配
     */
    @JSONField(name = "匹配类型", serialize = false)
    private Integer match_type;
    /**
     * 所属产业名称数组
     */
    @JSONField(name = "所属产业名称数组",serialize = false)
    private List<IndustrialChainData> industrial_chain;
    /**
     * 注销时间
     */
    @JSONField(name = "注销时间", serialize = false)
    private Long cancel_date;

    @Data
    public static class IndustrialChainData implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 一级所属产业名称
         */
        @JSONField(name = "一级产业名称")
        private String industrialChainL1;
        /**
         * 二级所属产业名称
         */
        @JSONField(name = "二级产业名称")
        private String industrialChainL2;
        /**
         * 三级所属产业名称
         */
        @JSONField(name = "三级产业名称")
        private String industrialChainL3;
    }
}
