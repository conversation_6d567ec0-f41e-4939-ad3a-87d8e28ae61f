/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.common;


import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平台型接口参数基类
 *
 * <AUTHOR>
 */
public abstract class ProductParam {

    /**
     * 平台ID
     */
    private Long appId;

    /**
     * 企业在平台侧的唯一ID
     */
    private String openId;

    /**
     * 当前 UNIX 时间戳，可记录发起 API 请求的时间。单位：毫秒 5分钟内有效，例如1529223702
     */
    private String timestamp;

    /**
     * 请求签名（根据签名算法计算得出）
     */
    private String signature;

    /**
     * 默认为SHA256，当前只支持一种算法（未来会支持国密算法）
     * 当前设置默认值后有 bug 待修复
     */
    private String signatureMethod;

    /**
     * 返回平台接口公共参数
     *
     * @return
     */
    public final Map<String, String> getParamsMap() {
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("appId", String.valueOf(appId));
        if (StrUtil.isNotEmpty(openId)) {
            paramMap.put("openId", openId);
        }
        timestamp = String.valueOf(System.currentTimeMillis());
        paramMap.put("timestamp", timestamp);

        Map<String, String> params = getParams();
        if (params != null) {
            paramMap.putAll(params);
        }
        return paramMap;
    }

    public final void validate() {
        validateBaseParam();
        validateParam();
    }

    /**
     * 返回除公共参数之外的参数
     *
     * @return
     */
    protected abstract Map<String, String> getParams();

    /**
     * 验证除公共参数之外的参数
     */
    protected abstract void validateParam();

    /**
     * 用于返回参与签名的参数
     * 因为不同接口，签名参数不统一。此处后续需要优化
     *
     * @return
     */
    public abstract List<String> getSignatureParams();

    private void validateBaseParam() {
        if (appId == null || appId <= 0) {
            throw new KnownException("appId 参数不能为空");
        }
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getSignatureMethod() {
        return signatureMethod;
    }

    public void setSignatureMethod(String signatureMethod) {
        this.signatureMethod = signatureMethod;
    }

    public String getTimestamp() {
        return timestamp;
    }
}
