package com.ce.scrm.center.service.eqixiu.sdk.domain.type;

/**
 * 授权类型
 * 0-未设置授权
 * 1-公众号授权
 * 2-手机号授权
 * 3-自定义授权
 * 4-无授权
 *
 * <AUTHOR>
 */

public enum AuthType {
    NONE(0, "未设置授权"),
    WECHAT(1, "公众号授权"),
    PHONE(2, "手机号授权"),
    CUSTOM(3, "自定义授权"),
    NO_AUTH(4, "匿名授权");
    private final int value;
    private final String title;

    AuthType(int value, String title) {
        this.value = value;
        this.title = title;
    }

    public int getValue() {
        return value;
    }

    public String getTitle() {
        return title;
    }
}
