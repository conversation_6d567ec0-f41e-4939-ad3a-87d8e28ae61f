package com.ce.scrm.center.service.business.abm;

import cn.ce.cesupport.emp.service.EmployeeAppService;
import cn.ce.cesupport.emp.vo.EmployeeVo;
import cn.ce.cesupport.framework.base.vo.EmployeeVO;
import cn.ce.cesupport.rbac.service.RoleRelationAppService;
import cn.ce.cesupport.sma.service.SendMessageAppService;
import cn.ce.cesupport.sma.service.WxMessageAppService;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmMessageNoticeDto;
import com.ce.scrm.center.service.business.entity.mq.TextCardMessage;
import com.ce.scrm.center.service.business.entity.mq.WechatMessageSendMqData;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.customer.dubbo.api.ICustomerDubbo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 转商机下发 通知
 */
@Slf4j
@Service
public class AbmMessageNoticeBusiness {

    @Resource
    private CmCustProtectService cmCustProtectService;

    @DubboReference
    private RoleRelationAppService roleRelationAppService;

    @DubboReference
    private WxMessageAppService wxMessageAppService;

    @DubboReference
    private SendMessageAppService sendMessageAppService;

    @DubboReference
    private EmployeeAppService employeeAppService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private RocketMqOperate rocketMqOperate;

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private ICustomerDubbo customerDubbo;

    @Value("${abm.protectionListUrl}")
    private String wecomProtectionListUrl;

    @Value("${mq.prefix}")
    private String mqPrefix;

    /***
     * 审核通知消息 【通知SDR、CC主管】
     * @param leaderId
     * @param message
     * <AUTHOR>
     * @date 2025/8/13 16:07
     * @version 1.0.0
     **/
    public void reviewNoticeMsg(String leaderId, String message) {
        if (StrUtil.isBlank(leaderId)) {
            log.info("{} 找不到对应的通知人", message);
        } else {
            // 发送企业微信
            log.info("leaderId ={} message={}", leaderId, message);
            wxMessageAppService.sendMessage(leaderId, message);
        }
    }

    /***
     * 商机下发之后，回执剩余15分钟，AI电话提醒
     * @param notifierMobile
     * <AUTHOR>
     * @date 2025/8/12 14:35
     * @version 1.0.0
     **/
    public void aiCallNotice(String notifierMobile) {
        Integer taskId = (int) (System.currentTimeMillis() / 1000); // 秒级时间戳
        // ai电话
        if (StrUtil.isNotBlank(notifierMobile)) {
            JSONObject aiParamJson = new JSONObject();
            aiParamJson.put("phone", notifierMobile);
            aiParamJson.put("taskId", taskId);
            log.info("notifierMobile ={} taskId={}", notifierMobile, taskId);
            rocketMqOperate.syncSend("AI_CALL_TOPIC", aiParamJson.toJSONString());
        } else {
            log.error("商机拨打电话失败，aiPhone={},taskId={}", notifierMobile, taskId);
        }
    }

    /***
     * 商机下发之后，回执剩余半小时，短信提醒
     * @param customerName
     * @param notifierMobile
     * <AUTHOR>
     * @date 2025/8/12 14:35
     * @version 1.0.0
     **/
    public void smsNotice(String customerName, String notifierMobile) {
        // 发送短信
        if (StrUtil.isNotBlank(notifierMobile)) {
            String smsTemplateId = "SMS_172355466";
            Map<String, String> smsParam = new HashMap<>();
            smsParam.put("cust_name", customerName);
            log.info("customerName ={} notifierMobile={}", customerName, notifierMobile);
            sendMessageAppService.sendMessage(notifierMobile, smsTemplateId, smsParam);
        } else {
            log.error("商机发送短信失败，aiPhone={}", notifierMobile);
        }
    }

    /***
     * 商机下发，触发企业消息通知
     * @param custName
     * @param notifierUserId
     * <AUTHOR>
     * @date 2025/8/12 14:17
     * @version 1.0.0
     **/
    public void wxNoticeMsg(String custName, String notifierUserId) {

        if (StrUtil.isBlank(custName) || StrUtil.isBlank(notifierUserId)) {
            log.error("custName={} notifierUserId={}  参数缺失，发生企微通知失败", custName, notifierUserId);
        } else {
            // 发送企业微信
            String qwStr = "系统分配您一个商机客户" + custName + "，请登录营销平台进行回执。";
            log.info("custName ={} msgStr={}", custName, qwStr);
            WechatMessageSendMqData wechatMessageSend = new WechatMessageSendMqData();
            wechatMessageSend.setToUserList(Lists.newArrayList(notifierUserId));
            wechatMessageSend.setMsgType("textcard");
            TextCardMessage textCard = new TextCardMessage();
            textCard.setTitle("跨境商机下发提醒");
            textCard.setDescription(qwStr);
            // 跳转链接
            textCard.setUrl(wecomProtectionListUrl);
            wechatMessageSend.setTextCard(textCard);
            log.info("企微消息通知MqData ={}", JSONObject.toJSONString(wechatMessageSend));
            rocketMqOperate.syncSend(mqPrefix + ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_WECHAT_MESSAGE_SEND_TOPIC, JSONObject.toJSONString(wechatMessageSend));
        }
    }

    /***
     * 当前通知人
     * @param customerId
     * <AUTHOR>
     * @date 2025/8/12 14:28
     * @version 1.0.0
     * @return java.util.Optional<com.ce.scrm.center.service.business.entity.dto.abm.AbmMessageNoticeDto>
     **/
    public Optional<AbmMessageNoticeDto> getNoticeInfoByCustomerId(String customerId) {
        CustProtectView custProtectView = cmCustProtectService.selectOneByCondition(
                CmCustProtect.builder().custId(customerId).build());
        if (Objects.isNull(custProtectView)) {
            log.warn("保护关系中找不到客户 {}", customerId);
            return Optional.empty();
        }
        AbmMessageNoticeDto abmMessageNoticeDto = new AbmMessageNoticeDto();
        abmMessageNoticeDto.setCustomerId(customerId);
        abmMessageNoticeDto.setCustomerName(custProtectView.getCustName());

        Integer status = custProtectView.getStatus();
        Integer taskId = null;
        String remindEmployeeId = null;
        String aiPhone = null;

        switch (status) {
            case 1:
                remindEmployeeId = custProtectView.getSalerId();
                EmployeeVO employeeVO = roleRelationAppService.findDeptInfoAndSubInfoOfEmp(remindEmployeeId);
                if (employeeVO != null) {
                    aiPhone = employeeVO.getBindMobile();
                    abmMessageNoticeDto.setNotifierMobile(aiPhone);
                }
                break;
            case 2:
                String subcompanyId = custProtectView.getSubcompanyId();
                EmployeeVo subEmpVo = employeeAppService.findOrgLeader(subcompanyId);
                if (subEmpVo != null) {
                    remindEmployeeId = subEmpVo.getId();
                    aiPhone = subEmpVo.getBindMobile();
                }
                break;
            case 3:
                String bussdeptId = custProtectView.getBussdeptId();
                EmployeeVo deptEmployeeVo = employeeAppService.findOrgLeader(bussdeptId);
                if (deptEmployeeVo != null) {
                    remindEmployeeId = deptEmployeeVo.getId();
                    aiPhone = deptEmployeeVo.getBindMobile();
                }
                break;
            case 4:
                log.warn("商机所在保护为线索池，cmCustProtectVo={}", JSONObject.toJSONString(custProtectView));
                return Optional.empty();
            case 5:
                String buId = custProtectView.getBuId();
                EmployeeVo buEmployeeVo = employeeAppService.findOrgLeader(buId);
                if (buEmployeeVo != null) {
                    remindEmployeeId = buEmployeeVo.getId();
                    aiPhone = buEmployeeVo.getBindMobile();
                }
                break;
            default:
                log.error("保护关系状态错误，cmCustProtectVo={}", JSONObject.toJSONString(custProtectView));
        }

        abmMessageNoticeDto.setNotifierUserId(remindEmployeeId);
        abmMessageNoticeDto.setNotifierMobile(aiPhone);
        return Optional.of(abmMessageNoticeDto);
    }
}
