package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * description:网络推广
 * @author: DD.Jiu
 * date: 2025/2/13.
 */
@Data
public class BigDataPromotionDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**企业名称*/
    private String entname;
    /**统一信用代码*/
    private String uncid;
    /**总数量*/
    private int total_count;
    /**总页数*/
    private int total_page;
    /**当前页*/
    private int page;
    @JSONField(name = "网络推广列表")
    private List<ListPromotion> list;

    @NoArgsConstructor
    @Data
    public static class ListPromotion implements Serializable {
        private static final long serialVersionUID = 1L;
        /**推广标题*/
        @JSONField(name = "推广标题")
        private String sem_title;
        /**推广平台*/
        @JSONField(name = "推广平台")
        private String sem_platform;
        /**推广链接*/
        @JSONField(name = "推广链接",serialize = false)
        private String sem_url;
        /**推广日期*/
        @JSONField(name = "推广日期",format = "yyyy-MM-dd HH:mm:ss")
        private Date sem_date;
        /**推广关键词*/
        @JSONField(name = "推广关键词")
        private List<String> sem_keywords;
        /**推广关键词数*/
        @JSONField(name = "推广关键词数")
        private String keywords_num;
    }
}
