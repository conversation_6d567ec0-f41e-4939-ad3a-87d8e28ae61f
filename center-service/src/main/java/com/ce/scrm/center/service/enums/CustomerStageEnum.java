package com.ce.scrm.center.service.enums;

import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityCustomerStageData;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户阶段枚举
 * <AUTHOR>
 * @date 2024/5/21 下午3:36
 * @version 1.0.0
 **/
@Getter
public enum CustomerStageEnum {

    OPEN_SEA(1, "公海"),
    CE_PROTECT(2, "中小保护"),
    GC_PROTECT(3, "高呈保护"),
    ;

    /**
     * 阶段
     */
    private final Integer stage;
    /**
     * 阶段名称
     */
    private final String stageName;

    CustomerStageEnum(Integer stage, String stageName) {
        this.stage = stage;
        this.stageName = stageName;
    }

    private final static Map<Integer, CustomerStageEnum> STAGE_MAP = new HashMap<>();

    /**
     * 客户阶段数据列表
     */
    private final static List<GcBusinessOpportunityCustomerStageData> GC_BUSINESS_OPPORTUNITY_CUSTOMER_STAGE_DATA_LIST = new ArrayList<>();

    /**
     * 获取客户阶段枚举
     * @param customerStage 客户阶段值
     * <AUTHOR>
     * @date 2024/5/21 下午4:27
     * @return com.ce.scrm.center.service.enums.CustomerStageEnum
     **/
    public static CustomerStageEnum get(Integer customerStage) {
        if (STAGE_MAP.isEmpty()) {
            for (CustomerStageEnum customerStageEnum : CustomerStageEnum.values()) {
                STAGE_MAP.put(customerStageEnum.stage, customerStageEnum);
            }
        }
        return STAGE_MAP.get(customerStage);
    }

    /**
     * 获取所有客户阶段数据
     * <AUTHOR>
     * @date 2024/5/28 下午2:04
     * @return java.util.List<com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityCustomerStageData>
     **/
    public static List<GcBusinessOpportunityCustomerStageData> list() {
        if (GC_BUSINESS_OPPORTUNITY_CUSTOMER_STAGE_DATA_LIST.isEmpty()) {
            for (CustomerStageEnum customerStageEnum : CustomerStageEnum.values()) {
                GcBusinessOpportunityCustomerStageData gcBusinessOpportunityCustomerStageData = new GcBusinessOpportunityCustomerStageData();
                gcBusinessOpportunityCustomerStageData.setStage(customerStageEnum.getStage());
                gcBusinessOpportunityCustomerStageData.setStageName(customerStageEnum.getStageName());
                GC_BUSINESS_OPPORTUNITY_CUSTOMER_STAGE_DATA_LIST.add(gcBusinessOpportunityCustomerStageData);
            }
        }
        return GC_BUSINESS_OPPORTUNITY_CUSTOMER_STAGE_DATA_LIST;
    }
}
