/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.product;

import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.common.ProductParam;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.ClearingType;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.CorpAccountType;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * https://hc.eqxiu.cn/doc/21/
 */
public class CreateCorpCmd extends ProductParam {

    /**
     * 企业名称
     */
    private String name;


    /**
     * 1-正式账号 2-试用账号（不传该参数 或者 传1 都是创建的正式账号；传2 并且 平台配置了试用期天数才会创建试用账号）
     */
    private CorpAccountType accountType = CorpAccountType.TRIAL;

    /**
     * 1先结算，2后结算
     */
    private ClearingType clearingType = ClearingType.PAY_LATER;

    @Override
    protected Map<String, String> getParams() {
        Map<String, String> map = new HashMap<>();
        map.put("name", name);
        map.put("accountType", String.valueOf(accountType.getValue()));
        map.put("clearingType", String.valueOf(clearingType.getValue()));

        return map;
    }

    @Override
    protected void validateParam() {
        // 验证name
        if (StrUtil.isEmpty(name)) {
            throw new KnownException("name 参数不能为空");
        }
        if (StrUtil.isEmpty(getOpenId())) {
            throw new KnownException("openId 参数不能为空");
        }
    }

    @Override
    public List<String> getSignatureParams() {
        List<String> params = new ArrayList<>();
        params.add(String.valueOf(getAppId()));
        params.add(getOpenId());
        params.add(getTimestamp());
        params.add(name);
        params.add(String.valueOf(accountType.getValue()));
        params.add(String.valueOf(clearingType.getValue()));

        return params;
    }

    public CorpAccountType getAccountType() {
        return accountType;
    }

    public void setAccountType(CorpAccountType accountType) {
        this.accountType = accountType;
    }

    public ClearingType getClearingType() {
        return clearingType;
    }

    public void setClearingType(ClearingType clearingType) {
        this.clearingType = clearingType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
