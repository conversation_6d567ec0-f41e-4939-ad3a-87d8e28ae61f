package com.ce.scrm.center.service.business.entity.view.segment;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 区域查询分群信息
 * @Author: lijinpeng
 * @Date: 2025/2/27 14:25
 */
@Data
public class QueryAreaSegmentBusinessView implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户ID
     */
    private String customerId;

    private String pid;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 客户状态
     */
    private String customerStateStr;

    /**
     * 保护关系快照(JSON结构存储分司/事业部/部门/商务)
     */
    private String protectSnapshot;

    /**
     * 已分配分司 格式：A、B
     */
    private String allocatedSubListNameStr;

    /**
     * 首次保护时间（仅初次有效）
     */
    private Date protectTime;

    /**
     * 下发后是否拜访 是、否
     */
    private String visitFlagStr;

    /**
     * 最近一次拜访时间（根据打卡表同步）
     */
    private Date visitLastTime;

    /**
     * 一级国标行业编码
     */
    private String firstIndustryCode;

    /**
     * 一级国标行业名称
     */
    private String firstIndustryName;

    /**
     * 二级国标行业编码
     */
    private String secondIndustryCode;

    /**
     * 二级国标行业名称
     */
    private String secondIndustryName;

    /**
     * 三级国标行业编码
     */
    private String thirdIndustryCode;

    /**
     * 三级国标行业名称
     */
    private String thirdIndustryName;

    /**
     * 四级国标行业编码
     */
    private String fourthIndustryCode;

    /**
     * 四级国标行业名称
     */
    private String fourthIndustryName;

    /**
     * 注册资本
     */
    private Double registerCapital;

    /**
     * 有无域名备案 1:是 0:否
     */
    private String icpFlagStr;

    /**
     * 有无进出口信用 1:是 0:否
     */
    private String jingchukouFlagStr;

    /**
     * 备注
     */
    private String remark;

}
