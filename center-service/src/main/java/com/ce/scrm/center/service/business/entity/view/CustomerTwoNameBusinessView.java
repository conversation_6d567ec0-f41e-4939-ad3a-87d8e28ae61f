package com.ce.scrm.center.service.business.entity.view;

import com.ce.scrm.center.service.business.entity.dto.CmaCustProtectDto;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2024/12/5 15:12
 */
@Data
@Builder
public class CustomerTwoNameBusinessView implements Serializable {

    /**
     * 原名称对应
     */
    CmaCustProtectDto oldCustomer;

    /**
     * 新名称对象
     */
    CmaCustProtectDto newCustomer;

}
