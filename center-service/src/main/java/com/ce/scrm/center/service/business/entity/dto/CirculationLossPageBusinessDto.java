package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * Description: 流转流失客户查询参数
 * @author: JiuDD
 * date: 2024/7/22
 */
@Data
@Accessors(chain = true)
public class CirculationLossPageBusinessDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户id
     */
    private String custId;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 区域
     */
    private String areaId;
    /**
     * 分公司
     */
    private String subId;
    /**
     * 事业部id
     */
    private String buId;
    /**
     * 部门
     */
    private String deptId;
    /**
     * 商务ID
     */
    private String salerId;
    /**
     * 预计流转（流失）日期
     */
    private LocalDate preDate;
    /**
     * 来源（流转、流失）
     * @see com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum
     */
    private Integer origin;
    /*
     * 删除标记，0：未删除，1：已删除
     */
    private Integer deleteFlag;
    /**
     * 排序字段：1预计流转日期
     */
    private Integer orderBy;
    /**
     * 排序方式：asc 升序 desc 降序
     */
    private String orderByType;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;


    /**
     * 客户分层（商务）
     */
    private Integer customerLayer;
}