package com.ce.scrm.center.service.utils;

import cn.ce.cesupport.enums.GcSjStateEnum;
import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunitySourceStateData;

import java.util.*;

/**
 * description: 高呈商机状态枚举 util
 * @author: DD.Jiu
 * date: 2024/8/9.
 */
public class GcSjStateEnumUtil {

    /**
     * 根据来源获取商机状态枚举列表
     */
    private final static Map<Integer, List<GcBusinessOpportunitySourceStateData.State>> GC_SJ_STATE_ENUM_LIST_MAP = new HashMap<>();

    /**
     * 根据来源获取商机状态枚举列表
     */
    private final static List<GcBusinessOpportunitySourceStateData.State> GC_SJ_STATE_ENUM_LIST_ALL = new ArrayList<>();

    /**
     * 根据来源获取商机状态枚举
     * @param source    商机来源
     * <AUTHOR>
     * @date 2024/5/16 下午4:23
     * @return java.util.List<com.ce.scrm.center.dubbo.entity.view.GcBusinessOpportunitySourceStateData.State>
     **/
    public static List<GcBusinessOpportunitySourceStateData.State> list(Integer source) {
        if (GC_SJ_STATE_ENUM_LIST_MAP.isEmpty()) {
            for (GcSjStateEnum gcSjStateEnum : GcSjStateEnum.values()) {
                GcBusinessOpportunitySourceStateData.State state = new GcBusinessOpportunitySourceStateData.State();
                state.setState(gcSjStateEnum.getState());
                state.setStateName(gcSjStateEnum.getStateName());
                state.setSelectState(gcSjStateEnum.getSelectState());
                gcSjStateEnum.getGcSjSourceEnumList().forEach(gcSjSourceEnum -> {
                    List<GcBusinessOpportunitySourceStateData.State> list = GC_SJ_STATE_ENUM_LIST_MAP.getOrDefault(gcSjSourceEnum.getSource(), new ArrayList<>());
                    list.add(state);
                    GC_SJ_STATE_ENUM_LIST_MAP.put(gcSjSourceEnum.getSource(), list);
                });
            }
        }
        return GC_SJ_STATE_ENUM_LIST_MAP.get(source);
    }

    /**
     * 根据来源获取商机状态枚举
     * <AUTHOR>
     * @date 2024/5/16 下午4:23
     * @return java.util.List<com.ce.scrm.center.dubbo.entity.view.GcBusinessOpportunitySourceStateData.State>
     **/
    public static List<GcBusinessOpportunitySourceStateData.State> listAll() {
        if (GC_SJ_STATE_ENUM_LIST_ALL.isEmpty()) {
            for (GcSjStateEnum gcSjStateEnum : GcSjStateEnum.values()) {
                GcBusinessOpportunitySourceStateData.State state = new GcBusinessOpportunitySourceStateData.State();
                state.setState(gcSjStateEnum.getState());
                state.setStateName(gcSjStateEnum.getStateName());
                state.setSelectState(gcSjStateEnum.getSelectState());
                GC_SJ_STATE_ENUM_LIST_ALL.add(state);
            }
        }
        return GC_SJ_STATE_ENUM_LIST_ALL;
    }

}