package com.ce.scrm.center.service.business.entity.ai;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/2/24 16:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AiStarLogBusinessDto implements Serializable {

    /**
     * pid
     */
    private String pid;

    /**
     * custId
     */
    private String custId;

    /**
     * ai拓展提示词
     */
    private String aiExtendPrompt;

    /**
     * ai输出的内容
     */
    private String aiOutputContent;

    /**
     * ai输出的思考过程
     */
    private String aiOutputReasoning;

    /**
     * 员工ID
     */
    private String loginEmployeeId;

    /**
     * 1 点赞 0 点 low 默认 1
     */
    private Integer operatorType;

    /**
     * 点赞或者 点 low的原因
     */
    private String starReason;

    /**
     * 分析主题ID
     */
    private String promptId;


    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;

}
