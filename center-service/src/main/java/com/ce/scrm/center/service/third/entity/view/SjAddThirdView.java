package com.ce.scrm.center.service.third.entity.view;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SjAddThirdView implements Serializable {

    private String id;

    private String clueId;

    private String busiOppoCode;

    private String createrId;

    private String createrName;

    private String createTimeFormat;

    private Integer createTimeDaysAgo;

    private Integer createTimeDaysAfter;

    private Date createTime;

    private Date updateTime;

    private String updateTimeFormat;

    private String trafficKey;

    private String custId;

    private String custName;

    private String linkmanName;

    private String sex;

    private String position;

    private String mobile;

    private String telephone;

    private String mail;

    private String province;

    private String city;

    private String district;

    private String address;

    private String qq;

    private String weChat;

    private String industryOneCode;

    private String industryTwoCode;

    private String mainBusiness;

    private String custRequirement;

    private String remark;

    private String status;

    private String isMainBusiOpp;

    private String isNewBusiOpp;

    private String commerce;

    private String deptId;

    private String deptName;

    private String branchOfficeName;

    private String branchOfficeId;

    private String regionId;

    private String regionName;

    private String isVisited;

    private String commerceId;

    private String area;

    private Object busiOpporLabel;

    private String mobileFlag;

    private String telephoneFlag;

    private String mailFlag;

    private String custNameFlag;

    private String visitorId;

    private String visitorName;

    private String opportunityVisitStatus;

    // 线索归属，隔离标记
    private String solationFlag;

    private String handlePersonId;

    private String handlePersonName;

    private String handleDeptId;

    private String handleDeptName;

    private String fileId;

    private Date handleTime;

    private String handleResult;

    private String memberCode;

    private Integer isOwn;

    // 放弃原因
    private String giveUpReason;

    private Date giveUpTime;

    /*来源*/
    private String source;

    /*分配建议*/
    private String assignProposal;

    /**
     * 分级标签
     * ABCDE
     */
    private String intentTag;

    /**
     * 回执标记:1、未回执，2、已回执
     */
    private Integer receiptFlag;

    /**
     * 提醒次数
     */
    private Integer remindTime;

    /**
     * 保护标记：1、先有保护，2、全新
     */
    private Integer protectFlag;

    /**
     * 通话记录ID
     */
    private String callId;

    /**
     * 通话记录录音url
     */
    private String callRecordUrl;

    /**
     * 通话接通时间
     */
    private Date callAnswerTime;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}