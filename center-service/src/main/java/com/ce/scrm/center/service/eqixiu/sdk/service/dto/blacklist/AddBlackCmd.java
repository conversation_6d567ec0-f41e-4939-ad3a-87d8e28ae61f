/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.blacklist;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.AuthType;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

/**
 * 添加黑白名单参数
 *
 * <AUTHOR>
 */
public class AddBlackCmd extends BaseParam {

    private AuthType authType;

    /**
     * 黑名单用户唯一标识，分别与授权类型相对应
     */
    private String authUniqueId;

    /**
     * 黑名单用户备注
     */
    private String remark;

    public AddBlackCmd(AuthType authType, String authUniqueId, String openId) {
        this.authType = authType;
        this.authUniqueId = authUniqueId;
        setOpenId(openId);
    }

    public AddBlackCmd(AuthType authType, String authUniqueId, String remark, String openId) {
        this.authType = authType;
        this.authUniqueId = authUniqueId;
        this.remark = remark;
        setOpenId(openId);
    }

    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> paramsMap = getBaseParamsMap();
        paramsMap.put("authType", String.valueOf(authType.getValue()));
        paramsMap.put("authUniqueId", authUniqueId);
        if (!StrUtil.isEmpty(remark)) {
            paramsMap.put("remark", remark);
        }
        return paramsMap;
    }

    @Override
    public void validate() {
        if (StrUtil.isEmpty(getOpenId())) {
            throw new KnownException("openId 参数不能为空");
        }
        if (authType == null) {
            throw new KnownException("authType 参数不能为空");
        }
        if (StrUtil.isEmpty(authUniqueId)) {
            throw new KnownException("authUniqueId 参数不能为空");
        }
    }

    public AuthType getAuthType() {
        return authType;
    }

    public void setAuthType(AuthType authType) {
        this.authType = authType;
    }

    public String getAuthUniqueId() {
        return authUniqueId;
    }

    public void setAuthUniqueId(String authUniqueId) {
        this.authUniqueId = authUniqueId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
