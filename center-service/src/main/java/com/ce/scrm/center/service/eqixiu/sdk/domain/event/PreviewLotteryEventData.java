package com.ce.scrm.center.service.eqixiu.sdk.domain.event;

import com.ce.scrm.center.service.eqixiu.sdk.domain.Customer;

/**
 * 中奖事件
 *
 * <AUTHOR>
 */
public class PreviewLotteryEventData extends CreationEventData {

    /**
     * 奖品等级
     */
    private String level;

    /**
     * 兑奖方式:0:线下兑奖；1:网页兑奖；2:淘口令兑奖；5:网页兑奖（新）；6:外部兑奖；7：实物兑奖
     */
    private Integer redemptionMethod;

    /**
     * prizeCode	String	奖品码(用户自定义)
     */
    private String prizeCode;

    /**
     * 奖品类型，0礼品，1优惠券，2现金红包，3电子兑奖码，4积分
     */
    private Integer prizeType;

    /**
     * 奖品名称
     */
    private String prizeName;

    /**
     * 单次中奖发放的奖品数量
     */
    private Integer amount;

    /**
     * 抽奖来源:hd_lottery:互动抽奖;hd_game_score:互动游戏分数达标;hd_game_rank:互动游戏排名;hd_sign:互动签到;hd_help:互动助力;form_enrollment:表单自主报名;form_submit:表单投票;form_rank:表单排行
     */
    private String lotteryBehavior;

    private Customer customerInfo;

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Integer getRedemptionMethod() {
        return redemptionMethod;
    }

    public void setRedemptionMethod(Integer redemptionMethod) {
        this.redemptionMethod = redemptionMethod;
    }

    public String getPrizeCode() {
        return prizeCode;
    }

    public void setPrizeCode(String prizeCode) {
        this.prizeCode = prizeCode;
    }

    public Integer getPrizeType() {
        return prizeType;
    }

    public void setPrizeType(Integer prizeType) {
        this.prizeType = prizeType;
    }

    public String getPrizeName() {
        return prizeName;
    }

    public void setPrizeName(String prizeName) {
        this.prizeName = prizeName;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getLotteryBehavior() {
        return lotteryBehavior;
    }

    public void setLotteryBehavior(String lotteryBehavior) {
        this.lotteryBehavior = lotteryBehavior;
    }

    public Customer getCustomerInfo() {
        return customerInfo;
    }

    public void setCustomerInfo(Customer customerInfo) {
        this.customerInfo = customerInfo;
    }
}
