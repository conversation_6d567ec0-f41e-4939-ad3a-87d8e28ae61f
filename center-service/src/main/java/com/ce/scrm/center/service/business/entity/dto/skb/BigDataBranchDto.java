package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 子公司与分支机构
 * @author: JiuDD
 * date: 2025/2/13
 */
@Data
public class BigDataBranchDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**企业名称*/
    private String entname;
    /**统一信用代码*/
    private String uncid;
    /**总数量*/
    private int total_count;
    /**总页数*/
    private int total_page;
    /**当前页*/
    private int  page;
    /**子公司与分支机构列表*/
    @JSONField(name = "分支机构列表")
    private List<ListBranch> list;

    @Data
    public static class ListBranch implements Serializable {
        private static final long serialVersionUID = 1L;
        /**分支机构名称*/
        @JSONField(name = "子公司名称")
        private String ent_name;
        /**分支机构注册号*/
        @JSONField(name = "分支机构注册号", serialize = false)
        private String reg_no;
        /**分支机构的监管机构*/
        @JSONField(name = "分支机构的监管机构", serialize = false)
        private String reg_org_cn;
        /**分支机构信用代码*/
        @JSONField(name = "分支机构信用代码", serialize = false)
        private String unisc_id;
        /**分支机构类型*/
        @JSONField(name = "分支机构类型", serialize = false)
        private String relation_type;
        /**法人类型*/
        @JSONField(name = "分支机构法人类型", serialize = false)
        private String legal_person_desc;
        /**法人名称*/
        @JSONField(name = "分支机构法人名称", serialize = false)
        private String legal_person;
        /**注册资本数字*/
        @JSONField(name = "分支机构注册资本数字", serialize = false)
        private String reg_cap;
        /**资产资本单位*/
        @JSONField(name = "分支机构资产资本单位", serialize = false)
        private String reg_cap_cur;
        /**分支机构成立时间*/
        @JSONField(name = "分支机构成立时间", serialize = false)
        private Long es_date;
        /**企业状态*/
        @JSONField(name = "子公司状态")
        private String ent_status_desc;
    }
}
