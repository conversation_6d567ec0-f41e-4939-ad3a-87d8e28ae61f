package com.ce.scrm.center.service.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Nlp nacos配置
 *
 * <AUTHOR>
 * @date 2023/6/14 17:36
 */
@Component
@Slf4j
@Data
public class NlpProperties {

    @NacosValue(value = "${systemPromote:}", autoRefreshed = true)
    private String systemPromote;


    @NacosValue(value = "${promoteGuide:}", autoRefreshed = true)
    private String promoteGuide;

    @NacosValue(value = "${xfappId:e1fbdf40}", autoRefreshed = true)
    private String appId;

    @NacosValue(value = "${xfapiSecret:NmFmYjNiYzdjNWU2ODc5MDMwZWVkMzQy}", autoRefreshed = true)
    private String apiSecret;


    @NacosValue(value = "${promotePerson:}", autoRefreshed = true)
    private String promotePerson;


    @NacosValue(value = "${webSearch:}", autoRefreshed = true)
    private String webSearch;

    @NacosValue(value = "${analyzeAuth:}", autoRefreshed = true)
    private String analyzeAuth;

    @NacosValue(value = "${chatPromote:接下来的回答必须在以上内容的范围内,如果超出这个范围必须拒绝回答,不要在回答中出现数字预估和承诺,不要有预期数据,不能做爬虫和联网查询}", autoRefreshed = true)
    private String chatPromote;

    @NacosValue(value = "${imageGenerator:你现在是一个ai图片生成机器人，我给你一些提示，你用你的想象力去生动摧述这幅图片，并转化成英文填充到下面 url的占位符中： https://image.pollinations.ai/prompt/{prompt}?width=1024&height=1024&seed=100&model=flux&nologo=true  e.g ## 输入 ## 春江花月夜 ## 输出 ## https://image.pollinations.ai/prompt/windy?width=1024&height=1024&seed=100&model=flux&nologo=true}", autoRefreshed = true)
    private String imageGenerator;


    @NacosValue(value = "${errorAlertCount:10}", autoRefreshed = true)
    private String errorAlertCount;

    @NacosValue(value = "${errorTime:3000}", autoRefreshed = true)
    private String errorTime;

    @NacosValue(value = "${timeDiff:10}", autoRefreshed = true)
    private String timeDiff;

    @NacosValue(value = "${customsExportMessage:}", autoRefreshed = true)
    private String customsExportMessage;

    @NacosValue(value = "${dataInspection:}", autoRefreshed = true)
    private String dataInspection;

    @NacosValue(value = "${webSiteReport:请跟据客户网站分析报告，分析网站问题，并给出修改建议}", autoRefreshed = true)
    private String webSiteReport;

    @NacosValue(value = "${imageGeneratorLimit:3}", autoRefreshed = true)
    private String imageGeneratorLimit;

    @NacosValue(value = "${codeModel:qwen3-coder-plus}", autoRefreshed = true)
    private String codeModel;

}
