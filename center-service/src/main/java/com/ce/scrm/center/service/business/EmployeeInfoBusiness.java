package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import cn.ce.cesupport.emp.consts.OrgConstant;
import cn.ce.cesupport.emp.enums.OrgTypeEnum;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.ce.cesupport.rbac.service.RoleRelationAppService;
import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.dao.entity.SmaRole;
import com.ce.scrm.center.dao.entity.SmaRoleOrg;
import com.ce.scrm.center.dao.entity.SmaRoleRelation;
import com.ce.scrm.center.dao.service.SmaRoleOrgService;
import com.ce.scrm.center.dao.service.SmaRoleRelationService;
import com.ce.scrm.center.dao.service.SmaRoleService;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.employee.ChangeRoleBusinessDto;
import com.ce.scrm.center.service.cache.EntVxCodeCacheHandler;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @version 1.0
 * @Description: 员工信息
 * @Author: lijinpeng
 * @Date: 2024/11/14 16:54
 */
@Service
@Slf4j
public class EmployeeInfoBusiness {

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private OrgThirdService orgThirdService;

    @Resource
    private SmaRoleRelationService smaRoleRelationService;

    @Resource
    private SmaRoleOrgService smaRoleOrgService;

    @Resource
    private SmaRoleService smaRoleService;

    @DubboReference
    private RoleRelationAppService roleRelationAppService;

    @Resource
    private EntVxCodeCacheHandler entVxCodeCacheHandler;

    /*
     * @Description 根据员工id获取员工信息 不检查state=1
     * <AUTHOR>
     * @date 2024/11/18 10:32
     * @param empId
     * @return com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto
     */
    public EmployeeInfoBusinessDto getEmployeeInfoByEmpId(String empId) {

        if(StringUtils.isBlank(empId)) {
//            log.error("empId is null");
            return null;
        }

        // 查询EMP系统员工信息
        Optional<EmployeeInfoThirdDto> employeeInfoThirdDtoOptional = employeeThirdService.getEmployeeByEmpId(empId);
        if (!employeeInfoThirdDtoOptional.isPresent()) {
            return null;
        }

        EmployeeInfoThirdDto employeeInfoThirdDto = employeeInfoThirdDtoOptional.get();
        EmployeeInfoBusinessDto result = BeanUtil.copyProperties(employeeInfoThirdDto, EmployeeInfoBusinessDto.class);

        return buildOrgInfo(result);
    }

    /*
     * @Description 根据员工id集合获取员工集合信息 不检查state=1
     * <AUTHOR>
     * @date 2024/12/3 16:44
     * @param empIdList
     * @return java.util.Map<java.lang.String,com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto>
     */
    public Map<String,EmployeeInfoBusinessDto> getEmployeeInfoByEmpIdList(List<String> empIdList) {

        if(CollectionUtils.isEmpty(empIdList)) {
            return Collections.emptyMap();
        }

        // 查询EMP系统员工信息
        Map<String, EmployeeInfoThirdDto> employeeDataMap = employeeThirdService.getEmployeeDataMap(empIdList);

        Map<String,EmployeeInfoBusinessDto> result = new HashMap<>();
        for (EmployeeInfoThirdDto value : employeeDataMap.values()) {
            EmployeeInfoBusinessDto employeeInfoBusinessDto = BeanUtil.copyProperties(value, EmployeeInfoBusinessDto.class);
            // 组装机构信息
            buildOrgInfo(employeeInfoBusinessDto);
            result.put(employeeInfoBusinessDto.getId(), employeeInfoBusinessDto);
        }
        return result;
    }

    /*
     * @Description 根据员工id获取员工信息(state=1)
     * <AUTHOR>
     * @date 2024/12/3 16:44
     * @param empId
     * @return com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto
     */
    public EmployeeInfoBusinessDto getEmployeeCheckStateByEmpId(String empId) {
        if(StringUtils.isBlank(empId)) {
//            log.error("empId is null");
            return null;
        }

        // 查询EMP系统员工信息
        Optional<EmployeeInfoThirdDto> employeeInfoThirdDtoOptional = employeeThirdService.getEmployeeCheckStateByEmpId(empId);
        if (!employeeInfoThirdDtoOptional.isPresent()) {
            return null;
        }

        EmployeeInfoThirdDto employeeInfoThirdDto = employeeInfoThirdDtoOptional.get();
        EmployeeInfoBusinessDto result = BeanUtil.copyProperties(employeeInfoThirdDto, EmployeeInfoBusinessDto.class);

        // 补充org参数
        return buildOrgInfo(result);
    }



    /*
     * @Description 补充org参数
     * <AUTHOR>
     * @date 2024/12/3 15:53
     * @param result
     * @return com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto
     */
    private EmployeeInfoBusinessDto buildOrgInfo(EmployeeInfoBusinessDto result) {
        // log.info("补充org参数,参数为result={}", result);
        if(result == null) {
            return null;
        }

        //默认设置
        result.setIsZbReport(YesOrNoEnum.NO.getCode());

        // position职务判断 以及包装角色处理
        SmaRoleRelation smaRoleRelation = smaRoleRelationService.lambdaQuery()
                .eq(SmaRoleRelation::getRelationId, result.getId())
                .eq(SmaRoleRelation::getIsDefault, 1)
                .last("limit 1").one();
        // log.info("position职务判断设置,smaRoleRelation={}", smaRoleRelation);
        if(smaRoleRelation != null && StringUtils.isNotBlank(smaRoleRelation.getRoleId())) {
            SmaRoleOrg smaRoleOrg = smaRoleOrgService.lambdaQuery()
                    .eq(SmaRoleOrg::getRoleId, smaRoleRelation.getRoleId())
                    .last("limit 1")
                    .one();
            log.info("position职务判断设置,smaRoleOrg={}", smaRoleOrg);
            if(smaRoleOrg == null) {
                SmaRole smaRole = smaRoleService.lambdaQuery().eq(SmaRole::getId, smaRoleRelation.getRoleId()).one();
                if(smaRole != null) {
                    result.setPosition(smaRole.getName());
                    result.setIsZbReport(smaRole.getIsZbReport());
                }
            }else {
                // 包装角色org身份
                result.setOrgId(smaRoleOrg.getDeptId());
                Optional<OrgThirdDto> orgThirdDto = orgThirdService.getOrgByOrgId(smaRoleOrg.getDeptId());
                if(orgThirdDto.isPresent()) {
                    if (Objects.equals(OrgTypeEnum.DEPT.getType(),orgThirdDto.get().getType())) {
                        result.setOrgName(orgThirdDto.get().getName());
                    }
                }

                if (OrgConstant.MANAGE_DEPT_AREA.equals(result.getOrgName()) || OrgConstant.KA_AREA.equals(result.getOrgName())) {
                    result.setPosition(EmpPositionConstant.BUSINESS_AREA);
                }else if (OrgConstant.MANAGE_DEPT_BU.equals(result.getOrgName())) {
                    result.setPosition(EmpPositionConstant.BUSINESS_MAJOR_BU);
                }else if (OrgConstant.MANAGE_DEPT_SUB.equals(result.getOrgName())) {
                    result.setPosition(EmpPositionConstant.BUSINESS_MAJOR);
                } else if (OrgConstant.GJ_DEPT_AREA.equals(result.getOrgName())) {
                    result.setPosition(EmpPositionConstant.GJ_AREA);
                } else if (OrgConstant.GJ_DEPT_SUB.equals(result.getOrgName())) {
                    result.setPosition(EmpPositionConstant.GJ_MANAGER_NEW);
                } else if (OrgConstant.DX_DEPT.equals(result.getOrgName())) {
                    result.setPosition(EmpPositionConstant.BUSINESS_DX_MANAGER);
                } else {
                    if (EmpPositionConstant.BUSINESS_DX_MANAGER.equals(result.getPosition())
                            || EmpPositionConstant.DX_SQGW_MANAGER.equals(result.getPosition())) {
//                    result.setPosition(result.getPosition());
                    } else {
                        result.setPosition(EmpPositionConstant.BUSINESS_MANAGER);
                    }
                }

                // SDR特殊需求
                SmaRole smaRole = smaRoleService.lambdaQuery().eq(SmaRole::getId, smaRoleRelation.getRoleId()).one();
                if (PositionUtil.isSdr(smaRole.getName())) {
                    result.setPosition(PositionUtil.SDR);
                }else if (PositionUtil.isCc(smaRole.getName())) {
                    result.setPosition(PositionUtil.CC);
                }else if (PositionUtil.isSdrZg(smaRole.getName())) {
                    result.setPosition(PositionUtil.SDR_ZG);
                }else if (PositionUtil.isCcZg(smaRole.getName())) {
                    result.setPosition(PositionUtil.CC_ZG);
                }
            }
        }

        // 部门数据
        String orgId = result.getOrgId();
        if(orgId == null) {
            log.error("获取员工信息的时候orgId为空，result={}", result);
            return result;
        }
        Optional<OrgThirdDto> orgInfo = orgThirdService.getOrgByOrgId(orgId);
        if(orgInfo.isPresent()) {
            OrgThirdDto orgThirdDto = orgInfo.get();
            if(Objects.equals(OrgTypeEnum.DEPT.getType(), orgThirdDto.getType())) {
                result.setOrgName(orgThirdDto.getName());
            }

            //部门的上级数据：可能是事业部，也可能是分司
            String orgParentId = orgThirdDto.getParentId();
            if(orgParentId == null) {
                log.error("获取员工机构的时候org的parentId为空，orgThirdDto={}", orgThirdDto);
                return result;
            }
            Optional<OrgThirdDto> orgParentInfo = orgThirdService.getOrgByOrgId(orgParentId);
            if(orgParentInfo.isPresent()) {
                // 分司对象，后面要用
                OrgThirdDto subThirdDto = new OrgThirdDto();
                // 部门上级的对象：可能是事业部，也可能是分司
                OrgThirdDto orgParentDto = orgParentInfo.get();
                if(Objects.equals(OrgTypeEnum.BU.getType(), orgParentDto.getType())) {
                    // 事业部
                    result.setBuId(orgParentDto.getId());
                    result.setBuName(orgParentDto.getName());

                    // 分司
                    String subId = orgParentDto.getParentId();
                    if(subId == null) {
                        log.error("获取员工机构的时候分司id为空，orgParentDto={}",orgParentDto);
                        return result;
                    }
                    Optional<OrgThirdDto> subInfo = orgThirdService.getOrgByOrgId(subId);
                    if(subInfo.isPresent()) {
                        subThirdDto = subInfo.get();
                    }
                }else if (Objects.equals(OrgTypeEnum.SUB.getType(), orgParentDto.getType())) {
                    // 那么部门的上级就是分司  其实中间没有事业部
                    subThirdDto = orgParentDto;
                }

                // 分司赋值
                result.setSubId(subThirdDto.getId());
                result.setSubName(subThirdDto.getName());
            }

            // 区域
            OrgThirdDto areaThirdDto = orgThirdService.selectAreaByOrgId(orgId);
            result.setAreaId(areaThirdDto.getId());
            result.setAreaName(areaThirdDto.getName());

            //特殊情况区域总监 subId和areaId一样
            if(EmpPositionConstant.BUSINESS_AREA.equals(result.getPosition())) {
                result.setSubId(result.getAreaId());
                result.setSubName(result.getAreaName());
            }
        }
        return result;
    }

    public Boolean changeRoleByEmpIdAndOrgId(ChangeRoleBusinessDto changeRoleBusinessDto) {

        String empId = changeRoleBusinessDto.getEmpId();
        if(StringUtils.isBlank(empId)) {
            log.error("切换角色失败，员工id为空!");
            return Boolean.FALSE;
        }
        String orgId = changeRoleBusinessDto.getOrgId();
        String roleId = null;
        if(StringUtils.isBlank(orgId)) {
            SmaRole smaRole = smaRoleService.lambdaQuery()
                    .eq(SmaRole::getName, "客户代表")
                    .last("limit 1")
                    .one();
            if (smaRole != null) {
                roleId = smaRole.getId();
            }
        }else {
            SmaRoleOrg smaRoleOrg = smaRoleOrgService.lambdaQuery()
                    .eq(SmaRoleOrg::getDeptId, orgId)
                    .last("limit 1")
                    .one();
            if(smaRoleOrg != null) {
                roleId = smaRoleOrg.getRoleId();
            }
        }
        if(roleId == null) {
//            log.error("切换角色失败，没有找到对应角色id,changeRoleBusinessDto={}", JSON.toJSONString(changeRoleBusinessDto));
            return Boolean.FALSE;
        }
        roleRelationAppService.changeRole(empId, roleId);
        return Boolean.TRUE;
    }

    /*
     * @Description 企业微信登录 存入redis key-userId value-code
     * <AUTHOR>
     * @date 2025/2/7 14:36
     * @param userId
     * @param code
     * @return java.lang.Boolean
     */
    public Boolean saveCodeOfRedis(String userId,String code) {
        entVxCodeCacheHandler.setCacheData(userId,code);
        return Boolean.TRUE;
    }

    public String getCodeOfRedisByUserId(String userId) {
        return entVxCodeCacheHandler.get(userId);
    }

}
