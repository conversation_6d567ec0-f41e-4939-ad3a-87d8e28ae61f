package com.ce.scrm.center.service.business.entity.view.follow;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class FollowDetailBusinessView implements Serializable {

    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名
     */
    private String customerName;

    /**
     * 员工id
     */
    private String empId;

    private String empName;

    /**
     * 部门id
     */
    private String deptId;

    private String deptName;

    /**
     * 事业部id
     */
    private String buId;

    private String buName;

    /**
     * 分公司id
     */
    private String subId;

    private String subName;

    /**
     * 区域id
     */
    private String areaId;

    private String areaName;

    /**
     * 预算
     */
    private BigDecimal expectDealAmount;

    /**
     * 预计签单日期
     */
    private Date predictSignDate;

    /**
     * 预计签单金额
     */
    private Integer predictSignMoney;

    /**
     * 预计到账日期
     */
    private Date predictAccountDate;

    /**
     * 预计到账金额
     */
    private Integer predictAccountMoney;

    /**
     * 丢单原因 code
     */
    private String loseOrderReason;

//    /**
//     * 销售阶段
//     */
//    private String salesStage;

    /**
     * 跟进时间
     */
    private Date followTime;

    /**
     * 客户类型（成交、非成交）
     */
    private String custTypeStr;

    /**
     * 跟进阶段 name
     */
    private String salesStageStr;

}
