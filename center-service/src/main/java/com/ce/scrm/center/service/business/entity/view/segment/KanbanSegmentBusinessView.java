package com.ce.scrm.center.service.business.entity.view.segment;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 */
@Data
public class KanbanSegmentBusinessView implements Serializable {


	/**
	 * 分群id
	 */
	private String segmentId;

	/**
	 * 分群名称
	 */
	private String segmentName;

	/**
	 * 下发时间
	 */
	private Date distributeTime;

	/**
	 * 下发总量
	 */
	private Long segmentDistributeCount;

	/**
	 * 下发时已保护数量
	 */
	private Long distributeProtectedCount;

	/**
	 * 下发时已签单数量
	 */
	private Long distributeSignCount;

	/**
	 * 昨日已处理数量
	 */
	private Long yesterdayCustomTagCount;

	/**
	 * 昨日处理率（小数点后1位）
	 */
	private String yesterdayCustomTagPercent;

	/**
	 * 累计处理数量
	 */
	private Long accumulatedCustomTagCount;

	/**
	 * 累计处理率（小数点后1位）
	 */
	private String accumulatedCustomTagPercent;

	/**
	 * 昨日保护数量
	 */
	private Long yesterdayProtectedCount;

	/**
	 * 昨日保护率（小数点后1位）
	 */
	private String yesterdayProtectedPercent;

	/**
	 * 累计保护数量
	 */
	private Long accumulatedProtectedCount;

	/**
	 * 累计保护率（小数点后1位）
	 */
	private String accumulatedProtectedPercent;

	/**
	 * 昨日拜访数量
	 */
	private Long yesterdayVisitedCount;

	/**
	 * 昨日拜访率（小数点后1位）
	 */
	private String yesterdayVisitedPercent;

	/**
	 * 累计拜访数量
	 */
	private Long accumulatedVisitedCount;

	/**
	 * 累计拜访率（小数点后1位）
	 */
	private String accumulatedVisitedPercent;

	/**
	 * 昨日签单数量
	 */
	private Long yesterdaySignedCount;

	/**
	 * 昨日签单率（小数点后1位）
	 */
	private String yesterdaySignedPercent;

	/**
	 * 累计签单数量
	 */
	private Long accumulatedSignedCount;

	/**
	 * 累计签单率（小数点后1位）
	 */
	private String accumulatedSignedPercent;

}
