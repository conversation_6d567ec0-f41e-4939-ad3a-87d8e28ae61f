/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.form;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Date;
import java.util.Map;
import java.util.Objects;


/**
 * 获取表单提交数据明细接口的查询
 */
public class FormDataQuery extends BaseParam {

    private Long id;

    private Date startTime;

    private Date endTime;

    private Long minCostTime;

    private Long maxCostTime;

    private String device;

    private String browser;

    /**
     * 是否星标 0：非星标；1：星标
     */
    private int star;

    private Long distributeId;

    private String customerUserId;

    private int showTrash;

    public FormDataQuery(Long id) {
        this.id = id;
    }

    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> map = getBaseParamsMap();
        if (id != null) {
            map.put("id", String.valueOf(id));
        }
        if (Objects.nonNull(startTime)) {
            map.put("startTime", String.valueOf(startTime.getTime()));
        }
        if (Objects.nonNull(endTime)) {
            map.put("endTime", String.valueOf(endTime.getTime()));
        }
        if (Objects.nonNull(minCostTime)) {
            map.put("minCostTime", String.valueOf(minCostTime));
        }
        if (Objects.nonNull(maxCostTime)) {
            map.put("maxCostTime", String.valueOf(maxCostTime));
        }
        if (StrUtil.isNotEmpty(device)) {
            map.put("device", device);
        }
        if (StrUtil.isNotEmpty(browser)) {
            map.put("browser", browser);
        }
        if (Integer.valueOf(star) != 0) {
            map.put("star", String.valueOf(star));
        }
        if (distributeId != null) {
            map.put("distributeId", String.valueOf(distributeId));
        }
        if (StrUtil.isNotEmpty(customerUserId)) {
            map.put("customerUserId", customerUserId);
        }
        if (showTrash == 1 || showTrash == 0) {
            map.put("showTrash", String.valueOf(showTrash));
        }

        return map;

    }

    @Override
    public void validate() {
        if (id == null) {
            throw new KnownException("id 不能为空");
        }
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getCustomerUserId() {
        return customerUserId;
    }

    public void setCustomerUserId(String customerUserId) {
        this.customerUserId = customerUserId;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public Long getDistributeId() {
        return distributeId;
    }

    public void setDistributeId(Long distributeId) {
        this.distributeId = distributeId;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMaxCostTime() {
        return maxCostTime;
    }

    public void setMaxCostTime(Long maxCostTime) {
        this.maxCostTime = maxCostTime;
    }

    public Long getMinCostTime() {
        return minCostTime;
    }

    public void setMinCostTime(Long minCostTime) {
        this.minCostTime = minCostTime;
    }

    public int getShowTrash() {
        return showTrash;
    }

    public void setShowTrash(int showTrash) {
        this.showTrash = showTrash;
    }

    public int getStar() {
        return star;
    }

    public void setStar(int star) {
        this.star = star;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
}
