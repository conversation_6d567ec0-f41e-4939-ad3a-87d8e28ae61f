package com.ce.scrm.center.service.business.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 查询区域商机列表参数
 * @Author: lijinpeng
 * @Date: 2024/12/26 15:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaBusinessOpportunityBusinessDto {

    /**
     *  首次分配区域id
     */
    private String firstDistributionAreaId;

    /**
     *  首次分配分司id
     */
    private String firstDistributionSubId;

    /**
     * 商机创建日期-开始
     */
    private Date startCreateTime;

    /**
     * 商机创建日期-结束
     */
    private Date endCreateTime;

    /**
     * 当前页
     */
    private Integer currentPage;

    /**
     * 页大小
     */
    private Integer pageSize;

}
