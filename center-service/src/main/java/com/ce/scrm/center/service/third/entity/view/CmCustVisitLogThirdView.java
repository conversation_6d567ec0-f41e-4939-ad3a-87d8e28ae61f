package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/2/17 15:58
 */
@Data
public class CmCustVisitLogThirdView implements Serializable {

    /**
     * id
     */
    private String id;

    /**
     * uuid
     */
    private String uuId;

    /**
     * 商务年
     */
    private Integer year;

    /**
     * 商务月
     */
    private Integer month;

    /**
     * 商务天
     */
    private Integer day;

    /**
     * 自然年
     */
    private Integer natureYear;

    /**
     * 自然月
     */
    private Integer natureMonth;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户类型（已联系，签单，二次开发）
     */
    private Integer custType;

    /**
     * 客户类型名称
     */
    private String custTypeLable;

    /**
     * 联系人id
     */
    private String linkManId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 联系人名称
     */
    private String linkManName;

    /**
     * 联系人手机号
     */
    private String linkManMobile;

    /**
     * 商务代表id
     */
    private String salerId;

    /**
     * 商务部门id
     */
    private String bussdeptId;

    /**
     * 商务分司id
     */
    private String subcompanyId;

    /**
     * 商务区域id
     */
    private String areaId;

    /**
     * 沟通类型
     */
    private Integer visitType;

    /**
     * 沟通时间
     */
    private Date visitTime;

    /**
     * 跟进状态
     */
    private Integer visitStatus;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 坐席电话id
     */
    private String phoneId;

    /**
     * 打卡记录id
     */
    private String followRecordId;

    /**
     * 录入方式
     */
    private String recordWay;

    /**
     * 联系内容
     */
    private String content;

    /**
     * 陪访商务代表id
     */
    private String accompanySalerId;

    /**
     * 陪访商务部门id
     */
    private String accompanySalerDeptId;

    /**
     * 销售阶段
     */
    private String salesStage;

    /**
     * 线索跟进阶段
     */
    private String clueVisitStage;

    /**
     * 预期成交金额
     */
    private BigDecimal expectDealAmount;

    /**
     * 联系人职位
     */
    private String linkManPosition;

    /**
     * 下次跟进时间
     */
    private Date nextVisitTime;

    /**
     * 客户需求 逗号分隔
     */
    private String custDemandsList;

    /**
     * 修改次数
     */
    private Integer updateNums;
    /**
     * 客户预算
     */
    private BigDecimal budget;

    /**
     * 附件列表
     */
    private String attachmentsList;

    private Integer returnVisitState;
    private String returnVisitContent;
    private String returnVisitEmpId;
    private Date returnVisitTime;

    /**
     * 列表中-产品数量
     */
    private Long totalCount;

}
