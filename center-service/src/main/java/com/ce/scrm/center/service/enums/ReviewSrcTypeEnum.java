package com.ce.scrm.center.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审核来源类型枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
@Getter
@AllArgsConstructor
public enum ReviewSrcTypeEnum {

    SDR(1, "SDR"),
    CC(2, "CC");

    private final Integer code;
    private final String name;

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ReviewSrcTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReviewSrcTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据code获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getNameByCode(Integer code) {
        ReviewSrcTypeEnum enumValue = getByCode(code);
        return enumValue != null ? enumValue.getName() : null;
    }
} 