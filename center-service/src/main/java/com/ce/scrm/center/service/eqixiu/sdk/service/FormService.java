package com.ce.scrm.center.service.eqixiu.sdk.service;

import com.ce.scrm.center.service.eqixiu.sdk.common.Result;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.form.EnrollmentAuditStat;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.form.FormDataQuery;
import com.ce.scrm.center.service.eqixiu.sdk.support.HttpClient;
import com.ce.scrm.center.service.eqixiu.sdk.support.TokenCache;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 表单接口服务
 * <a href="https://hc.eqxiu.cn/doc/594/">...</a>
 *
 * <AUTHOR>
 */
public class FormService extends ConnectService {

    private static final String API_FORM_LIST = "/api/v1/editor/m/lp/data/list";
    private static final String API_FORM_DELETE = "/api/v1/editor/form/delete";
    private static final String API_FORM_AUDIT_STATIC = "/api/v1/editor/m/form/voteEnrollment/countV2";

    public FormService(Secret secret) {
        super(secret);
    }

    public FormService(Secret secret, TokenCache tokenCache) {
        super(secret, tokenCache);
    }

    public FormService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        super(secret, tokenCache, httpClient);
    }

    /**
     * 查询表单数据
     *
     * @param query
     * @return
     */
    public Result<List> findFormData(FormDataQuery query) {
        paramValidate(query);
        JSONObject obj = httpClient.httpGet(getApiURL(API_FORM_LIST), null, query.getParamsMap());
        printLog(obj, "查询表单数据失败:{}");
        return getResult(obj, List.class);
    }

    /**
     * 删除表单数据
     *
     * @param creationId
     * @param dataId
     * @return
     */
    public boolean deleteFormData(Long creationId, Long... dataId) {
        if (creationId == null || dataId == null) {
            logger.error("参数不能为空");
            return false;
        }
        Map<String, String> map = createParamMapWithToken();
        map.put("creationId", creationId.toString());
        map.put("dataIds", Arrays.stream(dataId).map(Object::toString).collect(Collectors.joining(",")));

        JSONObject result = httpClient.httpPost(getApiURL(API_FORM_DELETE, map), null, map);
        printLog(result, "删除表单数据失败:{}");
        return result.getSuccess();
    }

    /**
     * 获取报名审核统计
     *
     * @param creationId
     * @param openId
     * @return
     */
    public EnrollmentAuditStat getEnrollmentAuditStat(Long creationId, String openId) {
        if (creationId == null || openId == null) {
            logger.error("参数为空");
            return null;
        }
        Map<String, String> params = createParamMapWithToken();
        params.put("pid", String.valueOf(creationId));
        params.put("openId", openId);

        JSONObject result = httpClient.httpGet(getApiURL(API_FORM_AUDIT_STATIC), null, params);
        if (result.getSuccess()) {
            Map<String, Integer> data = result.getResultMap();
            return new EnrollmentAuditStat(data);
        }
        printLog(result, "获取报名统计失败:{}");
        return null;
    }


}
