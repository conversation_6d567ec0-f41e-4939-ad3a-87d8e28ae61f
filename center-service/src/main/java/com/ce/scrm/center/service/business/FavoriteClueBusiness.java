package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import cn.ce.cesupport.enums.favorites.ClueCustStatusEnum;
import cn.ce.cesupport.newcustclue.vo.ClueCustVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.entity.dto.*;
import com.ce.scrm.center.service.router.RouterContext;
import com.ce.scrm.center.service.third.entity.dto.CallStatusThirdDto;
import com.ce.scrm.center.service.third.entity.dto.favorite.FavoriteCluePageThirdDto;
import com.ce.scrm.center.service.third.invoke.CallCenterThirdService;
import com.ce.scrm.center.service.third.invoke.ClueAssignThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description: 收藏业务
 * @Author: xukang
 * @Date: 2025-01-16 10:10
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FavoriteClueBusiness {

	private final ClueAssignThirdService clueAssignThirdService;
	private final CallCenterThirdService callCenterThirdService;

	/**
	 * 收藏夹商务库容信息
	 */
	public FavoritesCapacityBusinessResDto capacity() {
		EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
		Assert.notNull(currentUser, "登录人获取失败");
		ImmutablePair<Integer, Integer> salerCapacityedCount = clueAssignThirdService.getSalerCapacityedCount(
			currentUser.getId(), currentUser.getSubId(), currentUser.getJobGrade());
		return FavoritesCapacityBusinessResDto.builder()
			.nowCapacityedCount(salerCapacityedCount.getLeft())
			.totalCapacityedCount(salerCapacityedCount.getRight())
			.build();
	}

	/**
	 * 收藏夹分页
	 */
	public Page<FavoritesPageBusinessResDto> page(FavoritesPageBusinessDto favoritesPageBusinessDto) {
		EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
		Assert.notNull(currentUser, "登录人获取失败");
		FavoriteCluePageThirdDto favoriteCluePageThirdDto = buildFavoritePageReqParam(favoritesPageBusinessDto, currentUser);
		Page<ClueCustVo> clueCustPage = clueAssignThirdService.getClueCustPage(favoriteCluePageThirdDto);
		List<ClueCustVo> records = clueCustPage.getRecords();
		Page<FavoritesPageBusinessResDto> resultPage = new Page<>();
		if (CollectionUtils.isNotEmpty(records)) {
			List<FavoritesPageBusinessResDto> list = BeanCopyUtils.convertToVoList(records, FavoritesPageBusinessResDto.class);
			resultPage.setRecords(list);
			resultPage.setPages(clueCustPage.getPages());
			resultPage.setTotal(clueCustPage.getTotal());
			resultPage.setCurrent(clueCustPage.getCurrent());
			resultPage.setSize(clueCustPage.getSize());
			List<String> phones = list.stream().map(FavoritesPageBusinessResDto::getTel).filter(StringUtils::isNotBlank).collect(Collectors.toList());

			CallStatusBusinessDto callStatusBusinessDto = CallStatusBusinessDto.builder()
				.empId(currentUser.getId())
				.subId(currentUser.getSubId())
				.phones(phones)
				.build();
			CallStatusThirdDto callStatus = callCenterThirdService.getCallStatus(callStatusBusinessDto);
			if (callStatus != null && CollectionUtils.isNotEmpty(list)) {
				Map<String, CallStatusThirdDto.CallCenterStatusThirdDto> callStatusMap = callStatus.getCallcenterStatusViewMap();
				list.forEach(item -> {
					if (MapUtils.isNotEmpty(callStatusMap)) {
						CallStatusThirdDto.CallCenterStatusThirdDto callCenterStatusThirdDto = callStatusMap.get(item.getTel());
						if (callCenterStatusThirdDto != null) {
							item.setCallStatus(callCenterStatusThirdDto.getCallStatus());
							item.setCallMessage(callCenterStatusThirdDto.getCallMessage());
							item.setLineCode(callCenterStatusThirdDto.getLineCode());
							item.setLineName(callCenterStatusThirdDto.getLineName());
							item.setLineType(callCenterStatusThirdDto.getLineType());
							item.setCallSubId(callCenterStatusThirdDto.getSubId());
						}
					}
				});
			}
			return resultPage;
		}
		return resultPage;
	}

	private FavoriteCluePageThirdDto buildFavoritePageReqParam(FavoritesPageBusinessDto favoritesPageBusinessDto, EmployeeInfoBusinessDto currentUser) {
		if (favoritesPageBusinessDto == null) {
			return null;
		}

		FavoriteCluePageThirdDto favoriteCluePageThirdDto = new FavoriteCluePageThirdDto();
		// 单独职位判断
		String position = currentUser.getPosition();

		if (position != null && !PositionUtil.checkSalerRole(position)) { // 非商务职位不能增加startTime和endTime
			favoriteCluePageThirdDto.setStartTime(favoritesPageBusinessDto.getStartTime());
			favoriteCluePageThirdDto.setEndTime(favoritesPageBusinessDto.getEndTime());
		}
		String areaId = currentUser.getAreaId();
		String subId = currentUser.getSubId();
		String deptId = currentUser.getOrgId();
		String salerId = currentUser.getId();
		String buId = currentUser.getBuId();
		String subIdSelect = favoritesPageBusinessDto.getSubId();
		String deptIdSelect = favoritesPageBusinessDto.getDeptId();
		String salerIdSelect = favoritesPageBusinessDto.getSalerId();
		String buIdSelect = favoritesPageBusinessDto.getBuId();
		if (PositionUtil.checkIsBusinessAreaZj(position)) { // 区域总监
			favoriteCluePageThirdDto.setAreaId(areaId);
			favoriteCluePageThirdDto.setSubId(subIdSelect);
			favoriteCluePageThirdDto.setBuId(buIdSelect);
			favoriteCluePageThirdDto.setDeptId(deptIdSelect);
			favoriteCluePageThirdDto.setSalerId(salerIdSelect);
			favoriteCluePageThirdDto.setStatus(ClueCustStatusEnum.SALER.getValue());
		} else if (PositionUtil.isBusinessMajor(position)) { // 分司总监
			favoriteCluePageThirdDto.setSubId(subId);
			favoriteCluePageThirdDto.setBuId(buIdSelect);
			favoriteCluePageThirdDto.setDeptId(deptIdSelect);
			favoriteCluePageThirdDto.setSalerId(salerIdSelect);
			favoriteCluePageThirdDto.setStatus(ClueCustStatusEnum.SALER.getValue());
			if (favoritesPageBusinessDto.getFrom() == 1) {
				favoriteCluePageThirdDto.setStatus(ClueCustStatusEnum.MAJORDOMO.getValue());
			}
		} else if (PositionUtil.isBusinessBu(position)) { // 事业部
			favoriteCluePageThirdDto.setBuId(buId);
			favoriteCluePageThirdDto.setDeptId(deptIdSelect);
			favoriteCluePageThirdDto.setSalerId(salerIdSelect);
			favoriteCluePageThirdDto.setStatus(ClueCustStatusEnum.SALER.getValue());
			if (favoritesPageBusinessDto.getFrom() == 1) {
				favoriteCluePageThirdDto.setStatus(ClueCustStatusEnum.BU.getValue());
			}
		} else if (PositionUtil.isBusinessManager(position) || EmpPositionConstant.BUSINESS_DX_MANAGER.equals(position)) { // 商务经理
			favoriteCluePageThirdDto.setDeptId(deptId);
			favoriteCluePageThirdDto.setSalerId(salerIdSelect);
			favoriteCluePageThirdDto.setStatus(ClueCustStatusEnum.SALER.getValue());
			if (favoritesPageBusinessDto.getFrom() == 1) {
				favoriteCluePageThirdDto.setStatus(ClueCustStatusEnum.MANAGER.getValue());
			}
		} else if (PositionUtil.isBusinessSaler(position) || EmpPositionConstant.BUSINESS_DX_SALER.equals(position)) { // 客户代表
			favoriteCluePageThirdDto.setSalerId(salerId);
			favoriteCluePageThirdDto.setStatus(ClueCustStatusEnum.SALER.getValue());
		} else {
			log.warn("职位不对，position: {}", currentUser.getPosition());
			return null;
		}

		// 为了防止传参错误返回错误数据(客户代表传了subId)，手动set非职位相关的参数，职位相关的参数单独根据登录人职位判断
		favoriteCluePageThirdDto.setLablFrom(favoritesPageBusinessDto.getLablFrom());
		favoriteCluePageThirdDto.setBussOppType(favoritesPageBusinessDto.getBussOppType());
		favoriteCluePageThirdDto.setCustName(favoritesPageBusinessDto.getCustName());
		favoriteCluePageThirdDto.setEntId(favoritesPageBusinessDto.getCustId()); // entId
		favoriteCluePageThirdDto.setAddress(favoritesPageBusinessDto.getAddress());
		favoriteCluePageThirdDto.setIndustryZhongqi(favoritesPageBusinessDto.getIndustryZhongqi());
		favoriteCluePageThirdDto.setClueMission(favoritesPageBusinessDto.getClueMission());
		favoriteCluePageThirdDto.setSalerClueFrom(favoritesPageBusinessDto.getSalerClueFrom());
		favoriteCluePageThirdDto.setSalerClueFromSub(favoritesPageBusinessDto.getSalerClueFromSub());
		favoriteCluePageThirdDto.setClueVisitStage(favoritesPageBusinessDto.getClueVisitStage());
		favoriteCluePageThirdDto.setSecondIndustry(favoritesPageBusinessDto.getSecondIndustry());
		favoriteCluePageThirdDto.setNextVisitTimeStr(favoritesPageBusinessDto.getNextVisitDate());

		favoriteCluePageThirdDto.setPageNum(favoritesPageBusinessDto.getPageNum());
		favoriteCluePageThirdDto.setPageSize(favoritesPageBusinessDto.getPageSize());
		// 防止接口pageSize、pageNum传null
		if (favoritesPageBusinessDto.getPageNum() == null) {
			favoriteCluePageThirdDto.setPageNum(1);
		}
		if (favoritesPageBusinessDto.getPageSize() == null) {
			favoriteCluePageThirdDto.setPageSize(10);
		}

		return favoriteCluePageThirdDto;
	}

}
