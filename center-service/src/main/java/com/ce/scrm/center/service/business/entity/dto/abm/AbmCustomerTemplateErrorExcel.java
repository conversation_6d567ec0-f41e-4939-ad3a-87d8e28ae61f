package com.ce.scrm.center.service.business.entity.dto.abm;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * ABM客户导入模板Excel实体
 * <AUTHOR>
 * @date 2025-07-10
 * @version 1.0.0
 */
@Data
public class AbmCustomerTemplateErrorExcel {
    
    /**
     * 客户名称（必填）
     */
    @ExcelProperty(value = "客户名称（必填）", index = 0)
    private String customerName;
    
    /**
     * 法人姓名（必填）
     */
    @ExcelProperty(value = "法人姓名（必填）", index = 1)
    private String legalPersonName;
    
    /**
     * 主联系人手机号（必填）
     */
    @ExcelProperty(value = "主联系人手机号（必填）", index = 2)
    private String phoneNumber;

	/**
	 * 其他手机号 (多个 手机号, 用英文分号隔开)
	 */
	@ExcelProperty(value = "其他手机号 (多个手机号, 用英文分号隔开)", index = 3)
	private String otherPhoneNumbers;
    
    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱", index = 4)
    private String email;

	/**
	 * 其他邮箱 (多个邮箱, 用英文分号隔开)
	 */
	@ExcelProperty(value = "其他邮箱 (多个邮箱, 用英文分号隔开)", index = 5)
	private String otherEmails;
    
    /**
     * 办公地址（通讯地址）
     */
    @ExcelProperty(value = "办公地址（通讯地址）", index = 6)
    private String officeAddress;

	/**
	 * leadsCode
	 */
	@ExcelProperty(value = "leads code（必填）", index = 7)
	private String leadsCode;

	@ExcelProperty(value = "更多信息", index = 8)
	private String moreInfo;

	/**
	 * 错误原因
	 */
	@ExcelProperty(value = "导入失败原因", index = 9)
	private String errorMsg;
}
