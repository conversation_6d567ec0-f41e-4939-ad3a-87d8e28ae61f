/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.share;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;

import java.util.Map;

public class ModifyCreationShareCmd extends BaseParam {

    /**
     * 作品id
     */
    private Long creationId;

    /**
     * 共享类型：1.个人 2.部门及一下数据 3全部
     */
    private Integer shareType;

    /**
     * 关联类型：2：成员 3：外部成员
     */
    private Integer relType;

    /**
     * 关联id
     */
    private String relId;

    /**
     * 关联名称
     */
    private String relName;

    @Override
    public Map<String, String> getParamsMap() {
        return getBaseParamsMap();
    }

    @Override
    public void validate() {
        validateOpenId();
        if (creationId == null) {
            throw new KnownException("creationId 不能为空");
        }
        if (shareType == null) {
            throw new KnownException("shareType 不能为空");
        }
        if (relType == null) {
            throw new KnownException("relType 不能为空");
        }
        if (relId == null) {
            throw new KnownException("relId 不能为空");
        }
        if (relName == null) {
            throw new KnownException("relName 不能为空");
        }
    }

    public Long getCreationId() {
        return creationId;
    }

    public void setCreationId(Long creationId) {
        this.creationId = creationId;
    }

    public Integer getShareType() {
        return shareType;
    }

    public void setShareType(Integer shareType) {
        this.shareType = shareType;
    }

    public Integer getRelType() {
        return relType;
    }

    public void setRelType(Integer relType) {
        this.relType = relType;
    }

    public String getRelId() {
        return relId;
    }

    public void setRelId(String relId) {
        this.relId = relId;
    }

    public String getRelName() {
        return relName;
    }

    public void setRelName(String relName) {
        this.relName = relName;
    }
}
