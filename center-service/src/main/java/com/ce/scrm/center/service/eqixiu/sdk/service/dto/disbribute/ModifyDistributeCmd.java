/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.disbribute;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

/**
 * 修改渠道名称
 *
 * <AUTHOR>
 */
public class ModifyDistributeCmd extends BaseParam {

    /**
     * 关联类型：1.渠道 2.成员 （这里只能传1）
     */
    private final int relType = 1;
    private final String id;

    /**
     * 渠道名称，最大长度为256
     */
    private final String name;

    public ModifyDistributeCmd(String id, String name, String openId) {
        this.id = id;
        this.name = name;
        setOpenId(openId);
    }

    @Override
    public Map<String, String> getParamsMap() {
        return getBaseParamsMap();
    }

    @Override
    public void validate() {
        validateOpenId();
        if (StrUtil.isEmpty(id)) {
            throw new KnownException("id 参数不能为空");
        }
        if (StrUtil.isEmpty(name) || name.length() > 256) {
            throw new KnownException("name 参数不正确");
        }
    }

    public int getRelType() {
        return relType;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }
}
