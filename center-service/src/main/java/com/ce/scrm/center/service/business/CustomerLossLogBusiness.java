package com.ce.scrm.center.service.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CustomerLossLog;
import com.ce.scrm.center.dao.entity.CustomerWillCirculation;
import com.ce.scrm.center.dao.service.CustomerLossLogService;
import com.ce.scrm.center.service.business.entity.dto.CirculationLossPageBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.CustomerLossLogBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.CustomerLossPageBusinessDto;
import com.ce.scrm.center.service.business.entity.view.CustomerLossPageBusinessView;
import com.ce.scrm.center.service.business.entity.view.SmaDictionaryItemView;
import com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * description: 流失客户类
 *
 * @author: liyechao
 * date: 2024/9/2.
 */
@Slf4j
@Service
public class CustomerLossLogBusiness {
    @Resource
    private SmaDictionaryItemBusiness smaDictionaryItemBusiness;
    @Resource
    private CustomerLossLogService customerLossLogService;
    @Resource
    private CirculationLossBusiness circulationLossBusiness;


    /**
     * Description: 保存流失记录
     * @author: JiuDD
     * @param lossHistoryBusinessDto 流失记录
     * @return boolean  保存成功返回true, 失败返回false
     * date: 2024/10/21 15:01
     */
    public boolean saveCustomerLossLog(CustomerLossLogBusinessDto lossHistoryBusinessDto) {
        if (Objects.isNull(lossHistoryBusinessDto) || Objects.isNull(lossHistoryBusinessDto.getCustId())) {
            log.error("保存流失记录失败, 客户ID为空.");
            return false;
        }
        // 从流失记录中获取客户及当时的保护商务信息
        CirculationLossPageBusinessDto circulationLossBusinessDto = new CirculationLossPageBusinessDto();
        circulationLossBusinessDto.setCustId(lossHistoryBusinessDto.getCustId());
        circulationLossBusinessDto.setOrigin(AssignCustSourceSpecialEnum.LOSS.getValue());
        CustomerWillCirculation oneCirculationLoss = circulationLossBusiness.getOneCirculationLoss(circulationLossBusinessDto);
        if (Objects.nonNull(oneCirculationLoss)) {
            CustomerLossLog lossHistory = getCustomerLossLog(lossHistoryBusinessDto, oneCirculationLoss);
            return customerLossLogService.save(lossHistory);
        }
        return false;
    }

    @NotNull
    private static CustomerLossLog getCustomerLossLog(CustomerLossLogBusinessDto lossHistoryBusinessDto, CustomerWillCirculation oneCirculationLoss) {
        CustomerLossLog lossHistory = new CustomerLossLog();
        lossHistory.setAreaId(oneCirculationLoss.getAreaId());
        lossHistory.setAreaName(oneCirculationLoss.getAreaName());
        lossHistory.setSubId(oneCirculationLoss.getSubId());
        lossHistory.setSubName(oneCirculationLoss.getSubName());
        lossHistory.setDeptId(oneCirculationLoss.getDeptId());
        lossHistory.setDeptName(oneCirculationLoss.getDeptName());
        lossHistory.setSalerId(oneCirculationLoss.getSalerId());
        lossHistory.setSalerName(oneCirculationLoss.getSalerName());
        lossHistory.setCustId(oneCirculationLoss.getCustId());
        lossHistory.setCustName(oneCirculationLoss.getCustName());
        lossHistory.setReason(lossHistoryBusinessDto.getReason());
        lossHistory.setPreDate(oneCirculationLoss.getPreDate());
        return lossHistory;
    }

    /**
     * 获取流失客户列表
     *
     * @param businessDto
     * @return
     */
    public Page<CustomerLossPageBusinessView> getCustomerList(CustomerLossPageBusinessDto businessDto) {
        if (Objects.isNull(businessDto)) {
            return new Page<>();
        }
        Page<CustomerLossLog> page = Page.of(businessDto.getPageNum(), businessDto.getPageSize());
        LambdaQueryChainWrapper<CustomerLossLog> lambdaQueryChainWrapper = customerLossLogService.lambdaQuery()
                .eq(StrUtil.isNotBlank(businessDto.getCustId()), CustomerLossLog::getCustId, businessDto.getCustId())
                .eq(StrUtil.isNotBlank(businessDto.getAreaId()), CustomerLossLog::getAreaId, businessDto.getAreaId())
                .eq(StrUtil.isNotBlank(businessDto.getSubId()), CustomerLossLog::getSubId, businessDto.getSubId())
                .eq(StrUtil.isNotBlank(businessDto.getDeptId()), CustomerLossLog::getDeptId, businessDto.getDeptId())
                .eq(StrUtil.isNotBlank(businessDto.getSalerId()), CustomerLossLog::getSalerId, businessDto.getSalerId())
                .like(StrUtil.isNotBlank(businessDto.getCustName()), CustomerLossLog::getCustName, businessDto.getCustName())
                .in(!CollectionUtils.isEmpty(businessDto.getLossReasons()), CustomerLossLog::getReason, businessDto.getLossReasons())
                .orderByDesc(CustomerLossLog::getPreDate);
        Page<CustomerLossLog> dbResultPage = lambdaQueryChainWrapper.page(page);
        Page<CustomerLossPageBusinessView> businessViewPage = BeanUtil.copyProperties(dbResultPage, Page.class);
        if (businessViewPage.getTotal() < 1) {
            return businessViewPage;
        }
        if (!CollectionUtils.isEmpty(dbResultPage.getRecords())) {
            Optional<List<SmaDictionaryItemView>> notCirculation = smaDictionaryItemBusiness.findByDictionaryId("CRM_LOSS_REASON");
            dbResultPage.getRecords().forEach(item -> {
                if (StrUtil.isNotBlank(item.getReason()) && notCirculation.isPresent()) {
                    notCirculation.get().stream()
                            .filter(i -> i.getCode().equals(item.getReason()))
                            .findFirst()
                            .ifPresent(i -> item.setReason(i.getName()));
                }
            });
        }
        businessViewPage.setRecords(CglibUtil.copyList(dbResultPage.getRecords(), CustomerLossPageBusinessView::new));
        return businessViewPage;
    }
}
