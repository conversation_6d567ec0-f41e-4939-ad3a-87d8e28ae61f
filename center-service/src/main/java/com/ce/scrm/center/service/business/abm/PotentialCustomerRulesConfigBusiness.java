package com.ce.scrm.center.service.business.abm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.PotentialCustomerMarketingRules;
import com.ce.scrm.center.dao.entity.query.PotentialWillingBehaviorSelectedVo;
import com.ce.scrm.center.dao.service.PotentialCustomerMarketingRulesService;
import com.ce.scrm.center.service.business.entity.view.PotentialCustomerMarketingRulesBusinessView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 跨境ABM 潜客规则后台配置表
 * <AUTHOR>
 * @date 2025/8/9 13:22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PotentialCustomerRulesConfigBusiness {

	private final PotentialCustomerMarketingRulesService potentialCustomerMarketingRulesService;

	/**
	 * 后台配置表的分页查询
	 * @param pageSize 每页数量
	 * @param pageNum 当前页数
	 * @return 分页
	 */
	public Page<PotentialCustomerMarketingRulesBusinessView> page(Integer pageSize, Integer pageNum) {
		Page<PotentialCustomerMarketingRules> page = Page.of(pageNum, pageSize);
		Page<PotentialCustomerMarketingRulesBusinessView> rs = new Page<>(pageNum, pageNum);
		Page<PotentialCustomerMarketingRules> rules = potentialCustomerMarketingRulesService.page(page);
		if (CollectionUtils.isEmpty(rules.getRecords())) {
			return new Page<>(pageNum, pageSize);
		}
		List<PotentialCustomerMarketingRulesBusinessView> potentialCustomerMarketingRulesBusinessViews = BeanCopyUtils.convertToVoList(rules.getRecords(), PotentialCustomerMarketingRulesBusinessView.class);
		rs.setTotal(rules.getTotal());
		rs.setPages(rules.getPages());
		rs.setCurrent(page.getCurrent());
		rs.setRecords(potentialCustomerMarketingRulesBusinessViews);
		return rs;
	}


	/**
	 * ABM项目，SDR/主管工作台、公海的意愿行为下拉筛选框列表
	 */
	public List<PotentialWillingBehaviorSelectedVo> getWillingBehaviorSelected() {
		return potentialCustomerMarketingRulesService.getWillingBehaviorSelected();
	}

}
