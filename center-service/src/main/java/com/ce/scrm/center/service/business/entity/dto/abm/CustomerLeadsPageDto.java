package com.ce.scrm.center.service.business.entity.dto.abm;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 客户leads
 *
 * @TableName customer_leads
 */
@Data
public class CustomerLeadsPageDto extends SignData implements Serializable {

	/**
	 * 页号
	 */
	private Integer pageNum = 1;

	/**
	 * 页码
	 */
	private Integer pageSize = 10;

	/**
	 * 客户id
	 */
	@NotBlank(message = "客户id不能为空")
	private String customerId;

	private static final long serialVersionUID = 1L;
}