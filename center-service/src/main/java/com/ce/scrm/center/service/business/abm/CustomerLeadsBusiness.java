package com.ce.scrm.center.service.business.abm;

import cn.ce.cesupport.enums.CertificateTypeEnum;
import cn.ce.cesupport.enums.CustomerCreateWayEnum;
import cn.ce.cesupport.enums.CustomerPresentStageEnum;
import cn.ce.cesupport.enums.CustomerTypeEnum;
import cn.ce.cesupport.enums.abm.AbmDistributeChanelEnum;
import cn.ce.cesupport.enums.abm.LeadsDistributeEnum;
import cn.ce.cesupport.enums.abm.LeadsImportSourceEnum;
import cn.ce.cesupport.framework.base.vo.EmployeeVO;
import cn.ce.cesupport.framework.base.vo.MapResultBean;
import cn.ce.cesupport.sma.dto.ClueToMyCustDto;
import cn.ce.cesupport.sma.service.SalerRoleAppService;
import cn.ce.sequence.api.request.SequenceIdRequest;
import cn.ce.sequence.api.service.SequenceService;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.*;
import com.ce.scrm.center.dao.service.CustomerLeadsService;
import com.ce.scrm.center.dao.service.PotentialCustomerMarketingRulesService;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerLeadsAddDto;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerLeadsImportOrDistributeDto;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerLeadsPageDto;
import com.ce.scrm.center.service.business.entity.response.BusinessCodeMessageEnum;
import com.ce.scrm.center.service.business.entity.response.BusinessResult;
import com.ce.scrm.center.service.business.entity.view.CustomerLeadsView;
import com.ce.scrm.center.service.enums.ConvertRelationEnum;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerAddNotValidThirdDto;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerUpdateDistributeChannelInfoThirdDto;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerUpdateLeadsInfoThirdDto;
import com.ce.scrm.center.service.third.entity.view.BigDataCompanyDetail;
import com.ce.scrm.center.service.third.entity.view.CustomerAddThirdView;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.invoke.BigDataThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerLinkmanThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.customer.dubbo.api.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity.view.CustomerAddDubboView;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/***
* 客户leads业务
* <AUTHOR>
* @date 2025/8/8 19:22
*/
@Slf4j
@Service
public class CustomerLeadsBusiness {

    @Resource
    private CustomerLeadsService customerLeadsService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private CustomerLinkmanThirdService customerLinkmanThirdService;

	@Resource
	private PotentialCustomerMarketingRulesService leadsConfigService;

	@DubboReference
	private SalerRoleAppService salerRoleAppService;
	@Resource
	private EmployeeInfoBusiness employeeInfoBusiness;
	@DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
	private ICustomerDubbo customerDubbo;
	@Resource
	private EmployeeThirdService employeeThirdService;
	@Resource
	private SequenceService sequenceService;



	/**
	 * 默认兜底leads code
	 * <p>
	 * leadscode的匹配逻辑优先级如下：
	 * 第一优先级：接口调用时带有leadscode
	 * 第二优先级：匹配线上表中的识别码
	 * 第三优先级：兜底leadscode  =  20999
	 * </p>
	 */
	private static final String DEFAULT_LEADS_CODE = "20999";
	@Autowired
	private BigDataThirdService bigDataThirdService;

	/***
     * 添加单条leads
     * @param customerLeadsAddDto
     * <AUTHOR>
     * @date 2025/7/17 10:44
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Long>
    **/
    public BusinessResult<Long> addCustomerLead(CustomerLeadsAddDto customerLeadsAddDto) {
		try {
			log.info("添加 {}", JSON.toJSONString(customerLeadsAddDto));
			CustomerLeads entity = new CustomerLeads();
			BeanUtil.copyProperties(customerLeadsAddDto, entity);
			boolean save = customerLeadsService.save(entity);
			if (!save) {
				log.error("添加leads失败 entity={}", JSON.toJSONString(entity));
				return BusinessResult.error(BusinessCodeMessageEnum.CUSTOMER_LEADS_SAVE_FAIL);
			} else {
				Long id = entity.getId();
				log.info("生成 leads id={}", id);
				CustomerUpdateLeadsInfoThirdDto updateLeadsInfoThirdDto = new CustomerUpdateLeadsInfoThirdDto();
				updateLeadsInfoThirdDto.setLeadsCode(entity.getLeadsCode());
				updateLeadsInfoThirdDto.setLeadsIntentCode(entity.getLeadsType());
				updateLeadsInfoThirdDto.setCustomerId(entity.getCustomerId());
				log.info("leads创建后更新客户表的Leads信息，请求参数:{}", JSON.toJSONString(updateLeadsInfoThirdDto));
				Boolean updateCustomerResult = customerThirdService.updateCustLeadsInfoByCid(updateLeadsInfoThirdDto);
				if (updateCustomerResult) {
					log.info("更新customer表的leads信息成功, customerId={}", entity.getCustomerId());
				}
				return BusinessResult.success(id);
			}
		} catch (Exception e) {
			log.error("添加 leads 失败, customerLeadsAddDto={}", JSON.toJSONString(customerLeadsAddDto), e);
			throw new RuntimeException(e);
		}
	}


    /***
     * 获取集合
     * @param customerId
     * <AUTHOR>
     * @date 2025/7/17 10:54
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.customer.dubbo.entity.view.abm.CustomerLeadsDubboView>>
    **/
    public List<CustomerLeadsView> getListByCustomerId(String customerId) {
        log.info("获取集合 customerId={}", customerId);
        List<CustomerLeads> list = customerLeadsService.list(new LambdaQueryWrapper<CustomerLeads>()
                .eq(CustomerLeads::getCustomerId, customerId));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }else {
            return BeanUtil.copyToList(list, CustomerLeadsView.class);
        }
    }

	/**
	 * 获取leads分页
	 * @param customerLeadsPageDto 客户id及分页信息
	 * @return leads pages
	 */
    public Page<CustomerLeadsView> getPageByCustomerId(CustomerLeadsPageDto customerLeadsPageDto) {
        log.info("获取分页 customerId={}", customerLeadsPageDto);
        if (org.apache.commons.lang3.StringUtils.isBlank(customerLeadsPageDto.getCustomerId())) {

            return new Page<>();
        }
        LambdaQueryWrapper<CustomerLeads> wrapper = new LambdaQueryWrapper<CustomerLeads>().eq(CustomerLeads::getCustomerId, customerLeadsPageDto.getCustomerId());
        Page<CustomerLeads> page = Page.of(customerLeadsPageDto.getPageNum(), customerLeadsPageDto.getPageSize());

        Page<CustomerLeads> leadsPage = customerLeadsService.page(page, wrapper);
        if (CollectionUtils.isEmpty(leadsPage.getRecords())) {
            // 转换为标准的Page对象
            Page<CustomerLeadsView> resultPage = new Page<>();
            resultPage.setRecords(new ArrayList<>());
            resultPage.setCurrent(customerLeadsPageDto.getPageNum());
            resultPage.setSize(customerLeadsPageDto.getPageSize());
            return resultPage;
        }

        // 转换为CustomerLeadsView的Page对象
        Page<CustomerLeadsView> resultPage = new Page<>();
        resultPage.setRecords(BeanUtil.copyToList(leadsPage.getRecords(), CustomerLeadsView.class));
        resultPage.setTotal(leadsPage.getTotal());
        resultPage.setCurrent(leadsPage.getCurrent());
        resultPage.setSize(leadsPage.getSize());
        resultPage.setPages(leadsPage.getPages());
        return resultPage;
    }

    /**
     * 获取活动id leads列表 Map
     * @param custIds 客户id列表
     * @return map
     */
    public Map<String, List<CustomerLeads>> getCustIdLeadsMap(List<String> custIds) {
        if (CollectionUtils.isEmpty(custIds)){
            return Collections.emptyMap();
        }
        List<CustomerLeads> leadsList = customerLeadsService.lambdaQuery().in(CustomerLeads::getCustomerId, custIds).list();
        if (CollectionUtils.isEmpty(leadsList)) {
            log.warn("customer leads not exist in customerIds = {}", custIds);
            return Collections.emptyMap();
        }

        return leadsList.stream().collect(Collectors.groupingBy(CustomerLeads::getCustomerId));
    }


	/**
	 * 创建客户(若customer表不存在)、创建leads(根据规则)、创建保护关系(下发客户)
	 * @param dto 入参信息
	 * @return 是否操作成功
	 */
	public Boolean customerLeadsHandle(CustomerLeadsImportOrDistributeDto dto) {
		if (dto == null) {
			return false;
		}
		boolean isFromCrmExistChannels = Objects.equals(dto.getLeadsImportFrom(), LeadsImportSourceEnum.INTENT_CLUE.getCode());
		// 创建客户
		List<ClueInfo> clueList = dto.getClueList();
		List<ClueInfo> clueListWithCid = dto.getClueListWithCid();
		List<ActivityClueInfo> activityClueInfos = dto.getActivityClueInfos();
		List<CustomerTemplateImportDto> customerTemplateImportDtos = dto.getCustomerTemplateImportDtos();
		if (!CollectionUtils.isEmpty(clueList)) {
			fieldsTolerance(clueList);
			createCustomerWhetherNameExist(clueList, "1024leads", isFromCrmExistChannels);
			Iterator<ClueInfo> iterator = clueList.iterator();
			while (iterator.hasNext()) {
				ClueInfo clue = iterator.next();
				if (StringUtils.isBlank(clue.getCustName())) {
					log.error("线索创建客户失败，线索信息={}", JSON.toJSONString(clue));
					iterator.remove();
				}
			}
		}
		if (!CollectionUtils.isEmpty(clueListWithCid)) {
			fieldsTolerance(clueListWithCid);
			boolean hasBlankCustId = clueListWithCid.stream().anyMatch(x -> StringUtils.isBlank(x.getCustomerId()) && StringUtils.isBlank(x.getCustId()));
			if (hasBlankCustId) {
				log.error("历史线索库有cid的线索列表处理，包含客户id为空的线索，线索信息={}", JSON.toJSONString(clueListWithCid));
				return false;
			}
		}
		if (!CollectionUtils.isEmpty(activityClueInfos)) {
			createCustomerWhetherNameExist(activityClueInfos, "1024leads", isFromCrmExistChannels);
			Iterator<ClueInfo> iterator = clueList.iterator();
			while (iterator.hasNext()) {
				ClueInfo clue = iterator.next();
				if (StringUtils.isBlank(clue.getCustName())) {
					log.error("营销活动效果线索创建客户失败，线索信息={}", clue);
					iterator.remove();
				}
			}
		}
		if (!CollectionUtils.isEmpty(customerTemplateImportDtos)) {
			createCustomerWhetherNameExist(customerTemplateImportDtos, "1024leads", isFromCrmExistChannels);
		}
		// 导入leads信息
		List<Long> addLeadsIds = importLeadsByConfig(dto);

		if (Objects.equals(dto.getLeadsImportFrom(), LeadsImportSourceEnum.HISTORY_CLUE.getCode())) {
			log.info("历史线索不参与下发, LeadsImportSourceEnum={}", dto.getLeadsImportFrom());
			return true;
		}
		// 下发leads
		distributeByConfig(dto, addLeadsIds);
		return true;
	}


	/**
	 * 字段容错，ClueInfo和CustomerLeads表的字段含义一致，但名称有差异
	 * @param clueList 线索列表
	 */
	private static void fieldsTolerance(List<ClueInfo> clueList) {
		for (ClueInfo clueInfo : clueList) {
			if (StringUtils.isNotBlank(clueInfo.getCustName()) && StringUtils.isBlank(clueInfo.getCustomerName())) {
				clueInfo.setCustomerName(clueInfo.getCustName());
			}
			if (StringUtils.isBlank(clueInfo.getCustName()) && StringUtils.isNotBlank(clueInfo.getCustomerName())) {
				clueInfo.setCustName(clueInfo.getCustomerName());
			}
			if (StringUtils.isNotBlank(clueInfo.getCustRequirement())) {
				clueInfo.setDemandRemark(clueInfo.getCustRequirement());
			}
			if (StringUtils.isNotBlank(clueInfo.getSource())) {
				clueInfo.setClueType(clueInfo.getSource());
			}
			if (StringUtils.isNotBlank(clueInfo.getCreaterId())) {
				clueInfo.setCreatedId(clueInfo.getCreaterId());
			}
			if (StringUtils.isNotBlank(clueInfo.getSex())) {
				clueInfo.setGender(clueInfo.getSex());
			}
			if (StringUtils.isNotBlank(clueInfo.getTelephone())) {
				clueInfo.setFixedPhone(clueInfo.getTelephone());
			}
			if (StringUtils.isNotBlank(clueInfo.getMail())) {
				clueInfo.setEmail(clueInfo.getMail());
			}
			if (StringUtils.isNotBlank(clueInfo.getProvince())) {
				clueInfo.setProvinceCode(clueInfo.getProvince());
			}
			if (StringUtils.isNotBlank(clueInfo.getCity())) {
				clueInfo.setCity(clueInfo.getCity());
			}
			if (StringUtils.isNotBlank(clueInfo.getDistrict())) {
				clueInfo.setDistrictCode(clueInfo.getDistrict());
			}
			if (StringUtils.isNotBlank(clueInfo.getVdevice())) {
				clueInfo.setClientType(clueInfo.getVdevice());
			}
			if (StringUtils.isNotBlank(clueInfo.getCustId())) {
				clueInfo.setCustomerId(clueInfo.getCustId());
			}
		}
	}

	/**
	 * 无论有没有客户名称是否存在，创建客户
	 * @param dto 客户名、手机号、客户id
	 * @return
	 */
	private List<CustomerCreateWhetherNameDto> createCustomerWhetherNameExistOne(List<CustomerCreateWhetherNameDto> list) {
		if (!CollectionUtils.isEmpty(list)) {
			for (CustomerCreateWhetherNameDto clueInfo : list) {
				String custName = clueInfo.getCustomerName();
				if (StringUtils.isBlank(custName)) { // 如果客户名称为空，则用发号器生成一个客户名称
					SequenceIdRequest sequenceIdRequest = new SequenceIdRequest();
					sequenceIdRequest.setBusinessType(10);
					String genCustNameBySequenceId = sequenceService.generateId(sequenceIdRequest).getData().toString();
					clueInfo.setCustomerName(genCustNameBySequenceId);
					log.info("发号器生成客户名，custName={}", genCustNameBySequenceId);
					CustomerAddNotValidThirdDto customerAddNotValidThirdDto = BeanCopyUtils.convertToVo(clueInfo, CustomerAddNotValidThirdDto.class);
					CustomerAddDubboView customerAddDubboView = customerThirdService.addCustomerNotSkbValid(customerAddNotValidThirdDto);
					if (customerAddDubboView != null) {
						clueInfo.setCustomerId(customerAddDubboView.getCustomerId());
					}
				} else { // 有客户名, 去客户库查客户
					Optional<CustomerDataThirdView> custInfoFromDb = customerThirdService.getCustomerDataByCustomerName(custName);
					if (!custInfoFromDb.isPresent()) { // 客户库没有该客户名。去搜客宝查
						BigDataCompanyDetail custInfoFromSKB = bigDataThirdService.getBigDataCustomerInfo(custName);
						if (custInfoFromSKB != null) { // 搜客宝有，通过搜客宝验证的添加客户方法来创建客户
							try {
								CustomerAddThirdView customerAddThirdView = customerThirdService.addCustomer(null, custInfoFromSKB.getEntname(), clueInfo.getPresentStage().getCode(), CustomerCreateWayEnum.KJ_LEADS.getCode(), "1024leads");
								if (!StringUtils.isBlank(customerAddThirdView.getCustomerId())) {
									clueInfo.setCustomerId(customerAddThirdView.getCustomerId());
								} else {
									log.error("创建客户有校验customerThirdService.addCustomer失败，{}", JSON.toJSONString(customerAddThirdView));
								}
							} catch (Exception e) {
								log.error("创建客户有校验customerThirdService.addCustomer异常={}，入参={}", e.getMessage(), JSON.toJSONString(list));
							}
						} else { // 搜客宝没有
							String customerNameByTimeStamp = clueInfo.getCustomerName() + System.currentTimeMillis();
							clueInfo.setCustomerName(customerNameByTimeStamp);
							CustomerAddNotValidThirdDto customerAddNotValidThirdDto = BeanCopyUtils.convertToVo(clueInfo, CustomerAddNotValidThirdDto.class);
							CustomerAddDubboView customerAddDubboView = customerThirdService.addCustomerNotSkbValid(customerAddNotValidThirdDto);
							if (customerAddDubboView != null) {
								clueInfo.setCustomerId(customerAddDubboView.getCustomerId());
							}
						}
					} else { // 客户库有
						CustomerDataThirdView fromDbCust = custInfoFromDb.get();
						clueInfo.setCustomerId(fromDbCust.getCustomerId());
					}
				}
				
			}
		}
		return list;
	}


	/**
	 * 无论有没有客户名称是否存在，创建客户。
	 * <p>传入的list必须包含custName字段、customerId字段，customerId、customerName可以没有值，但必须要有这几个字段</p>
	 * @param list 客户名、客户id
	 * @param opertor 操作人
	 * @param isFromCrmExistingChannels 是否来源于crm现有渠道
	 * @return 创建客户,回填给list里的customerId、customerName
	 */
	public <T> void createCustomerWhetherNameExist(List<T> list, String opertor, boolean isFromCrmExistingChannels) {
		if (CollectionUtils.isEmpty(list)) {
			return;
		}

		try {
			Class<?> clazz = list.get(0).getClass();
			Field nameField = clazz.getDeclaredField("customerName");
			Field idField = clazz.getDeclaredField("customerId");

			nameField.setAccessible(true);
			idField.setAccessible(true);

			for (T item : list) {
				String custName = (String) nameField.get(item);

				if (StringUtils.isBlank(custName)) { // 客户名为空 → 用发号器生成
					SequenceIdRequest req = new SequenceIdRequest();
					req.setBusinessType(10);
					String genName = sequenceService.generateId(req).getData().toString();
					nameField.set(item, genName);
					log.info("发号器生成客户名，custName={}", genName);

					CustomerAddNotValidThirdDto dto = BeanCopyUtils.convertToVo(item, CustomerAddNotValidThirdDto.class);
					fillCustomerField(opertor, dto);

					CustomerAddDubboView view = customerThirdService.addCustomerNotSkbValid(dto);
					if (view != null) {
						idField.set(item, view.getCustomerId());
					}
				} else { // 有客户名 → 查客户库
					Optional<CustomerDataThirdView> dbCust = customerThirdService.getCustomerDataByCustomerName(custName);
					if (!dbCust.isPresent()) { // 客户库没有 → 查搜客宝
						BigDataCompanyDetail skb = bigDataThirdService.getBigDataCustomerInfo(custName);
						if (skb != null) { // 搜客宝有 → 通过搜客宝验证的添加客户方法来创建客户
							CustomerPresentStageEnum stage = CustomerPresentStageEnum.CLUE;
							try {
								CustomerAddThirdView view = customerThirdService.addCustomer(null, skb.getEntname(), stage.getCode(), CustomerCreateWayEnum.KJ_LEADS.getCode(), opertor);
								if (StringUtils.isNotBlank(view.getCustomerId())) {
									idField.set(item, view.getCustomerId());
								} else {
									log.error("创建客户有校验失败，{}", JSON.toJSONString(view));
								}
							} catch (Exception e) {
								log.error("创建客户有校验异常={}，入参={}", e.getMessage(), JSON.toJSONString(list));
							}
						} else { // 搜客宝没有 → 时间戳创建
							String nameWithTs = custName + System.currentTimeMillis();
							if (isFromCrmExistingChannels) {
								String custNameRegex = "([\\u4E00-\\u9FA5]|\\（|\\）|[0-9]|〇|《|》|—|、){5,100}";
								if (!custName.matches(custNameRegex)) {
									SequenceIdRequest req = new SequenceIdRequest();
									req.setBusinessType(10);
									nameWithTs= sequenceService.generateId(req).getData().toString();
								}
							}
							nameField.set(item, nameWithTs);
							CustomerAddNotValidThirdDto dto = BeanCopyUtils.convertToVo(item, CustomerAddNotValidThirdDto.class);
							fillCustomerField(opertor, dto);
							CustomerAddDubboView view = customerThirdService.addCustomerNotSkbValid(dto);
							if (view != null) {
								idField.set(item, view.getCustomerId());
							}
						}
					} else { // 客户库有，直接赋值
						idField.set(item, dbCust.get().getCustomerId());
					}
				}
			}
		} catch (NoSuchFieldException | IllegalAccessException e) {
			log.error("createCustomerWhetherNameExist方法反射获取字段异常={}", e.getMessage());
		}
	}


	/**
	 * 根据配置导入leads. leads里面要创建好标签
	 */
	private List<Long> importLeadsByConfig(CustomerLeadsImportOrDistributeDto dto) {
		// 去后台配置表查到leads_code，开始导入leads。把leads_code回写到ClueInfo类
		List<Long> leadsIdList = new ArrayList<>();
		try {
			Integer leadsImportFrom = dto.getLeadsImportFrom();
			log.info("根据配置导入leads, leadsImportFrom={}", LeadsImportSourceEnum.of(leadsImportFrom).getName());
			Set<CustomerLeadsAddDto> customerLeadsAddDtos = new HashSet<>();

			if (!CollectionUtils.isEmpty(dto.getActivityClueInfos())) {
				List<CustomerLeadsAddDto> tempDtos = BeanCopyUtils.convertToVoList(dto.getActivityClueInfos(), CustomerLeadsAddDto.class);
				assembleLeadsParams(tempDtos, 1, dto.getLeadsImportFrom());
				customerLeadsAddDtos.addAll(tempDtos);
			}
			if (!CollectionUtils.isEmpty(dto.getClueList())) {
				List<CustomerLeadsAddDto> tempDtos = BeanCopyUtils.convertToVoList(dto.getClueList(), CustomerLeadsAddDto.class);
				assembleLeadsParams(tempDtos, 0, dto.getLeadsImportFrom());
				customerLeadsAddDtos.addAll(tempDtos);
			}
			if (!CollectionUtils.isEmpty(dto.getClueListWithCid())) {
				List<CustomerLeadsAddDto> tempDtos = BeanCopyUtils.convertToVoList(dto.getClueListWithCid(), CustomerLeadsAddDto.class);
				assembleLeadsParams(tempDtos, 0, dto.getLeadsImportFrom());
				customerLeadsAddDtos.addAll(tempDtos);
			}
			if (!CollectionUtils.isEmpty(dto.getCustomerTemplateImportDtos())) {
				List<CustomerLeadsAddDto> tempDtos = BeanCopyUtils.convertToVoList(dto.getCustomerTemplateImportDtos(), CustomerLeadsAddDto.class);
				assembleLeadsParams(tempDtos, 1, dto.getLeadsImportFrom());
				customerLeadsAddDtos.addAll(tempDtos);
			}
			log.info("开始生成leads,参数信息={}", JSON.toJSONString(customerLeadsAddDtos));
			for (CustomerLeadsAddDto customerLeadsAddDto : customerLeadsAddDtos) {
				BusinessResult<Long> leadsIdRs = addCustomerLead(customerLeadsAddDto);
				if (!leadsIdRs.checkSuccess()) {
					log.error("addCustomerLead，创建客户leads失败，参数为:{}，返回数据为:{}", JSON.toJSONString(customerLeadsAddDto), JSON.toJSONString(leadsIdRs));
				} else {
					leadsIdList.add(leadsIdRs.getData());
				}
			}
		} catch (Exception leadsAddEx) {
			log.error("add leads interface error, {}", leadsAddEx.getMessage());
		}
		return leadsIdList;
	}

	/**
	 * 组装创建leads的参数
	 * @param tempDtos leads创建参数
	 */
	public void assembleLeadsParams(List<CustomerLeadsAddDto> tempDtos, Integer dataFromSource, Integer leadsImportFrom) {
		tempDtos.forEach(x -> {
			x.setDataFromSource(dataFromSource);
			String leadsCode = x.getLeadsCode();
			PotentialCustomerMarketingRules rules = leadsConfigService.lambdaQuery()
				.isNotNull(PotentialCustomerMarketingRules::getIdentifierCode)
				.isNotNull(PotentialCustomerMarketingRules::getIdentifierType)
				.and(wrapper -> wrapper
					.eq(StringUtils.isNotBlank(x.getClueType()), PotentialCustomerMarketingRules::getIdentifierCode, x.getClueType())
					.or()
					.eq(StringUtils.isNotBlank(x.getChannelSlug()), PotentialCustomerMarketingRules::getIdentifierCode, x.getChannelSlug())
				)
				.last("limit 1")
				.one();
			if (StringUtils.isBlank(leadsCode)) {
				if (rules == null) {
					log.error("[忽略,,,], 没有leadsCode并且没有根据识别码找到对应的leadsCode，走默认leadsCode分配规则={}", DEFAULT_LEADS_CODE);
					leadsCode = DEFAULT_LEADS_CODE;
					rules = leadsConfigService.lambdaQuery().eq(PotentialCustomerMarketingRules::getSourceCode, DEFAULT_LEADS_CODE).last("limit 1").one();
				} else {
					log.info("根据识别码和识别码类型找到leads配置规则, rules={}", JSON.toJSONString(rules));
					leadsCode = rules.getSourceCode();
				}
				x.setLeadsSource(rules.getSourceName());
				x.setLeadsDesc(rules.getSourceDesc());
				x.setLeadsType(rules.getIntentCode() + "-" + rules.getIntentName());
			} else {
				PotentialCustomerMarketingRules getByLeadsCode = leadsConfigService.lambdaQuery().eq(PotentialCustomerMarketingRules::getSourceCode, leadsCode).last("limit 1").one();
				if (getByLeadsCode == null) {
					log.error("leadsCode={}没有找到对应的leads配置规则,请检查leadsCode是否填写正确", leadsCode);
				} else {
					x.setLeadsSource(getByLeadsCode.getSourceName());
					x.setLeadsDesc(getByLeadsCode.getSourceDesc());
					x.setLeadsType(rules.getIntentCode() + "-" + rules.getIntentName());
				}
			}
			x.setLeadsCode(leadsCode);
			x.setCreateTime(new Date());
		});
	}

	/**
	 * leads线索分配
	 * <p>要不要分配，给哪个角色分配，怎么分，轮询分</p>
	 */
	public void distributeByConfig(CustomerLeadsImportOrDistributeDto dto, List<Long> addLeadsIds) {
		// 判断是否要分发leads，分发给谁：CC SDR 分司商务。此时已经建好leads信息，一定会有leads_code，根据leads_code就可以查询到分发给谁
		List<ClueInfo> clueList = dto.getClueList();
		List<ClueInfo> clueListWithCid = dto.getClueListWithCid();
		List<ActivityClueInfo> activityClueList = dto.getActivityClueInfos();
		List<CustomerTemplateImportDto> importCustLeadsList = dto.getCustomerTemplateImportDtos();

		List<String> leadsCodeSetOne = CollectionUtils.isEmpty(clueList) ? Collections.emptyList() : Lists.transform(clueList, ClueInfo::getLeadsCode);
		List<String> leadsCodeSetTwo = CollectionUtils.isEmpty(clueListWithCid) ? Collections.emptyList() : Lists.transform(clueListWithCid, ClueInfo::getLeadsCode);
		List<String> leadsCodeThree = CollectionUtils.isEmpty(activityClueList) ? Collections.emptyList() : Lists.transform(activityClueList, ActivityClueInfo::getLeadsCode);
		List<String> leadsCodeFour = CollectionUtils.isEmpty(importCustLeadsList) ? Collections.emptyList() : Lists.transform(importCustLeadsList, CustomerTemplateImportDto::getLeadsCode);

		List<String> leadsCodeList = Stream.of(leadsCodeSetOne, leadsCodeSetTwo, leadsCodeThree, leadsCodeFour)
			.flatMap(Collection::stream)
			.filter(StringUtils::isNotBlank)
			.distinct()
			.collect(Collectors.toList());
		// leadsCode和分配角色的对应关系
		Map<String, String> leadsCodeDistributeRoleMap;
		// L: 客户id, M: leadsCode, R: 分配角色名称
		List<Triple<List<String>, String, String>> tripleList = new ArrayList<>();

		if (!CollectionUtils.isEmpty(leadsCodeList)) {
			leadsCodeDistributeRoleMap = Optional.ofNullable(leadsConfigService.lambdaQuery()
					.select(PotentialCustomerMarketingRules::getSourceCode, PotentialCustomerMarketingRules::getAllocateRole)
					.in(PotentialCustomerMarketingRules::getSourceCode, leadsCodeList)
					.list()).orElse(Collections.emptyList())
				.stream()
				.collect(Collectors.toMap(
					PotentialCustomerMarketingRules::getSourceCode,
					PotentialCustomerMarketingRules::getAllocateRole,
					(v1, v2) -> v1
				));
			// 处理CRM现有线索列表
			processClueList(clueList, leadsCodeDistributeRoleMap, tripleList);

			// 处理包含cid的线索列表
			processClueList(clueListWithCid, leadsCodeDistributeRoleMap, tripleList);

			// 处理营销效果线索列表
			processActivityClueList(activityClueList, leadsCodeDistributeRoleMap, tripleList);

			// 处理导入leads列表
			processCustomerImportLeadsList(importCustLeadsList, leadsCodeDistributeRoleMap, tripleList);
		}

		// 跨境53KF，有一种情况需要单独处理：leadsCode=20999
		Map<String, String> leadsCodeEmpMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(clueList)) {
			for (ClueInfo clueInfo : clueList) {
				String createrId = clueInfo.getCreaterId();
				leadsCodeEmpMap.put(clueInfo.getLeadsCode(), createrId);
			}
		}
		distribute2Emp(tripleList, leadsCodeEmpMap, addLeadsIds);
	}


	/**
	 * leads分配，创建保护关系
	 * @param customerId 客户id
	 * @param empId 分发给谁，就是谁的员工id。只有可能是CC、SDR、商务
	 * @param addLeadsIds 添加leads成功后的leads表的id
	 */
	public boolean executeDistribute(String customerId, String empId, List<Long> addLeadsIds) {
		ClueToMyCustDto clueToMyCustDto = new ClueToMyCustDto();
		clueToMyCustDto.setCustId(customerId);
		EmployeeInfoBusinessDto employeeVO = employeeInfoBusiness.getEmployeeInfoByEmpId(empId);
		clueToMyCustDto.setEmployee(BeanCopyUtils.convertToVo(employeeVO, EmployeeVO.class));
		clueToMyCustDto.setConvertType(ConvertRelationEnum.ABM_ASSIGN_SALER.getValue());
		try {
			log.info("leads分配，创建保护关系，salerRoleAppService#clueToMyCustObject,入参={}", JSON.toJSONString(clueToMyCustDto));
			MapResultBean mapResultBean = salerRoleAppService.clueToMyCustObject(clueToMyCustDto);
			if (null != mapResultBean && null != mapResultBean.getData() && mapResultBean.getStatus() == 101) {
				if (!CollectionUtils.isEmpty(addLeadsIds)) { // 回填leads的下发时间
					Date now = new Date();
					customerLeadsService.lambdaUpdate().in(CustomerLeads::getId, addLeadsIds).set(CustomerLeads::getDispatchTime, now).update();
					CustomerUpdateDistributeChannelInfoThirdDto distributeChannelInfoThirdDto = new CustomerUpdateDistributeChannelInfoThirdDto();
					distributeChannelInfoThirdDto.setCustomerId(customerId);
					distributeChannelInfoThirdDto.setDistributeTime(now);
					distributeChannelInfoThirdDto.setDistributeChannel(AbmDistributeChanelEnum.LEADS_FLOW.getCode());
					distributeChannelInfoThirdDto.setOperator("1024distribute");
					customerThirdService.updateCustDistributeChannelByCid(distributeChannelInfoThirdDto);
				} else {
					log.error("下发创建保护关系失败，原因：{}", mapResultBean.getMsg());
					return false;
				}
				return true;
			}
		} catch (Exception e) {
			log.error("leads下发创建保护关系失败，custId={}, sdrId={}, errorMsg={}", customerId, JSON.toJSONString(employeeVO), e.getMessage());
		}
		return false;
	}

	/**
	 * 给SDR/CC/商务分配客户
	 * @param triples Left: 客户列表, Midle: leadsCode, Right: 角色id
	 * @param leadsCodeEmpIdMap K:leadsCode V:直接分配的员工id。如果不是直接分配到对应的员工id，此参数不用传
	 */
	public void distribute2Emp(List<Triple<List<String>, String, String>> triples, Map<String, String> leadsCodeEmpIdMap, List<Long> addLeadsIds) {
		if (CollectionUtils.isEmpty(triples)) {
			log.warn("下发组装参数错误, triples = null");
			return;
		}

		// 收集所有客户ID用于日志记录
		List<String> allCustIds = new ArrayList<>();
		for (Triple<List<String>, String, String> triple : triples) {
			List<String> cids = triple.getLeft();
			if (!CollectionUtils.isEmpty(cids)) {
				allCustIds.addAll(cids);
			}
		}
		log.info("分配的所有客户id={}", allCustIds);

		// 收集所有角色对应的客户和员工信息
		Map<String, List<String>> roleToCustomers = new HashMap<>();
		Map<String, List<String>> roleToEmpIds = new HashMap<>();

		for (Triple<List<String>, String, String> triple : triples) {
			List<String> currentCustIds = triple.getLeft();
			String leadsCode = triple.getMiddle();
			String distributeRoleName = triple.getRight();

			if (StringUtils.isBlank(distributeRoleName) ||
				Objects.equals(distributeRoleName, LeadsDistributeEnum.OPEN_SEA.getName())) {
				continue;
			}

			LeadsDistributeEnum leadsDistributeEnum = LeadsDistributeEnum.nameOf(distributeRoleName);
			if (Objects.isNull(leadsDistributeEnum)) {
				log.warn("未知的角色名: {}", distributeRoleName);
				continue;
			}

			if (Objects.equals(leadsDistributeEnum, LeadsDistributeEnum.CC) ||
				Objects.equals(leadsDistributeEnum, LeadsDistributeEnum.SDR)) {

				List<EmployeeDataThirdView> employeeVos = employeeThirdService.selectListByOrgIds(Collections.singletonList(leadsDistributeEnum.getOrgId()));
				List<String> empIds = Lists.transform(employeeVos, EmployeeDataThirdView::getId);

				if (CollectionUtils.isEmpty(empIds)) {
					log.warn("未找到该角色下的员工，角色名={}", distributeRoleName);
					continue;
				}

				roleToEmpIds.put(distributeRoleName, empIds);
				roleToCustomers.computeIfAbsent(distributeRoleName, k -> new ArrayList<>()).addAll(currentCustIds);
			}
		}

		// 开始公平分配
		for (Map.Entry<String, List<String>> entry : roleToCustomers.entrySet()) {
			String roleName = entry.getKey();
			List<String> custIds = entry.getValue();
			List<String> empIds = roleToEmpIds.get(roleName);

			if (CollectionUtils.isEmpty(custIds) || CollectionUtils.isEmpty(empIds)) {
				continue;
			}

			int empSize = empIds.size();
			int custSize = custIds.size();

			// 计算每个员工应得客户数（向下取整）
			int avgPerEmp = custSize / empSize;
			int extra = custSize % empSize;

			int index = 0;
			for (int i = 0; i < empSize; i++) {
				int count = avgPerEmp + (i < extra ? 1 : 0); // 多出来的客户优先分配给前面员工
				for (int j = 0; j < count; j++) {
					if (index >= custIds.size()) break;
					String customerId = custIds.get(index++);
					String empId = empIds.get(i);
					log.info("分配客户 - 客户ID: {}, 员工ID: {}, 角色: {}", customerId, empId, roleName);
					executeDistribute(customerId, empId, addLeadsIds);
				}
			}
		}
	}


	private void processClueList(List<ClueInfo> clueList, Map<String, String> leadsCodeDistributeRoleMap,
		List<Triple<List<String>, String, String>> tripleList) {
		if (CollectionUtils.isEmpty(clueList)) {
			return;
		}

		for (ClueInfo clueInfo : clueList) {
			String customerId = clueInfo.getCustomerId();
			String leadsCode = clueInfo.getLeadsCode();
			String allocateRole = leadsCodeDistributeRoleMap.get(leadsCode);

			if (StringUtils.isNotBlank(leadsCode) && StringUtils.isNotBlank(allocateRole)) { // 查找已存在的triple
				Triple<List<String>, String, String> existingTriple = null;
				for (Triple<List<String>, String, String> triple : tripleList) {
					if (Objects.equals(triple.getRight(), allocateRole) && Objects.equals(triple.getMiddle(), leadsCode)) {
						existingTriple = triple;
						break;
					}
				}

				if (existingTriple != null) { // 如果已存在，添加客户ID到列表
					existingTriple.getLeft().add(customerId);
				} else {
					List<String> customerIdsList = new ArrayList<>();
					customerIdsList.add(customerId);
					tripleList.add(Triple.of(customerIdsList, leadsCode, allocateRole));
				}
			}
		}
	}
	private void processActivityClueList(List<ActivityClueInfo> activityClueList, Map<String, String> leadsCodeDistributeRoleMap, List<Triple<List<String>, String, String>> tripleList) {
		if (CollectionUtils.isEmpty(activityClueList)) {
			return;
		}

		for (ActivityClueInfo activityClueInfo : activityClueList) {
			String customerId = activityClueInfo.getCustomerId();
			String leadsCode = activityClueInfo.getLeadsCode();
			String allocateRole = leadsCodeDistributeRoleMap.get(leadsCode);

			if (StringUtils.isNotBlank(leadsCode) && StringUtils.isNotBlank(allocateRole)) { // 查找已存在的triple
				Triple<List<String>, String, String> existingTriple = null;
				for (Triple<List<String>, String, String> triple : tripleList) {
					if (Objects.equals(triple.getRight(), allocateRole) && Objects.equals(triple.getMiddle(), leadsCode)) {
						existingTriple = triple;
						break;
					}
				}

				if (existingTriple != null) { // 如果已存在，添加客户ID到列表
					existingTriple.getLeft().add(customerId);
				} else { // 创建新的triple
					List<String> customerIdsList = new ArrayList<>();
					customerIdsList.add(customerId);
					tripleList.add(Triple.of(customerIdsList, leadsCode, allocateRole));
				}
			}
		}
	}

	/**
	 * 绿化后的数据导入
	 * @param customerTemplateImportDtos
	 * @param leadsCodeDistributeRoleMap
	 * @param tripleList
	 */
	private void processCustomerImportLeadsList(List<CustomerTemplateImportDto> customerTemplateImportDtos, Map<String, String> leadsCodeDistributeRoleMap, List<Triple<List<String>, String, String>> tripleList) {
		if (CollectionUtils.isEmpty(customerTemplateImportDtos)) {
			return;
		}
		for (CustomerTemplateImportDto clueInfo : customerTemplateImportDtos) {
			String customerId = clueInfo.getCustomerId();
			String leadsCode = clueInfo.getLeadsCode();
			String allocateRole = leadsCodeDistributeRoleMap.get(leadsCode);

			if (StringUtils.isNotBlank(leadsCode) && StringUtils.isNotBlank(allocateRole)) { // 查找已存在的triple
				Triple<List<String>, String, String> existingTriple = null;
				for (Triple<List<String>, String, String> triple : tripleList) {
					if (Objects.equals(triple.getRight(), allocateRole) && Objects.equals(triple.getMiddle(), leadsCode)) {
						existingTriple = triple;
						break;
					}
				}

				if (existingTriple != null) {
					existingTriple.getLeft().add(customerId);
				} else {
					List<String> customerIdsList = new ArrayList<>();
					customerIdsList.add(customerId);
					tripleList.add(Triple.of(customerIdsList, leadsCode, allocateRole));
				}
			}
		}
	}

	private void fillCustomerField(String opertor, CustomerAddNotValidThirdDto dto) {
		if (dto.getCustomerType() == null) {
			dto.setCustomerType(CustomerTypeEnum.COMPANY.getCode());
		}
		if (dto.getCreator() == null) {
			dto.setCreator("1024");
		}
		if (dto.getCreateWay() == null) {
			dto.setCreateWay(CustomerCreateWayEnum.KJ_LEADS.getCode());
		}
		if (dto.getCertificateType() == null) {
			dto.setCertificateType(CertificateTypeEnum.BUSINESS_LICENSE.getCode());
			dto.setCertificateCode("");
		}
		if (opertor != null) {
			dto.setOperator(opertor);
		}
	}

}
