package com.ce.scrm.center.service.third.entity.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerLeadsAddThirdDto implements Serializable {

	/**
	 * 数据来源 0:crm原有渠道  1:营销活动  2:手动录入
	 */
	private Integer dataFromSource;

	/**
	 * 客户id
	 */
	private String customerId;

	/**
	 * leads类别
	 */
	private String leadsType;

	/**
	 * leads code
	 */
	private String leadsCode;

	/**
	 * leads 来源
	 */
	private String leadsSource;

	/**
	 * 省
	 */
	private String provinceCode;
	/**
	 *  市
	 */
	private String cityCode;


	/**
	 * 详细地址
	 */
	private String address;
	/**
	 * 线索类型
	 */
	private String clueType;

	/**
	 * 渠道
	 */
	private String channel;

	/**
	 * 活动
	 */
	private String activity;

	/**
	 * 端口
	 */
	private String clientType;



	/**
	 * 意向产品
	 */
	private String intentionProduct;

	/**
	 * 需求备注
	 */
	private String demandRemark;

	/**
	 * 附件url
	 */
	private String attachmentUrl;

	/**
	 * leads url
	 */
	private String leadsUrl;

	/**
	 * Leads来源说明
	 */
	private String leadsDesc;
	/**
	 * 联系人
	 */
	private String linkmanName;
	/**
	 * 手机号
	 */
	private String mobile;

	/**
	 * 客户名称
	 */
	private String customerName;


	/**
	 * 创建者
	 */
	private String createdId;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 分发时间
	 */
	private Date dispatchTime;

	/**
	 * 联系人ID
	 */
	private String contactId;


	/**
	 * 性别：1、未知，2、男，3、女
	 */
	private String gender;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 职位
	 */
	private String position;

	/**
	 * 微信账号
	 */
	private String weChat;
	/**
	 * 固定电话
	 */
	private String fixedPhone;
}
