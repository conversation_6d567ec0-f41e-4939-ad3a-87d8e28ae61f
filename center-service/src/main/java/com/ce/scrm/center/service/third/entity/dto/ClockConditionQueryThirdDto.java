package com.ce.scrm.center.service.third.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClockConditionQueryThirdDto implements Serializable {

    /**
     * 客户id
     */
    @NotBlank
    private String custId;

    private String empId;

    /**
     * ge 筛选
     */
    private Date startTime;

    /**
     * 标记打卡是否有效 1：无效 2：有效
     */
    private Integer markClockValidFlag;

}
