package com.ce.scrm.center.service.business.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 高呈商机来源状态数据
 * <AUTHOR>
 * @date 2024/5/16 下午4:04
 * @version 1.0.0
 */
@Data
public class GcBusinessOpportunitySourceStateData implements Serializable {
    /**
     * 来源
     */
    private Integer source;
    /**
     * 来源名称
     */
    private String sourceName;
    /**
     * 选中状态
     */
    private Boolean selectState;
    /**
     * 当前来源下的状态
     */
    private List<State> stateList;

    /**
     * 状态数据
     */
    @Data
    public static class State implements Serializable {
        /**
         * 状态
         */
        private Integer state;
        /**
         * 状态名称
         */
        private String stateName;
        /**
         * 选中状态
         */
        private Boolean selectState;
    }
}