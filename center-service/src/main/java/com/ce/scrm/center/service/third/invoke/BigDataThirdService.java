package com.ce.scrm.center.service.third.invoke;

import cn.ce.cesupport.enums.UserActionTypeEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.third.entity.view.*;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.extend.dubbo.api.IBigDataDubbo;
import com.ce.scrm.extend.dubbo.entity.dto.BigDataCustomerFlagDto;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.*;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 大数据三方接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/1/15 11:16
 */
@Slf4j
@Service
public class BigDataThirdService {

    /**
     * 请求成功码
     */
    private final static Integer BIGDATA_HTTP_STATUS_OK = 0;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RocketMqOperate rocketMqOperate;

    @Value("${bigdata.enterpriseInfo:}")
    private String enterpriseInfo;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private IBigDataDubbo iBigDataDubbo;

    public BigDataCompanyDetail getBigDataCustomerInfo(String companyName) {
        Transaction t = Cat.newTransaction("service", "bigdata.getCompanyDetailByName");
        t.setStatus(Transaction.SUCCESS);
        try {
            if (StringUtils.isEmpty(companyName)) {
                return null;
            }
            String fullUrl = enterpriseInfo + companyName;
            //调用方法
            String responseBody = request(enterpriseInfo + companyName, HttpMethod.GET, null, null);
            if (StringUtils.isEmpty(responseBody)) {
                log.error("大数据接口根据名称获取企业基本信息失败，返回数据为空，url={}", fullUrl);
                return null;
            }
            //大数据返回的数据格式有点得儿，需要多次转换
            BigDataResult<BigDataData<BigDataCompanyDetail>> bigDataResult = JSON.parseObject(responseBody, new TypeReference<BigDataResult<BigDataData<BigDataCompanyDetail>>>() {
            });
            if (!Objects.equals(HttpStatus.OK.value(), bigDataResult.getCode())) {
                log.error("大数据接口根据名称获取企业基本信息失败，code={}, url={}", bigDataResult.getCode(), fullUrl);
                return null;
            }
            if (bigDataResult.getData() == null) {
                return null;
            }
            if (!Objects.equals(BIGDATA_HTTP_STATUS_OK, bigDataResult.getData().getCode())) {
                log.warn("大数据接口根据名称获取企业基本信息失败，code={}, url={}", bigDataResult.getData().getCode(), fullUrl);
                return null;
            }
            return bigDataResult.getData().getData();
        } catch (Exception e) {
            log.error("大数据接口根据名称获取企业基本信息失败，param={}", companyName, e);
            t.setStatus(e);
            Cat.logError(e);
        } finally {
            t.complete();
        }
        return null;
    }


    /**
     * 请求接口
     *
     * @param url       请求地址
     * @param method    请求方式
     * @param headerMap 请求头
     * @param paramMap  请求参数
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/15 11:30
     **/
    private String request(String url, HttpMethod method, Map<String, String> headerMap, Map<String, Object> paramMap) {
        try {
            //请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            String uuid = UUID.fastUUID().toString(true);
            headers.set("uid", uuid);
            if (headerMap != null) {
                headerMap.forEach(headers::set);
            }
            ResponseEntity<String> response = null;
            if (HttpMethod.GET.equals(method)) {
                response = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), String.class);
            } else if (HttpMethod.POST.equals(method)) {
                response = restTemplate.exchange(url, HttpMethod.POST,
                        new HttpEntity<>(paramMap, headers), String.class);
            }
            if (response == null || !response.getStatusCode().is2xxSuccessful()) {
                log.error("调用大数据接口失败,path={},状态码={}", url, response == null ? "" : response.getStatusCode().value());
                return null;
            }
            return response.getBody();
        } catch (Exception e) {
            log.error("调用大数据接口失败,path={},headerMap={},paramMap={}", url, JSONObject.toJSONString(headerMap), JSONObject.toJSONString(paramMap), e);
        }
        return null;
    }

    /**
     * 单个标记flag1
     *
     * @param sendBigDataView
     * @return
     */
    public boolean sendCustStatusToBigData(@NotNull SendBigDataView sendBigDataView) {
        if (sendBigDataView.getEntIdList().isEmpty() || sendBigDataView.getStatus() == null) {
            log.info("单个标记flag1 EntIdList参数为空！参数：{}", JSONObject.toJSONString(sendBigDataView));
            return false;
        }
        try {
            rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_BIGDATA_FLAG_TOPIC, JSON.toJSONString(new BigDataCustomerFlagDto(sendBigDataView.getEntIdList(), UserActionTypeEnum.FLAG_1.getId(), sendBigDataView.getStatus().toString())));
        } catch (Exception e) {
            log.error("sendCustStatusToBigData发mq消息同步客户状态到大数据异常，参数为：{}", JSONObject.toJSONString(sendBigDataView), e);
            return false;
        }
        return true;
    }

    /**
     * 单个标记flag5
     *
     * @param sendBigDataView
     * @return
     */
    public boolean sendCustOrgToBigData(@NotNull SendBigDataView sendBigDataView) {
        if (sendBigDataView.getEntIdList().isEmpty()) {
            log.info("单个标记flag5 EntIdList参数为空！参数：{}", JSONObject.toJSONString(sendBigDataView));
            return false;
        }
        StringBuilder markBuilder = new StringBuilder(150);
        markBuilder.append(sendBigDataView.getAreaId()).append("/").append(sendBigDataView.getSubId()).append("/").append(sendBigDataView.getDeptId()).append("/").append(sendBigDataView.getSalerId());
        try {
            rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_BIGDATA_FLAG_TOPIC, JSON.toJSONString(new BigDataCustomerFlagDto(sendBigDataView.getEntIdList(), UserActionTypeEnum.FLAG_5.getId(), markBuilder.toString())));
        } catch (Exception e) {
            log.error("sendCustOrgToBigData发mq消息同步客户机构到大数据异常，参数为：{}", JSONObject.toJSONString(sendBigDataView), e);
            return false;
        }
        return true;
    }

    /*
     * @Description 根据pid列表获取精准联系人数据
     * <AUTHOR>
     * @date 2024/12/9 16:34
     * @param pidList
     * @return java.util.List<com.ce.scrm.center.service.third.entity.view.BigDataPreciseLinkmanThirdView>
     */
    public List<BigDataPreciseLinkmanThirdView> getPreciseLinkman(List<String> pidList) {
        if(CollectionUtil.isEmpty(pidList)) {
            return Collections.emptyList();
        }
        DubboResult<List<BigDataPreciseLinkmanDubboView>> dubboResult = iBigDataDubbo.getPreciseLinkman(pidList);
        if(dubboResult == null || !dubboResult.checkSuccess()) {
            log.error("根据pid列表获取精准联系人数据接口失败，pidList={}", JSONObject.toJSONString(pidList));
            return Collections.emptyList();
        }
        if(CollectionUtil.isEmpty(dubboResult.getData())) {
//            log.error("根据pid列表获取精准联系人数据为空，pidList={}", JSONObject.toJSONString(pidList));
            return Collections.emptyList();
        }
        List<BigDataPreciseLinkmanThirdView> result =
                BeanCopyUtils.convertToVoList(dubboResult.getData(), BigDataPreciseLinkmanThirdView.class);
        return result;
    }

    /*
     * @Description 据手机号获取公司基本信息 （搜客宝数据源-lixiao）
     * <AUTHOR>
     * @date 2024/12/12 13:51
     * @param phone
     * @return java.util.List<com.ce.scrm.center.service.third.entity.view.BigDataSkbCompanyByPhoneView>
     */
    public List<BigDataSkbCompanyByPhoneView> getCompanyByPhoneListByPhone(String phone) {
        log.info("根据手机号获取公司基本信息 （搜客宝数据源-lixiao）,参数phone={}", JSONObject.toJSONString(phone));
        if(StringUtils.isBlank(phone)) {
            return Collections.emptyList();
        }
        DubboResult<List<BigDataSkbCompanyByPhoneDubboView>> dubboResult = iBigDataDubbo.getCompanyByPhoneListByPhone(phone);
        if(dubboResult == null || !dubboResult.checkSuccess()) {
            log.error("据手机号获取公司基本信息 （搜客宝数据源-lixiao），phone={}", JSONObject.toJSONString(phone));
            return Collections.emptyList();
        }
        if(CollectionUtil.isEmpty(dubboResult.getData())) {
//            log.error("据手机号获取公司基本信息 （搜客宝数据源-lixiao），phone={}", JSONObject.toJSONString(phone));
            return Collections.emptyList();
        }
        List<BigDataSkbCompanyByPhoneView> result =
                BeanCopyUtils.convertToVoList(dubboResult.getData(), BigDataSkbCompanyByPhoneView.class);
        log.info("据手机号获取公司基本信息 （搜客宝数据源-lixiao）,结果result={}", JSONObject.toJSONString(result));
        return result;
    }

    /*
     * @Description 根据PID列表获取搜客宝企业信息 (数据来源搜客宝)
     * <AUTHOR>
     * @date 2024/12/12 13:58
     * @param pidList
     * @return java.util.List<com.ce.scrm.center.service.third.entity.view.BigDataSkbCompanyView>
     */
    public List<BigDataSkbCompanyView> getSkbCompanyDetailByPidList(List<String> pidList) {
        log.info("根据PID列表获取搜客宝企业信息,参数phone={}", JSONObject.toJSONString(pidList));
        if(CollectionUtil.isEmpty(pidList)) {
            return Collections.emptyList();
        }
        DubboResult<List<BigDataSkbCompanyDetailView>> dubboResult = iBigDataDubbo.getSkbCompanyDetailByPidList(pidList);
        if(dubboResult == null || !dubboResult.checkSuccess()) {
            log.error("根据PID列表获取搜客宝企业信息，pidList={}", JSONObject.toJSONString(pidList));
            return Collections.emptyList();
        }
        if(CollectionUtil.isEmpty(dubboResult.getData())) {
            log.error("根据PID列表获取搜客宝企业信息，pidList={}", JSONObject.toJSONString(pidList));
            return Collections.emptyList();
        }
        List<BigDataSkbCompanyView> result =
                BeanCopyUtils.convertToVoList(dubboResult.getData(), BigDataSkbCompanyView.class);
        log.info("根据PID列表获取搜客宝企业信息,结果result={}", JSONObject.toJSONString(result));
        return result;
    }

    /*
     * @Description 根据PID获取联系方式 pid:企业唯一编码
     * <AUTHOR>
     * @date 2024/12/12 14:14
     * @param pid
     * @return java.util.List<com.ce.scrm.center.service.third.entity.view.BigDataSkbCompanyView>
     */
    public BigDataContactPersonView getContactPersonDetailByPid(String pid) {
        log.info("根据PID获取联系方式 pid:企业唯一编码,参数pid={}", JSONObject.toJSONString(pid));
        if(StringUtils.isBlank(pid)) {
            return null;
        }
        DubboResult<BigDataContactPersonDetailView> dubboResult = iBigDataDubbo.getContactPersonDetailByPid(pid);
        if(dubboResult == null || !dubboResult.checkSuccess()) {
            log.error("根据PID获取联系方式 pid:企业唯一编码，pid={}", pid);
            return null;
        }
        if(dubboResult.getData() == null) {
            log.error("根据PID获取联系方式 pid:企业唯一编码，pid={}", pid);
            return null;
        }
        BigDataContactPersonView result =
                BeanCopyUtils.convertToVo(dubboResult.getData(), BigDataContactPersonView.class,"list");
        List<BigDataContactsDetailView> list = dubboResult.getData().getList();
        if(CollectionUtil.isNotEmpty(list)) {
            result.setList(BeanCopyUtils.convertToVoList(list,BigDataContactsView.class));
        }
        log.info("根据PID获取联系方式 pid:企业唯一编码,结果result={}", JSONObject.toJSONString(result));
        return result;
    }



	/**
	 * 查询主要人员关联的企业信息
	 * @param pid pid
	 * @param name name
	 */
	public BigDataAffiliatedEnterpriseView getAffiliatedEnterprise(final String pid, final String name) {
		DubboResult<BigDataAffiliatedEnterpriseDubboView> dubboResult = iBigDataDubbo.getAffiliatedEnterprise(pid, name);

		BigDataAffiliatedEnterpriseView res = new BigDataAffiliatedEnterpriseView();
		if(dubboResult == null || !dubboResult.checkSuccess()) {
			log.error("查询主要人员关联的企业信息，pid={}, name={}", pid, name);
			return null;
		} else {
			BigDataAffiliatedEnterpriseDubboView data = dubboResult.getData();
			res.setTotal(data.getTotal());
			if (CollectionUtil.isNotEmpty(data.getItems())) {
				List<BigDataAffiliatedEnterpriseView.CustomerAffiliatedEnterpriseDetailBusinessView> resultItems = BeanUtil.copyToList(
					data.getItems(), BigDataAffiliatedEnterpriseView.CustomerAffiliatedEnterpriseDetailBusinessView.class);
				res.setItems(resultItems);
			}
		}
		return res;
	}

	/**
	 * 根据名称获取企业信息
	 * @param name 名称
	 * @return 企业信息
	 */
	public BigDataCompanyDetailByNameView getCompanyDetailByName(String name) {
		if(StringUtils.isBlank(name)) {
			return null;
		}
		DubboResult<BigDataCompanyDetailView> dubboResult = iBigDataDubbo.getCompanyDetailByName(name);
		if(dubboResult == null || !dubboResult.checkSuccess()) {
			log.error("根据名称获取企业信息，name={}", name);
			return null;
		}
		return BeanCopyUtils.convertToVo(dubboResult.getData(), BigDataCompanyDetailByNameView.class);
	}



    /***
     * 根据pid获取企业信息
     * @param pid
     * <AUTHOR>
     * @date 2024/12/20 22:47
     * @version 1.0.0
     * @return com.ce.scrm.center.service.third.entity.view.BigDataCompanyDetailByNameView
    **/
    public BigDataCompanyDetailByNameView getCompanyDetailByPid(String pid) {
        if(StringUtils.isBlank(pid)) {
            return null;
        }
        DubboResult<BigDataCompanyDetailView> dubboResult = iBigDataDubbo.getCompanyDetailByPid(pid);
        if(dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            log.error("根据pid获取企业信息为空，pid={}", pid);
            return null;
        }
        return BeanCopyUtils.convertToVo(dubboResult.getData(), BigDataCompanyDetailByNameView.class);
    }

	/*
	 * @Description 根据公司名称判断是否是曾用名
	 * <AUTHOR>
	 * @date 2024/10/29 16:04
	 * @param companyName
	 * @return java.lang.Boolean
	 */
	public Boolean getHistoryNameFlag(String companyName) {
		BigDataCompanyDetailByNameView companyDetailByName = getCompanyDetailByName(companyName);
		if(Objects.isNull(companyDetailByName)) {
			return false;
		}
		return !Objects.equals(companyDetailByName.getEntname(), companyName);
	}
}