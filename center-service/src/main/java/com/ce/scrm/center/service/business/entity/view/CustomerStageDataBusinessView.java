package com.ce.scrm.center.service.business.entity.view;

import com.ce.scrm.center.service.enums.CustomerStageEnum;
import lombok.Data;

/**
 * 客户阶段数据
 * <AUTHOR>
 * @date 2024/5/21 下午3:27
 * @version 1.0.0
 **/
@Data
public class CustomerStageDataBusinessView {
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户阶段
     */
    private CustomerStageEnum customerStageEnum;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司ID
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务ID
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;
}