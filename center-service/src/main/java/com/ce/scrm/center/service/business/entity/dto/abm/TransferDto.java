package com.ce.scrm.center.service.business.entity.dto.abm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ce.scrm.center.dao.entity.Transfertable;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:申请调度参数
 */

@Data
public class TransferDto  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    private String customerId;
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 员工ID
     */
    private String loginEmployeeId;

    /**
     * 员工名称
     */
    private String loginEmployeeName;
    /**
     * 分司名称
     */
    private String loginSubName;

    /**
     * 事业部名称
     */
    private String loginBuName;
    /**
     * 部门名称
     */
    private String loginOrgName;
    /**
     * 区域名称
     */
    private String loginAreaName;

    /**
     * 调拨原因
     */
    private String transferReason;

    /**
     * 证明信息url
     */
    private String proofInfoUrl;
}