package com.ce.scrm.center.service.business.entity.dto;

import cn.ce.cesupport.newcustclue.vo.ClueAssignVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 批量添加收藏构建数据
 * @Author: lijinpeng
 * @Date: 2025/1/9 13:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddFavoriteBatchBuildDataDto implements Serializable {

    /**
     * 分配的商务id
     */
    private String assignToSalerId;

    /**
     * 分配的部门id
     */
    private String assignToDeptId;

    /**
     * 分配的事业部id
     */
    private String assignToBuId;

    /**
     * 分配的分司id
     */
    private String assignToSubId;

    /**
     * 分配的区域
     */
    private String assignToAreaId;

    /**
     * 动作类型
     */
    private Integer actionType;

    /**
     * 收藏夹-公共数据
     */
    private ClueAssignVo clueAssignSaveVo;

    /**
     * 流转日志-公共数据
     */
    private ConvertLogBusinessDto convertLogBusinessDto;

    /**
     * 可用的库容
     */
    private Integer availableCapacity;

}
