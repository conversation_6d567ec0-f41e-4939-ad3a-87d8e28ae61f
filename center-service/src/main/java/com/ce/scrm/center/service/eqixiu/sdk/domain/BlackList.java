package com.ce.scrm.center.service.eqixiu.sdk.domain;

public class BlackList {

    private int id;
    private String corpId;
    private int authType;
    private String authUniqueId;
    private String remark;
    private String optStaffId;
    private String createTime;
    private String updateTime;


    public int getAuthType() {
        return authType;
    }

    public void setAuthType(int authType) {
        this.authType = authType;
    }

    public String getAuthUniqueId() {
        return authUniqueId;
    }

    public void setAuthUniqueId(String authUniqueId) {
        this.authUniqueId = authUniqueId;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getOptStaffId() {
        return optStaffId;
    }

    public void setOptStaffId(String optStaffId) {
        this.optStaffId = optStaffId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BlackList{" +
                "id=" + id +
                ", corpId='" + corpId + '\'' +
                ", authType=" + authType +
                ", authUniqueId='" + authUniqueId + '\'' +
                ", remark='" + remark + '\'' +
                ", optStaffId='" + optStaffId + '\'' +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                '}';
    }
}
