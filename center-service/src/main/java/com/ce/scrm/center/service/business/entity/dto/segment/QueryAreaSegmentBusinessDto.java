package com.ce.scrm.center.service.business.entity.dto.segment;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @version 1.0
 * @Description: 查询区总分群信息条件
 * @Author: lijinpeng
 * @Date: 2025/2/27 14:23
 */
@Data
public class QueryAreaSegmentBusinessDto implements Serializable {

    /**
     * 分群id
     */
    @NotNull
    private String segmentId;

    /**
     * 省份code
     */
    private String provinceCode;

    /**
     * 市code
     */
    private String cityCode;

    /**
     * 城市筛选项list
     */
    private List<String> cityCodeList;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 客户状态
     */
    private String customerStatus;

    /**
     * 筛选项 0否 1是
     */
    private Integer visitFlag;

    /**
     * 一级国标行业编码
     */
    private List<String> firstIndustryCodeList;

    /**
     * 二级国标行业编码
     */
    private List<String> secondIndustryCodeList;

    /**
     * 三级国标行业编码
     */
    private List<String> thirdIndustryCodeList;

    /**
     * 四级国标行业编码
     */
    private List<String> fourthIndustryCodeList;

    /**
     * 注册资本范围 小
     */
    private BigDecimal registerCapitalMin;

    /**
     * 注册资本范围 大
     */
    private BigDecimal registerCapitalMax;

    /**
     * 有无域名备案 1:是 0:否
     */
    private Integer icpFlag;

    /**
     * 有无进出口信用 1:是 0:否
     */
    private Integer jingchukouFlag;

    /**
     * 模糊查询备注
     */
    private String likeRemark;

    /**
     * 页码
     * 默认 1
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     * 默认 10
     */
    private Integer pageSize = 10;

    /**
     * 区域ID
     */
    private String loginAreaId;

    /**
     * 职位
     */
    private String loginPosition;

}
