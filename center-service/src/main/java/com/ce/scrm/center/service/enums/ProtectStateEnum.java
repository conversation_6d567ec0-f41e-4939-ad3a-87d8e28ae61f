package com.ce.scrm.center.service.enums;

import lombok.Getter;

/**
 * 保护状态枚举
 * <AUTHOR>
 * @date 2024/5/21 下午3:31
 * @version 1.0.0
 **/
@Getter
public enum ProtectStateEnum {
    PROTECT(1, "保护中"),
    MAJOR_WILL_ASSIGN(2, "总监待分配"),
    MANAGER_WILL_ASSIGN(3, "经理待分配"),
    CUSTOMER_POOL(4, "客户池"),
    BU_WILL_ASSIGN(5,"事业部总监待分配"),
    ;

    private final Integer state;
    private final String stateName;

    ProtectStateEnum(Integer state, String stateName) {
        this.state = state;
        this.stateName = stateName;
    }

    public static ProtectStateEnum getProtectStateEnum(Integer state) {
        for (ProtectStateEnum protectStateEnum : ProtectStateEnum.values()) {
            if (protectStateEnum.getState().equals(state)) {
                return protectStateEnum;
            }
        }
        return null;
    }
}
