package com.ce.scrm.center.service.third.entity.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * @version 1.0
 * @Description: 根据条件查询组织机构集合
 * @Author: lijinpeng
 * @Date: 2024/11/21 14:37
 */
@Data
@Builder
public class OrgConditionThirdDto {

    /**
     * 机构id
     */
    private String id;
    /**
     * 机构名称
     */
    private String name;
    /**
     * 机构父id
     */
    private String parentId;
    /**
     * 来源
     */
    private String source;
    /**
     * 数据状态
     */
    private Integer state;
    /**
     * 机构层级类型
     */
    private String type;
    /**
     * 机构业务类型
     */
    private Integer businessType;
    /**
     * 商务标签（0-通用 ，1-电商）
     */
    private Integer orgClass2;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 机构管家待分配是否设置自动分配
     */
    private String gjAutoStr;

    /**
     * n类市场
     */
    private Integer marketCategoryId;

    /**
     * 是否加盟的分司（0否；1是）
     */
    private Integer isJoin;

}
