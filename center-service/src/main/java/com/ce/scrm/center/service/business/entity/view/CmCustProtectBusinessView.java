package com.ce.scrm.center.service.business.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * description: 保护关系业务层
 * @author: DD.Jiu
 * date: 2024/7/25.
 */
@Data
public class CmCustProtectBusinessView implements Serializable {
    /**
     * id
     */
    private String id;
    /**
     * 客户ID
     */
    private String custId;

    /**
     * 客户类型（2保护客户；3网站客户；4非网站客户）
     */
    private Integer custType;

    /**
     * 合作意向状态
     */
    private Integer intentionalityType;

    /**
     * 商务ID
     */
    private String salerId;

    /**
     * 部门ID
     */
    private String bussdeptId;

    /**
     * 事业部id
     */
    private String buId;

    /**
     * 分公司ID
     */
    private String subcompanyId;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 市场ID
     */
    private String markId;

    /**
     * 阶段性保护时间

     */
    private Date protectTime;

    /**
     * 阶段性超期时间

     */
    private Date exceedTime;

    /**
     * 共享标签

     */
    private Integer isVisitState;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人ID
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 3分配，6自签
     */
    private Integer custSource;

    /**
     * 数据状态

     */
    private Integer dataState;

    /**
     * 最后一次拜访时间

     */
    private Date lastVisitTime;

    /**
     * 联系超期时间

     */
    private Date visitExceedTime;

    /**
     * 绝对保护期
     */
    private Date absoluteProtectTime;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 商机code
     */
    private String busioppoCode;

    /**
     * 是否重点客户
     */
    private Integer isStress;

    /**
     * 加重点的时间
     */
    private Date addStressTime;

    /**
     * 最后一次付款日期
     */
    private Date lastPayTime;

    /**
     * 是否已转高呈
     */
    private Integer isTurnGc;

    /**
     * 二级来源
     */
    private Integer custSourceSub;

    /**
     * 老客数据来源标识（1 1年以上2年以内；2 2年以上）
     */
    private Integer cooperate;

    /**
     * 是否计算库容（1不计算，0计算）
     */
    private Integer occupy;

    /**
     * 是否已打卡（1是，0否）
     */
    private Integer isClock;

    /**
     * 打卡所在省
     */
    private String clockProvince;

    /**
     * 打卡所在市
     */
    private String clockCity;

    /**
     * 打卡所在区
     */
    private String clockRegion;

    /**
     * 注册地所在省
     */
    private String regProvince;

    /**
     * 注册地所在市
     */
    private String regCity;

    /**
     * 注册地所在区
     */
    private String regRegion;

    /**
     * 统一社会信用代码查询
     */
    private String uncid;

    /**
     * 状态值（1、保护；2、总监；3、经理；4、客户池）
     */
    private Integer status;

    /**
     * 分配时间
     */
    private Date assignTime;

    /**
     * 客户进待分配表的来源
     */
    private Integer assignCustSource;

    /**
     * 待分配超期时间
     */
    private Date assignDate;

    /**
     * 客户来源（1=释放、2=流失）
     */
    private String source;

    /**
     * 释放原因
     */
    private String reason;

    /**
     * 原的部门ID
     */
    private String originalDeptId;

    /**
     * 表记录写入时间
     */
    private Date dbInsertTime;

    /**
     * 表记录更新时间
     */
    private Date dbUpdateTime;

    /**
     * 搜客宝 1:规模工业,2:规上服务业,3:规上建筑业,4:规上批发零售业,5:规上住宿餐饮业,6:规上房地产开发与经营业
     */
    private String tagFlag7;

    /**
     * 搜客宝 行业 1:律师,2:学校,3:医院
     */
    private String tagFlag8;

    /**
     * 科技型企业
     */
    private String tagTechcompany;

    /**
     * 自定义标签
     */
    private String customTags;

    /**
     * pid
     */
    private String entId;

    /**
     * 首次签单时间
     */
    private Date firstSignTime;
}
