package com.ce.scrm.center.service.business.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddProtectTimeEventBusinessDto implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 事件类型 1会销签到、2商务有效打卡、3微信绑定
     */
    private Integer eventType;

}
