package com.ce.scrm.center.service.business.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TransferWebDtoView implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;
	/**
	 * 客户id
	 */
	private String customerId;

	/**
	 * 客户名称
	 */
	private String customerName;

	/**
	 * 调拨原因
	 */
	private String transferReason;

	/**
	 * 证明信息url
	 */
	private String proofInfoUrl;

	/**
	 * 申请时间
	 */
	private Date applicationTime;

	/**
	 * 申请人
	 */
	private String applicant;

	/**
	 * 处理时间
	 */
	private Date processingTime;

	/**
	 * 处理人
	 */
	private String processor;

	/**
	 * 处理状态(1.未处理 2.通过 3.驳回 4.超时自动处理)
	 */
	private Integer processingStatus;

	/**
	 * 备注信息
	 */
	private String remarks;


}