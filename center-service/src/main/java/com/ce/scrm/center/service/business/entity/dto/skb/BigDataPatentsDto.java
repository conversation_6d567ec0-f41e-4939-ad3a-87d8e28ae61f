package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 专利
 * @author: JiuDD
 * date: 2025/2/13
 */
@Data
public class BigDataPatentsDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**统一信用代码*/
    @JSONField(serialize = false)
	private String uncid;
    /**总数量*/
    @JSONField(serialize = false)
	private int total_count;
    /**总页数*/
    @JSONField(serialize = false)
	private int total_page;
    /**当前页*/
    @JSONField(serialize = false)
	private int page;
    /**专利列表*/
    @JSONField(name = "专利列表",serialize = false)
    private List<ListPatents> list;

    @JSONField(name = "专利列表")
    private String listString;

    @NoArgsConstructor
    @Data
    public static class ListPatents implements Serializable {
        private static final long serialVersionUID = 1L;
        /**申请号*/
        @JSONField(serialize = false)
		private String apply_no;
        /**公开（公告）号*/
        @JSONField(serialize = false)
		private String apply_pub_no;
        /**申请日*/
        @JSONField(serialize = false)
        private String apply_date;
        /**公开（公告）日*/
        @JSONField(serialize = false)
		private String apply_pub_date;
        /**名称*/
        @JSONField(name = "专利名称")
		private String patent_name;
        /**申请人: 0 专利权人: 1*/
        @JSONField(serialize = false)
		private String identity;
        /**优先权号(英文逗号拼接的多个值)*/
        @JSONField(serialize = false)
		private String priority_no;
        /**优先权日期(多个日期用中文逗号分隔)*/
        @JSONField(serialize = false)
		private String priority_date;
        /**主分类号*/
        @JSONField(serialize = false)
		private String primary_classify_no;
        /**分类号*/
        @JSONField(serialize = false)
		private String classify_no;
        /**申请人*/
        @JSONField(serialize = false)
		private String applicant;
        /**发明设计人*/
        @JSONField(serialize = false)
		private String inventor;
        /**专利代理机构*/
        @JSONField(serialize = false)
		private String agency_ins;
        /**专利代理机构号*/
        @JSONField(serialize = false)
		private String institution_no;
        /**代理机构PID*/
        @JSONField(serialize = false)
		private String agency_pid;
        /**代理人*/
        @JSONField(serialize = false)
		private String agency_person;
        /**地址*/
        @JSONField(serialize = false)
		private String address;
        /**申请地址邮编*/
        @JSONField(serialize = false)
		private String address_no;
        /**专利类型 1发明专利，2实用新型，3外观设计，8进入中国国家阶段的PCT发明专利，9进入中国国家阶段的PCT实用新型专利*/
        @JSONField(name = "专利类型",serialize = false)
		private String notice_type;
        /**专利类型code*/
        @JSONField(serialize = false)
		private String notice_type_code;
        /**PCT申请号*/
        @JSONField(serialize = false)
		private String iapp_no;
        /**PCT申请日期*/
        @JSONField(serialize = false)
		private String iapp_date;
        /**PCT公开（公告）号*/
        @JSONField(serialize = false)
		private String ipub_no;
        /**PCT公开（公告）日期*/
        @JSONField(serialize = false)
		private String ipub_date;
        /**PCT进入国家阶段日*/
        @JSONField(serialize = false)
		private String den;
        /**摘要*/
        @JSONField(serialize = false)
		private String summary;
    }
}
