package com.ce.scrm.center.service.third.invoke;

import com.ce.scrm.center.service.third.entity.view.IndustryThirdView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.service.yml.ThirdCustomerConfig;
import com.ce.scrm.customer.dubbo.api.IIndustryDubbo;
import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.IndustryDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @version 1.0
 * @Description: 客户行业接口
 * @Author: lijinpeng
 * @Date: 2024/12/12 17:57
 */
@Slf4j
@Service
public class IndustryThirdService {

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private IIndustryDubbo iIndustryDubbo;

    @Resource
    private ThirdCustomerConfig thirdCustomerConfig;


    public List<IndustryThirdView> getAllData() {
        log.info("开始获取所有行业数据");
        DubboResult<List<IndustryDubboView>> dubboResult = iIndustryDubbo.getAllData();
        if(dubboResult == null || !dubboResult.checkSuccess()) {
            log.error("开始获取所有行业数据失败");
            return null;
        }
        if(dubboResult.getData() == null) {
            log.error("获取所有行业数据为空");
            return null;
        }
        List<IndustryThirdView> result =
                BeanCopyUtils.convertToVoList(dubboResult.getData(), IndustryThirdView.class);
//        log.info("获取所有行业数据,结果result={}", JSONObject.toJSONString(result));
        return result;
    }

    /**
     * 设置调用签名数据
     * @param signData  签名数据
     * <AUTHOR>
     * @date 2024/3/12 11:29
     **/
    private void setSignData(SignData signData) {
        signData.setSourceKey(thirdCustomerConfig.getKey());
        signData.setSourceSecret(thirdCustomerConfig.getSecret());
    }

}
