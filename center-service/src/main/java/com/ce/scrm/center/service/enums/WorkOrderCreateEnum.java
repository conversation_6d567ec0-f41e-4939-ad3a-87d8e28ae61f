package com.ce.scrm.center.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WorkOrderCreateEnum implements BaseEnumField {
    //workOrderCode("work_order_code", "work_order_code", "工单编号", CdpTypeEnum.String, true),
    //workOrderId("work_order_id", "work_order_id", "工单ID", CdpTypeEnum.String, false),
    workOrderSource("work_order_source", "work_order_source", "工单来源渠道", "来源渠道", CdpTypeEnum.String, false),
    //workOrderCreatorId("work_order_creator_id", "work_order_creator_id", "工单创建人ID", CdpTypeEnum.String, false),
    //workOrderCreatorName("work_order_creator_name", "work_order_creator_name", "创建人姓名", CdpTypeEnum.String, false),
    //workOrderCreatorPosition("work_order_creator_position", "work_order_creator_position", "创建人岗位", CdpTypeEnum.String, false),
    workOrderCreateTime("work_order_create_time", "work_order_create_time", "工单创建时间", "工单时间", CdpTypeEnum.Datetime, false),
    //workOrderTypeId("work_order_type_id", "work_order_type_id", "工单分类ID", CdpTypeEnum.String, true),
    //workOrderTypeName("work_order_type_name", "work_order_type_name", "工单分类名称", CdpTypeEnum.String, false),
    //workOrderPriority("work_order_priority", "work_order_priority", "工单处理优先级", CdpTypeEnum.String, false),
    //linkmanName("linkman_name", "linkman_name", "客户联系人", CdpTypeEnum.String, false),
    //linkmanPhone("linkman_phone", "linkman_phone", "客户联系人电话", CdpTypeEnum.String, false),
    //workOrderProductCode("work_order_product_code", "work_order_product_code", "产品分类CODE", CdpTypeEnum.String, false),
    workOrderProductName("work_order_product_name", "work_order_product_name", "产品分类名称", "关联产品", CdpTypeEnum.String, false),
    workOrderConsultationType("work_order_consultation_type", "work_order_consultation_type", "咨询类型", "咨询类型", CdpTypeEnum.String, false),
    //workOrderRemark("work_order_remark", "work_order_remark", "工单备注", CdpTypeEnum.String, false),
    //workOrderComplaintsReason("work_order_complaints_reason", "work_order_complaints_reason", "投诉原因", CdpTypeEnum.String, false),
    workOrderComplaintsDescription("work_order_complaints_description", "work_order_complaints_description", "客户投诉描述", "工单内容", CdpTypeEnum.String, false),
    //subId("sub_id", "sub_id", "分司ID", CdpTypeEnum.String, false),
    //subName("sub_name", "sub_name", "分司名称", CdpTypeEnum.String, false),
    //areaId("area_id", "area_id", "区域ID", CdpTypeEnum.String, false),
    //areaName("area_name", "area_name", "区域名称", CdpTypeEnum.String, false),
    //deptId("dept_id", "dept_id", "部门ID", CdpTypeEnum.String, false),
    //deptName("dept_name", "dept_name", "部门名称", CdpTypeEnum.String, false),
    //buId("bu_id", "bu_id", "商务归属部门事业部id", CdpTypeEnum.String, false),
    //buName("bu_name", "bu_name", "商务归属部门事业部名称", CdpTypeEnum.String, false),

//    auditGroupId("audit_group_id", "audit_group_id", "处理组ID", String, false),
//    auditGroupName("audit_group_name", "audit_group_name", "处理组名称", String, false),
    ;

    /**
     * CDP 属性名
     */
    private final String field;

    /**
     * CDP 类型
     */
    private final String tableField;

    /**
     * cdp描述
     */
    private final String description;
    /**
     * ai描述
     */
    private final String aiDescription;

    private final CdpTypeEnum fieldType;

    /**
     * 是否必传
     */
    private final Boolean isMust;

}
