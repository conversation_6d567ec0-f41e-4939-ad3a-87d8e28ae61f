package com.ce.scrm.center.service.third.invoke;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.business.entity.dto.EntWeChatApprovalDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * @version 1.0
 * @Description: 企业微信审批
 * @Author: lijinpeng
 * @Date: 2024/10/15 11:03
 */
@Slf4j
@Component
public class EntWeChatApprovalThirdService {

    /**
     * 企业CORP_ID
     */
    private static final String CORP_ID = "wwf1c477c862181a7f";

    /**
     * 企业CORP_ID_SECRET
     */
    private static final String CORP_ID_SECRET = "zSDrzl2LWwSeWydVEfaBYRDqeR619NzFZB_Nono-VTs";

    /**
     * 获取tokenURL
     */
    private static final String GET_TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";

    /**
     * 获取审批模版URL
     */
    private static final String APPROVAL_TEMPLATE_URL = "https://qyapi.weixin.qq.com/cgi-bin/oa/gettemplatedetail";

    /**
     * 提交审批URL
     */
    private static final String SUBMIT_APPROVAL_URL = "https://qyapi.weixin.qq.com/cgi-bin/oa/applyevent";

    /**
     * 模版id
     */
    public static final String TEMPLATE_ID = "C4ZT8yRLmXARMuccqbJT9tFyAD1mNzizMpt83YVS1";

    @Resource
    private RestTemplate restTemplate;

    /*
     * @Description 获取AccessToken
     * <AUTHOR>
     * @date 2024/10/15 12:09
     * @return java.lang.String
     */
    private String getAccessToken() {
        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(GET_TOKEN_URL)
                    .queryParam("corpid", CORP_ID)
                    .queryParam("corpsecret", CORP_ID_SECRET)
                    .build().toUri();

            HttpHeaders headers = new HttpHeaders();
            headers.add("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
            headers.add("Accept", "*/*");
            headers.add("Connection", "keep-alive");
            headers.add("Content-Type", "application/json;charset=UTF-8");

            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<String> exchange = restTemplate.exchange(uri, HttpMethod.GET, entity, String.class);

            String body = exchange.getBody();
            JSONObject jsonObject = JSON.parseObject(body);
            Integer errcode = jsonObject.getInteger("errcode");
            if(errcode != null && !errcode.equals(0)) {
                throw new RuntimeException("获取企业微信AccessToken失败:" + body);
            }
            String result = jsonObject.getString("access_token");
            return result;
        }catch (Exception e){
            log.error("getAccessToken方法，获取企业微信AccessToken失败",e);
            throw new RuntimeException("getAccessToken方法，获取企业微信AccessToken失败：" + e);
        }
    }

    /*
     * @Description 获取审批模版内容
     * <AUTHOR>
     * @date 2024/10/17 17:13
     * @param templateId
     * @return java.util.Map<java.lang.String,com.ce.scrm.center.service.business.entity.dto.EntWeChatApprovalDto.Content>
     */
    public Map<String, EntWeChatApprovalDto.Content> getApprovalTemplate(String templateId) {
        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(APPROVAL_TEMPLATE_URL)
                    .queryParam("access_token",getAccessToken())
                    .build().toUri();

            HttpHeaders headers = new HttpHeaders();
            headers.add("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
            headers.add("Accept", "*/*");
            headers.add("Connection", "keep-alive");
            headers.add("Content-Type", "application/json;charset=UTF-8");

            Map<String,String> body = new HashMap<>();
            body.put("template_id", templateId);

            HttpEntity<Map<String,String>> entity = new HttpEntity<>(body,headers);

            log.info("获取企微审批模版参数为：uri:{},entity:{},",uri,entity);
            ResponseEntity<String> exchange = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class);

            String responseBody = exchange.getBody();
            JSONObject jsonObject = JSON.parseObject(responseBody);
            Integer errcode = jsonObject.getInteger("errcode");
            log.info("获取企微审批模版结果：{}",jsonObject);
            if(errcode != null && !errcode.equals(0)) {
                throw new RuntimeException("getApprovalTemplate方法，获取企微审批模版失败" + body);
            }
            JSONObject templateContent = jsonObject.getJSONObject("template_content");
            JSONArray controls = templateContent.getJSONArray("controls");
            Map<String,EntWeChatApprovalDto.Content> result = new HashMap<>();
            for (int i = 0; i < controls.size(); i++) {
                JSONObject propertyMap = controls.getJSONObject(i);
                JSONObject property = propertyMap.getJSONObject("property");
                String control = property.getString("control");
                String id = property.getString("id");
                JSONArray jsonArray = property.getJSONArray("title");
                if(jsonArray != null && jsonArray.size() > 0) {
                    String text = jsonArray.getJSONObject(0).getString("text");
                    EntWeChatApprovalDto.Content content = EntWeChatApprovalDto.Content.builder()
                            .id(id)
                            .control(control)
                            .build();
                    result.put(text,content);
                }
            }
            return result;
        }catch (Exception e) {
            log.error("getApprovalTemplate方法，获取企微审批模版失败，参数templateId为：{}",templateId,e);
            throw new RuntimeException("getApprovalTemplate方法，获取企微审批模版失败：" + e);
        }
    }

    /*
     * @Description 提交审批
     * <AUTHOR>
     * @date 2024/10/16 17:24
     * @param entWeChatApprovalDto
     * @return java.lang.String
     */
    public String submitApproval(EntWeChatApprovalDto entWeChatApprovalDto) {
        try {
            log.info("submitApproval方法提交企业微信审批，参数为:{}",JSONObject.toJSONString(entWeChatApprovalDto));
            URI uri = UriComponentsBuilder.fromHttpUrl(SUBMIT_APPROVAL_URL)
                    .queryParam("access_token",getAccessToken())
                    .build().toUri();

            HttpHeaders headers = new HttpHeaders();
            headers.add("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
            headers.add("Accept", "*/*");
            headers.add("Connection", "keep-alive");
            headers.add("Content-Type", "application/json;charset=UTF-8");

            Map<String, Object> requestBody = buildSubmitApprovalRequestBody(entWeChatApprovalDto);

            HttpEntity<Map<String,Object>> entity = new HttpEntity<>(requestBody,headers);

            log.info("提交审批企微审批模版参数为：uri:{},entity:{},",uri,entity);
            ResponseEntity<String> exchange = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class);

            String responseBody = exchange.getBody();
            JSONObject jsonObject = JSON.parseObject(responseBody);
            Integer errcode = jsonObject.getInteger("errcode");
            log.info("提交审批企微审批模版结果：{}",jsonObject);
            if(errcode != null && !errcode.equals(0)) {
                throw new RuntimeException("submitApproval方法，发送审批请求失败" + requestBody);
            }
            String spNo = jsonObject.getString("sp_no");
            return spNo;
        }catch (Exception e) {
            log.error("submitApproval方法，发送审批请求失败，参数entWeChatApprovalDto为：{}",entWeChatApprovalDto);
            log.error("异常：{}", String.valueOf(e));
            throw new RuntimeException("getApprovalTemplate方法，获取企微审批模版失败：" + e);
        }
    }

    /*
     * @Description 构建提交审批参数
     * <AUTHOR>
     * @date 2024/10/16 13:50
     * @param entWeChatApprovalDto
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String,Object> buildSubmitApprovalRequestBody(EntWeChatApprovalDto entWeChatApprovalDto) {
        Map<String,Object> body = new HashMap<>();
        // 审批发起人
        body.put("creator_userid",entWeChatApprovalDto.getCreatorUserId());
//        body.put("creator_userid","101756");
        body.put("template_id",entWeChatApprovalDto.getTemplateId());
        body.put("use_template_approver",entWeChatApprovalDto.getUseTemplateApprover());
        body.put("process",entWeChatApprovalDto.getProcess());

        Map<String, EntWeChatApprovalDto.Content> approvalMap = getApprovalTemplate(entWeChatApprovalDto.getTemplateId());

        EntWeChatApprovalDto.Content content1 = approvalMap.get("客户名称");
        content1.setValue(new HashMap<String,String>(){{put("text",entWeChatApprovalDto.getProtectEmtName());}});

        EntWeChatApprovalDto.Content content2 = approvalMap.get("情况说明");
        content2.setValue(new HashMap<String,String>(){{put("text",entWeChatApprovalDto.getDescription());}});

        EntWeChatApprovalDto.Content content3 = approvalMap.get("客户保护商务");
        content3.setValue(new HashMap<String,String>(){{put("text",entWeChatApprovalDto.getProtectSalerName());}});

        EntWeChatApprovalDto.Content content4 = approvalMap.get("合作发起商务");
        content4.setValue(new HashMap<String,String>(){{put("text",entWeChatApprovalDto.getApprovalSalerName());}});

        EntWeChatApprovalDto.ApplyData applyData = EntWeChatApprovalDto.ApplyData.builder()
                .contents(Arrays.asList(content1,content2,content3,content4))
                .build();
        body.put("apply_data",applyData);
        body.put("summary_list",entWeChatApprovalDto.getSummaryList());
        return body;
    }


}
