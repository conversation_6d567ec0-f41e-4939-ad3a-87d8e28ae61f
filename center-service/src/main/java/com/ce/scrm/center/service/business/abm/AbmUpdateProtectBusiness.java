package com.ce.scrm.center.service.business.abm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.SmaMarketSubcompay;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.SmaMarketSubcompayService;
import com.ce.scrm.center.service.business.entity.dto.ProtectBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmReleaseProtectDto;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmUpdateProtectDto;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.NewCustomerClueThirdService;
import com.ce.scrm.center.service.third.invoke.SmaConvertLogThirdService;
import com.ce.scrm.center.service.yml.ThirdCustomerConfig;
import com.ce.scrm.customer.dubbo.api.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerUpdateDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 转商机下发 业务处理类
 */
@Slf4j
@Service
public class AbmUpdateProtectBusiness {

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private NewCustomerClueThirdService newCustomerClueThirdService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private SmaMarketSubcompayService smaMarketSubcompayService;

    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;

    @Resource
    private ThirdCustomerConfig thirdCustomerConfig;

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private ICustomerDubbo customerDubbo;

    /***
     * 修改保护关系&记录流转日志
     * @param abmUpdateProtectDto
     * <AUTHOR>
     * @date 2025/8/7 14:27
     * @version 1.0.0
     * @return void
     **/
    public Optional<String> updateProtect(AbmUpdateProtectDto abmUpdateProtectDto) {
        log.info("更新客户保护关系 = {}", JSONObject.toJSONString(abmUpdateProtectDto));

        if (abmUpdateProtectDto == null) {
            log.warn("保存保护关系，参数不能为空");
            return Optional.of("参数不能为空");
        }
        if (StrUtil.isBlank(abmUpdateProtectDto.getCustId())) {
            log.warn("保存保护关系，客户ID不能为空，参数为:{}", JSONObject.toJSONString(abmUpdateProtectDto));
            return Optional.of("客户ID不能为空");
        }
        if (Objects.isNull(abmUpdateProtectDto.getConvertType())) {
            log.warn("修改保存保护关系，流转日志code不能为空，参数为:{}", JSONObject.toJSONString(abmUpdateProtectDto));
            return Optional.of("流转日志code不能为空");
        }
        if (StrUtil.isBlank(abmUpdateProtectDto.getConvertTypeDesc())) {
            log.warn("修改保存保护关系，流转日志类型不能为空，参数为:{}", JSONObject.toJSONString(abmUpdateProtectDto));
            return Optional.of("流转日志类型不能为空");
        }
        ProtectBusinessDto protectBusinessDto = BeanUtil.copyProperties(abmUpdateProtectDto, ProtectBusinessDto.class);
        CmCustProtect protect = CglibUtil.copy(protectBusinessDto, CmCustProtect.class);
        protect.setProtectTime(new Date());
        protect.setCustName(abmUpdateProtectDto.getCustName());
        protect.setExceedTime(newCustomerClueThirdService.getExceedTime());
        protect.setUpdateBy(protectBusinessDto.getOperator());
        protect.setUpdateTime(new Date());
        // 默认都占 库容
        protect.setOccupy(0);

        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, protectBusinessDto.getCustId()).one();
        if (cmCustProtect == null) {
            protect.setCreateBy(protectBusinessDto.getOperator());
            protect.setCreateTime(new Date());
        } else {
            protect.setId(cmCustProtect.getId());
        }
        Optional<CustomerDataThirdView> customerDataThirdViewOptional = customerThirdService.getCustomerData(protectBusinessDto.getCustId());
        if (customerDataThirdViewOptional.isPresent()) {
            CustomerDataThirdView customerDataThirdView = customerDataThirdViewOptional.get();
            protect.setUncid(customerDataThirdView.getCertificateCode());
            protect.setEntId(customerDataThirdView.getSourceDataId());
            protect.setRegProvince(customerDataThirdView.getProvinceCode());
            protect.setRegCity(customerDataThirdView.getCityCode());
            protect.setRegRegion(customerDataThirdView.getDistrictCode());
            SmaMarketSubcompay smaMarketSubcompay = smaMarketSubcompayService.lambdaQuery().eq(SmaMarketSubcompay::getSubCompany, protectBusinessDto.getSubcompanyId()).last("limit 1").one();
            if (smaMarketSubcompay != null) {
                protect.setMarkId(smaMarketSubcompay.getMarketId());
            }
        }
        if (StrUtil.isBlank(protect.getId())) {
            protect.setId(UUID.fastUUID().toString(true));
            cmCustProtectService.saveData(protect);
        } else {
            cmCustProtectService.updateByCustId(protect);
        }
        Integer convertType = abmUpdateProtectDto.getConvertType();
        String convertTypeDesc = abmUpdateProtectDto.getConvertTypeDesc();
        log.info("{} 生成流转日志：{},{}", protect.getCustId(), convertType, convertTypeDesc);
        smaConvertLogThirdService.insertLog(convertType, convertTypeDesc, protectBusinessDto.getOperator(), cmCustProtect, protect);
        return Optional.empty();
    }

    /***
     * 释放保护关系
     * @param abmReleaseProtectDto
     * <AUTHOR>
     * @date 2025/8/9 23:48
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
    **/
    public Optional<String> releaseProtect(AbmReleaseProtectDto abmReleaseProtectDto) {
        log.info("释放客户保护关系 = {}", JSONObject.toJSONString(abmReleaseProtectDto));
        if (abmReleaseProtectDto == null) {
            log.warn("保存保护关系，参数不能为空");
            return Optional.of("参数不能为空");
        }
        if (StrUtil.isBlank(abmReleaseProtectDto.getCustId())) {
            log.warn("保存保护关系，客户ID不能为空，参数为:{}", JSONObject.toJSONString(abmReleaseProtectDto));
            return Optional.of("客户ID不能为空");
        }
        String operator = abmReleaseProtectDto.getOperator();
        if (StrUtil.isBlank(operator)) {
            log.warn("保存保护关系，操作人不能为空，参数为:{}", JSONObject.toJSONString(abmReleaseProtectDto));
            return Optional.of("Operator不能为空");
        }
        if (Objects.isNull(abmReleaseProtectDto.getConvertType())) {
            log.warn("修改保存保护关系，流转日志code不能为空，参数为:{}", JSONObject.toJSONString(abmReleaseProtectDto));
            return Optional.of("流转日志code不能为空");
        }
        if (StrUtil.isBlank(abmReleaseProtectDto.getConvertTypeDesc())) {
            log.warn("修改保存保护关系，流转日志类型不能为空，参数为:{}", JSONObject.toJSONString(abmReleaseProtectDto));
            return Optional.of("流转日志类型不能为空");
        }

        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, abmReleaseProtectDto.getCustId()).one();
        if (Objects.isNull(cmCustProtect)) {
            log.warn("释放失败，当前客户={}找不到对应保护关系", JSONObject.toJSONString(abmReleaseProtectDto));
            return Optional.of("释放失败，当前客户找不到对应保护关系");
        }
        String salerId = cmCustProtect.getSalerId();
        String bussdeptId = cmCustProtect.getBussdeptId();
        String buId = cmCustProtect.getBuId();
        String subcompanyId = cmCustProtect.getSubcompanyId();
        String areaId = cmCustProtect.getAreaId();

        CmCustProtect releaseCmCustProtect = new CmCustProtect();
        releaseCmCustProtect.setId(cmCustProtect.getId());
        releaseCmCustProtect.setCustId(cmCustProtect.getCustId());
        releaseCmCustProtect.setStatus(ProtectStateEnum.CUSTOMER_POOL.getState());
        releaseCmCustProtect.setBindFlag(0);
        releaseCmCustProtect.setSalerId(null);
        releaseCmCustProtect.setProtectTime(null);
        releaseCmCustProtect.setExceedTime(null);
        releaseCmCustProtect.setReason(null);
        releaseCmCustProtect.setLastSalerId(salerId);
        releaseCmCustProtect.setLastBuId(buId);
        releaseCmCustProtect.setLastBussdeptId(bussdeptId);
        releaseCmCustProtect.setLastSubcompanyId(subcompanyId);
        releaseCmCustProtect.setLastAreaId(areaId);
        releaseCmCustProtect.setUpdateBy(operator);
        releaseCmCustProtect.setUpdateTime(new Date());
        log.info("释放保护关系 ={}", JSONObject.toJSONString(releaseCmCustProtect));
        cmCustProtectService.updateByCustId(releaseCmCustProtect);

        Integer convertType = abmReleaseProtectDto.getConvertType();
        String convertTypeDesc = abmReleaseProtectDto.getConvertTypeDesc();
        log.info("{} 生成流转日志：{},{}", abmReleaseProtectDto.getCustId(), convertType, convertTypeDesc);
        smaConvertLogThirdService.insertLog(convertType, convertTypeDesc, abmReleaseProtectDto.getOperator(), cmCustProtect, releaseCmCustProtect);
        return Optional.empty();

    }

    /***
     * 更新客户表-是否需要回执,并且更新保护关系
     * @param customerId
     * @param operator
     * <AUTHOR>
     * @date 2025/8/11 21:46
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
    **/
    public Optional<String> clearRequireReceiptFlag(String customerId,String operator, String customerName){
        // 更新客户表-回执到期日期
        CustomerUpdateDubboDto customerUpdateDubboDto = new CustomerUpdateDubboDto();
        customerUpdateDubboDto.setCustomerId(customerId);
        customerUpdateDubboDto.setCustomerName(customerName);
        customerUpdateDubboDto.setOperator(operator);
        customerUpdateDubboDto.setSourceKey(thirdCustomerConfig.getKey());
        customerUpdateDubboDto.setSourceSecret(thirdCustomerConfig.getSecret());
        customerUpdateDubboDto.setReceiptSalerIdIsNull(1);
        DubboResult<Boolean> update = customerDubbo.update(customerUpdateDubboDto);
        if (!update.checkSuccess()) {
            // 不阻断，流程往下走
            log.error("更新客户表-是否需要回执失败 customerUpdateDubboDto={}", JSON.toJSONString(customerUpdateDubboDto));
            return Optional.of("更新客户表-是否需要回执失败 customerUpdateDubboDto=" + JSON.toJSONString(customerUpdateDubboDto));
        }
        return Optional.empty();
    }

    /***
     * 删除指定客户的保护关系表
     * @param fromCustomerId
     * @param operator
     * @param convertType
     * @param convertTypeDesc
     * <AUTHOR>
     * @date 2025/8/14 18:04
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
    **/
    public Optional<String> deleteProtect(String fromCustomerId,String operator,Integer convertType,String convertTypeDesc, String newCustomerId){
        CmCustProtect cmCustProtect = cmCustProtectService.getOne(new LambdaQueryWrapper<CmCustProtect>()
                .eq(CmCustProtect::getCustId, fromCustomerId));
        log.info("当前客户{} 保护关系详情 = {}", fromCustomerId, JSON.toJSONString(cmCustProtect));
        String id = cmCustProtect.getId();
        log.info("开始删除 id={}的记录",id);
        boolean removed = cmCustProtectService.remove(new LambdaQueryWrapper<CmCustProtect>()
                .eq(CmCustProtect::getId, id));
        if (!removed) {
            log.error("删除 fromCustomerId={} 保护关系失败 ",fromCustomerId);
            return Optional.of("处理保护关系失败");
        }else {
//            log.info("{} 生成流转日志：{},{}", fromCustomerId, convertType, convertTypeDesc);
//            CmCustProtect cmCustProtectTarget = new CmCustProtect();
//            cmCustProtectTarget.setCustId(newCustomerId);
//            smaConvertLogThirdService.insertLog(convertType, convertTypeDesc, operator, cmCustProtect, cmCustProtectTarget);
            return Optional.empty();
        }
    }


}
