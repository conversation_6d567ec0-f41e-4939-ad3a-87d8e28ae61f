package com.ce.scrm.center.service.business.entity.dto.follow;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/8/11
 */
@Data
public class CustomerFollowBusinessCreateDto implements Serializable {
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 联系人ID
     */
    private String contactPersonId;
    /**
     * 联系人名称
     */
    private String contactPersonName;
    /**
     * 联系人手机号
     */
    private String contactPersonMobile;

    /**
     * 省
     */
    private String provinceCode;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 市
     */
    private String cityCode;

    /**
     * 市
     */
    private String cityName;

    /**
     * 区
     */
    private String districtCode;

    /**
     * 区
     */
    private String districtName;

    /**
     * 通讯地址
     */
    private String address;

    /**
     * 跟进方式
     * VisitTypeEnum
     */
    private Integer visitType;

    /**
     * 电话接通情况
     */
    private String followCallStatus;

    /**
     * 意向产品列表
     */
    private List<String> intentionProductList;

    /**
     * 跟进内容
     */
    private String content;

    /**
     * 附件文件ID 列表
     */
    private List<String> fileIds;


    // 登录相关信息
    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 员工名称
     */
    private String loginEmployeeName;
    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;

    /**
     * 事业部id
     */
    private String loginBuId;

    /**
     * 事业部名称
     */
    private String loginBuName;

    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;
    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;
    /**
     * 手机号
     */
    private String loginMobile;
    /**
     * 工作邮箱
     */
    private String loginWorkMail;
    /**
     * 职位
     */
    private String loginPosition;
    /**
     * 职级
     */
    private String loginJobGrade;
}
