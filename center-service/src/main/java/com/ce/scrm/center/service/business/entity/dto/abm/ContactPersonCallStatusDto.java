package com.ce.scrm.center.service.business.entity.dto.abm;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import lombok.Data;

import java.io.Serializable;

/**
 * @project scrm-center
 * <AUTHOR>
 * @date 2025/7/16 11:09:16
 * @version 1.0
 * 查询客户ES业务层查询条件
 * 由于采用统一的ES查询方式，查询的字段都采用String，尽量不采用Integer等数据类型,Date等时间类型!!!
 */
@Data
public class ContactPersonCallStatusDto extends SignData implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 分司ID
     */
    private String loginSubId;
}