package com.ce.scrm.center.service.business.entity.mq;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 企业微信消息实体
 * <AUTHOR>
 * @date 2023/12/6
 * @version 1.0.0
 */
@Data
public class WechatMessageSendMqData implements Serializable {

    private List<String> toUserList;

    private List<String> toPartyList;

    private String toTag;

    /**
     * 消息类型 支持推送文本（text）、图片（image）、语音（voice）、视频（video）、文件（file）、卡片（textcard）等类型
     */
    private String msgType;

    /**
     * agentId，默认是营销平台应用
     */
    private String agentId;

    /**
     * 是否安全发送，默认0
     */
    private int safe;

    /**
     * 卡片类型的消息
     */
    TextCardMessage textCard;

    /**
     * 文本类型的消息
     */
    TextMessage text;

    /**
     * 模板消息的模板id
     */
    private Integer templateId;

    /**
     * sourceKey
     */
    private String sourceKey;
}
