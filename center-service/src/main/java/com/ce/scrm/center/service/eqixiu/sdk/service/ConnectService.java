package com.ce.scrm.center.service.eqixiu.sdk.service;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.Result;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Token;
import com.ce.scrm.center.service.eqixiu.sdk.support.*;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;
import com.ce.scrm.center.service.eqixiu.sdk.util.SHA256Util;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 提供通用接口
 *
 * <AUTHOR>
 */
public class ConnectService {

    protected static final Map<String, String> HEADER_JSON = Collections.singletonMap("Content-Type", "application/json");

    /**
     * 生产服务开放平台接口服务地址
     */
    private static final String SERVER_ADDR_PUD = "https://open.eqxiu.cn";
    private static final String API_TOKEN = "/api/v1/base/oauth/token";
    private static final String API_LOGIN_CODE = "/api/v1/base/oauth/code";
    private static final String API_LOGIN_OUT = "/api/v1/base/auth/logout";
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    private final Secret secret;
    protected BaseHttpClient httpClient;
    protected TokenCache tokenCache;
    private String serverAddr = SERVER_ADDR_PUD;

    public ConnectService(Secret secret) {
        this.secret = secret;
        this.httpClient = new BaseHttpClient(new ApacheHttpClient());
        tokenCache = new SimpleTokenCache();
    }

    public ConnectService(Secret secret, TokenCache tokenCache) {
        this.secret = secret;
        this.tokenCache = tokenCache;
        this.httpClient = new BaseHttpClient(new ApacheHttpClient());
    }

    public ConnectService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        this.secret = secret;
        this.httpClient = new BaseHttpClient(httpClient);
        this.tokenCache = tokenCache;
    }

    public ConnectService(Secret secret, String serverAddr) {
        this.secret = secret;
        this.tokenCache = new SimpleTokenCache();
        this.httpClient = new BaseHttpClient(new ApacheHttpClient());
        this.serverAddr = serverAddr;
    }

    /**
     * token 有调用次数限制，默认有效期2小时，添加了本地缓存
     *
     * @return token
     */
    public Token getToken() {
        Token token = tokenCache.get();
        if (token != null) {
            return token;
        }
        String timestamp = String.valueOf(System.currentTimeMillis());
        Map<String, String> param = new HashMap<>();
        param.put("secretId", secret.getSecretId());
        param.put("secretKey", secret.getSecretKey());
        param.put("type", "Server");
        param.put("timestamp", timestamp);

        String sign = SHA256Util.getSHA256Str(secret.getSecretId(), secret.getSecretKey(), "Server", timestamp);
        param.put("signature", sign);
        JSONObject result = httpClient.httpPost(getApiURL(API_TOKEN), null, param);
        if (result == null) {
            logger.error("get token failed");
            return null;
        }
        if (!result.getSuccess()) {
            logger.error("get token failed, msg:{}", result.getMsg());
            return null;
        }
        JSONObject map = result.getJSONObject("map");
        token = new Token(map.getStr("token"), map.getStr("corpId"), map.getInt("expires"));
        tokenCache.put(token);
        return token;
    }

    /**
     * 获取单点登录临时授权码，用于页面集成时员工账号可以直接登录易企秀
     * 该授权码只能使用一次，且一次性有效，不能重复使用。
     * 目前有效期为5分钟。
     *
     * @param openId
     * @param token
     * @return
     */
    public String getSSOLoginCode(String openId, String token) {
        String paramUrl = "?token=" + token + "&openId=" + openId;
        Map<String, String> param = new HashMap<>();
        param.put("openId", openId);
        JSONObject result = httpClient.httpPost(getApiURL(API_LOGIN_CODE + paramUrl), null, param);
        printLog(result, "getSSOLoginCode fail:{}");
        return result.getStr("obj");
    }

    /**
     * 注销登录
     *
     * @param openId
     * @param accessToken
     * @return
     */
    public boolean logout(String openId, String accessToken) {
        String paramUrl = "?token=" + accessToken + "&openId=" + openId;
        Map<String, String> param = new HashMap<>();
        param.put("openId", openId);
        JSONObject result = httpClient.httpPost(getApiURL(API_LOGIN_OUT + paramUrl), null, param);
        printLog(result, "logout fail:{}");
        return result.getSuccess();
    }

    protected String getApiURL(String api) {
        return serverAddr + api;
    }

    /**
     * 当请求 POST 接口时，需要给 URL 上拼接必须参数
     * 目前的必须参数为 token，openId，openIds
     *
     * @param api
     * @param params
     * @return
     */
    protected String getApiURL(String api, Map<String, String> params) {
        String url = serverAddr + api;
        if (params == null || params.isEmpty()) {
            return url;
        }
        url += "?token=" + params.get("token");
        params.remove("token");
        String openId = params.get("openId");
        if (StrUtil.isNotEmpty(openId)) {
            url += "&openId=" + openId;
            params.remove("openId");
        }
        String openIds = params.get("openIds");
        if (StrUtil.isNotEmpty(openIds)) {
            url += "&openIds=" + openIds;
            params.remove("openIds");
        }
        return url;
    }

    protected HashMap<String, String> createParamMapWithToken() {
        HashMap<String, String> map = new HashMap<>();
        map.put("token", getToken().getValue());
        return map;
    }

    protected void printLog(JSONObject obj, String format) {
        if (obj != null && !obj.getSuccess()) {
            logger.warn(format, obj.getMsg());
        }
    }

    /**
     * 参数校验并设置token
     *
     * @param cmd
     */
    protected void paramValidate(BaseParam cmd) {
        cmd.setToken(getToken().getValue());
        cmd.validate();
    }

    /**
     * 基于返回结果封装Result
     *
     * @param object
     * @param cType
     * @param <T>
     * @return
     */
    protected <T> Result<T> getResult(JSONObject object, Class<T> cType) {
        Result<T> result = new Result<>(object.getSuccess(), object.getCode(), object.getMsg());
        result.setList(object.getList(cType));
        result.setMap(object.getResultMap());
        result.setObj(object.getObj(cType));
        return result;
    }

    protected Secret getSecret() {
        return secret;
    }
}
