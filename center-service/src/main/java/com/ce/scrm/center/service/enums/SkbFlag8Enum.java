package com.ce.scrm.center.service.enums;

/**
 * description: 搜客宝的flag8枚举类（现代服务业）
 * https://wiki.300.cn/pages/viewpage.action?pageId=60269431
 * @author: DD.Jiu
 * date: 2025/2/18.
 */
public enum SkbFlag8Enum {
    flag8_1("flag8_1", "1", "律师事务所"),
    flag8_201("flag8_201", "201", "基础教育"),
    flag8_202("flag8_202", "202", "高等教育"),
    flag8_203("flag8_203", "203", "特殊教育"),
    flag8_204("flag8_204", "204", "培训机构"),
    flag8_3("flag8_3", "3", "医疗设备制造（医药制造）"),
    flag8_4("flag8_4", "4", "医药制品（医药制造）"),
    flag8_5("flag8_5", "5", "医院（医疗服务）"),
    flag8_6("flag8_6", "6", "疗养院及康复中心（医疗服务）"),
    flag8_7("flag8_7", "7", "体检机构（医疗服务）"),
    flag8_8("flag8_8", "8", "医美服务（医疗服务）"),
    flag8_9("flag8_9", "9", "基层医疗和公共卫生服务（医疗服务）"),
    flag8_10("flag8_10", "10", "医药零售（医疗服务）"),
    flag8_301("flag8_301", "301", "环境服务业"),
    flag8_302("flag8_302", "302", "能源科技制造业"),
    flag8_303("flag8_303", "303", "自然能源业"),
    flag8_401("flag8_401", "401", "物流运输业"),
    ;

    private String field;
    private String value;
    private String description;

    SkbFlag8Enum(String field, String value, String description) {
        this.field = field;
        this.value = value;
        this.description = description;
    }

    public String getField() {
        return field;
    }

    public String getDescription() {
        return description;
    }

    public String getValue() {
        return value;
    }

    public static SkbFlag8Enum getByValue(String value) {
        for (SkbFlag8Enum e : SkbFlag8Enum.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }

}

