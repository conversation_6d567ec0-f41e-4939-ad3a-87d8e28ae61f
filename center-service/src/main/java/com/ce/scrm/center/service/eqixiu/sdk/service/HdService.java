package com.ce.scrm.center.service.eqixiu.sdk.service;

import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.hd.ActivityStatQuery;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.hd.StatisticVO;
import com.ce.scrm.center.service.eqixiu.sdk.support.HttpClient;
import com.ce.scrm.center.service.eqixiu.sdk.support.TokenCache;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;

/**
 * 互动接口服务
 *
 * <AUTHOR>
 */
public class HdService extends ConnectService {

    private static final String API_LOTTERY_SUMMARY = "/api/v1/editor/lottery/record/summary/list";

    private static final String API_HD_COUNT_PV_AND_UV = "/api/v1/editor/hd/activity/day/statistics";

    private static final String API_HD_CREATION_WHITELIST = "/api/v1/editor/hd/whitelist";

    public HdService(Secret secret) {
        super(secret);
    }

    public HdService(Secret secret, TokenCache tokenCache) {
        super(secret, tokenCache);
    }

    public HdService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        super(secret, tokenCache, httpClient);
    }


    /**
     * 获取PV、UV、中奖人数等活动数据统计
     *
     * @param query
     * @return
     */
    public StatisticVO getActivityStat(ActivityStatQuery query) {
        paramValidate(query);
        JSONObject result = httpClient.httpGet(getApiURL(API_HD_COUNT_PV_AND_UV), null, query.getParamsMap());
        printLog(result, "查询活动统计数据失败:{}");
        return result.getObj(StatisticVO.class);
    }

}
