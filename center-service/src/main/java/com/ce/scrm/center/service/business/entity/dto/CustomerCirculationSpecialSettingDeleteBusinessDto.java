package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 成交客户流转流失特例配置表-添加请求体
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
public class CustomerCirculationSpecialSettingDeleteBusinessDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 员工ID
     */
    private String loginEmployeeId;

    /**
     * 分司ID
     */
    private String loginSubId;

}
