package com.ce.scrm.center.service.third.invoke;

import com.alibaba.fastjson.JSON;
import com.ce.call.center.dubbo.api.CallcenterDubbo;
import com.ce.call.center.dubbo.entity.dto.CallStatusDto;
import com.ce.call.center.dubbo.entity.response.DubboResult;
import com.ce.call.center.dubbo.entity.view.CallcenterStatusDetailView;
import com.ce.call.center.dubbo.entity.view.CallcenterStatusView;
import com.ce.scrm.center.service.business.entity.dto.CallStatusBusinessDto;
import com.ce.scrm.center.service.third.entity.dto.CallStatusThirdDto;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 线索分配三方业务
 * <AUTHOR>
 * @date 2025-01-16 10:10
 * @version 1.0.0
 **/
@Slf4j
@Service
public class CallCenterThirdService {

	@DubboReference(group="call-center-api", version="1.0.0", check=false, timeout=30000)
	private CallcenterDubbo callcenterDubbo;

	/**
	 * 获取拨打状态信息
	 * @param callStatusBusinessDto callStatusBusinessDto
	 * @return CallStatusThirdDto Map
	 */
	public CallStatusThirdDto getCallStatus(CallStatusBusinessDto callStatusBusinessDto) {
		if (callStatusBusinessDto == null) {
			return null;
		}
		CallStatusDto callStatusDto = new CallStatusDto();
		callStatusDto.setEmpId(callStatusBusinessDto.getEmpId());
		callStatusDto.setSubId(callStatusBusinessDto.getSubId());
		callStatusDto.setPhones(callStatusBusinessDto.getPhones());
		DubboResult<CallcenterStatusDetailView> callcenterStatusDetail = callcenterDubbo.callStatus(callStatusDto);
		if(!callcenterStatusDetail.checkSuccess() || null == callcenterStatusDetail.getData()) {
			log.warn("获取拨打状态信息异常，入参:{},返回值:{}", JSON.toJSONString(callStatusBusinessDto), JSON.toJSONString(callcenterStatusDetail));
			return null;
		}
		CallcenterStatusDetailView data = callcenterStatusDetail.getData();
		Map<String, CallcenterStatusView> callcenterStatusViewMap = data.getCallcenterStatusViewMap();
		// callcenterStatusViewMap 转为 CallStatusThirdDto
		CallStatusThirdDto callStatusThirdDto = new CallStatusThirdDto();
		Map<String, CallStatusThirdDto.CallCenterStatusThirdDto> callCenterStatusThirdDtoMap = callcenterStatusViewMap.entrySet()
			.stream()
			.collect(Collectors.toMap(
				Map.Entry::getKey,
				entry -> BeanCopyUtils.convertToVo(entry.getValue(), CallStatusThirdDto.CallCenterStatusThirdDto.class)
			));
		callStatusThirdDto.setCallcenterStatusViewMap(callCenterStatusThirdDtoMap);
		return callStatusThirdDto;
	}


}