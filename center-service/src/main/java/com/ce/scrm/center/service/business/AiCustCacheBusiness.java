package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.ce.cesupport.framework.base.vo.MapResultBean;
import cn.ce.cesupport.gj.service.CustomerWebsiteReportAppService;
import cn.ce.cesupport.gj.vo.CustomerWebsiteReportVo;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.ce.scrm.center.service.business.entity.ai.crm.CustomerFollowView;
import com.ce.scrm.center.service.business.entity.dto.AiBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.AiCacheBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.skb.*;
import com.ce.scrm.center.service.cache.AiCustCacheHandler;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CmCustVisitLogThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.extend.dubbo.api.AiCustCacheDubbo;
import com.ce.scrm.extend.dubbo.api.ExhibitionCompanyKjDubboService;
import com.ce.scrm.extend.dubbo.api.IBigDataDubbo;
import com.ce.scrm.extend.dubbo.api.ICompanyInfoEsDubbo;
import com.ce.scrm.extend.dubbo.entity.dto.ExhibitionCompanyDto;
import com.ce.scrm.extend.dubbo.entity.request.AiCustCacheReq;
import com.ce.scrm.extend.dubbo.entity.request.SkbParamsReq;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AiCustCacheBusiness {
    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private IBigDataDubbo iBigDataDubbo;
    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private AiCustCacheDubbo aiCustCacheDubbo;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private ICompanyInfoEsDubbo iCompanyInfoEsDubbo;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private ExhibitionCompanyKjDubboService exhibitionCompanyKjDubboService;

    /**
     * 业务平台生成网站报告
     */
    @DubboReference
    private CustomerWebsiteReportAppService customerWebsiteReportAppService;

    @Resource
    private AiCustCacheHandler aiCustCacheHandler;
    @Resource
    private AiCrmBusiness aiCrmBusiness;

    @Resource
    private AiCdpBusiness aiCdpBusiness;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private CmCustVisitLogThirdService cmCustVisitLogThirdService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    EmployeeThirdService employeeThirdService;

    private BigDataWorksCopyrightDto buildData3(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        DubboResult<BigDataWorksCopyrightDubboView> dubboResult = iBigDataDubbo.getWorksCopyrightByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataWorksCopyrightDubboView data = dubboResult.getData();
            return BeanUtil.copyProperties(data, BigDataWorksCopyrightDto.class);
        }
    }

    private BigDataWebsiteDto buildData2(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        DubboResult<BigDataWebsiteDubboView> dubboResult = iBigDataDubbo.getWebsiteByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataWebsiteDubboView data = dubboResult.getData();
            BigDataWebsiteDto dto = BeanUtil.copyProperties(data, BigDataWebsiteDto.class);
            if (CollectionUtils.isNotEmpty(dto.getList())) {
                for (BigDataWebsiteDto.ListWebsite listWebsite : dto.getList()) {
                    String authChuangyu = listWebsite.getAuth_chuangyu();
                    if (Objects.equals(authChuangyu, "1")) {
                        listWebsite.setAuth_chuangyu("认证");
                    } else if (Objects.equals(authChuangyu, "0")) {
                        listWebsite.setAuth_chuangyu("未认证");
                    } else {
                        listWebsite.setAuth_chuangyu(null);
                    }
                    String authCredibleBaike = listWebsite.getAuth_credible_baike();
                    if (Objects.equals(authCredibleBaike, "1")) {
                        listWebsite.setAuth_credible_baike("认证");
                    } else if (Objects.equals(authCredibleBaike, "0")) {
                        listWebsite.setAuth_credible_baike("未认证");
                    } else {
                        listWebsite.setAuth_credible_baike(null);
                    }
                    String authBaiduReputation = listWebsite.getAuth_baidu_reputation();
                    if (Objects.equals(authBaiduReputation, "1")) {
                        listWebsite.setAuth_baidu_reputation("认证");
                    } else if (Objects.equals(authBaiduReputation, "0")) {
                        listWebsite.setAuth_baidu_reputation("未认证");
                    } else {
                        listWebsite.setAuth_baidu_reputation(null);
                    }
                }
            }
            return dto;
        }
    }

    private JSONObject buildData(String pid) {
        DubboResult<BigDataContactPersonDetailView> dubboResult = iBigDataDubbo.getContactPersonDetailByPid(pid);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return new JSONObject();
        } else {
            List<BigDataContactsDetailView> list = dubboResult.getData().getList();
            // 李金澎 这里json转换可能有问题
            BigDataContactInformationDto dto = BeanUtil.copyProperties(list, BigDataContactInformationDto.class);
            return JSONObject.parseObject(JSONObject.toJSONString(dto));
        }
    }

    /***
     * 查询主要人员关联的企业信息
     * @param pid
     * @param lagalPerson
     * <AUTHOR>
     * @date 2025/2/18 09:18
     * @version 1.0.0
     * @return com.alibaba.fastjson.JSONObject
     **/
    private BigDataKeyPersonnelAffiliatedEnterpriseDtp assmbleKeyPersonnelAffiliatedEnterprise(String pid, String lagalPerson) {
        DubboResult<BigDataAffiliatedEnterpriseDubboView> affiliatedEnterprise = iBigDataDubbo.getAffiliatedEnterprise(pid, lagalPerson);
        if (affiliatedEnterprise == null || !affiliatedEnterprise.checkSuccess() || affiliatedEnterprise.getData() == null) {
            return null;
        } else {
            BigDataAffiliatedEnterpriseDubboView data = affiliatedEnterprise.getData();
            BigDataKeyPersonnelAffiliatedEnterpriseDtp dto = BeanUtil.copyProperties(data, BigDataKeyPersonnelAffiliatedEnterpriseDtp.class);
            if (dto != null && CollectionUtils.isNotEmpty(dto.getItems())) {
                dto.setItem(String.join(",", dto.getItems().stream().map(BigDataKeyPersonnelAffiliatedEnterpriseDtp.CustomerAffiliatedEnterpriseDetailWebView::getEntName).collect(Collectors.toList())));
            }
            return dto;
        }
    }

    private void checkData(JSONObject json) {


        // 统一去除分页参数
        json.remove("total_count");
        json.remove("total_page");
        json.remove("page");
        json.remove("uncid");
        json.remove("entname");

        // 去除 null "" [] 0 等情况
//        Set<String> keys = new CopyOnWriteArraySet<>(json.keySet());
//        for (String key : keys) {
//            Object value = json.get(key);
//
//            // 检查值是否为空
//            if (value == null || org.apache.commons.lang3.StringUtils.isBlank(value.toString()) || Objects.equals(value.toString(),"[]") || Objects.equals(value.toString(),"0")) {
//                json.remove(key);
//            }
//        }
    }

    public void recursiveTraverse(Object obj) {
        if (obj instanceof JSONObject) {
            // 如果是JSONObject，遍历其键值对
            for (String key : ((JSONObject) obj).keySet()) {
                Object value = ((JSONObject) obj).get(key);
                if (value == null || org.apache.commons.lang3.StringUtils.isBlank(value.toString()) || Objects.equals(value.toString(), "[]")) {
                    ((JSONObject) obj).put(key, null);
                }
//                System.out.println(indent + "Key: " + key + ", Value: " + value);
                // 如果值是JSONObject或JSONArray，递归遍历
                if (value instanceof JSONObject || value instanceof JSONArray) {
                    recursiveTraverse(value);
                }
            }
        } else if (obj instanceof JSONArray) {
            // 如果是JSONArray，遍历其元素
            for (int i = 0; i < ((JSONArray) obj).size(); i++) {
                Object value = ((JSONArray) obj).get(i);
                if (value == null || org.apache.commons.lang3.StringUtils.isBlank(value.toString()) || Objects.equals(value.toString(), "[]")) {
                    ((JSONArray) obj).set(i, null);
                }
//                System.out.println(indent + "Element " + i + ": " + value);
                // 如果元素是JSONObject或JSONArray，递归遍历
                if (value instanceof JSONObject || value instanceof JSONArray) {
                    recursiveTraverse(value);
                }
            }
        }
    }

    /**
     * Description: 获取企业基本信息
     *
     * @param pid date: 2025/2/13 11:07
     * @author: JiuDD
     */
    public BigDataCompanyDetailDto assembleCompanyDetail(String pid) {
        DubboResult<BigDataCompanyDetailView> dubboResult = iBigDataDubbo.getCompanyDetailByPid(pid);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataCompanyDetailView view = dubboResult.getData();
            BigDataCompanyDetailDto dto = BeanUtil.copyProperties(view, BigDataCompanyDetailDto.class);
            if (StringUtils.isNotBlank(dto.getReg_cap())) {
                if (StringUtils.isNotBlank(dto.getReg_cap_cur())) {
                    dto.setReg_cap(dto.getReg_cap() + dto.getReg_cap_cur());
                } else {
                    dto.setReg_cap(dto.getReg_cap() + "元");
                }
            }
            return dto;
        }
    }

    /**
     * Description: 获取企业简介
     *
     * @param pid date: 2025/2/13 11:07
     * @author: JiuDD
     */
    private BigDataEntProfileDto assembleEntProfile(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        DubboResult<BigDataEntProfileDubboView> dubboResult = iBigDataDubbo.getEntProfileByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataEntProfileDubboView view = dubboResult.getData();
            //skbInfo.put("企业简介", bigDataEntProfileView.getEnt_profile());
            return BeanUtil.copyProperties(view, BigDataEntProfileDto.class);
        }
    }

    /**
     * Description: 获取上市公司
     *
     * @param pid date: 2025/2/13 12:07
     * @author: JiuDD
     */
    private BigDataListedentDto assembleListedent(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        DubboResult<BigDataListedentDubboView> dubboResult = iBigDataDubbo.getListedentByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataListedentDubboView view = dubboResult.getData();
            BigDataListedentDto dto = BeanUtil.copyProperties(view, BigDataListedentDto.class);
            if (Objects.nonNull(dto.getListed_date()) && dto.getListed_date() > 0L) {
                dto.setIsMarket("是");
            }
            if (Objects.nonNull(dto.getListed_date()) && dto.getListed_date() > 0L) {
                Date date = new Date(dto.getListed_date());
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dto.setFloatationOfSharesTime(sdf.format(date));
            }
            return dto;
        }
    }

    /**
     * Description: 获取称号标签
     *
     * @param pid date: 2025/2/13 13:07
     * @author: JiuDD
     */
    private BigDataTagsDto assembleTags(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        DubboResult<BigDataTagsDubboView> dubboResult = iBigDataDubbo.getTagsByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataTagsDubboView view = dubboResult.getData();
            BigDataTagsDto dto = BeanUtil.copyProperties(view, BigDataTagsDto.class);
            if (dto.getIs_top_500() != null) {
                dto.setIs_top_500_str(1 == dto.getIs_top_500() ? "是" : "否");
            }
            if (dto.getIs_unicorn() != null) {
                dto.setIs_unicorn_str(1 == dto.getIs_unicorn() ? "是" : "否");
            }
            if (dto.getIs_gazelle() != null) {
                dto.setIs_gazelle_str(1 == dto.getIs_gazelle() ? "是" : "否");
            }
            return dto;
        }
    }

    /**
     * Description: 获取品牌信息
     *
     * @param pid date: 2025/2/13 13:15
     * @author: JiuDD
     */
    private BigDataBrandInfoDto assembleBrandInfo(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataBrandInfoDubboView> dubboResult = iBigDataDubbo.getBrandInfoByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataBrandInfoDubboView view = dubboResult.getData();
            return BeanUtil.copyProperties(view, BigDataBrandInfoDto.class);
        }
    }

    /**
     * Description: 获取网店(电商数据)
     *
     * @param pid date: 2025/2/13 13:39
     * @author: JiuDD
     */
    private BigDataShopDto assembleShop(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataShopDubboView> dubboResult = iBigDataDubbo.getShopByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataShopDubboView view = dubboResult.getData();
            return BeanUtil.copyProperties(view, BigDataShopDto.class);
        }
    }

    /**
     * Description: 获取进出口信用
     *
     * @param pid date: 2025/2/13 13:49
     * @author: JiuDD
     */
    private BigDataImportandExportCreditDto assembleImportandExportCredit(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataImportandExportCreditDubboView> dubboResult = iBigDataDubbo.getImportandExportCreditByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataImportandExportCreditDubboView view = dubboResult.getData();
            return BeanUtil.copyProperties(view, BigDataImportandExportCreditDto.class);
        }
    }

    /**
     * Description: 获取网络推广
     *
     * @param pid date: 2025/2/13 13:59
     * @author: JiuDD
     */
    private BigDataPromotionDto assemblePromotion(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataPromotionDubboView> dubboResult = iBigDataDubbo.getPromotionByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataPromotionDubboView view = dubboResult.getData();
            return BeanUtil.copyProperties(view, BigDataPromotionDto.class);
        }
    }

    /**
     * 获取分支机构
     *
     * @param pid
     * @return
     */
    private BigDataBranchDto assembleBranch(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataBranchDubboView> dubboResult = iBigDataDubbo.getBranchByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        }
        BigDataBranchDubboView view = dubboResult.getData();
        return BeanUtil.copyProperties(view, BigDataBranchDto.class);
    }

    /***
     * 获取资质证书
     * @param pid
     * <AUTHOR>
     * @date 2025/2/18 09:15
     * @version 1.0.0
     * @return com.alibaba.fastjson.JSONObject
     **/
    private BigDataCertificateDto assembleCertificate(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataCertificateDubboView> dubboResult = iBigDataDubbo.getCertificateByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataCertificateDubboView view = dubboResult.getData();
            BigDataCertificateDto dto = BeanUtil.copyProperties(view, BigDataCertificateDto.class);
            if (dto != null && CollectionUtils.isNotEmpty(dto.getList())) {
                dto.setListString(dto.getList().stream().map(item -> item.getCert_name()).collect(Collectors.joining(",")));
            }
            return dto;
        }
    }

    /***
     * 获取对外投资
     * @param pid
     * <AUTHOR>
     * @date 2025/2/18 09:16
     * @version 1.0.0
     * @return com.alibaba.fastjson.JSONObject
     **/
    private BigDataInvestmentDto assembleInvestment(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataInvestmentDubboView> dubboResult = iBigDataDubbo.getInvestmentByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataInvestmentDubboView view = dubboResult.getData();
            return BeanUtil.copyProperties(view, BigDataInvestmentDto.class);
        }
    }

    /***
     * 获取专利信息
     * @param pid
     * <AUTHOR>
     * @date 2025/2/18 09:16
     * @version 1.0.0
     * @return com.alibaba.fastjson.JSONObject
     **/
    private BigDataPatentsDto assemblePatents(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataPatentsDubboView> dubboResult = iBigDataDubbo.getPatentsByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataPatentsDubboView view = dubboResult.getData();
            BigDataPatentsDto dto = BeanUtil.copyProperties(view, BigDataPatentsDto.class);
            if (dto != null && CollectionUtils.isNotEmpty(dto.getList())) {
                dto.setListString(dto.getList().stream().map(item -> item.getPatent_name()).collect(Collectors.joining(",")));
            }
            return dto;
        }
    }

    /***
     * 获取招聘信息
     * @param pid
     * <AUTHOR>
     * @date 2025/2/18 09:16
     * @version 1.0.0
     * @return com.alibaba.fastjson.JSONObject
     **/
    private BigDataRecruitmentDto assembleRecruitment(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataRecruitmentDubboView> dubboResult = iBigDataDubbo.getRecruitmentByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataRecruitmentDubboView view = dubboResult.getData();
            return BeanUtil.copyProperties(view, BigDataRecruitmentDto.class);
        }
    }

    /***
     * 获取软件著作权信息
     * @param pid
     * <AUTHOR>
     * @date 2025/2/18 09:17
     * @version 1.0.0
     * @return com.alibaba.fastjson.JSONObject
     **/
    private BigDataSoftwareCopyrightDto assembleSoftwareCopyright(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataSoftwareCopyrightDubboView> dubboResult = iBigDataDubbo.getSoftwareCopyrightByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataSoftwareCopyrightDubboView view = dubboResult.getData();
            return BeanUtil.copyProperties(view, BigDataSoftwareCopyrightDto.class);
        }
    }

    /***
     * 获取商标信息
     * @param pid
     * <AUTHOR>
     * @date 2025/2/18 09:17
     * @version 1.0.0
     * @return com.alibaba.fastjson.JSONObject
     **/
    private BigDataTrademarkDto assembleTrademark(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataTrademarkDubboView> dubboResult = iBigDataDubbo.getTrademarkByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataTrademarkDubboView view = dubboResult.getData();
            BigDataTrademarkDto dto = BeanUtil.copyProperties(view, BigDataTrademarkDto.class);
            if (dto != null && CollectionUtils.isNotEmpty(dto.getList())) {
                List<BigDataTrademarkDto.ListTrademark> list = dto.getList().stream().filter(record -> StringUtils.isNotBlank(record.getMark_name())).collect(Collectors.toList());
                list.forEach(item -> {
                    if (Objects.nonNull(item.getIs_common_mark())) {
                        item.setIs_common_mark_str(1 == item.getIs_common_mark() ? "是" : "否");
                    }
                });
                dto.setList(list);
            } else {
                dto.setList(new ArrayList<>());
            }
            return dto;
        }
    }

    /***
     * 获取参展信息
     * @param pid
     * <AUTHOR>
     * @date 2025/2/18 09:18
     * @version 1.0.0
     * @return com.alibaba.fastjson.JSONObject
     **/
    private BigDataTradeshowDto assembleTradeshow(String pid) {
        SkbParamsReq req = new SkbParamsReq();
        req.setPid(pid);
        req.setPage(1);
        DubboResult<BigDataTradeshowDubboView> dubboResult = iBigDataDubbo.getTradeshowByPid(req);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            return null;
        } else {
            BigDataTradeshowDubboView view = dubboResult.getData();
            BigDataTradeshowDto dto = BeanUtil.copyProperties(view, BigDataTradeshowDto.class);
            if (dto != null && CollectionUtils.isNotEmpty(dto.getList())) {
                dto.setListString(dto.getList().stream().map(item -> item.getItem()).collect(Collectors.joining(",")));
            }
            return dto;
        }
    }

    public void saveStreamAnswerCache(AiCacheBusinessDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getMessageResult())) {
            return;
        }
        try {
            AiCustCacheReq streamAnswer = JSONObject.parseObject(dto.getMessageResult(), AiCustCacheReq.class);
            // content、reasoningContent任一字段有值，则放入cache
            if (Objects.nonNull(streamAnswer) && (StringUtils.isNotBlank(streamAnswer.getContent()) || StringUtils.isNotBlank(streamAnswer.getReasoningContent()))) {
                AiCustCacheReq req = new AiCustCacheReq();
                BeanUtil.copyProperties(streamAnswer, req);
                req.setPid(dto.getPid());
                req.setCustId(dto.getCustId());
                JSONObject jsonObject = JSONObject.parseObject(streamAnswer.getExtraInfo());
                String promptId = jsonObject.getString("promptId");
                if (StringUtils.isNotBlank(promptId)) {
                    req.setPromptId(Long.valueOf(promptId));
                }
                req.setExtraInfo(streamAnswer.getExtraInfo());
                DubboResult<AiCustCacheDubboView> saveResult = aiCustCacheDubbo.save(req);
                if (!saveResult.checkSuccess()) {
                    log.error("ai响应数据存入缓存失败，pid:{}");
                }
                if (StringUtils.isNotBlank(dto.getPid())) {
                    aiCustCacheHandler.del(dto.getPid() + "_" + promptId);
                }
            }
        } catch (Exception e) {
            log.error("ai模型返回数据解析失败", e);
        }
    }

    /**
     * 获取交易信息-已购产品列表
     *
     * @param custId 客户ID
     * @return JSONObject
     */
    public JSONArray assembleDealingsList(String custId) {
        com.ce.scrm.center.service.business.entity.dto.ai.AiBusinessDto aiBusinessDto = new com.ce.scrm.center.service.business.entity.dto.ai.AiBusinessDto();
        aiBusinessDto.setCustomerId(custId);
        String customerDealings = aiCrmBusiness.getCustomerDealings(aiBusinessDto);
        return JSONObject.parseArray(customerDealings);
    }

    /**
     * 跟进记录
     *
     * @param custId 客户ID
     * @return JSONObject
     */
    public List<CustomerFollowView> assembleFollowup(String custId) {
        com.ce.scrm.center.service.business.entity.dto.ai.AiBusinessDto aiBusinessDto = new com.ce.scrm.center.service.business.entity.dto.ai.AiBusinessDto();
        aiBusinessDto.setCustomerId(custId);
        return cmCustVisitLogThirdService.getVisitLogsBySiteClockRecordIdListByCustId(aiBusinessDto.getCustomerId()).stream().map(visitLog -> {
            CustomerFollowView customerFollowView = new CustomerFollowView();
            customerFollowView.setContent(visitLog.getContent());
            customerFollowView.setFollowTime(visitLog.getCreateTime());
            return customerFollowView;
        }).collect(Collectors.toList());
    }

    public JSONObject buildJsonData(AiBusinessDto businessDto) {

        JSONObject resultJson = new JSONObject(new LinkedHashMap<>());

        if (businessDto == null) {
            return resultJson;
        }

        String pid = businessDto.getPid();
        getSkbDataCache(pid, resultJson);

        String custId = businessDto.getCustId();
        if (!StringUtils.isEmpty(custId)) {
            try {
                JSONArray dealingsInfo = assembleDealingsList(custId);
                //TODO 20250618 去掉交易数据
                //resultJson.put("交易数据-已购产品列表", dealingsInfo);
                List<CustomerFollowView> customerFollowViewList = assembleFollowup(custId);
                // 不为空大于 50字
                customerFollowViewList = customerFollowViewList.stream().filter(T -> StringUtils.isNotBlank(T.getContent()) && T.getContent().trim().length() > 50)
//                        .peek(T -> {
//                            //意向等级 0无；1弱；2中；3强
//                            if (Objects.equals(T.getIntentionLevel(), "0")) {
//                                T.setIntentionLevel("无");
//                            }
//                            if (Objects.equals(T.getIntentionLevel(), "1")) {
//                                T.setIntentionLevel("弱");
//                            }
//                            if (Objects.equals(T.getIntentionLevel(), "2")) {
//                                T.setIntentionLevel("中");
//                            }
//                            if (Objects.equals(T.getIntentionLevel(), "3")) {
//                                T.setIntentionLevel("强");
//                            }
//                        })
                        .limit(10)
                        .collect(Collectors.toList());
                //TODO 20250618 去掉跟进记录
//                if (CollectionUtils.isNotEmpty(customerFollowViewList)) {
//                    resultJson.put("中企商务的跟进记录", customerFollowViewList);
//                }
            } catch (Exception e) {
                log.warn("获取信息失败", e);
            }
        }

        String busiOppoCode = businessDto.getBusiOppoCode();
        if (!StringUtils.isEmpty(busiOppoCode)) {
            JSONObject businessOpportunityView = buildBusiOppoData(busiOppoCode);
            resultJson.put("商机信息", businessOpportunityView);
        }
//        log.info("checkData 入参json={}", resultJson.toJSONString());
//        recursiveTraverse(resultJson);
        checkData(resultJson);
        String replace = null;
        try {
            replace = resultJson.toJSONString().replace("\\\"", "").replace("\\\"", "").replace("[]", "null").replace("\"\"", "null");
            replace = JSONObject.parseObject(replace, Feature.OrderedField).toJSONString();
        } catch (Exception e) {
            log.error("json 剔除 空字符串null值 异常", e);
            replace = JSONObject.parseObject(resultJson.toJSONString(), Feature.OrderedField).toJSONString();
        }
//        log.info("checkData 出参json={}", replace);
        return JSONObject.parseObject(replace, Feature.OrderedField);
    }

    private void getSkbDataCache(String pid, JSONObject resultJson) {
        if (pid == null) {
            return;
        }


        String cacheKey = "SCRM:ENTER:AI:SKB:DATA" + pid;

        Object o = redisTemplate.opsForValue().get(cacheKey);
        if (o != null) {
            JSONObject skbData = JSONObject.parseObject(o.toString(), Feature.OrderedField);
            resultJson.putAll(skbData);
            return;
        }

        JSONObject skbData = getSkbData(pid);
        resultJson.putAll(skbData);
        redisTemplate.opsForValue().set(cacheKey, skbData.toJSONString(), 2, TimeUnit.HOURS);
    }

    private JSONObject getSkbData(String pid) {
        JSONObject resultJson = new JSONObject(new LinkedHashMap<>());
        if (!StringUtils.isEmpty(pid)) {
            // 企业基本信息
            BigDataCompanyDetailDto bigDataCompanyDetailDto = assembleCompanyDetail(pid);
            if (bigDataCompanyDetailDto != null) {
                resultJson.put("企业名称", bigDataCompanyDetailDto.getEntname());
                resultJson.put("企业基本信息", bigDataCompanyDetailDto);
            } else {
                log.info("pid返回的企业基本信息为空,pid={}", pid);
                throw new ApiException(CodeMessageEnum.DATA_NOT_EXIST);
            }
            // 企业简介
            BigDataEntProfileDto bigDataEntProfileDto = assembleEntProfile(pid);
            resultJson.put("企业简介", bigDataEntProfileDto == null ? null : bigDataEntProfileDto.getEnt_profile());
            // 上市公司
            BigDataListedentDto bigDataListedentDto = assembleListedent(pid);
            if (bigDataListedentDto != null) {
                Map<String, String> map = new HashMap<>();
                if (bigDataListedentDto.getEmp_num() != null && bigDataListedentDto.getEmp_num() > 0) {
                    map.put("员工人数", String.valueOf(bigDataListedentDto.getEmp_num()));
                }
                if (StringUtils.isNotBlank(bigDataListedentDto.getFloatationOfSharesTime()) && !Objects.equals("0", bigDataListedentDto.getFloatationOfSharesTime())) {
                    map.put("股票上市时间", String.valueOf(bigDataListedentDto.getFloatationOfSharesTime()));
                }
                if (!map.isEmpty()) {
                    resultJson.put("上市信息", map);
                }
            }

            // 子公司与分支机构
            BigDataBranchDto bigDataBranchDto = assembleBranch(pid);
            resultJson.put("分支机构列表", bigDataBranchDto == null ? null : bigDataBranchDto.getList());
            // 称号标签
            BigDataTagsDto bigDataTagsDto = assembleTags(pid);
            resultJson.put("称号标签", bigDataTagsDto);
            // 品牌信息
            BigDataBrandInfoDto bigDataBrandInfoDto = assembleBrandInfo(pid);
            resultJson.put("品牌信息", bigDataBrandInfoDto == null ? null : bigDataBrandInfoDto.getList());
            // 网店(电商数据)
            BigDataShopDto bigDataShopDto = assembleShop(pid);
            resultJson.put("网店店铺列表", bigDataShopDto == null ? null : bigDataShopDto.getList().stream().filter(T -> StringUtils.isNotBlank(T.getShop_product()) && StringUtils.isNotBlank(T.getShop_brand()) && null != T.getShop_create_date()).collect(Collectors.toList()));
            // 进出口信用
            BigDataImportandExportCreditDto bigDataImportandExportCreditDto = assembleImportandExportCredit(pid);
            resultJson.put("进出口信用列表", bigDataImportandExportCreditDto == null ? null : bigDataImportandExportCreditDto.getList());
            // 网络推广
            BigDataPromotionDto bigDataPromotionDto = assemblePromotion(pid);
            resultJson.put("网络推广列表", bigDataPromotionDto == null ? null : bigDataPromotionDto.getList());
            // 资质证书
            BigDataCertificateDto bigDataCertificateDto = assembleCertificate(pid);
            resultJson.put("资质证书列表", bigDataCertificateDto == null ? null : bigDataCertificateDto.getListString());
            // 对外投资
            BigDataInvestmentDto bigDataInvestmentDto = assembleInvestment(pid);
            resultJson.put("对外投资企业列表", bigDataInvestmentDto == null ? null : bigDataInvestmentDto.getList());
            // 专利信息
            BigDataPatentsDto bigDataPatentsDto = assemblePatents(pid);
            resultJson.put("专利列表", bigDataPatentsDto == null ? null : bigDataPatentsDto.getListString());
            // 招聘信息
            BigDataRecruitmentDto bigDataRecruitmentDto = assembleRecruitment(pid);
            resultJson.put("招聘信息列表", bigDataRecruitmentDto == null ? null : bigDataRecruitmentDto.getList());
            // 软件著作权信息
            BigDataSoftwareCopyrightDto bigDataSoftwareCopyrightDto = assembleSoftwareCopyright(pid);
            resultJson.put("软件著作权列表", bigDataSoftwareCopyrightDto == null ? null : bigDataSoftwareCopyrightDto.getList());
            // 商标信息
            BigDataTrademarkDto bigDataTrademarkDto = assembleTrademark(pid);
            resultJson.put("商标信息列表", bigDataTrademarkDto == null ? null : bigDataTrademarkDto.getList());
            // 参展信息
            BigDataTradeshowDto bigDataTradeshowDto = assembleTradeshow(pid);
            resultJson.put("参展信息列表", bigDataTradeshowDto == null ? null : bigDataTradeshowDto.getListString());
            // 企业联系方式
//        JSONObject jsonObject1 = buildData(pid);
            // 网站信息
            BigDataWebsiteDto bigDataWebsiteDto = buildData2(pid);
            resultJson.put("网站信息列表", bigDataWebsiteDto == null ? null : bigDataWebsiteDto.getList());
            // 作品著作权
            BigDataWorksCopyrightDto bigDataWorksCopyrightDto = buildData3(pid);
            resultJson.put("作品著作列表", bigDataWorksCopyrightDto == null ? null : bigDataWorksCopyrightDto.getList());
            //查询主要人员关联的企业信息
            String lagalPerson = bigDataCompanyDetailDto == null ? null : bigDataCompanyDetailDto.getLegal_person();
            if (StringUtils.isNotBlank(lagalPerson)) {
                BigDataKeyPersonnelAffiliatedEnterpriseDtp bigDataKeyPersonnelAffiliatedEnterpriseDtp = assmbleKeyPersonnelAffiliatedEnterprise(pid, lagalPerson);
                resultJson.put("主要人员关联的企业信息列表", bigDataKeyPersonnelAffiliatedEnterpriseDtp == null ? null : bigDataKeyPersonnelAffiliatedEnterpriseDtp.getItem());
            }
            //通过elasticsearch 获取其他信息
            try {
                List<CompanyInfoDubboView> companyInfoDubboViewList = iCompanyInfoEsDubbo.listByPid(pid);
                if (CollectionUtils.isNotEmpty(companyInfoDubboViewList)) {
                    CompanyInfoDubboView companyInfoDubboView = companyInfoDubboViewList.get(0);
                    if (companyInfoDubboView.getIsGeneralTaxpayer()) {
                        resultJson.put("是否为一般纳税人", "是");
                    } else {
                        resultJson.put("是否为一般纳税人", "否");
                    }
                    if (companyInfoDubboView.getHasATaxCredit()) {
                        resultJson.put("是否为A级纳税人", "是");
                    } else {
                        resultJson.put("是否为A级纳税人", "否");
                    }
                    if (CollectionUtils.isNotEmpty(companyInfoDubboView.getFlag7())) {
                        resultJson.put("规上标签", String.join(",", companyInfoDubboView.getFlag7()));
                    }
                    if (CollectionUtils.isNotEmpty(companyInfoDubboView.getFlag8())) {
                        resultJson.put("现代服务业标签", String.join(",", companyInfoDubboView.getFlag8()));
                    }
                    if (CollectionUtils.isNotEmpty(companyInfoDubboView.getTechTypeCompany())) {
                        resultJson.put("科技型企业标签", String.join(",", companyInfoDubboView.getTechTypeCompany()));
                    }
                    if (CollectionUtils.isNotEmpty(companyInfoDubboView.getCertL1Type())) {
                        resultJson.put("涵盖证书类型", String.join(",", companyInfoDubboView.getCertL1Type()));
                    }
                    if (StringUtils.isNotBlank(companyInfoDubboView.getB2bProduct())) {
                        resultJson.put("主营产品", companyInfoDubboView.getB2bProduct());
                    }
                    if (CollectionUtils.isNotEmpty(companyInfoDubboView.getFirstEmergingIndustry())) {
                        resultJson.put("新兴行业标签", String.join(",", companyInfoDubboView.getFirstEmergingIndustry()));
                    }
                }
            } catch (Exception e) {
                log.info("调用skb elasitcsearch获取主营产品失败,pid={}", pid);
            }
        }
        return resultJson;
    }


    public JSONObject buildJsonDataTemp(AiBusinessDto businessDto) {

        JSONObject resultJson = new JSONObject(new LinkedHashMap<>());

        if (businessDto == null) {
            return resultJson;
        }

        String pid = businessDto.getPid();
        if (!StringUtils.isEmpty(pid)) {
            // 企业基本信息
            BigDataCompanyDetailDto bigDataCompanyDetailDto = assembleCompanyDetail(pid);
            if (bigDataCompanyDetailDto != null) {
                resultJson.put("企业名称", bigDataCompanyDetailDto.getEntname());
                resultJson.put("企业基本信息", bigDataCompanyDetailDto);
            } else {
                log.info("pid返回的企业基本信息为空,pid={}", pid);
                throw new ApiException(CodeMessageEnum.DATA_NOT_EXIST);
            }
            // 企业简介
            BigDataEntProfileDto bigDataEntProfileDto = assembleEntProfile(pid);
            resultJson.put("企业简介", bigDataEntProfileDto == null ? null : bigDataEntProfileDto.getEnt_profile());
            // 上市公司
            BigDataListedentDto bigDataListedentDto = assembleListedent(pid);
            if (bigDataListedentDto != null) {
                Map<String, String> map = new HashMap<>();
                if (bigDataListedentDto.getEmp_num() != null && bigDataListedentDto.getEmp_num() > 0) {
                    map.put("员工人数", String.valueOf(bigDataListedentDto.getEmp_num()));
                }
                if (StringUtils.isNotBlank(bigDataListedentDto.getFloatationOfSharesTime()) && !Objects.equals("0", bigDataListedentDto.getFloatationOfSharesTime())) {
                    map.put("股票上市时间", String.valueOf(bigDataListedentDto.getFloatationOfSharesTime()));
                }
                if (!map.isEmpty()) {
                    resultJson.put("上市信息", map);
                }
            }

            // 子公司与分支机构
            BigDataBranchDto bigDataBranchDto = assembleBranch(pid);
            resultJson.put("分支机构列表", bigDataBranchDto == null ? null : bigDataBranchDto.getList());
            // 称号标签
            BigDataTagsDto bigDataTagsDto = assembleTags(pid);
            resultJson.put("称号标签", bigDataTagsDto);
            // 品牌信息
            BigDataBrandInfoDto bigDataBrandInfoDto = assembleBrandInfo(pid);
            resultJson.put("品牌信息", bigDataBrandInfoDto == null ? null : bigDataBrandInfoDto.getList());
            // 网店(电商数据)
            BigDataShopDto bigDataShopDto = assembleShop(pid);
            resultJson.put("网店店铺列表", bigDataShopDto == null ? null : bigDataShopDto.getList().stream().filter(T -> StringUtils.isNotBlank(T.getShop_product()) && StringUtils.isNotBlank(T.getShop_brand()) && null != T.getShop_create_date()).collect(Collectors.toList()));
            // 进出口信用
            BigDataImportandExportCreditDto bigDataImportandExportCreditDto = assembleImportandExportCredit(pid);
            resultJson.put("进出口信用列表", bigDataImportandExportCreditDto == null ? null : bigDataImportandExportCreditDto.getList());
            // 网络推广
            BigDataPromotionDto bigDataPromotionDto = assemblePromotion(pid);
            resultJson.put("网络推广列表", bigDataPromotionDto == null ? null : bigDataPromotionDto.getList());
            // 资质证书
            BigDataCertificateDto bigDataCertificateDto = assembleCertificate(pid);
            resultJson.put("资质证书列表", bigDataCertificateDto == null ? null : bigDataCertificateDto.getListString());
            // 对外投资
            BigDataInvestmentDto bigDataInvestmentDto = assembleInvestment(pid);
            resultJson.put("对外投资企业列表", bigDataInvestmentDto == null ? null : bigDataInvestmentDto.getList());
            // 专利信息
            BigDataPatentsDto bigDataPatentsDto = assemblePatents(pid);
            resultJson.put("专利列表", bigDataPatentsDto == null ? null : bigDataPatentsDto.getListString());
            // 招聘信息
            BigDataRecruitmentDto bigDataRecruitmentDto = assembleRecruitment(pid);
            resultJson.put("招聘信息列表", bigDataRecruitmentDto == null ? null : bigDataRecruitmentDto.getList());
            // 软件著作权信息
            BigDataSoftwareCopyrightDto bigDataSoftwareCopyrightDto = assembleSoftwareCopyright(pid);
            resultJson.put("软件著作权列表", bigDataSoftwareCopyrightDto == null ? null : bigDataSoftwareCopyrightDto.getList());
            // 商标信息
            BigDataTrademarkDto bigDataTrademarkDto = assembleTrademark(pid);
            resultJson.put("商标信息列表", bigDataTrademarkDto == null ? null : bigDataTrademarkDto.getList());
            // 参展信息
            BigDataTradeshowDto bigDataTradeshowDto = assembleTradeshow(pid);
            resultJson.put("参展信息列表", bigDataTradeshowDto == null ? null : bigDataTradeshowDto.getListString());
            // 企业联系方式
//        JSONObject jsonObject1 = buildData(pid);
            // 网站信息
            BigDataWebsiteDto bigDataWebsiteDto = buildData2(pid);
            resultJson.put("网站信息列表", bigDataWebsiteDto == null ? null : bigDataWebsiteDto.getList());
            // 作品著作权
            BigDataWorksCopyrightDto bigDataWorksCopyrightDto = buildData3(pid);
            resultJson.put("作品著作列表", bigDataWorksCopyrightDto == null ? null : bigDataWorksCopyrightDto.getList());
            //查询主要人员关联的企业信息
            String lagalPerson = bigDataCompanyDetailDto == null ? null : bigDataCompanyDetailDto.getLegal_person();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(lagalPerson)) {
                BigDataKeyPersonnelAffiliatedEnterpriseDtp bigDataKeyPersonnelAffiliatedEnterpriseDtp = assmbleKeyPersonnelAffiliatedEnterprise(pid, lagalPerson);
                resultJson.put("主要人员关联的企业信息列表", bigDataKeyPersonnelAffiliatedEnterpriseDtp == null ? null : bigDataKeyPersonnelAffiliatedEnterpriseDtp.getItem());
            }
        }
        checkData(resultJson);
        String replace = resultJson.toJSONString().replace("\\\"", "").replace("\\\"", "").replace("[]", "null").replace("\"\"", "null");
        replace = JSONObject.parseObject(replace, Feature.OrderedField).toJSONString();
//        log.info("checkData 出参json={}", replace);
        return JSONObject.parseObject(replace, Feature.OrderedField);
    }

    private JSONObject buildBusiOppoData(String busiOppoCode) {
        com.ce.scrm.center.service.business.entity.dto.ai.AiBusinessDto aiBusinessDto = new com.ce.scrm.center.service.business.entity.dto.ai.AiBusinessDto();
        aiBusinessDto.setBusiOppoCode(busiOppoCode);
        return aiCrmBusiness.getBusinessOpportunityNew(aiBusinessDto);
    }

    public JSONObject buildJsonDataAndCdp(AiBusinessDto businessDto) {
        log.info("buildJsonDataAndCdp businessDto={}", businessDto);
        long l1 = System.currentTimeMillis();

        JSONObject result = new JSONObject(new LinkedHashMap<>());

        if (StringUtils.isBlank(businessDto.getCustId()) || !Objects.equals(businessDto.getGg(), "db8de903dde942539a2206214eae961c")) {
            throw new ApiException(CodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }

        String customerName = "";
        if (StringUtils.isNotBlank(businessDto.getCustId()) && StringUtils.isBlank(businessDto.getPid())) {
            Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(businessDto.getCustId());
            if (customerData.isPresent()) {
                CustomerDataThirdView customerDataThirdView = customerData.get();
                businessDto.setPid(customerDataThirdView.getSourceDataId());
                customerName = customerDataThirdView.getCustomerName();
            }
        }

        if (StringUtils.isNotBlank(customerName)) {
            result.put("企业名称", customerName);
        } else {
            throw new ApiException(CodeMessageEnum.DATA_NOT_EXIST);
        }

        JSONObject jsonObject = buildJsonDataTemp(businessDto);
        if (jsonObject != null && !jsonObject.keySet().isEmpty()) {
            result.put("基础数据", jsonObject);
        }
        long l2 = System.currentTimeMillis();
        log.info("buildJsonDataAndCdp cost={}", (l2 - l1));
        return result;
    }

    public ExhibitionCompanyKjDubboView getExhibitionCompany(String pid, String customerName) {
        ExhibitionCompanyDto exhibitionCompanyDto = new ExhibitionCompanyDto();
        exhibitionCompanyDto.setPid(pid);
        exhibitionCompanyDto.setCustomerName(customerName);
        DubboResult<ExhibitionCompanyKjDubboView> dubboResult = exhibitionCompanyKjDubboService.getExhibitionCompanyByCondation(exhibitionCompanyDto);
        if (dubboResult != null && dubboResult.checkSuccess()) {
            return dubboResult.getData();
        }
        return null;
    }



    /***
     * 获取网站报告
     * 生成过，直接方位
     * 未生成过，调用接口去生成报告
     * @param customerId
     * @param domain
     * @param employeeId
     * <AUTHOR>
     * @date 2025/6/24 10:17
     * @version 1.0.0
     * @return com.alibaba.fastjson.JSONObject
    **/
    public JSONObject getWebSiteReport(String customerId,String domain,String employeeId) {
        CustomerWebsiteReportVo websiteReportVo = customerWebsiteReportAppService.selectByDomainName(domain);
        log.info("查询网站报告 域名={},结果={}",domain,JSONObject.toJSONString(websiteReportVo));
        if (websiteReportVo==null){
            Optional<EmployeeInfoThirdDto> employee = employeeThirdService.getEmployeeByEmpId(employeeId);
            if (employee.isPresent()){
                EmployeeInfoThirdDto employeeInfoThirdDto = employee.get();
                String message = employeeInfoThirdDto.getName() +"  正在使用"+domain+"生成报告";
                sendWechatMessage(message);
            }
            log.info("该域名未生成过报告，调用接口去生成报告,域名={}",domain);
            //没有生成过报告，需要调用接口去生成报告
            createWebSiteReport(customerId,domain,employeeId);
            log.info("正在生成报告，请稍后,域名={}",domain);
            return null;
        }
        if (Objects.isNull(websiteReportVo.getMessageRecTime())){
            log.info("正在生成报告，请稍后,域名={}",domain);
            return null;
        }else {
            if (Objects.equals(1, websiteReportVo.getStatus())) {
                // 最终呈现的结果
                JSONObject jsonObject = new JSONObject();
                //检测成功的结果
                String data1 = websiteReportVo.getData1();
                if (StringUtils.isNotBlank(data1)) {
                    JSONObject data1Object = JSONObject.parseObject(data1);
//                    if (data1Object.containsKey("seo_report")) {
//                        JSONObject result = data1Object.getJSONObject("seo_report");
//                    }
                    if (data1Object.containsKey("whois_report")) {
                        JSONObject result = data1Object.getJSONObject("whois_report");
                        if (Objects.nonNull(result)) {
                            String registrar = result.getString("Registrar");
                            if (StringUtils.isNotBlank(registrar)) {
                                jsonObject.put("域名注册人", registrar);
                            }
                            String registrationTime = result.getString("RegistrationTime");
                            if (StringUtils.isNotBlank(registrationTime)) {
                                jsonObject.put("域名注册时间", registrationTime);
                            }
                            String expirationTime = result.getString("ExpirationTime");
                            if (StringUtils.isNotBlank(expirationTime)) {
                                jsonObject.put("域名到期时间", expirationTime);
                            }
                        }
                    }
                    if (data1Object.containsKey("include_report")) {
                        JSONObject result = data1Object.getJSONObject("include_report");
                        if (Objects.nonNull(result)) {
                            String baiduPcInclude = result.getString("baidu_pc_include");
                            if (StringUtils.isNotBlank(baiduPcInclude)) {
                                jsonObject.put("百度PC收录数", baiduPcInclude);
                            }
                            String googleInclude = result.getString("google_include");
                            if (StringUtils.isNotBlank(googleInclude)) {
                                jsonObject.put("谷歌收录数", googleInclude);
                            }
                            JSONObject baiduPcWeight = result.getJSONObject("baidu_pc_weight");
                            if (Objects.nonNull(baiduPcWeight) && baiduPcWeight.containsKey("Br")) {
                                jsonObject.put("百度PC权重", baiduPcWeight.getString("Br"));
                            }
                            JSONObject baiduMobileWeight = result.getJSONObject("baidu_mobile_weight");
                            if (Objects.nonNull(baiduMobileWeight) && baiduMobileWeight.containsKey("Br")) {
                                jsonObject.put("百度移动权重", baiduMobileWeight.getString("Br"));
                            }
                        }
                    }
                    if (data1Object.containsKey("check_report")) {
                        JSONObject result = data1Object.getJSONObject("check_report");
                        JSONObject icp_result = result.getJSONObject("icp");
                        if (Objects.nonNull(icp_result)) {
                            if (icp_result.containsKey("ServiceLicence")) {
                                jsonObject.put("ICP许可证", icp_result.getString("ServiceLicence"));
                            }
                        }
                        String ipv6 = result.getString("ipv6");
                        if (StringUtils.isNotBlank(ipv6)) {
                            jsonObject.put("是否支持IPv6", "是");
                        } else {
                            jsonObject.put("是否支持IPv6", "否");
                        }
                        String ssl = result.getString("ssl");
                        if (StringUtils.isNotBlank(ssl)) {
                            jsonObject.put("是否安装SSL证书", "是");
                        } else {
                            jsonObject.put("是否安装SSL证书", "否");
                        }
                        String speed = result.getString("speed");
                        if (StringUtils.isNotBlank(speed)) {
                            jsonObject.put("网页打开速度", speed);
                        }
                        String ping = result.getString("ping");
                        if (StringUtils.isNotBlank(ping)) {
                            jsonObject.put("PING速度", ping);
                        }
                        String cache = result.getString("cache");
                        if (StringUtils.isNotBlank(cache) && Objects.equals("1", cache)) {
                            jsonObject.put("是否开启浏览器缓存", "是");
                        } else {
                            jsonObject.put("是否开启浏览器缓存", "否");
                        }
                        String sitemap = result.getString("sitemap");
                        if (StringUtils.isNotBlank(sitemap)) {
                            jsonObject.put("是否已正确设置网站地图", "是");
                        } else {
                            jsonObject.put("是否已正确设置网站地图", "否");
                        }
                        JSONArray emptyLink = result.getJSONArray("empty_link");
                        if (CollectionUtils.isNotEmpty(emptyLink)) {
                            jsonObject.put("首页空链接数量", emptyLink.size());
                        }
                        JSONArray invalidLink = result.getJSONArray("invalid_links");
                        if (CollectionUtils.isNotEmpty(invalidLink)) {
                            jsonObject.put("网站死链接数量", invalidLink.size());
                        }
                        JSONObject site_hierarchy_result = result.getJSONObject("site_hierarchy");
                        if (Objects.nonNull(site_hierarchy_result)) {
                            jsonObject.put("首页html嵌套层数", site_hierarchy_result.getString("site_max_depth"));
                        }
                        String cdn = result.getString("cdn");
                        if (StringUtils.isNotBlank(cdn)) {
                            jsonObject.put("是否开启CDN加速", "是");
                        } else {
                            jsonObject.put("是否开启CDN加速", "否");
                        }
                    }
                }
                // google 检测结果
                String data4 = websiteReportVo.getData4();
                if (StringUtils.isNotBlank(data4)) {
                    JSONObject data4Object = JSONObject.parseObject(data4);
                    if (Objects.nonNull(data4Object)) {
                        JSONObject lighthouse_scores = data4Object.getJSONObject("lighthouse_scores");
                        if (Objects.nonNull(lighthouse_scores)) {
                            JSONObject performance = lighthouse_scores.getJSONObject("performance");
                            if (Objects.nonNull(performance)) {
                                jsonObject.put("性能得分", performance.getString("score"));
                            }
                            JSONObject seo = lighthouse_scores.getJSONObject("seo");
                            if (Objects.nonNull(seo)) {
                                jsonObject.put("搜索引擎优化得分", seo.getString("score"));
                            }
                        }
                        JSONObject performance_metrics = data4Object.getJSONObject("performance_metrics");
                        if (Objects.nonNull(performance_metrics)) {
                            JSONObject first_contentful_paint = performance_metrics.getJSONObject("first_contentful_paint");
                            if (Objects.nonNull(first_contentful_paint) && first_contentful_paint.containsKey("display_value")) {
                                jsonObject.put("首次内容绘制时间", first_contentful_paint.containsKey("display_value"));
                            }
                        }
                    }
                }
                return jsonObject;
            }else{
                //失败的话，重新生成报告
                Optional<EmployeeInfoThirdDto> employee = employeeThirdService.getEmployeeByEmpId(employeeId);
                if (employee.isPresent()){
                    EmployeeInfoThirdDto employeeInfoThirdDto = employee.get();
                    String message = employeeInfoThirdDto.getName() +"  正在使用"+domain+"生成报告";
                    sendWechatMessage(message);
                }
                log.info("该域名未生成过报告，调用接口去生成报告,域名={}",domain);
                //没有生成过报告，需要调用接口去生成报告
                createWebSiteReport(customerId,domain,employeeId);
                log.info("正在生成报告，请稍后,域名={}",domain);
                return null;
            }
        }
    }


    private Boolean createWebSiteReport(String customerId,String domain,String employeeId) {
        if (StringUtils.isNotBlank(domain)){
            CustomerWebsiteReportVo customerWebsiteReportVo = new CustomerWebsiteReportVo();
            customerWebsiteReportVo.setDomainName(domain);
            customerWebsiteReportVo.setCustId(customerId);
            customerWebsiteReportVo.setDataSource(5);
            customerWebsiteReportVo.setCreateBy(employeeId);
            log.info("生成报告参数{}",JSONObject.toJSONString(customerWebsiteReportVo));
            MapResultBean mapResultBean = customerWebsiteReportAppService.makeWebsiteReport(customerWebsiteReportVo);
            log.info("生成报告结果{}",JSONObject.toJSONString(mapResultBean));
        }
        return true;
    }

    /**
     * 消息最大长度
     */
    private final static int MSG_MAX_LENGTH = 4096;

    public Boolean sendWechatMessage(String message) {
        try {
            if (message.length() > MSG_MAX_LENGTH) {
                message = message.substring(0, MSG_MAX_LENGTH);
            }
            Map<String, String> contentMap = new HashMap<>();
            contentMap.put("content", message);
            Map<String, Object> param = new HashMap<>();
            param.put("msgtype", "markdown");
            param.put("markdown", contentMap);
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, JSONObject.toJSONString(param));
            Request request = new Request.Builder()
                    .url("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b6ffba6b-457b-43a3-b534-f245b3951744")
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            response.close();
        } catch (Exception e) {
            log.error("生成网站分析报告发送消息失败");
        }
        return true;
    }
}
