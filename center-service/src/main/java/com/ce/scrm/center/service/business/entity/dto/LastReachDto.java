package com.ce.scrm.center.service.business.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LastReachDto implements Serializable {

    private String lastEmpId;

    private String lastEmpName;

    private Integer type;

    private String typeName;

    private Date time;

}
