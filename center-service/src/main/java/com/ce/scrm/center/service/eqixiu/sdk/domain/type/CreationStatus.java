package com.ce.scrm.center.service.eqixiu.sdk.domain.type;

/**
 * 作品创建状态
 * -1	已删除
 * 1	未发布
 * 2	已发布
 * 3	已暂停、临时关闭
 * 4	已停用
 * 5	已结束
 * <AUTHOR>
 */

public enum CreationStatus {

    DELETED(-1, "已删除"),
    UNPUBLISHED(1, "未发布"),
    PUBLISHED(2, "已发布"),
    PAUSED(3, "已暂停、临时关闭"),
    STOPPED(4, "已停用"),
    ENDED(5, "已结束")

    ;
    private int value;
    private String title;

    CreationStatus(int value, String title) {
        this.value = value;
        this.title = title;
    }


    public int getValue() {
        return value;
    }

    public String getTitle() {
        return title;
    }
}
