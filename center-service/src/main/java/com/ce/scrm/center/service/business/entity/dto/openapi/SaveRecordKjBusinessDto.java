package com.ce.scrm.center.service.business.entity.dto.openapi;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/3/19 09:49
 */
@Data
public class SaveRecordKjBusinessDto implements Serializable {

    /**
     * 企业ID
     */
    private String enterpriseId;

    /**
     * 通话ID
     */
    @NotBlank
    private String mainUniqueId;

    /**
     * 录音时长
     */
    private String recordDuration;

    /**
     * 录音文件名
     */
    @NotBlank
    private String recordFileIndex;

    /**
     * mp3录音地址
     */
    @NotBlank
    private String preSignUrl;

    /**
     * wav客户侧录音地址
     */
    private String preSignClientUrl;

    /**
     * wav座席侧录音地址
     */
    private String preSignAgentUrl;

    /**
     * wav录音地址
     */
    private String preSignWavUrl;

}
