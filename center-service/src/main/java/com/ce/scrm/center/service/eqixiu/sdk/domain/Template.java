package com.ce.scrm.center.service.eqixiu.sdk.domain;

/**
 * 作品模板
 * //TODO 属性待完善
 */
public class Template {

    /**
     * 模板ID
     */
    private long id;

    /**
     * 模板标题
     */
    private String title;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 封面图完整路径
     */
    private String coverUrl;

    /**
     * pc端预览链接
     */
    private String shareUrl;

    /**
     * 手机端预览链接 （需拼接授权码）
     */
    private String mobileShareUrl;

    /**
     * 显示排序
     */
    private int sort;

    /**
     * 浏览量
     */
    private int views;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    public String getMobileShareUrl() {
        return mobileShareUrl;
    }

    public void setMobileShareUrl(String mobileShareUrl) {
        this.mobileShareUrl = mobileShareUrl;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getViews() {
        return views;
    }

    public void setViews(int views) {
        this.views = views;
    }

    @Override
    public String toString() {
        return "Template{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", coverUrl='" + coverUrl + '\'' +
                ", shareUrl='" + shareUrl + '\'' +
                ", mobileShareUrl='" + mobileShareUrl + '\'' +
                ", sort=" + sort +
                ", views=" + views +
                '}';
    }
}

