package com.ce.scrm.center.service.eqixiu.sdk.common;


import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;
import com.ce.scrm.center.service.eqixiu.sdk.util.JsonMapper;
import com.ce.scrm.center.service.eqixiu.sdk.util.SecretUtil;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

/**
 * 易企秀回调请求参数封装
 * 该类仅限于Controller层使用
 *
 * <AUTHOR>
 */
public class EqxiuCallCmd {

    /**
     * 请求时的时间戳
     */
    private String timestamp;

    /**
     * 随机字符串
     */
    private String nonce;

    /**
     * 请求签名
     */
    private String signature;

    /**
     * 加密后的消息
     */
    private String msgEncrypt;

    /**
     * 参数校验
     * 1. 不可为空
     * 2. 签名是否正确
     *
     * @return
     */
    public String validate(String signatureKey) {
        if (timestamp == null || nonce == null || signature == null || msgEncrypt == null) {
            return "参数不可为空";
        }
        if (StrUtil.isEmpty(signatureKey)) {
            return "未配置密钥信息";
        }
        String genSign = SecretUtil.genSign(this.nonce, this.timestamp, signatureKey);
        if (!genSign.equals(this.signature)) {
            return "签名错误";
        }
        return null;
    }

    public JSONObject getParamObj(String encodingKey) {
        String param = getParamStr(encodingKey);
        return JsonMapper.getInstance().fromJson(param, JSONObject.class);
    }

    public String getParamStr(String encodingKey) {
        return SecretUtil.decrypt(this.msgEncrypt, encodingKey);
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getMsgEncrypt() {
        return msgEncrypt;
    }

    public void setMsgEncrypt(String msgEncrypt) {
        this.msgEncrypt = msgEncrypt;
    }

    @Override
    public String toString() {
        return "EqxiuCallCmd{" +
                "timestamp='" + timestamp + '\'' +
                ", nonce='" + nonce + '\'' +
                ", signature='" + signature + '\'' +
                ", msgEncrypt='" + msgEncrypt + '\'' +
                '}';
    }
}
