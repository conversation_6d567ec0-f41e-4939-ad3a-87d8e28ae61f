package com.ce.scrm.center.service.eqixiu.config;

import com.ce.scrm.center.service.eqixiu.sdk.service.CreationService;
import com.ce.scrm.center.support.redis.RedisOperator;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.support.RedisTokenCache;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EqxiuConfig {


    @Bean
    public Secret secret(@Value("${ecmp.secretId:}") String secretId,
                         @Value("${ecmp.secretKey:}") String secretKey) {
        try {
            return new Secret(secretId, secretKey);
        } catch (Exception e) {
            return null;
        }
    }

    @Bean
    public RedisTokenCache redisTokenCache(RedisOperator redisOperator) {
        return new RedisTokenCache(redisOperator);
    }

    @Bean
    public CreationService creationService(RedisTokenCache redisTokenCache,Secret secret) {
        try {
            return new CreationService(secret, redisTokenCache);
        } catch (Exception e) {
            return null;
        }

    }

}
