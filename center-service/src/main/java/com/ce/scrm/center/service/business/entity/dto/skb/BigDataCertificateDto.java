package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 资质证书
 * @author: JiuDD
 * date: 2025/2/13
 */
@Data
public class BigDataCertificateDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**统一信用代码*/
    @JSONField(serialize = false)
	private String uncid;
    /**企业名称*/
    @JSONField(serialize = false)
	private String entname;
    /**总数量*/
    @JSONField(serialize = false)
	private int total_count;
    /**总页数*/
    @JSONField(serialize = false)
	private int total_page;
    /**当前页*/
    @JSONField(serialize = false)
	private int page;
    /**资质证书列表*/
    @JSONField(name = "资质证书列表",serialize = false)
    private List<ListCertificate> list;

    @JSONField(name = "资质证书列表")
    private String listString;

    @NoArgsConstructor
    @Data
    public static class ListCertificate implements Serializable {
        private static final long serialVersionUID = 1L;
        /**编号*/
        @JSONField(serialize = false)
		private String cert_id;
        /**证书名称*/
        @JSONField(name = "资质证书名称")
		private String cert_name;
        /**证书类型1级*/
        @JSONField(name = "证书类型1级",serialize = false)
		private String categoryL1;
        /**证书类型2级*/
        @JSONField(name = "证书类型2级",serialize = false)
		private String categoryL2;
        /**发证日期*/
        @JSONField(serialize = false)
		private String start_date;
        /**截至日期*/
        @JSONField(name = "截止日期",serialize = false)
		private String end_date;
        /**发证机关*/
        @JSONField(serialize = false)
		private String org_name;
    }
}
