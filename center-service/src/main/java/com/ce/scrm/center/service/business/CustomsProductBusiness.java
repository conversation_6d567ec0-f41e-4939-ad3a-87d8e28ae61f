package com.ce.scrm.center.service.business;

import com.ce.scrm.center.dao.entity.CustomsProduct;
import com.ce.scrm.center.dao.service.CustomsProductService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/20
 */
@Slf4j
@Service
public class CustomsProductBusiness {
    @Autowired
    CustomsProductService customsProductService;

    public List<CustomsProduct> getCustomsProductLikeName(String productName) {
        if (StringUtils.isBlank(productName)){
            return Lists.newArrayList();
        }
        return customsProductService.lambdaQuery().like(CustomsProduct::getProductName, productName).list();
    }
}
