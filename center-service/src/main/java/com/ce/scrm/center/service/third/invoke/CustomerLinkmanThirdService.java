package com.ce.scrm.center.service.third.invoke;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerContactPersonThirdDto;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.service.yml.ThirdCustomerConfig;
import com.ce.scrm.customer.dubbo.api.IContactPersonDubbo;
import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.dubbo.entity.dto.ContactPersonAddDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.ContactPersonDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerDetailDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @classname CustomerLinkmanThirdService
 * @description TODO
 * @date 2025/1/24 14:55
 * @create by gaomeijing
 */
@Slf4j
@Service
public class CustomerLinkmanThirdService {
    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private IContactPersonDubbo iContactPersonDubbo;

    @Resource
    private ThirdCustomerConfig thirdCustomerConfig;

    public List<ContactPersonDubboView> getCustomerContactListByCustomerId(String customerId) {
        if (StringUtils.isEmpty(customerId)) {
            return new ArrayList<>();
        }
        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setCustomerId(customerId);
        setSignData(customerDetailDubboDto);
        // 从缓存中获取客户联系人信息
        DubboResult<CustomerDubboView> customerContactDetail = iContactPersonDubbo.customerContactDetail(customerDetailDubboDto);
        if (!customerContactDetail.checkSuccess() || null == customerContactDetail.getData()) {
//            log.error("iContactPersonDubbo.customerContactDetail 获取客户联系人信息失败，customerId={}, errorMsg={}", customerId, customerContactDetail.getMsg());
            return new ArrayList<>();
        }
        // 客户联系人列表
        List<ContactPersonDubboView> contactPersonDubboViewList = customerContactDetail.getData().getContactPersonDubboViewList();
        if (CollectionUtils.isEmpty(contactPersonDubboViewList)) {
            return new ArrayList<>();
        }
        return contactPersonDubboViewList;
    }

    public ContactPersonDubboView getContactListByLinkmanId(String linkmanId) {
        if (StringUtils.isEmpty(linkmanId)) {
            return null;
        }
        // 根据联系人ID从缓存中获取联系方式信息
        ContactPersonDubboDto contactPersonDubboDto = new ContactPersonDubboDto();
        contactPersonDubboDto.setContactPersonId(linkmanId);
        setSignData(contactPersonDubboDto);
        DubboResult<ContactPersonDubboView> detail = iContactPersonDubbo.detail(contactPersonDubboDto);
        if(null == detail) {
            return null;
        }
        ContactPersonDubboView personFromDetail = detail.getData();
        if (!detail.checkSuccess() || null == personFromDetail) {
            return null;
        }
        // 联系方式列表
        List<ContactInfoDubboView> contactInfoDubboViews = personFromDetail.getContactInfoList();
        if (CollectionUtils.isEmpty(contactInfoDubboViews)) {
            return null;
        }

        return personFromDetail;
    }


    /**
     * 设置签名数据
     * @param signData
     */
    private void setSignData(SignData signData) {
        signData.setSourceKey(thirdCustomerConfig.getKey());
        signData.setSourceSecret(thirdCustomerConfig.getSecret());
    }

	/**
	 * 添加联系人
	 */
	public String  addContractPerson(CustomerContactPersonThirdDto customerContactPersonThirdDto) {
		if (Objects.isNull(customerContactPersonThirdDto)) {
			log.error("添加联系人失败，参数为空");
			return null;
		}
		ContactPersonAddDubboDto contactPersonAddDubboDto = BeanCopyUtils.convertToVo(customerContactPersonThirdDto, ContactPersonAddDubboDto.class);
		setSignData(contactPersonAddDubboDto);
		DubboResult<ContactPersonAddDubboView> dubboResult = iContactPersonDubbo.add(contactPersonAddDubboDto);
		if (!dubboResult.checkSuccess()) {
			log.error("添加联系人失败，原因={}", dubboResult.getMsg());
            return null;
		}
		ContactPersonAddDubboView dubboView = dubboResult.getData();
		log.info("添加联系人,入参={} , 结果={}", JSON.toJSONString(customerContactPersonThirdDto), JSON.toJSONString(dubboView));
		if (Objects.isNull(dubboView)) {
			return null;
		}
		return dubboView.getContactPersonId();
	}

    /**
     * <AUTHOR>
     * @date 2025/8/5 21:11:09
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>>
     * @desc 获取客户联系人数据
     */
    public List<ContactPersonDataDubboView> getCustomerData(CustomerDetailDubboDto customerDetailDubboDto) {
        setSignData(customerDetailDubboDto);
        DubboResult<List<ContactPersonDataDubboView>>  contactList = iContactPersonDubbo.getCustomerData(customerDetailDubboDto);
        if (!contactList.checkSuccess() || null == contactList.getData()) {
            return new ArrayList<>();
        }
        // 客户联系人列表
        List<ContactPersonDataDubboView> contactPersonDubboViewList = contactList.getData();
        if (CollectionUtils.isEmpty(contactPersonDubboViewList)) {
            return new ArrayList<>();
        }
        return contactPersonDubboViewList;
    }
}
