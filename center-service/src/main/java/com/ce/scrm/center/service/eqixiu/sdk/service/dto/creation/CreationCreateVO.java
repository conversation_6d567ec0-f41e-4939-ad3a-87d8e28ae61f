/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.creation;

public class CreationCreateVO {

    private final Long id;

    private final String editUrl;

    public CreationCreateVO(Long id, String editUrl) {
        this.id = id;
        this.editUrl = editUrl;
    }

    public Long getId() {
        return id;
    }

    public String getEditUrl() {
        return editUrl;
    }

    @Override
    public String toString() {
        return "CreationCreateVO{" +
                "id=" + id +
                ", editUrl='" + editUrl + '\'' +
                '}';
    }
}
