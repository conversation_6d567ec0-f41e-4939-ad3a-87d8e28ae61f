/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.template;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;

import java.util.Map;

public class CorpTemplateTagQuery extends BaseParam {

    private final String bizCode = "template_corp";

    /**
     * 父标签id（pid=0时一级标签）
     */
    private Long pId = 0L;

    /**
     * 是否查询下级标签
     */
    private boolean querySubTag = false;
    /**
     * 标签类型：2-企业自定义标签、3-员工自定义标签
     */
    private int bizType = 2;

    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> map = getBaseParamsMap();
        map.put("bizCode", bizCode);
        map.put("pId", pId.toString());
        map.put("querySubTag", String.valueOf(querySubTag));
        map.put("bizType", String.valueOf(bizType));
        return map;
    }

    @Override
    public void validate() {
        if (bizType != 2 && bizType != 3) {
            throw new KnownException("bizType只能为2或3");
        }
        if (pId == null) {
            throw new KnownException("pId不能为空");
        }
    }

    public String getBizCode() {
        return bizCode;
    }

    public Long getpId() {
        return pId;
    }

    public void setpId(Long pId) {
        this.pId = pId;
    }

    public boolean isQuerySubTag() {
        return querySubTag;
    }

    public void setQuerySubTag(boolean querySubTag) {
        this.querySubTag = querySubTag;
    }

    public int getBizType() {
        return bizType;
    }

    public void setBizType(int bizType) {
        this.bizType = bizType;
    }
}
