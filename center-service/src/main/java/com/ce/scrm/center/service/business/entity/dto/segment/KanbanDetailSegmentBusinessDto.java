package com.ce.scrm.center.service.business.entity.dto.segment;

import lombok.Data;

import java.io.Serializable;

/**
 * 追踪线索分页
 */
@Data
public class KanbanDetailSegmentBusinessDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * <p>看板详情类型</p>
	 * 昨日：已处理=1、保护=2、拜访客户=3、签单客户=4
	 * 累计：已处理=5、保护=6、拜访客户=7、签单客户=8
	 */
	private Integer type;

	/**
	 * 分群id
	 */
	private String segmentId;


	/**
	 * 筛选开始时间
	 */
	private String selectionStartDate;

	/**
	 * 筛选结束时间
	 */
	private String selectionEndDate;

	private String areaId;

	private String subId;

	private String buId;

	private String deptId;

	private String salerId;

	//--------以下登录人信息--------

	/**
	 * 员工ID
	 */
	private String loginEmployeeId;

	private String loginAreaId;

	private String loginSubId;

	private String loginBuId;

	/**
	 * 部门ID
	 */
	private String loginOrgId;

	/**
	 * 职位
	 */
	private String loginPosition;


	// 为loginEmployeeId、loginAreaId等登录人信息赋值

}
