package com.ce.scrm.center.service.business;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.business.entity.dto.TextMessageDto;
import com.ce.scrm.center.service.business.entity.dto.WechatMessageSendDto;
import com.ce.scrm.center.service.business.entity.dto.WxMessageDto;
import com.ce.scrm.center.service.business.entity.view.SmaDictionaryItemView;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.constant.SmsTemplateConstants;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.extend.dubbo.enums.ReleaseBussinessTagEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;

/**
 * 发送微信消息
 * <AUTHOR>
 */
@Slf4j
@Service
public class SendWxMessage {

    @Value("${wx.isopen}")
    Integer wxIsOpen;

    @Value("${mq.prefix}" + ServiceConstant.MqConstant.Topic.CESUPPORT_SCRM_WECHAT_MESSAGE_SEND_TOPIC)
    String CESUPPORT_SCRM_WECHAT_MESSAGE_SEND_TOPIC;

    @Autowired
    RocketMqOperate rocketMqOperate;


    @Resource
    SmaDictionaryItemBusiness smaDictionaryItemBusiness;
    /**
     * 经理补充释放理由并给总监发送微信消息
     */
    public void sendWxMessageLeader(WxMessageDto wxMessageDto){
        //给总监发送企业微信通知
        try{
            if (Objects.isNull(wxMessageDto.getLeaderName()) || wxMessageDto.getLeaderName().isEmpty()){
                log.info("[sendWxMessageLeader]leaderName为空！！！custName:{}",wxMessageDto.getCustName());
                return;
            }
            if (Objects.isNull(wxMessageDto.getMajorId()) || wxMessageDto.getMajorId().isEmpty()){
                log.info("[sendWxMessageLeader]majorId为空！！！custName:{}",wxMessageDto.getCustName());
                return;
            }
            if (Objects.isNull(wxMessageDto.getTagOpportunityOrigin())){
                log.info("[sendWxMessageLeader]tagOpportunityOrigin为空！！！custName:{}",wxMessageDto.getCustName());
                return;
            }
            Optional<SmaDictionaryItemView> byCode = smaDictionaryItemBusiness.findByCode(SmsTemplateConstants.SCRM_RELEASE_BUSSINESS_MANAGER);
            if (byCode.isPresent()){
                SmaDictionaryItemView smaDictionaryItemView = byCode.get();
                String format = MessageFormat.format(smaDictionaryItemView.getName(), wxMessageDto.getLeaderName(),
                        ReleaseBussinessTagEnum.getLable(wxMessageDto.getTagOpportunityOrigin()),wxMessageDto.getCustName() );
                log.info("发送的微信消息提醒为：{}", format);
                //总监发送
                sendMessage(wxMessageDto.getMajorId(), format);
            }
        }catch (Exception e){
            log.error("给总监发送企业微信通知失败，参数为：{}", wxMessageDto.toString(), e);
        }
    }


    public boolean sendMessage(String userId, String message) {
        if (wxIsOpen == null || !wxIsOpen.equals(1)) {
            log.info("【sendMessage】开关配置为空或者关闭状态！");
            return false;
        }
        if (userId == null || userId.isEmpty()) {
            log.info("【sendMessage】userId为空！");
            return false;
        }
        WechatMessageSendDto wechatMessageSend = new WechatMessageSendDto();
        wechatMessageSend.setMsgType("text");
        wechatMessageSend.setText(new TextMessageDto(message));
        List<String> toUserList = Lists.newArrayList(userId);
        wechatMessageSend.setToUserList(toUserList);
        rocketMqOperate.syncSend(CESUPPORT_SCRM_WECHAT_MESSAGE_SEND_TOPIC, JSONObject.toJSONString(wechatMessageSend));
        return true;
    }
}