package com.ce.scrm.center.service.eqixiu.sdk.domain.event;

import com.ce.scrm.center.service.eqixiu.sdk.domain.Customer;
import com.ce.scrm.center.service.eqixiu.sdk.domain.UserAgent;

/**
 * 参与抽奖事件
 *
 * <AUTHOR>
 */
public class JoinLotteryEventData extends CreationEventData {

    /**
     * 是否中奖。1-已中奖，0-未中奖
     */
    private int isWin;

    /**
     * 抽奖用户信息
     */
    private Customer customerInfo;

    private UserAgent ua;

    /**
     * 连抽次数。仅限连抽模式才有
     */
    private Integer drawNum;

}
