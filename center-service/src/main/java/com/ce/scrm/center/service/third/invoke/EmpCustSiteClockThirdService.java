package com.ce.scrm.center.service.third.invoke;

import cn.ce.cesupport.sma.vo.EmpCustSiteClockVo;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.service.business.ProtectBusiness;
import com.ce.scrm.center.service.business.entity.dto.AddProtectTimeEventBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.ClockMarkBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.EmpCustSiteClockBusinessDto;
import com.ce.scrm.center.service.third.entity.dto.ClockConditionQueryThirdDto;
import com.ce.scrm.center.service.third.entity.view.EmpCustSiteClockThirdView;
import com.ce.scrm.extend.dubbo.api.EmpCustSiteClockDubboService;
import com.ce.scrm.extend.dubbo.entity.request.ClockMarkReq;
import com.ce.scrm.extend.dubbo.entity.request.EmpCustSiteClockReq;
import com.ce.scrm.extend.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.response.EmpCustSiteClockRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * description: 地理打卡
 * @author: DD.Jiu
 * date: 2024/7/24.
 */
@Slf4j
@Service
public class EmpCustSiteClockThirdService {
    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private EmpCustSiteClockDubboService empCustSiteClockDubboService;

	@Resource
	private ProtectBusiness protectBusiness;

    /**
     * Description: 统计打卡次数
     * @author: JiuDD
     * @param businessDto
     * @return java.lang.Long 打卡次数
     * date: 2024/7/24 14:58
     */
    public Long getCountByParam(EmpCustSiteClockBusinessDto businessDto) {
        EmpCustSiteClockReq req = new EmpCustSiteClockReq();
        BeanUtils.copyProperties(businessDto, req);
        return empCustSiteClockDubboService.getCountByParam(req);
    }

    /**
     * 标记打卡是否有效
     *
     * @param clockMarkBusinessDto
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2024/10/11 11:46
     */
    public Boolean updateValidFlagById(ClockMarkBusinessDto clockMarkBusinessDto) {

		if (Objects.equals(clockMarkBusinessDto.getMarkClockValidFlag(),2)) {
			protectBusiness.addProtectTimeByEvent(AddProtectTimeEventBusinessDto.builder().customerId(clockMarkBusinessDto.getCustomerId()).eventType(2).build());
		}

        ClockMarkReq req = new ClockMarkReq();
        BeanUtils.copyProperties(clockMarkBusinessDto, req);
        DubboResult<Boolean> booleanDubboResult = empCustSiteClockDubboService.updateValidFlagById(req);
        if (!booleanDubboResult.checkSuccess()) {
            log.error("更新打卡有效状态失败！返回数据：{}", JSON.toJSONString(booleanDubboResult));
            return false;
        }
        return true;
    }

	/**
	 * 获取当前员工对应某个客户的最新打卡记录
	 * @param employeeId 员工id
	 * @param customerId 客户id
	 * @return scrm_extend.emp_cust_site_clock表最新一条记录
	 */
	public EmpCustSiteClockVo getCustSiteClockLastTime(final String employeeId, final String customerId) {
		try {
			if (StringUtils.isAnyBlank(employeeId, customerId)){
				return null;
			}
			EmpCustSiteClockReq empCustSiteClockReq = new EmpCustSiteClockReq();
			empCustSiteClockReq.setEmpId(employeeId);
			empCustSiteClockReq.setCustId(customerId);
			// 相当于limit 1
			DubboPageInfo<EmpCustSiteClockRes> dubboPageInfo = empCustSiteClockDubboService.queryPageList(1, 1, empCustSiteClockReq);
			if (dubboPageInfo != null && CollectionUtils.isNotEmpty(dubboPageInfo.getList())) {
				EmpCustSiteClockRes empCustSiteClockRes = dubboPageInfo.getList().get(0);
				EmpCustSiteClockVo empCustSiteClockVo = new EmpCustSiteClockVo();
				BeanUtils.copyProperties(empCustSiteClockRes, empCustSiteClockVo);
				return empCustSiteClockVo;
			}
		} catch (Exception ex) {
			log.error("获取最近一次地理打卡记录失败,customerId={}, employeeId:{}, {}", customerId, employeeId, ex.getMessage());
		}
		return null;
	}

	public List<EmpCustSiteClockThirdView> selectByCondition(@Valid ClockConditionQueryThirdDto conditionQueryThirdDto) {

		EmpCustSiteClockReq empCustSiteClockReq = BeanUtil.copyProperties(conditionQueryThirdDto, EmpCustSiteClockReq.class);
		List<EmpCustSiteClockRes> dubboResult = empCustSiteClockDubboService.selectByCondition(empCustSiteClockReq);
		List<EmpCustSiteClockThirdView> empCustSiteClockThirdViews = BeanUtil.copyToList(dubboResult, EmpCustSiteClockThirdView.class);
		return empCustSiteClockThirdViews;
	}
}
