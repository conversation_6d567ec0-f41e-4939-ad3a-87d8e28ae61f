package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description: 招聘信息
 * @author: JiuDD
 * date: 2025/2/13
 */
@Data
public class BigDataRecruitmentDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**企业名称*/
    @JSONField(serialize = false)
    private String entname;
    /**统一信用代码*/
    @JSONField(serialize = false)
    private String uncid;
    /**总数量*/
    @JSONField(serialize = false)
    private int total_count;
    /**总页数*/
    @JSONField(serialize = false)
    private int total_page;
    /**当前页*/
    @JSONField(serialize = false)
    private int page;

    @JSONField(name = "招聘信息列表")
    private List<ListRecruitment> list;

    @NoArgsConstructor
    @Data
    public static class ListRecruitment implements Serializable {
        private static final long serialVersionUID = 1L;
        /**职位名称*/
        @JSONField(name = "招聘职位名称")
        private String job_name;
        /**地区*/
        @JSONField(name = "招聘地区")
        private String location;
        /**发布时间*/
        @JSONField(name = "招聘发布时间",format = "yyyy-MM-dd HH:mm:ss")
        private Date release_date;
        /**薪资*/
        @JSONField(name = "薪资",serialize = false)
        private String salary;
        /**招聘来源*/
        @JSONField(name = "招聘来源")
        private String source_name;
        /**描述*/
        @JSONField(name = "描述",serialize = false)
        private String job_desc;
    }
}
