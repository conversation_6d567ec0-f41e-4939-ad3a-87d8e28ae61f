package com.ce.scrm.center.service.eqixiu.sdk.service;


import com.ce.scrm.center.service.eqixiu.sdk.common.Result;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Tag;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Template;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.template.*;
import com.ce.scrm.center.service.eqixiu.sdk.support.HttpClient;
import com.ce.scrm.center.service.eqixiu.sdk.support.TokenCache;
import com.ce.scrm.center.service.eqixiu.sdk.util.HttpClientUtils;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 编辑器通用模版接口
 *
 * <AUTHOR>
 */
public class TemplateService extends ConnectService {

    private static final String API_TEMPLATE_TAG_LIST = "/api/v1/base/tag/list/display";
    private static final String API_TEMPLATE_LIST = "/api/v1/biz/template/list/display";

    private static final String API_TEMPLATE_PREVIEW_TOKEN = "/api/v1/editor/template/token";

    private static final String API_TEMPLATE_INFO = "/api/v1/biz/template";
    private static final String API_TEMPLATE_CORP_LIST = "/api/v1/biz/template/corp";
    private static final String API_TEMPLATE_CORP_SAVE = "/api/v1/editor/creation/corp/template/save";
    private static final String API_TEMPLATE_CORP_DEL = "/api/v1/biz/template/corp/delete/";
    private static final String API_TEMPLATE_CORP_TAG_LIST = "/api/v1/base/tag/list";
    private static final String API_TAG_ADD = "/api/v1/base/tag/add";
    private static final String API_TAG_DEL = "/api/v1/base/tag/delete";
    private static final String API_TEMPLATE_TAG_BIND = "/api/v1/biz/template/tag/corp/";
    private static final String API_TEMPLATE_TAG_UNBIND = "/api/v1/biz/template/tag/unbind";
    private static final String API_TEMPLATE_CORP_TAG_ALL = "/api/v1/biz/template/tag/all";
    private static final String API_TEMPLATE_USED_RECORD = "/api/v1/biz/template/record/";
    private static final String API_TEMPLATE_USED_STAT = "/api/v1/biz/template/corp/total/";
    private static final String API_TEMPLATE_USED_CREATION = "/api/v1/biz/template/corp/detail/list/";


    public TemplateService(Secret secret) {
        super(secret);
    }

    public TemplateService(Secret secret, TokenCache tokenCache) {
        super(secret, tokenCache);
    }

    public TemplateService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        super(secret, tokenCache, httpClient);
    }

    /**
     * 获取模板标签列表
     *
     * @param pId 标签父id，互动为5，H5为5000，海报为6000，长页为7000，表单为8000
     * @return
     */
    public List<Tag> listTemplateTag(Long pId) {
        if (pId == null) {
            logger.warn("pId 不能为空");
            return Collections.emptyList();
        }
        Map<String, String> paramsMap = createParamMapWithToken();
        paramsMap.put("pId", pId.toString());
        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_TEMPLATE_TAG_LIST), null, paramsMap);
        printLog(object, "获取模板标签列表失败 fail:{}");
        return object.getList(Tag.class);
    }

    /**
     * 获取当前账号权限的所有模板资源列表
     *
     * @param query
     * @return
     */
    public Result<Template> findTemplate(TemplateQuery query) {
        paramValidate(query);
        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_TEMPLATE_LIST), null, query.getParamsMap());
        printLog(object, "获取当前账号权限的所有模板资源列表失败 fail:{}");
        return getResult(object, Template.class);
    }

    /**
     * 获取模版站外预览授权token，有效期为30分钟
     *
     * @param id 模板 ID
     * @return
     */
    public String getPreviewToken(Long id) {
        if (id == null) {
            logger.warn("id 不能为空");
            return null;
        }
        HashMap<String, String> map = createParamMapWithToken();
        map.put("id", id.toString());
        JSONObject object = HttpClientUtils.httpPost(getApiURL(API_TEMPLATE_PREVIEW_TOKEN, map), null, map);
        printLog(object, "获取模版站外预览授权token失败 fail:{}");
        return object.getStr("obj");
    }


    /**
     * 根据模板ID获取模板信息。
     *
     * @param id 模板ID
     * @return
     */
    public Template getTemplateById(Long id) {
        if (id == null) {
            logger.warn("id 不能为空");
            return null;
        }
        HashMap<String, String> map = createParamMapWithToken();
        map.put("id", id.toString());

        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_TEMPLATE_INFO), null, map);
        printLog(object, "根据模板ID获取模板信息失败 fail:{}");
        return object.getObj(Template.class);
    }


    //---- 以下企业模板相关接口

    /**
     * 获取企业模板标签列表
     *
     * @param query
     * @return
     */
    public List<Tag> listCorpTemplateTag(CorpTemplateTagQuery query) {
        paramValidate(query);
        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_TEMPLATE_CORP_TAG_LIST), null, query.getParamsMap());
        printLog(object, "获取企业模板标签列表失败 fail:{}");
        return object.getList(Tag.class);
    }

    /**
     * 添加企业模板标签
     *
     * @param cmd
     * @return
     */
    public Tag addCorpTemplateTag(AddCorpTagCmd cmd) {
        paramValidate(cmd);
        JSONObject object = HttpClientUtils.httpPost(getApiURL(API_TAG_ADD, cmd.getParamsMap()), null, cmd.getParamsMap());
        printLog(object, "添加企业模板标签失败 fail:{}");
        return object.getObj(Tag.class);
    }

    /**
     * 删除企业模板标签
     *
     * @param id
     * @return
     */
    public boolean deleteCorpTemplateTag(Long id) {
        if (id == null) {
            logger.warn("id 不能为空");
            return false;
        }
        HashMap<String, String> map = createParamMapWithToken();
        map.put("id", id.toString());
        map.put("bizType", "2");

        JSONObject object = HttpClientUtils.httpPost(getApiURL(API_TAG_DEL, map), null, map);
        printLog(object, "删除企业模板失败 fail:{}");
        return object.getSuccess();
    }

    /**
     * 绑定企业模板标签
     *
     * @param id
     * @param tagIds
     * @return
     */
    public boolean bindCorpTemplateTag(Long id, Long... tagIds) {
        if (id == null) {
            logger.warn("id 不能为空");
            return false;
        }
        if (tagIds == null || tagIds.length == 0) {
            logger.warn("tagIds 不能为空");
            return false;
        }
        Map<String, String> paramMap = createParamMapWithToken();
        paramMap.put("tagIds", Arrays.stream(tagIds).map(Object::toString).collect(Collectors.joining(",")));
        JSONObject object = HttpClientUtils.httpPost(getApiURL(API_TEMPLATE_TAG_BIND + id, paramMap), null, paramMap);
        printLog(object, "设置企业模板标签失败 fail:{}");
        return object.getSuccess();
    }

    /**
     * 解除企业模板标签
     *
     * @param id
     * @param tagIds
     * @return
     */
    public boolean unbindCorpTemplateTag(Long id, Long... tagIds) {
        if (id == null) {
            logger.warn("id 不能为空");
            return false;
        }
        if (tagIds == null || tagIds.length == 0) {
            logger.warn("tagIds 不能为空");
            return false;
        }
        Map<String, String> paramMap = createParamMapWithToken();
        paramMap.put("templateId", id.toString());
        paramMap.put("tagIds", Arrays.stream(tagIds).map(Object::toString).collect(Collectors.joining(",")));
        JSONObject object = HttpClientUtils.httpPost(getApiURL(API_TEMPLATE_TAG_UNBIND, paramMap), null, paramMap);
        printLog(object, "解除企业模板标签失败 fail:{}");
        return object.getSuccess();
    }

    /**
     * 获取模板标签列表
     *
     * @param templateId
     * @return
     */
    public List<Tag> listTagByTemplate(Long templateId) {
        if (templateId == null) {
            logger.warn("templateId 不能为空");
            return Collections.emptyList();
        }
        Map<String, String> paramMap = createParamMapWithToken();
        paramMap.put("templateId", templateId.toString());
        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_TEMPLATE_CORP_TAG_ALL), null, paramMap);
        printLog(object, "获取模板标签列表失败 fail:{}");
        return object.getList(Tag.class);
    }

    /**
     * 查询企业模板资源列表
     * 企业专属模板
     *
     * @param query
     * @return
     */
    public Result<Template> findCorpTemplate(TemplateQuery query) {
        paramValidate(query);
        JSONObject object = HttpClientUtils.httpPost(getApiURL(API_TEMPLATE_CORP_LIST, query.getParamsMap()), null, query.getParamsMap());
        printLog(object, "获取当前账号企业模板资源列表失败 fail:{}");
        return getResult(object, Template.class);
    }

    /**
     * 把作品设置为企业模板
     *
     * @param cmd
     * @return
     */
    public List<Template> makeCreationAsCorpTemplate(MakeCorpTemplateCmd cmd) {
        paramValidate(cmd);
        JSONObject object = HttpClientUtils.httpPost(getApiURL(API_TEMPLATE_CORP_SAVE, cmd.getParamsMap()), null, cmd.getParamsMap());
        printLog(object, "把作品设置为企业模板失败 fail:{}");
        return object.getList(Template.class);
    }

    /**
     * 根据模板ID删除企业模板
     *
     * @param id
     * @return
     */
    public boolean deleteCorpTemplate(Long id) {
        if (id == null) {
            logger.warn("id 不能为空");
            return false;
        }
        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_TEMPLATE_CORP_DEL + id), null, createParamMapWithToken());
        printLog(object, "根据模板ID删除企业模板失败 fail:{}");
        return object.getSuccess();
    }

    /**
     * 查询企业模板使用记录
     *
     * @param query
     * @return
     */
    public Result<TemplateUsedRecord> findTemplateUsedRecord(TemplateUsedRecordQuery query) {
        paramValidate(query);
        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_TEMPLATE_USED_RECORD + query.getTemplateId()), null, query.getParamsMap());
        printLog(object, "查询企业模板使用记录失败 fail:{}");
        return getResult(object, TemplateUsedRecord.class);
    }

    /**
     * 查询企业模板使用统计
     *
     * @param templateId
     * @return
     */
    public TemplateUsedStat getTemplateUsedStat(Long templateId) {
        if (templateId == null) {
            logger.warn("templateId 不能为空");
            return null;
        }
        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_TEMPLATE_USED_STAT + templateId), null, createParamMapWithToken());
        printLog(object, "查询企业模板使用统计失败 fail:{}");
        return object.getObj(TemplateUsedStat.class);
    }

    /**
     * 查询企业模板使用作品列表
     *
     * @param templateId
     * @return
     */
    public List<TemplateUsedCreation> listTemplateUsedCreation(Long templateId) {
        if (templateId == null) {
            logger.warn("templateId 不能为空");
            return Collections.emptyList();
        }
        JSONObject object = HttpClientUtils.httpGet(getApiURL(API_TEMPLATE_USED_CREATION + templateId), null, createParamMapWithToken());
        printLog(object, "查询企业模板使用统计失败 fail:{}");
        return object.getList(TemplateUsedCreation.class);
    }
}
