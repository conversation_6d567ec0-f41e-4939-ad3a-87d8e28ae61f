package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description:参展信息
 * @author: JiuDD
 * date: 2025/2/13
 */
@Data
public class BigDataTradeshowDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**企业名称*/
    @JSONField(serialize = false)
    private String entname;
    /**统一信用代码*/
    @JSONField(serialize = false)
    private String uncid;
    /**总数量*/
    @JSONField(serialize = false)
    private int total_count;
    /**总页数*/
    @JSONField(serialize = false)
    private int total_page;
    /**当前页*/
    @JSONField(serialize = false)
    private int page;
    /**参展信息列表*/
    @JSONField(name = "参展信息列表",serialize = false)
    private List<ListTradeShow> list;

    @JSONField(name = "参展信息列表")
    private String listString;

    @NoArgsConstructor
    @Data
    public static class ListTradeShow implements Serializable {
        private static final long serialVersionUID = 1L;
        /**展会名称*/
        @JSONField(name = "参展展会名称")
        private String item;
        /**展会开始时间*/
        @JSONField(name = "展会开始时间",serialize = false)
        private String start_date;
        /**展会结束时间*/
        @JSONField(name = "展会结束时间",serialize = false)
        private String end_date;
    }
}
