package com.ce.scrm.center.service.business.abm;

import com.alibaba.fastjson.JSON;
import com.ce.cdp.dubbo.api.SensorsDBDubbo;
import com.ce.cdp.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dao.entity.SmaRole;
import com.ce.scrm.center.dao.entity.SmaRoleRelation;
import com.ce.scrm.center.dao.service.SmaRoleRelationService;
import com.ce.scrm.center.dao.service.SmaRoleService;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerMarketingActivityAssigneeSelectBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerMarketingActivityCreatorSelectBusinessDto;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.invoke.CustomerMarketingActivityThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerMarketingActivitiesCreatorSelectDubboView;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 客户营销活动业务
 * <AUTHOR>
 * @Date 2025-07-14 09:56
 */
@Slf4j
@Service
public class CustomerMarketingActivityBusiness {

	@Resource
	private CustomerMarketingActivityThirdService customerMarketingActivityThirdService;
	@Resource
	private CustomerThirdService customerThirdService;
	@Resource
	private SmaRoleService smaRoleService;
	@Resource
	private SmaRoleRelationService smaRoleRelationService;
	@Resource
	private EmployeeThirdService employeeThirdService;
	@DubboReference(group = "scrm-cdp-api", version = "1.0.0", check = false)
	private SensorsDBDubbo sensorsDBDubbo;

	private static final String SDR_ROLE = "SDR商务代表";

	/**
	 * 营销活动创建人下拉框
	 */
	public List<CustomerMarketingActivityCreatorSelectBusinessDto> creatorSelect() {
		List<CustomerMarketingActivitiesCreatorSelectDubboView> dubboViews = customerMarketingActivityThirdService.creatorSelect();
		if (CollectionUtils.isEmpty(dubboViews)) {
			return Collections.emptyList();
		}
		return BeanCopyUtils.convertToVoList(dubboViews, CustomerMarketingActivityCreatorSelectBusinessDto.class);
	}

	/**
	 * 营销活动SDR电话触达的分配人员下拉框
	 */
	public List<CustomerMarketingActivityAssigneeSelectBusinessDto> assigneeSelect() {
		String roleId = Optional.ofNullable(smaRoleService.lambdaQuery().eq(SmaRole::getName, SDR_ROLE).select(SmaRole::getId).last("limit 1").one()).map(SmaRole::getId).orElse(null);
		if (roleId == null) {
			log.error("未找到SDR商务代表sma_role信息");
			return Collections.emptyList();
		}
		List<String> relationIds = Optional.ofNullable(smaRoleRelationService.lambdaQuery().select(SmaRoleRelation::getRelationId).eq(SmaRoleRelation::getRoleId, roleId).list())
			.orElse(Collections.emptyList()).stream().map(SmaRoleRelation::getRelationId).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(relationIds)) {
			log.error("SDR电话触达的分配人员下拉框，未找到对应角色信息");
			return Collections.emptyList();
		}
		Map<String, EmployeeInfoThirdDto> employeeDataMap = employeeThirdService.getEmployeeDataMap(relationIds);
		if (CollectionUtils.isEmpty(employeeDataMap)) {
			log.error("SDR电话触达的分配人员下拉框，未找到对应员工信息, relationIds={}", relationIds);
			return Collections.emptyList();
		}
		return employeeDataMap.values().stream().map(item -> {
			CustomerMarketingActivityAssigneeSelectBusinessDto dto = new CustomerMarketingActivityAssigneeSelectBusinessDto();
			dto.setAssigneeId(item.getId());
			dto.setAssigneeName(item.getName());
			return dto;
		}).collect(Collectors.toList());
	}


	/**
	 * 获取客群数量
	 * @param segmentCode cdp分群code
	 * @return 数量
	 */
	public List<String> getCountBySegmentCode(String sql) throws ApiException {
		DubboResult<List<Map<String, Object>>> result = sensorsDBDubbo.executeSql(sql);
		if (Objects.isNull(result) || !result.checkSuccess() || CollectionUtils.isEmpty(result.getData())) {
			log.error("获取客群数量失败, sql:{}, result={}", sql, JSON.toJSONString(result));
			throw new ApiException("获取客群数量失败", result.getMsg());
		}
		List<Map<String, Object>> data = result.getData();
		List<String> customerIdList = data.stream().map(entry -> String.valueOf(entry.get("customerId"))).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(customerIdList)) {
			log.error("当前客群人数信息为0，sql:{}, result={}", sql, JSON.toJSONString(data));
			return Collections.emptyList();
		}
		log.info("获取客群数量getCountBySegmentCode, resultData={}", JSON.toJSONString(customerIdList));
		return customerIdList;
	}

}
