package com.ce.scrm.center.service.eqixiu.sdk.common;


import com.ce.scrm.center.service.eqixiu.sdk.domain.event.Event;
import com.ce.scrm.center.service.eqixiu.sdk.util.JsonMapper;

import java.util.List;

/**
 * 事件推送参数封装
 *
 * <AUTHOR>
 */
public class EventPushCmd extends EqxiuCallCmd {
    public Event getEvent(String encodingKey) {
        List<Event> events = JsonMapper.getInstance().fromJson(getParamStr(encodingKey), List.class, Event.class);
        if (events != null && !events.isEmpty()) {
            return events.get(0);
        }
        return null;
    }

}
