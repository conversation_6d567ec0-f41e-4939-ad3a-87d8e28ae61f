package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 不流转不流失客户表-分页请求体
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
public class CustomerCirculationSpecialSettingPageBusinessDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer pageNum;
    private Integer pageSize;

    @NotNull(message = "不流转类型不能为空")
    private Integer notCirculationType;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 分公司ID
     */
    private String subId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 商户Id
     */
    private String salerId;

    /**
     * 客户名称
     */
    private String custName;

}
