package com.ce.scrm.center.service.business.abm;

import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.enums.CodeMessageEnum;
import com.ce.scrm.center.dao.entity.SjAssignSubConfig;
import com.ce.scrm.center.dao.service.SjAssignSubConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 商机下发分司配置业务处理类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
@Slf4j
@Service
public class SjAssignSubConfigBusiness {

    @Resource
    private SjAssignSubConfigService sjAssignSubConfigService;

    /***
     * 获取下一个分司ID（轮询）
     * @param marketIdList 市场idList
     * <AUTHOR>
     * @date 2024/12/19 10:00:00
     * @version 1.0.0
     * @return Optional<String>
     **/
    public Optional<String> getNextSubId(List<String> marketIdList) {
        log.info("获取下一个分司ID（轮询），市场列表={}", marketIdList);
        try {
            // 参数校验
            if (CollectionUtils.isEmpty(marketIdList)) {
                log.warn("市场列表为空");
                return Optional.empty();
            }

            // 获取下一个分司配置
            SjAssignSubConfig subConfig = sjAssignSubConfigService.getNextSubConfig(marketIdList);
            if (Objects.isNull(subConfig)) {
                log.warn("在指定分司列表中没有可用的分司配置，市场列表={}", marketIdList);
                return Optional.empty();
            }
            
            return Optional.of(subConfig.getSubId());
        } catch (Exception e) {
            log.error("获取下一个分司ID失败，市场列表={}", marketIdList, e);
            return Optional.empty();
        }
    }

    /***
     * 更新权重
     * @param subId 分司ID
     * <AUTHOR>
     * @date 2024/12/19 10:00:00
     * @version 1.0.0
     * @return boolean
     **/
    public boolean updateWeight(String subId) {
        log.info("更新分司配置权重，分司ID={}", subId);
        try {
            // 参数校验
            if (StringUtils.isBlank(subId)) {
                return false;
            }

            boolean result = sjAssignSubConfigService.updateWeight(subId);
            if (result) {
                log.info("更新分司配置权重成功，分司ID={}", subId);
            } else {
                log.error("更新分司配置权重失败，分司ID={}", subId);
            }
            return result;
        } catch (Exception e) {
            log.error("更新分司配置权重异常，分司ID={}", subId, e);
            return false;
        }
    }
} 