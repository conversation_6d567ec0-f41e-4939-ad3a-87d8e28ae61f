package com.ce.scrm.center.service.third.invoke;

import cn.ce.cecloud.base.utils.DateUtil;
import cn.ce.cesupport.base.utils.MyStringUtils;
import cn.ce.cesupport.enums.*;
import cn.ce.cesupport.enums.favorites.ClueCustStatusEnum;
import cn.ce.cesupport.enums.favorites.ClueRuleEnum;
import cn.ce.cesupport.enums.favorites.SalerGetclueFromEnum;
import cn.ce.cesupport.framework.base.vo.Pagination;
import cn.ce.cesupport.newcustclue.service.ClueAssignAppService;
import cn.ce.cesupport.newcustclue.service.ClueMissionAppService;
import cn.ce.cesupport.newcustclue.service.ClueRuleAppService;
import cn.ce.cesupport.newcustclue.vo.ClueAssignVo;
import cn.ce.cesupport.newcustclue.vo.ClueCustVo;
import cn.ce.cesupport.newcustclue.vo.ClueMissionVo;
import cn.ce.cesupport.newcustclue.vo.ClueRuleVo;
import cn.ce.oms.customer.dto.ContactInfo;
import cn.ce.oms.customer.dto.ContactInfoDTO;
import cn.ce.oms.customer.dto.Result;
import cn.ce.oms.customer.dubbo.api.CustContactServiceApi;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.entity.dto.favorite.FavoriteCluePageThirdDto;
import com.ce.scrm.center.service.third.entity.view.BigDataPreciseLinkmanThirdView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.util.date.DateUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 线索分配三方业务
 * <AUTHOR>
 * @date 2025-01-16 10:10
 * @version 1.0.0
 **/
@Slf4j
@Service
@SuppressWarnings("all")
public class ClueAssignThirdService {

	@DubboReference
	private ClueAssignAppService clueAssignAppService;

	@DubboReference
	private ClueRuleAppService clueRuleAppService;

	@DubboReference
	private CustContactServiceApi custContactServiceApi;
	@Resource
	private BigDataThirdService bigDataThirdService;
	@DubboReference
	private ClueMissionAppService clueMissionAppService;
	@Resource
	private OrgThirdService orgThirdService;
	@Resource
	private EmployeeThirdService employeeThirdService;

	private static final List<String> HIDDEN_LABELS = Lists.newArrayList("ce1", "ce2");

	/**
	 * ce-operation-system服务
	 * </br>
	 * 设置大数据联系人、手机号清洗（先从缓存里获取，没命中则去拉去大数据）
	 */
	private List<ClueCustVo> setClueContact(List<ClueCustVo> list) {
		try {
			List<String> pidList = new ArrayList<>();
			for (ClueCustVo vo : list) {
				if(StringUtils.isNotEmpty(vo.getEntId())){
					pidList.add(vo.getEntId());
				}
			}
			// 查询清洗后排序后的第一个联系人
			Result<List<ContactInfoDTO>> telWashedTopContactInfoByPid = custContactServiceApi.getTelWashedTopContactInfoByPid(pidList);
			if(telWashedTopContactInfoByPid != null && "SYS0000".equals(telWashedTopContactInfoByPid.getCode()) && telWashedTopContactInfoByPid.getData() != null) {
				// 如果数据正确返回取出联系人设置到线索vo中用于前端显示
				List<ContactInfoDTO> contactInfoDTOList = telWashedTopContactInfoByPid.getData();
				Map<String, ContactInfoDTO> pidMap = new HashMap<>();
				for(ContactInfoDTO dto : contactInfoDTOList){
					pidMap.put(dto.getPid(), dto);
				}
				for (ClueCustVo vo : list) {
					ContactInfoDTO contactInfoDTO = pidMap.get(vo.getEntId());
					if(contactInfoDTO != null) {
						List<ContactInfo> contactInfoList = contactInfoDTO.getContactInfoList();
						if(CollectionUtils.isNotEmpty(contactInfoList)) {
							ContactInfo contactInfo = contactInfoList.get(0);
							vo.setLinkManName(contactInfo.getContactName());
							vo.setTel(contactInfo.getTel());
						}
					}
				}
				return list;
			}
		} catch (Exception e) {
			log.info("批量设置联系人缓存异常，走被动缓存,param={}", JSON.toJSONString(list));
		}
		return list;
	}

	/**
	 * appservice-newcustclue-api服务 收藏夹列表
	 * @param favoriteCluePageThirdDto 请求参数拼装
	 * @return com.ce.scrm.extend.service.third.entity.view.CustomerAddThirdView
	 */
	public Page<ClueCustVo> getClueCustPage(FavoriteCluePageThirdDto favoriteCluePageThirdDto) {
		if (Objects.nonNull(favoriteCluePageThirdDto)) {
			Pagination<ClueAssignVo> pagination = assembleCustPageReqParams(favoriteCluePageThirdDto);
			try {
				Pagination<ClueAssignVo> clueAssignVoPage = clueAssignAppService.getListPageNew(pagination);
				if (clueAssignVoPage == null) {
					return new Page<>();
				}
				List<ClueAssignVo> clueAssignVoList = clueAssignVoPage.getList();
				List<ClueCustVo> clueCustVoList = new ArrayList<>();
				List<String> pidList = new ArrayList<>();
				if (!CollectionUtils.isEmpty(clueAssignVoList)) {

					List<String> orgIdList = new ArrayList<>();
					clueAssignVoList.forEach(clueAssignVo -> {
						orgIdList.add(clueAssignVo.getSubCompanyId());
						orgIdList.add(clueAssignVo.getDeptId());
						orgIdList.add(clueAssignVo.getAreaId());
					});
					// 为了获取、deptName、salerName
					List<OrgThirdDto> orgThirdDtoList = orgThirdService.selectListByIds(orgIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
					Map<String, List<OrgThirdDto>> orgMap = Optional.ofNullable(orgThirdDtoList).orElse(Lists.newArrayList()).stream().collect(Collectors.groupingBy(OrgThirdDto::getId));
					List<String> empIds = clueAssignVoList.stream().map(ClueAssignVo::getEmpId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
					Map<String, EmployeeInfoThirdDto> employeeMap = employeeThirdService.getEmployeeDataMap(empIds);

					for (ClueAssignVo clueAssignVo : clueAssignVoList) {
						ClueCustVo clueVo = new ClueCustVo();
						if (null != clueAssignVo) {
							// deptName subName salerName 赋值
							if (StringUtils.isNotBlank(clueAssignVo.getDeptId())) {
								List<OrgThirdDto> currentDeptList  = orgMap.get(clueAssignVo.getDeptId());
								if (CollectionUtils.isNotEmpty(currentDeptList)) {
									clueVo.setDeptName(currentDeptList.get(0).getName());
								}
							}
							if (StringUtils.isNotBlank(clueAssignVo.getSubCompanyId())) {
								List<OrgThirdDto> currentSubList  = orgMap.get(clueAssignVo.getSubCompanyId());
								if (CollectionUtils.isNotEmpty(currentSubList)) {
									clueVo.setSubName(currentSubList.get(0).getName());
								}
							}
							if (StringUtils.isNotBlank(clueAssignVo.getEmpId())) {
								EmployeeInfoThirdDto empInfo = employeeMap.get(clueAssignVo.getEmpId());
								clueVo.setSalerName(empInfo != null ? empInfo.getName() : null);
							}

							clueVo = clueVoSetParams(clueAssignVo, clueVo);
							clueCustVoList.add(clueVo);
							if(StringUtils.isNotEmpty(clueVo.getEntId())){
								pidList.add(clueVo.getEntId());
							}
						}
					}
					List<ClueCustVo> clueCustVos = BeanCopyUtils.convertToVoList(setJingzhunFromBigData(pidList, clueCustVoList), ClueCustVo.class);
					Page<ClueCustVo> resultPage = new Page<>();
					resultPage.setRecords(setClueContact(clueCustVos));
					resultPage.setTotal(clueAssignVoPage.getTotalCount());
					resultPage.setCurrent(clueAssignVoPage.getCurrentPage());
					resultPage.setSize(clueAssignVoPage.getPageSize());
					return resultPage;
				}
			} catch (Exception ex) {
				log.warn("appservice-newcustclue-api服务 收藏夹列表异常", ex);
			}
		}
		return new Page<>();
	}


	/**
	 * 调用中台接口匹配精准联系人
	 * @param pidList entId集合
	 * @param clueCustVoList 线索vo集合
	 */
	private List<ClueCustVo> setJingzhunFromBigData(List<String> pidList, List<ClueCustVo> clueCustVoList) {
		// MapResultBean exactContactByPidList = scrmCompanyContactAppService.findExactContactByPidList(pidList);
		try {
			List<BigDataPreciseLinkmanThirdView> exactContactByPidList = bigDataThirdService.getPreciseLinkman(pidList);
			if(CollectionUtils.isNotEmpty(exactContactByPidList)) {
				exactContactByPidList.forEach(contactByPid -> clueCustVoList.forEach(clueVo -> {
					if (Objects.equals(contactByPid.getPid(), clueVo.getEntId())) {
						clueVo.setJingzhunLinkMan(1);
					}
				}));
			}
			return clueCustVoList;
		} catch (Exception ex) {
			log.info("调用中台接口匹配精准联系人异常: {}", ex.getMessage());
		}
		return clueCustVoList;
	}

	/**
	 * clueVo赋值操作
	 */
	private ClueCustVo clueVoSetParams(ClueAssignVo clueAssignVo, ClueCustVo clueVo) {
		try {
			clueVo.setIsHiden(HIDDEN_LABELS.contains(clueAssignVo.getLabelFrom()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
			clueVo.setId(clueAssignVo.getId());
			clueVo.setEntId(clueAssignVo.getEntId());
			clueVo.setCustName(MyStringUtils.formatStringIfBlank(clueAssignVo.getCustName()));
			clueVo.setUpdateTime(clueAssignVo.getUpdateTime());
			// 线索跟进状态
			String clueVisitStage = clueAssignVo.getClueVisitStage();
			ClueVisitStageEnum clueVisitStageByCode = ClueVisitStageEnum.getClueVisitStageByCode(clueVisitStage);
			if (null != clueVisitStageByCode) {
				clueVo.setVisitStatusStr(clueVisitStageByCode.getName());
			}

			ClueTypeEnum clueTypeByCode = ClueTypeEnum.getClueTypeByCode(clueAssignVo.getBussOppType());
			if (null != clueTypeByCode) {
				clueVo.setBussOppType(clueAssignVo.getBussOppType());
				clueVo.setBussOppTypeStr(MyStringUtils.formatStringIfBlank(clueTypeByCode.getName()));
			}
			// 分配时间
			if (null != clueAssignVo.getCreateTime()) {
				clueVo.setAssignTime(clueAssignVo.getCreateTime());
				String assignTimeStr = DateUtils.formatDate(clueVo.getAssignTime());
				clueVo.setAssignTimeStr(MyStringUtils.formatStringIfBlank(assignTimeStr));
			}
			// 距离超时时间
			Date exceedTime = clueAssignVo.getExceedTime();
			String exceedTimeStr = DateUtils.formatSurplusDateZh(exceedTime);
			clueVo.setExceedTimeStr(MyStringUtils.formatStringIfBlank(exceedTimeStr));
			clueVo.setUid(clueAssignVo.getUid());
			// 跟进时间
			Date visitTime = clueAssignVo.getVisitTime();
			if (null != visitTime) {
				String visitTimeStr = DateUtil.getDateStringY(visitTime);
				clueVo.setVisitTimeStr(visitTimeStr);
			}
			// 下次跟进时间
			Date nextVisitTime = clueAssignVo.getNextVisitTime();
			if (null != nextVisitTime) {
				String nextVisitTimeStr = DateUtil.getDateStringY(nextVisitTime);
				clueVo.setNextVisitTimeStr(nextVisitTimeStr);
			}
			// 客户类型：个人或企业
			int salerGetclueFrom = clueAssignVo.getSalerGetclueFrom();
			SalerGetclueFromEnum enumByValue = SalerGetclueFromEnum.getSalerGetclueFromEnumByValue(salerGetclueFrom);
			if (null != enumByValue) {
				clueVo.setSalerClueFrom(enumByValue.getValue().toString());
				clueVo.setSalerClueFromStr(enumByValue.getLable());
				SalerGetclueFromEnum enumByValueSub = SalerGetclueFromEnum.getSalerGetclueFromEnumByValue(clueAssignVo.getSalerGetclueFromSub ());
				if(enumByValueSub != null){
					clueVo.setSalerClueFromSub(enumByValueSub.getValue().toString ());
					clueVo.setSalerClueFromStr(clueVo.getSalerClueFromStr() + "/" + enumByValueSub.getLable());
				}
			}
			clueVo.setIsReaded(clueAssignVo.getIsReaded());
			// 行业
			String firstIndustry = clueAssignVo.getFirstIndustry();
			FirstIndustryBigDataEnum firstIndustryByCode = FirstIndustryBigDataEnum.getFirstIndustryByCode(firstIndustry);
			if (null != firstIndustryByCode) {
				clueVo.setFirstIndustryStr(firstIndustryByCode.getName());
			}

			// 任务名称
			String mission = clueAssignVo.getMission();
			if (StringUtils.isNotEmpty(mission)) {
				ClueMissionVo clueMissionVo = new ClueMissionVo();
				clueMissionVo.setMissionCode(mission);
				List<ClueMissionVo> selectClueMissionList = clueMissionAppService.selectClueMissionList(clueMissionVo);
				if (!org.springframework.util.CollectionUtils.isEmpty(selectClueMissionList)) {
					ClueMissionVo missionVo = selectClueMissionList.get(0);
					clueVo.setClueMission(missionVo.getMissionName());
				}
			}
			String secondIndustry = clueAssignVo.getSecondIndustry();
			SecondIndustryBigDataEnum secondIndustryByCode = SecondIndustryBigDataEnum.getSecondIndustryBigDataByCode(secondIndustry);
			if (null != secondIndustryByCode) {
				clueVo.setSecondIndustryStr(secondIndustryByCode.getName());
			}
			clueVo.setAddress(clueAssignVo.getAddress());
			clueVo.setHasLinkManOrNot(clueAssignVo.getHasLinkManOrNot());
			clueVo.setHasRecuitOrNot(clueAssignVo.getHasRecuitOrNot());
			clueVo.setHasWebSiteOrNot(clueAssignVo.getHasWebSiteOrNot());
			clueVo.setIsForeinTradeOrNot(clueAssignVo.getIsForeinTradeOrNot());
			clueVo.setIsGroupOrNot(clueAssignVo.getIsGroupOrNot());
			clueVo.setCustId(clueAssignVo.getCustId());
			clueVo.setIndustryZhongqiStr(clueAssignVo.getIndustryZhongqi() == null ? "--" : clueAssignVo.getIndustryZhongqi());
			return clueVo;
		} catch (Exception ex) {
			log.info("clueVoSetParams参数设置异常: {}", ex.getMessage());
		}
		return clueVo;
	}

	/**
	 * 组装收藏夹列表入参
	 */
	private Pagination<ClueAssignVo> assembleCustPageReqParams(FavoriteCluePageThirdDto favoriteCluePageThirdDto) {
		Pagination<ClueAssignVo> pagination = new Pagination<>();
		pagination.setCurrentPage(favoriteCluePageThirdDto.getPageNum());
		pagination.setPageSize(favoriteCluePageThirdDto.getPageSize());

		Map<String, Object> params = new HashMap<>();
		params.put("entId", favoriteCluePageThirdDto.getEntId());
		params.put("lablFrom", favoriteCluePageThirdDto.getLablFrom());
		params.put("industryZhongqi", favoriteCluePageThirdDto.getIndustryZhongqi());
		params.put("startTime", favoriteCluePageThirdDto.getStartTime());
		params.put("endTime", favoriteCluePageThirdDto.getEndTime());
		params.put("subId", favoriteCluePageThirdDto.getSubId());
		params.put("buId", favoriteCluePageThirdDto.getBuId());
		params.put("deptId", favoriteCluePageThirdDto.getDeptId());
		params.put("empId", favoriteCluePageThirdDto.getSalerId());
		params.put("bussOppType", favoriteCluePageThirdDto.getBussOppType());
		params.put("custId", favoriteCluePageThirdDto.getCustId());
		params.put("custName", StringUtils.isNotBlank(favoriteCluePageThirdDto.getCustName()) ? favoriteCluePageThirdDto.getCustName() : null);
		params.put("address", favoriteCluePageThirdDto.getAddress());
		params.put("clueMission", favoriteCluePageThirdDto.getClueMission());
		params.put("secondIndustry", favoriteCluePageThirdDto.getSecondIndustry());
		params.put("salerClueFrom", favoriteCluePageThirdDto.getSalerClueFrom());
		params.put("salerClueFromSub", favoriteCluePageThirdDto.getSalerClueFromSub());
		params.put("clueVisitStage", favoriteCluePageThirdDto.getClueVisitStage());
		params.put("nextVisitTimeStr", favoriteCluePageThirdDto.getNextVisitTimeStr());
		params.put("status", favoriteCluePageThirdDto.getStatus());

		String nextVisitdate = favoriteCluePageThirdDto.getNextVisitTimeStr();
		if (StringUtils.isNotEmpty(nextVisitdate)) {
			String startTime = nextVisitdate + " 00:00:00";
			String endTime = nextVisitdate + " 23:59:59";
			params.put("nextStartTime", DateUtil.formatStringToDate(startTime, DateUtil.YYYY_MM_DDHHMMSS));
			params.put("nextEndTime", DateUtil.formatStringToDate(endTime, DateUtil.YYYY_MM_DDHHMMSS));
		}
		pagination.setParams(params);
		// log.info("clueAssignAppService#getListPage request params={}", JSON.toJSONString(pagination));
		return pagination;
	}

	/**
	 * 获取商务的线索池容量
	 * @param salerId 商务id
	 * @param subId 分司id
	 * @param jobGrade 职级
	 * @return left: 当前线索池数量，right: 线索池容量
	 */
	public ImmutablePair<Integer, Integer> getSalerCapacityedCount(String salerId, String subId, String jobGrade) {
		if (StringUtils.isAnyBlank(salerId, subId, jobGrade)) {
			return ImmutablePair.of(0, 0);
		}
		ClueAssignVo vo = new ClueAssignVo();
		vo.setEmpId(salerId);
		vo.setStatus(ClueCustStatusEnum.SALER.getValue());
		Integer now = clueAssignAppService.getCount(vo);
		ClueRuleVo clueRuleVo = new ClueRuleVo();
		clueRuleVo.setSubId(subId);
		clueRuleVo.setTypeCode(ClueRuleEnum.CLUE_SALERMAXNUM.getValue());
		clueRuleVo.setJobGrade(jobGrade);
		ClueRuleVo oneByClueRule = clueRuleAppService.getOneByClueRule(clueRuleVo);
		int right = 0;
		if (Objects.nonNull(oneByClueRule)) {
			right = oneByClueRule.getTypeValue();
		}
		return ImmutablePair.of(now, right);
	}


}