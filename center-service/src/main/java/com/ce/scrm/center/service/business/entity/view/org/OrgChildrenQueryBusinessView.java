package com.ce.scrm.center.service.business.entity.view.org;

import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 组织机构子集
 * @Author: lijinpeng
 * @Date: 2024/11/21 13:54
 */
@Data
public class OrgChildrenQueryBusinessView implements Serializable {

    /**
     * 本身
     */
    private OrgThirdDto orgVo;

    /**
     * 子集
     */
    private List<OrgThirdDto> children;

}
