/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.template;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

/**
 * 添加企业模板标签
 */
public class AddCorpTagCmd extends BaseParam {
    private final Integer bizType = 2;
    private final String bizCode = "template_corp";
    private Long pId;
    private String title;

    public AddCorpTagCmd(Long pId, String title) {
        this.pId = pId;
        this.title = title;
    }

    public AddCorpTagCmd(String title) {
        this.title = title;
    }

    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> paramMap = getBaseParamsMap();
        paramMap.put("bizType", bizType.toString());
        paramMap.put("bizCode", bizCode);
        if (pId != null) {
            paramMap.put("pId", pId.toString());
        }
        paramMap.put("title", title);
        return paramMap;
    }

    @Override
    public void validate() {
        if (StrUtil.isEmpty(title)) {
            throw new KnownException("title不能为空");
        }
        if (title.length() > 50) {
            throw new KnownException("title长度不能超过50");
        }
    }

    public Integer getBizType() {
        return bizType;
    }

    public Long getpId() {
        return pId;
    }

    public void setpId(Long pId) {
        this.pId = pId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBizCode() {
        return bizCode;
    }
}
