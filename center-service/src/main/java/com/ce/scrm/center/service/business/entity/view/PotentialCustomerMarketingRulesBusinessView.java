package com.ce.scrm.center.service.business.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 潜客营销规则配置表
 */
@Data
public class PotentialCustomerMarketingRulesBusinessView implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 潜客leads等级 A+、A、B+、B、C、D
     */
    private String level;

    /**
     * 类别
     */
    private String type;

    /**
     * 潜客code
     */
    private String code;

    /**
     * 分配SDR角色
     */
    private String allocateRole;

    /**
     * leads来源
     */
    private String source;

    /**
     * leads来源说明
     */
    private String sourceDesc;

    /**
     * 字典名称
     */
    private String dictName;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 识别码
     */
    private String identifierCode;

    /**
     * 位置标签
     */
    private String locateTag;

    /**
     * leads摘要（四要素）
     */
    private Integer leadsSummaries;

    /**
     * 更多详情
     */
    private String moreInfo;

    /**
     * 状态 0-暂无 1-新增 2-已有
     */
    private Integer state;

    /**
     * 一级标签
     */
    private String firstTag;
    /**
     * 二级标签
     */
    private String secondTag;
    /**
     * 三级标签
     */
    private String thirdTag;
    /**
     * 标签分类id（D类标签对应的分类id）
     */
    private Long tagCategoryId;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 修改人ID
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;
}