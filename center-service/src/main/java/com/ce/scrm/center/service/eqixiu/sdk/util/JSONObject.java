/*
 *   Copyright (c) 2021-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 替代 fastjson 的 JSONObject 类
 *
 * <AUTHOR>
 */
public class JSONObject extends HashMap<String, Object> {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public JSONObject() {
    }

    public JSONObject(Map map) {
        super(map);
    }

    public JSONObject(String key, Object value) {
        this.put(key, value);
    }

    public static JSONObject empty() {
        return new JSONObject();
    }


    /**
     * 获取Object属性值
     *
     * @param key          属性名
     * @param defaultValue 默认值
     * @return 属性值，无对应值返回defaultValue
     */
    public Object getObj(String key, Object defaultValue) {
        return super.getOrDefault(key, defaultValue);
    }

    /**
     * 获取字符串型属性值<br>
     * 若获得的值为不可见字符，使用默认值
     *
     * @param key          属性名
     * @param defaultValue 默认值
     * @return 属性值，无对应值返回defaultValue
     */
    public String getStr(String key, String defaultValue) {
        Object obj = super.getOrDefault(key, defaultValue);
        if (obj == null) {
            return null;
        }
        if (obj instanceof String) {
            return (String) obj;
        }
        return obj.toString();
    }

    public String getStr(String key) {
        return getStr(key, null);
    }

    /**
     * 获取int型属性值<br>
     * 若获得的值为不可见字符，使用默认值
     *
     * @param key          属性名
     * @param defaultValue 默认值
     * @return 属性值，无对应值返回defaultValue
     */
    public Integer getInt(String key, Integer defaultValue) {
        Object obj = super.getOrDefault(key, defaultValue);
        if (obj == null) {
            return null;
        }
        if (obj instanceof Integer) {
            return (Integer) obj;
        }
        if (obj instanceof String) {
            return Integer.parseInt((String) obj);
        }
        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        }
        logger.warn("转换 Integer 失败，value:{}", obj);
        return null;
    }

    public Integer getInt(String key) {
        return getInt(key, null);
    }

    /**
     * 获取short型属性值<br>
     * 若获得的值为不可见字符，使用默认值
     *
     * @return 属性值，无对应值返回defaultValue
     */
    public Short getShort(String key, Short defaultValue) {
        logger.warn("待实现");
        return null;
    }

    /**
     * 获取boolean型属性值<br>
     * 若获得的值为不可见字符，使用默认值
     *
     * @param key          属性名
     * @param defaultValue 默认值
     * @return 属性值，无对应值返回defaultValue
     */
    public Boolean getBool(String key, Boolean defaultValue) {
        Object obj = super.getOrDefault(key, defaultValue);
        if (obj == null) {
            return Boolean.FALSE;
        }
        if (obj instanceof Boolean) {
            return (Boolean) obj;
        }
        if (obj instanceof String) {
            return Boolean.parseBoolean((String) obj);
        }
        logger.warn("转换 Boolean 失败，value:{}", obj);
        return Boolean.FALSE;
    }

    public Boolean getBool(String key) {
        return getBool(key, Boolean.FALSE);
    }

    /**
     * 获取Long型属性值<br>
     * 若获得的值为不可见字符，使用默认值
     *
     * @param key          属性名
     * @param defaultValue 默认值
     * @return 属性值，无对应值返回defaultValue
     */
    public Long getLong(String key, Long defaultValue) {
        Object obj = super.getOrDefault(key, defaultValue);
        if (obj == null) {
            return null;
        }
        if (obj instanceof Long) {
            return (Long) obj;
        }
        if (obj instanceof String) {
            return Long.parseLong((String) obj);
        }
        if (obj instanceof Number) {
            return ((Number) obj).longValue();
        }
        logger.warn("转换 Long 失败，value:{}", obj);
        return null;
    }

    public Long getLong(String key) {
        return getLong(key, null);
    }

    /**
     * 获取double型属性值<br>
     * 若获得的值为不可见字符，使用默认值
     *
     * @param key          属性名
     * @param defaultValue 默认值
     * @return 属性值，无对应值返回defaultValue
     */
    public Double getDouble(String key, Double defaultValue) {
        logger.warn("待实现");
        return null;
    }


    /**
     * 获取BigDecimal型属性值<br>
     * 若获得的值为不可见字符，使用默认值
     *
     * @param key          属性名
     * @param defaultValue 默认值
     * @return 属性值，无对应值返回defaultValue
     */
    public BigDecimal getBigDecimal(String key, BigDecimal defaultValue) {
        logger.warn("待实现");
        return null;
    }


    /**
     * 获得Enum类型的值
     *
     * @param <E>          枚举类型
     * @param clazz        Enum的Class
     * @param key          KEY
     * @param defaultValue 默认值
     * @return Enum类型的值，无则返回Null
     */
    <E extends Enum<E>> E getEnum(Class<E> clazz, String key, E defaultValue) {
        logger.warn("待实现");
        return null;
    }

    public JSONObject getJSONObject(String key, JSONObject defaultValue) {
        Object obj = super.getOrDefault(key, defaultValue);
        if (obj == null) {
            return null;
        }
        if (obj instanceof JSONObject) {
            return (JSONObject) obj;
        }
        if (obj instanceof String) {
            return JsonMapper.getInstance().fromJson((String) obj, JSONObject.class);
        }
        if (obj instanceof Map) {
            return new JSONObject((Map) obj);
        }
        logger.warn("转换 JSONObject 失败，value:{}", obj);
        return null;
    }

    public JSONObject getJSONObject(String key) {
        return getJSONObject(key, null);
    }

    /**
     * 获取Date类型值
     *
     * @param key 属性名
     * @return Date类型属性值
     */
    public Date getDate(String key) {
        Object obj = super.getOrDefault(key, null);
        if (obj == null) {
            return null;
        }
        if (obj instanceof Date) {
            return (Date) obj;
        }

        if (obj instanceof String) {
            try {
                return dateFormat.parse((String) obj);
            } catch (ParseException e) {
                logger.warn("日期格式化异常: {}", e.getMessage());
                return null;
            }
        }
        if (obj instanceof Long) {
            return new Date((Long) obj);
        }
        logger.warn("转换 Date 失败，value:{}", obj);
        return null;
    }

    public Map getMap(String key) {
        Object obj = super.getOrDefault(key, null);
        if (obj == null) {
            return null;
        }
        if (obj instanceof Map) {
            return (Map) obj;
        }
        if (obj instanceof String) {
            return JsonMapper.getInstance().fromJson((String) obj, Map.class);
        }
        logger.warn("转换 Map 失败，value:{}", obj);
        return null;
    }

    public <T> List<T> getBeanList(String key, Class<T> tclass) {
        Object obj = super.get(key);
        if (obj == null) {
            return null;
        }
        if (obj instanceof List) {
            List result = (List) obj;
            return (List<T>) result.stream().map(item -> {
                if (item instanceof String) {
                    return JsonMapper.parse((String) item, tclass);
                }
                String json = JsonMapper.toJSON(item);
                return JsonMapper.parse(json, tclass);
            }).collect(Collectors.toList());
        }
        if (obj instanceof String) {
            return JsonMapper.getInstance().fromJson((String) obj, List.class, tclass);
        }
        logger.warn("转换 List<{}> 失败，value:{}", tclass, obj);
        return null;
    }

    public <T> T get(String key, Class<T> tclass) {
        Object obj = super.get(key);
        if (obj == null) {
            return null;
        }
        if (obj instanceof String) {
            return JsonMapper.parse((String) obj, tclass);
        }
        try {
            return JsonMapper.parse(JsonMapper.toJSON(obj), tclass);
        } catch (Exception e) {
            logger.warn("转换 {} 失败，value:{}", tclass, obj);
            return null;
        }
    }

    public boolean getSuccess() {
        return getBool("success");
    }

    public String getMsg() {
        return getStr("msg");
    }

    public <T> List<T> getList(Class<T> tclass) {
        return getBeanList("list", tclass);
    }

    public <T> T getObj(Class<T> tclass) {
        return get("obj", tclass);
    }

    public String getCode() {
        return getStr("code");
    }

    public Map getResultMap() {
        return getMap("map");
    }
}
