package com.ce.scrm.center.service.eqixiu.sdk.common;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 结果集包装类
 *
 * <AUTHOR>
 */
public class Result<T> implements java.io.Serializable {

    /**
     * 调用结果
     */
    private boolean success;

    /**
     * 错误或成功代码
     */
    private String code;

    /**
     * 服务器端返回消息
     */
    private String msg;

    /**
     * 数据对象
     */
    private T obj;

    private Map<String, Object> map;

    /**
     * 集合数据对象
     */
    private List<T> list;

    /**
     * 用于异常时返回详情信息
     */
    private String[] details;

    public Result(boolean success, String code, String msg) {
        this.success = success;
        this.code = code;
        this.msg = msg;
    }

    public static Result ofSuccess() {
        return new Result(true, "200", "success");
    }

    public static Result ofSuccess(String msg) {
        return new Result(true, "200", msg);
    }

    public static Result ofFail(String msg) {
        return new Result(false, "500", msg);
    }

    public static Result ofFail(String code, String msg) {
        return new Result(false, code, msg);
    }

    public Result<T> put(String key, Object value) {
        if (this.map == null) {
            this.map = new HashMap<>();
        }
        this.map.put(key, value);
        return this;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getObj() {
        return obj;
    }

    public Result setObj(T obj) {
        this.obj = obj;
        return this;
    }

    public Map<String, Object> getMap() {
        return map;
    }

    public Result setMap(Map<String, Object> map) {
        this.map = map;
        return this;
    }

    public List<T> getList() {
        return list;
    }

    public Result setList(List list) {
        this.list = list;
        return this;
    }

    public String getDetails() {
        if (details == null || details.length == 0) return null;
        return Arrays.toString(details);
    }

    private Result setDetails(String... detail) {
        this.details = detail;
        return this;
    }

    @Override
    public String toString() {
        return "{" +
                "success=" + success +
                ", code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                ", obj=" + obj +
                ", map=" + map +
                ", list=" + list +
                '}';
    }
}
