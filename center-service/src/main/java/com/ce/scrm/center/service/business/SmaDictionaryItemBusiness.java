package com.ce.scrm.center.service.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ce.scrm.center.dao.entity.SmaDictionaryItem;
import com.ce.scrm.center.dao.service.SmaDictionaryItemService;
import com.ce.scrm.center.service.business.entity.view.SmaDictionaryItemView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * description: sma 字典
 * @author: DD.Jiu
 * date: 2024/7/12.
 */
@Slf4j
@Service
public class SmaDictionaryItemBusiness {
    @Resource
    private SmaDictionaryItemService smaDictionaryItemService;

    public Optional<List<SmaDictionaryItemView>> findByDictionaryId(String dictionaryId) {
        if (StringUtils.isBlank(dictionaryId)) {
            return Optional.empty();
        }
        LambdaQueryChainWrapper<SmaDictionaryItem> lambdaQueryChainWrapper = smaDictionaryItemService.lambdaQuery()
                .eq(SmaDictionaryItem::getDictionaryId, dictionaryId)
                .eq(SmaDictionaryItem::getState, 1)
                .orderByDesc(SmaDictionaryItem::getDisplayOrder);
        return Optional.of(CglibUtil.copyList(lambdaQueryChainWrapper.list(), SmaDictionaryItemView::new));
    }

    public Optional<SmaDictionaryItemView> findByCode(String dictionaryItemCode) {
        if (StringUtils.isBlank(dictionaryItemCode)) {
            return Optional.empty();
        }
        return Optional.of(CglibUtil.copy(smaDictionaryItemService.lambdaQuery().eq(SmaDictionaryItem::getCode, dictionaryItemCode).one(), SmaDictionaryItemView.class));
    }

	/**
	 * 根据id获取字典
	 * @param dictionaryItemId sma_dictionary_item.id
	 */
	public Optional<SmaDictionaryItemView> getById(String dictionaryItemId) {
		if (StringUtils.isBlank(dictionaryItemId)) {
			return Optional.empty();
		}
		SmaDictionaryItem byId = smaDictionaryItemService.getById(dictionaryItemId);
		if (byId == null) {
			return Optional.empty();
		}
		return Optional.of(CglibUtil.copy(byId, SmaDictionaryItemView.class));
	}

    /*
     * @Description 根据字典id集合返回字段集合
     * <AUTHOR>
     * @date 2025/2/17 15:52
     * @param idList
     * @return java.util.List<com.ce.scrm.center.service.business.entity.view.SmaDictionaryItemView>
     */
    public List<SmaDictionaryItemView> getByIdList(List<String> idList){

        if(CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }

        List<SmaDictionaryItem> list = smaDictionaryItemService.lambdaQuery().in(SmaDictionaryItem::getId, idList).list();
        return BeanUtil.copyToList(list, SmaDictionaryItemView.class);
    }

}
