package com.ce.scrm.center.service.third.entity.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerDistributeSdrRecordThirdDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 活动ID
	 */
	private Long activityId;

	/**
	 * 员工id
	 */
	private String empId;

	/**
	 * 员工姓名
	 */
	private String empName;

	/**
	 * 职位 1-SDR 2-CC
	 */
	private Integer positionType;

	/**
	 * 客户id
	 */
	private String customerId;
}
