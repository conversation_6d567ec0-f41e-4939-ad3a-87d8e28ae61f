package com.ce.scrm.center.service.enums;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/15 11:43
 */
public enum CustomerLayerEnum {
    /**
     * VIP客户
     */
    VIP(1, "VIP客户"),

    /**
     * 一般客户
     */
    GENERAL(2, "一般客户"),

    /**
     * 低价值客户
     */
    LOW_VALUE(3, "低价值客户"),

    /**
     * 高价值客户
     */
    HIGH_VALUE(4, "高价值客户");

    private final Integer code;
    private final String description;

    /**
     * 构造函数
     * @param code 层级编码（自然数序号，1开始）
     * @param description 层级描述
     */
    CustomerLayerEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取层级编码
     * @return 编码值
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取层级描述
     * @return 描述文本
     */
    public String getDescription() {
        return description;
    }

    public static String getDescriptionByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomerLayerEnum customerLayerEnum : values()) {
            if (customerLayerEnum.getCode().equals(code)) {
                return customerLayerEnum.description;
            }
        }
        return null;
    }

    public static Integer getCodeByDescription(String description) {
        if (description == null) {
            return null;
        }
        for (CustomerLayerEnum customerLayerEnum : values()) {
            if (customerLayerEnum.getDescription().equals(description)) {
                return customerLayerEnum.code;
            }
        }
        return null;
    }

}
