package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 中小商务查询高呈商机分页参数
 * <AUTHOR>
 * @date 2024/5/28 下午3:32
 * @version 1.0.0
 **/
@Data
public class CeQueryGcBusinessOpportunityPageBusinessDto implements Serializable {
    /**
     * 操作人岗位
     */
    private String operatorPosition;
    /**
     * 操作人分司ID
     */
    private String operatorSubId;
    /**
     * 操作人部门ID
     */
    private String operatorDeptId;
    /**
     * 操作人ID
     */
    private String operator;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 每页数量
     */
    private Integer pageSize = 10;
}