package com.ce.scrm.center.service.yml;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 三方客户配置
 * <AUTHOR>
 * @date 2024/2/22 17:47
 * @version 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "third.customer")
public class ThirdCustomerConfig {
    /**
     * dubbo请求密钥
     */
    private String key;
    /**
     * dubbo请求密钥密文
     */
    private String secret;
}