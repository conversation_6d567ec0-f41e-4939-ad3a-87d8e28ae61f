package com.ce.scrm.center.service.utils;

import cn.ce.cesupport.framework.base.enums.State;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class FileUploadUtil {
    private static final Logger logger = LoggerFactory.getLogger(FileUploadUtil.class);

    /**
     * <b>方法名：</b>：fileUpload<br>
     * <b>功能说明：</b>：文件上传<br>
     * <AUTHOR> color='blue'>wangjianying</font> 
     * @date  2018年2月28日 下午5:29:03
     * @param file 上传的文件
     * @param filterFileExtNames 允许的文件扩展名，多个用“,”分隔
     * @param allowFileSize 允许的文件大小，单位：M
     * @param filePath 上传文件的路径（绝对路径，到最后一级目录）
     * @return
     */
    public static Map<String, String> fileUpload(MultipartFile file, String filterFileExtNames, double allowFileSize, String filePath) {
        logger.info("----------文件上传----------start");
        Map<String, String> resultMap = new HashMap<String, String>(); // 返回结果

        if (StringUtils.isBlank(filePath)) {
            resultMap = State.ERROR.toMap();
            resultMap.put("msg", "缺少参数，文件路径没有指定");
            return resultMap;
        }

        // 判断文件是否存在
        if (file == null || file.isEmpty()) {
            resultMap = State.ERROR.toMap();
            resultMap.put("msg", "上传文件为空");
            logger.error("上传文件为空，filePath:{}",filePath);
            return resultMap;
        }

        String oriFileName = file.getOriginalFilename();
        // 文件扩展名
        String fileExtName = FilenameUtils.getExtension(oriFileName);

        boolean fileExtNameCheckFlag = true;
        // 文件类型校验
        if (filterFileExtNames.equals("All_SUFFIX")) {

        } else {
            fileExtNameCheckFlag = isContainItem(filterFileExtNames, fileExtName);
        }

        if (!fileExtNameCheckFlag) {
            resultMap = State.NO_SUPPORT_EXTENSION.toMap();
            return resultMap;
        }

        // 文件大小校验
        int fileBytes = 0;
        try {
            fileBytes = file.getBytes().length;
        } catch (IOException e) {

        }
        logger.info("----------文件上传----------文件大小：" + fileBytes);
        if (fileBytes > allowFileSize * 1024 * 1024) {
            resultMap = State.OVER_FILE_LIMIT.toMap();
            return resultMap;
        }

        try {
            String fileId = UUIDGenerator.generator();
            String fileName = fileId + "." + fileExtName;
            String fullFilePath = filePath;
            File directory = new File(fullFilePath);
            if (!directory.exists()) {
                directory.mkdirs();
            }
            if (!fullFilePath.endsWith("/")) {
                fullFilePath += "/";
            }
            fullFilePath += fileName;
            InputStream in = null;
            OutputStream out = null;
            try {
                in = file.getInputStream();
                out = new FileOutputStream(fullFilePath);
                byte[] bytes = new byte[4096];
                int length = 0;
                while ((length = in.read(bytes)) != -1) {
                    out.write(bytes, 0, length);
                }
                out.flush();
            } catch (Throwable e) {
                logger.error("throwable error", e);
            } finally {
                if (out != null) out.close();
                if (in != null) in.close();
            }
            resultMap = State.OK.toMap();
            resultMap.put("fileId", fileId);
            resultMap.put("size", String.valueOf(fileBytes));
            resultMap.put("fileName", fileName);
            resultMap.put("extName", fileExtName);
            resultMap.put("oriFileName", oriFileName);
        } catch (IOException e) {
            logger.error("upload IOException", e);
            resultMap = State.ERROR.toMap();
        } catch (Exception e) {
            logger.error("upload Exception", e);
            resultMap = State.ERROR.toMap();
        }
        if (resultMap.containsKey("code")) {
            logger.info("----------文件上传----------返回：" + resultMap.get("code"));
        }
        logger.info("----------文件上传----------end");
        return resultMap;
    }

    /**
     * <b>方法名：</b>：isContainItem<br>
     * <b>功能说明：</b>：判断字符串列表中是否包含指定的字符串项，字符串列表以","分隔<br>
     * <AUTHOR> color='blue'>wangjianying</font> 
     * @date  2018年2月28日 下午4:33:41
     * @param filterItems
     * @param item
     * @return
     */
    public static boolean isContainItem(String filterItems, String item) {
        if (item == null || item.trim().isEmpty()) {
            return false;
        }
        if (filterItems == null || filterItems.isEmpty()) {
            return true;
        }

        // 包含判断标志
        boolean checkFlag = false;

        String[] filterItemArr = filterItems.split(",");
        for (int i = 0; i < filterItemArr.length; i++) {
            if (item.equalsIgnoreCase(filterItemArr[i].trim())) {
                checkFlag = true;
                break;
            }
        }
        return checkFlag;
    }

    /**
     * <b>方法名：</b>：fileUpload<br>
     * <b>功能说明：</b>：文件上传，文件名不变<br>
     * <AUTHOR> color='blue'>闫静杰</font> 
     * @date  2018年2月28日 下午5:29:03
     * @param file 上传的文件
     * @param filterFileExtNames 允许的文件扩展名，多个用“,”分隔
     * @param allowFileSize 允许的文件大小，单位：M
     * @param filePath 上传文件的路径（绝对路径，到最后一级目录）
     * @return
     */
    public static Map<String, String> fileUploadWithName(MultipartFile file, String filterFileExtNames, double allowFileSize, String filePath,
                    String fileName) {
        logger.info("----------文件上传----------start");
        Map<String, String> resultMap = new HashMap<String, String>(); // 返回结果

        if (StringUtils.isBlank(filePath)) {
            resultMap = State.ERROR.toMap();
            resultMap.put("msg", "缺少参数，文件路径没有指定");
            return resultMap;
        }

        // 判断文件是否存在
        if (file == null || file.isEmpty()) {
            resultMap = State.ERROR.toMap();
            resultMap.put("msg", "上传文件为空");
            return resultMap;
        }

        String oriFileName = file.getOriginalFilename();
        // 文件扩展名
        String fileExtName = FilenameUtils.getExtension(oriFileName);

        boolean fileExtNameCheckFlag = true;
        // 文件类型校验
        if (filterFileExtNames.equals("All_SUFFIX")) {

        } else {
            fileExtNameCheckFlag = isContainItem(filterFileExtNames, fileExtName);
        }

        if (!fileExtNameCheckFlag) {
            resultMap = State.NO_SUPPORT_EXTENSION.toMap();
            return resultMap;
        }

        // 文件大小校验
        int fileBytes = 0;
        try {
            fileBytes = file.getBytes().length;
        } catch (IOException e) {

        }
        logger.info("----------文件上传----------文件大小：" + fileBytes);
        if (fileBytes > allowFileSize * 1024 * 1024) {
            resultMap = State.OVER_FILE_LIMIT.toMap();
            return resultMap;
        }

        try {
            String fullFilePath = filePath;
            File directory = new File(fullFilePath);
            if (!directory.exists()) {
                directory.mkdirs();
            }
            if (!fullFilePath.endsWith("/")) {
                fullFilePath += "/";
            }
            fullFilePath += fileName;
            File fullFile = new File(fullFilePath);
            if (!fullFile.exists()) {
                fullFile.delete();
            }
            InputStream in = null;
            OutputStream out = null;
            try {
                in = file.getInputStream();
                out = new FileOutputStream(fullFilePath);
                byte[] bytes = new byte[4096];
                int length = 0;
                while ((length = in.read(bytes)) != -1) {
                    out.write(bytes, 0, length);
                }
                out.flush();
            } catch (Throwable e) {
                logger.error("throwable error", e);
            } finally {
                if (out != null) out.close();
                if (in != null) in.close();
            }
            resultMap = State.OK.toMap();
            resultMap.put("size", String.valueOf(fileBytes));
            resultMap.put("fileName", fileName);
            resultMap.put("extName", fileExtName);
            resultMap.put("oriFileName", oriFileName);
        } catch (IOException e) {
            logger.error("upload IOException", e);
            resultMap = State.ERROR.toMap();
        } catch (Exception e) {
            logger.error("upload Exception", e);
            resultMap = State.ERROR.toMap();
        }
        if (resultMap.containsKey("code")) {
            logger.info("----------文件上传----------返回：" + resultMap.get("code"));
        }
        logger.info("----------文件上传----------end");
        return resultMap;
    }

    /**
     * @描述: 从指定URL下载文件并保存到指定目录
     * @作者: <EMAIL>
     * @日期: 2022/5/25 10:58
     * @参数 [url, fileDir, fileName, method]
     * @返回值 java.io.File
     */
    public static File saveUrlAs(String url, String fileDir, String fileName, String method) {
        // 创建不同的文件夹目录
        File file = new File(fileDir);
        // 判断文件夹是否存在
        if (!file.exists()) {
            // 如果文件夹不存在，则创建新的的文件夹
            file.mkdirs();
        }
        FileOutputStream fileOut = null;
        HttpURLConnection conn = null;
        InputStream inputStream = null;
        BufferedOutputStream bos = null;
        BufferedInputStream bis = null;
        try {
            // 建立链接
            URL httpUrl = new URL(url);
            conn = (HttpURLConnection) httpUrl.openConnection();
            // 以Post方式提交表单，默认get方式
            conn.setRequestMethod(method);
            conn.setDoInput(true);
            conn.setDoOutput(true);
            // post方式不能使用缓存
            conn.setUseCaches(false);
            // 连接指定的资源
            conn.connect();
            // 获取网络输入流
            inputStream = conn.getInputStream();
            bis = new BufferedInputStream(inputStream);
            // 判断文件的保存路径后面是否以/结尾
            if (!fileDir.endsWith("/")) {
                fileDir += "/";
            }
            // 写入到文件
            fileOut = new FileOutputStream(fileDir + fileName);
            bos = new BufferedOutputStream(fileOut);
            byte[] buf = new byte[4096];
            int length = bis.read(buf);
            // 保存文件
            while (length != -1) {
                bos.write(buf, 0, length);
                length = bis.read(buf);
            }
            bos.close();
            bis.close();
            conn.disconnect();
        } catch (Exception e) {
            logger.error("下载异常！", e);
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    logger.error("字节缓冲输出流关闭异常！", e);
                }
            }
            if (bos != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    logger.error("字节缓冲输入流关闭异常！", e);
                }
            }
            if (conn != null) {
                conn.disconnect();
            }
        }
        return file;
    }

}
