package com.ce.scrm.center.service.third.entity.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerShareQueryThirdDto implements Serializable {

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * ge筛选
     */
    private Date startTime;

}
