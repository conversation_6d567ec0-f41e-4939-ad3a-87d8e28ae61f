package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.utils.PositionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.follow.QueryVisitLogBusinessDto;
import com.ce.scrm.center.service.business.entity.view.follow.VisitLogDetailBusinessView;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.extend.dubbo.api.CmCustVisitLogDubboService;
import com.ce.scrm.extend.dubbo.entity.request.CmCustVisitLogReq;
import com.ce.scrm.extend.dubbo.entity.response.CmCustVisitLogRes;
import com.ce.scrm.extend.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.extend.dubbo.enums.VisitTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/8/8
 */
@Slf4j
@Service
public class VisitLogBusiness {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private CmCustVisitLogDubboService cmCustVisitLogDubboService;

    @Resource
    private EmployeeInfoBusiness employeeInfoBusiness;



    /***
     * 查询跟进记录
     * @param queryVisitLogBusinessDto
     * <AUTHOR>
     * @date 2025/8/8 15:38
     * @version 1.0.0
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.center.service.business.entity.view.follow.VisitLogDetailBusinessView>
    **/
    public Page<VisitLogDetailBusinessView> getVisitLogDetailList(QueryVisitLogBusinessDto queryVisitLogBusinessDto) {
        Page<VisitLogDetailBusinessView> result = new Page<>();
        CmCustVisitLogReq cmCustVisitLog = new CmCustVisitLogReq();
        cmCustVisitLog.setCustId(queryVisitLogBusinessDto.getCustomerId());
        log.info("查询跟进记录：{}", JSONObject.toJSONString(cmCustVisitLog));
        DubboPageInfo<CmCustVisitLogRes> dubboPageInfo = cmCustVisitLogDubboService.queryVisitLogsPage(cmCustVisitLog, queryVisitLogBusinessDto.getCurrentPage(), queryVisitLogBusinessDto.getPageSize());
        if (dubboPageInfo!=null && CollectionUtils.isNotEmpty(dubboPageInfo.getList())){
            // 跟进人ID列表
            List<String> salerIds = dubboPageInfo.getList().stream().map(CmCustVisitLogRes::getSalerId).distinct().collect(Collectors.toList());
            Map<String, EmployeeInfoBusinessDto> employeeMap = employeeInfoBusiness.getEmployeeInfoByEmpIdList(salerIds);
            /*4.1
            1.SDR的跟进全部展示
            2.跟进填写人是跨境员工
                2.1.跨境员工本人查看或者本人的上级查看：全部展示
                2.2.跨境员工非本人查看：不展示意愿度、描述
            3.跟进填写人是中企员工———不展示联系人、意愿度、描述*/
            List<VisitLogDetailBusinessView> records = dubboPageInfo.getList().stream().map(record -> {
                VisitLogDetailBusinessView businessView = new VisitLogDetailBusinessView();
                BeanUtils.copyProperties(record, businessView);
                businessView.setCustomerId(record.getCustId());
                businessView.setCustomerName(record.getCustName());
                EmployeeInfoBusinessDto employeeInfo = employeeMap.get(record.getSalerId());
                if (employeeInfo!=null){
                    businessView.setSalerName(employeeInfo.getName());
                }
                if (Objects.equals("SDR", record.getRoleName())){
                    businessView.setSource("SDR");
                }else{
                    businessView.setSource("销售");
                    //写跟进的商务是否是跨境
                    boolean kj = PositionUtil.isKj(employeeInfo.getAreaId());
                    String sdrAreaId = "sdr001";
                    if (kj || Objects.equals(sdrAreaId, employeeInfo.getAreaId())){
                        //跨境员工本人查看或者本人的上级查看：全部展示
                        if (employeeInfo.getId().equals(queryVisitLogBusinessDto.getLoginEmployeeId())
                                || (Objects.equals(employeeInfo.getOrgId(), businessView.getBussdeptId())) && PositionUtil.isBusinessManager(employeeInfo.getPosition())
                                || (Objects.equals(employeeInfo.getSubId(), businessView.getSubcompanyId())) && PositionUtil.isBusinessMajor(employeeInfo.getPosition())
                                || (Objects.equals(employeeInfo.getAreaId(), businessView.getAreaId())) && PositionUtil.isBusinessArea(employeeInfo.getPosition())

                        ){

                        }else{
                            //跨境员工非本人查看：不展示意愿度、描述
                            businessView.setContent("--");
                            businessView.setIntentionLevel("--");
                        }
                    }else{
                        businessView.setLinkManName("--");
                        businessView.setLinkManMobile("--");
                        businessView.setLinkManPosition("--");
                        businessView.setContent("--");
                        businessView.setIntentionLevel("--");
                    }
                }

                VisitTypeEnum visitTypeEnumByValue = VisitTypeEnum.getVisitTypeEnumByValue(record.getVisitType());
                if (visitTypeEnumByValue!=null){
                    businessView.setVisitTypeDesc(visitTypeEnumByValue.getLable());
                }
                return businessView;
            }).collect(Collectors.toList());
            result.setTotal(dubboPageInfo.getTotal());
            result.setPages(dubboPageInfo.getPages());
            result.setCurrent(queryVisitLogBusinessDto.getCurrentPage());
            result.setSize(queryVisitLogBusinessDto.getPageSize());
            result.setRecords(records);
        }
        return result;
    }
}
