package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 发送大数据参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/08/01
 **/
@Data
public class SendBigDataView implements Serializable {
    /**
     * entId列表
     */
    private List<String> entIdList;
    /**
     * 状态 修改flag1用
     */
    private Integer status;
    /**
     * 区域ID 修改flag5用
     */
    private String areaId;
    /**
     * 分司ID 修改flag5用
     */
    private String subId;
    /**
     * 部门ID 修改flag5用
     */
    private String deptId;
    /**
     * 商务ID 修改flag5用
     */
    private String salerId;
}