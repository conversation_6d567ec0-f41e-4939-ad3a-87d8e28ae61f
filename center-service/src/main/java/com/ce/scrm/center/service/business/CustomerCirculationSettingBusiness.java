package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.enums.YesOrNoEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerCirculationSetting;
import com.ce.scrm.center.dao.entity.CustomerCirculationSpecialSetting;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.ICustomerCirculationSettingService;
import com.ce.scrm.center.dao.service.ICustomerCirculationSpecialSettingService;
import com.ce.scrm.center.service.business.entity.dto.*;
import com.ce.scrm.center.service.business.entity.response.BusinessCodeMessageEnum;
import com.ce.scrm.center.service.business.entity.response.BusinessResult;
import com.ce.scrm.center.service.business.entity.view.SmaDictionaryItemView;
import com.ce.scrm.center.service.enums.ProtectCustTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 客户流转配置业务
 */
@Slf4j
@Service
public class CustomerCirculationSettingBusiness {

    @Resource
    private ICustomerCirculationSettingService customerCirculationSettingService;
    @Resource
    private ICustomerCirculationSpecialSettingService customerCirculationSpecialSettingService;
    @Resource
    private SmaDictionaryItemBusiness smaDictionaryItemBusiness;
    @Resource
    private CmCustProtectService cmCustProtectService;
    @Resource
    private CirculationLossBusiness circulationLossBusiness;


    public BusinessResult<CustomerCirculationSetting> getCustomerCirculationSettingByDto(CustomerCirculationSettingBusinessDto dto) {
        return BusinessResult.success(customerCirculationSettingService.getOne(new LambdaQueryWrapper<CustomerCirculationSetting>()
                .eq(Objects.nonNull(dto.getId()), CustomerCirculationSetting::getId, dto.getId())
                .eq(StringUtils.isNotBlank(dto.getSubId()), CustomerCirculationSetting::getSubId, dto.getSubId())));
    }

    public BusinessResult<Boolean> updateCustomerCirculationSetting(CustomerCirculationSettingEditBusinessDto dto) {
        // 查询是否是是分公司总监
        boolean isBusinessMajor = PositionUtil.isBusinessMajor(dto.getLoginPosition());
        if (!isBusinessMajor) {
            return BusinessResult.error(BusinessCodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }
        // 查询操作的数据是否是自己对应分司
        if (!dto.getLoginSubId().equals(dto.getSubId())) {
            return BusinessResult.error(BusinessCodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }
        // TODO 查询操作频次是否小于1年 上线初期总监肯定会频繁修改规则 先放开
        /*CustomerCirculationSetting byId = customerCirculationSettingService.getById(dto.getId());
        if (Objects.isNull(byId)) {
            return BusinessResult.error(BusinessCodeMessageEnum.DATA_NOT_EXIST);
        }
        Boolean dateCompareFlag = compareDateForEdit(byId.getUpdateTime());
        if (dateCompareFlag) {
            return BusinessResult.error(BusinessCodeMessageEnum.CUSTOMER_CIRCULATION_SETTING_EDIT_LESS_THAN_ONE_YEAR);
        }*/
        // 更新
        CustomerCirculationSetting circulationSetting = new CustomerCirculationSetting();
        BeanUtils.copyProperties(dto, circulationSetting);
        circulationSetting.setUpdateUserId(dto.getLoginEmployeeId());
        circulationSetting.setUpdateTime(new Date());
        boolean updateFlag = customerCirculationSettingService.updateById(circulationSetting);
        return BusinessResult.success(updateFlag);
    }

    public BusinessResult<Page<CustomerCirculationSpecialSetting>> customerCirculationSpecialSettingPageList(CustomerCirculationSpecialSettingPageBusinessDto dto) {
        Page<CustomerCirculationSpecialSetting> page = customerCirculationSpecialSettingService.page(new Page<>(dto.getPageNum(), dto.getPageSize()),
                new LambdaQueryWrapper<CustomerCirculationSpecialSetting>()
                        .eq(Objects.nonNull(dto.getNotCirculationType()), CustomerCirculationSpecialSetting::getNotCirculationType, dto.getNotCirculationType())
                        .eq(CustomerCirculationSpecialSetting::getDeleteFlag, YesOrNoEnum.NO.getCode())
                        .eq(StringUtils.isNotBlank(dto.getAreaId()), CustomerCirculationSpecialSetting::getAreaId, dto.getAreaId())
                        .eq(StringUtils.isNotBlank(dto.getSubId()), CustomerCirculationSpecialSetting::getSubId, dto.getSubId())
                        .eq(StringUtils.isNotBlank(dto.getDeptId()), CustomerCirculationSpecialSetting::getDeptId, dto.getDeptId())
                        .eq(StringUtils.isNotBlank(dto.getSalerId()), CustomerCirculationSpecialSetting::getSalerId, dto.getSalerId())
                        .like(Objects.nonNull(dto.getCustName()), CustomerCirculationSpecialSetting::getCustName, dto.getCustName()));
        // 赋值字典名称
        Optional<List<SmaDictionaryItemView>> notCirculation = smaDictionaryItemBusiness.findByDictionaryId("NOT_CIRCULATION");
        Optional<List<SmaDictionaryItemView>> notLose = smaDictionaryItemBusiness.findByDictionaryId("NOT_LOSE");
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            page.getRecords().forEach(item -> {
                if (StringUtils.isNotBlank(item.getNotCirculationAddReason()) && notCirculation.isPresent()) {
                    notCirculation.get().stream()
                            .filter(i -> i.getCode().equals(item.getNotCirculationAddReason()))
                            .findFirst()
                            .ifPresent(i -> item.setNotCirculationAddReasonName(i.getName()));
                }
                if (StringUtils.isNotBlank(item.getNotLoseAddReason()) && notLose.isPresent()) {
                    notLose.get().stream()
                            .filter(i -> i.getCode().equals(item.getNotLoseAddReason()))
                            .findFirst()
                            .ifPresent(i -> item.setNotLoseAddReasonName(i.getName()));
                }
            });
        }
        return BusinessResult.success(page);
    }

    @Transactional(rollbackFor = Exception.class)
    public BusinessResult<Boolean> saveCustomerCirculationSpecialSetting(CustomerCirculationSpecialSettingAddBusinessDto dto) {
        // 从保护关系查询是否是成交客户
        CmCustProtect cmCustProtect = cmCustProtectService.getOne(new LambdaQueryWrapper<CmCustProtect>().eq(CmCustProtect::getCustId, dto.getCustId()));
        if (Objects.isNull(cmCustProtect)) {
            BusinessResult.error(BusinessCodeMessageEnum.DATA_NOT_EXIST);
        }
        // 校验是否是成交客户
        if (!Objects.equals(cmCustProtect.getCustType(), ProtectCustTypeEnum.ORDERED.getValue()) && !Objects.equals(cmCustProtect.getCustType(), ProtectCustTypeEnum.SECOND_DEVELOPMENT.getValue())) {
            return BusinessResult.error(BusinessCodeMessageEnum.NOT_CLOSING_CUSTOMER);
        }
        // 校验是否由本司保护
        if (!cmCustProtect.getSubcompanyId().equals(dto.getLoginSubId())) {
            return BusinessResult.error(BusinessCodeMessageEnum.NOT_CURRENT_COMPANY_PROTECT);
        }
        // 校验该流转类型下是否已超过20个
        long count = customerCirculationSpecialSettingService.count(new LambdaQueryWrapper<CustomerCirculationSpecialSetting>()
                .eq(CustomerCirculationSpecialSetting::getSubId, dto.getLoginSubId())
                .eq(CustomerCirculationSpecialSetting::getNotCirculationType, dto.getNotCirculationType())
                .eq(CustomerCirculationSpecialSetting::getDeleteFlag, YesOrNoEnum.NO.getCode()));
        if (count >= 20) {
            return BusinessResult.error(BusinessCodeMessageEnum.CUSTOMER_CIRCULATION_SPECIAL_SETTING_COUNT_MORE_THAN_20);
        }
        // 校验该数据是否已存
        CustomerCirculationSpecialSetting currentSetting = customerCirculationSpecialSettingService.getOne(new LambdaQueryWrapper<CustomerCirculationSpecialSetting>()
                .eq(CustomerCirculationSpecialSetting::getCustId, cmCustProtect.getCustId())
                .eq(CustomerCirculationSpecialSetting::getNotCirculationType, dto.getNotCirculationType())
                .eq(CustomerCirculationSpecialSetting::getDeleteFlag, YesOrNoEnum.NO.getCode())
        );
        if (Objects.nonNull(currentSetting)) {
            return BusinessResult.error(BusinessCodeMessageEnum.DATA_EXIST);
        }
        // 存储
        CustomerCirculationSpecialSetting setting = new CustomerCirculationSpecialSetting();
        setting.setCustId(cmCustProtect.getCustId());
        setting.setCustName(cmCustProtect.getCustName());
        setting.setNotCirculationType(dto.getNotCirculationType());
        setting.setAreaId(cmCustProtect.getAreaId());
        setting.setSubId(cmCustProtect.getSubcompanyId());
        setting.setSubName(dto.getLoginSubName());
        setting.setDeptId(cmCustProtect.getBussdeptId());
        setting.setSalerId(cmCustProtect.getSalerId());
        setting.setNotCirculationAddReason(dto.getNotCirculationAddReason());
        setting.setNotLoseAddReason(dto.getNotLoseAddReason());
        setting.setCreateUserId(dto.getLoginEmployeeId());
        setting.setCreateTime(new Date());
        // 删除待流转待流失记录
        CirculationLossUpdateBusinessDto circulationLossUpdateBusinessDto = new CirculationLossUpdateBusinessDto();
        circulationLossUpdateBusinessDto.setCustId(cmCustProtect.getCustId());
        circulationLossBusiness.deleteCirculationLoss(circulationLossUpdateBusinessDto);
        return BusinessResult.success(customerCirculationSpecialSettingService.save(setting));
    }

    public BusinessResult<Boolean> updateCustomerCirculationSpecialSetting(CustomerCirculationSpecialSettingDeleteBusinessDto dto) {
        CustomerCirculationSpecialSettingBusinessDto queryDto = new CustomerCirculationSpecialSettingBusinessDto();
        queryDto.setId(dto.getId());
        CustomerCirculationSpecialSetting setting = customerCirculationSpecialSettingService.getById(dto.getId());
        if (Objects.isNull(setting)) {
            return BusinessResult.error(BusinessCodeMessageEnum.DATA_NOT_EXIST);
        }
        if (!setting.getSubId().equals(dto.getLoginSubId())) {
            return BusinessResult.error(BusinessCodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }
        setting.setDeleteFlag(YesOrNoEnum.YES.getCode());
        setting.setUpdateUserId(dto.getLoginEmployeeId());
        setting.setUpdateTime(new Date());
        return BusinessResult.success(customerCirculationSpecialSettingService.updateById(setting));
    }

    private Boolean compareDateForEdit(Date updateTime) {
        LocalDate localDateToCompare = updateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localCurrentDate = new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        long yearsBetween = ChronoUnit.YEARS.between(localDateToCompare, localCurrentDate);
        if (yearsBetween < 1) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    public BusinessResult<?> getSubDealCustomer(SubDealCustomerBusinessDto dto) {
        List<CmCustProtect> list = cmCustProtectService.list(new LambdaQueryWrapper<CmCustProtect>()
                .eq(CmCustProtect::getSubcompanyId, dto.getLoginSubId())
                .like(CmCustProtect::getCustName, dto.getCustName())
                .and(wrapper -> wrapper
                        .eq(CmCustProtect::getCustType, ProtectCustTypeEnum.ORDERED.getValue())
                        .or()
                        .eq(CmCustProtect::getCustType, ProtectCustTypeEnum.SECOND_DEVELOPMENT.getValue())
                )
                .last("LIMIT 10")
        );
        return BusinessResult.success(list);
    }

    /**
     * Description: 不流转控制表中是否存在该客户
     * @author: JiuDD
     * @param custId 客户id
     * @return boolean true不流转，false流转
     * date: 2024/9/3 18:18
     */
    public boolean checkSpecialSetting(String custId) {
        if (StringUtils.isEmpty(custId)) {
            return false;
        }
        List<CustomerCirculationSpecialSetting> specialSettingList = customerCirculationSpecialSettingService.lambdaQuery()
                .eq(CustomerCirculationSpecialSetting::getNotCirculationType, 1)
                .eq(CustomerCirculationSpecialSetting::getCustId, custId)
                .eq(CustomerCirculationSpecialSetting::getDeleteFlag, 0)
                .list();
        if (!CollectionUtils.isEmpty(specialSettingList)) {
            log.info("当前客户在不流转控制表中存在，custId:{}", custId);
            return true;
        }
        return false;
    }
}
