package com.ce.scrm.center.service.eqixiu.sdk.service;

import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.common.Result;
import com.ce.scrm.center.service.eqixiu.sdk.domain.CorpDept;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Staff;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.AppType;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.corp.*;
import com.ce.scrm.center.service.eqixiu.sdk.support.HttpClient;
import com.ce.scrm.center.service.eqixiu.sdk.support.TokenCache;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业服务接口
 *
 * <AUTHOR> Yang
 */
public class CorpService extends ConnectService {

    private static final String API_CORP_CHANGE_SUB_DOMAINS = "/api/v1/base/corp/subDomain/set";
    private static final String API_CORP_SET_QR_CODE = "/api/v1/base/open/app/config/save";
    private static final String API_CORP_SWITCH_APP = "/api/v1/base/app/config/apps/open";
    private static final String API_CORP_RIGHT_LOGIN = "/api/v1/base/rights/corp";


    private static final String API_DEPT_LIST = "/api/v1/base/corp/dept/list";
    private static final String API_DEPT_ADD = "/api/v1/base/corp/dept/add";
    private static final String API_DEPT_DELETE = "/api/v1/base/corp/dept/delete";
    private static final String API_DEPT_EDIT = "/api/v1/base/corp/dept/edit";


    private static final String API_STAFF_DELETE = "/api/v1/base/corp/staff/delete";
    private static final String API_STAFF_CHANGE = "/api/v1/base/corp/staff/role/change";
    private static final String API_STAFF_ADD = "/api/v1/base/oauth/staff/register";
    private static final String API_STAFF_LIST = "/api/v1/base/corp/staff/list";
    private static final String API_STAFF_CHANGE_DEPT = "/api/v1/base/corp/staff/dept/change";
    private static final String API_STAFF_DISABLE = "/api/v1/base/corp/staff/disable";
    private static final String API_STAFF_ENABLE = "/api/v1/base/corp/staff/enable";
    private static final String API_CORP_WX_BIND = "/api/v1/biz/wx/auth/bind";

    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public CorpService(Secret secret) {
        super(secret);
    }

    public CorpService(Secret secret, TokenCache tokenCache) {
        super(secret, tokenCache);
    }

    public CorpService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        super(secret, tokenCache, httpClient);
    }

    //--------企业管理接口----


    /**
     * 企业二级域名修改
     * 需要新的自定义二级域名
     *
     * @param subDomain
     * @return
     */
    public boolean changeSubDomain(String subDomain) {

        if (StrUtil.isEmpty(subDomain)) {
            throw new KnownException("subDomain 不可以为空");
        }

        HashMap<String, String> map = createParamMapWithToken();
        map.put("newDomain", subDomain);

        JSONObject object = httpClient.httpPost(getApiURL(API_CORP_CHANGE_SUB_DOMAINS, map), null, map);
        printLog(object, "修改企业子域名失败:{}");
        return object.getSuccess();
    }

    /**
     * 设置企业的二维码
     *
     * @param qrcodeUrl 二维码地址
     */
    public boolean setCorpQrCode(String qrcodeUrl) {

        if (StrUtil.isEmpty(qrcodeUrl)) {
            throw new KnownException("qrcodeUrl参数不能为空");
        }

        HashMap<String, String> map = createParamMapWithToken();
        map.put("value", "{\"qrcode\":\"" + qrcodeUrl + "\"}");
        map.put("productId", String.valueOf(0));//参数写死
        map.put("cfgKey", String.valueOf(1014));//参数写死

        JSONObject object = httpClient.httpPost(getApiURL(API_CORP_SET_QR_CODE, map), null, map);
        printLog(object, "设置企业二维码失败 fail:{}");
        return object.getSuccess();
    }

    /**
     * 开启企业应用
     *
     * @param app
     * @return
     */
    public boolean enableApp(AppType app) {
        return switchApp(app, "1");
    }

    /**
     * 关闭企业应用
     *
     * @param app
     * @return
     */
    public boolean disableApp(AppType app) {
        return switchApp(app, "0");
    }


    /**
     * 查询企业过期时间
     *
     * @return
     */
    public Date getCorpExpireTime() {
        HashMap<String, String> params = createParamMapWithToken();
        JSONObject object = httpClient.httpGet(getApiURL(API_CORP_RIGHT_LOGIN), null, params);
        printLog(object, "获取企业过期时间失败 fail:{}");
        return object.getObj(JSONObject.class).getDate("expireTime");
    }

    /**
     * 绑定微信授权
     * 使用返回的grantUrl链接访问并进行公众号绑定
     *
     * @param redirectUrl 绑定成功后跳转地址
     * @return
     */
    public BindWxVO bindWx(String redirectUrl) {
        Map<String, String> paramMap = createParamMapWithToken();
        if (StrUtil.isNotEmpty(redirectUrl)) {
            paramMap.put("redirectUrl", redirectUrl);
        }
        JSONObject object = httpClient.httpPost(getApiURL(API_CORP_WX_BIND, paramMap), null, paramMap);
        printLog(object, "获取绑定微信URL fail:{}");
        return new BindWxVO((String) object.getResultMap().get("grantUrl"), (String) object.getResultMap().get("state"));
    }


    //------部门管理接口----

    /**
     * 创建部门
     *
     * @param parentId 上级部门id,企业默认会生成一个根部门节点，可以通过查询部门列表接口获得
     * @param name
     * @return
     */
    public CorpDept createCorpDept(Long parentId, String name) {
        if (parentId == null || StrUtil.isEmpty(name)) {
            throw new KnownException("parentId,name 参数不能为空");
        }
        if (parentId <= 0) {
            throw new KnownException("parentId参数不合法");
        }
        HashMap<String, String> paramMap = createParamMapWithToken();
        paramMap.put("parentId", String.valueOf(parentId));
        paramMap.put("name", name);
        JSONObject object = httpClient.httpPost(getApiURL(API_DEPT_ADD, paramMap), null, paramMap);
        printLog(object, "创建部门失败 fail:{}");
        return object.getObj(CorpDept.class);
    }

    /**
     * 企业部门查询
     *
     * @param name 部门名称，为模糊搜索
     * @return 返回部门list
     */
    public List<CorpDept> listDept(String name) {
        HashMap<String, String> map = createParamMapWithToken();
        if (StrUtil.isNotEmpty(name)) {
            map.put("name", name);
        }
        JSONObject object = httpClient.httpGet(getApiURL(API_DEPT_LIST), null, map);
        printLog(object, "listDept fail, msg:{}");
        return object.getList(CorpDept.class);
    }

    /**
     * 修改部门名称
     *
     * @param deptId
     * @param name
     * @return
     */
    public CorpDept changeDeptName(Long deptId, String name) {
        if (deptId == null || StrUtil.isEmpty(name)) {
            throw new KnownException("deptId,name 参数不能为空");
        }
        HashMap<String, String> paramMap = createParamMapWithToken();
        paramMap.put("id", String.valueOf(deptId));
        paramMap.put("name", name);
        JSONObject object = httpClient.httpPost(getApiURL(API_DEPT_EDIT, paramMap), null, paramMap);
        printLog(object, "changeDeptName fail, msg:{}");
        return object.getObj(CorpDept.class);
    }

    /**
     * 删除部门
     *
     * @param deptId
     * @return
     */
    public boolean deleteDept(Long deptId) {
        if (deptId == null) {
            logger.warn("deptId 参数不能为空");
            return false;
        }
        HashMap<String, String> paramMap = createParamMapWithToken();
        paramMap.put("id", String.valueOf(deptId));
        JSONObject object = httpClient.httpPost(getApiURL(API_DEPT_DELETE, paramMap), null, paramMap);
        printLog(object, "deleteDept fail, msg:{}");
        return object.getSuccess();
    }

    //------- 员工管理接口----

    /**
     * 将企业员工注册到内容中台，可用于单点登录
     *
     * @param cmd
     * @return 当前只返回员工ID
     */
    public Staff addStaff(AddStaffCmd cmd) {
        paramValidate(cmd);
        //这里有一个特殊处理，openId 加到 url 上后不能再添加到form 参数列表中，不然会导致生成的 openId 变重复字符串
        Map<String, String> paramMap = cmd.getParamsMap();
        JSONObject object = httpClient.httpPost(getApiURL(API_STAFF_ADD, paramMap), null, paramMap);
        printLog(object, "addStaff fail, msg:{}");
        return new Staff((String) object.getObj(Map.class).get("staffId"));
    }

    /**
     * 变更企业员工角色
     * 员工角色必须已存在，可更改的类型为
     * 1所有者，2管理员，3员工
     * 所有者不可更改，尤且只有一个
     *
     * @param cmd
     * @return
     */
    public boolean changeStaffRole(ChangeStaffRoleCmd cmd) {
        paramValidate(cmd);
        JSONObject object = httpClient.httpPost(getApiURL(API_STAFF_CHANGE, cmd.getParamsMap()), null, cmd.getParamsMap());
        printLog(object, "changeStaffRole fail, msg:{}");
        return object.getSuccess();

    }

    /**
     * 查询企业员工列表
     *
     * @param query
     * @return
     */
    public Result<Staff> findStaff(StaffQuery query) {
        paramValidate(query);
        JSONObject object = httpClient.httpGet(getApiURL(API_STAFF_LIST), null, query.getParamsMap());
        printLog(object, "findStaff fail, msg:{}");
        return getResult(object, Staff.class);
    }

    /**
     * 删除企业的员工
     *
     * @param openIds 企业员工的ID
     * @return
     */
    public boolean deleteStaff(String... openIds) {
        if (openIds == null || openIds.length == 0) {
            logger.warn("openIds参数不能为空");
            return false;
        }
        HashMap<String, String> map = createParamMapWithToken();
        map.put("openIds", String.join(",", openIds));
        JSONObject obj = httpClient.httpPost(getApiURL(API_STAFF_DELETE, map), null, map);
        printLog(obj, "deleteStaff fail, msg:{}");
        return obj.getSuccess();
    }

    /**
     * 变更员工部门
     *
     * @param cmd
     * @return
     */
    public boolean changeStaffDept(ChangeStaffDeptCmd cmd) {
        paramValidate(cmd);
        JSONObject object = httpClient.httpPost(getApiURL(API_STAFF_CHANGE_DEPT, cmd.getParamsMap()), null, cmd.getParamsMap());
        printLog(object, "changeStaffDept fail, msg:{}");
        return object.getSuccess();
    }

    /**
     * 停用员工账号，支持批量停用
     *
     * @param openIds
     * @return
     */
    public boolean disableStaff(String... openIds) {
        if (openIds == null || openIds.length == 0) {
            logger.warn("openIds参数不能为空");
            return false;
        }
        HashMap<String, String> map = createParamMapWithToken();
        map.put("openIds", String.join(",", openIds));
        JSONObject object = httpClient.httpPost(getApiURL(API_STAFF_DISABLE, map), null, map);
        printLog(object, "disableStaff fail, msg:{}");
        return object.getSuccess();
    }

    /**
     * 启用员工账号,支持批量停用
     *
     * @param openIds
     * @return
     */
    public boolean enableStaff(String... openIds) {
        if (openIds == null || openIds.length == 0) {
            logger.warn("openIds参数不能为空");
            return false;
        }
        HashMap<String, String> map = createParamMapWithToken();
        map.put("openIds", String.join(",", openIds));
        JSONObject object = httpClient.httpPost(getApiURL(API_STAFF_ENABLE, map), null, map);
        printLog(object, "disableStaff fail, msg:{}");
        return object.getSuccess();
    }

    private boolean switchApp(AppType app, String status) {
        if (app == null) {
            throw new KnownException("appType参数不能为空");
        }
        HashMap<String, String> map = createParamMapWithToken();
        map.put("app", app.getValue());
        map.put("value", status);

        JSONObject object = httpClient.httpPost(getApiURL(API_CORP_SWITCH_APP, map), null, map);
        printLog(object, status.equals("1") ? "开启" : "关闭" + "企业应用失败 fail:{}");
        return object.getSuccess();
    }

}
