package com.ce.scrm.center.service.eqixiu.sdk.service;


import com.ce.scrm.center.service.eqixiu.sdk.common.Result;
import com.ce.scrm.center.service.eqixiu.sdk.domain.BlackList;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.blacklist.AddBlackCmd;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.blacklist.BlackListQuery;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.blacklist.DelBlackListCmd;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.blacklist.RemarkBlackListCmd;
import com.ce.scrm.center.service.eqixiu.sdk.support.HttpClient;
import com.ce.scrm.center.service.eqixiu.sdk.support.TokenCache;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;


/**
 * 黑名单服务
 * 黑名单设置针对所有作品，一旦用户命中黑名单，则不可参与互动
 * https://hc.eqxiu.cn/doc/1107/
 *
 * <AUTHOR>
 */
public class BlackListService extends ConnectService {


    private static final String API_BLACK_LIST = "/api/v1/biz/customer/blacklist/list";
    private static final String API_BLACK_LIST_ADD = "/api/v1/biz/customer/blacklist/add";
    private static final String API_BLACK_LIST_REMARK = "/api/v1/biz/customer/blacklist/remark";
    private static final String API_BLACK_LIST_DELETE = "/api/v1/biz/customer/blacklist/del";

    BlackListService(Secret secret) {
        super(secret);
    }

    public BlackListService(Secret secret, TokenCache tokenCache) {
        super(secret, tokenCache);
    }

    public BlackListService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        super(secret, tokenCache, httpClient);
    }

    /**
     * 获取企业黑名单列表
     *
     * @param query
     * @return
     */
    public Result<BlackList> findBlackList(BlackListQuery query) {
        paramValidate(query);
        JSONObject object = httpClient.httpGet(getApiURL(API_BLACK_LIST), null, query.getParamsMap());
        printLog(object, "查询黑名单失败:{}");
        return getResult(object, BlackList.class);
    }


    /**
     * 添加黑名单
     *
     * @param cmd
     * @return
     */
    public BlackList addBlackList(AddBlackCmd cmd) {
        paramValidate(cmd);
        JSONObject object = httpClient.httpPostObj(getApiURL(API_BLACK_LIST_ADD, cmd.getParamsMap()), cmd.getParamsMap());
        printLog(object, "添加黑名单失败:{}");
        return object.getObj(BlackList.class);

    }

    /**
     * 修改黑名单备注
     *
     * @param cmd
     * @return
     */
    public BlackList remarkBlackList(RemarkBlackListCmd cmd) {
        paramValidate(cmd);
        JSONObject object = httpClient.httpPostObj(getApiURL(API_BLACK_LIST_REMARK, cmd.getParamsMap()), cmd.getParamsMap());
        printLog(object, "修改黑名单备注失败:{}");
        return object.getObj(BlackList.class);
    }

    /**
     * 删除黑名单
     *
     * @param cmd
     * @return
     */
    public BlackList deleteBlackList(DelBlackListCmd cmd) {
        paramValidate(cmd);
        JSONObject object = httpClient.httpGet(getApiURL(API_BLACK_LIST_DELETE), null, cmd.getParamsMap());
        printLog(object, "删除黑名单失败:{}");
        return object.getObj(BlackList.class);
    }

}
