package com.ce.scrm.center.service.third.invoke;

import cn.ce.cesupport.framework.base.vo.MapResultBean;
import cn.ce.cesupport.sma.service.SalerRoleAppService;
import cn.hutool.core.collection.CollectionUtil;
import com.ce.scrm.center.service.third.entity.view.SalerRoleDataThirdView;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 商务代表角色三方业务
 * <AUTHOR>
 * @date 2025-01-15 10:10
 * @version 1.0.0
 */
@Slf4j
@Service
public class SalerRoleThirdService {

    @DubboReference(check = false, timeout = 3000, retries = 0)
    private SalerRoleAppService salerRoleAppService;



    /**
     * Description: 检查保护库容是否已满
     * @author: Xukang
     * @param subId 分司Id
     * @param salerId 商务Id | 员工id
     * @return {@link SalerRoleDataThirdView}
     * date: 2025-01-15 10:10
     */
    public SalerRoleDataThirdView checkProtectRuleNums(String subId, String salerId) {
		// 入参顺序，先subId 后salerId
        MapResultBean mapResult = salerRoleAppService.checkProtectRuleNums(subId, salerId);
        Map<String, Object> data = mapResult.getData();
        if (CollectionUtil.isEmpty(data)) {
			log.warn("salerRoleAppService#checkProtectRuleNums data is null, {}", mapResult.getMsg());
            return SalerRoleDataThirdView.builder().build();
        }
        Integer nowCapacityedCount = (Integer) data.get("nowCapacityedCount");
        Integer totalCapacityedCount = (Integer) data.get("totalCapacityedCount");
        return SalerRoleDataThirdView.builder().nowCapacityedCount(nowCapacityedCount).totalCapacityedCount(totalCapacityedCount).build();
    }
}