package com.ce.scrm.center.service.business;

import cn.ce.cecloud.base.utils.DateUtil;
import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.base.utils.MyStringUtils;
import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.emp.enums.OrgTypeEnum;
import cn.ce.cesupport.emp.service.EmployeeAppService;
import cn.ce.cesupport.emp.vo.EmployeeVo;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.ce.cesupport.enums.CustomerStageEnum;
import cn.ce.cesupport.enums.UserActionTypeEnum;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.ce.cesupport.enums.favorites.*;
import cn.ce.cesupport.enums.segment.SalerCustomTagEnum;
import cn.ce.cesupport.enums.segment.SegmentCustomerStateEnum;
import cn.ce.cesupport.enums.segment.SegmentSendMessageTypeEnum;
import cn.ce.cesupport.enums.segment.SegmentStatusEnum;
import cn.ce.cesupport.newcustclue.service.ClueAssignAppService;
import cn.ce.cesupport.newcustclue.service.ClueRuleAppService;
import cn.ce.cesupport.newcustclue.vo.ClueAssignVo;
import cn.ce.cesupport.newcustclue.vo.ClueRuleVo;
import cn.ce.cesupport.sma.dto.CustomerStageInfoDto;
import cn.ce.cesupport.sma.service.CustomerStageAppService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.cache.constant.CacheConstant;
import com.ce.scrm.center.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.center.dao.entity.*;
import com.ce.scrm.center.dao.entity.query.AreaSegmentQuery;
import com.ce.scrm.center.dao.entity.query.SmaMarketSubcompanyQuery;
import com.ce.scrm.center.dao.entity.view.SmaMarketSubcompanyView;
import com.ce.scrm.center.dao.service.*;
import com.ce.scrm.center.service.business.entity.CdpSegmentDistributeMqData;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.segment.*;
import com.ce.scrm.center.service.business.entity.mq.SegmentAddFavoriteMqData;
import com.ce.scrm.center.service.business.entity.mq.SegmentSalerTagMqData;
import com.ce.scrm.center.service.business.entity.view.segment.*;
import com.ce.scrm.center.service.cache.SegmentDataStatisticsCacheHandler;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.entity.view.OrgDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.service.third.invoke.SmaConvertLogThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.util.date.BigDecimalUtils;
import com.ce.scrm.center.util.date.DateUtils;
import com.ce.scrm.extend.dubbo.api.ICompanyInfoEsDubbo;
import com.ce.scrm.extend.dubbo.entity.dto.BigDataCustomerFlagDto;
import com.ce.scrm.extend.dubbo.entity.request.SkbParamsReq;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.EntIndustryInfoView;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.*;

/**
 * @version 1.0
 * @Description: cdp分群下发
 * @Author: lijinpeng
 * @Date: 2025/2/27 11:57
 */
@Slf4j
@Service
public class SegmentBusiness {

    @Resource
    private SegmentService segmentService;

    @Resource
    private SegmentDetailService segmentDetailService;

    @Resource
    private SmaMarketAreaService smaMarketAreaService;

    @Resource
    private OrgThirdService orgThirdService;
	@Resource
	private EmployeeInfoBusiness employeeInfoBusiness;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @DubboReference
    private CustomerStageAppService customerStageAppService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private ProtectBusiness protectBusiness;

    @Resource
    SmaMarketSubcompanyService smaMarketSubcompanyService;
    @Resource
    private RocketMqOperate rocketMqOperate;
	@Resource
    private SegmentDataStatisticsCacheHandler segmentDataStatisticsCacheHandler;
    @Autowired
    private SendWxMessage sendWxMessage;
    @Resource
    private ClueRuleAppService clueRuleAppService;
    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;

    @Resource
    private ClueAssignAppService clueAssignAppService;

    @Resource
    private CmCustProtectService custProtectService;
	@Resource
	private SegmentEmpService segmentEmpService;
    @Autowired
    private OrgInfoBusiness orgInfoBusiness;

    @Resource
    private EmployeeAppService employeeAppService;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private ICompanyInfoEsDubbo iCompanyInfoEsDubbo;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /*
     * @Description 根据区域查询分群信息
     * <AUTHOR>
     * @date 2025/2/27 17:10
     * @param queryAreaSegmentBusinessDto
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.center.service.business.entity.view.segment.QueryAreaSegmentBusinessView>
     */
    public Page<QueryAreaSegmentBusinessView> getSegmentPageByAreaId(@Valid QueryAreaSegmentBusinessDto queryAreaSegmentBusinessDto) {

        log.info("getSegmentPageByAreaId 入参为={}",JSON.toJSONString(queryAreaSegmentBusinessDto));

        if(!PositionUtil.isBusinessArea(queryAreaSegmentBusinessDto.getLoginPosition()) || StringUtils.isBlank(queryAreaSegmentBusinessDto.getLoginAreaId())) {
            throw new ApiException(CodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }

        Page<SegmentDetail> objectPage = Page.of(queryAreaSegmentBusinessDto.getPageNum(), queryAreaSegmentBusinessDto.getPageSize());

        AreaSegmentQuery areaSegmentQuery = BeanUtil.copyProperties(queryAreaSegmentBusinessDto, AreaSegmentQuery.class);
        areaSegmentQuery.setAreaId(queryAreaSegmentBusinessDto.getLoginAreaId());
        Page<SegmentDetail> page = segmentDetailService.getSegmentPageByAreaId(objectPage, areaSegmentQuery);

        Page<QueryAreaSegmentBusinessView> result = new Page<>();
        BeanUtil.copyProperties(page, result);

        List<SegmentDetail> records = page.getRecords();
        Set<String> cityCodeSet = new HashSet<>();
        Set<String> subIdSet = new HashSet<>();
        for (SegmentDetail record : records) {

            String regCity = record.getRegCity();
            if(StringUtils.isNotBlank(regCity)) {
                cityCodeSet.add(regCity);
            }

            String subIdListStr = record.getSubIdListStr();
            if(StringUtils.isNotBlank(subIdListStr)) {
                String[] subIds = subIdListStr.split(",");
                subIdSet.addAll(Arrays.asList(subIds));
            }

        }

        Map<String,String> cityMap;
        if(CollectionUtils.isNotEmpty(cityCodeSet)) {
            List<SmaMarketArea> list = smaMarketAreaService.lambdaQuery().in(SmaMarketArea::getCityCode, cityCodeSet).list();
            cityMap = list.stream().collect(Collectors.toMap(SmaMarketArea::getCityCode, SmaMarketArea::getCityName, (key1, key2) -> key2));
        }else {
            cityMap = Collections.emptyMap();
        }

        Map<String,String> subMap;
        if (CollectionUtils.isNotEmpty(subIdSet)) {
            List<OrgThirdDto> orgThirdDtoList = orgThirdService.selectListByIds(new ArrayList<>(subIdSet));
            subMap =orgThirdDtoList.stream().collect(Collectors.toMap(OrgThirdDto::getId,OrgThirdDto::getName,(key1,key2) -> key2));
        }else {
            subMap = Collections.emptyMap();
        }


        List<QueryAreaSegmentBusinessView> resultList = new ArrayList<>();
        for (SegmentDetail segmentDetail : records) {

            QueryAreaSegmentBusinessView queryAreaSegmentBusinessView = BeanUtil.copyProperties(segmentDetail, QueryAreaSegmentBusinessView.class);

            String subIdListStr = segmentDetail.getSubIdListStr();
            StringBuilder sb = new StringBuilder();
            if(StringUtils.isNotBlank(subIdListStr)) {
                String[] subIds = subIdListStr.split(",");
                for (String subId : subIds) {
                    sb.append(subMap.get(subId) == null ? "" : subMap.get(subId) + "、");
                }
            }
            queryAreaSegmentBusinessView.setAllocatedSubListNameStr(sb.length()>0 ? sb.substring(0,sb.length()-1) : null);

            queryAreaSegmentBusinessView.setCityName(cityMap.get(segmentDetail.getRegCity()));
            String customerStateStr = getCustomerStateStr(segmentDetail);
            queryAreaSegmentBusinessView.setCustomerStateStr(customerStateStr);
            queryAreaSegmentBusinessView.setVisitFlagStr(segmentDetail.getVisitLastTime() == null ? "否" : "是");
            queryAreaSegmentBusinessView.setIcpFlagStr(Objects.equals(segmentDetail.getIcpFlag(),1) ? "是" : "否");
            queryAreaSegmentBusinessView.setJingchukouFlagStr(Objects.equals(segmentDetail.getJingchukouFlag(),1) ? "是" : "否");

            resultList.add(queryAreaSegmentBusinessView);
        }
        result.setRecords(resultList);
        return result;
    }

    private static String getCustomerStateStr(SegmentDetail segmentDetail) {
        String customerStateStr = null;
        if(Objects.equals(YesOrNoEnum.YES.getCode(), segmentDetail.getProtectStatus()) && Objects.equals(YesOrNoEnum.NO.getCode(), segmentDetail.getDealStatus())) {
            customerStateStr = SegmentCustomerStateEnum.PROTECT.getName();
        }else if (Objects.equals(YesOrNoEnum.NO.getCode(), segmentDetail.getProtectStatus()) && Objects.equals(YesOrNoEnum.NO.getCode(), segmentDetail.getDealStatus())) {
            customerStateStr = SegmentCustomerStateEnum.NOT_PROTECT.getName();
        }else if (Objects.equals(YesOrNoEnum.YES.getCode(), segmentDetail.getProtectStatus()) && Objects.equals(YesOrNoEnum.YES.getCode(), segmentDetail.getDealStatus())) {
            customerStateStr = SegmentCustomerStateEnum.MAKE_A_BARGAIN.getName();
        }
        return customerStateStr;
    }

    public Page<QueryRoleSegmentBusinessView> getSegmentPageByRole(@Valid QueryRoleSegmentBusinessDto queryDto) {

        log.info("getSegmentPageByRole 入参为={}",JSON.toJSONString(queryDto));

        String loginPosition = queryDto.getLoginPosition();
        if(PositionUtil.isBusinessMajor(loginPosition)) {
            queryDto.setSubId(queryDto.getLoginSubId());
        } else if (PositionUtil.isBusinessBu(loginPosition)) {
            queryDto.setSubId(queryDto.getLoginSubId());
            queryDto.setBuId(queryDto.getLoginBuId());
        } else if (PositionUtil.isBusinessManager(loginPosition)) {
            queryDto.setSubId(queryDto.getLoginSubId());
            queryDto.setBuId(queryDto.getLoginBuId());
            queryDto.setDeptId(queryDto.getLoginOrgId());
        } else if (PositionUtil.isBusinessSaler(loginPosition)) {
            queryDto.setSubId(queryDto.getLoginSubId());
            queryDto.setBuId(queryDto.getLoginBuId());
            queryDto.setDeptId(queryDto.getLoginOrgId());
            queryDto.setSalerId(queryDto.getLoginEmployeeId());
        } else {
            throw new ApiException(CodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }

        Page<SegmentDetail> detailPage = Page.of(queryDto.getPageNum(), queryDto.getPageSize());
        LambdaQueryChainWrapper<SegmentDetail> lambdaQuery = segmentDetailService.lambdaQuery();

        if(StringUtils.isNotBlank(queryDto.getSegmentId())) {
            lambdaQuery.eq(SegmentDetail::getSegmentId, queryDto.getSegmentId());
        }
        if(StringUtils.isNotBlank(queryDto.getSubId())) {
            if (Objects.equals(queryDto.getSubId(),"-1")) {
                lambdaQuery.isNull(SegmentDetail::getAreaDistributeTime);
            }else {
                lambdaQuery.eq(SegmentDetail::getSubId, queryDto.getSubId());
            }
        }
        if(StringUtils.isNotBlank(queryDto.getBuId())) {
            if (Objects.equals(queryDto.getBuId(),"-1")) {
                lambdaQuery.isNull(SegmentDetail::getSubDistributeTime);
            }else {
                lambdaQuery.eq(SegmentDetail::getBuId, queryDto.getBuId());
            }
        }
        if(StringUtils.isNotBlank(queryDto.getDeptId())) {
            if (Objects.equals(queryDto.getDeptId(),"-1")) {
                lambdaQuery.isNull(SegmentDetail::getBuDistributeTime);
            }else {
                lambdaQuery.eq(SegmentDetail::getDeptId, queryDto.getDeptId());
            }
        }
        if (StringUtils.isNotBlank(queryDto.getSalerId())) {
            if (Objects.equals(queryDto.getSalerId(),"-1")) {
                lambdaQuery.isNull(SegmentDetail::getDeptDistributeTime);
            }else {
                lambdaQuery.eq(SegmentDetail::getSalerId, queryDto.getSalerId());
            }
        }
        if (StringUtils.isNotBlank(queryDto.getSalerCustomTag())) {
            lambdaQuery.eq(SegmentDetail::getSalerCustomTag, queryDto.getSalerCustomTag());
        }
        if (StringUtils.isNotBlank(queryDto.getCustomerStatus())) {
            if (Objects.equals(queryDto.getCustomerStatus(),"1")) {
                lambdaQuery.eq(SegmentDetail::getProtectStatus, YesOrNoEnum.NO.getCode());
                lambdaQuery.eq(SegmentDetail::getDealStatus, YesOrNoEnum.NO.getCode());
            }else if(Objects.equals(queryDto.getCustomerStatus(),"2")) {
                lambdaQuery.eq(SegmentDetail::getProtectStatus, YesOrNoEnum.YES.getCode());
                lambdaQuery.eq(SegmentDetail::getDealStatus, YesOrNoEnum.NO.getCode());
            }else if(Objects.equals(queryDto.getCustomerStatus(),"3")) {
                lambdaQuery.eq(SegmentDetail::getProtectStatus, YesOrNoEnum.YES.getCode());
                lambdaQuery.eq(SegmentDetail::getDealStatus, YesOrNoEnum.YES.getCode());
            }
        }
        if(queryDto.getVisitFlag() != null) {
            if(Objects.equals(queryDto.getVisitFlag(),YesOrNoEnum.YES.getCode())) {
                lambdaQuery.isNotNull(SegmentDetail::getVisitLastTime);
            }else if (Objects.equals(queryDto.getVisitFlag(),YesOrNoEnum.NO.getCode())) {
                lambdaQuery.isNull(SegmentDetail::getVisitLastTime);
            }
        }
        if (CollectionUtils.isNotEmpty(queryDto.getCityCodeList())) {
            lambdaQuery.in(SegmentDetail::getRegCity, queryDto.getCityCodeList());
        }
        if (CollectionUtils.isNotEmpty(queryDto.getFirstIndustryCodeList())) {
            lambdaQuery.in(SegmentDetail::getFirstIndustryCode, queryDto.getFirstIndustryCodeList());
        }
        if (CollectionUtils.isNotEmpty(queryDto.getSecondIndustryCodeList())) {
            lambdaQuery.in(SegmentDetail::getSecondIndustryCode, queryDto.getSecondIndustryCodeList());
        }
        if (CollectionUtils.isNotEmpty(queryDto.getThirdIndustryCodeList())) {
            lambdaQuery.in(SegmentDetail::getThirdIndustryCode, queryDto.getThirdIndustryCodeList());
        }
        if (CollectionUtils.isNotEmpty(queryDto.getFourthIndustryCodeList())) {
            lambdaQuery.in(SegmentDetail::getFourthIndustryCode, queryDto.getFourthIndustryCodeList());
        }
        if(queryDto.getRegisterCapitalMin() != null) {
            lambdaQuery.ge(SegmentDetail::getRegisterCapital, queryDto.getRegisterCapitalMin());
        }
        if (queryDto.getRegisterCapitalMax() != null) {
            lambdaQuery.lt(SegmentDetail::getRegisterCapital, queryDto.getRegisterCapitalMax());
        }
        if (queryDto.getIcpFlag() != null) {
            lambdaQuery.eq(SegmentDetail::getIcpFlag, queryDto.getIcpFlag());
        }
        if (queryDto.getJingchukouFlag() != null) {
            lambdaQuery.eq(SegmentDetail::getJingchukouFlag, queryDto.getJingchukouFlag());
        }
        if (StringUtils.isNotBlank(queryDto.getLikeRemark())) {
            lambdaQuery.like(SegmentDetail::getRemark, queryDto.getLikeRemark());
        }

        Page<SegmentDetail> page = lambdaQuery.eq(SegmentDetail::getDeleteFlag,YesOrNoEnum.NO.getCode()).page(detailPage);

        Page<QueryRoleSegmentBusinessView> result = new Page<>();
        BeanUtil.copyProperties(page, result);

        List<SegmentDetail> records = page.getRecords();

        Set<String> cityCodeSet = new HashSet<>();
        Set<String> orgIdSet = new HashSet<>();
        Set<String> empIdSet = new HashSet<>();
        for (SegmentDetail segmentDetail : records) {

            String regCity = segmentDetail.getRegCity();
            if(StringUtils.isNotBlank(regCity)) {
                cityCodeSet.add(regCity);
            }

            String buId = segmentDetail.getBuId();
            if (StringUtils.isNotBlank(buId)) {
                orgIdSet.add(buId);
            }
            String deptId = segmentDetail.getDeptId();
            if (StringUtils.isNotBlank(deptId)) {
                orgIdSet.add(deptId);
            }
            String salerId = segmentDetail.getSalerId();
            if (StringUtils.isNotBlank(salerId)) {
                empIdSet.add(salerId);
            }

        }

        Map<String,String> cityMap;
        if(CollectionUtils.isNotEmpty(cityCodeSet)) {
            List<SmaMarketArea> list = smaMarketAreaService.lambdaQuery().in(SmaMarketArea::getCityCode, cityCodeSet).list();
            cityMap = list.stream().collect(Collectors.toMap(SmaMarketArea::getCityCode, SmaMarketArea::getCityName, (key1, key2) -> key2));
        }else {
            cityMap = Collections.emptyMap();
        }

        Map<String,String> orgMap;
        if (CollectionUtils.isNotEmpty(orgIdSet)) {
            List<OrgThirdDto> orgThirdDtoList = orgThirdService.selectListByIds(new ArrayList<>(orgIdSet));
            orgMap =orgThirdDtoList.stream().collect(Collectors.toMap(OrgThirdDto::getId,OrgThirdDto::getName,(key1,key2) -> key2));
        }else {
            orgMap = Collections.emptyMap();
        }

        Map<String, EmployeeInfoThirdDto> employeeMap;
        if (CollectionUtils.isNotEmpty(empIdSet)) {
            employeeMap = employeeThirdService.getEmployeeDataMap(new ArrayList<>(empIdSet));
        }else {
            employeeMap = Collections.emptyMap();
        }

        List<QueryRoleSegmentBusinessView> resultList = new ArrayList<>();
        for (SegmentDetail segmentDetail : records) {

            QueryRoleSegmentBusinessView item = BeanUtil.copyProperties(segmentDetail, QueryRoleSegmentBusinessView.class);

            item.setCityName(cityMap.get(segmentDetail.getRegCity()));

            String buName = orgMap.get(segmentDetail.getBuId());
            String deptName = orgMap.get(segmentDetail.getDeptId());
            String allocatedDeptNameStr = null;
            if(buName != null && deptName != null) {
                allocatedDeptNameStr = buName+"/"+deptName;
            }else if (buName == null && deptName != null) {
                allocatedDeptNameStr = deptName;
            }else if (deptName == null && buName != null) {
                allocatedDeptNameStr = buName;
            }
            item.setAllocatedDeptNameStr(allocatedDeptNameStr);

            item.setAllocatedSalerNameStr(employeeMap.get(segmentDetail.getSalerId()) != null ? employeeMap.get(segmentDetail.getSalerId()).getName() : null );
            item.setVisitFlagStr(segmentDetail.getVisitLastTime() == null ? "否" : "是");
            String customerStateStr = getCustomerStateStr(segmentDetail);
            item.setCustomerStateStr(customerStateStr);
            item.setSalerCustomTagStr(SalerCustomTagEnum.getNameByCode(segmentDetail.getSalerCustomTag()));
            item.setIcpFlagStr(Objects.equals(segmentDetail.getIcpFlag(),1) ? "是" : "否");
            item.setJingchukouFlagStr(Objects.equals(segmentDetail.getJingchukouFlag(),1) ? "是" : "否");

            // 如果登录人是商务角色  查询客户位置信息
            if (PositionUtil.isBusinessSaler(loginPosition)) {
                try {
                    CustomerStageInfoDto customerStageByCustomerName = customerStageAppService.getCustomerStageByCustomerName(segmentDetail.getCustomerName(), queryDto.getLoginEmployeeId());
                    if(customerStageByCustomerName != null) {
                        CustomerStageEnum customerStageEnum = customerStageByCustomerName.getCustomerStageEnum();
                        item.setCustomerLocationStr(customerStageEnum.getName());
                    }
                }catch (Exception e){
                    log.info("处理失败，查找客户位置返回空，customerName={},loginEmployeeId={}", segmentDetail.getCustomerName(), queryDto.getLoginEmployeeId());
//                throw new RuntimeException(e);
                }
            }

            resultList.add(item);
        }

        result.setRecords(resultList);
        return result;
    }

    /***
     * 通过分群ID获取分群信息
     * @param segmentId
     * <AUTHOR>
     * @date 2025/2/27 15:22
     * @version 1.0.0
     * @return com.ce.scrm.center.dao.entity.Segment
    **/
    public Segment detail(String segmentId){
        LambdaQueryWrapper<Segment> lambdaQueryChainWrapper = new LambdaQueryWrapper<>();
        lambdaQueryChainWrapper.eq(Segment::getSegmentId, segmentId).last("limit 1");
        return segmentService.getOne(lambdaQueryChainWrapper);
    }

    /***
     * 分配
     * 1:区域总监分配
     * 2:分公司总监分配
     * 3:事业部总监分配
     * 4:部门经理/小组组长分配
     * @param segmentAssignmentBusinessDto
     * <AUTHOR>
     * @date 2025/2/27 15:57
     * @version 1.0.0
     * @return java.util.Optional
    **/
    public Optional assignment(SegmentAssignmentBusinessDto segmentAssignmentBusinessDto){
        String segmentId=segmentAssignmentBusinessDto.getSegmentId();
        Date current = new Date();
        // 根据分群ID 和 客户列表  查询 未成交的客户列表,防止把 已成交的分出去
        LambdaQueryWrapper<SegmentDetail> segmentDetailQueryWrapper = new LambdaQueryWrapper<>();
        segmentDetailQueryWrapper.eq(SegmentDetail::getSegmentId,segmentAssignmentBusinessDto.getSegmentId());
        segmentDetailQueryWrapper.in(SegmentDetail::getCustomerId,segmentAssignmentBusinessDto.getCustomerIds());
        segmentDetailQueryWrapper.eq(SegmentDetail::getDealStatus,0);
        if (Objects.equals(segmentAssignmentBusinessDto.getAssignmentRoleType(),1)){
            segmentDetailQueryWrapper.eq(SegmentDetail::getAreaId,segmentAssignmentBusinessDto.getLoginAreaId());
        }else if (Objects.equals(segmentAssignmentBusinessDto.getAssignmentRoleType(),2)){
            segmentDetailQueryWrapper.eq(SegmentDetail::getSubId,segmentAssignmentBusinessDto.getLoginSubId());
        }else if (Objects.equals(segmentAssignmentBusinessDto.getAssignmentRoleType(),3)){
            segmentDetailQueryWrapper.eq(SegmentDetail::getBuId,segmentAssignmentBusinessDto.getLoginBuId());
        }else if (Objects.equals(segmentAssignmentBusinessDto.getAssignmentRoleType(),4)){
            segmentDetailQueryWrapper.eq(SegmentDetail::getDeptId,segmentAssignmentBusinessDto.getLoginOrgId());
        }
        List<SegmentDetail> list = segmentDetailService.list(segmentDetailQueryWrapper);
        if (CollectionUtils.isEmpty(list)){
            // 分的客户全部是已成交的客户
            return Optional.empty();
        }
        // 一个客户可能分配给多个人,按照客户ID分组
        Map<String, List<SegmentDetail>> segmentDetailGroupByCustomerIdMap = list.stream().collect(Collectors.groupingBy(SegmentDetail::getCustomerId));
        if (Objects.equals(segmentAssignmentBusinessDto.getAssignmentType(),1)){
            List<Integer> protectStageList =new ArrayList<>();
            protectStageList.add(1);
            protectStageList.add(2);
            protectStageList.add(3);
            protectStageList.add(5);
            segmentDetailGroupByCustomerIdMap.forEach((customerId, segmentDetails) -> {
                if (segmentDetails.isEmpty()) return;
                //获取保护关系
                CmCustProtect protect = protectBusiness.getCmCustProtect(customerId);
                if (Objects.nonNull(protect)){
                    if (Objects.equals(protect.getStatus(),1)){
                        SegmentDetail segmentDetail = segmentDetails.get(0);
                        SegmentDetail newRecord = cloneSegmentDetailWithUpdates(segmentDetail, detail -> {
                            detail.setAreaDistributeTime(current);
                            detail.setSubDistributeTime(current);
                            detail.setBuDistributeTime(current);
                            detail.setDeptDistributeTime(current);
                            detail.setAreaId(protect.getAreaId());
                            detail.setSubId(protect.getSubcompanyId());
                            detail.setBuId(protect.getBuId());
                            detail.setDeptId(protect.getBussdeptId());
                            detail.setSalerId(protect.getSalerId());
                            detail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                            detail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                            detail.setUpdateTime(current);
                            detail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                        });
                        // 商务保护
                        List<String> assignedSubIds = segmentDetails.stream().map(SegmentDetail::getSubId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(assignedSubIds) || assignedSubIds.contains(protect.getSubcompanyId())){
                            newRecord.setId(segmentDetail.getId());
                            rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                    .segmentId(newRecord.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                    .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                    .customerId(newRecord.getCustomerId())
                                    .distributeTime(current)
                                    .receivedId(newRecord.getSalerId())
                                    .receivedType(1)
                                    .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                    .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                    .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                    .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                    .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                    .build()));
                            updateSegmentDetailById(newRecord);
	                        saveSegmentEmp(newRecord.getSegmentId(), protect.getSalerId());
                        }else{
                            // 需要查一下之前是否已经分配给该商务 如果已经分配给改商务 当条数据需要删除
                            LambdaQueryWrapper<SegmentDetail> segmentDetailProtectQueryWrapper = new LambdaQueryWrapper<>();
                            segmentDetailProtectQueryWrapper.eq(SegmentDetail::getSegmentId,segmentAssignmentBusinessDto.getSegmentId());
                            segmentDetailProtectQueryWrapper.in(SegmentDetail::getCustomerId,segmentAssignmentBusinessDto.getCustomerIds());
                            segmentDetailProtectQueryWrapper.eq(SegmentDetail::getDealStatus,0);
                            segmentDetailProtectQueryWrapper.eq(SegmentDetail::getSalerId,protect.getSalerId());
                            List<SegmentDetail> segmentDetailProtectList = segmentDetailService.list(segmentDetailProtectQueryWrapper);
                            if (CollectionUtils.isNotEmpty(segmentDetailProtectList)){
                                newRecord.setId(segmentDetail.getId());
                                newRecord.setDeleteFlag(1);
                                newRecord.setDeleteReason("按照保护关系重新分配，该客户已经存在该商务");
                                updateSegmentDetailById(newRecord);
	                            saveSegmentEmp(newRecord.getSegmentId(), protect.getSalerId());
                            }else {
                                newRecord.setCreateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                newRecord.setCreateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                newRecord.setCreateTime(current);
                                rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                        .segmentId(newRecord.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                        .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                        .customerId(newRecord.getCustomerId())
                                        .distributeTime(current)
                                        .receivedId(newRecord.getSalerId())
                                        .receivedType(1)
                                        .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                        .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                        .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                        .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                        .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                        .build()));
                                segmentDetailService.save(newRecord);
	                            saveSegmentEmp(newRecord.getSegmentId(), protect.getSalerId());
                            }
                        }
                    }else if (Objects.equals(protect.getStatus(),2)){
                            // 总监待分配
                            SegmentDetail segmentDetail = segmentDetails.get(0);
                            SegmentDetail newRecord = cloneSegmentDetailWithUpdates(segmentDetail, detail -> {
                                detail.setAreaDistributeTime(current);
                                detail.setSubDistributeTime(current);
                                detail.setBuDistributeTime(null);
                                detail.setDeptDistributeTime(null);
                                detail.setAreaId(protect.getAreaId());
                                detail.setSubId(protect.getSubcompanyId());
                                detail.setBuId(null);
                                detail.setDeptId(null);
                                detail.setSalerId(null);
                                detail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                detail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                detail.setUpdateTime(current);
                                detail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                            });
                            List<String> assignedSubIds = segmentDetails.stream().map(SegmentDetail::getSubId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(assignedSubIds) ||assignedSubIds.contains(protect.getSubcompanyId()) ){
                                newRecord.setId(segmentDetail.getId());
                                rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                        .segmentId(newRecord.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                        .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                        .customerId(newRecord.getCustomerId())
                                        .distributeTime(current)
                                        .receivedOrgId(newRecord.getSubId())
                                        .receivedType(2)
                                        .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                        .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                        .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                        .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                        .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                        .build()));
                                updateSegmentDetailById(newRecord);
                            }else {
                                // 需要查一下之前是否已经分配给该商务 如果已经分配给改商务 当条数据需要删除
                                LambdaQueryWrapper<SegmentDetail> segmentDetailProtectQueryWrapper = new LambdaQueryWrapper<>();
                                segmentDetailProtectQueryWrapper.eq(SegmentDetail::getSegmentId,segmentAssignmentBusinessDto.getSegmentId());
                                segmentDetailProtectQueryWrapper.in(SegmentDetail::getCustomerId,segmentAssignmentBusinessDto.getCustomerIds());
                                segmentDetailProtectQueryWrapper.eq(SegmentDetail::getDealStatus,0);
                                segmentDetailProtectQueryWrapper.eq(SegmentDetail::getSubId,protect.getSubcompanyId());
                                List<SegmentDetail> segmentDetailProtectList = segmentDetailService.list(segmentDetailProtectQueryWrapper);
                                if (CollectionUtils.isNotEmpty(segmentDetailProtectList)){
                                    newRecord.setId(segmentDetail.getId());
                                    newRecord.setDeleteFlag(1);
                                    newRecord.setDeleteReason("按照保护关系重新分配，该客户已经存在该分司");
                                    updateSegmentDetailById(newRecord);
                                }else {
                                    newRecord.setCreateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                    newRecord.setCreateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                    newRecord.setCreateTime(current);
                                    rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                            .segmentId(newRecord.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                            .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                            .customerId(newRecord.getCustomerId())
                                            .distributeTime(current)
                                            .receivedOrgId(newRecord.getSubId())
                                            .receivedType(2)
                                            .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                            .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                            .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                            .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                            .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                            .build()));
                                    segmentDetailService.save(newRecord);
                                }
                            }
                    }else if (Objects.equals(protect.getStatus(),3)){
                            // 经理待分配
                            SegmentDetail segmentDetail = segmentDetails.get(0);
                            SegmentDetail newRecord = cloneSegmentDetailWithUpdates(segmentDetail, detail -> {
                                detail.setAreaDistributeTime(current);
                                detail.setSubDistributeTime(current);
                                detail.setBuDistributeTime(current);
                                detail.setDeptDistributeTime(current);
                                detail.setAreaId(protect.getAreaId());
                                detail.setSubId(protect.getSubcompanyId());
                                detail.setBuId(protect.getBuId());
                                detail.setDeptId(protect.getBussdeptId());
                                detail.setSalerId(null);
                                detail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                detail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                detail.setUpdateTime(current);
                                detail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                            });
                            List<String> assignedSubIds = segmentDetails.stream().map(SegmentDetail::getSubId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(assignedSubIds) ||assignedSubIds.contains(protect.getSubcompanyId()) ){
                                newRecord.setId(segmentDetail.getId());
                                rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                        .segmentId(newRecord.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                        .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                        .customerId(newRecord.getCustomerId())
                                        .distributeTime(current)
                                        .receivedOrgId(newRecord.getDeptId())
                                        .receivedType(4)
                                        .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                        .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                        .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                        .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                        .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                        .build()));
                                updateSegmentDetailById(newRecord);
                            }else {
                                // 需要查一下之前是否已经分配给该商务 如果已经分配给改商务 当条数据需要删除
                                LambdaQueryWrapper<SegmentDetail> segmentDetailProtectQueryWrapper = new LambdaQueryWrapper<>();
                                segmentDetailProtectQueryWrapper.eq(SegmentDetail::getSegmentId,segmentAssignmentBusinessDto.getSegmentId());
                                segmentDetailProtectQueryWrapper.in(SegmentDetail::getCustomerId,segmentAssignmentBusinessDto.getCustomerIds());
                                segmentDetailProtectQueryWrapper.eq(SegmentDetail::getDealStatus,0);
                                segmentDetailProtectQueryWrapper.eq(SegmentDetail::getDeptId,protect.getBussdeptId());
                                List<SegmentDetail> segmentDetailProtectList = segmentDetailService.list(segmentDetailProtectQueryWrapper);
                                if (CollectionUtils.isNotEmpty(segmentDetailProtectList)){
                                    newRecord.setId(segmentDetail.getId());
                                    newRecord.setDeleteFlag(1);
                                    newRecord.setDeleteReason("按照保护关系重新分配，该客户已经存在该部门");
                                    updateSegmentDetailById(newRecord);
                                }else {
                                    newRecord.setCreateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                    newRecord.setCreateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                    newRecord.setCreateTime(current);
                                    rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                            .segmentId(newRecord.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                            .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                            .customerId(newRecord.getCustomerId())
                                            .distributeTime(current)
                                            .receivedOrgId(newRecord.getDeptId())
                                            .receivedType(4)
                                            .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                            .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                            .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                            .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                            .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                            .build()));
                                    segmentDetailService.save(newRecord);
                                }
                            }
                    }else if (Objects.equals(protect.getStatus(),5)){
                        // 事业部待分配
                            SegmentDetail segmentDetail = segmentDetails.get(0);
                            SegmentDetail newRecord = cloneSegmentDetailWithUpdates(segmentDetail, detail -> {
                                detail.setAreaDistributeTime(current);
                                detail.setSubDistributeTime(current);
                                detail.setBuDistributeTime(current);
                                detail.setDeptDistributeTime(null);
                                detail.setAreaId(protect.getAreaId());
                                detail.setSubId(protect.getSubcompanyId());
                                detail.setBuId(protect.getBuId());
                                detail.setDeptId(null);
                                detail.setSalerId(null);
                                detail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                detail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                detail.setUpdateTime(current);
                                detail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                            });
                            List<String> assignedSubIds = segmentDetails.stream().map(SegmentDetail::getSubId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(assignedSubIds) ||assignedSubIds.contains(protect.getSubcompanyId()) ){
                                newRecord.setId(segmentDetail.getId());
                                rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                        .segmentId(newRecord.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                        .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                        .customerId(newRecord.getCustomerId())
                                        .distributeTime(current)
                                        .receivedOrgId(newRecord.getBuId())
                                        .receivedType(3)
                                        .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                        .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                        .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                        .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                        .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                        .build()));
                                updateSegmentDetailById(newRecord);
                            }else {
                                // 需要查一下之前是否已经分配给该商务 如果已经分配给改商务 当条数据需要删除
                                LambdaQueryWrapper<SegmentDetail> segmentDetailProtectQueryWrapper = new LambdaQueryWrapper<>();
                                segmentDetailProtectQueryWrapper.eq(SegmentDetail::getSegmentId,segmentAssignmentBusinessDto.getSegmentId());
                                segmentDetailProtectQueryWrapper.in(SegmentDetail::getCustomerId,segmentAssignmentBusinessDto.getCustomerIds());
                                segmentDetailProtectQueryWrapper.eq(SegmentDetail::getDealStatus,0);
                                segmentDetailProtectQueryWrapper.eq(SegmentDetail::getBuId,protect.getBuId());
                                List<SegmentDetail> segmentDetailProtectList = segmentDetailService.list(segmentDetailProtectQueryWrapper);
                                if (CollectionUtils.isNotEmpty(segmentDetailProtectList)){
                                    newRecord.setId(segmentDetail.getId());
                                    newRecord.setDeleteFlag(1);
                                    newRecord.setDeleteReason("按照保护关系重新分配，该客户已经存在该事业部");
                                    updateSegmentDetailById(newRecord);
                                }else {
                                    newRecord.setCreateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                    newRecord.setCreateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                    newRecord.setCreateTime(current);
                                    rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                            .segmentId(newRecord.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                            .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                            .customerId(newRecord.getCustomerId())
                                            .distributeTime(current)
                                            .receivedOrgId(newRecord.getBuId())
                                            .receivedType(3)
                                            .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                            .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                            .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                            .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                            .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                            .build()));
                                    segmentDetailService.save(newRecord);
                                }
                            }
                    }
                }
            });
        }else {
            /**
             * key 为type , value 为 count
             * key:
             *  1 -> 区总分配分司
             *  2 -> 分公司总监分配商务
             *  3 -> 分公司总监分配部门
             *  4 -> 分公司总监分配事业部
             *  5 -> 事业部总监 分配 商务
             *  6 -> 事业部总监 分配 部门
             *  7 -> 经理或组长 分配 商务
             */
            Map<Integer,Integer> countMap = new HashMap<>();
            switch (segmentAssignmentBusinessDto.getAssignmentRoleType()) {
                case 1:
                    Map<String,Integer> subCountMap = new HashMap<>();
                    //1:区域总监分配 给分司
                    for (Map.Entry<String, List<SegmentDetail>> entry : segmentDetailGroupByCustomerIdMap.entrySet()) {
                        String customerId = entry.getKey();
                        List<SegmentDetail> value = entry.getValue();
                        if (value.isEmpty()) {
                            log.warn("客户[{}]无分配记录，跳过处理", customerId);
                            continue;
                        }
                        List<String> subIdList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(segmentAssignmentBusinessDto.getSubIds())){
                            subIdList.addAll(segmentAssignmentBusinessDto.getSubIds());
                        }
                        if (StringUtils.isNotBlank(segmentAssignmentBusinessDto.getSubId())){
                            subIdList.add(segmentAssignmentBusinessDto.getSubId());
                        }
                        List<String> collect = subIdList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(collect)){
                            log.warn("客户[{}]无分配记录，跳过处理", customerId);
                            continue;
                        }
                        // 该客户已经分配的分司列表
                        List<String> assignedSubIds = value.stream().map(SegmentDetail::getSubId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        for (String subId : collect) {
                            //redis 判断是否分配过, key: segmentId+customerId+subId
                            boolean exists =assignedSubIds.contains(subId);
                            if (exists || getSegmentDistributeSubCache(segmentId,subId,customerId)) {
                                log.info("客户[{}]已分配分司[{}]，跳过分配", customerId, subId);
                            } else {
                                SegmentDetail segmentDetail = value.get(0);
                                //之前没有分过分司,第一次分的时候设置分司
                                if (CollectionUtils.isEmpty(assignedSubIds)) {
                                    segmentDetail.setAreaDistributeTime(current);
                                    segmentDetail.setSubDistributeTime(null);
                                    segmentDetail.setBuDistributeTime(null);
                                    segmentDetail.setDeptDistributeTime(null);
                                    segmentDetail.setSubId(subId);
                                    segmentDetail.setBuId(null);
                                    segmentDetail.setDeptId(null);
                                    segmentDetail.setSalerId(null);
                                    segmentDetail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                    segmentDetail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                    segmentDetail.setUpdateTime(current);
                                    segmentDetail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                                    rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                            .segmentId(segmentDetail.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                            .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                            .customerId(segmentDetail.getCustomerId())
                                            .distributeTime(current)
                                            .receivedOrgId(segmentDetail.getSubId())
                                            .receivedType(2)
                                            .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                            .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                            .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                            .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                            .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                            .build()));
                                    updateSegmentDetailById(segmentDetail);
                                    //分配到的公司ID列表
                                    assignedSubIds.add(subId);
                                    // 写redis,加锁
                                    setSegmentDistributeSubCache(segmentId,subId,customerId);
                                } else {
                                    SegmentDetail newRecord = cloneSegmentDetailWithUpdates(segmentDetail, detail -> {
                                        detail.setAreaDistributeTime(current);
                                        detail.setSubDistributeTime(null);
                                        detail.setBuDistributeTime(null);
                                        detail.setDeptDistributeTime(null);
                                        detail.setSubId(subId);
                                        detail.setBuId(null);
                                        detail.setDeptId(null);
                                        detail.setSalerId(null);
                                        detail.setCreateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                        detail.setCreateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                        detail.setCreateTime(current);
                                        detail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                        detail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                        detail.setUpdateTime(current);
                                        detail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                                    });
                                    rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                            .segmentId(newRecord.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                            .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                            .customerId(newRecord.getCustomerId())
                                            .distributeTime(current)
                                            .receivedOrgId(newRecord.getSubId())
                                            .receivedType(2)
                                            .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                            .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                            .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                            .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                            .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                            .build()));
                                    segmentDetailService.save(newRecord);
                                    //分配到的公司ID列表
                                    assignedSubIds.add(subId);
                                    // 写redis,加锁
                                    setSegmentDistributeSubCache(segmentId,subId,customerId);
                                }
                                subCountMap.merge(subId, 1, Integer::sum);
                                EmployeeVo orgLeader = employeeAppService.findOrgLeader(subId);
                                if (orgLeader != null) {
                                    saveSegmentEmp(segmentAssignmentBusinessDto.getSegmentId(), orgLeader.getId());
                                }
                            }
                        }
                    }
                    for (Map.Entry<String, Integer> entry : subCountMap.entrySet()) {
                        Map<Integer, Integer> countMaps = new HashMap<>();
                        countMaps.put(SegmentSendMessageTypeEnum.AREA_ASSIGN_SUB.getCode(), entry.getValue());
                        sendSegmentWxMessage(countMaps, segmentAssignmentBusinessDto.getSegmentId(), entry.getKey(), null, null, null);
                    }
                    break;
                case 2:
                    //2:分公司总监分配 分配给商务,部门,事业部
                    if (StringUtils.isNotBlank(segmentAssignmentBusinessDto.getSalerId())) {
                        segmentDetailGroupByCustomerIdMap.forEach((customerId, segmentDetails) -> {
                            if (segmentDetails.isEmpty()) return;
                            boolean exists = segmentDetails.stream()
                                    .anyMatch(item -> Objects.equals(item.getSalerId(), segmentAssignmentBusinessDto.getSalerId()));
                            if (exists) {
                                log.info("客户[{}]已分配商务[{}]，跳过分配", customerId, segmentAssignmentBusinessDto.getSalerId());
                            } else {
                                SegmentDetail segmentDetail = segmentDetails.get(0);
                                segmentDetail.setSubDistributeTime(current);
                                segmentDetail.setBuDistributeTime(current);
                                segmentDetail.setDeptDistributeTime(current);
                                segmentDetail.setBuId(segmentAssignmentBusinessDto.getBuId());
                                segmentDetail.setDeptId(segmentAssignmentBusinessDto.getDeptId());
                                segmentDetail.setSalerId(segmentAssignmentBusinessDto.getSalerId());
                                segmentDetail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                segmentDetail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                segmentDetail.setUpdateTime(current);
                                segmentDetail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                                rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                        .segmentId(segmentDetail.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                        .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                        .customerId(segmentDetail.getCustomerId())
                                        .distributeTime(current)
                                        .receivedId(segmentDetail.getSalerId())
                                        .receivedType(1)
                                        .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                        .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                        .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                        .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                        .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                        .build()));
                                updateSegmentDetailById(segmentDetail);
                                countMap.merge(SegmentSendMessageTypeEnum.SUB_ASSIGN_SALER.getCode(), 1, Integer::sum);
                                saveSegmentEmp(segmentAssignmentBusinessDto.getSegmentId(), segmentAssignmentBusinessDto.getSalerId());
                            }
                        });
                    } else if (StringUtils.isNotBlank(segmentAssignmentBusinessDto.getDeptId())) {
                        segmentDetailGroupByCustomerIdMap.forEach((customerId, segmentDetails) -> {
                            if (segmentDetails.isEmpty()) return;
                            boolean exists = segmentDetails.stream()
                                    .anyMatch(item -> Objects.equals(item.getDeptId(), segmentAssignmentBusinessDto.getDeptId()));
                            if (exists) {
                                log.info("客户[{}]已分配部门[{}]，跳过分配", customerId, segmentAssignmentBusinessDto.getDeptId());
                            } else {
                                SegmentDetail segmentDetail = segmentDetails.get(0);
                                segmentDetail.setSubDistributeTime(current);
                                segmentDetail.setBuDistributeTime(current);
                                segmentDetail.setDeptDistributeTime(null);
                                segmentDetail.setBuId(segmentAssignmentBusinessDto.getBuId());
                                segmentDetail.setDeptId(segmentAssignmentBusinessDto.getDeptId());
                                segmentDetail.setSalerId(null);
                                segmentDetail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                segmentDetail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                segmentDetail.setUpdateTime(current);
                                segmentDetail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                                rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                        .segmentId(segmentDetail.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                        .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                        .customerId(segmentDetail.getCustomerId())
                                        .distributeTime(current)
                                        .receivedOrgId(segmentDetail.getDeptId())
                                        .receivedType(4)
                                        .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                        .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                        .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                        .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                        .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                        .build()));
                                updateSegmentDetailById(segmentDetail);
                                countMap.merge(SegmentSendMessageTypeEnum.SUB_ASSIGN_DEPT.getCode(), 1, Integer::sum);
                                EmployeeVo orgLeader = employeeAppService.findOrgLeader(segmentAssignmentBusinessDto.getDeptId());
                                if (orgLeader != null) {
                                    saveSegmentEmp(segmentAssignmentBusinessDto.getSegmentId(), orgLeader.getId());
                                }
                            }
                        });
                    } else if (StringUtils.isNotBlank(segmentAssignmentBusinessDto.getBuId())) {
                        segmentDetailGroupByCustomerIdMap.forEach((customerId, segmentDetails) -> {
                            if (segmentDetails.isEmpty()) return;
                            boolean exists = segmentDetails.stream()
                                    .anyMatch(item -> Objects.equals(item.getBuId(), segmentAssignmentBusinessDto.getBuId()));
                            if (exists) {
                                log.info("客户[{}]已分配事业部[{}]，跳过分配", customerId, segmentAssignmentBusinessDto.getBuId());
                            } else {
                                SegmentDetail segmentDetail = segmentDetails.get(0);
                                segmentDetail.setSubDistributeTime(current);
                                segmentDetail.setBuDistributeTime(null);
                                segmentDetail.setDeptDistributeTime(null);
                                segmentDetail.setBuId(segmentAssignmentBusinessDto.getBuId());
                                segmentDetail.setDeptId(null);
                                segmentDetail.setSalerId(null);
                                segmentDetail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                segmentDetail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                segmentDetail.setUpdateTime(current);
                                segmentDetail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                                rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                        .segmentId(segmentDetail.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                        .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                        .customerId(segmentDetail.getCustomerId())
                                        .distributeTime(current)
                                        .receivedOrgId(segmentDetail.getBuId())
                                        .receivedType(3)
                                        .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                        .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                        .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                        .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                        .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                        .build()));
                                updateSegmentDetailById(segmentDetail);
                                countMap.merge(SegmentSendMessageTypeEnum.SUB_ASSIGN_BU.getCode(), 1, Integer::sum);
                                EmployeeVo orgLeader = employeeAppService.findOrgLeader(segmentAssignmentBusinessDto.getBuId());
                                if (orgLeader != null) {
                                    saveSegmentEmp(segmentAssignmentBusinessDto.getSegmentId(), orgLeader.getId());
                                }
                            }
                        });
                    }
                    break;
                case 3:
                    // 3:事业部总监分配
                    if (StringUtils.isNotBlank(segmentAssignmentBusinessDto.getSalerId())) {
                        segmentDetailGroupByCustomerIdMap.forEach((customerId, segmentDetails) -> {
                            if (segmentDetails.isEmpty()) return;
                            boolean exists = segmentDetails.stream()
                                    .anyMatch(item -> Objects.equals(item.getSalerId(), segmentAssignmentBusinessDto.getSalerId()));
                            if (exists) {
                                log.info("客户[{}]已分配商务[{}]，跳过分配", customerId, segmentAssignmentBusinessDto.getSalerId());
                            } else {
                                SegmentDetail segmentDetail = segmentDetails.get(0);
                                segmentDetail.setBuDistributeTime(current);
                                segmentDetail.setDeptDistributeTime(current);
                                segmentDetail.setDeptId(segmentAssignmentBusinessDto.getDeptId());
                                segmentDetail.setSalerId(segmentAssignmentBusinessDto.getSalerId());
                                segmentDetail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                segmentDetail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                segmentDetail.setUpdateTime(current);
                                segmentDetail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                                rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                        .segmentId(segmentDetail.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                        .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                        .customerId(segmentDetail.getCustomerId())
                                        .distributeTime(current)
                                        .receivedId(segmentDetail.getSalerId())
                                        .receivedType(1)
                                        .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                        .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                        .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                        .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                        .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                        .build()));
                                updateSegmentDetailById(segmentDetail);
                                countMap.merge(SegmentSendMessageTypeEnum.BU_ASSIGN_SALER.getCode(), 1, Integer::sum);
                                saveSegmentEmp(segmentAssignmentBusinessDto.getSegmentId(), segmentAssignmentBusinessDto.getSalerId());
                            }
                        });
                    } else if (StringUtils.isNotBlank(segmentAssignmentBusinessDto.getDeptId())) {
                        segmentDetailGroupByCustomerIdMap.forEach((customerId, segmentDetails) -> {
                            if (segmentDetails.isEmpty()) return;
                            boolean exists = segmentDetails.stream()
                                    .anyMatch(item -> Objects.equals(item.getDeptId(), segmentAssignmentBusinessDto.getDeptId()));
                            if (exists) {
                                log.info("客户[{}]已分配部门[{}]，跳过分配", customerId, segmentAssignmentBusinessDto.getDeptId());
                            } else {
                                SegmentDetail segmentDetail = segmentDetails.get(0);
                                segmentDetail.setBuDistributeTime(current);
                                segmentDetail.setDeptDistributeTime(null);
                                segmentDetail.setDeptId(segmentAssignmentBusinessDto.getDeptId());
                                segmentDetail.setSalerId(null);
                                segmentDetail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                segmentDetail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                segmentDetail.setUpdateTime(current);
                                segmentDetail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                                rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                        .segmentId(segmentDetail.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                        .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                        .customerId(segmentDetail.getCustomerId())
                                        .distributeTime(current)
                                        .receivedOrgId(segmentDetail.getDeptId())
                                        .receivedType(4)
                                        .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                        .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                        .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                        .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                        .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                        .build()));
                                updateSegmentDetailById(segmentDetail);
                                countMap.merge(SegmentSendMessageTypeEnum.BU_ASSIGN_DEPT.getCode(), 1, Integer::sum);
                                EmployeeVo orgLeader = employeeAppService.findOrgLeader(segmentAssignmentBusinessDto.getDeptId());
                                if (orgLeader != null) {
                                    saveSegmentEmp(segmentAssignmentBusinessDto.getSegmentId(), orgLeader.getId());
                                }
                            }
                        });
                    }
                    break;
                case 4:
                    // 部门经理/小组组长分配  分配给商务
                    if (StringUtils.isNotBlank(segmentAssignmentBusinessDto.getSalerId())) {
                        segmentDetailGroupByCustomerIdMap.forEach((customerId, segmentDetails) -> {
                            if (segmentDetails.isEmpty()) return;
                            boolean exists = segmentDetails.stream()
                                    .anyMatch(item -> Objects.equals(item.getSalerId(), segmentAssignmentBusinessDto.getSalerId()));
                            if (exists) {
                                log.info("客户[{}]已分配商务[{}]，跳过分配", customerId, segmentAssignmentBusinessDto.getSalerId());
                            } else {
                                SegmentDetail segmentDetail = segmentDetails.get(0);
                                segmentDetail.setDeptDistributeTime(current);
                                segmentDetail.setSalerId(segmentAssignmentBusinessDto.getSalerId());
                                segmentDetail.setUpdateId(segmentAssignmentBusinessDto.getLoginEmployeeId());
                                segmentDetail.setUpdateName(segmentAssignmentBusinessDto.getLoginEmployeeName());
                                segmentDetail.setUpdateTime(current);
                                segmentDetail.setAssignmentType(segmentAssignmentBusinessDto.getAssignmentType());
                                rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                                        .segmentId(segmentDetail.getSegmentId()).assignmentType(segmentAssignmentBusinessDto.getAssignmentType())
                                        .operatorId(segmentAssignmentBusinessDto.getLoginEmployeeId())
                                        .customerId(segmentDetail.getCustomerId())
                                        .distributeTime(current)
                                        .receivedId(segmentDetail.getSalerId())
                                        .receivedType(1)
                                        .loginOrgId(segmentAssignmentBusinessDto.getLoginOrgId()).loginOrgName(segmentAssignmentBusinessDto.getLoginOrgName())
                                        .loginBuId(segmentAssignmentBusinessDto.getLoginBuId()).loginBuName(segmentAssignmentBusinessDto.getLoginBuName())
                                        .loginSubId(segmentAssignmentBusinessDto.getLoginSubId()).loginSubName(segmentAssignmentBusinessDto.getLoginSubName())
                                        .loginAreaId(segmentAssignmentBusinessDto.getLoginAreaId()).loginAreaName(segmentAssignmentBusinessDto.getLoginAreaName())
                                        .loginPosition(segmentAssignmentBusinessDto.getLoginPosition())
                                        .build()));
                                updateSegmentDetailById(segmentDetail);
                                countMap.merge(SegmentSendMessageTypeEnum.DEPT_ASSIGN_SALER.getCode(), 1, Integer::sum);
	                            saveSegmentEmp(segmentAssignmentBusinessDto.getSegmentId(), segmentAssignmentBusinessDto.getSalerId());
                            }
                        });
                    }
                    break;
                default:
                    break;
            }
            sendSegmentWxMessage(countMap,segmentAssignmentBusinessDto.getSegmentId(),segmentAssignmentBusinessDto.getSubId(),segmentAssignmentBusinessDto.getBuId(),segmentAssignmentBusinessDto.getDeptId(),segmentAssignmentBusinessDto.getSalerId());
        }
        return Optional.empty();
    }

	/**
	 * 对非全员可见的分群，给群员工加权限
	 * @param segmentId 分群id
	 * @param empId 员工id
	 */
	private void saveSegmentEmp(String segmentId, String empId) {
		try {
			Segment segment = segmentService.lambdaQuery().eq(Segment::getSegmentId, segmentId).eq(Segment::getDeleteFlag, YesOrNoEnum.NO.getCode()).one();
			if (segment == null) {
				log.error("segment_emp保存员工信息,segmentId不存在,segmentId={}",segmentId);
				return;
			}
			if (Objects.equals(segment.getIsVisibleAll(), YesOrNoEnum.YES.getCode())) {
				log.info("全员可见，不保存员工信息");
				return;
			}
			Long count = segmentEmpService.lambdaQuery().eq(SegmentEmp::getSegmentId, segmentId).eq(SegmentEmp::getEmpId, empId).count();
			if (count > 0) {
				log.info("segment_emp保存员工信息,segmentId={},empId={},已存在",segmentId,empId);
				return;
			}
			SegmentEmp segmentEmp = new SegmentEmp();
			segmentEmp.setSegmentId(segmentId);
			segmentEmp.setEmpId(empId);
			segmentEmpService.save(segmentEmp);
		} catch (Exception e) {
			log.error("保存segmentEmp失败: {}", e.getMessage());
		}
	}


	private void sendSegmentWxMessage(Map<Integer, Integer> countMap,String segmentId,String subId,String buId,String deptId,String salerId) {

        try {
            if(countMap == null || segmentId == null) {
                return;
            }

            Segment segment = segmentService.lambdaQuery().eq(Segment::getSegmentId, segmentId).last("limit 1").one();
            if (segment == null) {
                log.error("发送企业微信消息的时候，未找到分群信息,segmentId={}",segmentId);
                return;
            }

            Set<Map.Entry<Integer, Integer>> entries = countMap.entrySet();
            for (Map.Entry<Integer, Integer> entry : entries) {

                Integer key = entry.getKey();
                Integer value = entry.getValue();

                String content = SegmentSendMessageTypeEnum.getContentByCode(key);

                if (StringUtils.isNotBlank(content)) {

                    String userId = null;
                    if (SegmentSendMessageTypeEnum.AREA_ASSIGN_SUB.getCode().equals(key)) {
                        Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(subId);
                        if (orgLeader.isPresent()) {
                            userId = orgLeader.get().getId();
                        }
                    }else if (SegmentSendMessageTypeEnum.SUB_ASSIGN_SALER.getCode().equals(key)) {
                        userId = salerId;
                    }else if (SegmentSendMessageTypeEnum.SUB_ASSIGN_DEPT.getCode().equals(key)) {
                        Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(deptId);
                        if (orgLeader.isPresent()) {
                            userId = orgLeader.get().getId();
                        }
                    }else if (SegmentSendMessageTypeEnum.SUB_ASSIGN_BU.getCode().equals(key)) {
                        Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(buId);
                        if (orgLeader.isPresent()) {
                            userId = orgLeader.get().getId();
                        }
                    }else if (SegmentSendMessageTypeEnum.BU_ASSIGN_SALER.getCode().equals(key)) {
                        userId = salerId;
                    }else if (SegmentSendMessageTypeEnum.BU_ASSIGN_DEPT.getCode().equals(key)) {
                        Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(deptId);
                        if (orgLeader.isPresent()) {
                            userId = orgLeader.get().getId();
                        }
                    }else if (SegmentSendMessageTypeEnum.DEPT_ASSIGN_SALER.getCode().equals(key)) {
                        userId = salerId;
                    }

                    String message = MessageFormat.format(content, segment.getSegmentName(), value);
                    sendWxMessage.sendMessage(userId,message);
                }

            }
        }catch (Exception e){
            log.error("分群下发发送企业微信消息失败,countMap={},segmentId={},subId={},buId={},deptId={},salerId={}",countMap,segmentId,subId,buId,deptId,salerId);
        }
    }

    private SegmentDetail cloneSegmentDetailWithUpdates(SegmentDetail source, Consumer<SegmentDetail> updater) {
        SegmentDetail target = new SegmentDetail();
        BeanUtils.copyProperties(source,target);
        target.setId(null);
        updater.accept(target);
        return target;
    }

	/**
	 * 移除分群
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean removeGroup(RemoveGroupSegmentBusinessDto removeGroupSegmentBusinessDto) {
		EmployeeInfoBusinessDto currentUser = employeeInfoBusiness.getEmployeeInfoByEmpId(removeGroupSegmentBusinessDto.getLoginEmployeeId());
		if (currentUser == null) {
			throw new ApiException(CodeMessageEnum.NOT_LOGIN);
		}
		try {
			log.info("{}开始移除分群:{}",currentUser.getName(), removeGroupSegmentBusinessDto.getSegmentId());
			segmentService.lambdaUpdate()
				.eq(Objects.nonNull(removeGroupSegmentBusinessDto.getId()), Segment::getId, removeGroupSegmentBusinessDto.getId())
				.eq(Segment::getSegmentId, removeGroupSegmentBusinessDto.getSegmentId())
				.set(Segment::getDeleteFlag, YesOrNoEnum.YES.getCode())
				.update();
			segmentDetailService.lambdaUpdate()
				.eq(SegmentDetail::getSegmentId, removeGroupSegmentBusinessDto.getSegmentId())
				.set(SegmentDetail::getDeleteFlag, YesOrNoEnum.YES.getCode())
				.update();
			return true;
		} catch (Exception e) {
			log.warn("{}移除分群异常: {}", JSON.toJSONString(removeGroupSegmentBusinessDto), e.getMessage());
			throw new ApiException(CodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
		}
	}


	/**
	 * 拉群，segment_id可修改
	 */
	public Boolean pullGroup(PullGroupSegmentBusinessDto pullGroupSegmentBusinessDto) {
		EmployeeInfoBusinessDto currentUser = employeeInfoBusiness.getEmployeeInfoByEmpId(pullGroupSegmentBusinessDto.getLoginEmployeeId());
		if (currentUser == null) {
			throw new ApiException(CodeMessageEnum.NOT_LOGIN);
		}
		log.info("{}-{}-{} 开始分群: {}", currentUser.getId(),currentUser.getName(), currentUser.getPosition(), JSON.toJSONString(pullGroupSegmentBusinessDto));
		return pullGroupSaveOrUpdate(pullGroupSegmentBusinessDto, currentUser);
	}


	/**
	 * 新增分群 或 编辑分群
	 */
	private Boolean pullGroupSaveOrUpdate(PullGroupSegmentBusinessDto pullGroupSegmentBusinessDto, EmployeeInfoBusinessDto currentUser) {
		try {
			if (pullGroupSegmentBusinessDto.getId() == null) { // insert
				Segment segment = new Segment();
				segment.setSegmentId(pullGroupSegmentBusinessDto.getSegmentId());
				segment.setSegmentName(pullGroupSegmentBusinessDto.getSegmentName());
				segment.setSegmentDesc(pullGroupSegmentBusinessDto.getSegmentDesc());
				Date segmentBeginTime = DateUtils.toDate(pullGroupSegmentBusinessDto.getSegmentStartDate());
				Date segmentEndTime = DateUtils.toDate(pullGroupSegmentBusinessDto.getSegmentEndDate());
				segment.setSegmentBeginTime(segmentBeginTime);
				segment.setSegmentEndTime(segmentEndTime);
				segment.setCreateId(currentUser.getId());
				segment.setCreateName(currentUser.getName());
				return segmentService.save(segment);
			} else { // update by id
				Segment byId = segmentService.getById(pullGroupSegmentBusinessDto.getId());
				byId.setSegmentId(pullGroupSegmentBusinessDto.getSegmentId());
				byId.setSegmentName(pullGroupSegmentBusinessDto.getSegmentName());
				byId.setSegmentDesc(pullGroupSegmentBusinessDto.getSegmentDesc());
				Date segmentBeginTime = DateUtils.toDate(pullGroupSegmentBusinessDto.getSegmentStartDate());
				Date segmentEndTime = DateUtils.toDate(pullGroupSegmentBusinessDto.getSegmentEndDate());
				byId.setSegmentBeginTime(segmentBeginTime);
				byId.setSegmentEndTime(segmentEndTime);
				byId.setUpdateId(currentUser.getId());
				byId.setUpdateName(currentUser.getName());
				byId.setUpdateTime(new Date());
				return segmentService.updateById(byId);
			}
		} catch (Exception e) {
			log.error("分群下发异常: {}", e.getMessage());
		}
		return false;
	}

	/**
	 * 总部线索数据下发-总部分群列表
	 */
	public Page<ClueSegmentBusinessView> cluePage(ClueSegmentPageBusinessDto clueSegmentBusinessDto) {
		EmployeeInfoBusinessDto currentUser = employeeInfoBusiness.getEmployeeInfoByEmpId(clueSegmentBusinessDto.getLoginEmployeeId());
		if (currentUser == null) {
			throw new ApiException(CodeMessageEnum.NOT_LOGIN);
		}
		boolean isCenterRole = Objects.equals(currentUser.getIsZbReport(), YesOrNoEnum.YES.getCode());
		if (!isCenterRole) {
			return new Page<>(clueSegmentBusinessDto.getPageNum(), clueSegmentBusinessDto.getPageSize());
		}
		Page<Segment> segmentPage = Page.of(clueSegmentBusinessDto.getPageNum(), clueSegmentBusinessDto.getPageSize());
		Page<Segment> page = segmentService.lambdaQuery().eq(Segment::getDeleteFlag, YesOrNoEnum.NO.getCode()).page(segmentPage);
		if (Objects.nonNull(page)) {
			if (CollectionUtils.isNotEmpty(page.getRecords())) {
				List<ClueSegmentBusinessView> views = BeanCopyUtils.convertToVoList(page.getRecords(), ClueSegmentBusinessView.class);
                if (CollectionUtils.isNotEmpty(views)) {
                    for (ClueSegmentBusinessView view : views) {
                        SegmentStatusEnum segmentStatusEnum = SegmentStatusEnum.of(view.getSegmentStatus());
                        if (segmentStatusEnum != null) {
                            view.setSegmentStatusName(segmentStatusEnum.getValue());
                        }
                    }
                }
				Page<ClueSegmentBusinessView> pageResult = new Page<>();
				BeanUtil.copyProperties(page, pageResult);
				pageResult.setRecords(views);
				return pageResult;
			}
		}
		return new Page<>(clueSegmentBusinessDto.getPageNum(), clueSegmentBusinessDto.getPageSize());
	}


    /***
     * 初始化 segmentDetail
     * @param segmentDetail
     * <AUTHOR>
     * @date 2025/2/27 21:40
     * @version 1.0.0
     * @return java.lang.Boolean
    **/
    public Boolean segmentDetailCreate(SegmentDetail segmentDetail) {
        List<String> distirbuteAreaList = new ArrayList<>();
        String segmentId = segmentDetail.getSegmentId();
        String customerId = segmentDetail.getCustomerId();
        Segment detail = detail(segmentId);
        if (Objects.isNull(detail)){
            log.error("分群不存在,segmentId:{}", segmentDetail.getCustomerId());
            return false;
        }
        //获取客户信息
        Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(customerId);
        if(!customerData.isPresent()) {
            log.error("客户不存在,custId:{}", segmentDetail.getCustomerId());
            return false;
        }

        // 获取行业
        SkbParamsReq skbParamsReq = new SkbParamsReq();
        skbParamsReq.setEntName(customerData.get().getCustomerName());
        DubboResult<EntIndustryInfoView> dubboResult = iCompanyInfoEsDubbo.getEntInfoFromSkbEs(skbParamsReq);
        if (dubboResult == null || !dubboResult.checkSuccess()) {
            log.error("获取行业失败,custName:{}", customerData.get().getCustomerName());
//            return false;
        }else {
            EntIndustryInfoView data = dubboResult.getData();
            segmentDetail.setFirstIndustryCode(data.getFirstIndustry());
            segmentDetail.setFirstIndustryName(data.getFirstIndustryName());
            segmentDetail.setSecondIndustryCode(data.getSecondIndustry());
            segmentDetail.setSecondIndustryName(data.getSecondIndustryName());
            segmentDetail.setThirdIndustryCode(data.getThirdIndustry());
            segmentDetail.setThirdIndustryName(data.getThirdIndustryName());
            segmentDetail.setFourthIndustryCode(data.getFourthIndustry());
            segmentDetail.setFourthIndustryName(data.getFourthIndustryName());
            segmentDetail.setIcpFlag(data.getHasIcp()?1:0);
            segmentDetail.setRegisterCapital(data.getRegCapUnify() != null ? data.getRegCapUnify().doubleValue() : 0.0);
            segmentDetail.setJingchukouFlag(data.getHasImportAndExportCredit()?1:0);
        }

        List<Integer> protectStageList =new ArrayList<>();
        protectStageList.add(1);
        protectStageList.add(2);
        protectStageList.add(3);
        protectStageList.add(5);
        //获取保护关系
        CmCustProtect protect = protectBusiness.getCmCustProtect(segmentDetail.getCustomerId());
        if (Objects.nonNull(protect) && Objects.nonNull(protect.getStatus()) && protectStageList.contains(protect.getStatus())){
            if (Objects.equals("3950",protect.getAreaId()) || Objects.equals("4343",protect.getAreaId())){
                return false;
            }
        }
        segmentDetail.setSegmentId(detail.getSegmentId());
        segmentDetail.setCustomerId(segmentDetail.getCustomerId());
        segmentDetail.setPid(customerData.get().getSourceDataId());
        segmentDetail.setCustomerName(customerData.get().getCustomerName());
        segmentDetail.setRegProvince(customerData.get().getProvinceCode());
        segmentDetail.setRegCity(customerData.get().getCityCode());
        segmentDetail.setRegDistrict(customerData.get().getDistrictCode());
        segmentDetail.setCreateId(detail.getCreateId());
        segmentDetail.setCreateName(detail.getCreateName());
        segmentDetail.setCreateTime(detail.getCreateTime());
        segmentDetail.setUpdateId(detail.getCreateId());
        segmentDetail.setUpdateName(detail.getCreateName());
        segmentDetail.setUpdateTime(detail.getCreateTime());
        // 默认都是未成交，未保护
        segmentDetail.setDealStatus(0);
        segmentDetail.setProtectStatus(0);

        if (Objects.nonNull(protect)) {
            StringBuilder stringBuilder = new StringBuilder();
            Set<String> orgIdSet = new HashSet<>();
            Set<String> empIdSet = new HashSet<>();
            if (Objects.equals(1, protect.getStatus())) {
                segmentDetail.setProtectStatus(1);
                distirbuteAreaList.add(protect.getAreaId());
                if (Objects.equals(3, protect.getCustType()) || Objects.equals(4, protect.getCustType())) {
                    segmentDetail.setDealStatus(1);
                    segmentDetail.setSubId(protect.getSubcompanyId());
                    segmentDetail.setBuId(protect.getBuId());
                    segmentDetail.setDeptId(protect.getBussdeptId());
                    segmentDetail.setSalerId(protect.getSalerId());
                }
                orgIdSet.add(protect.getAreaId());
                orgIdSet.add(protect.getSubcompanyId());
                orgIdSet.add(protect.getBuId());
                orgIdSet.add(protect.getBussdeptId());
                empIdSet.add(protect.getSalerId());
            } else if (Objects.equals(2, protect.getStatus())) {
                segmentDetail.setProtectStatus(1);
                distirbuteAreaList.add(protect.getAreaId());
                orgIdSet.add(protect.getAreaId());
                orgIdSet.add(protect.getSubcompanyId());
            } else if (Objects.equals(3, protect.getStatus())) {
                segmentDetail.setProtectStatus(1);
                distirbuteAreaList.add(protect.getAreaId());
                orgIdSet.add(protect.getAreaId());
                orgIdSet.add(protect.getSubcompanyId());
                orgIdSet.add(protect.getBuId());
                orgIdSet.add(protect.getBussdeptId());
            } else if (Objects.equals(5, protect.getStatus())) {
                segmentDetail.setProtectStatus(1);
                distirbuteAreaList.add(protect.getAreaId());
                orgIdSet.add(protect.getAreaId());
                orgIdSet.add(protect.getSubcompanyId());
                orgIdSet.add(protect.getBuId());
            }

            Map<String,String> orgMap;
            if (CollectionUtils.isNotEmpty(orgIdSet)) {
                List<OrgThirdDto> orgThirdDtoList = orgThirdService.selectListByIds(new ArrayList<>(orgIdSet));
                orgMap =orgThirdDtoList.stream().collect(Collectors.toMap(OrgThirdDto::getId,OrgThirdDto::getName,(key1,key2) -> key2));
            }else {
                orgMap = Collections.emptyMap();
            }

            Map<String, EmployeeInfoThirdDto> employeeMap;
            if (CollectionUtils.isNotEmpty(empIdSet)) {
                employeeMap = employeeThirdService.getEmployeeDataMap(new ArrayList<>(empIdSet));
            }else {
                employeeMap = Collections.emptyMap();
            }
            if (Objects.equals(1, protect.getStatus())) {
                if (Objects.nonNull(orgMap.get(protect.getAreaId()))){
                    stringBuilder.append(orgMap.get(protect.getAreaId()));
                }
                if (Objects.nonNull(orgMap.get(protect.getSubcompanyId()))){
                    stringBuilder.append("/"+orgMap.get(protect.getSubcompanyId()));
                }
                if (Objects.nonNull(orgMap.get(protect.getBuId()))){
                    stringBuilder.append("/"+orgMap.get(protect.getBuId()));
                }
                if (Objects.nonNull(orgMap.get(protect.getBussdeptId()))){
                    stringBuilder.append("/"+orgMap.get(protect.getBussdeptId()));
                }
                if (Objects.nonNull(employeeMap.get(protect.getSalerId()))){
                    stringBuilder.append("/"+employeeMap.get(protect.getSalerId()).getName());
                }
            } else if (Objects.equals(2, protect.getStatus())) {
                if (Objects.nonNull(orgMap.get(protect.getAreaId()))){
                    stringBuilder.append(orgMap.get(protect.getAreaId()));
                }
                if (Objects.nonNull(orgMap.get(protect.getSubcompanyId()))){
                    stringBuilder.append("/"+orgMap.get(protect.getSubcompanyId()));
                }
            } else if (Objects.equals(3, protect.getStatus())) {
                if (Objects.nonNull(orgMap.get(protect.getAreaId()))){
                    stringBuilder.append(orgMap.get(protect.getAreaId()));
                }
                if (Objects.nonNull(orgMap.get(protect.getSubcompanyId()))){
                    stringBuilder.append("/"+orgMap.get(protect.getSubcompanyId()));
                }
                if (Objects.nonNull(orgMap.get(protect.getBuId()))){
                    stringBuilder.append("/"+orgMap.get(protect.getBuId()));
                }
                if (Objects.nonNull(orgMap.get(protect.getBussdeptId()))){
                    stringBuilder.append("/"+orgMap.get(protect.getBussdeptId()));
                }
            } else if (Objects.equals(5, protect.getStatus())) {
                if (Objects.nonNull(orgMap.get(protect.getAreaId()))){
                    stringBuilder.append(orgMap.get(protect.getAreaId()));
                }
                if (Objects.nonNull(orgMap.get(protect.getSubcompanyId()))){
                    stringBuilder.append("/"+orgMap.get(protect.getSubcompanyId()));
                }
                if (Objects.nonNull(orgMap.get(protect.getBuId()))){
                    stringBuilder.append("/"+orgMap.get(protect.getBuId()));
                }
            }
            segmentDetail.setProtectSnapshot(stringBuilder.toString());
        }
        //如果区域为空，根据省市区去找市场，找对应的区域
        if (CollectionUtils.isEmpty(distirbuteAreaList)) {
            List<SmaMarketArea> list = getMarketAreaByDistrict(segmentDetail.getRegDistrict());
            if (CollectionUtils.isEmpty(list)){
                list = getMarketAreaByCity(segmentDetail.getRegCity());
                if (CollectionUtils.isEmpty(list)){
                    list = getMarketAreaByProvince(segmentDetail.getRegProvince());
                }
            }
            //根据市场ID 获取区域ID
            if (CollectionUtils.isNotEmpty(list)){
                // 重构
                List<String> marketIds = list.stream().map(SmaMarketArea::getMarketId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(marketIds)) {
                    // 根据市场 id list 获取分司列表，然后去重，然后随机取一个
                    SmaMarketSubcompanyQuery condition = new SmaMarketSubcompanyQuery();
                    condition.setMarketIdList(marketIds);
                    List<SmaMarketSubcompanyView> marketSubcompanyViews = smaMarketSubcompanyService.selectByCondition(condition);
                    if (CollectionUtils.isNotEmpty(marketSubcompanyViews)) {
                        //t.length()<8 排除掉测试分公司 和 跨境分公司
                        List<String> kjSubList = new ArrayList<>(Arrays.asList(
                                "133", "136", "139", "151", "167", "260", "266", "272", "273",
                                "2229", "2402", "2403", "3674", "4033", "4038", "4134", "4212",
                                "4228", "4330", "61", "65", "69"
                        ));
                        List<String> subIdList = marketSubcompanyViews.stream().map(SmaMarketSubcompanyView::getSubCompany).filter(StringUtils::isNotBlank).distinct().filter(t->t.length()<8).filter(t->!kjSubList.contains(t)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(subIdList)) {
                            //subIdList 随机取一个 第一版
                            /*String subId = subIdList.get(new Random().nextInt(subIdList.size()));
                            OrgThirdDto orgByOrgId = getOrgById(subId);
                            if (Objects.nonNull(orgByOrgId)) {
                                segmentDetail.setAreaId(orgByOrgId.getParentId());
                            }*/
                            for (String subId : subIdList){
                                OrgThirdDto orgByOrgId = getOrgById(subId);
                                if (Objects.nonNull(orgByOrgId)) {
                                    distirbuteAreaList.add(orgByOrgId.getParentId());
                                }
                            }
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(distirbuteAreaList)){
            log.info("该客户没有找到对应的区域,customerId={}",customerId);
            List<SegmentDetail> segmentDetails = segmentDetailService.lambdaQuery()
                    .eq(SegmentDetail::getSegmentId, segmentId).eq(SegmentDetail::getCustomerId, customerId).list();
            if (CollectionUtils.isNotEmpty(segmentDetails)){
                //什么也不干
            }else {
                segmentDetail.setAreaId(null);
//                rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
//                        .segmentId(segmentDetail.getSegmentId()).assignmentType(0)
//                        .operatorId("系统")
//                        .customerId(segmentDetail.getCustomerId())
//                        .distributeTime(new Date())
//                        .receivedOrgId(null)
//                        .receivedType(5)
//                        .loginPosition("总部")
//                        .build()));
//                segmentDetailService.save(segmentDetail);
            }
        }else{
            List<String> areaIdList = distirbuteAreaList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            for (String areaId : areaIdList) {
                List<SegmentDetail> segmentDetails = segmentDetailService.lambdaQuery()
                        .eq(SegmentDetail::getSegmentId, segmentId).eq(SegmentDetail::getCustomerId, customerId).eq(SegmentDetail::getAreaId, areaId).list();
                if (CollectionUtils.isNotEmpty(segmentDetails)){
                    //什么也不干
                }else {
                    segmentDetail.setId(null);
                    segmentDetail.setAreaId(areaId);
                    rocketMqOperate.syncSend(CDP_SEGMENT_DISTRIBUTE_TOPIC, JSON.toJSONString(CdpSegmentDistributeMqData.builder()
                            .segmentId(segmentDetail.getSegmentId()).assignmentType(0)
                            .operatorId("系统")
                            .customerId(segmentDetail.getCustomerId())
                            .distributeTime(new Date())
                            .receivedOrgId(segmentDetail.getAreaId())
                            .receivedType(5)
                            .loginPosition("总部")
                            .build()));
                    segmentDetailService.save(segmentDetail);
                }
            }
            //下发数量
            segmentService.updateDistributeCount(segmentId,1);
        }
        return true;
    }


    /***
     * 根据区域code获取市场
     * @param districtCode
     * <AUTHOR>
     * @date 2025/2/27 22:26
     * @version 1.0.0
     * @return java.util.List<com.ce.scrm.center.dao.entity.SmaMarketArea>
    **/
    private List<SmaMarketArea> getMarketAreaByDistrict(String districtCode){
        if (StringUtils.isEmpty(districtCode)){
            return Lists.newArrayList();
        }
        return smaMarketAreaService.lambdaQuery().in(SmaMarketArea::getAreaCode, districtCode).list();
    }

    /***
     * 根据市code获取市场
     * @param cityCode
     * <AUTHOR>
     * @date 2025/2/27 22:27
     * @version 1.0.0
     * @return java.util.List<com.ce.scrm.center.dao.entity.SmaMarketArea>
    **/
    private List<SmaMarketArea> getMarketAreaByCity(String cityCode){
        if (StringUtils.isEmpty(cityCode)){
            return Lists.newArrayList();
        }
        return smaMarketAreaService.lambdaQuery().in(SmaMarketArea::getCityCode, cityCode).list();
    }
    /***
     * 根据省code获取市场
     * @param provinceCode
     * <AUTHOR>
     * @date 2025/2/27 22:27
     * @version 1.0.0
     * @return java.util.List<com.ce.scrm.center.dao.entity.SmaMarketArea>
    **/
    private List<SmaMarketArea> getMarketAreaByProvince(String provinceCode){
        if (StringUtils.isEmpty(provinceCode)){
            return Lists.newArrayList();
        }
        return smaMarketAreaService.lambdaQuery().in(SmaMarketArea::getProvinceCode, provinceCode).list();
    }
	/**
	 * 专项线索追踪
	 */
	public Page<TrackClueSegmentBusinessView> trackCluePage(TrackClueSegmentPageBusinessDto trackClueSegmentBusinessView) {
		Page<Segment> segmentPage = Page.of(trackClueSegmentBusinessView.getPageNum(), trackClueSegmentBusinessView.getPageSize());
		Page<Segment> page = segmentService.lambdaQuery().eq(Segment::getDeleteFlag, YesOrNoEnum.NO.getCode()).page(segmentPage);
		if (Objects.nonNull(page)) {

		}
		return new Page<>(trackClueSegmentBusinessView.getPageNum(), trackClueSegmentBusinessView.getPageSize());
	}

    public Boolean updateSalerCustomTagById(UpdateCustomTagBusinessDto updateCustomTagBusinessDto) {

        log.info("updateSalerCustomTagById 入参为={}",JSON.toJSONString(updateCustomTagBusinessDto));

        if(!PositionUtil.isBusinessSaler(updateCustomTagBusinessDto.getLoginPosition())) {
            throw new ApiException(CodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }

        SegmentDetail segmentDetail = segmentDetailService.lambdaQuery().eq(SegmentDetail::getId, updateCustomTagBusinessDto.getId()).one();

        if(segmentDetail == null) {
            throw new ApiException(CodeMessageEnum.DATA_NOT_EXIST);
        }

        Date currentDate = new Date();
        segmentDetail.setSalerCustomTag(updateCustomTagBusinessDto.getSalerCustomTag());
        if (segmentDetail.getSalerCustomTagTime() == null) {
            segmentDetail.setSalerCustomTagTime(currentDate);
        }
        segmentDetail.setUpdateId(updateCustomTagBusinessDto.getLoginEmployeeId());
        segmentDetail.setUpdateName(updateCustomTagBusinessDto.getLoginEmployeeName());
        segmentDetail.setUpdateTime(currentDate);

        boolean b = segmentDetailService.updateById(segmentDetail);

        log.info("updateSalerCustomTagById 出参为={}",b);

        // 上报CDP
        try {
            Segment segment = segmentService.lambdaQuery().eq(Segment::getSegmentId, segmentDetail.getSegmentId()).last("limit 1").one();
            SegmentSalerTagMqData mqData = SegmentSalerTagMqData.builder()
                    .customerId(segmentDetail.getCustomerId())
                    .zqSegmentSubjectId(segmentDetail.getSegmentId())
                    .zqSegmentSubjectName(segment == null ? null : segment.getSegmentName())
                    .salerId(updateCustomTagBusinessDto.getLoginEmployeeId())
                    .salerName(updateCustomTagBusinessDto.getLoginEmployeeName())
                    .salerDeptId(updateCustomTagBusinessDto.getLoginOrgId())
                    .salerDeptName(updateCustomTagBusinessDto.getLoginOrgName())
                    .salerBuId(updateCustomTagBusinessDto.getLoginBuId())
                    .salerBuName(updateCustomTagBusinessDto.getLoginBuName())
                    .salerSubId(updateCustomTagBusinessDto.getLoginSubId())
                    .salerSubName(updateCustomTagBusinessDto.getLoginSubName())
                    .salerAreaId(updateCustomTagBusinessDto.getLoginAreaId())
                    .salerAreaName(updateCustomTagBusinessDto.getLoginAreaName())
                    .protectStatus(segmentDetail.getProtectStatus() != null && segmentDetail.getProtectStatus() == 1 ? "是" : "否")
                    .dealStatus(segmentDetail.getDealStatus() != null && segmentDetail.getDealStatus() == 1 ? "是" : "否")
                    .handleResult(SalerCustomTagEnum.getNameByCode(updateCustomTagBusinessDto.getSalerCustomTag()))
                    .handleTime(currentDate)
                    .build();
            rocketMqOperate.syncSend(CDP_SEGMENT_GROUP_SALER_TAG_TOPIC, JSON.toJSONString(mqData));
        }catch (Exception e) {
            log.error("分群下发里商务添加收藏，上报CDP失败",e);
        }

        return b;
    }

    public Boolean distribute(String segmentId) {
        Segment detail = detail(segmentId);
        if (Objects.isNull(detail)) {
            throw new RuntimeException("分群不存在");
        }
        //判断下发状态
        if (!Objects.equals(detail.getSegmentStatus(), 0)) {
            throw new RuntimeException("分群重复下发");
        }
        //执行下发动作
        try {
            detail.setSegmentStatus(1);
            segmentService.updateById(detail);
            rocketMqOperate.syncSend(CDP_CUSTOMER_GROUP_ID_TOPIC, segmentId);
            return true;
        } catch (Exception e) {
            log.error("创建下发任务，参数为:{}", segmentId);
        }
        return false;
    }
	/**
	 * 看板页面
	 * @param kanbanSegmentBusinessDto 看板查询入参信息
	 * <AUTHOR>
	 * @date 2025/2/28 11:27
	 * @return {@link KanbanSegmentBusinessView}
	 **/
	public KanbanSegmentBusinessView kanbanInfo(KanbanSegmentBusinessDto kanbanSegmentBusinessDto) {
		EmployeeInfoBusinessDto currentUser = employeeInfoBusiness.getEmployeeInfoByEmpId(kanbanSegmentBusinessDto.getLoginEmployeeId());
		if (currentUser == null) {
			throw new ApiException(CodeMessageEnum.NOT_LOGIN);
		}
		if (PositionUtil.isBusinessSaler(currentUser.getPosition())) {
			return null;
		}
		KanbanSegmentBusinessView view = new KanbanSegmentBusinessView();
		Pair<Date, Date> dateSelection = getStartEndDateForSelection(kanbanSegmentBusinessDto.getSelectionStartDate(), kanbanSegmentBusinessDto.getSelectionEndDate());
		Segment segment = segmentService.lambdaQuery()
			.eq(Segment::getDeleteFlag, YesOrNoEnum.NO.getCode())
			// .ge(Objects.nonNull(dateSelection.getLeft()), Segment::getUpdateTime, dateSelection.getLeft())
			// .le(Objects.nonNull(dateSelection.getRight()), Segment::getUpdateTime, dateSelection.getRight())
			.eq(Segment::getSegmentId, kanbanSegmentBusinessDto.getSegmentId())
			.last("limit 1")
			.one();
		if (Objects.isNull(segment)) {
			log.info("分群主表筛选信息不存在,入参=>{}", JSON.toJSONString(kanbanSegmentBusinessDto));
			return view;
		}
		view.setSegmentId(segment.getSegmentId());
		view.setSegmentName(segment.getSegmentName());
		view.setDistributeTime(segment.getSegmentBeginTime());
		// view.setSegmentDistributeCount(segment.getSegmentDistributeCount());

		SegmentDataStatisticsCacheView cacheView = segmentDataStatisticsCacheHandler.get(kanbanSegmentBusinessDto.getLoginEmployeeId() + "," + view.getSegmentId()
			+ "," + kanbanSegmentBusinessDto.getSelectionStartDate() + "," + kanbanSegmentBusinessDto.getSelectionEndDate());
		log.info("kanban统计信息={}", JSON.toJSONString(cacheView));
		if (Objects.isNull(cacheView)) {
			return view;
		}
		// 当前职位的：下发总量、下发时已保护、下发时已签单
		view.setSegmentDistributeCount(cacheView.getSegmentDistributeCount());
		view.setDistributeProtectedCount(cacheView.getDistributeProtectedCount());
		view.setDistributeSignCount(cacheView.getDistributeSignCount());

		view.setYesterdayCustomTagCount(cacheView.getYesterdayHandleCount());
		view.setYesterdayProtectedCount(cacheView.getYesterdayProtectedCount());
		view.setYesterdayVisitedCount(cacheView.getYesterdayVisitCount());
		view.setYesterdaySignedCount(cacheView.getYesterdaySignCount());
		view.setAccumulatedCustomTagCount(cacheView.getAllHandleCount());
		view.setAccumulatedProtectedCount(cacheView.getAllProtectedCount());
		view.setAccumulatedVisitedCount(cacheView.getAllVisitCount());
		view.setAccumulatedSignedCount(cacheView.getAllSignCount());


		Long segmentDistributeCount = view.getSegmentDistributeCount();
		// 比例计算 percent换为count/segmentDistributeCount + "%"
		view.setYesterdayCustomTagPercent(BigDecimalUtils.calculatePercent(view.getYesterdayCustomTagCount(), segmentDistributeCount));
		view.setYesterdayProtectedPercent(BigDecimalUtils.calculatePercent(view.getYesterdayProtectedCount(), segmentDistributeCount));
		view.setYesterdayVisitedPercent(BigDecimalUtils.calculatePercent(view.getYesterdayVisitedCount(), segmentDistributeCount));
		view.setYesterdaySignedPercent(BigDecimalUtils.calculatePercent(view.getYesterdaySignedCount(), segmentDistributeCount));
		view.setAccumulatedCustomTagPercent(BigDecimalUtils.calculatePercent(view.getAccumulatedCustomTagCount(), segmentDistributeCount));
		view.setAccumulatedProtectedPercent(BigDecimalUtils.calculatePercent(view.getAccumulatedProtectedCount(), segmentDistributeCount));
		view.setAccumulatedVisitedPercent(BigDecimalUtils.calculatePercent(view.getAccumulatedVisitedCount(), segmentDistributeCount));
		view.setAccumulatedSignedPercent(BigDecimalUtils.calculatePercent(view.getAccumulatedSignedCount(), segmentDistributeCount));

		return view;
	}


	/**
	 * 开始结束时间转换
	 */
	private Pair<Date, Date> getStartEndDateForSelection(String start, String end) {
		if (StringUtils.isAnyBlank(start, end)) {
			return Pair.of(null, null);
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date startTime;
		Date endTime;
		try {
			startTime = sdf.parse(start);
			endTime = sdf.parse(end);
			return Pair.of(startTime, endTime);
		} catch (ParseException e) {
			log.warn("parse date error");
		}
		return Pair.of(null, null);
	}

    public Page<TrackClueSegmentBusinessView> trackClueList(TrackClueSegmentPageBusinessDto queryDto) {

        log.info("trackClueList 入参为={}",JSON.toJSONString(queryDto));

        String loginPosition = queryDto.getLoginPosition();
        if(PositionUtil.isBusinessArea(loginPosition)) {
            queryDto.setAreaId(queryDto.getLoginAreaId());
        }else if (PositionUtil.isBusinessMajor(loginPosition)) {
            queryDto.setAreaId(queryDto.getLoginAreaId());
            queryDto.setSubId(queryDto.getLoginSubId());
        } else if (PositionUtil.isBusinessBu(loginPosition)) {
            queryDto.setAreaId(queryDto.getLoginAreaId());
            queryDto.setSubId(queryDto.getLoginSubId());
            queryDto.setBuId(queryDto.getLoginBuId());
        } else if (PositionUtil.isBusinessManager(loginPosition)) {
            queryDto.setAreaId(queryDto.getLoginAreaId());
            queryDto.setSubId(queryDto.getLoginSubId());
            queryDto.setBuId(queryDto.getLoginBuId());
            queryDto.setDeptId(queryDto.getLoginOrgId());
        } else if (PositionUtil.isBusinessSaler(loginPosition)) {
            queryDto.setAreaId(queryDto.getLoginAreaId());
            queryDto.setSubId(queryDto.getLoginSubId());
            queryDto.setBuId(queryDto.getLoginBuId());
            queryDto.setDeptId(queryDto.getLoginOrgId());
            queryDto.setSalerId(queryDto.getLoginEmployeeId());
        } else {
            throw new ApiException(CodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }

        Page<Segment> objectPage = Page.of(queryDto.getPageNum(), queryDto.getPageSize());
        Page<Segment> segmentPage;
        EmployeeInfoBusinessDto employeeInfoByEmpId = employeeInfoBusiness.getEmployeeInfoByEmpId(queryDto.getLoginEmployeeId());

        if (employeeInfoByEmpId != null && Objects.equals(employeeInfoByEmpId.getIsZbReport(),1)){
            segmentPage = segmentService.lambdaQuery().eq(Segment::getDeleteFlag, YesOrNoEnum.NO.getCode()).page(objectPage);
        } else {
	        segmentPage = segmentService.getDiffSubIdAndStatusPage(objectPage, queryDto.getLoginSubId(), queryDto.getLoginEmployeeId());
        }

        Page<TrackClueSegmentBusinessView> resultPage = new Page<>();
        BeanUtil.copyProperties(segmentPage, resultPage);

        List<Segment> records = segmentPage.getRecords();
        List<TrackClueSegmentBusinessView> resultList = new ArrayList<>();
        for (Segment record : records) {

            SegmentDataStatisticsCacheView segmentDataStatisticsCacheView = segmentDataStatisticsCacheHandler.get(queryDto.getLoginEmployeeId()  + "," + record.getSegmentId());
            TrackClueSegmentBusinessView build = TrackClueSegmentBusinessView.builder()
                    .id(record.getId())
                    .segmentId(record.getSegmentId())
                    .segmentName(record.getSegmentName())
                    .segmentCount(segmentDataStatisticsCacheView != null ? segmentDataStatisticsCacheView.getSegmentDistributeCount() : null)
                    .segmentBeginTime(record.getSegmentBeginTime())
                    .yesterdayVisitCount(segmentDataStatisticsCacheView != null ? segmentDataStatisticsCacheView.getYesterdayVisitCount() : null)
                    .yesterdaySignCount(segmentDataStatisticsCacheView != null ? segmentDataStatisticsCacheView.getYesterdaySignCount() : null)
                    .allVisitCount(segmentDataStatisticsCacheView != null ? segmentDataStatisticsCacheView.getAllVisitCount() : null)
                    .allSignCount(segmentDataStatisticsCacheView != null ? segmentDataStatisticsCacheView.getAllSignCount() : null)
                    .build();

            resultList.add(build);

        }

        resultPage.setRecords(resultList);
        return resultPage;
    }

	/**
	 * 看板详情
	 * @param detailDto 看板详情查询入参信息
	 * <AUTHOR>
	 * @date 2025/2/28 14:27
	 * @return {@link KanbanDetailBusinessView}
	 **/
	public List<KanbanDetailBusinessView> getKanbanDetail(KanbanDetailSegmentBusinessDto detailDto) {
		EmployeeInfoBusinessDto employeeInfo = employeeInfoBusiness.getEmployeeInfoByEmpId(detailDto.getLoginEmployeeId());
		List<Integer> types = Lists.newArrayList(1, 2, 3, 4);
		Date start = null;
		Date end = null;
		if (types.contains(detailDto.getType())) {
			Pair<Date, Date> startEndDateForSelection = getStartEndDateForSelection(detailDto.getSelectionStartDate(), detailDto.getSelectionEndDate());
			start = startEndDateForSelection.getLeft();
			end = startEndDateForSelection.getRight();
		}
		String position = detailDto.getLoginPosition();
		LambdaQueryChainWrapper<SegmentDetail> query = segmentDetailService.lambdaQuery()
		.eq(SegmentDetail::getSegmentId, detailDto.getSegmentId())
		.eq(SegmentDetail::getDeleteFlag, YesOrNoEnum.NO.getCode());
		String orgType;
		if(PositionUtil.isBusinessArea(position)) {
			query.eq(SegmentDetail::getAreaId, employeeInfo.getAreaId());
			orgType = OrgTypeEnum.AREA.getType();
		} else if (PositionUtil.isBusinessMajor(position)) {
			query.eq(SegmentDetail::getAreaId, employeeInfo.getAreaId());
			query.eq(SegmentDetail::getSubId, employeeInfo.getSubId());
			orgType = OrgTypeEnum.SUB.getType();
		} else if (PositionUtil.isBusinessBu(position)) {
			query.eq(SegmentDetail::getAreaId, employeeInfo.getAreaId());
			query.eq(SegmentDetail::getSubId, employeeInfo.getSubId());
			query.eq(SegmentDetail::getBuId, employeeInfo.getBuId());
			orgType = OrgTypeEnum.BU.getType();
		} else if (PositionUtil.isBusinessManager(position)) {
			query.eq(SegmentDetail::getAreaId, employeeInfo.getAreaId());
			query.eq(SegmentDetail::getSubId, employeeInfo.getSubId());
			query.eq(Objects.nonNull(employeeInfo.getBuId()), SegmentDetail::getBuId, employeeInfo.getBuId());
			query.eq(SegmentDetail::getDeptId, employeeInfo.getOrgId());
			orgType = OrgTypeEnum.DEPT.getType();
		} else if (PositionUtil.isBusinessSaler(position)) {
			return Collections.emptyList();
		} else if (employeeInfo.getIsZbReport() == 1) {
			orgType = OrgTypeEnum.HQ.getType();
		} else {
			return Collections.emptyList();
		}

		// Map<String, OrgDataThirdView> orgMap = orgThirdService.getOrgData(new);
		List<KanbanDetailBusinessView> result = new ArrayList<>();
		boolean hasTimeCondition = Objects.nonNull(start) || Objects.nonNull(end);
		Date currentDate = new Date();
		LocalDate yesterday = LocalDate.now().minusDays(1);
		LocalDateTime startOfYesterday = LocalDateTime.of(yesterday, LocalTime.MIN);
		LocalDateTime endOfYesterday = LocalDateTime.of(yesterday, LocalTime.MAX);
		switch (detailDto.getType()) {
			case 1:
			    query.ge(hasTimeCondition, SegmentDetail::getSalerCustomTagTime, start)
					.le(hasTimeCondition, SegmentDetail::getSalerCustomTagTime, end)
				    .ge(!hasTimeCondition, SegmentDetail::getSalerCustomTagTime, startOfYesterday)
				    .le(!hasTimeCondition, SegmentDetail::getSalerCustomTagTime, endOfYesterday)
					.ne(SegmentDetail::getSalerCustomTag, SalerCustomTagEnum.UNTREATED.getCode());

				result = diffViewPoint(orgType, query, result);
				break;
			case 2:
				query.ge(hasTimeCondition, SegmentDetail::getProtectTime, start)
					.le(hasTimeCondition, SegmentDetail::getProtectTime, end)
					.ge(!hasTimeCondition, SegmentDetail::getProtectTime, startOfYesterday)
					.le(!hasTimeCondition, SegmentDetail::getProtectTime, endOfYesterday)
				;
				result = diffViewPoint(orgType, query, result);
				break;
			case 3:
				query.ge(hasTimeCondition, SegmentDetail::getVisitLastTime, start)
					.le(hasTimeCondition, SegmentDetail::getVisitLastTime, end)
					.ge(!hasTimeCondition, SegmentDetail::getVisitLastTime, startOfYesterday)
					.le(!hasTimeCondition, SegmentDetail::getVisitLastTime, endOfYesterday);
				result = diffViewPoint(orgType, query, result);
				break;
			case 4:
				query.ge(hasTimeCondition, SegmentDetail::getSignFirstTime, start)
					.le(hasTimeCondition, SegmentDetail::getSignFirstTime, end)
					.ge(!hasTimeCondition, SegmentDetail::getSignFirstTime, startOfYesterday)
					.le(!hasTimeCondition, SegmentDetail::getSignFirstTime, endOfYesterday);
				result = diffViewPoint(orgType, query, result);
				break;
			case 5:
				query.le(SegmentDetail::getSalerCustomTagTime, currentDate)
					.ne(SegmentDetail::getSalerCustomTag, SalerCustomTagEnum.UNTREATED.getCode());
				result = diffViewPoint(orgType, query, result);
				break;
			case 6:
				query.le(SegmentDetail::getProtectTime, currentDate);
				result = diffViewPoint(orgType, query, result);
				break;
			case 7:
				query.le(SegmentDetail::getVisitLastTime, currentDate);
				result = diffViewPoint(orgType, query, result);
				break;
			case 8:
				query.le(SegmentDetail::getSignFirstTime, currentDate);
				result = diffViewPoint(orgType, query, result);
				break;

		}
		List<String> orgIds = result.stream().map(KanbanDetailBusinessView::getOrgId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
		Map<String, OrgDataThirdView> orgMap = orgThirdService.getOrgData(orgIds);
		Map<String, EmployeeInfoThirdDto> employeeMap = employeeThirdService.getEmployeeDataMap(orgIds);
		result.forEach(i -> {
			if (orgMap.containsKey(i.getOrgId())) {
				OrgDataThirdView orgDataThirdView = orgMap.get(i.getOrgId());
				if (orgDataThirdView != null) i.setOrgName(orgDataThirdView.getName());
			}
			if (PositionUtil.isBusinessManager(position)) {
				EmployeeInfoThirdDto employeeInfoThirdDto = employeeMap.get(i.getOrgId());
				if (employeeInfoThirdDto != null) i.setOrgName(employeeInfoThirdDto.getName());
			}
		});
		return result;
	}

	private static List<KanbanDetailBusinessView> diffViewPoint(String orgType, LambdaQueryChainWrapper<SegmentDetail> query, List<KanbanDetailBusinessView> result) {
		if (OrgTypeEnum.HQ.getType().equals(orgType)) {
			// query.eq(SegmentDetail::getAreaDistributeTime, )
			List<SegmentDetail> type1List = query.list();
			Map<String, Integer> areaIdMap = type1List.stream().collect(Collectors.groupingBy(SegmentDetail::getAreaId,Collectors.summingInt(e -> 1)));
			result = areaIdMap.entrySet().stream().map(entry -> new KanbanDetailBusinessView(entry.getKey(), entry.getValue())).collect(Collectors.toList());
		}
		if (OrgTypeEnum.AREA.getType().equals(orgType)) {
			List<SegmentDetail> type1List = query.list();
			// 区总下所有sub
			Map<String, Integer> subIdCountMap = type1List.stream().collect(Collectors.groupingBy(SegmentDetail::getSubId,Collectors.summingInt(e -> 1)));
			result = subIdCountMap.entrySet().stream().map(entry -> new KanbanDetailBusinessView(entry.getKey(), entry.getValue())).collect(Collectors.toList());
		}
		if (OrgTypeEnum.SUB.getType().equals(orgType)) {
			List<SegmentDetail> type1List = query.list();
			// 分司下所有部门
			Map<String, Integer> deptIdCountMap = type1List.stream().collect(Collectors.groupingBy(SegmentDetail::getDeptId,Collectors.summingInt(e -> 1)));
			result = deptIdCountMap.entrySet().stream().map(entry -> new KanbanDetailBusinessView(entry.getKey(), entry.getValue())).collect(Collectors.toList());
		}
		if (OrgTypeEnum.BU.getType().equals(orgType)) {
			List<SegmentDetail> type1List = query.list();
			// 事业部下所有部门
			Map<String, Integer> deptIdCountMap = type1List.stream().collect(Collectors.groupingBy(SegmentDetail::getDeptId, Collectors.summingInt(e -> 1)));
			result = deptIdCountMap.entrySet().stream().map(entry -> new KanbanDetailBusinessView(entry.getKey(), entry.getValue())).collect(Collectors.toList());
		}
		if (OrgTypeEnum.DEPT.getType().equals(orgType)) {
			List<SegmentDetail> type1List = query.list();
			// 部门下所有商务
			Map<String, Integer> salerIdCountMap = type1List.stream().collect(Collectors.groupingBy(SegmentDetail::getSalerId,Collectors.summingInt(e -> 1)));
			result = salerIdCountMap.entrySet().stream().map(entry -> new KanbanDetailBusinessView(entry.getKey(), entry.getValue())).collect(Collectors.toList());
		}
		return result;
	}

    private OrgThirdDto getOrgById(String orgId){
        if (StringUtils.isEmpty(orgId)){
            return null;
        }
        Optional<OrgThirdDto> orgByOrgId = orgThirdService.getOrgByOrgId(orgId);
        if (orgByOrgId.isPresent()){
            return orgByOrgId.get();
        }
        return null;
    }

    private Boolean updateSegmentDetailById(SegmentDetail segmentDetail){
        LambdaUpdateWrapper<SegmentDetail> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(SegmentDetail::getId,segmentDetail.getId());
        lambdaUpdateWrapper.set(SegmentDetail::getAreaId,segmentDetail.getAreaId());
        lambdaUpdateWrapper.set(SegmentDetail::getSubId,segmentDetail.getSubId());
        lambdaUpdateWrapper.set(SegmentDetail::getBuId,segmentDetail.getBuId());
        lambdaUpdateWrapper.set(SegmentDetail::getDeptId,segmentDetail.getDeptId());
        lambdaUpdateWrapper.set(SegmentDetail::getSalerId,segmentDetail.getSalerId());
        lambdaUpdateWrapper.set(SegmentDetail::getAreaDistributeTime,segmentDetail.getAreaDistributeTime());
        lambdaUpdateWrapper.set(SegmentDetail::getSubDistributeTime,segmentDetail.getSubDistributeTime());
        lambdaUpdateWrapper.set(SegmentDetail::getBuDistributeTime,segmentDetail.getBuDistributeTime());
        lambdaUpdateWrapper.set(SegmentDetail::getDeptDistributeTime,segmentDetail.getDeptDistributeTime());
        lambdaUpdateWrapper.set(SegmentDetail::getAssignmentType,segmentDetail.getAssignmentType());
        return segmentDetailService.update(segmentDetail,lambdaUpdateWrapper);
    }

    /*
     * @Description 添加收藏 - 临时给分群重写一份
     * <AUTHOR>
     * @date 2025/3/5 10:34
     * @param addDto
     * @return java.lang.Boolean
     */
    public Boolean addFavoriteBySegmentDetailId(@Valid AddSegmentFavoriteBusinessDto addDto) {

        String segmentDetailId = addDto.getSegmentDetailId();
        SegmentDetail segmentDetail = segmentDetailService.lambdaQuery().eq(SegmentDetail::getId, segmentDetailId).last("limit 1").one();
        if(segmentDetail == null || StringUtils.isBlank(segmentDetail.getCustomerId())) {
            throw new ApiException(CodeMessageEnum.DATA_NOT_EXIST);
        }

        String customerId = segmentDetail.getCustomerId();

        Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(customerId);
        if (!customerData.isPresent()){
            throw new ApiException(CodeMessageEnum.DATA_NOT_EXIST);
        }
        CustomerDataThirdView customerDataThirdView = customerData.get();

        //分配给商务  需要判断商务的容量是否够用
        // 查询销售已用的库容数
        ClueAssignVo vo = new ClueAssignVo();
        vo.setEmpId(addDto.getLoginEmployeeId());
        vo.setStatus(ClueCustStatusEnum.SALER.getValue());
        int salerHadClueNum = clueAssignAppService.getCount(vo);
        // 查询系统分配的容量
        ClueRuleVo clueRuleVo = new ClueRuleVo();
        clueRuleVo.setSubId(addDto.getLoginSubId());
        clueRuleVo.setTypeCode(ClueRuleEnum.CLUE_SALERMAXNUM.getValue());
        clueRuleVo.setJobGrade(addDto.getLoginJobGrade());
        ClueRuleVo ruleVo = clueRuleAppService.getOneByClueRule(clueRuleVo);
        if(ruleVo == null) {
            throw new ApiException(CodeMessageEnum.SALER_REPERTORY_NOT_EXIST);
        }
        Integer availableCapacity = ruleVo.getTypeValue() - salerHadClueNum;
        if (1 > availableCapacity) {
            throw new ApiException(CodeMessageEnum.INSUFFICIENT_STORAGE);
        }

        String otherParenthesisName = MyStringUtils.getOtherParenthesisName(customerDataThirdView.getCustomerName());
        // 判断是否在线索库存在
        ClueAssignVo custIdOrCustName = clueAssignAppService.getOneByCustIdOrCustNameOrUncId(customerDataThirdView.getCustomerId(),customerDataThirdView.getCustomerName(),otherParenthesisName,customerDataThirdView.getCertificateCode());
        if(custIdOrCustName != null) {
            handleStateMessage(Collections.singletonList(customerDataThirdView.getSourceDataId()));
            throw new ApiException(CodeMessageEnum.CLUE_CUSTOMER_IS_EXIST);
        }
        // 判断是否在 保护表中存在 不等于4的数据
        CmCustProtect cmCustProtectByName = custProtectService.lambdaQuery()
                .ne(CmCustProtect::getStatus, ProtectStateEnum.CUSTOMER_POOL.getState())
                .in(CmCustProtect::getCustName, ListUtil.of(customerDataThirdView.getCustomerName(), otherParenthesisName))
                .one();
        if (cmCustProtectByName != null) {
            handleStateMessage(Collections.singletonList(customerDataThirdView.getSourceDataId()));
            throw new ApiException(CodeMessageEnum.PROTECT_CUSTOMER_IS_EXIST);
        }

        //线索超期时间
        ClueRuleVo clueRuleVo1 = new ClueRuleVo();
        clueRuleVo1.setSubId(addDto.getLoginSubId());
        clueRuleVo1.setTypeCode(ClueRuleEnum.SALEREXCEED.getValue());
        ClueRuleVo ruleVo1 = clueRuleAppService.getOneByClueRule(clueRuleVo1);
        if(ruleVo1 == null) {
            throw new ApiException(CodeMessageEnum.DATA_NOT_EXIST);
        }
        Date exceedTime = DateUtil.getDateAddDate(new Date(), 5, ruleVo.getTypeValue() / 24);

        Date currentDate = new Date();
        ClueAssignVo clueAssignSaveVo = new ClueAssignVo();
        clueAssignSaveVo.setEmpId(addDto.getLoginEmployeeId());
        clueAssignSaveVo.setDeptId(addDto.getLoginOrgId());
        clueAssignSaveVo.setBuId(addDto.getLoginBuId());
        clueAssignSaveVo.setSubCompanyId(addDto.getLoginSubId());
        clueAssignSaveVo.setAreaId(addDto.getLoginAreaId());
        clueAssignSaveVo.setExceedTime(exceedTime);
        clueAssignSaveVo.setCreateTime(currentDate);
        clueAssignSaveVo.setCreateUser(addDto.getLoginEmployeeId());
        clueAssignSaveVo.setIsReaded(IsReadEnum.NOREADED.getValue());
        clueAssignSaveVo.setSalerGetclueFrom(SalerGetclueFromEnum.SKB_PULL.getValue());
        clueAssignSaveVo.setClueVisitStage("CLUEVISITSTAGE_WCL");
        clueAssignSaveVo.setOptType(ConvertRelationEnum.SYSCLUECUST_SALER_ASSIGN.getValue());
        clueAssignSaveVo.setCustId(customerDataThirdView.getCustomerId());
        clueAssignSaveVo.setCustName(customerDataThirdView.getCustomerName());
        clueAssignSaveVo.setEntId(customerDataThirdView.getSourceDataId());
        clueAssignSaveVo.setProvinceCode(customerDataThirdView.getProvinceCode());
        clueAssignSaveVo.setCityCode(customerDataThirdView.getCityCode());
        clueAssignSaveVo.setDistrictCode(customerDataThirdView.getDistrictCode());
        clueAssignSaveVo.setUncid(customerDataThirdView.getCertificateCode());
        clueAssignSaveVo.setAddress(customerDataThirdView.getRegisterAddress());
        clueAssignSaveVo.setStatus(ClueCustStatusEnum.SALER.getValue());
        clueAssignAppService.save(clueAssignSaveVo);

        // 保存流转日志
        // 流转日志build
        ConvertLogBusinessDto convertLogBusinessDto = ConvertLogBusinessDto.builder()
                .curSalerId(addDto.getLoginEmployeeId())
                .deptOfCurSalerId(addDto.getLoginOrgId())
                .buOfCurSalerId(addDto.getLoginBuId())
                .subcompanyOfCurSalerId(addDto.getLoginSubId())
                .areaOfCurSalerId(addDto.getLoginAreaId())
                .createTime(currentDate)
                .createBy(addDto.getLoginEmployeeId())
                .convertType(ConvertRelationEnum.SYSCLUECUST_SALER_ASSIGN.getValue())
                .custId(customerDataThirdView.getCustomerId())
                .custName(customerDataThirdView.getCustomerName())
                .build();
        smaConvertLogThirdService.insertLog(convertLogBusinessDto);

        // 将收藏成功的客户的flag1设置为收藏中
        if (StringUtils.isNotBlank(customerDataThirdView.getSourceDataId())) {
            List<String> successPidList = Collections.singletonList(customerDataThirdView.getSourceDataId());
            try {
                rocketMqOperate.syncSend(CESUPPORT_SCRM_BIGDATA_FLAG_TOPIC,JSON.toJSONString(new BigDataCustomerFlagDto(successPidList, UserActionTypeEnum.FLAG_1.getId(), HighSearchProtectStatusEnum.COLLECTING.getValue() + "")));
            } catch (Exception e) {
                log.error("收藏线索客户时pId为：{}的数据给大数据发kafka消息是异常" , StringUtils.join(successPidList));
            }
            // 将收藏成功的客户的联系人放入缓存中
            try {
                rocketMqOperate.syncSend(CESUPPORT_SCRM_CONTACTPERSON_TO_REDIS_TOPIC, JSON.toJSONString(successPidList));
            } catch (Exception e) {
                log.error("收藏线索客户时pId为：{}的客户联系人放入redis异常" , StringUtils.join(successPidList));
            }
        }

        // 上报CDP
        try {
            Segment segment = segmentService.lambdaQuery().eq(Segment::getSegmentId, segmentDetail.getSegmentId()).last("limit 1").one();
            SegmentAddFavoriteMqData mqData = SegmentAddFavoriteMqData.builder()
                    .customerId(customerId)
                    .wishlistHandleType("专项线索收藏")
                    .zqSegmentSubjectId(segmentDetail.getSegmentId())
                    .zqSegmentSubjectName(segment == null ? null : segment.getSegmentName())
                    .salerId(addDto.getLoginEmployeeId())
                    .salerName(addDto.getLoginEmployeeName())
                    .salerDeptId(addDto.getLoginOrgId())
                    .salerDeptName(addDto.getLoginOrgName())
                    .salerBuId(addDto.getLoginBuId())
                    .salerBuName(addDto.getLoginBuName())
                    .salerSubId(addDto.getLoginSubId())
                    .salerSubName(addDto.getLoginSubName())
                    .salerAreaId(addDto.getLoginAreaId())
                    .salerAreaName(addDto.getLoginAreaName())
                    .collectTime(currentDate)
                    .build();
            rocketMqOperate.syncSend(CDP_SEGMENT_GROUP_ADD_FAVORITE_TOPIC, JSON.toJSONString(mqData));
        }catch (Exception e) {
            log.error("分群下发里商务添加收藏，上报CDP失败",e);
        }

        return Boolean.TRUE;
    }

    private void handleStateMessage(List<String> fPidList) {
        if(CollectionUtils.isEmpty(fPidList)) {
            return;
        }
        fPidList = fPidList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fPidList)){
            return;
        }
        // 将flag1设置为收藏中-保护过
        try {
            rocketMqOperate.syncSend(CESUPPORT_SCRM_BIGDATA_FLAG_TOPIC, JSON.toJSONString(new BigDataCustomerFlagDto(fPidList, UserActionTypeEnum.FLAG_1.getId(), HighSearchProtectStatusEnum.COLLECTING_PROTECTED.getValue() + "")));
        } catch (Exception e) {
            log.error("收藏线索客户时pId为：{}的数据给大数据发kafka消息是异常", StringUtils.join(fPidList));
        }
        // 将flag5置空
        try {
            rocketMqOperate.syncSend(CESUPPORT_SCRM_BIGDATA_FLAG_TOPIC, JSON.toJSONString(new BigDataCustomerFlagDto(fPidList, UserActionTypeEnum.FLAG_5.getId(), null)));
        } catch (Exception e) {
            log.error("收藏线索客户时pId为：{}的数据给大数据发kafka消息是异常", StringUtils.join(fPidList));
        }
    }


    /***
     * 区总批量发客户分给多个分司 加锁
     * @param segmentId
     * @param subId
     * @param customerId
     * <AUTHOR>
     * @date 2025/7/1 19:22
     * @version 1.0.0
     * @return java.lang.Boolean
    **/
    private Boolean setSegmentDistributeSubCache(String segmentId, String subId,String customerId) {
        String key = CacheConstant.CACHE_PREFIX+ CacheConstant.CACHE_KEY_SEPARATOR+ CacheKeyEnum.SEGMENT_CACHE
                + CacheConstant.CACHE_KEY_SEPARATOR+segmentId
        + CacheConstant.CACHE_KEY_SEPARATOR+subId
        + CacheConstant.CACHE_KEY_SEPARATOR+customerId;
        stringRedisTemplate.opsForValue().set(key, "1", 1, TimeUnit.DAYS);
        return true;
    }

    /***
     * 区总批量发客户分给多个分司 获取锁
     * @param segmentId
     * @param subId
     * @param customerId
     * <AUTHOR>
     * @date 2025/7/1 19:22
     * @version 1.0.0
     * @return java.lang.Boolean
     **/
    private Boolean getSegmentDistributeSubCache(String segmentId, String subId,String customerId) {
        String key = CacheConstant.CACHE_PREFIX+ CacheConstant.CACHE_KEY_SEPARATOR+ CacheKeyEnum.SEGMENT_CACHE
                + CacheConstant.CACHE_KEY_SEPARATOR+segmentId
                + CacheConstant.CACHE_KEY_SEPARATOR+subId
                + CacheConstant.CACHE_KEY_SEPARATOR+customerId;
        String string = stringRedisTemplate.opsForValue().get(key);
        return !StringUtils.isBlank(string);
    }
}
