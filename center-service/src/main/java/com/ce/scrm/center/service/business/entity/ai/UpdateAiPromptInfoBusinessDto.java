package com.ce.scrm.center.service.business.entity.ai;

import com.ce.scrm.center.dao.entity.AiPromptAuthInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 更新提示词信息
 * @Author: lijinpeng
 * @Date: 2025/2/20 10:11
 */
@Data
public class UpdateAiPromptInfoBusinessDto implements Serializable {

    private Long id;

    private String title;

    private String content;

    private Integer startFlag;

    private Integer deleteFlag;

    private String remark;

    private Integer promptType;


    /**
     * 员工ID
     */
    private String loginEmployeeId;

    private List<AiPromptAuthInfo> aiPromptAuthInfos;
    /**
     * ce 中企 gboss 跨境
     */
    private String company;
}
