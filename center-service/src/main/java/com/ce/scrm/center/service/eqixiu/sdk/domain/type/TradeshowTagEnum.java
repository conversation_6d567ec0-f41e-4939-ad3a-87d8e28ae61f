package com.ce.scrm.center.service.eqixiu.sdk.domain.type;

import java.util.Optional;

public enum TradeshowTagEnum {

    CHINA_IC_EXPO_2025("中国半导体产业与应用博览会（IC Expo）", "flag_china_icexpo_2025", 1),
    CHINA_CMEF91_2025("CMEF第91届中国国际医疗器械博览会", "flag_china_cmef91_2025", 2),
    CHINA_PACK_CONTAINERS_2025("2025中国包装容器展", "flag_china_pack_containers_2025", 3),
    CHINA_PLASTIC_RUBBER_2025("2025全球领先国际塑料橡胶展", "flag_china_plastic_rubber_2025", 4),
    CHINA_ELECTRONICA_2025("慕尼黑上海电子展（electronica China）", "flag_china_electronica_2025", 5),
    CHINA_DYE_INDUSTRY_2025("第24届中国国际染料工业及有机颜料、纺织化学品展览会", "flag_china_dye_industry_2025", 6),
    CHINA_BEIJING_MODEL23_2025("2025第二十三届中国北京国际模型博览会", "flag_china_beijing_model23_2025", 7),
    CHINA_IE_EXPO26_2025("第26届中国环博会 IE expo China 2025", "flag_china_ie_expo26_2025", 8),
    CHINA_NEPCON_2025("NEPCON China 2025亚洲电子生产设备暨微电子展工业展览会", "flag_china_nepcon_2025", 9),
    CHINA_CDIIF_2025("CDIIF 2025成都国际工业博览会", "flag_china_cdiif_2025", 10),
    CHINA_HEALTH34_2025("2025第34届（北京）中国国际健康产业博览会", "flag_china_health34_2025", 11),
    CHINA_INTERTRAFFIC_2025("Intertraffic China 2025第十八届国际交通工程、智能交通技术与设施展览会", "flag_china_intertraffic_2025", 12),
    CHINA_REFRIGERATION_2025("第三十六届国际制冷、空调、供暖、通风及食品冷冻加工展览会", "flag_china_refrigeration_2025", 13),
    CHINA_CIMT_2025("第十九届中国国际机床展览会（CIMT2025）", "xxxxxx", 14),

    CHINA_SMART_CAR_TECH_2025("2025中国智能汽车技术展", "flag_china_smart_car_tech_2025", 15),
    CHONGQING_SEMICONDUCTOR_2024("2024第六届全球半导体产业（重庆）博览会", "flag_chongqing_semiconductor_2024", 16),
    CANGZHOU_CNC_EQUIPMENT_2025("2025第九届沧州国际数控机床及智能装备展览会", "flag_cangzhou_cnc_equipment_2025", 17),
    CHINA_RETAIL_2025("中国零售业博览会", "flag_china_retail_2025", 18),
    ZHENGZHOU_HARDWARE_ELECTRICAL_2025("第 21 届郑州五金机电博览会", "flag_zhengzhou_hardware_electrical_2025", 19),
    CHONGQING_LIJIA_EQUIPMENT_2025("重庆立嘉国际智能装备展览会", "flag_chongqing_lijia_equipment_2025", 20),
    BEIJING_PRINTING_TECH_2025("第十一届北京国际印刷技术展览会（CHINA PRINT 2025）", "flag_beijing_printing_tech_2025", 21),
    ZHEJIANG_MEDICAL_DEVICE_2025("2025 第 8 届浙江国际医疗器械展览会", "flag_zhejiang_medical_device_2025", 22),
    GUIZHOU_ENERGY_2025("2025中国贵州国际能源产业博览交易会", "flag_guizhou_energy_2025", 23),
    WORLD_GAS_CONFERENCE_2025("第 29 届世界燃气大会", "flag_world_gas_conference_2025", 24),
    SIAL_FOOD_DRINK_SHANGHAI_2025("2025 SIAL 西雅国际食品和饮料展览会（上海）", "flag_sial_food_drink_shanghai_2025", 25),
    CHINA_CASTING_2025("第二十三届中国国际铸造博览会", "flag_china_casting_2025", 26),
    CHINA_DIE_CASTING_2025("第十八届中国国际压铸工业展览会", "flag_china_die_casting_2025", 27),
    SHANGHAI_BUILDING_DECORATION_2025("2025 中国（上海）国际建筑装饰博览会", "flag_shanghai_building_decoration_2025", 28),
    API_CHINA_PHARMA_EQUIPMENT_2025("API China 第92届中国制药设备展", "flag_api_china_pharma_equipment_2025", 29),
    XIAN_FURNITURE_2025("第24届西安国际家具博览会", "flag_xian_furniture_2025", 30),
    SHANGHAI_FASTENER_INDUSTRY_2025("中国·上海国际紧固件工业博览会", "flag_shanghai_fastener_industry_2025", 31),
    SPORTING_GOODS_2025("2025（第42届）中国国际体育用品博览会", "flag_sporting_goods_2025", 32),
    ANHUI_PLASTIC_2025("2025 第六届中国安徽国际塑料产业博览会", "flag_anhui_plastic_2025", 33),
    SHANDONG_SMART_AGRICULTURE_2025("2025 中国山东国际智慧农业博览会", "flag_shandong_smart_agriculture_2025", 34),
    SHANGHAI_METROLOGY_TESTING_2025("2025 第七届上海国际计量测试技术与设备博览会", "flag_shanghai_metrology_testing_2025", 35),
    ZHENGZHOU_AUTO_PARTS_2025("2025 第十六届中国（郑州）汽车零部件及产业服务博览会", "flag_zhengzhou_auto_parts_2025", 36),
    QINGDAO_FOOD_PACKAGING_2025("青岛国际食品加工和包装机械展览会", "flag_qingdao_food_packaging_2025", 37),
    NINGBO_CROSSBORDER_ECOMMERCE_2025("中国（宁波）出口跨境电商博览会", "flag_ningbo_crossborder_ecommerce_2025", 38),
    WENZHOU_HARDWARE_SMARTLOCK_2025("2025 中国（温州）国际五金、智能锁具展览会", "flag_wenzhou_hardware_smartlock_2025", 39),

    ;


    private final String tradeshowName;
    private final String tagName;
    private final int tagVal;

    TradeshowTagEnum(String tradeshowName, String tagName, int tagVal) {
        this.tradeshowName = tradeshowName;
        this.tagName = tagName;
        this.tagVal = tagVal;
    }

    public String getTradeshowName() {
        return tradeshowName;
    }

    public String getTagName() {
        return tagName;
    }

    public int getTagVal() {
        return tagVal;
    }

    public static Optional<TradeshowTagEnum> fromTradeshowName(String tradeshowName) {
        for (TradeshowTagEnum e : values()) {
            if (e.tradeshowName.equals(tradeshowName.trim())) {
                return Optional.of(e);
            }
        }
        return Optional.empty();
    }
}
