package com.ce.scrm.center.service.third.entity.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2024/12/9 11:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerAddNotValidThirdDto implements Serializable {

	/**
	 * 客户id
	 */
	private String customerId;

	/**
	 * 源数据ID
	 */
	private String sourceDataId;

	/**
	 * 客户类型：1、国内企业，2、个人，3、国外及港澳台
	 */
	private Integer customerType;

	/**
	 * 客户/企业名称
	 */
	private String customerName;

	/**
	 * 证件类型：1、营业执照，2、身份证，3、军人身份证，4、护照，5、港澳通行证
	 */
	private Integer certificateType;

	/**
	 * 证件编码
	 */
	private String certificateCode;

	/**
	 * 登记状态
	 */
	private String checkInState;

	/**
	 * 成立日期
	 */
	private Date establishDate;

	/**
	 * 注册资本
	 */
	private String registerCapital;

	/**
	 * 实缴资本
	 */
	private String paidInCapital;

	/**
	 * 组织机构代码
	 */
	private String organizationCode;

	/**
	 * 工商注册号 
	 */
	private String registerNo;

	/**
	 * 纳税人识别号
	 */
	private String taxpayerNo;

	/**
	 * 企业类型
	 */
	private String enterpriseType;

	/**
	 * 营业开始时间
	 */
	private Date openStartTime;

	/**
	 * 营业结束时间
	 */
	private Date openEndTime;

	/**
	 * 纳税人资质
	 */
	private String taxpayerQualification;

	/**
	 * 人员规模
	 */
	private String staffScale;

	/**
	 * 参保人数
	 */
	private String insuredNum;

	/**
	 * 核准日期
	 */
	private Date approveDate;

	/**
	 * 省编码
	 */
	private String provinceCode;

	/**
	 * 省名称
	 */
	private String provinceName;

	/**
	 * 市编码
	 */
	private String cityCode;

	/**
	 * 市名称
	 */
	private String cityName;

	/**
	 * 区编码
	 */
	private String districtCode;

	/**
	 * 区名称
	 */
	private String districtName;

	/**
	 * 登记机关
	 */
	private String registrationAuthority;

	/**
	 * 进出口企业代码
	 */
	private String importExportEnterpriseCode;

	/**
	 * 一级国标行业编码
	 */
	private String firstIndustryCode;

	/**
	 * 一级国标行业名称
	 */
	private String firstIndustryName;

	/**
	 * 二级国标行业编码
	 */
	private String secondIndustryCode;

	/**
	 * 二级国标行业名称
	 */
	private String secondIndustryName;

	/**
	 * 注册地址
	 */
	private String registerAddress;

	/**
	 * 经营范围
	 */
	private String businessScope;

	/**
	 * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
	 */
	private Integer presentStage;

	/**
	 * 客户创建方式：1、大数据，2、商务创建，3、市场商机
	 */
	private Integer createWay;

	/**
	 * 客户来源
	 */
	private String labelFrom;

	/**
	 * 删除标记：1、删除，0、未删除
	 */
	private Integer deleteFlag;

	/**
	 * 创建者凭证
	 */
	private String creatorKey;

	/**
	 * 创建者
	 */
	private String creator;

	/**
	 * 更新人凭证
	 */
	private String operatorKey;

	/**
	 * 更新人
	 */
	private String operator;
}
