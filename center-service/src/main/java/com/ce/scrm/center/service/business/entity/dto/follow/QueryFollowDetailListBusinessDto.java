package com.ce.scrm.center.service.business.entity.dto.follow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class QueryFollowDetailListBusinessDto implements Serializable {

    private String subId;

    private String buId;

    private String deptId;

    private String salerId;

    private Date followTimeStart;

    private Date followTimeEnd;

    private String salesStage;

    /**
     * 跟进阶段多选筛选
     */
    private List<String> salesStageList;

    private Date predictSignDateStart;

    private Date predictSignDateEnd;

    private Date predictAccountDateStart;

    private Date predictAccountDateEnd;

    /**
     * 商务月参数
     */
    private String businessMonth;

    /**
     * 类型
     */
    private String type;

    /**
     * 当前页
     */
    private Integer currentPage = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

//----------------------
    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 员工名称
     */
    private String loginEmployeeName;
    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;

    /**
     * 事业部id
     */
    private String loginBuId;

    /**
     * 事业部名称
     */
    private String loginBuName;

    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;
    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;

    /**
     * 职位
     */
    private String loginPosition;

}
