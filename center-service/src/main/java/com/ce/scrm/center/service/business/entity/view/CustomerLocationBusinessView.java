package com.ce.scrm.center.service.business.entity.view;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/4/1 10:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerLocationBusinessView implements Serializable {

    /**
     * 客户位置
     */
    private String customerLocationStr;

    /**
     * 客户ID
     */
    private String customerId;

}
