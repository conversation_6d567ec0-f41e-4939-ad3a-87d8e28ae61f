/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.domain.type;

/**
 * 分组 业务类型
 * 业务类型 1：作品 2：素材
 *
 * <AUTHOR>
 */
public enum GroupBusType {
    CREATION(1, "作品"),
    MATERIAL(2, "素材");
    private final int value;
    private final String title;

    GroupBusType(int value, String title) {
        this.value = value;
        this.title = title;
    }

    public static GroupBusType of(Integer busType) {
        for (GroupBusType type : GroupBusType.values()) {
            if (type.getValue() == busType) {
                return type;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getTitle() {
        return title;
    }
}
