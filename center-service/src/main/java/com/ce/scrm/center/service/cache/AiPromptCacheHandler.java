package com.ce.scrm.center.service.cache;

import cn.ce.cesupport.enums.YesOrNoEnum;
import com.ce.scrm.center.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.center.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.center.dao.entity.AiPromptInfo;
import com.ce.scrm.center.dao.service.AiPromptInfoService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
* @Description: ai提示词
* @Author: lijinpeng
* @Date: 2025/2/20 13:39
* @version 1.0
*/
@Component
public class AiPromptCacheHandler extends AbstractStringCacheHandler<AiPromptInfo> {

    @Resource
    private AiPromptInfoService aiPromptInfoService;

    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.AI_PROMPT_CACHE;
    }

    @Override
    protected Class<AiPromptInfo> getClazz() {
        return AiPromptInfo.class;
    }

    /*
     * @Description customerKey = "1"
     * <AUTHOR>
     * @date 2025/2/20 13:49
     * @param customerKey
     * @return com.ce.scrm.center.dao.entity.AiPromptInfo
     */
    @Override
    protected AiPromptInfo queryDataBySource(String promptId) {
        return aiPromptInfoService.lambdaQuery()
                .eq(AiPromptInfo::getId, Long.parseLong(promptId))
                .eq(AiPromptInfo::getStartFlag, YesOrNoEnum.YES.getCode())
                .eq(AiPromptInfo::getDeleteFlag, YesOrNoEnum.NO.getCode())
                .last("limit 1")
                .one();
    }

}
