package com.ce.scrm.center.service.business.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class CustomerLeadsDubboView implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 数据来源 0:crm原有渠道  1:营销活动 2:手动录入
     */
    private Integer dataFromSource;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * leads类别
     */
    private String leadsType;

    /**
     * leads code
     */
    private String leadsCode;

    /**
     * leads 来源
     */
    private String leadsSource;

    /**
     * 省
     */
    private String provinceCode;
    /**
     *  市
     */
    private String cityCode;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 线索类型
     */
    private String clueType;

    /**
     * 渠道
     */
    private String channel;
    /**
     * 活动
     */
    private String activity;

    /**
     * 端口
     */
    private String port;


    /**
     * 意向产品
     */
    private String intentionProduct;
    /**
     * 需求备注
     */
    private String demandRemark;

    /**
     * 附件url
     */
    private String attachmentUrls;

    /**
     * leads url
     */
    private String leadsUrl;

    /**
     * Leads来源说明
     */
    private String leadsDesc;


    /**
     * 联系人
     */
    private String linkmanName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 更多信息
     */
    private String moreInfo;

    /**
     * 创建者
     */
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     *联系人id
     */
    private String contactId;
    /**
     * 分发时间
     */
    private Date dispatchTime;

    /**
     * 来源URL
     */
    private String sourceUrl;

    /**
     * 落地URL
     */
    private String landUrl;

    /**
     * 备注
     */
    private String remark;

    private String others;

}