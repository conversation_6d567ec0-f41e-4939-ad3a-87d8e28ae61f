package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 网站信息
 * @Author: lijinpeng
 * @Date: 2025/2/18 11:32
 */
@Data
public class BigDataWebsiteDto implements Serializable {

    private String entname;
    private int total_count;
    private int total_page;
    private int page;

    @JSONField(name = "网站列表")
    private List<ListWebsite> list;

    @NoArgsConstructor
    @Data
    public static class ListWebsite {
        /**
         * 网站域名
         */
        @JSONField(serialize = false)
        private String site_domain;
        /**
         * 许可证号
         */
        @JSONField(serialize = false)
        private String license_key;
        /**
         * 许可证序号
         */
        @JSONField(serialize = false)
        private String license_no;
        /**
         * 网站类型
         */
        @JSONField(serialize = false)
        private String site_type;
        /**
         * 网站名称
         */
        @JSONField(name = "网站名称")
        private String site_name;
        /**
         * 来源网站名称
         */
        @JSONField(serialize = false)
        private String web_name;
        /**
         * 企业类型
         */
        @JSONField(serialize = false)
        private String company_type;
        /**
         * 网站首页网址 去掉https或者http
         */
        @JSONField(name = "网站首页网址")
        private String site_home;
        /**
         * 主页是否可以访问
         */
        @JSONField(serialize = false)
        private String site_home_access;
        /**
         * 审核时间
         */
        @JSONField(serialize = false)
        private String check_date;
        /**
         * 公司名称
         */
        @JSONField(serialize = false)
        private String company_name;
        /**
         * 可信网对网站的评估（9:标记官网+可信网;8：标记官网;7：无官网标记，只有可信网标记
         */
        @JSONField(serialize = false)
        private String kxsource;
        /**
         * Alexa网站排名, 值越小表示优先级越高
         */
        @JSONField(name = "Alexa网站排名,值越小表示优先级越高")
        private String alexa_rank;
        /**
         * 网站名是否是公司名
         */
        @JSONField(serialize = false)
        private String site_eq_com;
        /**
         * 备案号尾数
         */
        @JSONField(serialize = false)
        private String tail_num;
        /**
         * 百度权重PC端
         */
        @JSONField(name = "百度权重PC端")
        private String weight_pc_baidu;
        /**
         * 百度权重移动端
         */
        @JSONField(name = "百度权重移动端")
        private String weight_mobile_baidu;
        /**
         * 360权重PC端
         */
        @JSONField(name = "360权重PC端")
        private String weight_pc_qihoo;
        /**
         * 360权重移动端
         */
        @JSONField(name = "360权重移动端")
        private String weight_mobile_qihoo;
        /**
         * sougou权重PC端
         */
        @JSONField(name = "sougou权重PC端")
        private String weight_pc_sougou;
        /**
         * sougou权重移动端
         */
        @JSONField(name = "sougou 权重移动端")
        private String weight_mobile_sougou;
        /**
         * 域名创建时间
         */
        @JSONField(serialize = false)
        private String domain_create_time;
        /**
         * 域名过期时间
         */
        @JSONField(serialize = false)
        private String domain_expire_time;
        /**
         * 域名年龄:当前日期-注册日期
         */
        @JSONField(name = "域名年龄:当前日期-注册日期")
        private String domain_age;
        /**
         * 百度总收录量
         */
        @JSONField(name = "百度总收录量")
        private String total_collection_baidu;
        /**
         * 搜狗总收录量
         */
        @JSONField(name = "搜狗总收录量")
        private String total_collection_sougou;
        /**
         * 360总收录量
         */
        @JSONField(name = "360总收录量")
        private String total_collection_qihoo;
        /**
         * 神马总收录量
         */
        @JSONField(name = "神马总收录量")
        private String total_collection_shenma;
        /**
         * goole总收录量
         */
        @JSONField(name = "goole总收录量")
        private String total_collection_google;
        /**
         * 神马权重
         */
        @JSONField(name = "神马权重")
        private String weight_shenma;
        /**
         * 头条权重
         */
        @JSONField(name = "头条权重")
        private String weight_toutiao;
        /**
         * ip地址所在地区
         */
        @JSONField(serialize = false)
        private String ip_address_area;
        /**
         * 可信百科认证（1为认证，0为未认证）
         */
        @JSONField(name = "可信百科认证")
        private String auth_credible_baike;
        /**
         * 创宇认证（1为认证，0为未认证）
         */
        @JSONField(name = "创宇认证")
        private String auth_chuangyu;
        /**
         * 百度信誉认证（1为认证，0为未认证）
         */
        @JSONField(name = "百度信誉认证")
        private String auth_baidu_reputation;
        /**
         * 百度反链数
         */
        @JSONField(name = "百度反链数")
        private String inverse_chain_baidu;
        /**
         * sougou反链数
         */
        @JSONField(name = "sougou反链数")
        private String inverse_chain_sougou;
        /**
         * 360反链数
         */
        @JSONField(name = "360反链数")
        private String inverse_chain_qihoo;
        /**
         * 神马反链数
         */
        @JSONField(name = "神马反链数")
        private String inverse_chain_shenma;
        /**
         * goole反链数
         */
        @JSONField(name = "goole反链数")
        private String inverse_chain_google;
        /**
         * 页面tdk信息标题
         */
        @JSONField(serialize = false)
        private String seo_title;
        /**
         * 页面tdk信息关键词
         */
        @JSONField(serialize = false)
        private String seo_keywords;
        /**
         * 网站速度（ms）
         */
        @JSONField(serialize = false)
        private String website_speed;
        /**
         * 协议类型
         */
        @JSONField(serialize = false)
        private String protocol;
        /**
         * 页面类型
         */
        @JSONField(serialize = false)
        private String content_type;
        /**
         * 服务器类型
         */
        @JSONField(serialize = false)
        private String server_type;
        /**
         * 该域名是否安全,1=安全 0=不安全
         */
        @JSONField(serialize = false)
        private String is_domain_safe;
    }

}
