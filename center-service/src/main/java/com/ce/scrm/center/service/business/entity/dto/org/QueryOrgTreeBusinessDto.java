package com.ce.scrm.center.service.business.entity.dto.org;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: scrm-center
 * @ClassName QueryOrgTreeBusinessDto
 * @description:
 * @author: lijinpeng
 * @create: 2025-08-11 10:13
 * @Version 1.0
 **/
@Data
public class QueryOrgTreeBusinessDto implements Serializable {

    //-----------

    /**
     * 员工id
     */
    private String loginEmployeeId;

    /**
     * 员工名称
     */
    private String loginEmployeeName;
    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;

    /**
     * 事业部id
     */
    private String loginBuId;

    /**
     * 事业部名称
     */
    private String loginBuName;

    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;
    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;
    /**
     * 手机号
     */
    private String loginMobile;
    /**
     * 工作邮箱
     */
    private String loginWorkMail;
    /**
     * 职位
     */
    private String loginPosition;

}
