package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 大数据结果数据
 * <AUTHOR>
 * @date 2024/1/15 11:15
 * @version 1.0.0
 **/
@Data
public class BigDataData<T> implements Serializable {
    /**
     * 状态返回码
     *  成功:200
     *  失败:非200
     *  三方错误码：
     *  10000 请求参数错误
     *  10001 未查询到相关的企业
     */
    private Integer code;
    /**
     * 返回的数据
     */
    private T data;
    /**
     * 错误信息
     */
    private String message;
}