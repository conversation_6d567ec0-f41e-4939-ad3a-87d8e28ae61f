package com.ce.scrm.center.service.enums;

import cn.ce.cesupport.framework.base.enums.BaseCustomEnum;


/**
 * Description: 流转日志相关枚举，对应
 * 				只摘取了源枚举类{@link com.ce.scrm.extend.service.enums.ConvertRelationEnum}的部分枚举值
 * @author: JiuDD
 * date: 2024/7/11
 */
public enum ConvertRelationEnum implements BaseCustomEnum<Integer> {
	SALER_RELEASE_MYCUST_TO_CUSTPOOL("商务释放我的客户到客户池", 152),
	SYSTEM_RELEASE_MYCUST_TO_CUSTPOOL("超时系统释放我的客户到客户池", 158),
	CIRCULATION("成交客户流转", 632),
	LOSS_ZJDFP("成交客户流失-进总监待分配", 633),
	LOSS_KHC("成交客户流失-进客户池", 634),
	LOSS_ZJSF("总监释放流失客户", 635),
	SJ_ANEW_ASSIGN("商机重盘", 639),
	CUSTOMER_LAYER_CHANGE("客户分层（商务）变化", 640),
	SJ_UNTREATED_RECEIPT("商机客户由于没打回执，自动释放到客户池", 641),
	SJ_UNTREATED_CONFIRM("商机客户由于没做保护确认，自动释放到客户池", 642),
	SJ_UNTREATED_CONFIRM_NEXT_SALER("超3天未确认，流转", 643),


//	ABM_SJ_DISTRIBUTE_ZONGJIAN("商机下发到总监", 700),
//	ABM_SJ_DISTRIBUTE_jingli("商机下发到经理", 701),
//	ABM_SJ_DISTRIBUTE_bu("商机下发到事业部总监", 702),
	ABM_SJ_DISTRIBUTE_SALER("商机下发到销售", 703),
	ABM_ASSIGN_SALER("分配线索给售前", 704),
	ABM_MARE_LIBERUM_MY_CUSTOMER("售前公海捞取客户", 705),
	ABM_ASSIGN_CLUE_TO_BUSINESS("分配线索给商务", 706),
	ABM_CROSS_BORDER_PUSH_BUSINESS("跨境推送商机", 707),
	ABM_SJ_HUIZHI_CHAOQI_RELEASE("商机回执超期释放客户", 800),
	ABM_SJ_GENGJIN_CHAOQI_RELEASE("商机48小时未跟进释放客户", 801),
	ABM_SJ_MEGER_CUSTOMER_INFO("跨境商机下发时,客户名称重复导致信息合并", 802),
	SIGNED("客户签单", 165),
    ;

	private String lable;
	private Integer value;

	ConvertRelationEnum(String lable, Integer value) {
		this.lable = lable;
		this.value = value;
	}

	public String getLable() {
		return lable;
	}

	public void setLable(String lable) {
		this.lable = lable;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

    public static ConvertRelationEnum getConvertRelationEnumByValue(Integer value) {
        ConvertRelationEnum[] enums = ConvertRelationEnum.values();
        for (ConvertRelationEnum convertRelationEnum : enums) {
            if (convertRelationEnum.getValue().intValue() == value.intValue()) {
                return convertRelationEnum;
            }
        }
        return null;
    }
	
	public static Integer getConvertRelationValueByType(String lable) {
		return null;
	}
}
