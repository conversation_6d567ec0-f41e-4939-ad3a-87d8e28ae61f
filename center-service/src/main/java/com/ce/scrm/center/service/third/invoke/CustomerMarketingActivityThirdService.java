package com.ce.scrm.center.service.third.invoke;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerMarketingActivityCreateThirdDto;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerMarketingActivityQueryThirdDto;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerMarketingActivityUpdateThirdDto;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.service.yml.ThirdCustomerConfig;
import com.ce.scrm.customer.dubbo.api.ICustomerMarketingActivitiesDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerDistributeSdrRecordDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerMarketingActivitiesCreateDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerMarketingActivitiesPageDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerMarketingActivitiesUpdateDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerMarketingActivitiesCreatorSelectDubboView;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerMarketingActivitiesDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 客户营销活动三方业务
 * <AUTHOR>
 * @date 2025/7/11 17:40
 * @version 1.0.0
 **/
@Slf4j
@Service
public class CustomerMarketingActivityThirdService {

	@DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
	private ICustomerMarketingActivitiesDubbo iCustomerMarketingActivitiesDubbo;

	@Resource
	private ThirdCustomerConfig thirdCustomerConfig;
	@Resource
	private CustomerMarketingActivityThirdService customerMarketingActivityThirdService;


	/**
	 * 创建跨境营销活动
	 * @params createThirdDto
	 */
	public void createActivity(CustomerMarketingActivityCreateThirdDto createThirdDto) {
		CustomerMarketingActivitiesCreateDubboDto createActDto = BeanCopyUtils.convertToVo(createThirdDto, CustomerMarketingActivitiesCreateDubboDto.class);
		List<CustomerDistributeSdrRecordDubboDto> distributeSdrRecordDubboDtos = BeanCopyUtils.convertToVoList(createThirdDto.getDistributeSdrRecordThirdDtos(), CustomerDistributeSdrRecordDubboDto.class);
		createActDto.setDistributeSdrRecordDubboDtos(distributeSdrRecordDubboDtos);
		createActDto.setSourceKey(thirdCustomerConfig.getKey());
		createActDto.setSourceSecret(thirdCustomerConfig.getSecret());
		log.info("营销活动创建，参数={}", JSON.toJSONString(createActDto));
		DubboResult<?> activity = iCustomerMarketingActivitiesDubbo.createActivity(createActDto);
		if(!activity.checkSuccess()) {
			log.warn("创建跨境市场营销活动失败，dubbo接口异常，参数为:{},返回值:{}", JSON.toJSONString(createActDto), JSON.toJSONString(activity.getMsg()));
			throw new RuntimeException("创建跨境市场营销活动失败, " + activity.getMsg());
		}
	}

	/**
	 * 修改跨境营销活动
	 * @params createThirdDto
	 */
	public void updateActivity(CustomerMarketingActivityUpdateThirdDto updateThirdDto) {
		CustomerMarketingActivitiesUpdateDubboDto updateActDto = BeanCopyUtils.convertToVo(updateThirdDto, CustomerMarketingActivitiesUpdateDubboDto.class);
		updateActDto.setSourceKey(thirdCustomerConfig.getKey());
		updateActDto.setSourceSecret(thirdCustomerConfig.getSecret());
		DubboResult<?> updateActivity = iCustomerMarketingActivitiesDubbo.updateActivity(updateActDto);
		if(!updateActivity.checkSuccess()) {
			log.warn("修改跨境市场营销活动失败，dubbo接口异常，参数为:{},返回值:{}", JSON.toJSONString(updateActDto), JSON.toJSONString(updateActivity.getMsg()));
			throw new RuntimeException("修改跨境市场营销活动失败, " + updateActivity.getMsg());
		}
	}


	/**
	 * 获取跨境营销活动列表（分页）按创建时间倒序
	 * @params queryThirdDto
	 */
	public DubboPageInfo<CustomerMarketingActivitiesDubboView> getCustomerActivitiesPage(CustomerMarketingActivityQueryThirdDto queryThirdDto) {
		CustomerMarketingActivitiesPageDubboDto reqDto = BeanCopyUtils.convertToVo(queryThirdDto, CustomerMarketingActivitiesPageDubboDto.class);
		reqDto.setSourceKey(thirdCustomerConfig.getKey());
		reqDto.setSourceSecret(thirdCustomerConfig.getSecret());
		DubboResult<DubboPageInfo<CustomerMarketingActivitiesDubboView>> dubboPageIoResult = iCustomerMarketingActivitiesDubbo.activitiesPages(reqDto);
		if(!dubboPageIoResult.checkSuccess()) {
			log.warn("获取跨境市场营销活动列表失败，dubbo接口异常，参数为:{},返回值:{}", JSON.toJSONString(reqDto), JSON.toJSONString(dubboPageIoResult));
			return new DubboPageInfo<>();
		}
		return dubboPageIoResult.getData();
	}

	/**
	 * 获取跨境营销活动详情
	 * @params queryThirdDto
	 */
	public CustomerMarketingActivitiesDubboView getCustomerActivitiesDetail(Long activityId) {
		DubboResult<CustomerMarketingActivitiesDubboView> detailDubboRs = iCustomerMarketingActivitiesDubbo.activityDetail(activityId);
		if(!detailDubboRs.checkSuccess()) {
			log.warn("获取跨境市场营销活动详情失败，dubbo接口异常，参数为:{},返回值:{}", activityId, JSON.toJSONString(detailDubboRs));
			return null;
		}
		return detailDubboRs.getData();
	}

	/**
	 * 下发后，修改下发状态
	 * @param customerId 客户id
	 * @return 是否下发成功
	 */
	public Boolean updateDistributeState(String customerId) {
		DubboResult<Boolean> dubboResult = iCustomerMarketingActivitiesDubbo.updateDistributeState(customerId);
		if(!dubboResult.checkSuccess()) {
			log.error("修改下发状态失败，dubbo接口异常，参数为:{},返回值:{}", customerId, JSON.toJSONString(dubboResult));
			return false;
		}
		return dubboResult.getData();
	}

	/**
	 * 营销活动列表，创建人下拉选
	 * @return
	 */
	public List<CustomerMarketingActivitiesCreatorSelectDubboView> creatorSelect() {
		DubboResult<List<CustomerMarketingActivitiesCreatorSelectDubboView>> listDubboResult = iCustomerMarketingActivitiesDubbo.creatorSelect();
		if(!listDubboResult.checkSuccess()) {
			log.error("获取创建人列表失败，dubbo接口异常,返回值:{}", JSON.toJSONString(listDubboResult));
			return Collections.emptyList();
		}
		return listDubboResult.getData();
	}

}