package com.ce.scrm.center.service.business.entity.dto.segment;

import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 根据角色查询分群入参条件
 * @Author: lijinpeng
 * @Date: 2025/2/27 14:24
 */
@Data
public class ClueSegmentPageBusinessDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 员工ID
	 */
	private String loginEmployeeId;

	/**
	 * 职位
	 */
	private String loginPosition;

	/**
	 * 页码
	 */
	private Integer pageNum = 1;
	/**
	 * 每页数量
	 */
	private Integer pageSize = 10;

}
