/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.form;

import java.util.Map;

/**
 * 报名审核结果统计
 * "0": 2, // 待审核
 * "1": 0, // 审核通过
 * "2": 0 // 审核未通过
 */
public class EnrollmentAuditStat {

    private final Integer pending;

    private final Integer passed;

    private final Integer rejected;

    public EnrollmentAuditStat(Integer pending, Integer passed, Integer rejected) {
        this.pending = pending;
        this.passed = passed;
        this.rejected = rejected;
    }

    public EnrollmentAuditStat(Map<String, Integer> data) {
        this.pending = data.get("0");
        this.passed = data.get("1");
        this.rejected = data.get("2");
    }

    public Integer getPending() {
        return pending;
    }

    public Integer getPassed() {
        return passed;
    }

    public Integer getRejected() {
        return rejected;
    }

    @Override
    public String toString() {
        return "EnrollmentAuditStat{" +
                "pending=" + pending +
                ", passed=" + passed +
                ", rejected=" + rejected +
                '}';
    }
}
