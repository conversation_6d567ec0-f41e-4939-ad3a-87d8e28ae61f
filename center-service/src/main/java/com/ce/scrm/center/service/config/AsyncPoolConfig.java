package com.ce.scrm.center.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@Slf4j
public class AsyncPoolConfig {

	public static final Integer PROCESSOR_NUM = Runtime.getRuntime().availableProcessors();
	public static final String PREFIX = "Async-Thread-CRM-";
    @Bean
    public Executor crmAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
	    executor.setCorePoolSize(PROCESSOR_NUM);
	    executor.setMaxPoolSize(2 * PROCESSOR_NUM);
	    executor.setQueueCapacity(100);
	    executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix(PREFIX);
        // 是否等待所有线程执行完毕才关闭线程池，默认值为false。
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // awaitTerminationSeconds：waitForTasksToCompleteOnShutdown的等待的时长，默认值为0，即不等待。
        executor.setAwaitTerminationSeconds(30);
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}