package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 根据pid集合查询公司信息（数据来源搜客宝）
 * @Author: lijinpeng
 * @Date: 2024/12/12 13:56
 */
@Data
public class BigDataSkbCompanyView implements Serializable {

    /** 注册日期，以毫秒为单位的时间戳 */
    private Long esdate;

    /** 统一注册资本 */
    private String regCapUnify;

    /** 公司经营范围 */
    private String opScope;

    /** 公司联系地址 */
    private String contactAddress;

    /** 官方网站URL */
    private String officialWebsite;

    /** 公司名称 */
    private String entName;

    /** 省份代码 */
    private String province;

    /** 地区信息 */
    private String district;

    /** 次级地区信息 */
    private String secdistrict;

    /** PID（可能是标识符） */
    private String pid;

    /** 法人姓名 */
    private String legalPerson;

    /** 公司社会信用代码 */
    private String socialCreditCode;

    /** 用于各种目的的标志（根据需要自定义） */
    private String[] flag1;
    private String[] flag2;
    private String[] flag3;
    private String[] flag4;
    private String[] flag5;
    private String[] flag6;
    private String[] flag7;
    private String[] flag8;

    /**
     * 经营状态
     */
    private String entStatus;

    /** 行业类别 */
    private String firstIndustry;

    /** 销售发布日期 */
    private Long salesReleaseDate;

}
