package com.ce.scrm.center.service.eqixiu.sdk.domain;

/**
 * 访客
 */
public class Customer {

    private long id;

    /**
     * 授权用户类型，1微信，2手机，3自定义
     */
    private int type;

    /**
     * 易企秀用户唯一标识
     */
    private String encodeId;

    /**
     * userType为3时，客户侧 访客ID
     */
    private String thirdUserId;

    /**
     * appId类型，1、易企秀 ；非 1，非易企秀
     */
    private int appIdType;


    /**
     * 微信信息,appId
     */
    private String thirdAppId;

    /**
     * userType为1时：微信openid
     */
    private String openId;

    /**
     * 微信信息，unionId
     */
    private String unionId;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * userType为1时：微信昵称
     */
    private String nickName;

    /**
     * 手机号，userType为2时
     */
    private String mobile;

    private String headImgUrl;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getEncodeId() {
        return encodeId;
    }

    public void setEncodeId(String encodeId) {
        this.encodeId = encodeId;
    }

    public String getThirdUserId() {
        return thirdUserId;
    }

    public void setThirdUserId(String thirdUserId) {
        this.thirdUserId = thirdUserId;
    }

    public int getAppIdType() {
        return appIdType;
    }

    public void setAppIdType(int appIdType) {
        this.appIdType = appIdType;
    }

    public String getThirdAppId() {
        return thirdAppId;
    }

    public void setThirdAppId(String thirdAppId) {
        this.thirdAppId = thirdAppId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getHeadImgUrl() {
        return headImgUrl;
    }

    public void setHeadImgUrl(String headImgUrl) {
        this.headImgUrl = headImgUrl;
    }

    @Override
    public String toString() {
        return "Customer{" +
                "id=" + id +
                ", type=" + type +
                ", encodeId='" + encodeId + '\'' +
                ", thirdUserId='" + thirdUserId + '\'' +
                ", appIdType=" + appIdType +
                ", thirdAppId='" + thirdAppId + '\'' +
                ", openId='" + openId + '\'' +
                ", unionId='" + unionId + '\'' +
                ", corpId='" + corpId + '\'' +
                ", nickName='" + nickName + '\'' +
                ", mobile='" + mobile + '\'' +
                ", headImgUrl='" + headImgUrl + '\'' +
                '}';
    }
}
