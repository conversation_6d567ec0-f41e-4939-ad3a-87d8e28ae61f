package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 查询主要人员关联的企业信息
 * @Author: xukang
 * @Date: 2024/12/19
 */
@Data
public class BigDataAffiliatedEnterpriseView implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 关联企业数量
	 */
	private Integer total;

	/**
	 * 关联企业详情列表
	 */
	private List<CustomerAffiliatedEnterpriseDetailBusinessView> items;


	/**
	 * 详情信息
	 */
	@Data
	public static class CustomerAffiliatedEnterpriseDetailBusinessView implements Serializable {
		private static final long serialVersionUID = 1L;

		/**
		 * 企业名称
		 */
		private String entName;

		/**
		 * 注册资本
		 */
		private String  regCapital;

		/**
		 * 职务/职位
		 */
		private String position;

		/**
		 * 任职状态
		 */
		private String  positionStatus;

		/**
		 * 市场
		 */
		private String marketName;

		/**
		 * 客户保护状态
		 */
		private String customerState;

		/**
		 * 是否可以保护 1是 0否
		 */
		private Integer protectFlag;
	}

}
