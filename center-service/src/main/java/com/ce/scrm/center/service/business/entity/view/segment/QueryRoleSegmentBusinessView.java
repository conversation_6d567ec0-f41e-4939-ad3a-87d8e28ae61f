package com.ce.scrm.center.service.business.entity.view.segment;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 角色查询分群信息
 * @Author: lijinpeng
 * @Date: 2025/2/27 14:30
 */
@Data
public class QueryRoleSegmentBusinessView implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 搜客宝PID
     */
    private String pid;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 已分配部门
     */
    private String allocatedDeptNameStr;

    /**
     * 已分配商务
     */
    private String allocatedSalerNameStr;

    /**
     * 客户状态
     */
    private String customerStateStr;

    /**
     * 首次保护时间（仅初次有效）
     */
    private Date protectTime;

    /**
     * 下发后是否拜访 是、否
     */
    private String visitFlagStr;

    /**
     * 最近一次拜访时间（根据打卡表同步）
     */
    private Date visitLastTime;

    /**
     * 客户位置
     */
    private String customerLocationStr;

    /**
     * 商务标注 0:未处理 1:有价值 2:无价值
     */
    private Integer salerCustomTag;

    /**
     * 客户处理情况
     */
    private String salerCustomTagStr;

    /**
     * 一级国标行业编码
     */
    private String firstIndustryCode;

    /**
     * 一级国标行业名称
     */
    private String firstIndustryName;

    /**
     * 二级国标行业编码
     */
    private String secondIndustryCode;

    /**
     * 二级国标行业名称
     */
    private String secondIndustryName;

    /**
     * 三级国标行业编码
     */
    private String thirdIndustryCode;

    /**
     * 三级国标行业名称
     */
    private String thirdIndustryName;

    /**
     * 四级国标行业编码
     */
    private String fourthIndustryCode;

    /**
     * 四级国标行业名称
     */
    private String fourthIndustryName;

    /**
     * 注册资本
     */
    private Double registerCapital;

    /**
     * 有无域名备案 1:是 0:否
     */
    private String icpFlagStr;

    /**
     * 有无进出口信用 1:是 0:否
     */
    private String jingchukouFlagStr;

    /**
     * 备注
     */
    private String remark;

}
