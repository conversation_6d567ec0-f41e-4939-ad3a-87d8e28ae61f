package com.ce.scrm.center.service.business.entity.view.protect;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 客户池分页查询结果
 * @Author: lijinpeng
 * @Date: 2025/1/15 10:40
 */
@Data
public class CustPoolQueryBusinessView implements Serializable {

    /** 客户ID */
    private String custId;

    private String id;

    private String custName;

    /** 客户类型（来源） */
    private Integer custType;

    /** 商务ID */
    private String salerId;

    /** 管家ID */
    private String gjId;

    /** 商务名称 */
    private String salerName;

    /** 管家姓名 */
    private String gjName;

    /** 部门ID */
    private String bussdeptId;

    /** 部门部门ID */
    private String gjDeptId;

    /** 部门名称 */
    private String bussdeptName;

    /** 部门ID */
    private String gjDeptName;

    /** 分司ID */
    private String subcompanyId;

    /** 分司Name */
    private String subcompanyName;

    /** 区域ID */
    private String areaId;

    /** 市场ID */
    private String markId;

    /** 市场NAME */
    private String markName;

    /** 释放原因 */
    private String reason;

    /** 释放原因 */
    private String reasonStr;

    /** 所属小行业 */
    private String industryClassSmall;

    /** 所属大行业 */
    private String industryClassBig;

    /** 所属小行业 */
    private String industryClassSmallStr;

    /** 所属大行业 */
    private String industryClassBigStr;

    /** 创建人ID */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新人ID */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    private String source;

    private Date establishDate;

    private Double registerAmount;

    /** 省 */
    private String province;

    /** 市 */
    private String city;

    /** 区 */
    private String region;

    private static final long serialVersionUID = 1L;
    /** 商机id */
    private String busiOppoId;

    /** 商机code */
    private String busioppoCode;

    /** 二级来源 */
    private Integer custSourceSub;

    /**
     * 是否已打卡
     */
    private Integer isClock;

    /**
     * 打卡所在省
     */
    private String clockProvince;

    /**
     * 打卡所在市
     */
    private String clockCity;

    /**
     * 打卡所在区
     */
    private String clockRegion;

    /**
     * 统一社会信用代码
     */
    private String uncid;

    /**
     * 保护阶段（1、保护；2、总监；3、经理；4、客户池）
     */
    private Integer status;

    /**
     * 分配时间
     */
    private Date assignTime;

    /**
     * 客户进待分配表的来源 0:异动；1：手动释放；2：系统自动释放；3：总监调整客户；4：商机客户
     */
    private Integer assignCustSource;

    /**
     * 分配超期时间
     */
    private Date assignDate;

    /**
     * 客户来源
     */
    private Integer custSource;

    /**
     * 保护时间
     */
    private Date protectTime;
    /**
     * 超期时间
     */
    private Date exceedTime;

    /**
     * 是否计算库容 (0占库容；1不占库容)
     */
    private Integer occupy;

    /** 绝对保护期 */
    private Date absoluteProtectTime;

    /**
     * 原部门ID
     */
    private String originalDeptId;

    /**
     * 注册资金
     */
    private String registerCapital;

}
