package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ClueAssignThirdView implements Serializable {

	private static final long serialVersionUID = -5962131379362641781L;

	//id
	private String id;
	//线索id
	private String clueId;
	//客户唯一标识
	private String uid;
	//客户id
	private String custId;
	//市场ID
	private String marketId;
	//区域id
	private String areaId;
	//分司Id
	private String subCompanyId;
	// 事业部id
	private String buId;
	//部门id
	private String deptId;
	//员工id
	private String empId;
	//当前所在状态1 总监  2 经理  3 商务
	private Integer status;
	//超期时间
	private Date exceedTime;
	//创建时间
	private Date createTime;
	//创建人
	private String createUser;
	//修改时间
	private Date updateTime;
	//修改人
	private String updateUser;
	//运营行业编码
	private String industryZhongqi;
	//线索类型
	private String bussOppType;
	//客户名称
	private String custName;
	//来源
	private String labelFrom;
	//企业id
	private String entId;
	//商务获客来源
	private Integer salerGetclueFrom;
	//跟进方式code
	private String visitTypeCode;
	//跟进方式name
	private String visitTypeName;
	//跟进时间
	private Date visitTime;
	//下次跟进时间
	private Date nextVisitTime;
	//线索跟进阶段
	private String clueVisitStage;
	//是否已读
	private Integer isReaded;
	//任务
	private String mission;
	//一级行业
	private String firstIndustry;
	//一级行业汉字
	private String firstIndustryStr;
	//二级行业
	private String secondIndustry;
	//二级行业汉字
	private String secondIndustryStr;
	//企业成立日期
	private String foundDate;
	//企业注册资本
	private String registerMoney;
	//企业注册资本(没有单位)
	private String registerMoneyNoUnit;
	//企业网址
	private String url;
	//地址
	private String address;
	//省
	private String provinceCode;
	//市
	private String cityCode;
	//区
	private String districtCode;
	//公司性质：国企  私企
	private String entProperty;
	//公司性质：国企  私企(汉字)
	private String entPropertyStr;
	//法人
	private String legalRepresent;
	//所属市场字符串
	private String marketStr;
	//是否有联系人
	private String hasLinkManOrNot;
	//是否有招聘信息
	private String hasRecuitOrNot;
	//是否有网站
	private String hasWebSiteOrNot;
	//是否为外贸企业
	private String isForeinTradeOrNot;
	//是否为集团
	private String isGroupOrNot;
	//是否为运营推荐筛选出来的
	private Integer isRecommendOrNot;
	//话术
	private String speechcraft;
	//联系人名称
	private String linkmanName;
	//联系人职位
	private String manPostName;
	//联系人手机号（必填项）
	private String linkmanPhonenum;
	//主营产品
	private String productName;
	/**
	 * 批次
	 */
	private String batchId;
	/**
	 * 操作类型
	 */
	private Integer optType;
	/**
	 * 运营推荐
	 */
	private String recommendType;

	//商务获客来源-二级
	private Integer salerGetclueFromSub;
}