package com.ce.scrm.center.service.business.entity.ai.crm;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Description: 客户跟进
 * @Author: lijinpeng
 * @Date: 2025/2/17 14:37
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerFollowView implements Serializable {

    /**
     * 意向等级 0无；1弱；2中；3强
     */
    @JSONField(name = "意向级别",serialize = false)
    private String intentionLevel;

    /**
     * 是否vip 0否；1是
     */
    @JSONField(name = "是否vip",serialize = false)
    private String vipFlag;

    /**
     * 预计成交金额
     */
    @JSONField(name = "预计成交金额",serialize = false)
    private BigDecimal expectDealAmount;

    /**
     * 跟进内容
     */
    @JSONField(name = "跟进内容")
    private String content;

    /**
     * 跟进时间
     */
    @JSONField(name = "跟进时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date followTime;

    /**
     * 跟进方式
     */
    @JSONField(name = "跟进方式",serialize = false)
    private String visitType;

    /**
     * 意向产品类型
     */
    @JSONField(name = "意向产品类型列表",serialize = false)
    private List<String> custDemandsList;

}
