package com.ce.scrm.center.service.enums;

import lombok.Getter;

/**
 * 跟进电话接通情况
 * <AUTHOR>
 * @date 2024/7/23
 * @version 1.0.0
 **/
@Getter
public enum FollowCallStatusEnum {

    REJECTED(1, "拒接"),
    INVALID_NUMBER(2, "号码错误"),
    HANGUP_IMMEDIATELY(3, "秒挂"),
    NO_ANSWER(4, "无人接听"),
    NEED_FOLLOW_UP(5, "接听需二次联系"),
    CONNECTED(6, "正常接听"),
    UNKNOWN(7, "无");


    private final Integer value;
    private final String label;

    FollowCallStatusEnum(Integer value, String label) {
        this.label = label;
        this.value = value;
    }
}
