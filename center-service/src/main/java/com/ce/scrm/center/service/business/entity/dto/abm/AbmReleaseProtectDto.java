package com.ce.scrm.center.service.business.entity.dto.abm;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * abm  释放客户保护关系
 */
@Data
public class AbmReleaseProtectDto implements Serializable {

    /***********************保护关系表 相关字段************************/

    /**
     * 客户id
     */
    private String custId;
    /**
     * 释放原因
     */
    private String releaseReason;

    /**
     * 操作人
     */
    private String operator;
    /***********************流转日志表 相关字段************************/

    /**
     * 流转类型code
     */
    private Integer convertType;

    /**
     * 流转类型desc
     */
    private String convertTypeDesc;


}