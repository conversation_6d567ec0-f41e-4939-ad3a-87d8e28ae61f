package com.ce.scrm.center.service.business.entity.dto.segment;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 添加客户条件
 * @Author: lijinpeng
 * @Date: 2025/3/4 18:25
 */
@Data
public class AddSegmentFavoriteBusinessDto implements Serializable {

    /**
     * segment_detail表id
     */
    @NotNull(message = "参数不能为空")
    private String segmentDetailId;

    //----------------员工信息

    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 员工名称
     */
    private String loginEmployeeName;
    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;

    /**
     * 事业部id
     */
    private String loginBuId;

    /**
     * 事业部名称
     */
    private String loginBuName;

    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;
    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;
    /**
     * 手机号
     */
    private String loginMobile;
    /**
     * 工作邮箱
     */
    private String loginWorkMail;
    /**
     * 职位
     */
    private String loginPosition;
    /**
     * 职级
     */
    private String loginJobGrade;

}
