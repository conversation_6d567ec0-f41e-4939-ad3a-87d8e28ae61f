package com.ce.scrm.center.service.eqixiu.sdk.domain;

import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;

/**
 * 易企秀回调密钥
 *
 * <AUTHOR>
 */
public class CallbackSecret {

    /**
     * 易企秀回调签名key,用于参数签名,防篡改
     */
    private String signatureKey;

    /**
     * 易企秀回调加密key,用于请求参数加密,避免明文传输参数
     */
    private String encodingKey;


    public CallbackSecret(String signatureKey, String encodingKey) {
        this.signatureKey = signatureKey;
        this.encodingKey = encodingKey;
        if (signatureKey == null || encodingKey == null){
            throw new KnownException("易企秀回调密钥未配置");
        }
    }

    public String getSignatureKey() {
        return signatureKey;
    }

    public String getEncodingKey() {
        return encodingKey;
    }
}
