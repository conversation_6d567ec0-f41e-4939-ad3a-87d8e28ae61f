package com.ce.scrm.center.service.third.invoke;

import cn.hutool.core.lang.UUID;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogPageBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogUpdateBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.WxMessageDto;
import com.ce.scrm.center.service.business.entity.view.ConvertLogPageBusinessView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.extend.dubbo.api.SmaConvertLogDubboService;
import com.ce.scrm.extend.dubbo.entity.request.SmaConvertLogReq;
import com.ce.scrm.extend.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.extend.dubbo.entity.response.SmaConvertLogRes;
import com.ce.scrm.extend.dubbo.enums.ReleaseBussinessTagEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 流转日志三方业务
 * <AUTHOR>
 * @date 2024/5/21 下午8:38
 * @version 1.0.0
 */
@Slf4j
@Service
public class SmaConvertLogThirdService {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private SmaConvertLogDubboService smaConvertLogDubboService;

    @Resource
    private CmCustProtectService cmCustProtectService;
    @Resource
    private SendWxMessage sendWxMessage;

    /**
     * Description: 插入流转日志
     * @author: JiuDD
     * @param businessDto  流转日志
     * date: 2024/7/19 14:51
     */
    public void insertLog(ConvertLogBusinessDto businessDto) {
        smaConvertLogDubboService.save(CglibUtil.copy(businessDto, SmaConvertLogReq.class));
    }

    /**
     * @Description 批量插入流转日志 那边1000条一批
     * <AUTHOR>
     * @date 2025/1/6 15:57
     * @param businessDtoList
     */
    public void saveBatch(List<ConvertLogBusinessDto> businessDtoList) {
        if(CollectionUtils.isEmpty(businessDtoList)) {
            log.error("saveBatch ，集合参数为空");
            return;
        }
        List<SmaConvertLogReq> smaConvertLogRes = BeanCopyUtils.convertToVoList(businessDtoList, SmaConvertLogReq.class);
        smaConvertLogDubboService.saveBatch(smaConvertLogRes);
    }

    /**
     * 插入流转日志
     * @param convertType   流转类型
     * @param convertTypeDesc   流转描述
     * @param operator  操作人
     * @param cmCustProtectOrigin    原始保护关系
     * @param cmCustProtectTarget  新保护关系
     * <AUTHOR>
     * @date 2024/5/21 下午9:11
     **/
    public void insertLog(Integer convertType, String convertTypeDesc, String operator, CmCustProtect cmCustProtectOrigin, CmCustProtect cmCustProtectTarget) {
        SmaConvertLogReq smaConvertLogReq = new SmaConvertLogReq();
        smaConvertLogReq.setId(UUID.fastUUID().toString(true));
        if (cmCustProtectOrigin != null) {
            smaConvertLogReq.setSalerId(cmCustProtectOrigin.getSalerId());
            smaConvertLogReq.setDeptOfSalerId(cmCustProtectOrigin.getBussdeptId());
            smaConvertLogReq.setSubcompanyOfSalerId(cmCustProtectOrigin.getSubcompanyId());
            smaConvertLogReq.setAreaOfSalerId(cmCustProtectOrigin.getAreaId());
        }
        if (cmCustProtectTarget != null) {
            smaConvertLogReq.setCustId(cmCustProtectTarget.getCustId());
            smaConvertLogReq.setCustName(cmCustProtectTarget.getCustName());
            smaConvertLogReq.setCurSalerId(cmCustProtectTarget.getSalerId());
            smaConvertLogReq.setDeptOfCurSalerId(cmCustProtectTarget.getBussdeptId());
            smaConvertLogReq.setSubcompanyOfCurSalerId(cmCustProtectTarget.getSubcompanyId());
            smaConvertLogReq.setAreaOfCurSalerId(cmCustProtectTarget.getAreaId());
        }
        smaConvertLogReq.setCreateBy(operator);
        smaConvertLogReq.setCreateTime(new Date());
        smaConvertLogReq.setConvertType(convertType);
        smaConvertLogReq.setReleaseReason(convertTypeDesc);
        smaConvertLogDubboService.save(smaConvertLogReq);
    }

    public Page<ConvertLogPageBusinessView> getPageForReleaseReason(ConvertLogPageBusinessDto businessDto) {
        SmaConvertLogReq req = CglibUtil.copy(businessDto, SmaConvertLogReq.class);
        DubboPageInfo<SmaConvertLogRes> dubboPageInfo = smaConvertLogDubboService.queryPageSpecialList(req);
        Page<ConvertLogPageBusinessView> page = new Page<>();
        page.setCurrent(dubboPageInfo.getPageNum());
        page.setSize(dubboPageInfo.getPageSize());
        page.setPages(dubboPageInfo.getPages());
        page.setTotal(dubboPageInfo.getTotal());
        List<ConvertLogPageBusinessView> collect = dubboPageInfo.getList().stream().map(smaConvertLogRes -> {
            ConvertLogPageBusinessView businessView = new ConvertLogPageBusinessView();
            BeanUtils.copyProperties(smaConvertLogRes, businessView);
            businessView.setTagOpportunityOriginDesp(ReleaseBussinessTagEnum.getLable(smaConvertLogRes.getTagOpportunityOrigin()));
            return businessView;
        }).collect(Collectors.toList());
        page.setRecords(collect);
        return page;
    }


    public boolean updateJlReleaseReason(ConvertLogUpdateBusinessDto businessDto) {
        if (Objects.isNull(businessDto.getId())) {
            return false;
        }
        SmaConvertLogReq req = new SmaConvertLogReq();
        req.setId(businessDto.getId());
        SmaConvertLogRes smaConvertLogRes = smaConvertLogDubboService.getById(req);

        req.setJlReleaseTime(businessDto.getJlReleaseTime());
        req.setJlReasonText(businessDto.getJlReasonText());
        if (smaConvertLogDubboService.updateById(req)){
            //发送企微消息
            WxMessageDto wxMessageDto = new WxMessageDto();
            wxMessageDto.setMajorId(businessDto.getZjId());
            wxMessageDto.setLeaderName(businessDto.getJlName());
            wxMessageDto.setTagOpportunityOrigin(smaConvertLogRes.getTagOpportunityOrigin());
            wxMessageDto.setCustName(smaConvertLogRes.getCustName());
            sendWxMessage.sendWxMessageLeader(wxMessageDto);
        }
        return true;
    }
}