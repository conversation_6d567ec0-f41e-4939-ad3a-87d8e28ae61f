package com.ce.scrm.center.service.business;

import com.ce.scrm.center.service.business.entity.dto.openapi.SaveCallRecordKjBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.openapi.SaveRecordKjBusinessDto;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.extend.dubbo.api.CloudCustomerServiceCallLogDubbo;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.CloudCustomerServiceCallLogDubboView;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tinet.clink.cc.model.CdrObRecordModel;
import com.tinet.clink.cc.request.cdr.DescribeRecordFileUrlRequest;
import com.tinet.clink.cc.response.cdr.DescribeRecordFileUrlResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import com.tinet.clink.cc.request.cdr.ListCdrObsRequest;
import com.tinet.clink.cc.response.cdr.ListCdrObsResponse;
import com.tinet.clink.core.client.Client;
import com.tinet.clink.core.client.ClientConfiguration;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/4/7 10:00
 */
@Slf4j
@Service
public class HistoryBusiness {

    @Resource
    private RecordKjBusiness recordKjBusiness;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private CloudCustomerServiceCallLogDubbo cloudCustomerServiceCallLogDubbo;


    public Boolean recordHandle() {

        long startTime = 1741104000L;

        long endTime = 1742832000L;
        int count = 0;
//        Set<String> set = new HashSet<>();
        List<CdrObRecordModel> callListR = new ArrayList<>();
//        for (int i = 1; i <= 160; i++) {
        for (int i = 1; i <= 160; i++) {
            //43200
            log.error("计数：i={}",i);
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            long startTimes = startTime+10800*(i-1);
            long endTimes = startTime+10800*i;
//            System.out.println("startTime: " + startTimes + " endTime: " + endTimes);
            List<CdrObRecordModel> callListResult = getCallList(startTimes,endTimes );
            callListR.addAll(callListResult);
//            System.out.println(callList.size());
//            List<String> collect = callList.stream().map(CdrObRecordModel::getUniqueId).collect(Collectors.toList());
//            set.addAll(collect);
            count += callListResult.size();
        }

        log.error("callListSize={}",callListR.size());
//        List<SaveCallRecordKjBusinessDto> callList = getCallList(i);
        List<SaveCallRecordKjBusinessDto> saveCallRecordKjBusinessDtos = BeanCopyUtils.convertToVoList(callListR, SaveCallRecordKjBusinessDto.class);
        for (SaveCallRecordKjBusinessDto saveCallRecordKjBusinessDto : saveCallRecordKjBusinessDtos) {

            DubboResult<CloudCustomerServiceCallLogDubboView> detail = cloudCustomerServiceCallLogDubbo.detail(saveCallRecordKjBusinessDto.getUniqueId());
            if (detail != null && detail.checkSuccess() && detail.getData() == null) {
                saveCallRecordKjBusinessDto.setMainUniqueId(saveCallRecordKjBusinessDto.getUniqueId());
                saveCallRecordKjBusinessDto.setAnswerTime(saveCallRecordKjBusinessDto.getBridgeTime());
                recordKjBusiness.saveCallRecordKj(saveCallRecordKjBusinessDto);

                if(StringUtils.isNotBlank(saveCallRecordKjBusinessDto.getMainUniqueId())
                        && StringUtils.isNotBlank(saveCallRecordKjBusinessDto.getRecordFile())) {

                    String recordUrl = getRecordUrl(saveCallRecordKjBusinessDto.getMainUniqueId());
                    if (StringUtils.isNotBlank(recordUrl)) {
                        SaveRecordKjBusinessDto saveRecordKj = new SaveRecordKjBusinessDto();
                        saveRecordKj.setMainUniqueId(saveCallRecordKjBusinessDto.getMainUniqueId());
                        saveRecordKj.setRecordFileIndex(saveCallRecordKjBusinessDto.getRecordFile());
                        saveRecordKj.setPreSignUrl(recordUrl);
                        recordKjBusiness.saveRecordKj(saveRecordKj);
                    }
                }
            }
        }


        log.error("录音历史数据完成");
        return true;
    }

    private String getRecordUrl(String mainUniqueId) {

        // 创建访问服务的client实例并初始化
        ClientConfiguration configuration = new ClientConfiguration(
                "0510a394b7e7ba090ee7260a7ea3bf2c",          // AccessKeyId
                "34K21g3BqS07416E1DrG");     // AccessKeySecret

        /*
            设置请求平台：
                北京平台：api-bj.clink.cn
                上海平台：api-sh.clink.cn
            默认为北京平台
        */
        configuration.setHost("api-sh.clink.cn");

        Client client = new Client(configuration);

        // 创建API请求并设置参数
        DescribeRecordFileUrlRequest request = new DescribeRecordFileUrlRequest();
        request.setMainUniqueId(mainUniqueId);

        DescribeRecordFileUrlResponse response = null;
        String recordFileUrl = null;

        try {
            response = client.getResponseModel(request);
            recordFileUrl = response.getRecordFileUrl();
            return recordFileUrl;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private List<CdrObRecordModel> getCallList(Long startTime, Long endTime) {

        // 创建访问服务的client实例并初始化
        ClientConfiguration configuration = new ClientConfiguration(
                "0510a394b7e7ba090ee7260a7ea3bf2c",          // AccessKeyId
                "34K21g3BqS07416E1DrG");     // AccessKeySecret

        /*
            设置请求平台：
                北京平台：api-bj.clink.cn
                上海平台：api-sh.clink.cn
            默认为北京平台
        */
        configuration.setHost("api-sh.clink.cn");

        Client client = new Client(configuration);

        // 创建API请求并设置参数
        ListCdrObsRequest request = new ListCdrObsRequest();
        request.setStartTime(startTime);//2025-03-05 00:00:00
        request.setEndTime(endTime);//2025-03-25 00:00:00
        request.setLimit(100);
        request.setOffset(0);

        // 发起请求并处理应答或异常
        ListCdrObsResponse response;
        ObjectMapper mapper = new ObjectMapper();

        try {
            response = client.getResponseModel(request);
            return response.getCdrObs();

        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return new ArrayList<>();
    }

}
