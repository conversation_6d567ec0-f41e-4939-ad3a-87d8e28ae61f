/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.template;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.CreationType;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

public class TemplateQuery extends BaseParam {

    /**
     * 模板类型，参考CreationType 枚举
     */
    private CreationType type;

    /**
     * 标签id组，多个用英文逗号分割，例：”1,2,3”
     */
    private String tagIds;

    /**
     * 模板名称,支持模糊匹配
     */
    private String name;

    /**
     * 分页排序，排序值为hots:热度，
     * create_time：时间 , 升序为asc 降序为desc
     * 例如热度降序：hots desc，默认为按时间降序排列
     */
    private String orderBy;

    public TemplateQuery(CreationType type) {
        this.type = type;
    }

    @Override
    public void validate() {
        if (type == null) {
            throw new KnownException("type不能为空");
        }
    }

    public Map<String, String> getParamsMap() {
        Map<String, String> map = getBaseParamsMap();
        map.put("type", type.getValue());
        if (StrUtil.isNotEmpty(tagIds)) {
            map.put("tagIds", tagIds);
        }
        if (StrUtil.isNotEmpty(name)) {
            map.put("name", name);
        }
        if (StrUtil.isNotEmpty(orderBy)) {
            map.put("orderBy", orderBy);
        }
        return map;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getTagIds() {
        return tagIds;
    }

    public void setTagIds(String tagIds) {
        this.tagIds = tagIds;
    }

    public CreationType getType() {
        return type;
    }

    public void setType(CreationType type) {
        this.type = type;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }
}
