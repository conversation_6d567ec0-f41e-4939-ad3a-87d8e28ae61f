package com.ce.scrm.center.service.enums;

import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityLevelData;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 高呈商机等级枚举
 * <AUTHOR>
 * @date 2024/5/13 下午2:16
 * @version 1.0.0
 **/
@Getter
public enum GcSjLevelEnum {
    /**
     * A
     */
    A(1, "A（高）"),
    /**
     * B
     */
    B(2, "B（中）"),
    /**
     * C
     */
    C(3, "C（低）"),
    /**
     * D
     */
    D(4, "D（其他）"),
    ;

    /**
     * 等级
     */
    private final Integer level;

    /**
     * 等级名称
     */
    private final String levelName;

    GcSjLevelEnum(Integer level, String levelName) {
        this.level = level;
        this.levelName = levelName;
    }

    /**
     * 等级数据映射
     */
    private final static Map<Integer, GcSjLevelEnum> LEVEL_MAP = new HashMap<>();

    /**
     * 枚举name数据映射
     */
    private final static Map<String, GcSjLevelEnum> NAME_LEVEL_MAP = new HashMap<>();

    /**
     * 等级数据列表
     */
    private final static List<GcBusinessOpportunityLevelData> GC_BUSINESS_OPPORTUNITY_LEVEL_DATA_LIST = new ArrayList<>();

    /**
     * 获取高呈商机等级枚举
     * @param level 等级
     * <AUTHOR>
     * @date 2024/5/17 下午1:55
     * @return com.ce.scrm.center.dubbo.enums.GcSjLevelEnum
     **/
    public static GcSjLevelEnum get(Integer level) {
        if (LEVEL_MAP.isEmpty()) {
            for (GcSjLevelEnum gcSjLevelEnum : GcSjLevelEnum.values()) {
                LEVEL_MAP.put(gcSjLevelEnum.getLevel(), gcSjLevelEnum);
            }
        }
        return LEVEL_MAP.get(level);
    }

    /**
     * Description: 中小意向商机等级 转换为 高呈商机等级
     *              中小意向商机等级 {"A", "B", "C", "D", "E"}， 其中{"A", "B", "C", "D"} 对应高呈的 A, B, C, D 等级，{"E"}级丢弃
     * @param intentSjLevel 中小意向商机等级
     * @return GcSjLevelEnum 高呈商机等级
     * @author: JiuDD
     * date: 2024/5/23 17:13
     */
    public static GcSjLevelEnum intentSjLevel2GcEnum(String intentSjLevel) {
        if (NAME_LEVEL_MAP.isEmpty()) {
            for (GcSjLevelEnum gcSjLevelEnum : GcSjLevelEnum.values()) {
                NAME_LEVEL_MAP.put(gcSjLevelEnum.name(), gcSjLevelEnum);
            }
        }
        return NAME_LEVEL_MAP.get(intentSjLevel);
    }

    /**
     * 获取所有高呈商机等级枚举数据
     * <AUTHOR>
     * @date 2024/5/17 下午1:57
     * @return java.util.List<com.ce.scrm.center.dubbo.entity.view.GcBusinessOpportunityLevelData>
     **/
    public static List<GcBusinessOpportunityLevelData> list() {
        if (GC_BUSINESS_OPPORTUNITY_LEVEL_DATA_LIST.isEmpty()) {
            for (GcSjLevelEnum gcSjLevelEnum : GcSjLevelEnum.values()) {
                GcBusinessOpportunityLevelData gcBusinessOpportunityLevelData = new GcBusinessOpportunityLevelData();
                gcBusinessOpportunityLevelData.setLevel(gcSjLevelEnum.getLevel());
                gcBusinessOpportunityLevelData.setLevelName(gcSjLevelEnum.getLevelName());
                GC_BUSINESS_OPPORTUNITY_LEVEL_DATA_LIST.add(gcBusinessOpportunityLevelData);
            }
        }
        return GC_BUSINESS_OPPORTUNITY_LEVEL_DATA_LIST;
    }
}
