package com.ce.scrm.center.service.business.entity.ai;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiOrgPerformanceInfoEnView implements Serializable {
	/**
	 * 到账月
	 */
	private String toAccountMonth;

	/**
	 * 产品名称
	 */
	private String productName;

	/**
	 * 签单金额
	 */
	private String signAmount;

	/**
	 * 签单商务数
	 */
	private Integer signBusinessNum;

	/**
	 * 签单客户数
	 */
	private Integer signCustNum;

	/**
	 * 签单新客户数
	 */
	private Integer signNewCustNum;

	/**
	 * 签单老客户数
	 */
	private Integer signOldCustNum;

}