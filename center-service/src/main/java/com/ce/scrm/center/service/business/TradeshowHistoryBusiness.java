package com.ce.scrm.center.service.business;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dao.entity.TradeshowHistory;
import com.ce.scrm.center.dao.service.TradeshowHistoryService;
import com.ce.scrm.center.service.business.entity.dto.TradeshowHistoryBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.openapi.SaveCallRecordKjBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.openapi.SaveRecordKjBusinessDto;
import com.ce.scrm.center.service.business.entity.view.CustomerStageDataBusinessView;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.TradeshowTagEnum;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.extend.dubbo.api.CloudCustomerServiceCallLogDubbo;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.CloudCustomerServiceCallLogDubboView;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tinet.clink.cc.model.CdrObRecordModel;
import com.tinet.clink.cc.request.cdr.DescribeRecordFileUrlRequest;
import com.tinet.clink.cc.request.cdr.ListCdrObsRequest;
import com.tinet.clink.cc.response.cdr.DescribeRecordFileUrlResponse;
import com.tinet.clink.cc.response.cdr.ListCdrObsResponse;
import com.tinet.clink.core.client.Client;
import com.tinet.clink.core.client.ClientConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.common.filter.impl.Op;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/4/7 10:00
 */
@Slf4j
@Service
public class TradeshowHistoryBusiness {

    @Resource
    private TradeshowHistoryService tradeshowHistoryService;

//    @Resource
//    private CustomerBusiness customerBusiness;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private RocketMqOperate rocketMqOperate;

    /***
     * 导入公司展会记录
     * @param tradeshowHistoryBusinessDto
     * <AUTHOR>
     * @date 2025/4/28 16:06
     * @version 1.0.0
     * @return boolean
    **/
    public Optional<String> importTradeshowHistory(TradeshowHistoryBusinessDto tradeshowHistoryBusinessDto) {
        log.info("导入公司展会记录 {}", JSON.toJSONString(tradeshowHistoryBusinessDto));
        String customerId = tradeshowHistoryBusinessDto.getCustomerId();
        if (StringUtils.isBlank(customerId)) {
            return Optional.of("客户id不能为空!");
        }

//        CustomerStageDataBusinessView customerStageData = customerBusiness.getCustomerStageData(customerId);
//        if (Objects.isNull(customerStageData)) {
//            return Optional.of("客户id找不到对应的记录!!");
//        }
        Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(customerId);
        if (!customerData.isPresent()) {
            return Optional.of("客户id找不到对应的记录!!");
        }


        String tradeshowName = tradeshowHistoryBusinessDto.getTradeshowName();
        if (StringUtils.isBlank(tradeshowName)) {
            return Optional.of("展会名称不能为空!");
        }

        TradeshowHistory tradeshowHistory = new TradeshowHistory();
        tradeshowHistory.setCustomerId(customerId);
        tradeshowHistory.setTradeshowName(tradeshowName);
        tradeshowHistory.setTradeshowTime(tradeshowHistoryBusinessDto.getTradeshowTime());
        /**
         * 展会映射cdp属性字段
         */
        Optional<TradeshowTagEnum> enumOpt = TradeshowTagEnum.fromTradeshowName(tradeshowName);
        if (!enumOpt.isPresent()) {
            // 防止瞎填
            log.error("找不到展会 tradeshowName={}", tradeshowName);
            return Optional.of("找不到展会对应的配置");
        }
        tradeshowHistory.setCreateTime(new Date());
        log.info("保存公司展会记录 tradeshowHistory={}", JSON.toJSONString(tradeshowHistory));
        boolean save = tradeshowHistoryService.save(tradeshowHistory);
        if (!save){
            log.error("保存公司展会记录失败 tradeshowHistory={}", JSON.toJSONString(tradeshowHistory));
            return Optional.of("保存公司展会记录失败");
        }
        return Optional.empty();
    }

}
