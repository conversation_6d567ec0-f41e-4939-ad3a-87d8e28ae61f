package com.ce.scrm.center.service.business.entity.ai;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @classname AiChatLogBusinessDto
 * @description TODO
 * @date 2025/3/14 15:47
 * @create by gaomeijing
 */
@Data
public class AiChatLogBusinessDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id; // 主键 ID
    private String channelId; // 渠道 ID
    private String pid; // pid
    private String custId; // 客户 ID
    private String promptId; // 提示词 ID
    private String platform; // 平台
    private String chatId; // 对话 ID
    private String loginEmployeeId; // 登录人 ID
    private String content; // 对话内容
    private Date dbInsertTime; // 表记录写入时间
    private Date dbUpdateTime; // 表记录更新时间
    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;
}
