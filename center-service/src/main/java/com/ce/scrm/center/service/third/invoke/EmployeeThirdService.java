package com.ce.scrm.center.service.third.invoke;

import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import cn.ce.cesupport.emp.service.EmployeeAppService;
import cn.ce.cesupport.emp.vo.EmployeeVo;
import cn.ce.cesupport.enums.abm.LeadsDistributeEnum;
import cn.ce.cesupport.framework.base.vo.EmployeeVO;
import cn.ce.cesupport.rbac.service.RoleRelationAppService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.entity.view.OrgDataThirdView;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 员工三方业务
 * <AUTHOR>
 * @date 2023/5/15 16:14
 * @version 1.0.0
 */
@Service
@Slf4j
public class EmployeeThirdService {

    @DubboReference
    private EmployeeAppService employeeAppService;

    @DubboReference
    private RoleRelationAppService roleRelationAppService;

    @Resource
    private OrgThirdService orgThirdService;

    /*
     * @Description 根据员工id获取员工信息 不检查state = 1
     * <AUTHOR>
     * @date 2024/11/15 17:42
     * @param empId
     * @return java.util.Optional<com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto>
     */
    public Optional<EmployeeInfoThirdDto> getEmployeeByEmpId(String empId) {
        if (StringUtils.isBlank(empId)) {
            log.error("根据员工ID获取员工数据，员工ID为空");
            return Optional.empty();
        }
        EmployeeVo employeeVo = employeeAppService.selectOneNofilter(empId);
        if (employeeVo == null) {
            log.error("根据员工ID获取员工数据，返回数据为空，员工ID为:{}", empId);
            return Optional.empty();
        }
        EmployeeInfoThirdDto employeeInfoThirdDto = CglibUtil.copy(employeeVo, EmployeeInfoThirdDto.class);
        return Optional.of(employeeInfoThirdDto);
    }

    /*
     * @Description 根据员工id获取员工信息 检查state = 1
     * <AUTHOR>
     * @date 2024/12/9 09:22
     * @param empId
     * @return java.util.Optional<com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto>
     */
    public Optional<EmployeeInfoThirdDto> getEmployeeCheckStateByEmpId(String empId) {
        if (StringUtils.isBlank(empId)) {
            log.error("根据员工ID获取员工数据，state = 1的员工ID为空");
            return Optional.empty();
        }
        EmployeeVo employeeVo = employeeAppService.selectOne(empId);
        if (employeeVo == null) {
            log.error("根据员工ID获取state = 1的员工数据，返回数据为空，员工ID为:{}", empId);
            return Optional.empty();
        }
        EmployeeInfoThirdDto employeeInfoThirdDto = CglibUtil.copy(employeeVo, EmployeeInfoThirdDto.class);
        return Optional.of(employeeInfoThirdDto);
    }

    /**
     * 获取员工数据（包含机构分司等数据）
     * @param employeeId    员工ID
     * <AUTHOR>
     * @date 2024/2/2 15:10
     * @return java.util.Optional<com.ce.scrm.extend.service.third.entity.view.EmployeeDataThirdView>
     **/
    public Optional<EmployeeDataThirdView> getEmployeeData(String employeeId) {
        if(StringUtils.isEmpty(employeeId)) {
            log.error("根据员工ID获取员工数据，员工ID为空:{}", employeeId);
        }
        EmployeeVO employeeVo = roleRelationAppService.findDeptInfoAndSubInfoOfEmp(employeeId);
        if (employeeVo == null) {
            log.warn("根据员工ID获取员工数据，返回数据为空，员工ID为:{}", employeeId);
            return Optional.empty();
        }
        EmployeeDataThirdView employeeDataThirdView = CglibUtil.copy(employeeVo, EmployeeDataThirdView.class);
        if (orgThirdService.gcAreaFlag(employeeVo.getAreaId())) {
            employeeDataThirdView.setGcEmployeeFlag(true);
        }
        return Optional.of(employeeDataThirdView);
    }

    /**
     * 获取所有高呈商务数据
     * <AUTHOR>
     * @date 2024/5/22 下午1:40
     * @return java.util.List<com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView>
     **/
    public List<EmployeeDataThirdView> getGcAllSalerData() {
        List<OrgDataThirdView> gcAllSubList = orgThirdService.getGcAllSubList();
        if(CollectionUtil.isEmpty(gcAllSubList)){
            log.warn("获取所有高呈员工数据，高呈分司数据为空，无法获取员工数据");
            return Lists.newArrayList();
        }
        return gcAllSubList.stream().flatMap(orgDataThirdView -> Optional.ofNullable(employeeAppService.findAllSalerIdsBySubIdAndPositions(orgDataThirdView.getId(), Arrays.asList(EmpPositionConstant.BUSINESS_GCKHDB, EmpPositionConstant.BUSINESS_GCMANAGER, EmpPositionConstant.BUSINESS_GCMAJOR))).orElseGet(Lists::newArrayList).stream()).map(this::getEmployeeData).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
    }

    public List<EmployeeDataThirdView> selectListByOrgIds(List<String> orgIdList) {
        if (CollectionUtil.isEmpty(orgIdList)) {
            return Lists.newArrayList();
        }
        List<EmployeeVo> employeeVos = employeeAppService.selectListByOrgIds(orgIdList);
        if (CollectionUtil.isEmpty(employeeVos)) {
            return Lists.newArrayList();
        }

        return BeanUtil.copyToList(employeeVos, EmployeeDataThirdView.class);
    }


    /**
     * Description: 通过机构id获取机构的领导
     *              仅限商务体系。例如：orgId是区域id，返回区域管理部下的区总； orgId是分司id，返回管理部下的分总；orgId如果是部门id，返回商务经理
     * @author: JiuDD
     * @param orgId
     * @return com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView
     * date: 2024/7/11 19:11
     */
    public Optional<EmployeeLiteThirdView> getOrgLeader(String orgId) {
        if(StringUtils.isEmpty(orgId)) {
            log.error("通过机构id获取机构的领导，机构ID为空:{}", orgId);
            return Optional.empty();
        }
        EmployeeVo manager = employeeAppService.findOrgLeader(orgId);
        if (Objects.nonNull(manager) && StringUtils.isNotBlank(manager.getId())) {
            EmployeeLiteThirdView thirdView = new EmployeeLiteThirdView();
            BeanUtils.copyProperties(manager, thirdView);
            return Optional.of(thirdView);
        }
        return Optional.empty();
    }

    /*
     * @Description 根据员工id集合回查员工集合信息（离职员工state!=1的也会被查出）
     * <AUTHOR>
     * @date 2024/12/3 16:25
     * @param empIdList
     * @return java.util.Map<java.lang.String,com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto>
     */
    public Map<String,EmployeeInfoThirdDto> getEmployeeDataMap(List<String> empIdList) {
        if(CollectionUtil.isEmpty(empIdList)) {
            return Collections.emptyMap();
        }
        List<String> idList = empIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if(CollectionUtil.isEmpty(idList)) {
            return Collections.emptyMap();
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("idList",idList);
        List<EmployeeVo> employeeList =  employeeAppService.findByMap(paramMap);
        if(CollectionUtil.isEmpty(employeeList)) {
            log.info("查询员工集合为空,empIdList={}", empIdList);
            return Collections.emptyMap();
        }
        Map<String, EmployeeInfoThirdDto> result = employeeList.stream().collect(Collectors.toMap(
                EmployeeVo::getId,
                e -> BeanUtil.copyProperties(e, EmployeeInfoThirdDto.class),
                (e1, e2) -> e1
        ));
        return result;
    }

	/**
	 *
	 * @param orgId 组织id
	 * @param leadsDistributeEnum 分配SDR角色枚举
	 * @return
	 */
	public List<EmployeeVo> selectListByDeptIdAndPositions(LeadsDistributeEnum leadsDistributeEnum) {
		List<LeadsDistributeEnum> enums = Lists.newArrayList(LeadsDistributeEnum.CC, LeadsDistributeEnum.SDR);
		if (enums.contains(leadsDistributeEnum)) {
			log.warn("selectListByDeptIdAndPositions()方法，仅支持SDR、CC");
			return Collections.emptyList();
		}
		String orgId = null;
		if (leadsDistributeEnum.equals(LeadsDistributeEnum.CC)) {
			orgId = "cc005";
		}
		if (leadsDistributeEnum.equals(LeadsDistributeEnum.SDR)) {
			orgId = "sdr005";
		}
		List<String> salerPositonNoManagerList = EmpPositionConstant.salerPositonNoManagerList;
		salerPositonNoManagerList.add(leadsDistributeEnum.getName());
		return employeeAppService.selectListByDeptIdAndPositions(orgId, salerPositonNoManagerList);
	}


}