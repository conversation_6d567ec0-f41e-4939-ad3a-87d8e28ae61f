package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * description: 中小总监审核转高呈参数
 * @author: DD.Jiu
 * date: 2024/5/22.
 */
@Data
public class MajorCheckCustBusinessDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 高呈商机ID（主键）
     */
    @NotNull(message = "商机ID不能为空")
    private Long id;
    /**
     * 审核总监ID
     */
    @NotBlank(message = "审核总监ID不能为空")
    private String checkMajorId;
    /**
     * 审核总监姓名
     */
    @NotBlank(message = "审核总监姓名不能为空")
    private String checkMajorName;
    /**
     * 审核总监手机号
     */
    @NotBlank(message = "审核总监手机号不能为空")
    private String checkMajorPhone;
    /**
     * 审核状态（1审批通过；2审批未通过；）
     */
    @NotNull(message = "审核状态不能为空")
    private Integer approvalState;
    /**
     * 拒绝原因
     */
    private String refuseReason;
    /**
     * 分司ID
     */
    private String subId;
    /**
     * 操作人ID
     */
    private String operator;
    /**
     * 操作人名称
     */
    private String operatorName;
}
