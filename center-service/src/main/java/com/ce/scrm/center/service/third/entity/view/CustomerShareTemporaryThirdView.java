package com.ce.scrm.center.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CustomerShareTemporaryThirdView implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户id：C、企业客户标识，P、个人客户标识，Q、其他客户标识（历史客户ID保持不变）
     */
    private String customerId;

    /**
     * 商务代表ID
     */
    private String salerId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门ID
     */
    private String buId;

    /**
     * 分公司ID
     */
    private String subId;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 分享时间
     */
    private Date shareTime;

    /**
     * 创建时间
     */
    private Date createTime;

}
