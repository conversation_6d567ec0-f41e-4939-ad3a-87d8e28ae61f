package com.ce.scrm.center.service.enums;

import lombok.Getter;

@Getter
public enum TransferProcessingStatusEnum {
    UNPROCESSED(1, "未处理"),
    PASS(2, "通过"),
    REJECT(3, "驳回"),
    TIMEOUT_AUTO_PROCESS(4, "超时自动处理"),
    ;

    private final Integer code;
    private final String name;

    TransferProcessingStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransferProcessingStatusEnum Enum : values()) {
            if (Enum.getCode().equals(code)) {
                return Enum.getName();
            }
        }
        return null;
    }
    public static Integer getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (TransferProcessingStatusEnum Enum : values()) {
            if (Enum.getName().equals(name)) {
                return Enum.getCode();
            }
        }
        return null;
    }

}
