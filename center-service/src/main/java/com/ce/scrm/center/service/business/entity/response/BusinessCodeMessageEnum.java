package com.ce.scrm.center.service.business.entity.response;

/**
 * 返回码枚举类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/05/28 下午3:39
 */
public enum BusinessCodeMessageEnum {
    //成功返回码
    REQUEST_SUCCESS("200", "请求成功"),
    //会员登录异常，特殊处理，兼容之前的校验码
    //异常返回码，从100001开始
    SERVER_INTERNAL_EXCEPTION("100001", "网络超时，请稍后重试哦～"),
    REQUEST_METHOD_EXCEPTION("100002", "请求方式异常"),
    REQUEST_ADDRESS_EXCEPTION("100003", "请求地址不存在"),
    REQUEST_PARAM_EXCEPTION("100004", "请求参数异常"),
    RPC_EXCEPTION("100005", "网络连接超时，请稍后重试哦～"),
    REQUEST_PARAM_NOT_NULL("100006", "请求参数不能为空"),
    REQUEST_DUBBO_GENERIC_PASSWORD_ERROR("100007", "请确认当前接口的正确性"),
    NOT_LOGIN("100008", "登录超时"),
    REQUEST_POSITION_NOT_MATCH("100009", "无权操作"),
    DATA_NOT_EXIST("100010", "数据不存在"),
    CUSTOMER_CIRCULATION_SETTING_EDIT_LESS_THAN_ONE_YEAR("100011", "每年只可以修改一次规则"),
    NOT_CLOSING_CUSTOMER("100012", "该客户非成交客户，不能添加"),
    NOT_CURRENT_COMPANY_PROTECT("100013", "该客户未由您的分司保护，不能添加"),
    DATA_EXIST("100015", "数据已存在，请勿重复添加"),
    CUSTOMER_CIRCULATION_SPECIAL_SETTING_COUNT_MORE_THAN_20("100016","不流失或不流转最多添加20个客户"),



    CUSTOMER_LEADS_DATASOURCE_EMPTY("100100","Leads数据来源不能为空"),
    CUSTOMER_LEADS_CUSTOMERID_EMPTY("100101","Leads客户不能为空"),
    CUSTOMER_LEADS_TYPE_EMPTY("100102","Leads客户不能为空"),
    CUSTOMER_LEADS_CODE_EMPTY("100103","LeadsCode不能为空"),
    CUSTOMER_LEADS_SOURCE_EMPTY("100104","Leads来源不能为空"),
    CUSTOMER_LEADS_CUSTOMER_ADD_FAIL("100105","Leads创建客户失败"),
    CUSTOMER_LEADS_CONTACT_ADD_FAIL("100105","Leads创建联系人失败"),
    //渠道不能为空
    CUSTOMER_LEADS_CHANNEL_EMPTY("100106","Leads渠道不能为空"),
    //活动不能为空
    CUSTOMER_LEADS_ACTIVITY_EMPTY("100107","Leads活动不能为空"),
    //端口不能为空
    CUSTOMER_LEADS_CLIENT_TYPE_EMPTY("100108","Leads端口不能为空"),
    //保存leads失败
    CUSTOMER_LEADS_SAVE_FAIL("100109","Leads保存失败"),








    ;

    private final String code;
    private final String msg;

    BusinessCodeMessageEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
