package com.ce.scrm.center.service.business.entity.dto.abm;

import com.ce.scrm.center.dao.entity.ActivityClueInfo;
import com.ce.scrm.center.dao.entity.ClueInfo;
import com.ce.scrm.center.dao.entity.CustomerTemplateImportDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 客户leads导入或下发客户
 */
@Data
public class CustomerLeadsImportOrDistributeDto implements Serializable {

	/**
	 * 【必传】导入来源 1-历史线索导入 2-53客服等渠道的线索 3-营销效果回收 4-手工录入(不按leadsCode分保护关系，直接分给当前登录的SDR) 5-绿化后的客户导入
	 */
	private Integer leadsImportFrom;

	/**
	 * 营销活动效果回收的线索信息
	 */
	private List<ActivityClueInfo> activityClueInfos;

	/**
	 * 无cid的线索列表
	 */
	private List<ClueInfo> clueList;

	/**
	 * 有cicd的历史线索列表
	 */
	private List<ClueInfo> clueListWithCid;

	/**
	 * 绿化后的数据导入
	 */
	private List<CustomerTemplateImportDto> customerTemplateImportDtos;

	private static final long serialVersionUID = 1L;
}