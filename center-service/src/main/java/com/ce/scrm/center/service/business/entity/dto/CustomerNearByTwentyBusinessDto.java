package com.ce.scrm.center.service.business.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 查询客户附近20公里内的相同二级行业成交客户
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerNearByTwentyBusinessDto implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 页码
	 */
	private Integer pageNum = 1;

	/**
	 * 每页数量
	 */
	private Integer pageSize = 10;
	/**
	 * pid
	 */
	private String pid;

	/**
	 * custId 客户id
	 */
	private String custId;

	/**
	 * 附近多少米的客户
	 */
	private String distanceRangeMeter = "2000";

}
