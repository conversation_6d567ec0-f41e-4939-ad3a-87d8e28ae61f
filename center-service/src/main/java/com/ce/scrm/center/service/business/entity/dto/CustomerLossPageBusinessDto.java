package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * Description: 流失客户
 *
 * @author: liyechao
 * date: 2024/9/2
 */
@Data
@Accessors(chain = true)
public class CustomerLossPageBusinessDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户id
     */
    private String custId;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 区域
     */
    private String areaId;
    /**
     * 分公司
     */
    private String subId;
    /**
     * 部门
     */
    private String deptId;
    /**
     * 商务ID
     */
    private String salerId;
    /**
     * 流失日期
     */
    private LocalDate preDate;
    /**
     * 流失原因
     */
    private List<String> lossReasons;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;
}