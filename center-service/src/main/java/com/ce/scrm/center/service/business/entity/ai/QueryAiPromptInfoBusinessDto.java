package com.ce.scrm.center.service.business.entity.ai;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: ai提示词
 * @Author: lijinpeng
 * @Date: 2025/2/20 09:40
 */
@Data
public class QueryAiPromptInfoBusinessDto implements Serializable {

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;

    private Integer startFlag;

    /**
     * 分司ID
     */
    private String subId;


    /**
     * 事业部id
     */
    private String buId;


    /**
     * 部门ID
     */
    private String deptId;
    private Integer promptType;
    private List<Integer> promptTypeList;

    /**
     * 区域ID
     */
    private String areaId;

    private List<String> company;

    private String employeeId;

}
