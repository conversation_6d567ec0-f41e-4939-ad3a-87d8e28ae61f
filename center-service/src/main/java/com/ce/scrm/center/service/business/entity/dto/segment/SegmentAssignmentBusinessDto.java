package com.ce.scrm.center.service.business.entity.dto.segment;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/27
 */
@Data
public class SegmentAssignmentBusinessDto implements Serializable {
    /**
     * 分群ID
     */
    private String segmentId;

    /**
     * 1:区域总监分配
     * 2:分公司总监分配
     * 3:事业部总监分配
     * 4:部门经理/小组组长分配
     */
    private Integer assignmentRoleType;

    /**
     * 分配的客户ID集合
     */
    private List<String> customerIds;

    /**
     * 1:按保护关系自动分配
     * 2:按组织结构手动分配
     */
    private Integer assignmentType;

    /**
     * assignmentRoleType=1:区域总监分配
     * 分司ID
     */
    private String subId;
    /**
     * 区总分配给分司的ID集合
     */
    private List<String> subIds;
    /**
     * 事业部ID
     */
    private String buId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 商务ID
     */
    private String salerId;


    //=============================登录员工信息=============================

    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 员工名称
     */
    private String loginEmployeeName;

    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;

    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;

    /**
     * 事业部ID
     */
    private String loginBuId;
    /**
     * 事业部名称
     */
    private String loginBuName;
    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;

    /**
     * 职位
     */
    private String loginPosition;
}
