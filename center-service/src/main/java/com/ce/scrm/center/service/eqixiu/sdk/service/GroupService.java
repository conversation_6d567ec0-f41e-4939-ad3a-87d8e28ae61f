package com.ce.scrm.center.service.eqixiu.sdk.service;


import com.ce.scrm.center.service.eqixiu.sdk.domain.Group;
import com.ce.scrm.center.service.eqixiu.sdk.domain.Secret;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.group.DelGroupCmd;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.group.GroupQuery;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.group.SaveGroupCmd;
import com.ce.scrm.center.service.eqixiu.sdk.service.dto.group.SetGroupCmd;
import com.ce.scrm.center.service.eqixiu.sdk.support.HttpClient;
import com.ce.scrm.center.service.eqixiu.sdk.support.TokenCache;
import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;

import java.util.List;

/**
 * 分组管理接口
 * <a href="https://hc.eqxiu.cn/doc/38/">...</a>
 *
 * <AUTHOR>
 */
public class GroupService extends ConnectService {

    public static final String API_GROUP_LIST = "/api/v1/biz/group/list";
    public static final String API_GROUP_CREATE_UPDATE_GROUP = "/api/v1/biz/group/saveOrUpdate";
    public static final String API_GROUP_DELETE = "/api/v1/biz/group/delete";
    public static final String API_GROUP_SETTING = "/api/v1/biz/group/setting";

    public GroupService(Secret secret) {
        super(secret);
    }

    public GroupService(Secret secret, TokenCache tokenCache) {
        super(secret, tokenCache);
    }

    public GroupService(Secret secret, TokenCache tokenCache, HttpClient httpClient) {
        super(secret, tokenCache, httpClient);
    }

    /**
     * 获取作品或素材的分组列表。
     *
     * @param query
     * @return
     */
    public List<Group> listGroup(GroupQuery query) {
        paramValidate(query);
        JSONObject result = httpClient.httpGet(getApiURL(API_GROUP_LIST), null, query.getParamsMap());
        printLog(result, "获取作品或素材的分组列表失败:{}");
        return result.getList(Group.class);
    }

    /**
     * 为作品或素材新增/更新分组信息。
     *
     * @param cmd
     * @return id
     */
    public Long saveOrUpdateGroup(SaveGroupCmd cmd) {
        paramValidate(cmd);
        JSONObject result = httpClient.httpPostObj(getApiURL(API_GROUP_CREATE_UPDATE_GROUP, cmd.getParamsMap()), cmd.getParamsMap());
        printLog(result, "为作品或素材新增/更新分组信息失败:{}");
        return result.getLong("obj");
    }

    /**
     * 根据分组ID删除一个资源分组信息。
     *
     * @param cmd
     * @return
     */
    public boolean deleteGroup(DelGroupCmd cmd) {
        paramValidate(cmd);
        JSONObject result = httpClient.httpPost(getApiURL(API_GROUP_DELETE, cmd.getParamsMap()), null, cmd.getParamsMap());
        printLog(result, "根据分组ID删除一个资源分组信息失败:{}");
        return result.getSuccess();
    }

    /**
     * 将作品、素材等资源移动至指定分组。
     *
     * @param cmd
     * @return
     */
    public boolean settingGroup(SetGroupCmd cmd) {
        paramValidate(cmd);
        JSONObject result = httpClient.httpPostObj(getApiURL(API_GROUP_SETTING, cmd.getParamsMap()), cmd);
        printLog(result, "将作品、素材等资源移动至指定分组失败:{}");
        return result.getSuccess();
    }


}
