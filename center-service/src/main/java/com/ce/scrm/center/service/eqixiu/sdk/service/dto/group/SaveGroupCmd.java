/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.group;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.GroupBusType;
import com.ce.scrm.center.service.eqixiu.sdk.domain.type.GroupCreateType;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

public class SaveGroupCmd extends BaseParam {


    /**
     * 新增不需要填，更新必填
     */
    private Long id;

    /**
     * 分组名称
     */
    private String title;

    /**
     * 业务类型 1：作品 2：素材
     */
    private GroupBusType busType;

    /**
     * 创建者类型 1：创建人 2：创建企业 3：创建平台
     */
    private GroupCreateType createType;

    public static SaveGroupCmd newStaffGroup(String title, GroupBusType busType, String openId) {
        SaveGroupCmd cmd = new SaveGroupCmd();
        cmd.setTitle(title);
        cmd.setBusType(busType);
        cmd.setCreateType(GroupCreateType.STAFF);
        cmd.setOpenId(openId);
        return cmd;
    }

    public static SaveGroupCmd newCorpGroup(String title, GroupBusType busType) {
        SaveGroupCmd cmd = new SaveGroupCmd();
        cmd.setTitle(title);
        cmd.setBusType(busType);
        cmd.setCreateType(GroupCreateType.CORP);
        return cmd;
    }

    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> map = getBaseParamsMap();
        if (id != null) {
            map.put("id", String.valueOf(id));
        }
        if (StrUtil.isNotEmpty(title)) {
            map.put("title", title);
        }
        if (busType != null) {
            map.put("busType", String.valueOf(busType.getValue()));
        }
        if (createType != null) {
            map.put("createType", String.valueOf(createType.getValue()));
        }
        return map;
    }

    @Override
    public void validate() {
        if (busType == null) {
            throw new KnownException("busType 参数不能为空");
        }
        if (id == null && StrUtil.isEmpty(title)) {
            throw new KnownException("title 参数不能为空");
        }
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setBusType(GroupBusType busType) {
        this.busType = busType;
    }

    public void setCreateType(GroupCreateType createType) {
        this.createType = createType;
    }
}
