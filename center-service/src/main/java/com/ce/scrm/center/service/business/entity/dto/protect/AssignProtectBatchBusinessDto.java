package com.ce.scrm.center.service.business.entity.dto.protect;

import lombok.Data;

import java.util.List;

/**
 * @version 1.0
 * @Description: 分配保护关系
 * @Author: lijinpeng
 * @Date: 2025/1/2 15:13
 */
@Data
public class AssignProtectBatchBusinessDto {

    /**
     * 客户id集合
     */
    private List<String> custIdList;

    /**
     * 分配的商务id
     */
    private String assignToSalerId;

    /**
     * 分配的部门id
     */
    private String assignToDeptId;

    /**
     * 分配的事业部id
     */
    private String assignToBuId;

    /**
     * 分配的分司id
     */
    private String assignToSubId;

    /**
     * 动作类型
     */
    private Integer actionType;

}
