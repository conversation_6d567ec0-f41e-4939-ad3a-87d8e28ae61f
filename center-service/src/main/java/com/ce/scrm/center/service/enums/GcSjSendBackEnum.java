package com.ce.scrm.center.service.enums;

import lombok.Getter;

/**
 * 高呈退回枚举
 *
 * <AUTHOR>
 * @date 2024/8/20
 **/
@Getter
public enum GcSjSendBackEnum {
    GC_MARKET_DEPARTMENT_BACK(1, "高呈市场部退回"),
    GC_BUSINESS_BACK(2, "高呈商务退回"),
    ;

    private final Integer state;
    private final String stateName;

    GcSjSendBackEnum(Integer state, String stateName) {
        this.state = state;
        this.stateName = stateName;
    }
}