package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * description: 上市公司
 * @author: DD.Jiu
 * date: 2025/2/13.
 */
@Data
public class BigDataListedentDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**企业名称*/
    @JSONField(name = "企业名称", serialize = false)
    private String company_name;
    /**统一信用代码*/
    @JSONField(name = "统一信用代码", serialize = false)
    private String uncid;
    /**法人代表*/
    @JSONField(name = "法人代表", serialize = false)
    private String legal_person;
    /**总经理*/
    @JSONField(name = "总经理", serialize = false)
    private String general_manager;
    /**股票类型*/
    @JSONField(name = "股票类型", serialize = false)
    private String stock_type;
    /**公司简介*/
    @JSONField(name = "公司简介", serialize = false)
    private String company_info;
    /**董事长*/
    @JSONField(name = "董事长", serialize = false)
    private String chairman;
    /**股票代码*/
    @JSONField(name = "股票代码", serialize = false)
    private String stock_code;
    /**董秘*/
    @JSONField(name = "董秘", serialize = false)
    private String secretary;
    /**成立时间*/
    @JSONField(name = "成立时间", serialize = false)
    private Date establish_time;
    /**公司简称*/
    @JSONField(name = "公司简称", serialize = false)
    private String short_name;
    /**英文全称*/
    @JSONField(name = "英文全称", serialize = false)
    private String company_name_en;
    /**公司代码*/
    @JSONField(name = "公司代码", serialize = false)
    private String company_code;
    /**股票简称*/
    @JSONField(name = "股票简称", serialize = false)
    private String stock_short_name;
    /**股票上市时间*/
    @JSONField(name = "股票上市时间",serialize = false)
    private Long listed_date;
    /**股票总股本*/
    @JSONField(name = "股票总股本", serialize = false)
    private Double total_stock;
    /**股票流通股本*/
    @JSONField(name = "股票流通股本", serialize = false)
    private Integer circulation_stock;
    /**公司历史名称*/
    @JSONField(name = "公司历史名称", serialize = false)
    private String history_short_name;
    /**省份*/
    @JSONField(name = "省份", serialize = false)
    private String province;
    /**所属行业*/
    @JSONField(name = "所属行业", serialize = false)
    private String industry_phy;
    /**员工人数*/
    @JSONField(name = "员工人数")
    private Integer emp_num;
    /**公司简称曾用名*/
    @JSONField(name = "公司简称曾用名", serialize = false)
    private String short_history_name;
    /**独立董事*/
    @JSONField(name = "独立董事", serialize = false)
    private String ind_director;
    /**网址*/
    @JSONField(name = "网址",serialize = false)
    private String website;
    /**办公地址*/
    @JSONField(name = "办公地址", serialize = false)
    private String address;
    /**是否已退市 0否 1是*/
    @JSONField(name = "是否已退市",serialize = false)
    private Integer is_listed;
    /**退市时间*/
    @JSONField(name = "退市时间", serialize = false)
    private Long delisted_date;
    @JSONField(name = "是否上市")
    private String isMarket;
    @JSONField(name = "股票上市时间")
    private String floatationOfSharesTime;
}
