package com.ce.scrm.center.service.third.entity.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Description: 客户分页查询
 * @Author: lijinpeng
 * @Date: 2025/1/10 14:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomerPageThirdDto implements Serializable {

    /**
     * 客户id：C、企业客户标识，P、个人客户标识，Q、其他客户标识（历史客户ID保持不变）
     */
    private List<String> customerIdList;

    /**
     * 客户类型
     */
    private Integer customerType;

    /**
     * 元数据
     */
    private String sourceDataId;
    /**
     * 证件号码
     */
    private String certificateCode;
    /**
     * 证件类型
     */
    private String certificateType;

    /**
     * 客户/企业名称
     */
    private String customerName;

    /**
     * 创建开始时间（yyyy-MM-dd）
     */
    private String createTimeStart;

    /**
     * 创建结束时间（yyyy-MM-dd）
     */
    private String createTimeEnd;

    /**
     * 页号
     */
    private Integer pageNum = 1;

    /**
     * 页码
     */
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 是否正序
     */
    private boolean ascFlag;

    /**
     * 是否删除标记
     */
    private Integer deleteFlag;

    /**
     * 成为合格新客户开始时间
     */
    private Date qualifiedNewCustBeginTime;

    /**
     * 成为合格新客户结束时间
     */
    private Date qualifiedNewCustEndTime;

    /**
     * 证件号码 或者等于 拼接
     */
    private String orCertificateCode;

}
