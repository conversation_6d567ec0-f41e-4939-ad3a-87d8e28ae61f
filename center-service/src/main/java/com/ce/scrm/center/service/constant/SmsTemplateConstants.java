package com.ce.scrm.center.service.constant;

/**
 *
 * @类描述: 模板常量类
 */
public class SmsTemplateConstants {

    /**
     * 企微通知每条消息客户数（每条消息包含50个客户名称时，会有极少数消息长度超长被企微截断，为保险起见，每条消息最多只包含40个客户名称）
     */
    public final static Integer CUST_NUM_PER_MSG = 40;

    /**
     * 新增逻辑，商务释放商机、报价客户、转介绍客户，相应经理或组长，必填“释放原因”。给总监发企微通知
     * 经理（组长）{0}，为{1}客户 {2}，填写了释放原因。请登录PC端查看。
     */
    public final static String SCRM_RELEASE_BUSSINESS_MANAGER = "SCRM_RELEASE_BUSSINESS_MANAGER";

    /**
     *  流失客户提醒模板
     *  "以下客户即将流失（产品剩余{0}天到期）：
     * {1}
     * 具体情况请到营销平台PC端：成交客户--成交客户--即将流失客户（分栏）查看。"
     */
    public final static String SCRM_CUSTOMER_LOSS = "SCRM_CUSTOMER_LOSS";

    /**
     *  流转客户提醒模板
     * "以下客户即将流转（打卡未达标）：
     * {0}
     * 具体情况请到营销平台PC端：成交客户--成交客户--待流转客户查看。"
     */
    public final static String SCRM_CUSTOMER_CIRCULATION = "SCRM_CUSTOMER_CIRCULATION";

    /**
     *  每天定时任务发送总监未处理客户提醒消息
     * "今天在“总监待分配”还有一些待分配的客户，请处理。（如果已处理请忽略）"
     */
    public final static String SCRM_ZJ_UNDISPOSED_CUSTOMER = "SCRM_ZJ_UNDISPOSED_CUSTOMER";

    /**
     * 每天定时任务发送经理未处理客户提醒消息 "你（商务经理角色或商务组长角色）还有一些客户没有分配，请尽快登录营销平台进行分配。"
     */
    public final static String SCRM_JL_UNDISPOSED_CUSTOMER = "SCRM_JL_UNDISPOSED_CUSTOMER";

    /**
     * {}的合作申请相关经理、总监已审核通过，合作达成。具体内容请在营销平台PC端查询。
     */
    public final static String SCRM_COOPERATION_APPROVE_AGREE = "SCRM_COOPERATION_APPROVE_AGREE";

    /**
     * {}的合作申请被相关经理、总监驳回，无法合作。具体内容请在营销平台PC端查询。
     */
    public final static String SCRM_COOPERATION_APPROVE_DISAGREE = "SCRM_COOPERATION_APPROVE_DISAGREE";

    /**
     * 经双方商务协商同意，解除客户：{}的合作，请知晓。
     */
    public final static String SCRM_COOPERATION_CANCEL = "SCRM_COOPERATION_CANCEL";

    /**
     * 发起申请后，到审批人（经理、总监）企微提示”客户“XX公司”，保护商务xxx、合作商务xxx，本次申请合作期限为XX天，请在合作期限内完成签单，本次合作时间超期后即为失效，若再次合作，需重新发起申请。“
     * 注：失效时间按照商务所选择的时间为主；如选择合作时间为”30“那么第31天合作失效，如果需要再次合作，需要重新申请
     */
    public final static String SCRM_COOPERATION_AGREE_SEND_MESSAGE = "SCRM_COOPERATION_AGREE_SEND_MESSAGE";

    /**
     * 客户“XX公司”合作期限将在7天后失效，请尽快登录营销平台完成签单。
     */
    public final static String SCRM_COOPERATION_PERIOD_VALIDITY_SEVEN_DAY_REMIND_MESSAGE = "SCRM_COOPERATION_PERIOD_VALIDITY_SEVEN_DAY_REMIND_MESSAGE";

    /**
     * 阿里的
     * 是否接受商机客户：$｛name｝，请在$｛time｝分前确认是否接受此商机，超时默认接受。
     */
    public final static String SMS_481290062 = "SMS_481290062";

    /**
     * 数据库里的
     * 是否接受商机客户：$｛name｝，请在$｛time｝分前确认是否接受此商机，超时默认接受。
     */
    public final static String SCRM_SMS_481290062 = "SCRM_SMS_481290062";

}
