package com.ce.scrm.center.service.eqixiu.sdk.domain.type;


import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

public enum TemplateCreationEnums {
    H5("h5", "s", "c"),
    LC("lc", "ls", "lc"),
    FORM("form", "fv", "fe"),
    HD("hd", "gs", "gc"),
    DESIGN("design", "ds", "design"),
    VIDEO("video", "", ""),
    COL_VIDEO("col_video", "", ""),
    TEMPLATE("temp", "", ""),
    ALL("all", "", "");

    private final String code;
    private final String preview;
    private final String edit;

    private TemplateCreationEnums(String code, String preview, String edit) {
        this.code = code;
        this.preview = preview;
        this.edit = edit;
    }

    public String getCode() {
        return this.code;
    }

    public String getPreview() {
        return this.preview;
    }

    public String getEdit() {
        return this.edit;
    }

    public static TemplateCreationEnums typeOf(String code) {
        if (StrUtil.isEmpty((code))) {
            return null;
        } else {
            TemplateCreationEnums[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                TemplateCreationEnums value = var1[var3];
                if (code.equalsIgnoreCase(value.getCode())) {
                    return value;
                }
            }

            return null;
        }
    }

    public boolean codeEquals(String code) {
        return StrUtil.isEmpty(code) ? Boolean.FALSE : this.getCode().equalsIgnoreCase(code);
    }

    public static boolean validType(TemplateCreationEnums typeEnum) {
        if (typeEnum == null) {
            return false;
        } else {
            boolean isValid = false;
            TemplateCreationEnums[] var2 = values();
            int var3 = var2.length;

            for(int var4 = 0; var4 < var3; ++var4) {
                TemplateCreationEnums value = var2[var4];
                if (value.equals(typeEnum)) {
                    isValid = true;
                    break;
                }
            }

            return isValid;
        }
    }

    public static boolean validType(String code) {
        return typeOf(code) != null;
    }
}
