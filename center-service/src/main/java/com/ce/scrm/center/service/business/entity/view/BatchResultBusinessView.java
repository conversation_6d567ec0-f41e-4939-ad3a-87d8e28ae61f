package com.ce.scrm.center.service.business.entity.view;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 批量操作返回
 * @Author: lijinpeng
 * @Date: 2025/1/2 15:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchResultBusinessView implements Serializable {


    private Integer successCount;

    private Integer failCount;

    @Builder.Default
    private String cause = "操作完成";

    public Integer getAllCount() {
        return successCount+failCount;
    }

    public String getMessage() {
        return cause+"，成功"+successCount+"条，失败"+failCount+"条";
    }

}
