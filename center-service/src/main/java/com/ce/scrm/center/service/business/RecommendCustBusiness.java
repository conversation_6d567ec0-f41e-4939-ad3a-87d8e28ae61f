package com.ce.scrm.center.service.business;

import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.RecommendCust;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.RecommendCustService;
import com.ce.scrm.center.service.business.entity.dto.RecommendCustBusinessDto;
import com.ce.scrm.center.service.business.entity.view.RecommendCustBusinessView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Slf4j
@Service
public class RecommendCustBusiness {
    @Resource
    private RecommendCustService recommendCustService;
    @Resource
    private CmCustProtectService cmCustProtectService;

    public Optional<RecommendCustBusinessView> findFirstRecommendCust(RecommendCustBusinessDto recommendCustBusinessDto) {
        LambdaQueryWrapper<RecommendCust> lambdaQueryChainWrapper = new LambdaQueryWrapper<>();
        if (recommendCustBusinessDto.getRecommendedCustId() != null){
            lambdaQueryChainWrapper.eq(RecommendCust::getRecommendedCustId, recommendCustBusinessDto.getRecommendedCustId());
        }
        if (recommendCustBusinessDto.getRecommendCustId() != null){
            lambdaQueryChainWrapper.eq(RecommendCust::getRecommendCustId, recommendCustBusinessDto.getRecommendCustId());
        }
        lambdaQueryChainWrapper.orderByDesc(RecommendCust::getCreateTime).last("limit 1");
        RecommendCust recommendCust = recommendCustService.getOne(lambdaQueryChainWrapper);
        if (recommendCust == null) {
            return Optional.empty();
        }
        RecommendCustBusinessView recommendCustBusinessView = CglibUtil.copy(recommendCust, RecommendCustBusinessView.class);
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, recommendCust.getRecommendCustId()).one();
        if (cmCustProtect == null) {
            return Optional.empty();
        }
        recommendCustBusinessView.setRecommendCustName(cmCustProtect.getCustName());
        return Optional.of(recommendCustBusinessView);
    }
}
