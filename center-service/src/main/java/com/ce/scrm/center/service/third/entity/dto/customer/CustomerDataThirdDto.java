package com.ce.scrm.center.service.third.entity.dto.customer;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import lombok.*;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2024/12/9 11:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerDataThirdDto implements Serializable {

    /**
     * 客户联系方式
     */
    private String contactWay;

    /**
     * 客户联系方式类型
     */
    private Integer contactType;

}
