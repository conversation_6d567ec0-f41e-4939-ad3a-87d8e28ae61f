package com.ce.scrm.center.service.business.entity.dto.skb;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * description:进出口信用
 * @author: DD.Jiu
 * date: 2025/2/13.
 */
@Data
public class BigDataImportandExportCreditDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**企业名称*/
    @JSONField(serialize = false)
    private String entname;
    /**统一信用代码*/
    @JSONField(serialize = false)
    private String uncid;
    /**总数量*/
    private int total_count;
    /**总页数*/
    private int total_page;
    /**当前页*/
    private int page;
    @JSONField(name = "进出口信用列表")
    private List<ListImportandExportCredit> list;

    @NoArgsConstructor
    @Data
    public static class ListImportandExportCredit implements Serializable {
        private static final long serialVersionUID = 1L;
        /**海关注册编码*/
        @JSONField(name = "海关注册编码",serialize = false)
        private String cust_code;
        /**注册日期*/
        @JSONField(name = "注册日期", serialize = false)
        private String register_date;
        /**企业中文名称*/
        @JSONField(name = "企业中文名称", serialize = false)
        private String company_name;
        /**注册海关*/
        @JSONField(name = "注册海关")
        private String register_org;
        /**海关登记经营类别*/
        @JSONField(name = "海关登记经营类别", serialize = false)
        private String op_category;
        /**海关行业登记类别*/
        @JSONField(name = "海关行业登记类别", serialize = false)
        private String indus_category;
        /**海关行业登记类别分类*/
        @JSONField(name = "海关行业登记类别分类", serialize = false)
        private String indus_category_type;
        /**信用等级*/
        @JSONField(name = "进出口信用等级")
        private String credit_level;
        /**报关有效期*/
        @JSONField(name = "报关有效期",format = "yyyy-MM-dd HH:mm:ss")
        private Date valid_date_to;//1
        /**跨境电子商务类型*/
        @JSONField(name = "跨境电子商务类型")
        private List<String> business_type;
        /**行政区域*/
        @JSONField(name = "行政区域",serialize = false)
        private String adminis_division;
        /**经济区域*/
        @JSONField(name = "进出口经济区域")
        private String economic_division;
        /**特殊贸易区域*/
        @JSONField(name = "特殊贸易区域")
        private String special_trade_area;
        /**海关注销标志: 注销，正常，其他,*/
        @JSONField(name = "海关注销标志")
        private String cust_status_desc;
        /**年报情况,未送达，已送达，其他*/
        @JSONField(name = "年报情况",serialize = false)
        private String ann_status_desc;
        /**信用信息异常情况*/
        @JSONField(name = "进出口信用信息异常情况")
        private String credit_abnormal_desc;
    }
}
