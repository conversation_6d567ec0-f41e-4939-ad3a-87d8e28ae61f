package com.ce.scrm.center.service.business;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.ce.cdp.dubbo.api.SensorsDBDubbo;
import com.ce.cdp.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.service.business.entity.dto.ai.AiBusinessDto;
import com.ce.scrm.center.service.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @version 1.0
 * @Description: ai cdp 业务
 * @Author: lijinpeng
 * @Date: 2025/2/17 13:53
 */
@Slf4j
@Service
public class AiCdpBusiness {
    /**
     * 项目ID
     */
    @Value("${sensors.projectId}")
    private String projectId;
    @DubboReference(group = "scrm-cdp-api", version = "1.0.0", check = false)
    private SensorsDBDubbo sensorsDBDubbo;

    public JSONObject getAiCdpData(AiBusinessDto dto) {
        String customerId = dto.getCustomerId();
        if (StringUtils.isEmpty(customerId)) {
            return null;
        }

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT ");
        // 用户属性
        for (SyncCrmFieldEnum value : SyncCrmFieldEnum.values()) {
            stringBuilder.append(value.getField()).append(",");
        }
        // 标签
        for (SyncBiaoqianFieldEnum value : SyncBiaoqianFieldEnum.values()) {
            stringBuilder.append(value.getField()).append(",");
        }
        stringBuilder.append("id FROM ");
        stringBuilder.append(CdpDatabaseEnum.getEnumByProjectId(projectId).getDatabase());
        //stringBuilder.append(".users where customer_id='").append(customerId).append("'");
        stringBuilder.append(".users where $identity_login_id='").append(customerId).append("'");
        String sql = stringBuilder.toString();
        DubboResult<List<Map<String, Object>>> result = sensorsDBDubbo.executeSql(sql);
        log.info("result:{}", result);
        if (Objects.isNull(result) || !result.checkSuccess()) {
            return null;
        }
        List<Map<String, Object>> data = result.getData();
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        JSONObject json = new JSONObject(new LinkedHashMap<>());
        log.info("dataOfCdpUser:{}", JSONObject.toJSONString(data));
        try {
            data.forEach(item -> {
                // CDP用户属性的处理
                JSONObject userProfile = assembleUserProfile(item);
                // CDP用户标签的处理
                JSONObject biaoqian = assembleBiaoqian(item);
                json.putAll(userProfile);
                json.putAll(biaoqian);
            });
        }catch (Exception e) {
            log.error("ai调用cdp的users格式转换异常,data={}",JSONObject.toJSONString(data),e);
        }
        return json;
    }

    /**
     * Description: CDP用户属性的处理
     * @author: JiuDD
     * @param item CDP返回的用户信息
     * @return com.alibaba.fastjson.JSONObject
     * date: 2025/2/18 15:06
     */
    private static JSONObject assembleUserProfile(Map<String, Object> item) {
        JSONObject json = new JSONObject();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (SyncCrmFieldEnum userFieldEnum : SyncCrmFieldEnum.values()) {
            Object o = item.get(userFieldEnum.getField());
            if (Objects.nonNull(o)) {
                if (Objects.equals("无", o.toString())) {
                    continue;
                }
                if (userFieldEnum.getFieldType() == CdpTypeEnum.String && StringUtils.hasText(o.toString())) {
                    json.put(userFieldEnum.getDescription(), o);
                } else if (userFieldEnum.getFieldType() == CdpTypeEnum.Datetime && o instanceof Long && (Long) o > 0L) {
                    Date date = new Date((Long) o);
                    json.put(userFieldEnum.getDescription(), sdf.format(date));
                } else if (userFieldEnum.getFieldType() == CdpTypeEnum.Bool) {
                    if (Objects.equals("1", String.valueOf(o)) || Objects.equals("1.000", String.valueOf(o))) {
                        json.put(userFieldEnum.getDescription(), "是");
                    } else {
                        json.put(userFieldEnum.getDescription(), "否");
                    }
                } else {
                    json.put(userFieldEnum.getDescription(), o);
                }
            }
        }
        // flag7是个枚举类型，需要转换
        Object flag7Value = json.get(SyncCrmFieldEnum.tags_flag7.getDescription());
        if (Objects.nonNull(flag7Value)) {
            SkbFlag7Enum byValue = SkbFlag7Enum.getByValue(flag7Value.toString());
            if (Objects.nonNull(byValue)) {
                json.put(SyncCrmFieldEnum.tags_flag7.getDescription(), byValue.getDescription());
            }
        }
        // flag8是个枚举类型，需要转换
        Object flag8Value = json.get(SyncCrmFieldEnum.tags_flag8.getDescription());
        if (Objects.nonNull(flag8Value)) {
            SkbFlag8Enum byValue = SkbFlag8Enum.getByValue(flag8Value.toString());
            if (Objects.nonNull(byValue)) {
                json.put(SyncCrmFieldEnum.tags_flag8.getDescription(), byValue.getDescription());
            }
        }
        return json;
    }

    /**
     * Description: CDP用户标签的处理
     * @author: JiuDD
     * @param item CDP返回的用户信息
     * @return com.alibaba.fastjson.JSONObject
     * date: 2025/2/18 15:03
     */
    private static JSONObject assembleBiaoqian(Map<String, Object> item) {
        JSONObject json = new JSONObject();
        Object o1 = item.get(SyncBiaoqianFieldEnum.user_tag_attend_huixiao.getField());
        //cdp标签的此选项值只有“是”，故此处只判断是否为“是”
        if (Objects.nonNull(o1) && Objects.equals("是", String.valueOf(o1))) {
            json.put(SyncBiaoqianFieldEnum.user_tag_attend_huixiao.getDescription(), "是");
        }
        Object o2 = item.get(SyncBiaoqianFieldEnum.user_tag_gmgszmp.getField());
        //cdp标签的此选项值只有“数字名片客户”，故此处只判断“数字名片客户”
        if (Objects.nonNull(o2) && Objects.equals("数字名片客户", String.valueOf(o2))) {
            json.put(SyncBiaoqianFieldEnum.user_tag_gmgszmp.getDescription(), "是");
        }
        Object o3 = item.get(SyncBiaoqianFieldEnum.user_tag_sfcjggjh2024qj136j.getField());
        //cdp标签的此选项值只有“是”，故此处只判断是否为“是”
        if (Objects.nonNull(o3) && Objects.equals("是", String.valueOf(o3))) {
            json.put(SyncBiaoqianFieldEnum.user_tag_sfcjggjh2024qj136j.getDescription(), "是");
        }
        Object o4 = item.get(SyncBiaoqianFieldEnum.user_tag_buy_waimao_product.getField());
        //cdp标签的此选项值只有“是”，故此处只判断是否为“是”
        if (Objects.nonNull(o4) && Objects.equals("是", String.valueOf(o4))) {
            json.put(SyncBiaoqianFieldEnum.user_tag_buy_waimao_product.getDescription(), "是");
        }
        Object o5 = item.get(SyncBiaoqianFieldEnum.user_tag_zglsfcygjh.getField());
        //cdp标签的此选项值只有“是”，故此处只判断是否为“是”
        if (Objects.nonNull(o5) && Objects.equals("是", String.valueOf(o5))) {
            json.put(SyncBiaoqianFieldEnum.user_tag_zglsfcygjh.getDescription(), "是");
        }
        return json;
    }

    public JSONObject getCdpCreateWorkOrderData(AiBusinessDto dto) {
        String customerId = dto.getCustomerId();
        if (StringUtils.isEmpty(customerId)) {
            return null;
        }
        // 获取最近一个月的工单
        JSONObject json = new JSONObject(new LinkedHashMap<>());
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT ");
        for (WorkOrderCreateEnum value : WorkOrderCreateEnum.values()) {
            stringBuilder.append(value.getField()).append(",");
        }
        stringBuilder.append("event_id FROM ");
        stringBuilder.append(CdpDatabaseEnum.getEnumByProjectId(projectId).getDatabase());
        stringBuilder.append(".events WHERE event ='workorder_create' AND $identity_login_id='").append(customerId).append("' AND work_order_create_time >= ").append(DateUtil.offsetMonth(DateUtil.date(), -1).getTime());
        //stringBuilder.append(".events WHERE event ='workorder_create' AND $identity_login_id='").append(customerId).append("' AND work_order_create_time >= (SELECT date_sub(now(), 30))");
        String sql = stringBuilder.toString();
        DubboResult<List<Map<String, Object>>> result = sensorsDBDubbo.executeSql(sql);
        log.info("result:{}", result);
        if (!result.checkSuccess()) {
            return null;
        }
        List<Map<String, Object>> data = result.getData();
        if (CollectionUtils.isEmpty(data)) {
            json.put("最近一个月的工单数量", 0);
            // 最近一个月的工单数量为0，则查询最后一个工单
            StringBuilder stringBuilderOne = new StringBuilder();
            stringBuilderOne.append("SELECT ");
            for (WorkOrderCreateEnum value : WorkOrderCreateEnum.values()) {
                stringBuilderOne.append(value.getField()).append(",");
            }
            stringBuilderOne.append("event_id FROM ");
            stringBuilderOne.append(CdpDatabaseEnum.getEnumByProjectId(projectId).getDatabase());
            stringBuilderOne.append(".events WHERE event ='workorder_create' AND $identity_login_id='").append(customerId).append("' order by work_order_create_time desc limit 1 ");
            log.info("stringBuilderOne:{}", stringBuilderOne);
            DubboResult<List<Map<String, Object>>> lastOneResult = sensorsDBDubbo.executeSql(stringBuilderOne.toString());
            log.info("lastOneResult:{}", lastOneResult);
            if (lastOneResult.checkSuccess()) {
                data = lastOneResult.getData();
                log.info("dataNum:{}", data);
            }
        } else {
            json.put("最近一个月的工单数量", data.size());
        }
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        log.info("data:{}", data);
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<JSONObject> array = new ArrayList<>();
            data.forEach(item -> {
                JSONObject jsonEachWorkOrder = new JSONObject();
                for (WorkOrderCreateEnum value : WorkOrderCreateEnum.values()) {
                    Object o = item.get(value.getField());
                    if (Objects.nonNull(o)) {
                        if (value.getFieldType() == CdpTypeEnum.String && StringUtils.hasText(o.toString())) {
                            jsonEachWorkOrder.put(value.getAiDescription(), o);
                        } else if (value.getFieldType() == CdpTypeEnum.Datetime && o instanceof Long && (Long) o > 0L) {
                            Date date = new Date((Long) o);
                            jsonEachWorkOrder.put(value.getAiDescription(), sdf.format(date));
                        } else {
                            jsonEachWorkOrder.put(value.getAiDescription(), o);
                        }
                    }
                }
                if (!jsonEachWorkOrder.isEmpty()) {
                    array.add(jsonEachWorkOrder);
                }
            });
            if (!array.isEmpty()) {
                json.put("工单列表", array);
            }
        } catch (Exception e) {
            log.error("ai调用cdp的events（工单创建）格式转换异常,data={}",JSONObject.toJSONString(data),e);
        }
        return json;
    }

}
