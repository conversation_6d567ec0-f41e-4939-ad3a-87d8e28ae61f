package com.ce.scrm.center.service.business.entity.dto.segment;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 修改商务标注条件
 * @Author: lijinpeng
 * @Date: 2025/2/28 09:43
 */
@Data
public class UpdateCustomTagBusinessDto implements Serializable {

    /**
     * segment_detail表id
     */
    @NotNull
    private String id;

    /**
     * 商务标注
     */
    @NotNull
    private Integer salerCustomTag;

    // ----------------------下面是员工登录信息

    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 员工名称
     */
    private String loginEmployeeName;
    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;

    /**
     * 事业部id
     */
    private String loginBuId;

    /**
     * 事业部名称
     */
    private String loginBuName;

    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;
    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;

    /**
     * 职位
     */
    @NotNull
    private String loginPosition;

}
