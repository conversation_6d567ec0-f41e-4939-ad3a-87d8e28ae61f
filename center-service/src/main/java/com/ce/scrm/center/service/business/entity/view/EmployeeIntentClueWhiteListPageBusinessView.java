package com.ce.scrm.center.service.business.entity.view;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: 市场部人员组织表
 * @author: JiuDD
 * date: 2024/11/5 9:48
 */
@Data
public class EmployeeIntentClueWhiteListPageBusinessView implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 员工id
     */
    private String empId;

    /**
     * 员工名称
     */
    private String empName;

    /**
     * 组织类型: 0未知 1中企 2跨境
     */
    private Byte OrgType;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位
     */
    private String positon;

}