package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 成交客户流转流失特例配置表-添加请求体
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
public class CustomerCirculationSpecialSettingAddBusinessDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer notCirculationType;

    private String custId;

    /**
     * 不流转添加原因：字典NOT_CIRCULATION
     */
    private String notCirculationAddReason;

    /**
     * 不流失添加原因：字典NOT_LOSE
     */
    private String notLoseAddReason;

    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 员工名称
     */
    private String loginEmployeeName;
    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;
    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;
    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;
    /**
     * 手机号
     */
    private String loginMobile;
    /**
     * 工作邮箱
     */
    private String loginWorkMail;
    /**
     * 职位
     */
    private String loginPosition;
    /**
     * 职级
     */
    private String loginJobGrade;

}
