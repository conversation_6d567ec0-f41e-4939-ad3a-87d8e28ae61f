package com.ce.scrm.center.service.enums;

/**
 * description: 同步到ai的标签字段枚举
 * @author: DD.Jiu
 * date: 2025/2/17.
 */
public enum SyncBiaoqianFieldEnum {
    user_tag_sfcjggjh2024qj136j("user_tag_sfcjggjh2024qj136j", "是否参加过广交会(2024秋季136届)", CdpTypeEnum.String),
    user_tag_gmgszmp("user_tag_gmgszmp", "购买过数字名片", CdpTypeEnum.String),
    user_tag_attend_huixiao("user_tag_attend_huixiao", "参加过会销的客户", CdpTypeEnum.String),
    user_tag_buy_waimao_product("user_tag_buy_waimao_product", "是否买过外贸产品", CdpTypeEnum.String),
    user_tag_zglsfcygjh("user_tag_zglsfcygjh", "是否参与广交会", CdpTypeEnum.String),
    ;

    private String field;
    private String description;
    private CdpTypeEnum fieldType;

    SyncBiaoqianFieldEnum(String field, String description, CdpTypeEnum fieldType) {
        this.field = field;
        this.description = description;
        this.fieldType = fieldType;
    }

    public String getField() {
        return field;
    }

    public String getDescription() {
        return description;
    }

    public CdpTypeEnum getFieldType() {
        return fieldType;
    }
}

