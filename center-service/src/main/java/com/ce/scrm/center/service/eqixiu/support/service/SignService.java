package com.ce.scrm.center.service.eqixiu.support.service;

import com.ce.scrm.center.service.eqixiu.support.util.Sha1;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 签名service
 *
 * <AUTHOR>
 */
@Service
public class SignService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SignService.class);

    @Value("${ecmp.signatureKey:}")
    private String signatureKey;
    @Value("${ecmp.encodingKey:}")
    private String encodingKey;

    /**
     * 检查签名
     *
     * @param timestamp 时间戳
     * @param nonce     随机数
     * @param signature 签名
     * @return true 通过
     */
    public boolean checkSignature(String timestamp, String nonce, String signature) {
        // 时间验证
        long times = Long.parseLong(timestamp);
        if (System.currentTimeMillis() - times > 5 * 60 * 60) {
            return false;
        }
        String sha1Signature = Sha1.gen(signatureKey, timestamp, nonce);
        return Objects.equals(sha1Signature, signature);
    }

    /**
     * 解密消息
     *
     * @param msgEncrypt 加密的消息
     * @return 消息
     */
    @SuppressWarnings("unchecked")
    public Map<String, String> decryptMsg(String msgEncrypt) {
        String decrypt = decrypt(msgEncrypt);
        LOGGER.info("解密消息:[{}]", decrypt);
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(decrypt, Map.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Collections.emptyMap();
    }

    /**
     * 解密消息
     *
     * @param msgEncrypt 加密的消息
     * @return 消息
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> decryptMsgList(String msgEncrypt) {
        String decrypt = decrypt(msgEncrypt);
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, Map.class);
            return objectMapper.readValue(decrypt, javaType);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Collections.emptyList();
    }

    private String decrypt(String cipherText) {
        byte[] original;
        try {
            // 移除 encodingKey 中的空白字符
            String sanitizedKey = encodingKey.replaceAll("\\s", "");
            byte[] aesKey = Base64Utils.decodeFromString(sanitizedKey);
            // 添加 BouncyCastleProvider
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            // 设置解密模式为 AES 的 CBC 模式
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
            SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
            IvParameterSpec iv = new IvParameterSpec(aesKey, 0, 16);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, iv);
            // 使用 BASE64 对密文进行解码
            byte[] encrypted = Base64.decodeBase64(cipherText);
            // 解密
            original = cipher.doFinal(encrypted);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return new String(original);
    }
}
