package com.ce.scrm.center.service.third.invoke;

import cn.ce.cesupport.ehr.service.AchForOtherAppService;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.center.dao.entity.CurrentBusinessMonthTemporaryTable;
import com.ce.scrm.center.dao.service.CurrentBusinessMonthTemporaryTableService;
import com.ce.scrm.center.service.business.entity.view.CurrentBusinessMonthView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Service
public class AchForOtherThirdService {

    @DubboReference
    private AchForOtherAppService achForOtherAppService;

    @Resource
    private CurrentBusinessMonthTemporaryTableService currentBusinessMonthTemporaryTableService;

    public CurrentBusinessMonthView getCurrentBusinessMonth() {

        //获取当前商务月
        String findBusinessMonth = achForOtherAppService.findBusinessMonth();
        if (StringUtils.isEmpty(findBusinessMonth)) {
            log.info("获取商务月接口返回值为空");
            throw new RuntimeException("商务月获取失败");
        }
        JSONObject parseObject = JSONObject.parseObject(findBusinessMonth);
        if (parseObject == null) {
            log.info("获取商务月接口返回值为空");
            throw new RuntimeException("商务月获取失败");
        }
        Integer status = parseObject.getInteger("status");
        if (status == null || status != 101) {
            log.info("获取商务月接口返回码错误");
            throw new RuntimeException("商务月获取失败");
        }
        String dataObject = parseObject.getString("data");

        CurrentBusinessMonthView currentBusinessMonth = JSONObject.parseObject(dataObject, CurrentBusinessMonthView.class);

        if (currentBusinessMonth == null || StringUtils.isBlank(currentBusinessMonth.getBussinessMonth()) ||
                currentBusinessMonth.getBeginDate() == null || currentBusinessMonth.getEndDate() == null) {
            log.error("ZqCustomerFollowStageSummaryJobHandler currentBusinessMonth={}", currentBusinessMonth);
            throw new RuntimeException("商务月获取失败");
        }

        // 存储商务月 为计算总监工作台月结数据做准备
        LambdaQueryWrapper<CurrentBusinessMonthTemporaryTable> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CurrentBusinessMonthTemporaryTable::getBussinessMonth, currentBusinessMonth.getBussinessMonth());
        CurrentBusinessMonthTemporaryTable one = currentBusinessMonthTemporaryTableService.getOne(queryWrapper);
        if (one == null) {
            CurrentBusinessMonthTemporaryTable save = BeanUtil.copyProperties(currentBusinessMonth, CurrentBusinessMonthTemporaryTable.class);
            currentBusinessMonthTemporaryTableService.save(save);
        }else if (!Objects.equals(one.getEndDate(), currentBusinessMonth.getEndDate()) || !Objects.equals(one.getBeginDate(), currentBusinessMonth.getBeginDate())) {
            one.setBeginDate(currentBusinessMonth.getBeginDate());
            one.setEndDate(currentBusinessMonth.getEndDate());
            currentBusinessMonthTemporaryTableService.updateById(one);
        }

        return currentBusinessMonth;

    }

}
