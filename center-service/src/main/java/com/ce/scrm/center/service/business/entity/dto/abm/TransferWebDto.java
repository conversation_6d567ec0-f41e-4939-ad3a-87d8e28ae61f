package com.ce.scrm.center.service.business.entity.dto.abm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransferWebDto {
    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 每页数量
     */
    private Integer pageSize = 10;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 处理状态
     */
    private Integer processingStatus;

    /**
     * 创建时间开始
     */
    private Date startTime;

    /**
     * 创建时间结束
     */
    private Date endTime;
}
