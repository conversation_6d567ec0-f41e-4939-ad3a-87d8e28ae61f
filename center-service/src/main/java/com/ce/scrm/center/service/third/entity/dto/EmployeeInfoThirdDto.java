package com.ce.scrm.center.service.third.entity.dto;

import lombok.Data;

import java.util.Date;

/**
 * @version 1.0
 * @Description: 员工信息
 * @Author: lijinpeng
 * @Date: 2024/11/15 16:39
 */
@Data
public class EmployeeInfoThirdDto {

    /**
     * 员工ID
     */
    private String id;
    /**
     * 账号
     */
    private String account;
    /**
     * 密码
     */
    private String password;
    /**
     * 初始密码
     */
    private String oriPassword;
    /**
     * 员工姓名
     */
    private String name;
    /**
     * 所属机构
     */
    private String orgId;
    /**
     * 职级
     */
    private String jobGrade;
    /**
     * 工作邮箱
     */
    private String workMail;
    /**
     * 工作电话
     */
    private String officePhone;
    /**
     * ehr的手机号
     */
    private String mobile;
    /**
     * 绑定的手机号
     */
    private String bindMobile;
    /**
     * 数据状态
     */
    private Integer state;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 入职时间
     */
    private Date enterDate;
    /**
     * 离职时间
     */
    private Date dismissDate;
    /**
     * 职位
     */
    private String position;
    /**
     * 数据来源
     */
    private String source;
    private Integer sort;
    /**
     * 是否试用期
     */
    private Integer regularFlag;
    /**
     * hr同步时间
     */
    private Date hrUpdateDate;
    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 大区名称
     */
    private String areaName;
    /**
     * 大区id
     */
    private String areaId;
    /**
     * 分司名称
     */
    private String subName;
    /**
     * 分司id
     */
    private String subId;
    /**
     * 事业部 id
     */
    private String buId;
    /**
     * 事业部名称
     */
    private String buName;

}
