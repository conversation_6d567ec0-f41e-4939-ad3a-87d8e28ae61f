package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.enums.cooperation.ApplyCooperationStatusEnum;
import cn.ce.cesupport.enums.cooperation.ApproveDetailsStatusEnum;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dao.entity.ApplyCooperation;
import com.ce.scrm.center.dao.entity.CmEntWechatApproveDetail;
import com.ce.scrm.center.dao.entity.SmaDictionaryItem;
import com.ce.scrm.center.dao.service.ApplyCooperationService;
import com.ce.scrm.center.dao.service.CmEntWechatApproveDetailService;
import com.ce.scrm.center.dao.service.SmaDictionaryItemService;
import com.ce.scrm.center.service.business.entity.dto.EntWeChatApprovalCallBackDto;
import com.ce.scrm.center.service.business.entity.dto.EntWeChatApprovalDto;
import com.ce.scrm.center.service.business.entity.dto.cooperation.AgreeAndApproveBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.cooperation.AgreeCancelCooperationBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.cooperation.ApplyCancelCooperationBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.cooperation.GetApproveDetailsBusinessDto;
import com.ce.scrm.center.service.business.entity.view.CmEntWechatApproveDetailWebView;
import com.ce.scrm.center.service.constant.SmsTemplateConstants;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.EntWeChatApprovalThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;

import static cn.ce.cesupport.enums.CodeMessageEnum.APPLY_STATE_ERROR;

/**
 * @version 1.0
 * @Description: 合作业务层
 * @Author: lijinpeng
 * @Date: 2024/10/17 15:42
 */
@Slf4j
@Service
public class CooperationBusiness {

    @Resource
    private ApplyCooperationService applyCooperationService;

    @Resource
    private EntWeChatApprovalThirdService entWeChatApprovalThirdService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private CmEntWechatApproveDetailService entWechatApproveDetailService;

    @Resource
    private SendWxMessage sendWxMessage;

    @Resource
    private SmaDictionaryItemService smaDictionaryItemService;

    @Transactional
    public Boolean agreeAndApprove(AgreeAndApproveBusinessDto agreeAndApproveBusinessDto) {

        ApplyCooperation applyCooperation = applyCooperationService.lambdaQuery()
                .eq(ApplyCooperation::getId, agreeAndApproveBusinessDto.getApplyCooperationId())
                .one();

        if(applyCooperation == null) {
            throw new RuntimeException("没有合作信息");
        }
        if(!Objects.equals(0,applyCooperation.getState())) { //申请中
            throw new ApiException(APPLY_STATE_ERROR);
        }

        // process数据准备
        String cooperationSalerId = applyCooperation.getCooperationSalerId();
        String protectSalerId = applyCooperation.getProtectSalerId();
        EntWeChatApprovalDto.Process process = buildProcessData(cooperationSalerId,protectSalerId);

        // 如果没有审批人
        List<String> userIdList = process.getNodeList().get(0).getUserId();
        if(CollectionUtils.isEmpty(userIdList)) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, applyCooperation.getPeriodValidityDay() == null?180:applyCooperation.getPeriodValidityDay());
            Date time = calendar.getTime();

            // 处理合作表状态
            applyCooperationService.lambdaUpdate()
                    .eq(ApplyCooperation::getId, applyCooperation.getId())
                    .set(ApplyCooperation::getState,ApplyCooperationStatusEnum.HEZUOZHONG.getState())
                    .set(ApplyCooperation::getExceedTime,time)
                    .set(ApplyCooperation::getHandleTime,new Date())
                    .set(ApplyCooperation::getUpdateDate,new Date())
                    .set(ApplyCooperation::getUpdateUser,agreeAndApproveBusinessDto.getLoginEmployeeId())
                    .update();
            return true;
        }

        // 获取被保护的客户数据
        String custId = applyCooperation.getCustId();
        Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(custId);
        String protectEmtName = customerData.isPresent()?customerData.get().getCustomerName():"";

        // 获取保护商务数据
        Optional<EmployeeDataThirdView> protectSalerEmployeeData = employeeThirdService.getEmployeeData(protectSalerId);
        String protectSalerName = protectSalerEmployeeData.isPresent()?protectSalerEmployeeData.get().getName():"";

        // 获取合作商务数据
        Optional<EmployeeDataThirdView> cooperationSalerEmployeeData = employeeThirdService.getEmployeeData(cooperationSalerId);
        String approvalSalerName = cooperationSalerEmployeeData.isPresent()?cooperationSalerEmployeeData.get().getName():"";

        //getSummaryList
        List<Object> summaryList = getSummaryList(protectEmtName, protectSalerName, approvalSalerName);

        // 摘要信息
        EntWeChatApprovalDto entWeChatApprovalDto = EntWeChatApprovalDto.builder()
                // 审批发起人
                .creatorUserId(applyCooperation.getCooperationSalerId())
                // 模版id
                .templateId(EntWeChatApprovalThirdService.TEMPLATE_ID)
                .useTemplateApprover(0)
                .process(process)
                .protectEmtName(protectEmtName)
                .description(applyCooperation.getRemark())
                .protectSalerName(protectSalerName)
                .approvalSalerName(approvalSalerName)
                .summaryList(summaryList)
                .build();


        // 发起审批
        String spNo = entWeChatApprovalThirdService.submitApproval(entWeChatApprovalDto);

        // 给双方领导发送消息
        SmaDictionaryItem smaDictionaryItem = smaDictionaryItemService.lambdaQuery().eq(SmaDictionaryItem::getCode, SmsTemplateConstants.SCRM_COOPERATION_AGREE_SEND_MESSAGE).one();
        if(smaDictionaryItem == null) {
            log.error("获取企微消息字典模版失败,code={}",SmsTemplateConstants.SCRM_COOPERATION_AGREE_SEND_MESSAGE);
            throw new RuntimeException("获取企微消息字典模版失败");
        }
        String message = MessageFormat.format(
                smaDictionaryItem.getName(),
                protectEmtName,
                protectSalerName,
                approvalSalerName,
                applyCooperation.getPeriodValidityDay() == null ? 180 : applyCooperation.getPeriodValidityDay()
        );
        log.info("合作通知,message={}",message);
        userIdList.forEach(userId -> {
            log.info("合作通知,userId={}", userId);

            sendWxMessage.sendMessage(userId,message);
        });

        return applyCooperationService.lambdaUpdate()
                .eq(ApplyCooperation::getId, agreeAndApproveBusinessDto.getApplyCooperationId())
                .set(ApplyCooperation::getSpNo, Long.parseLong(spNo))
                .set(ApplyCooperation::getState, ApplyCooperationStatusEnum.APPROVE.getState()) // 审批中
                .update();
    }

    /*
     * @Description 组装摘要信息
     * <AUTHOR>
     * @date 2024/10/21 14:52
     * @param protectEmtName
     * @param protectSalerName
     * @param approvalSalerName
     * @return java.util.List<java.lang.Object>
     */
    @NotNull
    private List<Object> getSummaryList(String protectEmtName, String protectSalerName, String approvalSalerName) {
        EntWeChatApprovalDto.SummaryInfo summaryInfo1 = EntWeChatApprovalDto.SummaryInfo.builder()
                .text(String.format("客户名称: %s", protectEmtName))
                .lang("zh_CN")
                .build();
        Map<String,Object> map1 = new HashMap<String,Object>(){{put("summary_info",summaryInfo1);}};

        EntWeChatApprovalDto.SummaryInfo summaryInfo2 = EntWeChatApprovalDto.SummaryInfo.builder()
                .text(String.format("客户保护商务: %s", protectSalerName))
                .lang("zh_CN")
                .build();
        Map<String,Object> map2 = new HashMap<String,Object>(){{put("summary_info",summaryInfo2);}};

        EntWeChatApprovalDto.SummaryInfo summaryInfo3 = EntWeChatApprovalDto.SummaryInfo.builder()
                .text(String.format("合作发起商务: %s", approvalSalerName))
                .lang("zh_CN")
                .build();
        Map<String,Object> map3 = new HashMap<String,Object>(){{put("summary_info",summaryInfo3);}};
        // 摘要信息
        List<Object> summaryList = Arrays.asList(
                map1,
                map2,
                map3
        );
        return summaryList;
    }

    /*
     * @Description 构建审批数据
     * <AUTHOR>
     * @date 2024/10/21 10:02
     * @param cooperationSalerId
     * @param protectSalerId
     * @return com.ce.scrm.center.service.business.entity.dto.EntWeChatApprovalDto.Process
     */
    private EntWeChatApprovalDto.Process buildProcessData(String cooperationSalerId, String protectSalerId) {

        // 构建审批userIdList数据
        List<String> userIdList = buildProcessUserIdListData(cooperationSalerId,protectSalerId);

        List<EntWeChatApprovalDto.Node> nodeList = new ArrayList<>();
        nodeList.add(
                EntWeChatApprovalDto.Node.builder()
                        .type(1)//审批人
                        .apvRel(3)//依次审批
                        .userId(userIdList)
                        .build()
        );

        return EntWeChatApprovalDto.Process.builder().nodeList(nodeList).build();
    }

    /*
     * @Description 构建审批userIdList数据
     * <AUTHOR>
     * @date 2024/10/21 10:01
     * @param cooperationSalerId
     * @param protectSalerId
     * @return java.util.List<java.lang.String>
     */
    @NotNull
    public List<String> buildProcessUserIdListData(String cooperationSalerId, String protectSalerId) {

        if(cooperationSalerId == null) {
            throw new RuntimeException("合作人id为空");
        }
        if (protectSalerId == null) {
            throw new RuntimeException("保护商务为空");
        }
        if(Objects.equals(cooperationSalerId, protectSalerId)) {
            throw new RuntimeException("合作人和保护商务为一个人");
        }

        List<String> result = new ArrayList<>();
        // 保护
        Optional<EmployeeDataThirdView> employeeDataProtect = employeeThirdService.getEmployeeData(protectSalerId);
        EmployeeDataThirdView employeeDataThirdViewProtect = employeeDataProtect.get();
        String orgIdProtect = employeeDataThirdViewProtect.getOrgId();
        String buIdProtect = employeeDataThirdViewProtect.getBuId();
        String subIdProtect = employeeDataThirdViewProtect.getSubId();
        // 合作
        Optional<EmployeeDataThirdView> employeeDataCooperation = employeeThirdService.getEmployeeData(cooperationSalerId);
        EmployeeDataThirdView employeeDataThirdViewCooperation = employeeDataCooperation.get();
        String orgIdCooperation = employeeDataThirdViewCooperation.getOrgId();
        String buIdCooperation = employeeDataThirdViewCooperation.getBuId();
        String subIdCooperation = employeeDataThirdViewCooperation.getSubId();

        if(orgIdProtect != null && Objects.equals(orgIdProtect, orgIdCooperation)) {// 同部门判断
            // 部门经理
            Optional<EmployeeLiteThirdView> orgLeaderProtect = employeeThirdService.getOrgLeader(orgIdProtect);
            if(orgLeaderProtect.isPresent() && !StringUtils.isEmpty(orgLeaderProtect.get().getId())) {
                result.add(orgLeaderProtect.get().getId());
            }
        }else if (subIdProtect != null && Objects.equals(subIdProtect, subIdCooperation)) {//同分司
            // 保护部门经理
            Optional<EmployeeLiteThirdView> orgLeaderProtect = employeeThirdService.getOrgLeader(orgIdProtect);
            if(orgLeaderProtect.isPresent() && !StringUtils.isEmpty(orgLeaderProtect.get().getId())) {
                result.add(orgLeaderProtect.get().getId());
            }
            // 分司部门经理
            Optional<EmployeeLiteThirdView> orgLeaderCooperation = employeeThirdService.getOrgLeader(orgIdCooperation);
            if(orgLeaderCooperation.isPresent() && !StringUtils.isEmpty(orgLeaderCooperation.get().getId())) {
                result.add(orgLeaderCooperation.get().getId());
            }
            // 保护事业部
            if(buIdProtect!= null) {
                Optional<EmployeeLiteThirdView> buLeaderProtect = employeeThirdService.getOrgLeader(buIdProtect);
                if(buLeaderProtect.isPresent() && !StringUtils.isEmpty(buLeaderProtect.get().getId())) {
                    result.add(buLeaderProtect.get().getId());
                }
            }
            // 合作的事业部
            if(buIdCooperation != null && !Objects.equals(buIdProtect, buIdCooperation) ) {
                Optional<EmployeeLiteThirdView> buLeaderCooperation = employeeThirdService.getOrgLeader(buIdCooperation);
                if(buLeaderCooperation.isPresent() && !StringUtils.isEmpty(buLeaderCooperation.get().getId())) {
                    result.add(buLeaderCooperation.get().getId());
                }
            }
            // 分司总监
            Optional<EmployeeLiteThirdView> subLeaderProtect = employeeThirdService.getOrgLeader(subIdProtect);
            if(subLeaderProtect.isPresent() && !StringUtils.isEmpty(subLeaderProtect.get().getId())) {
                result.add(subLeaderProtect.get().getId());
            }
        }else if (subIdProtect != null && !Objects.equals(subIdProtect, subIdCooperation)) {//不同分司
            // 保护部门经理
            Optional<EmployeeLiteThirdView> orgLeaderProtect = employeeThirdService.getOrgLeader(orgIdProtect);
            if(orgLeaderProtect.isPresent() && !StringUtils.isEmpty(orgLeaderProtect.get().getId())) {
                result.add(orgLeaderProtect.get().getId());
            }
            // 合作部门经理
            Optional<EmployeeLiteThirdView> orgLeaderCooperation = employeeThirdService.getOrgLeader(orgIdCooperation);
            if(orgLeaderCooperation.isPresent() && !StringUtils.isEmpty(orgLeaderCooperation.get().getId())) {
                result.add(orgLeaderCooperation.get().getId());
            }
            // 保护事业部
            if(buIdProtect!= null) {
                Optional<EmployeeLiteThirdView> buLeaderProtect = employeeThirdService.getOrgLeader(buIdProtect);
                if(buLeaderProtect.isPresent() && !StringUtils.isEmpty(buLeaderProtect.get().getId())) {
                    result.add(buLeaderProtect.get().getId());
                }
            }
            // 合作的事业部
            if(buIdCooperation != null && !Objects.equals(buIdProtect, buIdCooperation) ) {
                Optional<EmployeeLiteThirdView> buLeaderCooperation = employeeThirdService.getOrgLeader(buIdCooperation);
                if(buLeaderCooperation.isPresent() && !StringUtils.isEmpty(buLeaderCooperation.get().getId())) {
                    result.add(buLeaderCooperation.get().getId());
                }
            }
            // 保护分司总监
            Optional<EmployeeLiteThirdView> subLeaderProtect = employeeThirdService.getOrgLeader(subIdProtect);
            if(subLeaderProtect.isPresent() && !StringUtils.isEmpty(subLeaderProtect.get().getId())) {
                result.add(subLeaderProtect.get().getId());
            }
            // 合作分司总监
            Optional<EmployeeLiteThirdView> subLeaderCooperation = employeeThirdService.getOrgLeader(subIdCooperation);
            if(subLeaderCooperation.isPresent() && !StringUtils.isEmpty(subLeaderCooperation.get().getId())) {
                result.add(subLeaderCooperation.get().getId());
            }
        }

//        if(CollectionUtils.isEmpty(result)) {
//            log.error("审批人为空cooperationSalerId：{},protectSalerId：{}",cooperationSalerId,protectSalerId);
//            throw new RuntimeException("审批人为空");
//        }
        return result;
    }

    /*
     * @Description 申请取消合作
     * <AUTHOR>
     * @date 2024/10/23 15:01
     * @param applyCancelCooperationBusinessDto
     * @return java.lang.Boolean
     */
    public Boolean applyCancelCooperation(ApplyCancelCooperationBusinessDto applyCancelCooperationBusinessDto) {
        ApplyCooperation applyCooperation = applyCooperationService.lambdaQuery().eq(ApplyCooperation::getId, applyCancelCooperationBusinessDto.getApplyCooperationId()).one();
        if(!Objects.equals(applyCooperation.getState(),ApplyCooperationStatusEnum.HEZUOZHONG.getState())) {
            throw new RuntimeException("合作状态异常，取消合作状态失败！");
        }
        boolean update = applyCooperationService.lambdaUpdate()
                .eq(ApplyCooperation::getId, applyCancelCooperationBusinessDto.getApplyCooperationId())
                .set(ApplyCooperation::getState, ApplyCooperationStatusEnum.APPLY_CANCELING.getState())
                .set(ApplyCooperation::getApplyCancelSalerId,applyCancelCooperationBusinessDto.getSalerId())
                .update();
        return update;
    }

    /*
     * @Description 同意取消合作
     * <AUTHOR>
     * @date 2024/10/23 15:17
     * @param agreeCancelCooperationBusinessDto
     * @return java.lang.Boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean agreeCancelCooperation(AgreeCancelCooperationBusinessDto agreeCancelCooperationBusinessDto) {
        ApplyCooperation applyCooperation = applyCooperationService.lambdaQuery().eq(ApplyCooperation::getId, agreeCancelCooperationBusinessDto.getApplyCooperationId()).one();
        if(!Objects.equals(applyCooperation.getState(),ApplyCooperationStatusEnum.APPLY_CANCELING.getState())) {
            throw new RuntimeException("合作状态异常，同意合作状态失败！");
        }
        boolean update = applyCooperationService.lambdaUpdate()
                .eq(ApplyCooperation::getId, agreeCancelCooperationBusinessDto.getApplyCooperationId())
                .set(ApplyCooperation::getState, ApplyCooperationStatusEnum.CANCELED.getState())
                .update();

        // 合作取消之后 发送消息给对应的经理、总监
        SmaDictionaryItem smaDictionaryItem = smaDictionaryItemService.lambdaQuery().eq(SmaDictionaryItem::getCode, SmsTemplateConstants.SCRM_COOPERATION_CANCEL).one();
        if(smaDictionaryItem == null) {
            log.error("发送企业微信失败，未找到消息模板,{}", SmsTemplateConstants.SCRM_COOPERATION_CANCEL);
        }
        Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(applyCooperation.getCustId());
        if(!customerData.isPresent()) {
            log.error("客户不存在,custId:{}", applyCooperation.getCustId());
            throw new RuntimeException("客户不存在");
        }
        String customerName = customerData.get().getCustomerName();
        String message = MessageFormat.format(smaDictionaryItem.getName(), customerName);
        List<String> userIdList = buildProcessUserIdListData(applyCooperation.getCooperationSalerId(),applyCooperation.getProtectSalerId());
        for (String userId : userIdList) {
            // 发消息
            sendWxMessage.sendMessage(userId,message);
        }
        return update;
    }

    /*
     * @Description 获取审批详情
     * <AUTHOR>
     * @date 2024/10/23 15:39
     * @param getApproveDetailsBusinessDto
     * @return com.ce.scrm.center.service.business.entity.view.CmEntWechatApproveDetailWebView
     */
    public CmEntWechatApproveDetailWebView getApproveDetailsBySpNo(GetApproveDetailsBusinessDto getApproveDetailsBusinessDto) {
        CmEntWechatApproveDetail cmEntWechatApproveDetail = entWechatApproveDetailService.lambdaQuery()
                .eq(CmEntWechatApproveDetail::getSpNo, getApproveDetailsBusinessDto.getSpNo())
                .orderByDesc(CmEntWechatApproveDetail::getCreateTime)
                .last("LIMIT 1")
                .one();

        if(cmEntWechatApproveDetail == null) {
            return new CmEntWechatApproveDetailWebView();
        }

        EntWeChatApprovalCallBackDto.SpRecord spRecord = JSON.parseObject(cmEntWechatApproveDetail.getSpRecord(), EntWeChatApprovalCallBackDto.SpRecord.class);

        if(spRecord == null || CollectionUtils.isEmpty(spRecord.getDetails())) {
            return new CmEntWechatApproveDetailWebView();
        }

        List<EntWeChatApprovalCallBackDto.Details> details = spRecord.getDetails();
        List<CmEntWechatApproveDetailWebView.SpRecordDetails> spRecordDetailsList = new ArrayList<>();
        for (EntWeChatApprovalCallBackDto.Details detail : details) {
            String empId = detail.getApprover()!=null?detail.getApprover().getUserId():"";
            Optional<EmployeeDataThirdView> employeeData = employeeThirdService.getEmployeeData(empId);
            String empName = employeeData.isPresent()?employeeData.get().getName():"";
            CmEntWechatApproveDetailWebView.SpRecordDetails build = CmEntWechatApproveDetailWebView.SpRecordDetails.builder()
                    .empId(empId)
                    .empName(empName)
                    .speech(detail.getSpeech())
                    .spStatus(detail.getSpStatus())
                    .spStatusName(ApproveDetailsStatusEnum.getNameByState(Integer.parseInt(detail.getSpStatus())))
                    .spTime(detail.getSpTime() == null || detail.getSpTime() == 0L ? null : new Date(detail.getSpTime() * 1000L))
                    .build();
            spRecordDetailsList.add(build);
        }

        CmEntWechatApproveDetailWebView cmEntWechatApproveDetailWebView = BeanUtil.copyProperties(cmEntWechatApproveDetail, CmEntWechatApproveDetailWebView.class);
        cmEntWechatApproveDetailWebView.setSpRecordList(spRecordDetailsList);
        return cmEntWechatApproveDetailWebView;
    }

    /*
     * @Description 拒绝取消合作
     * <AUTHOR>
     * @date 2024/10/24 11:35
     * @param agreeCancelCooperationBusinessDto
     * @return java.lang.Boolean
     */
    public Boolean disagreeCancelCooperation(AgreeCancelCooperationBusinessDto agreeCancelCooperationBusinessDto) {
        ApplyCooperation applyCooperation = applyCooperationService.lambdaQuery().eq(ApplyCooperation::getId, agreeCancelCooperationBusinessDto.getApplyCooperationId()).one();
        if(!Objects.equals(applyCooperation.getState(),ApplyCooperationStatusEnum.APPLY_CANCELING.getState())) {
            throw new RuntimeException("合作状态异常，拒绝合作状态失败！");
        }
        boolean update = applyCooperationService.lambdaUpdate()
                .eq(ApplyCooperation::getId, agreeCancelCooperationBusinessDto.getApplyCooperationId())
                .set(ApplyCooperation::getState, ApplyCooperationStatusEnum.HEZUOZHONG.getState())
                .update();
        return update;
    }

}
