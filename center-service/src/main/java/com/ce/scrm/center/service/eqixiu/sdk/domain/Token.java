package com.ce.scrm.center.service.eqixiu.sdk.domain;

/**
 * 易企秀接口调用token
 *
 * <AUTHOR>
 */
public class Token {

    /**
     * token 值
     */
    private String value;

    /**
     * 所属企业ID
     */
    private String corpId;

    /**
     * 类型,当前只有Server类型
     */
    private String type = "Server";

    /**
     * 有效时间,单位秒
     */
    private int expires;

    public Token(String value, String corpId, int expires) {
        this.value = value;
        this.corpId = corpId;
        this.expires = expires;
    }

    public String getValue() {
        return value;
    }

    public String getCorpId() {
        return corpId;
    }

    public String getType() {
        return type;
    }

    public int getExpires() {
        return expires;
    }
}
