package com.ce.scrm.center.service.business.entity.dto.segment;

import lombok.Data;

import java.io.Serializable;

/**
 * 追踪线索分页
 */
@Data
public class TrackClueSegmentPageBusinessDto implements Serializable {
	private static final long serialVersionUID = 1L;

	private String areaId;

	private String subId;

	private String buId;

	private String deptId;

	private String salerId;

	/**
	 * 页码
	 */
	private Integer pageNum = 1;
	/**
	 * 每页数量
	 */
	private Integer pageSize = 10;

	//--------以下登录人信息--------

	/**
	 * 员工ID
	 */
	private String loginEmployeeId;

	private String loginAreaId;

	private String loginSubId;

	private String loginBuId;

	/**
	 * 部门ID
	 */
	private String loginOrgId;

	/**
	 * 职位
	 */
	private String loginPosition;

}
