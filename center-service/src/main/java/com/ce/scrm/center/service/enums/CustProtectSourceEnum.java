package com.ce.scrm.center.service.enums;

import lombok.Getter;

/**
 * CmCustProtect客户来源枚举
 * <AUTHOR>
 * @date 2024/7/23
 * @version 1.0.0
 **/
@Getter
public enum CustProtectSourceEnum {

    /** 释放 */
    RELEASE(1, "释放"),
    /** 流失 */
    LOSS(2, "流失");

    private final Integer value;
    private final String label;

    CustProtectSourceEnum(Integer value, String label) {
        this.label = label;
        this.value = value;
    }
}
