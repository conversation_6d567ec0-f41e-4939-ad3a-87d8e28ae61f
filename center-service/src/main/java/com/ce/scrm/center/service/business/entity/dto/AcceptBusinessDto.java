package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 商机接口或拒绝
 * @Author: lijinpeng
 * @Date: 2025/3/27 11:27
 */
@Data
public class AcceptBusinessDto implements Serializable {

    /**
     * business_opportunity 表 id
     */
    @Valid
    private String id;

    /**
     * 是否接受商机：0不接受1接受
     */
    @Valid
    private Integer acceptFlag;

    /**
     * 接收情况下 需要选择进度
     */
    private Integer followRate;

    /**
     * 不接受的原因
     */
    private String waiveExplain;

    // --------------------登录信息

    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 员工名称
     */
    private String loginEmployeeName;
    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;

    /**
     * 事业部id
     */
    private String loginBuId;

    /**
     * 事业部名称
     */
    private String loginBuName;

    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;
    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;
    /**
     * 手机号
     */
    private String loginMobile;
    /**
     * 工作邮箱
     */
    private String loginWorkMail;
    /**
     * 职位
     */
    private String loginPosition;

}
