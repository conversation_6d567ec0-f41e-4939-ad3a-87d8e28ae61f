package com.ce.scrm.center.service.business.entity.view.customer;

import com.ce.scrm.center.service.business.entity.view.CustomerLeadsDubboView;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerTagDubboView;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @project scrm-center
 * <AUTHOR>
 * @date 2025/7/16 11:10:14
 * @version 1.0
 * 查询客户ES业务层返回数据
 */
@Data
public class CustomerESBusinessView implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 搜客宝公司唯一ID
     */
    private String pid;

    /**
     * 客户类型：1、国内企业，2、个人，3、国外及港澳台
     */
    private Integer customerType;

    /**
     * 客户/企业名称
     */

    private String customerName;

    /**
     * 证件类型：1、营业执照，2、身份证，3、军人身份证，4、护照，5、港澳通行证
     */
    private Integer certificateType;

    /**
     * 证件编码
     */
    private String certificateCode;

    /**
     * 登记状态
     */
    private String checkInState;

    /**
     * 成立日期
     */
    private LocalDate establishDate;

    /**
     * 注册资本
     */
    private String registerCapital;

    /**
     * 工商注册号
     */
    private String registerNo;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 营业开始时间
     */
    private LocalDate openStartTime;

    /**
     * 营业结束时间
     */
    private LocalDate openEndTime;

    /**
     * 核准日期
     */
    private LocalDate approveDate;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 登记机关
     */
    private String registrationOrg;

    /**
     * 一级国标行业编码
     */
    private String firstIndustryCode;

    /**
     * 一级国标行业名称
     */
    private String firstIndustryName;

    /**
     * 二级国标行业编码
     */
    private String secondIndustryCode;

    /**
     * 二级国标行业名称
     */
    private String secondIndustryName;


    /**
     * 三级国标行业编码
     */
    private String thirdIndustryCode;

    /**
     * 三级国标行业名称
     */
    private String thirdIndustryName;


    /**
     * 四级国标行业编码
     */
    private String fourthIndustryCode;

    /**
     * 四级国标行业名称
     */
    private String fourthIndustryName;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
     * 需要db-es转换
     */
    private Integer stage;

    /**
     * 客户创建方式：1、大数据，2、商务创建，3、市场商机
     */
    private Integer createWay;

    /**
     * 客户来源
     */
    private String labelFrom;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者凭证（废弃）
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人凭证（废弃）
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;

    /**
     * 无效标记：1、无效，0、有效
     */
    private Integer invalidFlag;

    /**
     * 是否是外贸用户
     */
    private Integer tagWaimao;

    /**
     * 是否门户连带客户 0:否，1:是
     */
    private Integer tagMenhuRelated;

    /**
     * 是否生态转门户 0:否，1:是
     */
    private Integer tagEcoToMenhu;

    /**
     * 是否生态客户 0:否，1:是
     */
    private Integer tagEcoCust;

    /**
     * 是否数字版本门户 0:否，1:是
     */
    private Integer tagMenhuDigital;

    /**
     * 是否2023版本门户 0:否，1:是
     */
    private Integer tagMenhu2023;

    /**
     * 是否低版本门户 0:否，1:是
     * 需要db-es转换
     */
    private Integer tagMenhuLower;

    /**
     * 是否门户应升已升客户 0:否，1:是
     */
    private Integer tagMenhuUpgradeableUpgrade;

    /**
     * 是否门户应升级客户 0:否，1:是
     */
    private Integer tagMenhuUpgradeable;

    /**
     * 是否门户应续已续客户 0:否，1:是
     */
    private Integer tagMenhuRenewableRenew;

    /**
     * 是否门户应续客户 0:否，1:是
     */
    private Integer tagMenhuRenewable;

    /**
     * 是否交叉购买客户 0:否，1:是
     */
    private Integer tagCrossBuy;

    /**
     * 是否纯门户客户 0:否，1:是
     */
    private Integer tagPureMenhu;

    /**
     * 搜客宝 1:规模工业，2:规上服务业，3:规上建筑业，4:规上批发零售业，5:规上住宿餐饮业，6:规上房地产开发与经营业
     */
    private String tagFlag7;

    /**
     * 搜客宝 行业 1:律师，2:学校，3:医院
     */
    private String tagFlag8;

    /**
     * 科技型企业
     */
    private String tagTechcompany;

    /**
     * 门户新客户首次购买时间
     */
    private Date tagMenhuNewTime;

    /**
     * 门户新客户首次购买产品类别 0:低版本 1:门户2023 2:数字门户
     */
    private String tagMenhuNewCategory;

    /**
     * 是否是报价客户 0:否 1:是
     */
    private Integer tagQuoteCust;

    /**
     * 推荐客户的id
     */
    private String recommendCustId;

    /**
     * 推荐客户创建时间
     */
    private LocalDateTime recommendCustCreateTime;


    /**
     * 是否保有客户 0:否，1:是
     */
    private Integer tagRetainCust;

    /**
     * 到期时间
     */
    private Date tagRetainTime;

    /**
     * 是否流失客户 0:否，1:是
     */
    private Integer tagLostCust;

    /**
     * 流失时间
     */
    private Date tagLostTime;

    /**
     * 是否转介绍新客 0:否，1:是
     */
    private Integer tagInvitedNewCust;

    /**
     * 是否合格新客 0:否，1:是
     */
    private Integer tagQualifiedNewCust;

    /**
     * 是否合格老客0:否，1:是
     */
    private Integer tagQualifiedOldCust;

    /**
     * 是否低价值客户 0:否 1:是
     */
    private Integer tagLowValue;

    /**
     * 所属商务id
     * 需要db-es转换
     */
    private String salerId;

    /**
     * 部门id
     * 需要db-es转换
     */
    private String deptId;

    /**
     * 分公司ID
     * 需要db-es转换
     */
    private String subId;

    /**
     * 区域ID
     * 需要db-es转换
     */
    private String areaId;

    /**
     * 状态值（1、保护；2、总监；3、经理；4、客户池）
     * 需要db-es转换
     */
    private String status;

    /**
     * 客户阶段。(2：保护跟进；3：网站客户；4：非网站客户)
     * 需要db-es转换
     */
    private String tradeProductType;

    /**
     * 阶段性保护时间
     * 需要db-es转换
     */
    private Date protectTime;

    /**
     * 已保护天数(仅2023年以后数据)
     */
    private Integer protectDay;

    /**
     * 本月打卡次数
     * 需要db-es转换
     */
    private Integer clockCountMonth;

    /**
     * 累计打卡次数
     * 需要db-es转换
     */
    private Integer clockCount;

    /**
     * 客户订单首次支付时间
     * 需要db-es转换
     */
    private Date firstPaymentTime;

    /**
     * 成为合格新客时间
     */
    private Date tagQualifiedNewCustTime;

    private String tagFlag12;

    /**
     * 是否是大客户（ka）新
     */
    private Integer tagDakehu;

    /**
     * 客户分层
     */
    private Integer customerLayer;

    /**
     * 客户等级
     */
    private Integer customerGrade;

    /**
     * CDP标签user_tag_sfvwcjkh20250530 未成交客户是否是vip判断依据
     */
    private Integer tagWeichengjiaoVip;

    /**
     * 法人
     */
    private String legalPerson;

    /*
     * 分发时间：指跨境ABM项目，leads分发给SDR的时间
     */
    private Date distributeTime;

    /*
     * 保护释放时间
     */
    private Date releaseTime;

    /*
     * 线索产出时间
     */
    private Date leadsCreateTime;

    /*
     * 来源编码
     */
    private String leadsSourceCode;

    /*
     * 意愿编码
     */
    private String leadsIntentCode;

    /*
     * 事业部ID
     */
    private String buId;

    /*
     * 保护结束时间
     */
    private Date protectEndTime;

    /*
     * 最近拜访类型
     */
    private Integer lastVisitType;

    /*
     * 最近拜访时间
     */
    private Date lastFollowTime;

    /*
     * 最近上门时间
     */
    private Date lastSiteTime;

    /**
     * 绑定客户标识 0未绑定 1绑定
     */
    private Integer bindFlag;

    /**
     * 商机是否确认标记 0 正常（已经确认） 1 未确认 2 流转过一次-未确认
     */
    private Integer businessOpportunityConfirmationFlag;

    /**
     * 分发渠道
     */
    private Integer distributeChannel;

    /**
     * 推送销售审核状态
     */
    private Integer reviewStatus;

    /**
     * 跟进次数
     */
    private Integer followCount;

    /**
     * 上门次数
     */
    private Integer siteCount;

    /*
     * SDR跟进时间
     */
    private Date lastSdrFollowTime;

    /**
     * SDR跟进次数
     */
    private Integer sdrFollowCount;

    /**
     * 回执终止时间
     */
    private Date receiptEndTime;

    /**
     * 是否需要回执：0：否 1： 是
     */
    private Integer receiptFlag;

    /**
     * 需要回执的商务
     */
    private String receiptSalerId;

    /**
     * 官网情况
     */
    private Integer officialWebsiteFlag;

    /**
     * 标签得分
     */
    private BigDecimal tagScore;

    /**
     * 客户标签列表
     */
    private List<CustomerESTagBusinessView> tagList;

    /**
     * 客户leads列表
     */
    private List<CustomerLeadsDubboView> customerLeadsList;

    /**
     * 联系人列表
     */
    private List<ContactPersonBusinessView> contactPersonList;

    /**
     * 客户标签列表
     */
    private List<CustomerTagDubboView> customerTagList;

}
