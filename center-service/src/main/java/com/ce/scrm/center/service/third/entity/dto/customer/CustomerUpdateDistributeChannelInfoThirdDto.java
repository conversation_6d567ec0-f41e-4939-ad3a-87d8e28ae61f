package com.ce.scrm.center.service.third.entity.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2024/12/9 11:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerUpdateDistributeChannelInfoThirdDto implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 分发渠道
	 */
	private Integer distributeChannel;

	/**
	 * 分发时间
	 */
	private Date distributeTime;

	/**
	 * 客户id
	 */
	private String customerId;

	/**
	 * 客户名称
	 */
	private String customerName;

	/**
	 * 操作人
	 */
	private String operator = "1024distribute";
}
