package com.ce.scrm.center.service.business.entity.view.ai;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Description: ai提示词信息
 * @Author: lijinpeng
 * @Date: 2025/2/20 09:41
 */
@Data
public class AiPromptInfoBusinessView implements Serializable {

    private Long id;

    private String title;

    private String content;

    private Integer startFlag;

    private Integer deleteFlag;

    private String operator;

    private String remark;


    private Date createTime;

    private Date updateTime;
    private Integer promptType;


    private List<String> deptIdsList;
    private List<String> buIdsList;
    private List<String> subIdsList;
    private List<String> areaIdsList;

    private String company;


}
