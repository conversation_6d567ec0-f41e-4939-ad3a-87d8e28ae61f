package com.ce.scrm.center.service.enums;

import lombok.Getter;

@Getter
public enum ZqSummaryOrgTypeEnum {

    NATIONWIDE(0,"全国"),
    AREA(1,"区域"),
    SUB(2,"分司"),
    BU(3,"事业部"),
    DEPT(4,"部门"),
    SALER(5,"商务"),
    ;

    private final Integer code;
    private final String name;

    ZqSummaryOrgTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ZqSummaryOrgTypeEnum zqSummaryOrgTypeEnum : values()) {
            if (zqSummaryOrgTypeEnum.getCode().equals(code)) {
                return zqSummaryOrgTypeEnum.getName();
            }
        }
        return null;
    }

}
