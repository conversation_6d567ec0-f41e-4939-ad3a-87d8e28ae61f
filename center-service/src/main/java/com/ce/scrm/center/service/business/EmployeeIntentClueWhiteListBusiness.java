package com.ce.scrm.center.service.business;

import cn.ce.cesupport.emp.enums.BusinessTypeEnum;
import cn.ce.cesupport.emp.enums.OrgTypeEnum;
import cn.ce.cesupport.emp.enums.StateEnum;
import cn.ce.cesupport.emp.service.OrgAppService;
import cn.ce.cesupport.emp.vo.OrgVo;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.EmployeeIntentClueWhiteList;
import com.ce.scrm.center.dao.service.EmployeeIntentClueWhiteListService;
import com.ce.scrm.center.service.business.entity.dto.EmployeeIntentClueWhiteListBusinessDto;
import com.ce.scrm.center.service.business.entity.view.EmployeeIntentClueWhiteListPageBusinessView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * Description: 市场部人员组织
 * @author: JiuDD
 * date: 2024/11/5
 */
@Slf4j
@Service
public class EmployeeIntentClueWhiteListBusiness {
    @Resource
    private EmployeeIntentClueWhiteListService employeeIntentClueWhiteListService;
    @Resource
    private EmployeeThirdService employeeThirdService;
    @DubboReference
    private OrgAppService orgAppService;


    /**
     * Description: 市场部人员组织
     * @author: JiuDD
     * date: 2024/11/5 10:01
     */
    public EmployeeIntentClueWhiteListPageBusinessView getCurrentEmployee(String empId) {
        if (StrUtil.isBlank(empId)) {
            return null;
        }
        // 获取当前登录员工的组织
        EmployeeIntentClueWhiteList currentEmployee = employeeIntentClueWhiteListService.lambdaQuery().eq(EmployeeIntentClueWhiteList::getEmpId, empId).last("LIMIT 1").one();
        if (Objects.isNull(currentEmployee)) {
            return null;
        }
        return CglibUtil.copy(currentEmployee, EmployeeIntentClueWhiteListPageBusinessView.class);
    }

    /**
     * Description: 市场部人员组织
     * @author: JiuDD
     * date: 2024/11/5 10:01
     */
    public Page<EmployeeIntentClueWhiteListPageBusinessView> getEmployeeWhiteList(EmployeeIntentClueWhiteListBusinessDto businessDto) {
        if (Objects.isNull(businessDto) || StrUtil.isBlank(businessDto.getLoginEmployeeId())) {
            return new Page<>();
        }
        String loginEmployeeId = businessDto.getLoginEmployeeId();
        // 获取当前登录员工的组织
        EmployeeIntentClueWhiteList currentEmployee = employeeIntentClueWhiteListService.lambdaQuery().eq(EmployeeIntentClueWhiteList::getEmpId, loginEmployeeId).last("LIMIT 1").one();
        if (Objects.isNull(currentEmployee)) {
            return new Page<>();
        }
        // 获取当前登录员工的组织的所有员工
        Page<EmployeeIntentClueWhiteList> page = Page.of(businessDto.getPageNum(), businessDto.getPageSize());
        LambdaQueryChainWrapper<EmployeeIntentClueWhiteList> lambdaQueryChainWrapper = employeeIntentClueWhiteListService.lambdaQuery()
                .eq(EmployeeIntentClueWhiteList::getOrgType, currentEmployee.getOrgType());
        Page<EmployeeIntentClueWhiteList> dbResultPage = lambdaQueryChainWrapper.page(page);
        Page<EmployeeIntentClueWhiteListPageBusinessView> businessViewPage = BeanUtil.copyProperties(dbResultPage, Page.class);
        if (businessViewPage.getTotal() < 1) {
            return businessViewPage;
        }
        businessViewPage.setRecords(CglibUtil.copyList(dbResultPage.getRecords(), EmployeeIntentClueWhiteListPageBusinessView::new));
        businessViewPage.getRecords().forEach(item -> employeeThirdService.getEmployeeData(item.getEmpId()).ifPresent(employeeData -> {
            item.setEmpName(employeeData.getName());
            item.setDeptId(employeeData.getOrgId());
            item.setDeptName(employeeData.getOrgName());
            item.setPositon(employeeData.getPosition());
        }));
        return businessViewPage;
    }

    /**
     * Description: 区分跨境和中企，中企人员只能看到中企的区域，跨境人员只能看到跨境的区域
     * @author: JiuDD
     * date: 2025/2/18 9:01
     */
    public List<OrgVo> getAreaByOrgType(String empId) {
        if (StrUtil.isBlank(empId)) {
            return null;
        }
        // 获取当前登录员工的组织
        EmployeeIntentClueWhiteList currentEmployee = employeeIntentClueWhiteListService.lambdaQuery().eq(EmployeeIntentClueWhiteList::getEmpId, empId).last("LIMIT 1").one();
        if (Objects.isNull(currentEmployee)) {
            return null;
        }
        Byte orgType = currentEmployee.getOrgType();
        // 0未知 1中企 2跨境
        if (Objects.equals(orgType, (byte) 0)) {
            log.warn("未知组织类型, empId: {}", empId);
            return null;
        }
        // 获取所有区域
        OrgVo orgVo = new OrgVo();
        //区域、分司去掉BUSINESS_TYPE=1 的判断，只有部门需要限定 BUSINESS_TYPE=1
        //orgVo.setBusinessType(BusinessTypeEnum.Commerce.getValue());
        orgVo.setState(StateEnum.Enable.getValue());
        orgVo.setType(OrgTypeEnum.AREA.toString());
        List<OrgVo> areaList = orgAppService.selectList(orgVo);
        // 特殊处理
        List<String> empIds = Arrays.asList("67314","105052","65978");
        if (empIds.contains(empId)) {
            return areaList;
        }else if (Objects.equals(orgType, (byte) 1)) {
            // 去除 areaList 中 id=3950 的数据
            areaList.removeIf(item -> Objects.equals(item.getId(), "3950"));
            return areaList;
        }else{
            //拿区域ID 3950去查
            List<OrgVo> orgVos = orgAppService.selectListByIds(Lists.newArrayList("4343", "3950"));
            return orgVos;
        }
    }
}
