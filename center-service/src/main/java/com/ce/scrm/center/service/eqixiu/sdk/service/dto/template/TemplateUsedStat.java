/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto.template;

/**
 * 模板使用统计
 *
 * <AUTHOR>
 */
public class TemplateUsedStat {

    /**
     * 使用人数
     */
    private int useTotal;

    /**
     * 生成作品数
     */
    private int creationTotal;

    /**
     * 累计浏览数（pv）
     */
    private int pvTotal;

    /**
     * 累计访客数（uv）
     */
    private int uvTotal;

    /**
     * 累计表单数
     */
    private int formTotal;

    /**
     * 分享数(此字段数据仅供参考)
     */
    private int shareTotal;

    public int getUseTotal() {
        return useTotal;
    }

    public void setUseTotal(int useTotal) {
        this.useTotal = useTotal;
    }

    public int getCreationTotal() {
        return creationTotal;
    }

    public void setCreationTotal(int creationTotal) {
        this.creationTotal = creationTotal;
    }

    public int getPvTotal() {
        return pvTotal;
    }

    public void setPvTotal(int pvTotal) {
        this.pvTotal = pvTotal;
    }

    public int getUvTotal() {
        return uvTotal;
    }

    public void setUvTotal(int uvTotal) {
        this.uvTotal = uvTotal;
    }

    public int getFormTotal() {
        return formTotal;
    }

    public void setFormTotal(int formTotal) {
        this.formTotal = formTotal;
    }

    public int getShareTotal() {
        return shareTotal;
    }

    public void setShareTotal(int shareTotal) {
        this.shareTotal = shareTotal;
    }

    @Override
    public String toString() {
        return "{" +
                "useTotal=" + useTotal +
                ", creationTotal=" + creationTotal +
                ", pvTotal=" + pvTotal +
                ", uvTotal=" + uvTotal +
                ", formTotal=" + formTotal +
                ", shareTotal=" + shareTotal +
                '}';
    }
}
