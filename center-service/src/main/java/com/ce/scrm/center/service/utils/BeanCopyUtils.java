package com.ce.scrm.center.service.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * 对象复制
 * @param <T>
 * @param <V>
 */
@Slf4j
@SuppressWarnings("all")
public class BeanCopyUtils<T, V> {


	public static<T, V>  V convertToVo(T t,Class<V> clazz){
		if(null == t) return null;
		try {
			V v = clazz.newInstance();
			BeanUtils.copyProperties(t, v);
			try {
				//自定义增加字段属性写在Vo的overRideVo方法中
				clazz.getMethod("overRideVo", clazz).invoke(v, v);
			}catch (NoSuchMethodException e) {
			}
			return v;
		} catch (InstantiationException e) {
			
		} catch (IllegalAccessException e) {
			
		}catch (InvocationTargetException e) {
			
		}
		return null;
	};

	public static<T, V>  V convertToVo(T t,Class<V> clazz,String... ignoreProperties){
		if(null == t) return null;
		try {
			V v = clazz.newInstance();
			BeanUtils.copyProperties(t, v,ignoreProperties);
			try {
				//自定义增加字段属性写在Vo的overRideVo方法中
				clazz.getMethod("overRideVo", clazz).invoke(v, v);
			}catch (NoSuchMethodException e) {
			}
			return v;
		} catch (InstantiationException e) {

		} catch (IllegalAccessException e) {

		}catch (InvocationTargetException e) {

		}
		return null;
	};

	public static<T, V> List<V> convertToVoList(List<T> list,Class<V> clazz){
		if(null == list) return null;
		List<V> result = new ArrayList<V>();
		for (T t : list) {
			try {
				V v = clazz.newInstance();
				BeanUtils.copyProperties(t, v);
				try {
					//自定义增加字段属性写在Vo的overRideVo方法中
					clazz.getMethod("overRideVo", clazz).invoke(v, v);
				}catch (NoSuchMethodException e) {
				}
				result.add(v);
			} catch (InstantiationException e) {
				
			} catch (IllegalAccessException e) {
				
			}catch (InvocationTargetException e) {
				
			}
		}
		return result;
	}

	public static<T, V> List<V> convertToVoList(List<T> list,Class<V> clazz,String... ignoreProperties){
		if(null == list) return null;
		List<V> result = new ArrayList<V>();
		for (T t : list) {
			try {
				V v = clazz.newInstance();
				BeanUtils.copyProperties(t, v,ignoreProperties);
				try {
					//自定义增加字段属性写在Vo的overRideVo方法中
					clazz.getMethod("overRideVo", clazz).invoke(v, v);
				}catch (NoSuchMethodException e) {
				}
				result.add(v);
			} catch (InstantiationException e) {

			} catch (IllegalAccessException e) {

			}catch (InvocationTargetException e) {

			}
		}
		return result;
	}


	private static Class getTrueType(Field srcField) {
		Type genericType = srcField.getGenericType();
		ParameterizedType pt = (ParameterizedType) genericType;
		Class actulClass = (Class) pt.getActualTypeArguments()[0];
		return actulClass;
	}
	private static Class getMapValueTrueType(Field srcField) {
		Type genericType = srcField.getGenericType();
		ParameterizedType pt = (ParameterizedType) genericType;
		Class actulClass = (Class) pt.getActualTypeArguments()[1];
		return actulClass;
	}
}
