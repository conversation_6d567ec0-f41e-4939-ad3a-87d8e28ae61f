/*
 *   Copyright (c) 2014-2024 北京中网易企秀科技有限公司
 *   All rights reserved.
 *
 *  本源码是北京中网易企秀科技有限公司的商业闭源产品，未经公司明确书面许可，
 *  任何个人或组织不得以任何形式或任何目的对本源码进行复制、修改、传播、
 *  出版或进行其他任何形式的使用。违反上述声明者，本公司将依法追究其法律责任。
 *
 *  本声明适用于中华人民共和国法律，任何因本源码引起的争议均应提交至北京仲裁委员会，按照其仲裁规则进行解决。
 */

package com.ce.scrm.center.service.eqixiu.sdk.service.dto;

import com.ce.scrm.center.service.eqixiu.sdk.common.BaseParam;
import com.ce.scrm.center.service.eqixiu.sdk.common.KnownException;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;

import java.util.Map;

public class SaveConfigCmd extends BaseParam {


    /**
     * 0：自用 1：平台id，在开放管理内获取
     */
    private Long productId;

    /**
     * 集成管理业务key 1002:自主用户系统 1006:互动-自定义规则 1012：互动-自定义抽奖次数 1004: 页面集成
     */
    private String cfgKey;

    /**
     * 规则id 可选传参：若可以针对业务 key可以配置多条的 更新时需要传此字段（比如抽奖规则）
     */
    private Long cfgId;

    private String value;

    public static SaveConfigCmd saveCorpConfig(String cfgKey, String value) {
        SaveConfigCmd cmd = new SaveConfigCmd();
        cmd.setProductId(0L);
        cmd.setCfgKey(cfgKey);
        cmd.setValue(value);
        return cmd;
    }

    public static SaveConfigCmd saveCorpConfig(String cfgKey, Long cfgId, String value) {
        SaveConfigCmd cmd = new SaveConfigCmd();
        cmd.setProductId(0L);
        cmd.setCfgKey(cfgKey);
        cmd.setCfgId(cfgId);
        cmd.setValue(value);
        return cmd;
    }

    public static SaveConfigCmd saveProductConfig(Long productId, String cfgKey, String value) {
        SaveConfigCmd cmd = new SaveConfigCmd();
        cmd.setProductId(productId);
        cmd.setCfgKey(cfgKey);
        cmd.setValue(value);
        return cmd;
    }

    public static SaveConfigCmd saveProductConfig(Long productId, String cfgKey, Long cfgId, String value) {
        SaveConfigCmd cmd = new SaveConfigCmd();
        cmd.setProductId(productId);
        cmd.setCfgKey(cfgKey);
        cmd.setCfgId(cfgId);
        cmd.setValue(value);
        return cmd;
    }

    @Override
    public Map<String, String> getParamsMap() {
        Map<String, String> map = getBaseParamsMap();
        map.put("productId", String.valueOf(productId));
        map.put("value", value);
        if (StrUtil.isNotEmpty(cfgKey)) {
            map.put("cfgKey", cfgKey);
        }
        if (cfgId != null) {
            map.put("cfgId", String.valueOf(cfgId));
        }
        return map;
    }

    @Override
    public void validate() {
        if (StrUtil.isEmpty(cfgKey)) {
            throw new KnownException("cfgKey 不能为空");
        }
        if (StrUtil.isEmpty(value)) {
            throw new KnownException("value 不能为空");
        }
    }

    public Long getCfgId() {
        return cfgId;
    }

    public void setCfgId(Long cfgId) {
        this.cfgId = cfgId;
    }

    public String getCfgKey() {
        return cfgKey;
    }

    public void setCfgKey(String cfgKey) {
        this.cfgKey = cfgKey;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "IntegratedConfig{" +
                "cfgId=" + cfgId +
                ", productId=" + productId +
                ", cfgKey='" + cfgKey + '\'' +
                ", value='" + value + '\'' +
                '}';
    }
}
