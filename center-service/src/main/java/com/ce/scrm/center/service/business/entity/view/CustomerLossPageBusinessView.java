package com.ce.scrm.center.service.business.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 流失客户
 *
 * @author: liyechao
 * date: 2024/9/2
 */
@Data
public class CustomerLossPageBusinessView implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 流失原因
     */
    private String reason;

    /**
     * 流失日期
     */
    private Date preDate;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

}