package com.ce.scrm.center.util.constant;

import cn.hutool.core.lang.UUID;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 公共常量类
 * <AUTHOR>
 * @date 2021/05/30 下午8:29
 * @version 1.0.0
 */
public class UtilConstant {
    /** cat分割符 */
    public final static String CAT_SEPARATOR = ".";
    /** key值拼装分隔符 */
    public final static String KEY_SEPARATOR = "-";
    /** 数据分割符 */
    public final static String DATA_SEPARATOR = ",";
    /** 路径分割符 */
    public final static String PATH_SEPARATOR = "/";
    /** 文件夹分隔符 */
    public final static String FILE_SEPARATOR = "//";
    /** 键值分割符 */
    public final static String KEY_VALUE_SEPARATOR = ":";
    /** 数据分割符 */
    public final static String DATA_VALUE_SEPARATOR = ":::";
    /** 参数连接符 */
    public final static String PARAM_CONCAT_SEPARATOR = "&";
    /** url参数连接符 */
    public final static String URL_PARAM_SEPARATOR = "?";
    /** 参数键值连接符 */
    public final static String PARAM_KEY_VALUE_SEPARATOR = "=";
    /** 初始化容器数量 */
    public final static int INIT_COLLECTION_SIZE = 10;
    /** 金额小数位长度 */
    public final static int AMOUNT_POINT_LENGTH = 2;
    /** 百分比变成小数时要除以100 **/
    public final static String ONE_HUNDRED = "100";
    /** 默认跨时区数 **/
    public final static int DEFAULT_ZONE_NUM = 8;
    /**
     * 操作人默认
     */
    public static class Operator {
        /**
         * 操作人ID
         */
        public final static String OPERATOR_ID = "0";
        /**
         * 操作人名称
         */
        public final static String OPERATOR_NAME = "system";

        /**
         * 操作人名称
         */
        public final static String OPERATOR_CHINESE_NAME = "系统";
    }

    /**
     * mdc相关常量
     */
    public static class Mdc {
        /**
         * 请求唯一ID名称
         */
        public final static String REQUEST_ID_NAME = "traceId";

        /**
         * 请求开始时间名称
         */
        public final static String START_TIME = "startTime";

		/**
	     * 员工id
	     */
	    public final static String SOURCE_EMP_ID = "sourceEmpId";

        /**
         * 放入MDC的属性列表
         */
        public final static Map<String, Supplier<String>> MDC_LIST = new HashMap<>(INIT_COLLECTION_SIZE);

        static {
            MDC_LIST.put(REQUEST_ID_NAME, () -> UUID.fastUUID().toString(true));
            MDC_LIST.put(START_TIME, () -> String.valueOf(System.currentTimeMillis()));
	        MDC_LIST.put(SOURCE_EMP_ID, () -> "");
        }
    }

    /**
     * 方法重试
     */
    public static class MethodRetry {
        /** 重试次数（包含第一次） */
        public final static int MAX_ATTEMPTS = 4;
        /** 重试时间间隔 */
        public final static int DELAY = 2000;
        /** delay时间的间隔倍数 */
        public final static int MULTIPLIER = 0;
        /** 重试之间的最大间隔时间 */
        public final static int MAX_DELAY = 3000;
    }

    /**
     * resultFul请求
     */
    public static class RestFul {
        /** 连接超时时间 */
        public final static int CONNECT_TIME_OUT = 2000;
        /** 读取超时时间 */
        public final static int READ_TIME_OUT = 2000;
    }


}
