package com.ce.scrm.center.util.date;

import cn.hutool.core.collection.CollectionUtil;

import java.util.Comparator;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.ToIntFunction;

public class RankingUtil {

    public static <T> void setRanking(List<T> list, ToIntFunction<T> keyExtractor, BiConsumer<T, Integer> rankSetter) {
        if (CollectionUtil.isEmpty(list)) return;

        list.sort(Comparator.comparingInt(keyExtractor).reversed());

        int currentRank = 1;
        for (int i = 0; i < list.size(); i++) {
            if (i > 0 && keyExtractor.applyAsInt(list.get(i)) != keyExtractor.applyAsInt(list.get(i - 1))) {
                currentRank = i + 1;
            }
            rankSetter.accept(list.get(i), currentRank);
        }

    }

}
