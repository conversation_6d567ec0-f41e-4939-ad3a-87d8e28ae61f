package com.ce.scrm.center.util.thread;

import com.ce.scrm.center.util.thread.core.TaskManager;
import com.ce.scrm.center.util.thread.core.ThreadFactoryManager;
import com.ce.scrm.center.util.thread.enums.ThreadRunnerTypeEnum;
import com.ce.scrm.center.util.thread.core.ThreadPoolManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.stream.IntStream;

/**
 * 测试线程池
 * 测试显示创建线程
 * <AUTHOR>
 * @date 2021/4/13 上午10:16
 * @version 1.0.0
 */
@SuppressWarnings("all")
public class Test {

    private final static Logger LOGGER = LoggerFactory.getLogger(Test.class);

    public static void main(String[] args) {
        LOGGER.error("主任务数据");
        ThreadPoolManager test = new ThreadPoolManager(ThreadRunnerTypeEnum.CPU, new ThreadFactoryManager("test", true));
        IntStream.range(0, 1).boxed().forEach(integer -> {
            test.addExecuteTask(new TaskManager("当前任务序列号" + integer, () -> LOGGER.error("子任务序列号：" + integer)));
        });
        //禁止使用这种方式，会导致traceId丢失
        new Thread(() -> LOGGER.error("子任务序列号：2")).start();
        new Thread(new TaskManager("当前任务序列号:3", () -> LOGGER.error("子任务序列号：3"))).start();
    }
}
