package com.ce.scrm.center.util.date;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * BigDecimal工具类
 */
public class BigDecimalUtils {
	public static String calculatePercent(Long numerator, Long denominator) {
		if (denominator == null || denominator == 0L || numerator == null) {
			return "0.0%";
		}

		// 使用 BigDecimal 计算百分比
		BigDecimal num = new BigDecimal(numerator);
		BigDecimal den = new BigDecimal(denominator);
		BigDecimal percentage = num.multiply(BigDecimal.valueOf(100)).divide(den, 1, RoundingMode.HALF_UP);
		return percentage + "%";
	}
}
