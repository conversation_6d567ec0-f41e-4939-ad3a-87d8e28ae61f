package com.ce.scrm.center.util.thread.core;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程工厂
 * <AUTHOR>
 * @date 2021/05/21 上午11:27
 * @version 1.0.0
 */
public class ThreadFactoryManager implements ThreadFactory {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(ThreadFactoryManager.class);

    /** 线程名称前缀 */
    private final String namePrefix;

    /** 线程类型（是否为守护线程） */
    private final Boolean threadType;

    /** 线程组 */
    private final ThreadGroup threadGroup;

    /** 线程数量 */
    private final AtomicInteger threadNumber = new AtomicInteger(1);

    /**
     * 线程工厂构造
     * @param namePrefix    线程名称
     * @param threadType    线程类型
     * <AUTHOR>
     * @date 2021/05/21 下午1:34
     **/
    public ThreadFactoryManager(String namePrefix, Boolean threadType) {
        SecurityManager s = System.getSecurityManager();
        threadGroup = (s != null) ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
        this.namePrefix = namePrefix + "-thread-";
        this.threadType = threadType;
    }

    /**
     * 自定义创建线程
     * @param runnable 线程任务
     * <AUTHOR>
     * @date 2021/05/21 下午1:35
     * @return java.lang.Thread
     **/
    @Override
    public Thread newThread(Runnable runnable) {
        if (runnable == null) {
            LOGGER.error("自定义创建线程，需要执行的任务不能为空");
            throw new RuntimeException("需要执行的任务不能为空");
        }
        Thread thread = new Thread(threadGroup, runnable, namePrefix + threadNumber.getAndIncrement(), 0);
        //守护线程
        thread.setDaemon(threadType);
        //线程优先级（jdk默认）
        if (thread.getPriority() != Thread.NORM_PRIORITY) {
            thread.setPriority(Thread.NORM_PRIORITY);
        }
        //处理未捕捉的异常
        thread.setUncaughtExceptionHandler((t, e) -> LOGGER.error("当前执行任务为:{}, 未捕获异常信息为:{}", JSON.toJSONString(runnable), e.getMessage(), e));
        return thread;
    }
}
