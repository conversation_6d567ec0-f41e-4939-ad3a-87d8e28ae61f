package com.ce.scrm.center.util.date;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * 日期工具类
 * <AUTHOR>
 * @date 2023/4/8 15:28
 * @version 1.0.0
 **/
@Slf4j
public class DateUtils {
    /**
     * 老客(该自动流转功能上线时间(2024-08-02)之前的成交客户)的打卡考核周期 开始时间是  2024-08-01
     */
    public static final Date START_LIMIT_DAY = DateUtil.truncate(DateUtil.parse("2024-08-01"), DateField.DAY_OF_YEAR);

    /**
     * xxl-job延迟秒数
     * 为了解决xxl-job在提交任务是如果距离当前时间过近，可能导致任务无法执行的问题
     */
    private final static int XXL_JOB_DURATION_SECOND = 1;
    /**
     * 秒转毫秒的进制
     */
    private final static int SECOND_TO_MILLIS = 1000;

	/**
	 * 日期时间格式
	 */
	private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 自定义cron表达式格式
     */
    public static final DateTimeFormatter CUSTOM_CRON_FORMATTER = DateTimeFormatter.ofPattern("ss mm HH dd MM ? yyyy");

    /**
     * 时间转换cron表达式（仅执行一次）
     * @param localDateTime    任务执行时间
     * <AUTHOR>
     * @date 2023/4/8 15:28
     * @return java.lang.String
     **/
    public static String generateCron(LocalDateTime localDateTime) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        long second;
        if (currentDateTime.isAfter(localDateTime) && (second = Duration.between(currentDateTime, localDateTime).toMillis() / SECOND_TO_MILLIS) < XXL_JOB_DURATION_SECOND) {
            localDateTime = localDateTime.plusSeconds(XXL_JOB_DURATION_SECOND - second);
        }
        return localDateTime.format(DateTimeFormatter.ofPattern(DateTimeFormatUtil.XXL_JOB_CRON_FORMAT.getFormat()));
    }

    /**
     * Description: 判断今天是否是 打卡考核点/到账考核点
     * @author: JiuDD
     * @param configStartTime 考核开始时间
     * @param periodDay 周期天数
     * @return boolean true: 今天是打卡考核点/到账考核点，false: 今天不是打卡考核点/到账考核点
     * date: 2024/7/17 18:58
     */
    @Deprecated
    public static boolean isCirculationDay(Date configStartTime, int periodDay) {
        if (configStartTime == null || periodDay == 0) {
            return false;
        }
        if (DateUtil.betweenDay(DateUtil.truncate(configStartTime, DateField.DAY_OF_YEAR), DateUtil.truncate(new Date(), DateField.DAY_OF_YEAR), true) % periodDay == 0) {
            return true;
        }
        return false;
    }

    /**
     * Description: 判断今天是否是 打卡考核点
     * @author: JiuDD
     * @param startTime 考核开始时间
     * @param periodMonth 未打卡自然月数
     * @return boolean true: 今天是打卡考核点，false: 今天不是打卡考核点
     * date: 2024/7/29 19:42
     */
    @Deprecated
    public static boolean isVisitCirculationDay(Date startTime, int periodMonth) {
        if (startTime == null || periodMonth == 0) {
            return false;
        }
        // 若今天不是1号，则不是打卡考核点
        if (DateUtil.dayOfMonth(DateUtil.date()) != 1) {
            return false;
        }
        // startTime 共分为两类：
        // 1. 该功能上线时，对老成交客户统一配置的固定的打卡考核开始日期："2024-08-01"
        // 2. 该功能上线后，新成交客户独立的起始打卡考核开始日期：首次签单日期的“次月首日”。而新成交客户的打卡考核首日，刚刚开始考核，因此不能是打卡考核点
        if (DateUtil.isSameDay(startTime, new Date())) {
            return false;
        }
        // 若今天是1号，则判断是否是打卡考核点
        return DateUtil.betweenMonth(DateUtil.beginOfMonth(startTime), DateUtil.beginOfMonth(new Date()), true) % periodMonth == 0;
    }

    /**
     * Description: 根据开始日期和未打卡自然月数，计算考核周期起止日期
     * @author: JiuDD
     * @param startDate 初始考核日期
     * @param monthWithoutClock 未打卡自然月数
     * @return com.ce.scrm.center.util.date.DateUtils.CirculationLimitDateResult
     * date: 2024/7/29 20:40
     */
    public static CirculationLimitDateResult visitCirculationLimitDate(Date startDate, int monthWithoutClock) {
        // 上一个考核点
        Date previousLimitDate = DateUtil.beginOfMonth(startDate);
        // 下一个考核点
        Date nextLimitDate = DateUtil.offsetMonth(previousLimitDate, monthWithoutClock);
        // 今天
        Date today = DateUtil.beginOfDay(new Date());
        while (nextLimitDate.before(today)) {
            previousLimitDate = nextLimitDate;
            nextLimitDate = DateUtil.offsetMonth(previousLimitDate, monthWithoutClock);
        }
        return new CirculationLimitDateResult().setPreviousLimitDate(previousLimitDate).setNextLimitDate(nextLimitDate);
    }

    /**
     * Description: 获取打卡考核周期开始时间
     *              该自动流转功能上线后，首次成交客户的打卡考核周期开始时间为 首次签单时间（cm_cust_protect 表的 first_sign_time）的次月首日
     *              其他老客 打卡考核周期 开始时间是  2024-08-01
     * @author: JiuDD
     * @param firstSignTime 当前商务保护的这个客户的首次签单时间(即新成交客户的首次签单时间)
     * @return java.util.Date 打卡考核周期开始时间
     * date: 2024/7/25 15:56
     */
    public static Date getStartLimitDay(Date firstSignTime) {
        // 老客(2024-08-02 之前的成交客户) 打卡考核周期 开始时间是  2024-08-01
        Date subLimitDay = START_LIMIT_DAY;
        if (Objects.nonNull(firstSignTime)) {
            log.info("当前客户是首次签单客户，首次签单时间，firstSignTime:{}", firstSignTime);
            // 次月首日
            subLimitDay = DateUtil.beginOfMonth(DateUtil.offsetMonth(firstSignTime, 1));
        }
        return subLimitDay;
    }

    /**
     * Description: 当前客户的考核周期
     * @author: JiuDD
     * @param firstSignTime 当前商务保护的这个客户的首次签单时间(即新成交客户的首次签单时间)
     * @param monthWithoutClock 分司设置的考核周期月数
     * @return com.ce.scrm.center.util.date.DateUtils.CirculationLimitDateResult
     * date: 2024/9/5 13:46
     */
    public static CirculationLimitDateResult getVisitCirculationLimitDate(Date firstSignTime, int monthWithoutClock) {
        // 客户流转的初始考核日期
        Date startLimitDay = getStartLimitDay(firstSignTime);
        // 当前客户的考核周期开始日期(以该功能上线时间2024.08.02为界限，之前成交的客户为老成交，之后成交的客户为新成交。老成交客户考核起始时间统一为"2024.08.01"，新成交客户考核起始时间为首次签单时间（cm_cust_protect 表的 first_sign_time）的次月首日)
        DateUtils.CirculationLimitDateResult visitLimitDate = DateUtils.visitCirculationLimitDate(startLimitDay, monthWithoutClock);
        visitLimitDate.setStartLimitDay(startLimitDay);
        return visitLimitDate;
    }

    /**
     * Description: 客户的考核周期（绝对保护期内的客户专用）
     * @author: JiuDD
     * @param absoluteProtectTime 绝对保护期结束时间
     * @param firstSignTime 当前商务保护的这个客户的首次签单时间(即新成交客户的首次签单时间)
     * @param monthWithoutClock 分司设置的考核周期月数
     * @return com.ce.scrm.center.util.date.DateUtils.CirculationLimitDateResult
     * date: 2025/1/14 10:19
     */
    public static CirculationLimitDateResult getVisitCirculationLimitDate(Date absoluteProtectTime, Date firstSignTime, int monthWithoutClock) {
        if (Objects.isNull(absoluteProtectTime)) {
            return getVisitCirculationLimitDate(firstSignTime, monthWithoutClock);
        }
        // 客户考核周期的开始日期(以该功能上线时间2024.08.02为界限，之前成交的客户为老成交，之后成交的客户为新成交。老成交客户考核起始时间统一为"2024.08.01"，新成交客户考核起始时间为首次签单时间（cm_cust_protect 表的 first_sign_time）的次月首日
        Date startLimitDay = getStartLimitDay(firstSignTime);
        DateUtils.CirculationLimitDateResult visitLimitDate = DateUtils.visitCirculationLimitDate(startLimitDay, monthWithoutClock);
        while (visitLimitDate.getNextLimitDate().before(absoluteProtectTime)) {
            visitLimitDate = DateUtils.visitCirculationLimitDate(visitLimitDate.getNextLimitDate(), monthWithoutClock);
        }
        visitLimitDate.setStartLimitDay(startLimitDay);
        return visitLimitDate;
    }

    /**
     * Description: 当前客户是否在考核期
     * @author: JiuDD
     * @param visitLimitDate 当前客户的考核周期
     * @return boolean true: 在考核期，false: 不在考核期
     * date: 2024/9/5 14:06
     */
    public static boolean isInAppraisalPeriod(DateUtils.CirculationLimitDateResult visitLimitDate) {
        if (visitLimitDate == null || visitLimitDate.getPreviousLimitDate() == null || visitLimitDate.getNextLimitDate() == null) {
            return false;
        }
        DateTime now = DateUtil.date();
        return now.after(visitLimitDate.getStartLimitDay());
    }

    @Data
    @Accessors(chain = true)
    public static class CirculationLimitDateResult {
        // 初始考核日期
        Date startLimitDay;
        // 上一个考核点
        Date previousLimitDate;
        // 下一个考核点
        Date nextLimitDate;
    }

	/**
	 * Description: 格式化剩余时间
	 * @param exceedTime 阶段性超期时间
	 * @return java.lang.String 剩余时间格式化字符串
	 */
	public static String formatSurplusDateZh(Date exceedTime) {
		StringBuilder exceedTimeStr = new StringBuilder();
		if (null != exceedTime) {
			long between_days;
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date smdate;
			Date endDate;
			long diff;
			try {
				smdate = sdf.parse(sdf.format(new Date()));
				endDate = sdf.parse(sdf.format(exceedTime));
				Calendar cal = Calendar.getInstance();
				cal.setTime(smdate);
				long time1 = cal.getTimeInMillis();
				cal.setTime(endDate);
				long time2 = cal.getTimeInMillis();
				diff = time2 - time1;
				if (diff >= 0) {
					between_days = diff / (24 * 60 * 1000 * 60);
					if (between_days > 0) {
						exceedTimeStr.append(between_days + "天");
					}
					between_days = diff % (24 * 60 * 1000 * 60);
					between_days = between_days / (60 * 1000 * 60);
					if (between_days >= 0) {
						exceedTimeStr.append(between_days + "小时");
					}
					between_days = diff % (60 * 1000 * 60);
					between_days = between_days / (60 * 1000);
					if (between_days >= 0) {
						exceedTimeStr.append(between_days + "分");
					}
				} else {
					exceedTimeStr.append("已过期");
				}
			} catch (ParseException e) {
				log.error("日期格式化异常", e);
			}

			return exceedTimeStr.toString();
		}
		return "";
	}

	/**
	 * 日期格式化为 yyyy-MM-dd HH:mm:ss
	 * @param date date类型日期
	 * @return 字符串
	 */
	public static String formatDate(Date date) {
		if (date != null) {
			LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
			return FORMATTER.format(localDateTime);
		}
		return null;
	}

	/**
	 * 计算两个日期字符串(yyyy-MM-dd HH:mm:ss) 的差值,单位是天
	 */
	public static Long getDaysBetween(String startDate, String endDate) {
		try {
			LocalDate start = LocalDate.parse(startDate.substring(0, 10), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			LocalDate end = LocalDate.parse(endDate.substring(0, 10), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			return ChronoUnit.DAYS.between(start, end);
		} catch (Exception e) {
			return null;
		}
	}

	public static Date toDate(String dateStr) {
		try {
			LocalDateTime dateTime = LocalDateTime.parse(dateStr, FORMATTER);
			return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
		} catch (Exception e) {
			log.warn("日期格式化异常{}", dateStr);
		}
		return null;
	}
}
