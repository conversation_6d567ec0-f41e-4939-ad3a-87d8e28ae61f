package com.ce.scrm.center.util.thread.enums;

import com.ce.scrm.center.util.thread.core.ThreadPoolParamCalculator;

/**
 * 线程任务类型枚举
 * <AUTHOR>
 * @date 2021/05/19 下午2:20
 * @version 1.0.0
 **/
public enum ThreadRunnerTypeEnum {
    /** IO密集型任务 */
    IO(0, "IO密集型", 0.5),
    /** CPU密集型任务 */
    CPU(1, "CPU密集型", 0.0),
    /***/
    BOTH(2, "综合型", ThreadPoolParamCalculator.DEFAULT_BLOCK_RATE),
    ;
    private Integer code;
    private String name;
    private Double blockRate;

    ThreadRunnerTypeEnum(Integer code, String name, Double blockRate) {
        this.code = code;
        this.name = name;
        this.blockRate = blockRate;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Double getBlockRate() {
        return blockRate;
    }

    public void setBlockRate(Double blockRate) {
        this.blockRate = blockRate;
    }
}
