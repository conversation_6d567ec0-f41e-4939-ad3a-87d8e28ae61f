package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 商机
 * @Author: lijinpeng
 * @Date: 2024/12/26 15:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaBusinessOpportunityWebDto extends LoginInfo implements Serializable {


    /**
     *  首次分配区域id
     */
    @NotNull(message = "区域id不能为空")
    private String firstDistributionAreaId;

    /**
     *  首次分配分司id
     */
    private String firstDistributionSubId;

    /**
     * 商机创建日期-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date startCreateTime;

    /**
     * 商机创建日期-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endCreateTime;

    /**
     * 当前页
     */
    @NotNull
    private Integer currentPage;

    /**
     * 页大小
     */
    @NotNull
    private Integer pageSize;

}
