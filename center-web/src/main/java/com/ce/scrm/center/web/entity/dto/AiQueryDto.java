package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.dao.entity.AiAccessAuth;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.util.gptstreamutil.MessageContentExtend;
import lombok.Data;

import java.util.List;

@Data
public class AiQueryDto extends LoginInfo {

    private String channelId;

    /**
     * pid
     */
    private String pid;
    /**
     * custId
     */
    private String custId;

    /**
     * 商机code
     */
    private String busiOppoCode;

    /**
     * 拓展提示词
     */
    private String extendPrompt;

    /**
     * 是否重新分析，0：否，1：是
     */
    private Integer reAnalyst;

    /**
     * 特殊密钥
     */
    private String gg;


    private List<MessageContentExtend> messageContentList;

    /**
     * promptId
     */
    private String promptId;

    /**
     * 来源 WECOM / pc
     */
    private String platform;


    private String chatId;

    private boolean needSearch;

    private String pptContext;

    private boolean imageGenerator;

    private String analyzeJson;

    private String orgId;

    private String custName;

    private List<AiAccessAuth> aiAccessAuths;

    private boolean convert2md;

    private String systemContent;

}
