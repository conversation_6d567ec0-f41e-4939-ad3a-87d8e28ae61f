package com.ce.scrm.center.web.aop.base;

import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import lombok.Getter;

import java.io.Serializable;

/**
 * 登录信息
 * <AUTHOR>
 * @date 2024/3/11 13:39
 * @version 1.0.0
 */
@Getter
public class LoginInfo implements Serializable {
    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 员工名称
     */
    private String loginEmployeeName;
    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;

    /**
     * 事业部id
     */
    private String loginBuId;

    /**
     * 事业部名称
     */
    private String loginBuName;

    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;
    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;
    /**
     * 手机号
     */
    private String loginMobile;
    /**
     * 工作邮箱
     */
    private String loginWorkMail;
    /**
     * 职位
     */
    private String loginPosition;
    /**
     * 职级
     */
    private String loginJobGrade;
    /**
     * 高呈员工标记
     */
    private Boolean loginGcEmployeeFlag;

    /**
     * 转换属性
     * @param employeeDataThirdView 员工数据
     * <AUTHOR>
     * @date 2024/5/24 下午1:59
     **/
    public void convert(EmployeeDataThirdView employeeDataThirdView){
        this.loginEmployeeId = employeeDataThirdView.getId();
        this.loginEmployeeName = employeeDataThirdView.getName();
        this.loginSubId = employeeDataThirdView.getSubId();
        this.loginSubName = employeeDataThirdView.getSubName();
        this.loginBuId = employeeDataThirdView.getBuId();
        this.loginBuName = employeeDataThirdView.getBuName();
        this.loginOrgId = employeeDataThirdView.getOrgId();
        this.loginOrgName = employeeDataThirdView.getOrgName();
        this.loginAreaId = employeeDataThirdView.getAreaId();
        this.loginAreaName = employeeDataThirdView.getAreaName();
        this.loginMobile = employeeDataThirdView.getMobile();
        this.loginWorkMail = employeeDataThirdView.getWorkMail();
        this.loginPosition = employeeDataThirdView.getPosition();
        this.loginJobGrade = employeeDataThirdView.getJobGrade();
        this.loginGcEmployeeFlag = employeeDataThirdView.getGcEmployeeFlag();
    }
}