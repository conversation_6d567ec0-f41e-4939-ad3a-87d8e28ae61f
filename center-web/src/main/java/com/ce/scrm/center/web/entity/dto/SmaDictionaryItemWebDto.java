package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description: 查询字典表数据
 * @author: lyc
 * date: 2024/7/22
 */
@EqualsAndHashCode
@Data
public class SmaDictionaryItemWebDto extends LoginInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 字典id
     */
    @NotNull(message = "字典ID不能为空")
    private String dictionaryId;


}