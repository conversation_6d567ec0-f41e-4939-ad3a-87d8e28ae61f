package com.ce.scrm.center.web.controller;

import cn.ce.cesupport.emp.vo.OrgVo;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.EmployeeIntentClueWhiteListBusiness;
import com.ce.scrm.center.service.business.entity.view.EmployeeIntentClueWhiteListPageBusinessView;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.controller.base.BaseController;
import com.ce.scrm.center.web.entity.dto.EmployeeIntentClueWhiteListWebPageDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.EmployeeIntentClueWhiteListWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: 市场部人员组织
 * @author: JiuDD
 * date: 2024/11/5 10:01
 */
@Login
@Slf4j
@RestController
@RequestMapping("clue")
public class EmployeeIntentClueWhiteListController extends BaseController {
    @Resource
    private EmployeeIntentClueWhiteListBusiness employeeIntentClueWhiteListBusiness;

    /**
     * Description: 获取当前登录人员(市场部)的人员信息
     * @author: JiuDD
     * @param loginInfo
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.EmployeeIntentClueWhiteListWebView>
     * date: 2024/11/5 10:50
     */
    @PostMapping("getCurrentEmployee")
    public WebResult<EmployeeIntentClueWhiteListWebView> getCurrentEmployee(@RequestBody LoginInfo loginInfo) {
        EmployeeIntentClueWhiteListPageBusinessView businessView = employeeIntentClueWhiteListBusiness.getCurrentEmployee(loginInfo.getLoginEmployeeId());
        if (businessView == null) {
            return WebResult.success(null);
        }
        EmployeeIntentClueWhiteListWebView webView = CglibUtil.copy(businessView, EmployeeIntentClueWhiteListWebView.class);
        return WebResult.success(webView);
    }

    /**
     * Description: 获取当前登录人员(市场部)所在组织的所有人员
     * @author: JiuDD
     * @param webPageDto  入参
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo<com.ce.scrm.center.web.entity.view.EmployeeIntentClueWhiteListWebView>>
     * date: 2024/11/5 10:51
     */
    @PostMapping("getEmployeeWhiteList")
    public WebResult<WebPageInfo<EmployeeIntentClueWhiteListWebView>> getEmployeeWhiteList(@RequestBody EmployeeIntentClueWhiteListWebPageDto webPageDto) {
        Page<EmployeeIntentClueWhiteListPageBusinessView> page = employeeIntentClueWhiteListBusiness.getEmployeeWhiteList(webPageDto.packageBusinessDto(webPageDto));
        WebPageInfo<EmployeeIntentClueWhiteListWebView> webPageInfo = WebPageInfo.pageConversion(page, EmployeeIntentClueWhiteListWebView.class);
        return WebResult.success(webPageInfo);
    }

    /**
     * Description: 获取区域。区分跨境和中企，中企人员获取中企的区域，跨境人员获取跨境的区域
     * @author: JiuDD
     * date: 2025/2/18 9:01
     */
    @PostMapping("getAreaByOrgType")
    public WebResult<Map<String, Object>> getAreaByOrgType(@RequestBody LoginInfo loginInfo) {
        List<OrgVo> areaByOrgType = employeeIntentClueWhiteListBusiness.getAreaByOrgType(loginInfo.getLoginEmployeeId());
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("areaList", areaByOrgType);
        return WebResult.success(dataMap);
    }
}
