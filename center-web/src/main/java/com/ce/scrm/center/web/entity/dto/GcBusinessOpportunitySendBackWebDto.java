package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.service.business.entity.dto.GcBusinessOpportunitySendBackDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 高呈商机回退操作参数
 * <AUTHOR>
 * @date 2024/6/20
 * @version 1.0.0
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class GcBusinessOpportunitySendBackWebDto extends GcBusinessOpportunityOperateWebDto implements Serializable {

    /**
     * 高呈商机退回原因
     */
    private String reasonBack;
    /**
     * 退回类型：1:高呈市场部退回 2:高呈商务退回
     */
    private Integer type;

    /**
    * @Description: 更新回退原因
    * @Param:
    * @return: * @return GcBusinessOpportunityOperateBusinessDto
    * @Author: liyechao
    * @Date: 2024/6/20 下午2:17
    */
    public GcBusinessOpportunitySendBackDto packageOperateBackParam() {
        GcBusinessOpportunitySendBackDto gcBusinessOpportunitySendBackDto = new GcBusinessOpportunitySendBackDto();
        gcBusinessOpportunitySendBackDto.setId(this.getSjId());
        gcBusinessOpportunitySendBackDto.setOperator(this.getLoginEmployeeId());
        gcBusinessOpportunitySendBackDto.setOperatorName(this.getLoginEmployeeName());
        gcBusinessOpportunitySendBackDto.setOperatorName(this.getLoginEmployeeName());
        gcBusinessOpportunitySendBackDto.setReasonBack(this.getReasonBack());
        gcBusinessOpportunitySendBackDto.setType(this.getType());
        return gcBusinessOpportunitySendBackDto;
    }
}