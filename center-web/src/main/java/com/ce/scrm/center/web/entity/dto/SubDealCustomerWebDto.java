package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 成交客户查询入参
 */
@Data
public class SubDealCustomerWebDto extends LoginInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "客户名称不能为空")
    private String custName;

}