package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class EqixiuActivityInfoDto extends LoginInfo {

    /**
     *
     */
    private String id;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动基础地址
     */
    private String activityUrl;

    /**
     * 1 应用场景 2:分司活动
     */
    private Integer activityType;

    /**
     * 活动开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") // 指定格式
    private Date startTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") // 指定格式
    private Date endTime;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 分享标题
     */
    private String shareTitle;

    /**
     * 分享描述
     */
    private String shareDesc;

    /**
     *
     */
    private String sharePic;

    /**
     *
     */
    private String sharePosterInfo;

    /**
     * 可见范围 1: 全部 2 部分
     */
    private Integer visibleRange;


    /**
     * 是否可以卡片分享 0 不可以 1 可以
     */
    private Integer cardShare;

    /**
     * 是否可以海报分享 0 不可以 1 可以
     */
    private Integer posterShare;


    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 每页数量
     */
    private Integer pageSize = 10;


}
