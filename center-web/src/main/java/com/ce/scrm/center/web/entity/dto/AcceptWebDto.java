package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 商机接收/拒绝
 * @Author: lijinpeng
 * @Date: 2025/3/27 11:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AcceptWebDto extends LoginInfo implements Serializable {

    /**
     * business_opportunity 表 id
     */
    @Valid
    private String id;

    /**
     * 是否接受商机：0不接受1接受
     */
    @Valid
    private Integer acceptFlag = 0;

    /**
     * 接收情况下 需要选择进度
     */
    private Integer followRate;

    /**
     * 不接受的原因
     */
    private String waiveExplain;

}
