package com.ce.scrm.center.web.entity.view.abm;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @project scrm-center
 * @ClassName ContactPersonsWebView
 * @description TODO
 * @date 2025/8/8 19:58:10
 */
@Data
public class ContactPersonsWebView {
    /**
     * 联系人id
     */
    private String contactPersonId;
    /**
     * 联系人姓名
     */
    private String contactPersonName;
    /**
     * 性别：1、未知，2、男，3、女
     */
    private Integer gender;
    /**
     * 职位
     */
    private String position;
    /**
     * 联系人部门
     */
    private String department;
    /**
     * 证件类型
     */
    private Integer certificatesType;
    /**
     * 证件号码
     */
    private String certificatesNumber;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区编码
     */
    private String districtCode;
    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系人来源key
     */
    private String sourceKey;

    /**
     * 证件号码
     */
    private String remarks;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 手机
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 微信
     */
    private String wechat;

    /**
     * 企业微信
     */
    private String wecome;

    /**
     * qq
     */
    private String qq;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 默认标记：1、默认，0、不默认
     */
    private Integer defaultFlag;

    /**
     * 0:未开启，仅可以复制
     * 1:可以正常拨打
     * 2:黑名单
     * 3:超频，如果是超频 callMessage 会给出下次拨打提示，前端直接展示
     */
    private String callStatus;

    /**
     * 不能拨打原因
     */
    private String callMessage;

    /**
     * 当 callStatus =1 的时候有值
     * 第三方线路编码
     */
    private String lineCode;

    /**
     * 第三方线路名称
     */
    private String lineName;

    /**
     * 第三方线路类型 1普通 2双呼
     */
    private Integer lineType;

    /**
     * 分公司ID
     */
    private String subId;
}
