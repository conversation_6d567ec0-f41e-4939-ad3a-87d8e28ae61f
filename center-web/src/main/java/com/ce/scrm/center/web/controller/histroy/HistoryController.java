package com.ce.scrm.center.web.controller.histroy;

import com.ce.scrm.center.service.business.HistoryBusiness;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.entity.dto.ClockMarkWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @version 1.0
 * @Description: 历史数据处理
 * @Author: lijinpeng
 * @Date: 2025/4/7 09:11
 */
@Slf4j
@RestController
@RequestMapping("/history")
@Login
public class HistoryController {

    @Resource
    private HistoryBusiness historyBusiness;

//    @PostMapping("recordHandle")
//    public WebResult<Boolean> recordHandle(@RequestBody ClockMarkWebDto clockMarkWebDto) {
//        if (!Objects.equals(clockMarkWebDto.getId(),"lijinpeng123")) {
//            return WebResult.success(false);
//        }
//        Boolean result = historyBusiness.recordHandle();
//        return WebResult.success(result);
//    }


}
