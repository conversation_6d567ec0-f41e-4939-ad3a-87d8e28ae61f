package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 组织机构查询子集以及本身的查询实体类
 * @Author: lijinpeng
 * @Date: 2024/11/21 13:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgChildrenQueryWebDto extends LoginInfo implements Serializable {

    /**
     * 前端传参 areaId、subId、buId、deptId
     * 如果为 -1 则查所有区域
     */
    private String id;

    /**
     * 事业部筛选项（只有id为subId才有效） 默认1 查询带事业部 0不带事业部
     */
    private Integer buFlag = 1;

}
