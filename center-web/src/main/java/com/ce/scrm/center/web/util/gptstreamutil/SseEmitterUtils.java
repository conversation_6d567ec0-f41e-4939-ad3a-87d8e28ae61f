package com.ce.scrm.center.web.util.gptstreamutil;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static com.ce.scrm.center.web.util.gptstreamutil.MessageTypeEnum.CARD_MESSAGE;
import static com.ce.scrm.center.web.util.gptstreamutil.MessageTypeEnum.PATTERN;


/**
 * <AUTHOR>
 * @since 2023/6/27
 */
public class SseEmitterUtils {

    private static final int defaultSpeed = 50;

    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            5,
            10,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.AbortPolicy()
    );

    @SneakyThrows
    public static void sendMessage(String message, SseEmitter sseEmitter) {
        sendMessage(message, sseEmitter, defaultSpeed, null);
    }


    @SneakyThrows
    public static void sendMessage(String message, SseEmitter sseEmitter, String chatId) {
        sendMessage(message, sseEmitter, defaultSpeed, chatId);
    }

    public static void sendMessage(String message, SseEmitter sseEmitter, int speed, String chatId) {
        sendMessage(message, null, sseEmitter, speed, chatId);
    }

    public static void sendMessage(String message, String reasoningContent, SseEmitter sseEmitter) {
        sendMessage(message, reasoningContent, sseEmitter, defaultSpeed, null);
    }

    public static void sendMessage(String message, String reasoningContent, SseEmitter sseEmitter, int speed, String chatId) {
        sendMessage(message, reasoningContent, sseEmitter, speed, chatId, false, new JSONObject());
    }

    @SneakyThrows
    public static void sendMessage(String message, String reasoningContent, SseEmitter sseEmitter, int speed, String chatId, boolean onetime, JSONObject extraInfo) {
        List<String> messages = getDataList(message).stream().filter(StringUtils::hasText).collect(Collectors.toList());
        boolean cardMessage = JSONObject.toJSONString(message).contains(CARD_MESSAGE.getValue());
        executor.submit(() -> {
            try {
                int streamIndex = 0;
                StreamAnswer[] streamAnswers = new StreamAnswer[messages.size()];
                for (String str : messages) {
                    if (str.startsWith(MessageTypeEnum.TEXT.getValue())) {
                        int step = 3; // 每次发送两个字符
                        String contentText = str.replace(MessageTypeEnum.TEXT.getValue(), "");
                        StringBuilder stringBuilder = new StringBuilder();
                        StringBuilder stringBuilderReasoning = new StringBuilder();
                        if (StringUtils.hasText(reasoningContent)) {
                            String reasoningContentText = reasoningContent.replace(MessageTypeEnum.TEXT.getValue(), "");
                            if (onetime) {
                                stringBuilderReasoning.append(reasoningContentText);
                                streamAnswers[streamIndex] = new StreamAnswer("", MessageTypeEnum.TEXT.getType(), stringBuilderReasoning.toString());
                                sseEmitter.send(SseEmitter.event().comment(
                                        buildResponseMessage(Arrays.asList(streamAnswers), false, chatId, cardMessage, extraInfo))
                                );
                            } else {
                                for (int i = 0; i < reasoningContentText.length(); i += step) {
                                    String content = reasoningContentText.substring(i, Math.min(i + step, reasoningContentText.length()));
                                    stringBuilderReasoning.append(content);
                                    streamAnswers[streamIndex] = new StreamAnswer("", MessageTypeEnum.TEXT.getType(), content);
                                    sseEmitter.send(SseEmitter.event().comment(
                                            buildResponseMessage(Arrays.asList(streamAnswers), false, chatId, cardMessage))
                                    );
                                    Thread.sleep(speed);
                                }
                            }
                        }
                        if (onetime) {
                            stringBuilder.append(contentText);
                            streamAnswers[streamIndex] = new StreamAnswer(stringBuilder.toString(), MessageTypeEnum.TEXT.getType(), "");
                            sseEmitter.send(SseEmitter.event().comment(
                                    buildResponseMessage(Arrays.asList(streamAnswers), false, chatId, cardMessage, extraInfo))
                            );
                        } else {
                            for (int i = 0; i < contentText.length(); i += step) {
                                String content = contentText.substring(i, Math.min(i + step, contentText.length()));
                                stringBuilder.append(content);
                                streamAnswers[streamIndex] = new StreamAnswer(content, MessageTypeEnum.TEXT.getType(), "");
                                sseEmitter.send(SseEmitter.event().comment(
                                        buildResponseMessage(Arrays.asList(streamAnswers), false, chatId, cardMessage))
                                );
                                Thread.sleep(speed);
                            }
                        }
                    } else {
                        for (MessageTypeEnum messageTypeEnum : MessageTypeEnum.values()) {
                            if (str.startsWith(messageTypeEnum.getValue())) {
                                streamAnswers[streamIndex] = new StreamAnswer(str.replace(messageTypeEnum.getValue(), ""), messageTypeEnum.getType());
                                sseEmitter.send(SseEmitter.event().comment(buildResponseMessage(Arrays.asList(streamAnswers), false, chatId, cardMessage)));
                                Thread.sleep(speed);
                            }
                        }
                    }
                    streamIndex++;
                }
                sseEmitter.send(SseEmitter.event().comment(
                        SseEmitterUtils.buildResponseMessage(
                                Collections.singletonList(new StreamAnswer("", MessageTypeEnum.END_POINT.getType(), "")), true, chatId, false, JSONObject.parseObject(JSONObject.toJSONString(extraInfo)))));
                sseEmitter.complete();
            } catch (Exception e) {
                sseEmitter.completeWithError(e);
            }
        });
    }


    @SneakyThrows
    public static SseEmitter sendMessage(String message, int speed) {
        SseEmitter sseEmitter = getSseEmitter();
        sendMessage(message, sseEmitter, speed, null);
        return sseEmitter;
    }

    @SneakyThrows
    public static SseEmitter sendMessage(String message) {
        SseEmitter sseEmitter = getSseEmitter();
        sendMessage(message, sseEmitter, defaultSpeed, null);
        return sseEmitter;
    }

    public static SseEmitter getSseEmitter() {
        return new SseEmitter(10 * 60 * 1000L);
    }

    public static SseEmitter getSseEmitter(Long timeOut) {
        return new SseEmitter(timeOut);
    }

    public static List<String> getDataList(String content) {
        content = MessageTypeEnum.TEXT.getValue() + content;
        content = MessageTypeEnum.formatContent(content);
        Matcher matcher = PATTERN.matcher(content);
        List<String> result = new ArrayList<>();
        while (matcher.find()) {
            result.add(matcher.group());
        }
        return result.stream().filter(StringUtils::hasText).map(MessageTypeEnum::formatContentRevert).collect(Collectors.toList());
    }

    public static String buildResponseMessage(List<StreamAnswer> streamAnswers, boolean gptAnswer, String chatId, boolean cardMessage, JSONObject extraInfo) {
        StreamAnswerWrapper streamAnswerWrapper = new StreamAnswerWrapper(gptAnswer, streamAnswers.stream().filter(Objects::nonNull).collect(Collectors.toList()), chatId, cardMessage, extraInfo);
        return JSONObject.toJSONString(streamAnswerWrapper);
    }

    public static String buildResponseMessage(List<StreamAnswer> streamAnswers, boolean gptAnswer, String chatId, boolean cardMessage) {
        StreamAnswerWrapper streamAnswerWrapper = new StreamAnswerWrapper(gptAnswer, streamAnswers.stream().filter(Objects::nonNull).collect(Collectors.toList()), chatId, cardMessage, new JSONObject());
        return JSONObject.toJSONString(streamAnswerWrapper);
    }

    public static String getSimpleAnswer(String answer) {
        if (StringUtils.hasText(answer)) {
            for (MessageTypeEnum messageTypeEnum : MessageTypeEnum.values()) {
                answer = answer.replace(messageTypeEnum.getValue(), "");
            }
        }
        return answer;
    }

    public static List<Content> parseContent(String input) {
        return getDataList(input).stream().map(T -> {
            for (MessageTypeEnum messageTypeEnum : MessageTypeEnum.values()) {
                if (T.startsWith(messageTypeEnum.getValue())) {
                    return new Content(T.replace(messageTypeEnum.getValue(), ""), messageTypeEnum.getType());
                }
            }
            return new Content(T, MessageTypeEnum.TEXT.getType());
        }).collect(Collectors.toList());
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Content {

        private String content;

        private int type;

    }

}



