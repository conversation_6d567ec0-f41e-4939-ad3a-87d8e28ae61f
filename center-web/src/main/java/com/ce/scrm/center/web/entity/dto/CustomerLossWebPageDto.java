package com.ce.scrm.center.web.entity.dto;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import com.ce.scrm.center.service.business.entity.dto.CustomerLossPageBusinessDto;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * Description: 流失客户查询参数
 *
 * @author: liyechao
 * date: 2024/9/2
 */
@EqualsAndHashCode
@Data
public class CustomerLossWebPageDto extends LoginInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户id
     */
    private String custId;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 区域
     */
    private String areaId;
    /**
     * 分公司
     */
    private String subId;
    /**
     * 部门
     */
    private String deptId;
    /**
     * 商务ID
     */
    private String salerId;
    /**
     * 流失日期
     */
    private LocalDate preDate;
    /**
     * 流失原因
     */
    private List<String> lossReasons;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;


    public CustomerLossPageBusinessDto packageBusinessDto(CustomerLossWebPageDto webPageDto) {
        CustomerLossPageBusinessDto businessDto = new CustomerLossPageBusinessDto();
        BeanUtils.copyProperties(webPageDto, businessDto);
        // 组织架构筛选条件
        if (EmpPositionConstant.BUSINESS_AREA.equals(webPageDto.getLoginPosition())) {
            businessDto.setAreaId(webPageDto.getLoginAreaId());
        } else if (PositionUtil.isBusinessMajor(webPageDto.getLoginPosition())) {
            businessDto.setSubId(webPageDto.getLoginSubId());
        } else if (PositionUtil.isBusinessManager(webPageDto.getLoginPosition())) {
            businessDto.setDeptId(webPageDto.getLoginOrgId());
        } else if (PositionUtil.isBusinessSaler(webPageDto.getLoginPosition())) {
            businessDto.setSalerId(webPageDto.getLoginEmployeeId());
        } else if (EmpPositionConstant.BUSINESS_SYS_MANAGER.equals(webPageDto.getLoginPosition())) {
            // 系统管理员默认显示全部数据
        } else {
            // 其他角色默认不显示数据
            return null;
        }
        return businessDto;
    }
}