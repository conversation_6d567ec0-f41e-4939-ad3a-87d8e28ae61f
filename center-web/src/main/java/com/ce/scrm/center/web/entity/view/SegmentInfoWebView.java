package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 专项线索追踪
 */
@Data
public class SegmentInfoWebView implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	private Integer id;

	/**
	 * 分群ID
	 */
	private String segmentId;

	/**
	 * 分群总量
	 */
	private Integer segmentCount;

	/**
	 * 分群实际下发总量
	 */
	private Integer segmentDistributeCount;

	/**
	 * 分群名称
	 */
	private String segmentName;

	/**
	 * 分群描述
	 */
	private String segmentDesc;

	/**
	 * 分群开始时间
	 */
	private Date segmentBeginTime;

	/**
	 * 分群结束时间
	 */
	private Date segmentEndTime;

	/**
	 * 分群状态 0:未下发 1:下发中 2:已下发
	 */
	private Integer segmentStatus;

	/**
	 * 创建人ID
	 */
	private String createId;

	/**
	 * 创建人名称
	 */
	private String createName;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新人ID
	 */
	private String updateId;

	/**
	 * 更新人名称
	 */
	private String updateName;

	/**
	 * 更新时间
	 */
	private Date updateTime;
}