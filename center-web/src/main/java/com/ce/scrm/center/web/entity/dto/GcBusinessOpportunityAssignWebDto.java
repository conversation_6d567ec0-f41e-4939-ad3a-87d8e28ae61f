package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 高呈商机分配参数
 * <AUTHOR>
 * @date 2024/5/16 下午4:45
 * @version 1.0.0
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class GcBusinessOpportunityAssignWebDto extends LoginInfo implements Serializable {

    /**
     * 高呈商机ID（更新必传）
     */
    @NotNull(message = "商机ID不能为空")
    private Long sjId;

    /**
     * 商务ID
     */
    @NotBlank(message = "商务ID不能为空")
    private String salerId;
}