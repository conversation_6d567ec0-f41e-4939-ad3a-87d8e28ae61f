package com.ce.scrm.center.web.entity.dto.cooperation;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @version 1.0
 * @Description: 获取审批详情 实体
 * @Author: lijinpeng
 * @Date: 2024/10/23 15:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetApproveDetailsWebDto extends LoginInfo {

    /**
     * 审批号
     */
    @NotNull
    private Long spNo;

}
