package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description 看板
 * <AUTHOR>
 * @Date 2025-02-27 13:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KanbanDetailSegmentWebDto extends LoginInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * <p>看板详情类型</p>
	 * 昨日：已处理=1、保护=2、拜访客户=3、签单客户=4
	 * 累计：已处理=5、保护=6、拜访客户=7、签单客户=8
	 */
	@NotNull(message = "看板查询类型不能为空")
	@Range(min = 1, max =8, message = "请输入正确的看板查询类型")
	private Integer type;

	/**
	 * 分群id
	 */
	@NotBlank(message = "分群id不能为空")
	private String segmentId;


	/**
	 * 筛选开始时间
	 */
	private String selectionStartDate;

	/**
	 * 筛选结束时间
	 */
	private String selectionEndDate;

}
