package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 高呈商机web返回数据
 * <AUTHOR>
 * @date 2024/5/15 上午11:27
 * @version 1.0.0
 **/
@Data
public class GcBusinessOpportunityWebView implements Serializable {
    /**
     * 高呈商机ID（主键）
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 发起方：0、无，1、中小商务，2、高呈
     */
    private Integer initiator;
    /**
     * 发起方名称
     */
    private String initiatorName = "";

    /**
     * 等级：1、A（高），2、B（中），3、C（低），4、D（其他）
     */
    private Integer level;

    /**
     * 等级名称
     */
    private String levelName = "";

    /**
     * 来源
     */
    private Integer source;

    /**
     * 来源
     */
    private String sourceName = "";

    /**
     * 联系人名称
     */
    private String linkmanName;

    /**
     * 联系人手机号
     */
    private String linkmanPhone;

    /**
     * 联系人邮箱
     */
    private String linkmanEmail;

    /**
     * 联系人部门
     */
    private String linkmanDept;

    /**
     * 联系人性别：0、未知，1、男，2、女
     */
    private Integer linkmanSex;

    /**
     * 联系人座机
     */
    private String linkmanLandline;

    /**
     * 联系人职务
     */
    private String linkmanJob;

    /**
     * 联系人微信
     */
    private String linkmanWechat;

    /**
     * 高呈商机状态
     */
    private Integer state;

    /**
     * 高呈商机状态
     */
    private String stateName = "";

    /**
     * 高呈区域ID
     */
    private String gcAreaId;

    /**
     * 高呈区域名称
     */
    private String gcAreaName;

    /**
     * 高呈分司ID
     */
    private String gcSubId;

    /**
     * 高呈分司名称
     */
    private String gcSubName;

    /**
     * 高呈部门ID
     */
    private String gcDeptId;

    /**
     * 高呈部门名称
     */
    private String gcDeptName;

    /**
     * 高呈商务ID
     */
    private String gcSalerId;

    /**
     * 高呈商务名称
     */
    private String gcSalerName;
    /**
     * 高呈商务手机号
     */
    private String gcSalerPhone = "";
    /**
     * 高呈商务邮箱
     */
    private String gcSalerEmail = "";
    /**
     * 需求类型：GcSjRequirementEnum
     */
    private Integer requirementType;
    /**
     * 需求类型：GcSjRequirementEnum
     */
    private String requirementName = "";
    /**
     * 需求详情
     */
    private String requirementDetail;

    /**
     * 客户预算
     */
    private BigDecimal customerBudget;

    /**
     * 是否面谈（1、是，0、否）
     */
    private Integer interviewState;

    /**
     * 创建时间/提交时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 期望高呈区域ID
     */
    private String expectGcAreaId;

    /**
     * 期望高呈区域名称
     */
    private String expectGcAreaName;

    /**
     * 期望高呈分司ID
     */
    private String expectGcSubId;

    /**
     * 期望高呈分司名称
     */
    private String expectGcSubName;

    /**
     * 期望高呈部门ID
     */
    private String expectGcDeptId;

    /**
     * 期望高呈部门名称
     */
    private String expectGcDeptName;

    /**
     * 期望高呈商务ID
     */
    private String expectGcSalerId;

    /**
     * 期望高呈商务名称
     */
    private String expectGcSalerName;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司ID
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务ID
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;
    /**
     * 商务手机号
     */
    private String salerPhone = "";
    /**
     * 商务邮箱
     */
    private String salerEmail = "";

    /**
     * 当前保护区域id
     */
    private String currentAreaId;
    /**
     * 当前保护区域名称
     */
    private String currentAreaName;
    /**
     * 当前保护分司ID
     */
    private String currentSubId;
    /**
     * 当前保护分司名称
     */
    private String currentSubName;
    /**
     * 当前保护部门ID
     */
    private String currentDeptId;
    /**
     * 当前保护部门名称
     */
    private String currentDeptName;
    /**
     * 当前保护商务ID
     */
    private String currentSalerId;
    /**
     * 当前保护商务名称
     */
    private String currentSalerName;
    /**
     * 当前保护商务手机号
     */
    private String currentSalerPhone;
    /**
     * 当前保护商务邮箱
     */
    private String currentSalerEmail;
    /**
     * 是否展示当前保护信息
     */
    private Boolean showCurrentInfo;
    /**
     * 中小市场部商机code
     */
    private String telSjCode;

    /**
     * 附件ID（多个使用,分隔）
     */
    private String attachmentIds;

    /**
     * 文件列表
     */
    private List<FileData> fileDataList;

    /**
     * 操作日志
     */
    private List<OperateLog> operateLogList;

    /**
     * 高呈市场部退回原因
     */
    private String reasonBack;

    /**
     * 高呈商务退回原因
     */
    private String reasonBusinessBack;
    /**
     * 文件数据
     */
    @Data
    public static class FileData implements Serializable {
        /**
         * 文件ID
         */
        private String fileId;
        /**
         * 文件名称
         */
        private String fileName;
        /**
         * 文件地址
         */
        private String fileUrl;
    }

    /**
     * 操作日志
     */
    @Data
    public static class OperateLog implements Serializable {
        /**
         * 操作人
         */
        private String operatorName;
        /**
         * 操作时间
         */
        private LocalDateTime operateTime;
        /**
         * 操作名称
         */
        private String operateTypeName;
        /**
         * 备注
         */
        private String remark;
    }
}