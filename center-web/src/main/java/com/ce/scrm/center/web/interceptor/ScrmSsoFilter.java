package com.ce.scrm.center.web.interceptor;

import cn.ce.sso.client.check.FilterCheckService;
import cn.ce.sso.client.model.ClientResponse;
import cn.ce.sso.client.session.RedisHttpSession;
import cn.ce.sso.client.session.RedisSessionRequestWrapper;
import cn.ce.sso.client.session.listener.task.RedisSessionExpireHandler;
import cn.ce.sso.client.session.listener.task.RedisSessionExpireHandlerExecutors;
import cn.ce.sso.client.utils.Conf;
import cn.ce.sso.client.utils.StringUtils;
import cn.ce.sso.common.utils.CommonUtils;
import cn.ce.sso.common.utils.CookieUtil;
import cn.ce.sso.common.utils.ParamUtil;
import cn.ce.sso.common.utils.URLEncodeUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/28
 */
@Component("scrmSsoFilter")
public class ScrmSsoFilter implements Filter {
    private static final Logger log = LoggerFactory.getLogger(ScrmSsoFilter.class);
    private FilterCheckService filterCheckService;
    private String ssoServer;

    public ScrmSsoFilter() {
    }
    public void init(FilterConfig filterConfig) throws ServletException {
        Conf.INSTANCE.init(filterConfig);
        this.filterCheckService = Conf.INSTANCE.getFilterHandler();
        this.ssoServer = Conf.INSTANCE.getSsoServer();
    }

    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        RedisSessionRequestWrapper request = new RedisSessionRequestWrapper((HttpServletRequest)servletRequest, (HttpServletResponse)servletResponse);
        HttpServletResponse response = (HttpServletResponse)servletResponse;
        String signUserId = CookieUtil.getValue(request, "CBOST");
        if (StringUtils.hasText(signUserId)){
            // log.info("自定义Filter 实现跨境登录");
            chain.doFilter(request, response);
        }else{
            // log.info("自定义Filter 实现中企登录");
            if ((request.getServletPath() != null && request.getServletPath().startsWith("/entwx/")) || this.filterCheckService.checkExcludedPaths(request, response)) {
                chain.doFilter(request, response);
            } else {
                String destroySession = request.getParameter("destroySession");
                String sessionId = request.getParameter("sessionId");
                if (!CommonUtils.isNullString(destroySession) && !CommonUtils.isNullString(sessionId) && "true".equals(destroySession)) {
                    RedisHttpSession httpSession = RedisHttpSession.createWithExistSession(sessionId, (ServletContext)null);
                    httpSession.setAutoExpire(true);
                    RedisSessionExpireHandlerExecutors.invalidate(new RedisSessionExpireHandler(httpSession));
                } else {
                    String ticket = request.getParameter(Conf.INSTANCE.getTicketParameterName());
                    String logoutRequest = request.getParameter(Conf.INSTANCE.getLogoutParameterName());
                    boolean status;
                    if (!CommonUtils.isNullString(ticket)) {
                        status = this.filterCheckService.checkTicket(ticket, request, response);
                        if (!status) {
                            this.redirectLogin(request, response);
                            return;
                        }

                        if (this.filterCheckService.checkBackUrl(request, response)) {
                            try {
                                String backUrl = request.getParameter("backurl");
                                if (log.isDebugEnabled()) {
                                    log.debug("backurl => {}", backUrl);
                                }

                                response.sendRedirect(backUrl);
                                return;
                            } catch (IOException var12) {
                                var12.printStackTrace();
                            }
                        }
                    } else {
                        if (!CommonUtils.isNullString(logoutRequest)) {
                            this.filterCheckService.logout(request, response);
                            return;
                        }

                        status = this.filterCheckService.checkSession(request, response);
                        if (!status) {
                            this.redirectLogin(request, response);
                            return;
                        }
                    }
                    chain.doFilter(request, response);
                }
            }
        }
    }

    public void destroy() {
    }

    protected void redirectLogin(HttpServletRequest request, HttpServletResponse response) {
        try {
            String urlToRedirectTo = this.getRedirectUrl(request);
            String callback = request.getParameter("callback");
            String requestType = request.getHeader("X-Requested-With");
            ClientResponse result = new ClientResponse();
            result.setStatus(999);
            if (requestType != null && requestType.toLowerCase().contains("xmlhttprequest")) {
                if (callback != null) {
                    result.setLocation(urlToRedirectTo);
                    result.setResult(urlToRedirectTo);
                    response.getWriter().print(callback + "(" + JSONObject.toJSONString(result) + ")");
                    response.getWriter().flush();
                    return;
                }

                response.setContentType("application/json");
                result.setLocation(urlToRedirectTo);
                result.setResult(urlToRedirectTo);
                response.getWriter().print(JSONObject.toJSONString(result));
                response.getWriter().flush();
            } else {
                requestType = request.getHeader("Access-Control-Request-Headers");
                if (requestType != null && requestType.toLowerCase().contains("x-requested-with")) {
                    response.setContentType("application/json");
                    result.setLocation(urlToRedirectTo);
                    result.setResult(urlToRedirectTo);
                    response.getWriter().print(JSONObject.toJSONString(result));
                    response.getWriter().flush();
                } else {
                    response.sendRedirect(urlToRedirectTo);
                }
            }
        } catch (IOException var7) {
            var7.printStackTrace();
        }

    }

    protected String getRedirectUrl(HttpServletRequest request) {
        try {
            return this.ssoServer + getLoginUrlWithService(request);
        } catch (UnsupportedEncodingException var3) {
            var3.printStackTrace();
            return Conf.INSTANCE.getLoginUri();
        }
    }

    protected static String getLoginUrlWithService(HttpServletRequest request) throws UnsupportedEncodingException {
        String clientDomain = Conf.INSTANCE.getClientDomain();
        String serviceUrl = "";
        if (!CommonUtils.isNullString(clientDomain)) {
            serviceUrl = clientDomain + request.getRequestURI();
        } else {
            serviceUrl = request.getRequestURL().toString();
        }

        String queryString = ParamUtil.getQueryParamsWithoutTicket(request, Conf.INSTANCE.getTicketParameterName());
        if (!CommonUtils.isNullString(queryString)) {
            serviceUrl = URLEncodeUtil.encode(serviceUrl + "?" + queryString);
        } else {
            serviceUrl = URLEncodeUtil.encode(serviceUrl);
        }

        return Conf.INSTANCE.getLoginUri() + "?" + "service" + "=" + serviceUrl;
    }
}
