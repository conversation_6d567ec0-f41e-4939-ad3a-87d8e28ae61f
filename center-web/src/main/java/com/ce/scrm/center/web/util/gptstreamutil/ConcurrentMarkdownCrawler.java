package com.ce.scrm.center.web.util.gptstreamutil;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

public class ConcurrentMarkdownCrawler {
    private static final int THREAD_COUNT = 10; // 并发线程数

    public static void main(String[] args) {
        List<String> urls = Arrays.asList(
                "https://example.com",
                "https://www.wikipedia.org",
                "https://news.ycombinator.com"
        );

        List<String> markdownResults = fetchMarkdownFromUrls(urls);

        // 输出 Markdown 结果
        markdownResults.forEach(md -> {
            System.out.println("===== Markdown Content =====");
            System.out.println(md);
            System.out.println("\n-------------------------\n");
        });
    }

    /**
     * 多线程爬取网页，并转换为 Markdown，返回 List<String>
     */
    public static List<String> fetchMarkdownFromUrls(List<String> urls) {
        ExecutorService executorService = Executors.newFixedThreadPool(THREAD_COUNT);
        List<Future<String>> futures = urls.stream()
                .map(url -> executorService.submit(() -> fetchAndConvert(url)))
                .collect(Collectors.toList());
        // 关闭线程池，不再接受新任务
        executorService.shutdown();
        // 收集结果
        return futures.stream()
                .map(future -> {
                    try {
                        return future.get(); // 获取转换后的 Markdown
                    } catch (Exception e) {
                        return null; // 发生异常时返回 null
                    }
                })
                .filter(Objects::nonNull) // 过滤掉 null 结果
                .collect(Collectors.toList());
    }

    /**
     * 爬取网页并转换为 Markdown
     */
    private static String fetchAndConvert(String url) {
        try {
            Document document = Jsoup.connect(url)
                    .timeout(10000) // 设置超时时间 10 秒
                    .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36") // 伪装成浏览器
                    .get();
            Element doc = document.selectFirst("article, div.content, div.main, body"); // 提取主要内容
            if (doc != null) {
                // 2. 移除 <script>, <style>, <iframe>, <noscript> 等不必要的标签
                // 2. 移除所有 <script>、<style>、<head>、<iframe>、<svg>、<noscript> 等
                doc.select("script, style, iframe, svg, noscript, head, link, meta, form, button, input").remove();
                // 3. 只提取 **纯文本内容**
                return doc.text();
            } else {
                System.out.println("未找到正文内容：" + url);
                return "";
            }
        } catch (IOException e) {
            System.err.println("爬取失败：" + url + "，错误：" + e.getMessage());
            return "";
        }
    }
}
