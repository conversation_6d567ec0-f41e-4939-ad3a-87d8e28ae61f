package com.ce.scrm.center.web.dubbo.provider;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dubbo.api.EmployeeInfoDubbo;
import com.ce.scrm.center.dubbo.entity.dto.ChangeRoleDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.EmployeeInfoDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.employee.ChangeRoleBusinessDto;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import cn.hutool.core.bean.BeanUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Description: 员工信息
 * @Author: lijinpeng
 * @Date: 2024/11/14 16:51
 */
@Slf4j
@DubboService(interfaceClass = EmployeeInfoDubbo.class)
public class EmployeeInfoDubboService implements EmployeeInfoDubbo {

    @Resource
    private EmployeeInfoBusiness employeeInfoBusiness;

    /*
     * @Description 根据员工id获取员工信息 不检查state=1
     * <AUTHOR>
     * @date 2024/11/18 10:32
     * @param empId
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.dto.EmployeeInfoDubboDto>
     */
    @Override
    public DubboResult<EmployeeInfoDubboDto> getEmployeeInfoByEmpId(String empId) {
        log.info("根据员工id获取员工信息 empId={}", empId);
        EmployeeInfoBusinessDto employeeInfoBusinessDto = employeeInfoBusiness.getEmployeeInfoByEmpId(empId);
        log.info("根据员工id获取员工信息={}", employeeInfoBusinessDto);
        return DubboResult.success(BeanUtil.copyProperties(employeeInfoBusinessDto, EmployeeInfoDubboDto.class));
    }

    @Override
    public DubboResult<Boolean> changeRoleByEmpIdAndOrgId(ChangeRoleDubboDto changeRoleDubboDto) {
        log.info("切换员工角色，参数changeRoleDubboDto={}", JSON.toJSONString(changeRoleDubboDto));
        ChangeRoleBusinessDto changeRoleBusinessDto =
                BeanCopyUtils.convertToVo(changeRoleDubboDto, ChangeRoleBusinessDto.class);
        Boolean result = employeeInfoBusiness.changeRoleByEmpIdAndOrgId(changeRoleBusinessDto);
        log.info("切换员工角色，结果result={}", result);
        return DubboResult.success(Boolean.TRUE);
    }

    /*
     * @Description 根据员工id集合获取员工集合信息   不检查state=1
     * <AUTHOR>
     * @date 2024/12/3 16:50
     * @param empIdList
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.dto.EmployeeInfoDubboDto>
     */
    @Override
    public DubboResult<Map<String, EmployeeInfoDubboDto>> getEmployeeInfoByEmpIdList(List<String> empIdList) {
        log.info("根据员工id集合获取员工集合信息(state!=1的也会被查出来，离职员工) empIdList={}", empIdList);
        Map<String, EmployeeInfoBusinessDto> employeeInfoMap = employeeInfoBusiness.getEmployeeInfoByEmpIdList(empIdList);
        log.info("根据员工id集合获取员工集合信息(state!=1的也会被查出来，离职员工) employeeInfoMap={}", employeeInfoMap);
        Map<String, EmployeeInfoDubboDto> result = new HashMap<>();
        for (Map.Entry<String, EmployeeInfoBusinessDto> item : employeeInfoMap.entrySet()) {
            String key = item.getKey();
            EmployeeInfoBusinessDto value = item.getValue();
            result.put(key, BeanCopyUtils.convertToVo(value, EmployeeInfoDubboDto.class));
        }
        return DubboResult.success(result);
    }

    /*
     * @Description 根据员工id获取员工信息(state=1)
     * <AUTHOR>
     * @date 2024/12/3 16:47
     * @param empId
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.dto.EmployeeInfoDubboDto>
     */
    @Override
    public DubboResult<EmployeeInfoDubboDto> getEmployeeCheckStateByEmpId(String empId) {
        log.info("根据员工id获取员工信息(state!=1的也会被查出来，离职员工) empId={}", empId);
        EmployeeInfoBusinessDto employeeInfo = employeeInfoBusiness.getEmployeeCheckStateByEmpId(empId);
        log.info("根据员工id获取员工信息(state!=1的也会被查出来，离职员工) employeeInfo={}", employeeInfo);
        return DubboResult.success(BeanUtil.copyProperties(employeeInfo, EmployeeInfoDubboDto.class));
    }

    /*
     * @Description 企业微信登录 存入redis key-userId value-code
     * <AUTHOR>
     * @date 2025/2/7 11:41
     * @param employeeInfoRedisDubboDto
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.Boolean>
     */
    @Override
    public DubboResult<Boolean> saveCodeOfRedis(String userId,String code) {
        log.info("企业微信登录save redis：入参，userId={},code={}", userId, code);
        Boolean result = employeeInfoBusiness.saveCodeOfRedis(userId,code);
        log.info("企业微信登录save redis：出参，result={}", result);
        return DubboResult.success(result);
    }

    /*
     * @Description 企业微信登录 取出redis key-userId value-code
     * <AUTHOR>
     * @date 2025/2/7 14:36
     * @param userId
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.String>
     */
    @Override
    public DubboResult<String> getCodeOfRedisByUserId(String userId) {
        log.info("企业微信登录get redis：入参，userId={}", userId);
        String result = employeeInfoBusiness.getCodeOfRedisByUserId(userId);
        log.info("企业微信登录get redis：出参，result={}", result);
        return DubboResult.success(result);
    }


}
