package com.ce.scrm.center.web.controller.customer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.PotentialCustomerBusiness;
import com.ce.scrm.center.service.business.entity.dto.MyProtectCustomerBusinessDto;
import com.ce.scrm.center.service.business.entity.view.customer.MyProtectCustomerBusinessView;
import com.ce.scrm.center.service.business.entity.view.customer.MyProtectCustomerCapacityedCountBusinessView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.MyProtectCapacityedCountWebDto;
import com.ce.scrm.center.web.entity.dto.MyProtectCustomerWebPageDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.MyProtectCustomerWebView;
import com.ce.scrm.center.web.entity.view.ProtectCustomerCapacityWebView;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 保护关系控制器
 * <AUTHOR>
 * @date 2025-01-03 11:02
 */
@RestController
@Login
@RequestMapping("potential")
@Slf4j
@AllArgsConstructor
@SuppressWarnings("unused")
public class PotentialCustomerController {

	private final PotentialCustomerBusiness potentialCustomerBusiness;


	/**
	 * 销售管理- 客户管理 - 我的保护
	 * <p> 迁移自老项目：PotentialCustomerController#findMyProtectCustomer </p>
	 */
	@PostMapping("findMyProtectCustomer")
	public WebResult<WebPageInfo<MyProtectCustomerWebView>> findMyProtectCustomer(@RequestBody @Valid MyProtectCustomerWebPageDto myProtectCustomerWebPageDto) {
		Transaction t = Cat.newTransaction("Controller", "PotentialCustomerController.findMyProtectCustomer");
		try {
			MyProtectCustomerBusinessDto myProtectCustomerBusinessDto = BeanCopyUtils.convertToVo(myProtectCustomerWebPageDto, MyProtectCustomerBusinessDto.class);
			Page<MyProtectCustomerBusinessView> businessPageView = potentialCustomerBusiness.findMyProtectCustomer(myProtectCustomerBusinessDto);
			WebPageInfo<MyProtectCustomerWebView> pageWeb = WebPageInfo.pageConversion(businessPageView, MyProtectCustomerWebView.class);
			return WebResult.success(pageWeb);
		} catch (Exception e) {
			log.error("查询我的保护列表失败,参数={},异常{}", JSONObject.toJSONString(myProtectCustomerWebPageDto), JSONObject.toJSONString(e.getMessage()));
			t.setStatus(e);
			Cat.logError(e);
			return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
		} finally {
			t.complete();
		}
	}

	/**
	 * 获取当前商务的库容
	 */
	@PostMapping("myProtectCapacity")
	public WebResult<ProtectCustomerCapacityWebView> ProtectCustomer(@RequestBody MyProtectCapacityedCountWebDto req) {
		MyProtectCustomerCapacityedCountBusinessView myProtectCustomerCapacityedCountBusinessView = potentialCustomerBusiness.myProtectCapacity();
		ProtectCustomerCapacityWebView webView = BeanCopyUtils.convertToVo(myProtectCustomerCapacityedCountBusinessView, ProtectCustomerCapacityWebView.class);
		return WebResult.success(webView);
	}
}
