package com.ce.scrm.center.web.entity.view;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 流转日志
 * @author: JiuDD
 * date: 2024/7/11
 */
@Data
public class ClueSegmentWebView implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	private Integer id;

	/**
	 * 分群id
	 */
	private String segmentId;

	/**
	 * 分群名称
	 */
	private String segmentName;

	/**
	 * 下发总量
	 */
	private Integer segmentCount;

	/**
	 * 分群实际下发总量
	 */
	private Integer segmentDistributeCount;

	/**
	 * 下发状态 0:未下发 1:下发中 2:已下发
	 */
	private Integer segmentStatus;

	/**
	 * 下发状态描述 0:未下发 1:下发中 2:已下发
	 */
	private String segmentStatusName;

	/**
	 * 专项线索名称（分司可见）
	 */
	private String segmentDesc;

	/**
	 * 分群开始时间
	 */
	@JsonProperty("segmentStartDate")
	private Date segmentBeginTime;

	/**
	 * 分群结束时间
	 */
	@JsonProperty("segmentEndDate")
	private Date segmentEndTime;

}