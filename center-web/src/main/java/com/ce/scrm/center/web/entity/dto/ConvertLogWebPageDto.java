package com.ce.scrm.center.web.entity.dto;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogPageBusinessDto;
import com.ce.scrm.center.service.enums.ConvertRelationEnum;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

/**
 * Description: 流转日志查询参数
 * @author: JiuDD
 * date: 2024/7/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConvertLogWebPageDto extends LoginInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 释放时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;
    /**
     * 释放时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
    /**
     * 释放原因补写状态：0-未补写，1-已补写
     */
    private String supplementStatus;
    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 每页数量
     */
    private Integer pageSize = 10;


    public ConvertLogPageBusinessDto packageBusinessDto() {
        ConvertLogPageBusinessDto businessDto = new ConvertLogPageBusinessDto();
        businessDto.setCustName(this.custName);
        businessDto.setStartTime(this.startTime);
        if (Objects.nonNull(this.endTime)) {
            businessDto.setEndTime(DateUtil.offset(this.endTime, DateField.DAY_OF_YEAR, 1));
        }
        businessDto.setSupplementStatus(this.supplementStatus);
        businessDto.setPageNum(this.pageNum);
        businessDto.setPageSize(this.pageSize);
        // 组织架构筛选条件
        if (EmpPositionConstant.BUSINESS_AREA.equals(this.getLoginPosition())) {
            businessDto.setAreaOfSalerId(this.getLoginAreaId());
        } else if (PositionUtil.isBusinessMajor(this.getLoginPosition())) {
            businessDto.setSubcompanyOfSalerId(this.getLoginSubId());
        } else if (PositionUtil.isBusinessManager(this.getLoginPosition())) {
            businessDto.setDeptOfSalerId(this.getLoginOrgId());
        } else if (PositionUtil.isBusinessSaler(this.getLoginPosition())) {
            businessDto.setSalerId(this.getLoginEmployeeId());
        }
        businessDto.setClueConvertTypeList(Arrays.asList(ConvertRelationEnum.SALER_RELEASE_MYCUST_TO_CUSTPOOL.getValue(), ConvertRelationEnum.SYSTEM_RELEASE_MYCUST_TO_CUSTPOOL.getValue()));
        return businessDto;
    }
}