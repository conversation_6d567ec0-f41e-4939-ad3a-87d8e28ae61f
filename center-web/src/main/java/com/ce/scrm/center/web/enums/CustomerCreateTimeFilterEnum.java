package com.ce.scrm.center.web.enums;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:45:58
 * @desc 客户引入时间筛选项枚举
 */
public enum CustomerCreateTimeFilterEnum implements EnumBase {

    CCTF0(0, "今天"),
    CCTF1(1, "近1日内"),
    CCTF2(2, "近1周内"),
    CCTF3(3, "近1个月内"),
    CCTF4(4, "近3个月内"),
    CCTF5(5, "近6个月内"),
    CCTF6(6, "近1年内"),
    CCTF7(7, "近2年内")
    ;

    @Getter
    private final Integer label;
    @Getter
    private final String value;

    // Getter 方法
    public String getKey() {
        return String.valueOf(label);
    }

    // 构造函数
    CustomerCreateTimeFilterEnum(Integer label, String value) {
        this.label = label;
        this.value = value;
    }

    public static String getDateRange(Integer label) {
        Date endDate = new Date();
        DateTime startDate = null;
        switch (label) {
            case 0:
                startDate = DateUtil.beginOfDay(endDate);
                break;
            case 1:
                startDate = DateUtil.offsetHour(endDate, -24);
                break;
            case 2:
                startDate = DateUtil.offsetHour(endDate, -24 * 7);
                break;
            case 3:
                startDate = DateUtil.offsetHour(endDate, -24 * 30);
                break;
            case 4:
                startDate = DateUtil.offsetHour(endDate, -24 * 90);
                break;
            case 5:
                startDate = DateUtil.offsetHour(endDate, -24 * 180);
                break;
            case 6:
                startDate = DateUtil.offsetHour(endDate, -24 * 365);
                break;
            case 7:
                startDate = DateUtil.offsetHour(endDate, -24 * 365 * 2);
                break;
            default:
                startDate = DateUtil.beginOfDay(endDate);
        }
        String strStartDate = DateUtil.format(startDate, "yyyy-MM-dd HH:mm:ss");
        String strEndDate = DateUtil.format(endDate, "yyyy-MM-dd HH:mm:ss");
        return strStartDate + "," + strEndDate;
    }
}
