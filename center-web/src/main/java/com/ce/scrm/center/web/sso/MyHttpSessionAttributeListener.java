package com.ce.scrm.center.web.sso;


import javax.servlet.http.HttpSessionAttributeListener;
import javax.servlet.http.HttpSessionBindingEvent;

/**
 * 配置sso
 * <AUTHOR>
 * @date 2024/5/22 下午9:09
 * @version 1.0.0
 **/
public class MyHttpSessionAttributeListener implements HttpSessionAttributeListener {
    @Override
 public void attributeAdded(HttpSessionBindingEvent httpSessionBindingEvent) {
 }

    @Override
 public void attributeRemoved(HttpSessionBindingEvent httpSessionBindingEvent) {
    }

    @Override
 public void attributeReplaced(HttpSessionBindingEvent httpSessionBindingEvent) {
    }
}