package com.ce.scrm.center.web.enums;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:45:58
 * @desc 跨境电商筛选项
 */
public enum CustomerCBECFilterEnum implements EnumBase {

    CCBECF0("0", "全部"),
    CCBECF1("tag10026", "阿里国际站"),
    CCBECF2("tag10029", "中国制造网"),
    ;

    @Getter
    private final String label;
    @Getter
    private final String value;

    // Getter 方法
    public String getKey() {
        return String.valueOf(label);
    }
    // 构造函数
    CustomerCBECFilterEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }
}
