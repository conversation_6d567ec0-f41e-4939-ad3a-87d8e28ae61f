package com.ce.scrm.center.web.controller.entwx.ai;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.AiEventLog;
import com.ce.scrm.center.dao.entity.AiPromptInfo;
import com.ce.scrm.center.service.business.entity.ai.AiEventLogDto;
import com.ce.scrm.center.service.business.entity.view.ai.AiPromptInfoBusinessView;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginType;
import com.ce.scrm.center.web.entity.dto.AiQuerySmartDto;
import com.ce.scrm.center.web.entity.dto.ai.AiCustomsProductWebDto;
import com.ce.scrm.center.web.entity.dto.ai.AiEventLogWebDto;
import com.ce.scrm.center.web.entity.dto.ai.VoiceUpload;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.util.gptstreamutil.AiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.stream.Collectors;

/**
 * ai
 */
@Slf4j
@RestController
@Login(loginType = LoginType.ENT_WECHAT)
@RequestMapping("entwx/analysis/smart")
public class AiSmartEntWxAnalyzeController {

    @Autowired
    private AiService aiService;

    /**
     * 首次分析
     */
    @RequestMapping(value = "/completions/getAnalysisiTips", method = RequestMethod.POST, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<SseEmitter> getAnalysisiTips(@RequestBody AiQuerySmartDto aiQueryDto) {
        return aiService.getAnalysisiTips(aiQueryDto);
    }

    /**
     * 点赞 low 故障上报  复制 等事件
     */
    @RequestMapping(value = "/saveAnalysisStarLog", method = RequestMethod.POST)
    public WebResult<Boolean> saveAiStarLog(@RequestBody AiQuerySmartDto aiQuerySmartDto) {
        return WebResult.success(aiService.saveAiStarLog(aiQuerySmartDto));
    }

    /**
     * 聊天接口
     */
    @RequestMapping(value = "/completions/getChatCompletions", method = RequestMethod.POST, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<SseEmitter> getAiChatComplete(@RequestBody AiQuerySmartDto aiQueryDto) {
        log.info("请求信息getAiChatComplete:{}", JSONObject.toJSONString(aiQueryDto));
        return aiService.getAiChatComplete(aiQueryDto);
    }

    /**
     * 前端主动点击生成 ppt
     */
    @RequestMapping(value = "/generatorPPTFromAnalysisiOneTips", method = RequestMethod.POST)
    public WebResult<String> generatorPPTFromAnalysisiOneTips(@RequestBody AiQuerySmartDto aiQueryDto) {
        return aiService.generatorPPTFromAnalysisiOneTips(aiQueryDto);
    }

    /**
     * 海关数据
     */
    @RequestMapping(value = "/customs/trade/ranking", method = RequestMethod.POST)
    public WebResult<JSONObject> customsTradeRanking(@RequestBody AiCustomsProductWebDto customsProductWebDto) {
        if (customsProductWebDto == null || CollectionUtils.isEmpty(customsProductWebDto.getProductCodes())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        JSONObject result = aiService.customsTradeRanking(customsProductWebDto);
        return WebResult.success(result);
    }


    @RequestMapping(value = "/voiceAnalyze", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public WebResult<?> voiceAnalyze(@RequestBody VoiceUpload voiceAnalyzeWebDto) {
        log.info("当前登录人信息:{}", JSONObject.toJSONString(voiceAnalyzeWebDto));
        JSONObject jsonObject = aiService.uploadFiles(voiceAnalyzeWebDto);
        if (null == jsonObject) {
            return WebResult.error("400", "录音分析异常");
        }
        return WebResult.success(jsonObject);
    }


    @RequestMapping(value = "/customs/retainCust", method = RequestMethod.POST)
    public WebResult<JSONObject> retainCust(@RequestBody AiQuerySmartDto aiQuerySmartDto) {
        Integer result = aiService.retainCust(aiQuerySmartDto.getCustId());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("retainCust", result);
        return WebResult.success(jsonObject);
    }


    @RequestMapping(value = "/getAnalysisPromptIdAndTitleListUsed", method = RequestMethod.POST)
    public WebResult<List<AiPromptInfoBusinessView>> getAnalysisPromptIdAndTitleListUsed(@RequestBody AiQuerySmartDto aiQuerySmartDto) {
        List<AiPromptInfo> result = aiService.getAllPromptsAnyUsed(aiQuerySmartDto);
        List<AiPromptInfoBusinessView> promptInfoPage = result.stream().map(T -> {
            AiPromptInfoBusinessView aiPromptInfoBusinessView = new AiPromptInfoBusinessView();
            BeanUtils.copyProperties(T, aiPromptInfoBusinessView);
            return aiPromptInfoBusinessView;
        }).collect(Collectors.toList());
        return WebResult.success(promptInfoPage);
    }


    @RequestMapping(value = "/getImageGeneratorList", method = RequestMethod.POST)
    public WebResult<WebPageInfo<AiEventLog>> getImageGeneratorList(@RequestBody AiEventLogWebDto aiQueryDto) {
        log.info("当前登录信息:{}", JSONObject.toJSONString(aiQueryDto));
        AiEventLogDto aiEventLogWebDto = BeanUtil.copyProperties(aiQueryDto, AiEventLogDto.class);
        Page<AiEventLog> aiEventLogPage = aiService.getPageEventLog(aiEventLogWebDto);
        WebPageInfo<AiEventLog> result = WebPageInfo.pageConversion(aiEventLogPage, AiEventLog.class);
        return WebResult.success(result);
    }

}
