package com.ce.scrm.center.web.config;

import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;

import java.time.format.DateTimeFormatter;

/**
 * 自定义时间序列化转换器
 * <AUTHOR>
 * @date 2024/5/23 下午2:24
 * @version 1.0.0
 */
public class CustomLocalDateTimeSerializer extends LocalDateTimeSerializer {
    /**
     * 单例
     */
    public static final CustomLocalDateTimeSerializer INSTANCE = new CustomLocalDateTimeSerializer();

    private CustomLocalDateTimeSerializer() {
        super(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}