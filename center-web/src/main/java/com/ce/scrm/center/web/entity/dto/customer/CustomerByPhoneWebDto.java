package com.ce.scrm.center.web.entity.dto.customer;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2024/12/9 10:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerByPhoneWebDto extends LoginInfo implements Serializable {

    /**
     * 客户人手机号
     */
    @NotNull
    private String phone;

}
