package com.ce.scrm.center.web.aop.aspect;

import cn.ce.cesupport.base.vo.ScrmResult;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;
import java.util.Objects;

import static org.apache.http.entity.ContentType.APPLICATION_JSON;

/***
* filter
* <AUTHOR>
* @date 2025/3/17 20:52
*/
@Slf4j
public class FirewallFilter implements Filter {

    private final List<String> blackKeyword = Arrays.asList("{jndi:", "ldap://", "spring.");


    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
        log.info("FirewallFilter loading...");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        if (StringUtils.contains(request.getContentType(), APPLICATION_JSON.getMimeType())) {
            // 包装HttpServletRequest对象，以便在验证请求时，不影响到现有的@RequestBody注解
            FirewallHttpServletRequetWrapper firewallHttpServletRequetWrapper = new FirewallHttpServletRequetWrapper(httpRequest);
            if (!isValidRequest(firewallHttpServletRequetWrapper)) {

                ScrmResult<Object> mktResult = ScrmResult.error("50001","研发小哥哥正在加急处理，请稍后再试DENIED");
                httpResponse.addHeader("content-type", "application/json");
                httpResponse.getOutputStream().write(JSON.toJSONString(mktResult).getBytes(StandardCharsets.UTF_8));
                httpResponse.getOutputStream().flush();
                return;
            }
            httpRequest = firewallHttpServletRequetWrapper;
        }
        chain.doFilter(httpRequest, response);
    }


    private boolean isValidRequest(HttpServletRequest request) {
        String param = null;
        if (StringUtils.contains(request.getContentType(), APPLICATION_JSON.getMimeType())) {
            try {
                ServletInputStream inputStream = request.getInputStream();
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                byte[] byteArray = new byte[1024];
                int len = -1;
                while ((len = inputStream.read(byteArray)) != -1) {
                    byteArrayOutputStream.write(byteArray, 0, len);
                }
                param = new String(byteArrayOutputStream.toByteArray(), StandardCharsets.UTF_8);
            } catch (Exception exception) {
                log.info("isValidRequest fail", exception);
            }
        } else {
            param = WebAspect.getPrettyParam(request).toString();
        }
        if (Objects.nonNull(param)) {
            for (String keyword : blackKeyword) {
                if (param.contains(keyword)) {
                    Cat.logEvent("Firewall", keyword);
                    return false;
                }
            }
        }

        //还要校验请求头
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerValue = request.getHeader(headerNames.nextElement());
            if (StringUtils.isBlank(headerValue)) {
                continue;
            }
            for (String keyword : blackKeyword) {
                if (headerValue.contains(keyword)) {
                    Cat.logEvent("Firewall", keyword);
                    return false;
                }
            }
        }
        return true;
    }
}
