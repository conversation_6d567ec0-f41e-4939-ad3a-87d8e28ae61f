package com.ce.scrm.center.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CustomerCirculationSetting;
import com.ce.scrm.center.dao.entity.CustomerCirculationSpecialSetting;
import com.ce.scrm.center.service.business.CustomerCirculationSettingBusiness;
import com.ce.scrm.center.service.business.entity.dto.*;
import com.ce.scrm.center.service.business.entity.response.BusinessResult;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.controller.base.BaseController;
import com.ce.scrm.center.web.entity.dto.*;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 客户流转配置控制器
 */
@Login
@Slf4j
@RestController
@RequestMapping("customer/circulation/setting")
public class CustomerCirculationSettingController extends BaseController {

    @Resource
    private CustomerCirculationSettingBusiness customerCirculationSettingBusiness;

    /**
     * 查询客户流转配置详情
     */
    @PostMapping("/info")
    public WebResult<CustomerCirculationSetting> info(@Validated @RequestBody LoginInfo loginInfo) {
        CustomerCirculationSettingBusinessDto dto = new CustomerCirculationSettingBusinessDto();
        dto.setSubId(loginInfo.getLoginSubId());
        BusinessResult<CustomerCirculationSetting> businessResult = customerCirculationSettingBusiness.getCustomerCirculationSettingByDto(dto);
        return WebResult.convertBusinessResult(businessResult);
    }

    /**
     * 查询客户流转配置编辑
     */
    @PostMapping("/edit")
    public WebResult<Boolean> edit(@Validated @RequestBody CustomerCirculationSettingEditDto dto) {
        CustomerCirculationSettingEditBusinessDto businessDto = new CustomerCirculationSettingEditBusinessDto();
        BeanUtils.copyProperties(dto, businessDto);
        BusinessResult<Boolean> booleanBusinessResult = customerCirculationSettingBusiness.updateCustomerCirculationSetting(businessDto);
        return WebResult.convertBusinessResult(booleanBusinessResult);
    }

    /**
     * 查询不流转不流失控制表分页
     */
    @PostMapping("/special/pageList")
    public WebResult<WebPageInfo<CustomerCirculationSpecialSetting>> specialPageList(@Validated @RequestBody CustomerCirculationSpecialSettingPageDto dto) {
        CustomerCirculationSpecialSettingPageBusinessDto businessDto = dto.packageBusinessDto(dto);
        BusinessResult<Page<CustomerCirculationSpecialSetting>> businessResult = customerCirculationSettingBusiness.customerCirculationSpecialSettingPageList(businessDto);
        return WebResult.convertBusinessPageResult(businessResult);
    }

    /**
     * 查询不流转不流失控制表添加
     */
    @PostMapping("/special/add")
    public WebResult<Boolean> specialSettingAdd(@Validated @RequestBody CustomerCirculationSpecialSettingAddDto dto) {
        CustomerCirculationSpecialSettingAddBusinessDto businessDto = new CustomerCirculationSpecialSettingAddBusinessDto();
        BeanUtils.copyProperties(dto, businessDto);
        BusinessResult<Boolean> booleanBusinessResult = customerCirculationSettingBusiness.saveCustomerCirculationSpecialSetting(businessDto);
        return WebResult.convertBusinessResult(booleanBusinessResult);
    }

    /**
     * 查询不流转不流失控制表删除
     */
    @PostMapping("/special/delete")
    public WebResult<Boolean> specialSettingDelete(@Validated @RequestBody CustomerCirculationSpecialSettingDeleteDto dto) {
        CustomerCirculationSpecialSettingDeleteBusinessDto businessDto = new CustomerCirculationSpecialSettingDeleteBusinessDto();
        BeanUtils.copyProperties(dto, businessDto);
        BusinessResult<Boolean> booleanBusinessResult = customerCirculationSettingBusiness.updateCustomerCirculationSpecialSetting(businessDto);
        return WebResult.convertBusinessResult(booleanBusinessResult);
    }

    /**
     * 获取本公司成交客户列表
     */
    @PostMapping("/special/getSubDealCustomer")
    public WebResult<?> getSubDealCustomer(@Validated @RequestBody SubDealCustomerWebDto dto) {
        SubDealCustomerBusinessDto businessDto = new SubDealCustomerBusinessDto();
        BeanUtils.copyProperties(dto, businessDto);
        BusinessResult<?> booleanBusinessResult = customerCirculationSettingBusiness.getSubDealCustomer(businessDto);
        return WebResult.convertBusinessResult(booleanBusinessResult);
    }

}