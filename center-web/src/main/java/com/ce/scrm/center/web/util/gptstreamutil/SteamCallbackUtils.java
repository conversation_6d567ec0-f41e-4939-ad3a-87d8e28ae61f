package com.ce.scrm.center.web.util.gptstreamutil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.AiChannelConfig;
import com.ce.scrm.center.service.config.NlpProperties;
import com.ce.scrm.center.util.constant.UtilConstant;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.BufferedSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.Duration;
import java.util.Collections;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;


/**
 * @version 1.0.0
 * @description: okHttp回调继承
 * @author: chengxin
 * @date: 2023-05-06 09:55
 **/
@AllArgsConstructor
@Slf4j
public class SteamCallbackUtils implements Callback {

    private SseEmitter sseEmitter;

    private Consumer<StreamAnswer> consumer;

    private long responseSpeed;

    private ExtralInfo extraInfo;

    private static final int MAX_RETRY_TIMES = 3;

    private int retryStartCount;

    private CountDownLatch latchWrapper = new CountDownLatch(1);

    private AtomicBoolean isSuccess = new AtomicBoolean(false);

    AtomicReference<String> errorMsgRef = new AtomicReference<>("");

    private StringRedisTemplate stringRedisTemplate;

    private NlpProperties nlpProperties;

    private AiChannelConfig aiChannelConfig;

    private static final StreamAnswer POISON_PILL = new StreamAnswer("END", MessageTypeEnum.TEXT.getType(), "END");

    //http失败回调 这里面估计应该只会有time out
    @Override
    public void onFailure(Call call, IOException e) {
        retry(call, MAX_RETRY_TIMES, null);
        //SseEmitterUtils.sendMessage(ResponseConstant.ERROR_RESPONSE_MESSAGE, this.sseEmitter, "0");
    }

    private void error(Call call, Response response, int retryCount) {
        try (ResponseBody responseBody = response.body()) {
            if (responseBody == null) {
                log.warn("响应体为空，尝试重试，第 {} 次,channelConfig:{}", retryCount, JSONObject.toJSONString(aiChannelConfig));
                retry(call, retryCount, null);
                return;
            }
            StringBuilder msg = new StringBuilder();
            BufferedSource source = responseBody.source();
            while (!source.exhausted()) {
                msg.append(source.readUtf8Line());
            }
            retry(call, retryCount, msg.toString());
        } catch (Exception e) {
            log.warn("处理异常时发生错误，终止重试：{}", e.getMessage());
            latchWrapper.countDown();
            errorMsgRef.set("处理异常时发生错误，终止重试：" + e.getMessage());
        }
    }

    private void retry(Call oldCall, int retryCount, String msg) {
        //判断是否是图片channel
        if (null != aiChannelConfig && StringUtils.isNotBlank(msg) && Objects.equals(aiChannelConfig.getId(), 9999L)) {
            ApiErrorCode tips = ApiErrorCode.getTips(msg);
            if (null != tips) {
                consumer.accept(new StreamAnswer(tips.toString(), -1));
                SseEmitterUtils.sendMessage(tips.getTips(), sseEmitter);
                return;
            }
        }
        log.warn("响应体为空，尝试重试，第 {} 次,{},channelConfig:{}", retryCount, msg, JSONObject.toJSONString(aiChannelConfig));
        if (retryCount > MAX_RETRY_TIMES) {
            throw new RuntimeException("已经到最大重试次数:" + retryCount + ",错误信息:" + msg);
        }
        try {
            Call newCall = oldCall.clone(); // OkHttp 的 Call 是可以 clone 的
            newCall.enqueue(new SteamCallbackUtils(sseEmitter, consumer, responseSpeed, extraInfo, retryCount, latchWrapper, isSuccess, errorMsgRef, stringRedisTemplate, nlpProperties, aiChannelConfig));
        } catch (Exception e) {
            log.warn("重试过程中发生错误，终止重试{},channelConfig:{}", retryCount, JSONObject.toJSONString(aiChannelConfig), e);
            throw new RuntimeException("请求gpt异常,重试结束" + ",错误信息:" + msg);
        }
    }

    @Override
    public void onResponse(Call call, Response response) throws IOException {
        BlockingQueue<StreamAnswer> messageQueue = new LinkedBlockingQueue<>();
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder reasoningBuilder = new StringBuilder();
        CountDownLatch latch = new CountDownLatch(1);
        executorService.submit(() -> {
            try {
                while (true) {
                    try {
                        StreamAnswer message = null;
                        Transaction serviceCat = Cat.newTransaction("SERVICE", "SteamCallbackUtils" + UtilConstant.CAT_SEPARATOR + "responseStreamOwn");
                        serviceCat.setStatus(Transaction.SUCCESS);
                        //Thread.sleep(responseSpeed);
                        try {
                            long start = System.currentTimeMillis();
                            message = messageQueue.take();
                            long endTime = System.currentTimeMillis();
                            if ((endTime - start) > Long.parseLong(nlpProperties.getErrorTime())) {
                                log.warn("***************调用间隔卡了***********{}ms", endTime - start);
                                String alarmKey = "CRM:AI:errorCount";
                                long count = stringRedisTemplate.opsForValue().increment("CRM:AI:" + "errorCount", 1L);
                                if (count == 1) {
                                    // 第一次触发，设置 10 分钟过期
                                    stringRedisTemplate.expire(alarmKey, Duration.ofMinutes(Long.parseLong(nlpProperties.getTimeDiff())));
                                }
                                if (count >= Long.parseLong(nlpProperties.getErrorAlertCount())) {
                                    log.error("***************调用间隔卡了:{}***********{}ms", JSONObject.toJSONString(aiChannelConfig), endTime - start);
                                    // 可以选择打一个标记，防止10分钟内重复报警
                                    stringRedisTemplate.delete(alarmKey);
                                }
                            }
                        } catch (Exception e) {
                            serviceCat.setStatus(e);
                            Cat.logError(e);
                        } finally {
                            serviceCat.complete();
                        }
                        if (null == message) {
                            continue;
                        }
                        // 检测到 POISON_PILL，退出循环
                        if (message == POISON_PILL) {
                            this.sseEmitter.send(SseEmitter.event().comment(
                                    SseEmitterUtils.buildResponseMessage(
                                            Collections.singletonList(new StreamAnswer("", MessageTypeEnum.END_POINT.getType(), "")), true, extraInfo.getChatId(), false, JSONObject.parseObject(JSONObject.toJSONString(extraInfo)))));
                            break;
                        }
                        long start = System.currentTimeMillis();
                        this.sseEmitter.send(SseEmitter.event().comment(
                                SseEmitterUtils.buildResponseMessage(
                                        Collections.singletonList(message), true, extraInfo.getChatId(), false, JSONObject.parseObject(JSONObject.toJSONString(extraInfo)))));
                        long endTime = System.currentTimeMillis();
                        if ((endTime - start) > 1000) {
                            log.info("**************和前端的通讯有问题***********{}ms", endTime - start);
                        }
//                    } catch (InterruptedException e) {
//                        Thread.currentThread().interrupt(); // 处理中断异常
//                        break;
                    } catch (Exception e) {
                        break;
                    }
                }
            } finally {
                latch.countDown();
            }
        });
        try (ResponseBody responseBody = response.body()) {
            if (null == responseBody || !response.isSuccessful()) {
                retryStartCount++;
                this.error(call, response, retryStartCount);
                return;
            }
            boolean isThink = false;
            BufferedSource source = responseBody.source();
            while (true) {
                Transaction serviceCat = Cat.newTransaction("SERVICE", "SteamCallbackUtils" + UtilConstant.CAT_SEPARATOR + "onResponseStream");
                serviceCat.setStatus(Transaction.SUCCESS);
                boolean isExhausted;
                try {
                    isExhausted = source.exhausted();
                } catch (Exception e) {
                    isExhausted = true; // 出现异常则认为 exhausted 失败
                    serviceCat.setStatus(e);
                    Cat.logError(e);
                } finally {
                    serviceCat.complete();
                }
                if (isExhausted) {
                    break;
                }
                String sourceStr = source.readUtf8Line();
                if (null == sourceStr || sourceStr.isEmpty()) {
                    continue;
                }
                JSONArray jsonArray = new JSONArray();
                try {
                    jsonArray = JSON.parseObject(sourceStr.replace("data: ", "")).getJSONArray("choices");
                } catch (Exception e) {
                    if (sourceStr.contains("data_inspection_failed")) {
                        log.error("解析异常,响应内容中可能包含敏感信息,info:{}：response:{},reasoning:{}", sourceStr, stringBuilder, reasoningBuilder);
                    }
                    continue;
                }
                // 解析 reasoningContent
                try {
                    String reasoningContent = jsonArray.getJSONObject(0).getJSONObject("delta").getString("reasoning_content");
                    this.addReasoningContent(reasoningBuilder, reasoningContent, messageQueue);
                } catch (Exception e) {
                    log.warn("解析reasoningContent异常");
                }
                // 解析 content
                try {
                    String content = jsonArray.getJSONObject(0).getJSONObject("delta").getString("content");
                    if (null == content) {
                        continue;
                    }
                    //解析截取think
                    if (content.startsWith("<think>")) {
                        isThink = true;
                        continue;
                    }
                    if (content.startsWith("</think>")) {
                        isThink = false;
                        continue;
                    }
                    if (isThink) {
                        this.addReasoningContent(reasoningBuilder, content, messageQueue);
                    } else {
                        this.addNormalContent(stringBuilder, content, messageQueue);
                    }
                } catch (Exception e) {
                    log.warn("解析content异常:{}", e.getMessage());
                }
            }
            consumer.accept(new StreamAnswer(stringBuilder.toString(), MessageTypeEnum.TEXT.getType(), reasoningBuilder.toString(), extraInfo));
            messageQueue.put(POISON_PILL);
            latch.await();
            isSuccess.set(true);
            latchWrapper.countDown();
            this.sseEmitter.complete();
            log.info("请求结束 :{}", JSONObject.toJSONString(extraInfo));
        } catch (Exception e) {
            errorMsgRef.set(e.getMessage());
            log.warn("解析 ai返回结果失败，请求失败", e);
            retryStartCount++;
            this.error(call, response, retryStartCount);
        }
    }

    private void addReasoningContent(StringBuilder reasoningBuilder, String reasoningContent, BlockingQueue<StreamAnswer> messageQueue) throws InterruptedException {
        reasoningContent = null == reasoningContent ? "" : reasoningContent;
        reasoningBuilder.append(reasoningContent);
        messageQueue.put(new StreamAnswer("", MessageTypeEnum.TEXT.getType(), reasoningContent));
//        if (!StringUtils.isEmpty(reasoningContent) && !isOnlyWhitespace(reasoningContent)) {
//            for (String content : reasoningContent.split("")) {
//                messageQueue.put(new StreamAnswer("", MessageTypeEnum.TEXT.getType(), content));
//            }
//        } else {
//            messageQueue.put(new StreamAnswer("", MessageTypeEnum.TEXT.getType(), reasoningContent));
//        }
    }

    private void addNormalContent(StringBuilder stringBuilder, String resultContent, BlockingQueue<StreamAnswer> messageQueue) throws InterruptedException {
        resultContent = null == resultContent ? "" : resultContent;
        stringBuilder.append(resultContent);
        messageQueue.put(new StreamAnswer(resultContent, MessageTypeEnum.TEXT.getType(), ""));
//        if (!StringUtils.isEmpty(resultContent) && !isOnlyWhitespace(resultContent)) {
//            for (String content : resultContent.split("")) {
//                messageQueue.put(new StreamAnswer(content, MessageTypeEnum.TEXT.getType(), ""));
//            }
//        } else {
//            messageQueue.put(new StreamAnswer(resultContent, MessageTypeEnum.TEXT.getType(), ""));
//        }
    }

    public static boolean isOnlyWhitespace(String str) {
        return str != null && str.trim().isEmpty();
    }
}
