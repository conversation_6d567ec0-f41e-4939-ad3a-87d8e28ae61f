package com.ce.scrm.center.web.controller;

import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.EqixiuActivityInfo;
import com.ce.scrm.center.dao.entity.EqixiuDataResult;
import com.ce.scrm.center.dao.entity.EqixiuDataResultAnalyze;
import com.ce.scrm.center.dao.entity.EqixiuDataResultQuery;
import com.ce.scrm.center.dao.mapper.EqixiuDataResultMapper;
import com.ce.scrm.center.dao.service.IEqixiuActivityInfoService;
import com.ce.scrm.center.dao.service.IEqixiuDataResultService;
import com.ce.scrm.center.dubbo.api.ScrmActivityDubbo;
import com.ce.scrm.center.dubbo.entity.dto.EqixiuActivityInfoDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.EqixiuActivityUrlDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.service.config.UniqueIdService;
import com.ce.scrm.center.service.eqixiu.support.entity.*;
import com.ce.scrm.center.service.eqixiu.support.service.SignService;
import com.ce.scrm.center.service.router.RouterContext;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.entity.dto.EqixiuCodeDto;
import com.ce.scrm.center.web.entity.dto.EqixiuActivityInfoDto;
import com.ce.scrm.center.web.entity.dto.EqxiuDataResultDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.extend.dubbo.api.ShortUrlDubboService;
import com.ce.scrm.extend.dubbo.entity.view.ShortUrlView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_EQIXIU_MESSAGE_NOTIFY_TOPIC;

/***
* 已废弃
* <AUTHOR>
* @date 2025/7/23 10:31
*/
@Slf4j
@RestController
@RequestMapping("eqixiu")
public class EqixiuController {

    @Autowired
    private RocketMqOperate rocketMqOperate;

    @Autowired
    private SignService signService;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private ShortUrlDubboService shortUrlDubboService;

    @Autowired
    private EqixiuDataResultMapper eqxiuDataResultMapper;

    @Autowired
    private IEqixiuActivityInfoService eqixiuActivityInfoService;


    @Autowired
    private IEqixiuDataResultService eqixiuDataResultService;


    @Autowired
    private UniqueIdService uniqueIdService;


    @Autowired
    private ScrmActivityDubbo scrmActivityDubbo;

    /**
     * 处理数据推送
     */
    @PostMapping("dataReceiver")
    public Map<String, Object> lotteryMobileLimit(@RequestBody EcmpData ecmpData) throws Exception {
        log.info("接收eqxiu互动推送数据, ecmpData:[{}]", JSONObject.toJSONString(ecmpData));
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("msg", "illegal msg");
        if (ecmpData == null) {
            return result;
        }
        boolean valid = signService.checkSignature(ecmpData.getTimestamp(), ecmpData.getNonce(), ecmpData.getSignature());
        if (!valid) {
            return result;
        }
        List<Map<String, Object>> mapList = signService.decryptMsgList(ecmpData.getMsgEncrypt());
        if (CollectionUtils.isEmpty(mapList)) {
            log.error("消息解密失败:{}", JSONObject.toJSONString(ecmpData));
            return result;
        }
        //发送MQ 消息等待处理
        rocketMqOperate.syncSendBatch(SCRM_EQIXIU_MESSAGE_NOTIFY_TOPIC, mapList.stream().map(JSONObject::toJSONString).collect(Collectors.toList()));
        result.put("code", 200);
        result.put("msg", "ok");
        return result;
    }


    @GetMapping(value = "/authByCode", produces = {"application/json;charset=UTF-8"})
    public Result getUserInfo(@RequestParam(value = "code") String code) {
        try {
            ShortUrlView shortUrlView = shortUrlDubboService.getLongUrlByUniqueCode(code).getData();
            EqixiuCodeInfo eqixiuCodeDto = JSONObject.parseObject(shortUrlView.getLongUrl(), EqixiuCodeInfo.class);
            EqixiuDataResult eqixiuDataResult = eqixiuDataResultService.getById(eqixiuCodeDto.getEqixiuDataResultId());
            if (null == eqixiuDataResult) {
                log.error("解析code失败:{}", code);
                return Result.ofError();
            }
            String customerId = eqixiuDataResult.getCustomerId();
            String customerName = eqixiuDataResult.getCustomerName();
            if (StringUtils.isBlank(customerId) || StringUtils.isBlank(customerName)) {
                log.error("解析code失败:{}", code);
                return Result.ofError();
            }
            ThirdUser user = new ThirdUser();
            user.setUserId(customerId);
            user.setNickName(customerName);
            user.setHeadImg("https://asset.eqxiu.cn/common/default-head-img.png?code=" + code);
            return Result.ofSuccess().setObj(user);
        } catch (Exception e) {
            log.error("解析code失败", e);
            return Result.ofError();
        }
    }

    /************************************************************************************************************************************************/

    @Login
    @PostMapping("/getActivityUrl")
    public WebResult<Map<String, Object>> getActivityUrl(@RequestBody EqixiuCodeDto eqixiuCodeDto) throws Exception {
        EqixiuActivityUrlDubboDto eqixiuActivityUrlDubboDto = new EqixiuActivityUrlDubboDto();
        BeanUtils.copyProperties(eqixiuCodeDto, eqixiuActivityUrlDubboDto);
        DubboResult<Map<String, Object>> dubboResult = scrmActivityDubbo.getShareLinkUrl(eqixiuCodeDto.getLoginEmployeeId(), eqixiuActivityUrlDubboDto);
        if (dubboResult.checkSuccess()) {
            return WebResult.success(dubboResult.getData());
        }
        return WebResult.error("500", "查询失败");
    }


    @Login
    @PostMapping("getEqixiuDataResultList")
    public WebResult<WebPageInfo<EqixiuDataResultAnalyze>> getEqixiuDataResultList(@Validated @RequestBody EqxiuDataResultDto eqxiuDataResultDto) {
        Page<EqixiuDataResultAnalyze> page = new Page<>(eqxiuDataResultDto.getPageNum(), eqxiuDataResultDto.getPageSize());
        EqixiuDataResultQuery eqixiuDataResultQuery = new EqixiuDataResultQuery();
        eqixiuDataResultQuery.setCustomerName(eqxiuDataResultDto.getCustomerName());
        eqixiuDataResultQuery.setAreaId(eqxiuDataResultDto.getAreaId());
        eqixiuDataResultQuery.setSubId(eqxiuDataResultDto.getSubId());
        eqixiuDataResultQuery.setDeptId(eqxiuDataResultDto.getDeptId());
        eqixiuDataResultQuery.setBuId(eqxiuDataResultDto.getBuId());
        //1、商务角色、电商运营顾问，只能查到本人通话记录，就是登录人id =  通话记录表里的  emp_id 字段
        //2、经理角色，只能查看本部门的记录，就是登录人 所在部门id = 通话记录表里的 dept_id 字段
        //3、总监角色，只能查看本分司的记录，就是登录人所在分司id= 通话记录表里的 sub_id 字段 培训师一样
        String position = eqxiuDataResultDto.getLoginPosition();
        log.info("当前登录人信息:{}", JSONObject.toJSONString(eqxiuDataResultDto));
        if (StringUtils.isNotBlank(eqxiuDataResultDto.getSalerId())) {
            eqixiuDataResultQuery.setSalerId(eqxiuDataResultDto.getSalerId());
        } else {
            if (EmpPositionConstant.BUSINESS_SALER.equals(position)
                    || EmpPositionConstant.BUSINESS_SALER_OLD.equals(position)
                    || EmpPositionConstant.GJ_DSGW.equals(position)) {
                eqixiuDataResultQuery.setSalerId(eqxiuDataResultDto.getLoginEmployeeId());
            }
        }
        if (EmpPositionConstant.BUSINESS_MANAGER.equals(position)) {
            eqixiuDataResultQuery.setDeptId(eqxiuDataResultDto.getLoginOrgId());
        }
        if (EmpPositionConstant.BUSINESS_MAJOR.equals(position) || EmpPositionConstant.BUSINESS_PXS.equals(position)) {
            eqixiuDataResultQuery.setSubId(eqxiuDataResultDto.getLoginSubId());
        }
        if (EmpPositionConstant.BUSINESS_AREA.equals(position)) {
            eqixiuDataResultQuery.setAreaId(eqxiuDataResultDto.getLoginAreaId());
        }
        if (EmpPositionConstant.BUSINESS_MAJOR_BU.equals(position)) {
            eqixiuDataResultQuery.setBuId(RouterContext.getCurrentUser().getBuId());
        }
        eqixiuDataResultQuery.setActivityType(eqxiuDataResultDto.getActivityType());
        eqixiuDataResultQuery.setActivityId(eqxiuDataResultDto.getActivityId());
        eqixiuDataResultQuery.setLotteryTimes(eqxiuDataResultDto.getLotteryTimes());
        eqixiuDataResultQuery.setIsWin(eqxiuDataResultDto.getIsWin());
        eqixiuDataResultQuery.setShareTimesSort(eqxiuDataResultDto.getShareTimesSort());
        eqixiuDataResultQuery.setLotteryTimesSort(eqxiuDataResultDto.getLotteryTimesSort());
        eqixiuDataResultQuery.setWinTimesSort(eqxiuDataResultDto.getWinTimesSort());
        eqixiuDataResultQuery.setOpenTimesSort(eqxiuDataResultDto.getOpenTimesSort());
        log.info("查询参数：{}", JSONObject.toJSONString(eqixiuDataResultQuery));
        Page<EqixiuDataResultAnalyze> eqixiuDataResults = eqxiuDataResultMapper.selectActivityList(page, eqixiuDataResultQuery);
        eqixiuDataResults.setRecords(eqixiuDataResults.getRecords().stream().peek(T -> {
            if (StringUtils.isNotBlank(T.getWinInfoConcat())) {
                List<WinInfo> winInfos = JSONObject.parseArray("[" + T.getWinInfoConcat() + "]", WinInfo.class);
                T.setWinInfoConcat(winInfos.stream().map(WinInfo::getPrizeName).collect(Collectors.joining(",")));
            }
        }).collect(Collectors.toList()));
        WebPageInfo<EqixiuDataResultAnalyze> webPageInfo = WebPageInfo.pageConversion(eqixiuDataResults, EqixiuDataResultAnalyze.class);
        return WebResult.success(webPageInfo);
    }

    @Login
    @PostMapping("/getActivityInfo")
    public WebResult getActivityInfo(@RequestBody LoginInfo loginInfo) {
        List<EqixiuActivityInfo> eqixiuActivityInfos = eqixiuActivityInfoService.list();
        if (CollectionUtils.isEmpty(eqixiuActivityInfos)) {
            return WebResult.success(new ArrayList<>());
        } else {
            return WebResult.success(eqixiuActivityInfos.stream().map(T -> {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("label", T.getActivityName());
                        jsonObject.put("value", T.getActivityId());
                        return jsonObject;
                    }).collect(Collectors.toList())
            );
        }
    }

    @Login
    @PostMapping("getActivityStatistics")
    public WebResult<WebPageInfo<EqixiuDataResultAnalyze>> getActivityStatistics(@Validated @RequestBody EqxiuDataResultDto eqxiuDataResultDto) {
        Page<EqixiuDataResultAnalyze> page = new Page<>(eqxiuDataResultDto.getPageNum(), eqxiuDataResultDto.getPageSize());
        EqixiuDataResultQuery eqixiuDataResultQuery = new EqixiuDataResultQuery();
        eqixiuDataResultQuery.setAreaId(eqxiuDataResultDto.getAreaId());
        eqixiuDataResultQuery.setSubId(eqxiuDataResultDto.getSubId());
        eqixiuDataResultQuery.setDeptId(eqxiuDataResultDto.getDeptId());
        eqixiuDataResultQuery.setActivityId(eqxiuDataResultDto.getActivityId());
        eqixiuDataResultQuery.setBuId(eqxiuDataResultDto.getBuId());
        //1、商务角色、电商运营顾问，只能查到本人通话记录，就是登录人id =  通话记录表里的  emp_id 字段
        //2、经理角色，只能查看本部门的记录，就是登录人 所在部门id = 通话记录表里的 dept_id 字段
        //3、总监角色，只能查看本分司的记录，就是登录人所在分司id= 通话记录表里的 sub_id 字段 培训师一样
        String position = eqxiuDataResultDto.getLoginPosition();
        log.info("当前登录人信息:{}", JSONObject.toJSONString(eqxiuDataResultDto));
        if (StringUtils.isNotBlank(eqxiuDataResultDto.getSalerId())) {
            eqixiuDataResultQuery.setSalerId(eqxiuDataResultDto.getSalerId());
        } else {
            if (EmpPositionConstant.BUSINESS_SALER.equals(position)
                    || EmpPositionConstant.BUSINESS_SALER_OLD.equals(position)
                    || EmpPositionConstant.GJ_DSGW.equals(position)) {
                eqixiuDataResultQuery.setSalerId(eqxiuDataResultDto.getLoginEmployeeId());
            }
        }
        if (EmpPositionConstant.BUSINESS_MANAGER.equals(position)) {
            eqixiuDataResultQuery.setDeptId(eqxiuDataResultDto.getLoginOrgId());
        }
        if (EmpPositionConstant.BUSINESS_MAJOR.equals(position) || EmpPositionConstant.BUSINESS_PXS.equals(position)) {
            eqixiuDataResultQuery.setSubId(eqxiuDataResultDto.getLoginSubId());
        }
        if (EmpPositionConstant.BUSINESS_AREA.equals(position)) {
            eqixiuDataResultQuery.setAreaId(eqxiuDataResultDto.getLoginAreaId());
        }
        if (EmpPositionConstant.BUSINESS_MAJOR_BU.equals(position)) {
            eqixiuDataResultQuery.setBuId(RouterContext.getCurrentUser().getBuId());
        }
        eqixiuDataResultQuery.setLevel(eqxiuDataResultDto.getLevel());
        eqixiuDataResultQuery.setActivityType(eqxiuDataResultDto.getActivityType());
        log.info("查询参数:{}", JSONObject.toJSONString(eqixiuDataResultQuery));
        Page<EqixiuDataResultAnalyze> eqixiuDataResults = eqxiuDataResultMapper.getActivityStatistics(page, eqixiuDataResultQuery);
        WebPageInfo<EqixiuDataResultAnalyze> webPageInfo = WebPageInfo.pageConversion(eqixiuDataResults, EqixiuDataResultAnalyze.class);
        return WebResult.success(webPageInfo);
    }


    @Login
    @RequestMapping("saveActivityInfo")
    public void saveActivityInfo(@RequestBody EqixiuActivityInfoDto eqixiuActivityInfoDto) {
        EqixiuActivityInfo eqixiuActivityInfo = new EqixiuActivityInfo();
        String activityId = null;
        //是否是易企秀的活动
        if (eqixiuActivityInfoDto.getActivityUrl().contains("eqxiu")) {
            URL url = null;
            try {
                url = new URL(eqixiuActivityInfoDto.getActivityUrl());
            } catch (MalformedURLException e) {
                throw new RuntimeException(e);
            }
            String path = url.getPath();
            String[] pathParts = path.split("/");
            activityId = pathParts[pathParts.length - 1];
        } else {
            activityId = "OWN" + uniqueIdService.getId();
        }
        eqixiuActivityInfoDto.setActivityId(activityId);
        eqixiuActivityInfoDto.setCreateUserId(eqixiuActivityInfoDto.getLoginEmployeeId());
        eqixiuActivityInfoDto.setCreateUserName(eqixiuActivityInfoDto.getLoginEmployeeName());
        BeanUtils.copyProperties(eqixiuActivityInfoDto, eqixiuActivityInfo);
        eqixiuActivityInfo.setId(uniqueIdService.getId());
        eqixiuActivityInfoService.save(eqixiuActivityInfo);
    }

    @Login
    @RequestMapping("getActivityList")
    public WebResult<?> getActivityList(@RequestBody EqixiuActivityInfoDto eqixiuActivityInfoDto) {
        LambdaQueryWrapper<EqixiuActivityInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(eqixiuActivityInfoDto.getId()), EqixiuActivityInfo::getId, eqixiuActivityInfoDto.getId());
        queryWrapper.eq(StringUtils.isNotBlank(eqixiuActivityInfoDto.getActivityId()), EqixiuActivityInfo::getActivityId, eqixiuActivityInfoDto.getActivityId());
        queryWrapper.eq(null != eqixiuActivityInfoDto.getActivityType(), EqixiuActivityInfo::getActivityType, eqixiuActivityInfoDto.getActivityType());
        queryWrapper.eq(EqixiuActivityInfo::getDeleteFlag, 0);
        queryWrapper.like(StringUtils.isNotBlank(eqixiuActivityInfoDto.getActivityName()), EqixiuActivityInfo::getActivityName, eqixiuActivityInfoDto.getActivityName());
        queryWrapper.orderByDesc(EqixiuActivityInfo::getCreateTime);
        Page<EqixiuActivityInfo> page = new Page<>(eqixiuActivityInfoDto.getPageNum(), eqixiuActivityInfoDto.getPageSize());
        Page<EqixiuActivityInfo> eqixiuActivityInfos = eqixiuActivityInfoService.page(page, queryWrapper);
        WebPageInfo webPageInfo = WebPageInfo.pageConversion(eqixiuActivityInfos, EqixiuActivityInfo.class);
        webPageInfo.setList(eqixiuActivityInfos.getRecords().stream().peek(T -> {
            //1 未开始  // 2 进行中   // 3 已结束
            if (T.getStartTime() != null && new Date().before(T.getStartTime())) {
                // 当前时间早于开始时间
                T.setActivityStatus(1);
            } else if (T.getEndTime() != null && new Date().after(T.getEndTime())) {
                // 当前时间晚于结束时间
                T.setActivityStatus(3);
            } else {
                // 其他情况认为是进行中
                T.setActivityStatus(2);
            }
        }).collect(Collectors.toList()));
        return WebResult.success(webPageInfo);
    }


    @Login
    @RequestMapping("updateById")
    public WebResult updateActivity(@RequestBody EqixiuActivityInfoDto eqixiuActivityInfoDto) {
        if (StringUtils.isBlank(eqixiuActivityInfoDto.getId())) {
            return WebResult.error("500", "活动不存在");
        }
        EqixiuActivityInfo eqixiuActivityInfoUpdate = eqixiuActivityInfoService.getById(eqixiuActivityInfoDto.getId());
        if (null == eqixiuActivityInfoUpdate) {
            return WebResult.error("500", "活动不存在");
        }
        BeanUtils.copyProperties(eqixiuActivityInfoDto, eqixiuActivityInfoUpdate);
        eqixiuActivityInfoUpdate.setUpdateUserId(eqixiuActivityInfoDto.getLoginEmployeeId());
        eqixiuActivityInfoUpdate.setUpdateUserName(eqixiuActivityInfoDto.getLoginEmployeeName());
        eqixiuActivityInfoUpdate.setUpdateTime(new Date());
        String activityId = null;
        //是否是易企秀的活动
        if (eqixiuActivityInfoDto.getActivityUrl().contains("eqxiu")) {
            URL url = null;
            try {
                url = new URL(eqixiuActivityInfoDto.getActivityUrl());
            } catch (MalformedURLException e) {
                throw new RuntimeException(e);
            }
            String path = url.getPath();
            String[] pathParts = path.split("/");
            activityId = pathParts[pathParts.length - 1];
        } else {
            activityId = "OWN" + uniqueIdService.getId();
        }
        eqixiuActivityInfoUpdate.setActivityId(activityId);
        eqixiuActivityInfoService.updateById(eqixiuActivityInfoUpdate);
        return WebResult.success(eqixiuActivityInfoUpdate);
    }


    @Login
    @RequestMapping("getShareActivityList")
    public WebResult<Map<String, Object>> getShareActivityList(@RequestBody EqixiuActivityInfoDto eqixiuActivityInfoDto) {
        EqixiuActivityInfoDubboDto eqixiuActivityInfoDubboDto = new EqixiuActivityInfoDubboDto();
        BeanUtils.copyProperties(eqixiuActivityInfoDto, eqixiuActivityInfoDubboDto);
        DubboResult<Map<String, Object>> dubboResult = scrmActivityDubbo.getActivityList(eqixiuActivityInfoDto.getLoginEmployeeId(), eqixiuActivityInfoDubboDto);
        if (dubboResult.checkSuccess()) {
            return WebResult.success(dubboResult.getData());
        }
        return WebResult.error("500", "查询失败");
    }

}
