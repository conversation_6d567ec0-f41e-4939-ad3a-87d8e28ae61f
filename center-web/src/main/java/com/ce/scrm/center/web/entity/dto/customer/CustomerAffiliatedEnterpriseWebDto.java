package com.ce.scrm.center.web.entity.dto.customer;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 关联企业信息查询入参信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerAffiliatedEnterpriseWebDto extends LoginInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 是否获取详情
	 */
	@NotNull(message = "是否获取关联企业详情的标记不能为空")
	@Range(min = 0, max = 1, message = "请输入正确的查询类型")
	private Integer type;

	/**
	 * 企业唯一编码
	 */
	@NotBlank(message = "pid不能为空")
	private String pid;

	/**
	 * 人员的姓名
	 */
	@NotBlank(message = "人员姓名不能为空")
	private String name;


	/**
	 * 保护关系表 cm_cust_protect.cust_name
	 */
	@NotBlank(message = "客户名称不能为空")
	private String customerName;

}
