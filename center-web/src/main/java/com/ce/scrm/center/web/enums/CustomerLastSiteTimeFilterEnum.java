package com.ce.scrm.center.web.enums;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:30:48
 * @desc 上门时间筛选项枚举
 */
public enum CustomerLastSiteTimeFilterEnum implements EnumBase {

    CLSTF0(0, "全部"),
    CLSTF1(1, "排除今天"),
    CLSTF2(2, "排除近1周"),
    CLSTF3(3, "排除近2周"),
    CLSTF4(4, "排除近1个月"),
    CLSTF5(5, "排除近3个月"),
    CLSTF6(6, "排除近6个月"),
    CLSTF8(7, "从未上门")
    ;

    @Getter
    private final Integer label;
    @Getter
    private final String value;

    // Getter 方法
    public String getKey() {
        return String.valueOf(label);
    }

    // 构造函数
    CustomerLastSiteTimeFilterEnum(Integer label, String value) {
        this.label = label;
        this.value = value;
    }

    public static String getDateRange(Integer label) {
        Date startDate = new Date();
        DateTime endDate = null;
        switch (label) {
            case 0:
                //如果全部的话，查询条件中不包括该字段即可。
                return null;
            case 1:
                endDate = DateUtil.beginOfDay(startDate);
                break;
            case 2:
                endDate = DateUtil.offsetHour(startDate, -24 * 7);
                break;
            case 3:
                endDate = DateUtil.offsetHour(startDate, -24 * 7 * 2);
                break;
            case 4:
                endDate = DateUtil.offsetHour(startDate, -24 * 30);
                break;
            case 5:
                endDate = DateUtil.offsetHour(startDate, -24 * 30 * 3);
                break;
            case 6:
                endDate = DateUtil.offsetHour(startDate, -24 * 30 * 6);
                break;
            case 7:
                //!@,表示must_not + exists
                return "!@";
            default:
                endDate = DateUtil.date();
        }
        String strEndDate = DateUtil.format(endDate, "yyyy-MM-dd HH:mm:ss");
        return "r:<" + strEndDate;
    }
}
