package com.ce.scrm.center.web.entity.view.favorite;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/***
* 收藏夹列表
* <AUTHOR>
* @date 2024/7/18 16:48
*/
@Data
public class FavoritesWebView implements Serializable {
	private static final long serialVersionUID = 1L;
	private String id;
	private String uid;
	private String custName;
	private String labelFrom;
	private String custAddressProvince;
	private String custAddressCity;
	private String custAddressRegion;
	private String industryZhongqi;
	private String bussOppType;
	private Date createTime;
	private Date createUser;
	private String updateTime;
	private String updateUser;
	private Integer isDeleted;
	private String lablFromStr;
	private String industryZhongqiStr;
	private String bussOppTypeStr;
	private Date assignTime;
	private String assignTimeStr;
	private String exceedTimeStr;
	private String linkManName;
	private String linkManNameId;
	private String mobilePhone;
	private String tid;
	private String linkManLablFrom;
	private String startTime;
	private String endTime;
	private String marketId;
	private String isAssignedToMajorOrNot;
	private Integer isHiden;
	private String scene;
	private String deptName;
	private String salerName;
	private String custTypeStr = "线索";
	private String visitTypeStr;
	private String visitStatusStr;
	private String entId;
	private String clueMission;
	private String salerClueFrom;
	private String salerClueFromSub;
	private String salerClueFromStr;
	private String clueVisitStage;
	private String visitTimeStr;
	private String nextVisitTimeStr;
	private Integer custType;
	private String custIdNumber;
	private String firstIndustry;
	private String firstIndustryStr;
	private String secondIndustry;
	private String secondIndustryStr;
	private Integer isReaded;
	private String address;
	private String custId;
	private String subId;
	private String subName;
	private String hasLinkManOrNot;
	private String hasRecuitOrNot;
	private String hasWebSiteOrNot;
	private String isForeinTradeOrNot;
	private String isGroupOrNot;
	private Integer jingzhunLinkMan;
	private String tel;
	private String kaFlag;
	private String kaBackFlag;
	private String callStatus;

	/**
	 * 不能拨打原因
	 */
	private String callMessage;

	/**
	 * 当 callStatus =1 的时候有值
	 * 第三方线路编码
	 */
	private String lineCode;

	/**
	 * 第三方线路名称
	 */
	private String lineName;
	private Integer lineType;
	private String callSubId;
}
