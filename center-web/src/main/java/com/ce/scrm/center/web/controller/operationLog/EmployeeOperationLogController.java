package com.ce.scrm.center.web.controller.operationLog;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.web.entity.dto.EmployeeOperationLogPageWebDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.extend.dubbo.api.EmployeeOperationLogDubbo;
import com.ce.scrm.extend.dubbo.entity.dto.EmployeeOperationLogPageDubboDto;
import com.ce.scrm.extend.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.EmployeeOperationLogDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/21
 */

@Slf4j
@RestController
@RequestMapping("/operationLog")
public class EmployeeOperationLogController {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    EmployeeOperationLogDubbo employeeOperationLogDubbo;

    @Autowired
    private EmployeeThirdService employeeThirdService;

    @PostMapping(value = "/pageList")
    public WebResult<WebPageInfo<EmployeeOperationLogDubboView>> pageList(@RequestBody EmployeeOperationLogPageWebDto employeeOperationLogPageWebDto) {
        if (employeeOperationLogPageWebDto == null) {
            log.warn("员工操作履历分页列表，请求参数不能为空");
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        EmployeeOperationLogPageDubboDto employeeOperationLogPageDubboDto = new EmployeeOperationLogPageDubboDto();
        BeanUtils.copyProperties(employeeOperationLogPageWebDto, employeeOperationLogPageDubboDto);
        DubboResult<DubboPageInfo<EmployeeOperationLogDubboView>> dubboPageInfoDubboResult = employeeOperationLogDubbo.pageList(employeeOperationLogPageDubboDto);
        WebPageInfo<EmployeeOperationLogDubboView> pageInfo = new WebPageInfo<>();
        pageInfo.setTotal(0);
        pageInfo.setPageNum(employeeOperationLogPageWebDto.getPageNum());
        pageInfo.setPageSize(employeeOperationLogPageWebDto.getPageSize());
        pageInfo.setPages(0);
        pageInfo.setList(Lists.newArrayList());
        if (!dubboPageInfoDubboResult.checkSuccess() || dubboPageInfoDubboResult.getData()==null || CollectionUtil.isEmpty(dubboPageInfoDubboResult.getData().getList())) {
            log.warn("员工操作履历分页列表，获取分页列表失败，参数为:{},返回数据为:{}", JSON.toJSONString(employeeOperationLogPageWebDto), JSON.toJSONString(dubboPageInfoDubboResult));
            return WebResult.success(pageInfo);
        }
        DubboPageInfo<EmployeeOperationLogDubboView> data = dubboPageInfoDubboResult.getData();
        List<EmployeeOperationLogDubboView> list = data.getList();
        List<String> empIdList = list.stream().map(EmployeeOperationLogDubboView::getEmpId).distinct().collect(Collectors.toList());
        Map<String, EmployeeInfoThirdDto> employeeDataMap = employeeThirdService.getEmployeeDataMap(empIdList);
        for (EmployeeOperationLogDubboView employeeOperationLogDubboView : list){
            employeeOperationLogDubboView.setEmpName(employeeDataMap.get(employeeOperationLogDubboView.getEmpId()).getName());
            EmployeeOperationMethodEnum methodEnum = EmployeeOperationMethodEnum.getByUrl(employeeOperationLogDubboView.getUrl());
            if (methodEnum!=null){
                employeeOperationLogDubboView.setDescription(methodEnum.getMsg());
            }else{
                employeeOperationLogDubboView.setDescription("--");
            }
        }
        pageInfo.setTotal(data.getTotal());
        pageInfo.setPageNum(data.getPageNum());
        pageInfo.setPageSize(data.getPageSize());
        pageInfo.setPages(data.getPages());
        pageInfo.setList(list);
        return WebResult.success(pageInfo);
    }
}
