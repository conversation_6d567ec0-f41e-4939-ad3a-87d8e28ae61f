package com.ce.scrm.center.web.entity.dto.abm;

import com.ce.scrm.center.dao.entity.ActivityClueInfo;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 客户营销列表请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerMarketingActivityEffectRecoveryWebDto extends LoginInfo implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 线索列表
	 */
	@NotEmpty(message = "营销效果回收，线索列表不能为空")
	List<ActivityClueInfo> clueInfoList;

}
