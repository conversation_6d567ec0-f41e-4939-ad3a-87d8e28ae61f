package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云客服通话记录web返回值
 * <AUTHOR>
 * @date 2024/5/6 下午4:59
 * @version 1.0.0
 **/
@Data
public class CloudCustomerServiceCallLogWebView implements Serializable {
    /**
     * 通话ID
     */
    private String callId;

    /**
     * 呼叫开始时间
     */
    private LocalDateTime startTime;

    /**
     * 通话时长 挂机时间-接通时间
     */
    private Integer callDuration;

    /**
     * 客户号码，来电是主叫号码，去电是被叫号码
     */
    private String customerNumber;

    /**
     * 通话录音文件地址
     */
    private String recordUrl;
}