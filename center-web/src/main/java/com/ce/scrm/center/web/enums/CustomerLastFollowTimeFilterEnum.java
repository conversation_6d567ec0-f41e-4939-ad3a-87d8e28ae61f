package com.ce.scrm.center.web.enums;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:30:48
 * @desc 最近跟进时间筛选项枚举
 */
public enum CustomerLastFollowTimeFilterEnum implements EnumBase {

    CLFTF0(0, "排除今天"),
    CLFTF1(1, "排除近1周"),
    CLFTF2(2, "排除近2周"),
    CLFTF3(3, "排除近1个月"),
    CLFTF4(4, "排除近3个月"),
    CLFTF5(5, "排除近6个月"),
    CLFTF7(6, "从未拜访")
    ;

    @Getter
    private final Integer label;
    @Getter
    private final String value;

    // Getter 方法
    public String getKey() {
        return String.valueOf(label);
    }

    // 构造函数
    CustomerLastFollowTimeFilterEnum(Integer label, String value) {
        this.label = label;
        this.value = value;
    }

    public static String getDateRange(Integer label) {
        Date startDate = new Date();
        DateTime endDate = null;
        switch (label) {
            case 0:
                endDate = DateUtil.beginOfDay(startDate);
                break;
            case 1:
                endDate = DateUtil.offsetHour(startDate, -24 * 7);
                break;
            case 2:
                endDate = DateUtil.offsetHour(startDate, -24 * 7 * 2);
                break;
            case 3:
                endDate = DateUtil.offsetHour(startDate, -24 * 30);
                break;
            case 4:
                endDate = DateUtil.offsetHour(startDate, -24 * 30 * 3);
                break;
            case 5:
                endDate = DateUtil.offsetHour(startDate, -24 * 30 * 6);
                break;
            case 6:
                //!@,表示must_not + exists
                return "!@";
            default:
                startDate = DateUtil.beginOfDay(endDate);
        }
        String strEndDate = DateUtil.format(endDate, "yyyy-MM-dd HH:mm:ss");
        return "r:<" + strEndDate;
    }
}
