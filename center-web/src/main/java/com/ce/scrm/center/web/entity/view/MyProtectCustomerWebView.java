package com.ce.scrm.center.web.entity.view;

import cn.ce.cesupport.sma.vo.SjIntentInfoVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MyProtectCustomerWebView implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 客户id
	 */
	private String custId;

	/**
	 *
	 */
	private String id;

	/**
	 * 客户类型（2保护客户；3网站客户；4非网站客户）
	 */
	private Integer custType;

	/**
	 * 合作意向状态
	 */
	private Integer intentionalityType;

	/**
	 * 商务ID
	 */
	private String salerId;

	/**
	 * 商务名称
	 */
	private String salerName;

	/**
	 * 商务所属部门名称
	 */
	private String deptName;

	/**
	 * 区域名称
	 */
	private String areaName;

	/**
	 * 分司名称
	 */
	private String subName;

	/**
	 * 部门ID
	 */
	private String bussdeptId;

	/**
	 * 事业部id
	 */
	private String buId;

	/**
	 * 分公司ID
	 */
	private String subcompanyId;

	/**
	 * 区域ID
	 */
	private String areaId;

	/**
	 * 市场ID
	 */
	private String markId;

	/**
	 * 阶段性保护时间

	 */
	private Date protectTime;

	/**
	 * 阶段性超期时间

	 */
	private Date exceedTime;

	/**
	 * 共享标签

	 */
	private Integer isVisitState;

	/**
	 * 创建人ID
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新人ID
	 */
	private String updateBy;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 3分配，6自签
	 */
	private Integer custSource;

	/**
	 * 数据状态

	 */
	private Integer dataState;

	/**
	 * 最后一次拜访时间

	 */
	private Date lastVisitTime;

	/**
	 * 联系超期时间

	 */
	private Date visitExceedTime;

	/**
	 * 绝对保护期
	 */
	private Date absoluteProtectTime;

	/**
	 * 客户名称
	 */
	private String custName;

	/**
	 * 商机code
	 */
	private String busioppoCode;

	/**
	 * 是否重点客户
	 */
	private Integer isStress;

	/**
	 * 加重点的时间
	 */
	private Date addStressTime;

	/**
	 * 最后一次付款日期
	 */
	private Date lastPayTime;

	/**
	 * 是否已转高呈
	 */
	private Integer isTurnGc;

	/**
	 * 二级来源
	 */
	private Integer custSourceSub;

	/**
	 * 老客数据来源标识（1 1年以上2年以内；2 2年以上）
	 */
	private Integer cooperate;

	/**
	 * 是否计算库容（1不计算，0计算）
	 */
	private Integer occupy;

	/**
	 * 是否已打卡（1是，0否）
	 */
	private Integer isClock;

	/**
	 * 打卡所在省
	 */
	private String clockProvince;

	/**
	 * 打卡所在市
	 */
	private String clockCity;

	/**
	 * 打卡所在区
	 */
	private String clockRegion;

	/**
	 * 注册地所在省
	 */
	private String regProvince;

	/**
	 * 注册地所在市
	 */
	private String regCity;

	/**
	 * 注册地所在区
	 */
	private String regRegion;

	/**
	 * 统一社会信用代码查询
	 */
	private String uncid;

	/**
	 * 状态值（1、保护；2、总监；3、经理；4、客户池）
	 */
	private Integer status;

	/**
	 * 分配时间
	 */
	private Date assignTime;

	/**
	 * 客户进待分配表的来源
	 */
	private Integer assignCustSource;

	/**
	 * 待分配超期时间
	 */
	private Date assignDate;

	/**
	 * 客户来源（1=释放、2=流失）
	 */
	private String source;

	/**
	 * 释放原因
	 */
	private String reason;

	/**
	 * 原的部门ID
	 */
	private String originalDeptId;

	/**
	 * 表记录写入时间
	 */
	private Date dbInsertTime;

	/**
	 * 表记录更新时间
	 */
	private Date dbUpdateTime;

	/**
	 * 搜客宝 1:规模工业,2:规上服务业,3:规上建筑业,4:规上批发零售业,5:规上住宿餐饮业,6:规上房地产开发与经营业
	 */
	private String tagFlag7;

	/**
	 * 搜客宝 行业 1:律师,2:学校,3:医院
	 */
	private String tagFlag8;

	/**
	 * 科技型企业
	 */
	private String tagTechcompany;

	/**
	 * 自定义标签
	 */
	private String customTags;

	/**
	 * pid
	 */
	private String entId;

	/**
	 * 首次签单时间
	 */
	private Date firstSignTime;

	/**
	 * 上一次商务id
	 */
	private String lastSalerId;

	/**
	 * 上一次商务所属部门id
	 */
	private String lastBussdeptId;

	/**
	 * 上一次商务所属事业部id
	 */
	private String lastBuId;

	/**
	 * 上一次商务所属分司id
	 */
	private String lastSubcompanyId;

	/**
	 * 上一次商务所属区域id
	 */
	private String lastAreaId;

	/**
	 * 是否是曾用名 true是 false否
	 */
	private Boolean historyNameFlag;

	/**
	 * 最近一次打卡日期
	 */
	private Date lastSiteClockDate;

	/**
	 * 是否是ka用户
	 */
	private Integer kaFlag;

	/**
	 * 是否实名认证 0未认证 1 已认证
	 */
	private String realNameAuthentication;

	/**
	 * 销售阶段 cm_cust_protect.salestage存储的字典id
	 */
	private String salesStage;

	/**
	 * 销售阶段
	 */
	private String salesStageStr;

	/**
	 * 阶段性保护剩余时间
	 */
	private String surplusTime;

	/**
	 * 是否红色字体显示
	 */
	private boolean surplusTimeRedOrNot;

	/** 客户来源 */
	private String custSourceStr;

	/** 客户阶段 */
	private String custTypeStr;

	/**
	 * 高呈商机ID
	 */
	private Long gcSjId;

	/**
	 * 商机客户信息
	 */
	private SjIntentInfoVo sjIntentInfoVo;

	/**
	 * 是否商机客户
	 */
	private Integer isSj;

	/**
	 * 是否商机客户
	 */
	private Integer sjSource;

	/**
	 * 标签
	 */
	private String label;

	/** 商机code */
	private String busiOppoCode;

	/**
	 * 绑定客户标识 0未绑定 1绑定
	 */
	private Integer bindFlag;

	/**
	 * 商机是否确认标记 0 正常（已经确认） 1 未确认  2 流转过一次-未确认
	 */
	private Integer businessOpportunityConfirmationFlag;

	/**
	 * 回执倒计时
	 */
	private String remainReceiptTime;
}