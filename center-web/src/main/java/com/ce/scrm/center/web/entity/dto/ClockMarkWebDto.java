package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class ClockMarkWebDto extends LoginInfo implements Serializable {

    /**
     * 打卡记录id
     */
    private String id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 标记是否有效 1-无效 2-有效
     */
    private Integer markClockValidFlag;

    /**
     * 回访内容
     */
    private String content;

    /**
     * 参数校验
     *
     * <AUTHOR>
     * @date 2024/10/11 11:35
     */
    public void validate() {
        if (this.id == null) {
            throw new IllegalArgumentException("打卡记录id不能为空");
        }
        if (this.markClockValidFlag == null || (this.markClockValidFlag != 1 && this.markClockValidFlag != 2)) {
            throw new IllegalArgumentException("标记是否有效必须为1或2");
        }
    }

}
