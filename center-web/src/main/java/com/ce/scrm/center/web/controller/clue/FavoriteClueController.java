package com.ce.scrm.center.web.controller.clue;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.FavoriteClueBusiness;
import com.ce.scrm.center.service.business.entity.dto.FavoritesCapacityBusinessResDto;
import com.ce.scrm.center.service.business.entity.dto.FavoritesPageBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.FavoritesPageBusinessResDto;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.controller.base.BaseController;
import com.ce.scrm.center.web.entity.dto.favorite.FavoritesCapacityWebDto;
import com.ce.scrm.center.web.entity.dto.favorite.FavoritesPageWebDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.favorite.FavoritesCapacityWebView;
import com.ce.scrm.center.web.entity.view.favorite.FavoritesWebView;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 客户管理 线索 收藏夹
 *
 * @author: Xukang
 * @date 2025-01-16 10:10
 */
@Login
@RestController
@RequestMapping("/clue/favorite")
@RequiredArgsConstructor
public class FavoriteClueController extends BaseController {

	private final FavoriteClueBusiness favoriteClueBusiness;

	/**
	 * 客户管理->收藏夹->列表
	 * <p>原接口：/security/cluecustomer/getClueCustomerPageUnderSaler</p>
	 *
	 * @param favoritesPageWebDto 收藏夹列表筛选
	 * @return {@link com.ce.scrm.center.web.entity.response.WebResult}
	 **/
	@PostMapping(value = "page")
	public WebResult<WebPageInfo<FavoritesWebView>> page(@RequestBody @Valid FavoritesPageWebDto favoritesPageWebDto) {
		FavoritesPageBusinessDto copy = BeanCopyUtils.convertToVo(favoritesPageWebDto, FavoritesPageBusinessDto.class);
		Page<FavoritesPageBusinessResDto> page = favoriteClueBusiness.page(copy);
		return WebResult.success(WebPageInfo.pageConversion(page, FavoritesWebView.class));
	}

	/**
	 * 收藏夹商务库容信息
	 * @param favoritesCapacityWebDto extend LoginInfo
	 * @return {@link com.ce.scrm.center.web.entity.response.WebResult}
	 */
	@PostMapping(value = "capacity")
	public WebResult<FavoritesCapacityWebView> capacity(@RequestBody @Valid FavoritesCapacityWebDto favoritesCapacityWebDto) {
		FavoritesCapacityBusinessResDto capacity = favoriteClueBusiness.capacity();
		return WebResult.success(BeanCopyUtils.convertToVo(capacity, FavoritesCapacityWebView.class));
	}


}
