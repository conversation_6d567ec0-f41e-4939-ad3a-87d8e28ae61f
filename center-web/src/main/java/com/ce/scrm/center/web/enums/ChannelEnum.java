package com.ce.scrm.center.web.enums;

public enum ChannelEnum {

    //中供
    ZHONG_GUO_KU_KE("中供", 1),
    //百度搜索
    BAIDU_SEARCH("百度搜索", 2),
    //360搜索—360信息流
    XIANG_GOU_SEARCH_360_INFORMATION_FLOW("360搜索—360信息流", 3),
    //微信小程序
    WECHAT_MINI_PROGRAM("微信小程序", 4),
    //admin5
    ADMIN_FIVE("admin5", 5),
    //中供-会员
    ZHONG_GUO_KU_KE_MEMBER("中供-会员", 6),
    //头条信息流
    TOUTIAO_INFORMATION_FLOW("头条信息流", 7),
    //300会员中心-支付成功页
    ZHONG_QI_KU_KE_CENTER_PAYMENT_SUCCESS_PAGE("300会员中心-支付成功页", 8),
    //300会员中心-注册成功页
    ZHONG_QI_KU_KE_CENTER_REGISTRATION_SUCCESS_PAGE("300会员中心-注册成功页", 9),
    //微信海报1
    WECHAT_POSTER_ONE("微信海报1", 10),
    //微信海报2
    WECHAT_POSTER_TWO("微信海报2", 11),
    //搜狗搜索
    SOU_GOU_SEARCH("搜狗搜索", 12),
    //网易推广-有道信息流
    NETEASE_PROMOTION_YOU_DAO_INFORMATION_FLOW("网易推广-有道信息流", 13),
    //网易推广-有道智选
    NETEASE_PROMOTION_YOU_DAO_ZHI_XUAN("网易推广-有道智选", 14),
    //网易推广-网易新闻
    NETEASE_PROMOTION_NETEASE_NEWS("网易推广-网易新闻", 15),
    //搜狐汇算
    SHOU_GOU_HUI_SUAN("搜狐汇算", 16),
    //中企酷客
    ZHONG_QI_KU_KE("中企酷客", 17),
    //线下二维码-宣传册
    OFFLINE_QR_CODE_ADVERTISING_BOOKLET("线下二维码-宣传册", 18),
    //谷歌搜索
    GOOGLE_SEARCH("谷歌搜索", 19),
    //新媒体-微博
    NEW_MEDIA_WEIBO("新媒体-微博", 20),

    //新媒体-微信订阅号
    NEW_MEDIA_WECHAT_SUBSCRIPTION_NUMBER("新媒体-微信订阅号", 21),
    //新媒体-微信服务号
    NEW_MEDIA_WECHAT_SERVICE_NUMBER("新媒体-微信服务号", 22),
    //新媒体-QQ群
    NEW_MEDIA_QQ_GROUP("新媒体-QQ群", 23),
    //新媒体-头条号
    NEW_MEDIA_TOUTIAO_NUMBER("新媒体-头条号", 24),
    //新媒体-微信个人号
    NEW_MEDIA_WECHAT_PERSONAL_NUMBER("新媒体-微信个人号", 25),
    //新媒体-H5秀
    NEW_MEDIA_H5_SHOW("新媒体-H5秀", 26),
    //新媒体-盟主直播
    NEW_MEDIA_MENG_ZHU_LIVE("新媒体-盟主直播", 27),
    //新媒体-百度小程序
    NEW_MEDIA_BAIDU_MINI_PROGRAM("新媒体-百度小程序", 28),

    //自然搜索
    ZHONG_QI_SEARCH("自然搜索", 30),
    //直接输入
    ZHONG_QI_INPUT("直接输入", 31),
    //腾讯搜索
    TENCENT_SEARCH("腾讯搜索", 32),
    //神马搜索
    SHEN_MA_SEARCH("神马搜索", 33),
    //BD
    BD("BD", 34),
    //朋友介绍
    FRIENDS_INTRODUCTION("朋友介绍", 35),
    //百度信息流
    BAIDU_INFORMATION_FLOW("百度信息流", 36),
    //马可波罗
    MARCO_POPOLO("马可波罗", 37),
    //自有EDM
    OWN_EDM("自有EDM", 38),
    //QQ空间
    QQ_ZONE("QQ空间", 39),
    //马可波罗EDM
    MARCO_POPOLO_EDM("马可波罗EDM", 40),
    //马可波罗会员中心
    MARCO_POPOLO_MEMBER_CENTER("马可波罗会员中心", 41),
    //马可波罗搜索
    MARCO_POPOLO_SEARCH("马可波罗搜索", 42),
    //E展网
    EXHIBITION_NET("E展网", 43),
    //福步
    FU_BU("福步", 44),
    //第一展会网
    FIRST_EXHIBITION_NET("第一展会网", 45),

    //官网banner
    WEBSITE_BANNER("官网banner", 46),
    //分公司banner
    BRANCH_OFFICE_BANNER("分公司banner", 47),
    //数据库营销
    DATABASE_MARKETING("数据库营销", 48),
    //知乎信息流
    ZHIHU_INFORMATION_FLOW("知乎信息流", 49),
    //寻客-百度寻客
    ZHU_KE_BAIDU_SEARCH("寻客-百度寻客", 50),
    //寻客-360寻客
    ZHU_KE_360_SEARCH("寻客-360寻客", 51),
    //付费搜索
    PAY_SEARCH("付费搜索", 52),
    //官网焦点区
    WEBSITE_FOCUS_ZONE("官网焦点区", 53),
    //官网热门活动banner
    WEBSITE_HOT_ACTIVITY_BANNER("官网热门活动banner", 54),
    //新网
    NEW_WEB("新网", 55),
    //EDM移动端
    EDM_MOBILE_END("EDM移动端", 56),
    //采集案例页
    COLLECTION_CASES_PAGE("采集案例页", 57),
    //营销站首页
    MARKETING_STATION_HOME_PAGE("营销站首页", 58),
    //SEO
    SEO("SEO", 59),
    //中企产品推广
    ZHONG_QI_PRODUCT_PROMOTION("中企产品推广", 60),
    //邮箱登录页
    EMAIL_LOGIN_PAGE("邮箱登录页", 61),
    //趣头条
    QUE_TOU_TOU("趣头条", 62),
    //短信-老客短信
    SMS_OLD_CUSTOMER_SMS("短信-老客短信", 63),
    //线下扫码
    OFFLINE_QR_CODE("线下扫码", 64),
    //互动吧
    INTERACTION_BA("互动吧", 65),
    //分公司广告位
    BRANCH_OFFICE_ADVERTISING_SPACE("分公司广告位", 66),
    //百度商学院
    BAIDU_SCIENCE_BUSINESS("百度商学院", 67),
    //AI营销
    AI_MARKETING("AI营销", 68),
    //中企智能名片
    ZHONG_QI_SMART_CARD("中企智能名片", 69),
    //商学院小程序
    BUSINESS_SCIENCE_MINI_PROGRAM("商学院小程序", 70),
    //商务
    BUSINESS("商务", 71),
    //厦门电话外呼
    XIANG_MEN_PHONE_OUTSIDE_CALL("厦门电话外呼", 72),
    //售前顾问系统
    SALES_PRE_SALES_SYSTEM("售前顾问系统", 73),
    //首页掉落
    HOME_DROP("首页掉落", 74),
    //网站检测工具
    WEBSITE_DETECTION_TOOL("网站检测工具", 75),
    //53客服
    ZHONG_QI_53_CUSTOMER_SERVICE("53客服", 76),
    //汇推
    HUITUI("汇推", 77),
    //404页面
    FOUR_ZERO_FOUR_PAGE("404页面", 78),
    //京东试用订单
    JD_TRY_ORDER("京东试用订单", 79),
    //黄页88
    YELLOW_PAGE_88("黄页88", 80),
    //黄页88-2
    YELLOW_PAGE_88_2("黄页88-2", 81),
    //慧聪网-慧聪网2
    HUI_CONG_NET_HUI_CONG_NET_2("慧聪网-慧聪网2", 82),
    //直播课
    LIVE_COURSE("直播课", 83),
    //天久
    TIAN_JU("天久", 84),
    //百度开屏
    BAIDU_OPEN_SCREEN("百度开屏", 85),
    //社群-社群1
    COMMUNITY_COMMUNITY_1("社群-社群1", 86),

    //社群-社群2
    COMMUNITY_COMMUNITY_2("社群-社群2", 87),
    //社群-社群3
    COMMUNITY_COMMUNITY_3("社群-社群3", 88),
    //社群-社群4
    COMMUNITY_COMMUNITY_4("社群-社群4", 89),
    //社群-社群5
    COMMUNITY_COMMUNITY_5("社群-社群5", 90),
    //社群-三方社群
    COMMUNITY_THIRD_PARTY_COMMUNITY("社群-三方社群", 91),
    //产品搜索
    PRODUCT_SEARCH("产品搜索", 92),
    //搜了网
    SEARCH_SOU_LE_WANG("搜了网", 93),
    //首页弹窗
    HOME_DROP_WINDOW("首页弹窗", 94),
    //中企高呈
    ZHONG_QI_GONG_CHENG("中企高呈", 95),
    //商务个人平台
    BUSINESS_PERSONAL_PLATFORM("商务个人平台", 96),
    //跨境默认渠道
    CROSS_BORDER_DEFAULT_CHANNEL("跨境默认渠道", 97),
    //跨境-头条
    CROSS_BORDER_TOUTIAO("跨境-头条", 98),

    ;

    /**
     * lable
     */
    private String lable;

    /**
     * value
     */
    private Integer value;

    private ChannelEnum(String lableStr, Integer value) {
        this.lable = lableStr;
        this.value = value;
    }

    public String getLable() {
        return lable;
    }

    public void setLable(String lable) {
        this.lable = lable;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public static String ByValue(Integer in) {
        for (ChannelEnum statusEnum : ChannelEnum.values()) {
            if (statusEnum.getValue().equals(in)) {
                return statusEnum.getLable();
            }
        }
        return null;
    }

}
