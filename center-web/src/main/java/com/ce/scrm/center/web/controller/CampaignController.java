package com.ce.scrm.center.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.eqixiu.support.entity.CampaignReportDto;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_CAMPAIGN_REPORT_TOPIC;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/21
 */

@Slf4j
@RestController
@RequestMapping("/campaign")
public class CampaignController {

    @Autowired
    private RocketMqOperate rocketMqOperate;
    @PostMapping("/report")
    public WebResult<Boolean> report(@RequestBody CampaignReportDto campaignReportDto){
        if (campaignReportDto==null || StringUtils.isBlank(campaignReportDto.getCode())){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
        try {
            rocketMqOperate.syncSend(SCRM_CAMPAIGN_REPORT_TOPIC, JSONObject.toJSONString(campaignReportDto));
        }catch (Exception e){
            log.error("活动上报发送MQ失败,消息体={}",JSONObject.toJSONString(campaignReportDto));
        }
        return WebResult.success(true);
    }
}
