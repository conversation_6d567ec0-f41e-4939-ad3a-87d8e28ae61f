package com.ce.scrm.center.web.entity.view.follow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/8/8
 */
@Data
public class VisitLogWebView implements Serializable {
    private String id;
    /**
     * 客户id
     */
    private String customerId;
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 联系人名称
     */
    private String linkManName;

    /**
     * 联系人手机号
     */
    private String linkManMobile;

    /**
     * 跟进人ID
     */
    private String salerId;

    /**
     * 商务部门id
     */
    private String bussdeptId;

    /**
     * 商务分司id
     */
    private String subcompanyId;

    /**
     * 商务区域id
     */
    private String areaId;

    /**
     * 沟通类型
     */
    private Integer visitType;

    /**
     * 沟通时间
     */
    private Date visitTime;

    /**
     * 跟进状态
     */
    private Integer visitStatus;

    /**
     * 打卡记录id
     */
    private String followRecordId;

    /**
     * 录入方式
     */
    private String recordWay;

    /**
     * 联系内容
     */
    private String content;

    /**
     * 联系人职位
     */
    private String linkManPosition;

    /**
     * 下次跟进时间
     */
    private Date nextVisitTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 跟进人名称
     */
    private String salerName;

    /**
     * 意愿度
     */
    private String intentionLevel;

    /**
     * 跟进方式描述
     */
    private String visitTypeDesc;

    /**
     * 跟进人来源
     * 销售
     * SDR
     */
    private String source;
}
