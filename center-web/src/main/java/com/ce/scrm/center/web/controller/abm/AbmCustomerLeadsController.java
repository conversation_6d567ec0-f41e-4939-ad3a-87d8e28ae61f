package com.ce.scrm.center.web.controller.abm;

import cn.ce.cecloud.business.entity.BusinessOpportunity;
import cn.ce.cecloud.business.service.BusinessAppService;
import cn.ce.cecloud.business.service.BusinessOpportunityService;
import cn.ce.cecloud.clue.entity.Clue;
import cn.ce.cecloud.clue.service.ClueService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.ClueInfo;
import com.ce.scrm.center.service.business.abm.CustomerLeadsBusiness;
import com.ce.scrm.center.service.business.abm.PotentialCustomerRulesConfigBusiness;
import com.ce.scrm.center.service.business.entity.dto.CallStatusBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerLeadsImportOrDistributeDto;
import com.ce.scrm.center.service.business.entity.view.CustomerLeadsDubboView;
import com.ce.scrm.center.service.business.entity.view.PotentialCustomerMarketingRulesBusinessView;
import com.ce.scrm.center.service.third.entity.dto.CallStatusThirdDto;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerLeadsAddThirdDto;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.entity.view.CustomerLeadsThirdView;
import com.ce.scrm.center.service.third.invoke.CallCenterThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.abm.CustomerLeadsListWebDto;
import com.ce.scrm.center.web.entity.dto.abm.CustomerLeadsPageWebDto;
import com.ce.scrm.center.web.entity.dto.abm.CustomerLeadsWebDto;
import com.ce.scrm.center.web.entity.dto.abm.CustomerMarketingActivityPageWebDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.CloudCustomerServiceCallLogWebView;
import com.ce.scrm.center.web.entity.view.abm.CustomerConfigPageWebView;
import com.ce.scrm.center.web.entity.view.abm.CustomerLeadsWebView;
import com.ce.scrm.center.web.enums.IntentionPorductEnum;
import com.ce.scrm.center.web.enums.SexEnum;
import com.ce.scrm.extend.dubbo.api.CloudCustomerServiceCallLogDubbo;
import com.ce.scrm.extend.dubbo.entity.dto.CloudCustomerServiceCallLogBatchPageDubboDto;
import com.ce.scrm.extend.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.CloudCustomerServiceCallLogDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 跨境ABM营销leads控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-07-23
 */
@Slf4j
@Login
@RestController
@RequestMapping("/abm/customer/leads")
public class AbmCustomerLeadsController {
    @Resource
    private CustomerThirdService customerThirdService;
    @Resource
    private PotentialCustomerRulesConfigBusiness potentialCustomerRulesConfigBusiness;

    @DubboReference
    private ClueService ClueService;

    @DubboReference
    private BusinessAppService businessAppService;

    @Resource
    private CustomerLeadsBusiness customerLeadsBusiness;
    @Resource
    private BusinessOpportunityService businessOpportunityService;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private CloudCustomerServiceCallLogDubbo cloudCustomerServiceCallLogDubbo;
    @Resource
    private CallCenterThirdService callCenterThirdService;




    /**
     * 获取leads列表
     */

    @Deprecated
    @PostMapping("/list")
    public WebResult<List<CustomerLeadsWebView>> getLeads(@Valid @RequestBody CustomerLeadsListWebDto customerLeadsListWebDto) {
        List<CustomerLeadsThirdView> leadsByCustomerId = customerThirdService.getLeadsByCustomerId(customerLeadsListWebDto.getCustomerId());
        if (CollectionUtils.isEmpty(leadsByCustomerId)) {
            return WebResult.success();
        }
        return WebResult.success(BeanCopyUtils.convertToVoList(leadsByCustomerId, CustomerLeadsWebView.class));
    }

    /**
     * 手工录入
     */
    @PostMapping("/add")
    public WebResult<String> addLeads(@Valid @RequestBody CustomerLeadsWebDto customerLeadsWebDto) {
        if (ObjectUtils.isEmpty(customerLeadsWebDto.getDataFromSource())) {
            log.error("数据来源不能为空");
            return WebResult.error(WebCodeMessageEnum.CUSTOMER_LEADS_DATASOURCE_EMPTY);
        }
        if (ObjectUtils.isEmpty(customerLeadsWebDto.getChannel())) {
            log.error("渠道不能为空");
            return WebResult.error(WebCodeMessageEnum.CUSTOMER_LEADS_CHANNEL_EMPTY);
        }
        if (ObjectUtils.isEmpty(customerLeadsWebDto.getActivity())) {
            log.error("活动不能为空");
            return WebResult.error(WebCodeMessageEnum.CUSTOMER_LEADS_ACTIVITY_EMPTY);
        }
        if (ObjectUtils.isEmpty(customerLeadsWebDto.getClientType())) {
            log.error("端口不能为空");
            return WebResult.error(WebCodeMessageEnum.CUSTOMER_LEADS_CLIENT_TYPE_EMPTY);
        }
        CustomerLeadsAddThirdDto customerLeadsAddThirdDto = BeanCopyUtils.convertToVo(customerLeadsWebDto, CustomerLeadsAddThirdDto.class);

        customerLeadsAddThirdDto.setGender(SexEnum.parseByValue(customerLeadsWebDto.getGender()));
        List<String> products = new ArrayList<>();
        if (customerLeadsWebDto.getIntentionProduct() != null) {
            products = customerLeadsWebDto.getIntentionProduct().stream().map(IntentionPorductEnum::parseByValue).collect(Collectors.toList());
        }
        customerLeadsAddThirdDto.setIntentionProduct(JSONObject.toJSONString(products));
        customerLeadsAddThirdDto.setCreatedId(customerLeadsWebDto.getLoginEmployeeId());
        customerLeadsAddThirdDto.setCreateTime(new Date());
        customerLeadsAddThirdDto.setCreatedId(customerLeadsWebDto.getLoginEmployeeId());

        List<CustomerLeadsAddThirdDto> dtoList = new ArrayList<>();
        dtoList.add(customerLeadsAddThirdDto);
        //添加客户
        customerLeadsBusiness.createCustomerWhetherNameExist(dtoList,customerLeadsAddThirdDto.getCreatedId(), false);
        Optional<CustomerDataThirdView> customerDataByCustomerName = customerThirdService.getCustomerDataByCustomerName(customerLeadsAddThirdDto.getCustomerName());
        if (!customerDataByCustomerName.isPresent()) {
            log.error("手工录入添加客户后，查询客户失败，customerName={}", customerLeadsAddThirdDto.getCustomerName());
            return WebResult.error(WebCodeMessageEnum.CUSTOMER_CREATE_FAIL);
        }

        CustomerLeadsThirdView addCustomerLeads = customerThirdService.addCustomerLeads(customerLeadsAddThirdDto);
        if (!Objects.isNull(addCustomerLeads) && Objects.isNull(addCustomerLeads.getId())) {
            return WebResult.error(WebCodeMessageEnum.CUSTOMER_LEADS_CREATE_FAIL);
        }
        if (StringUtils.isNotBlank(addCustomerLeads.getSalerName()) && addCustomerLeads.getIsProtect().equals("1")) {
            //该客户是XXX商务的保护客户，该线索已记录，并企微通知商务
            String msg = "该客户是[" + addCustomerLeads.getSalerName() + "]商务的保护客户，该线索已记录，并企微通知["+addCustomerLeads.getSalerName()+"]商务" ;
            return WebResult.success(msg,WebCodeMessageEnum.CUSTOMER_LEADS_PROTECT_SUCCESS.getMsg());
        }
        return WebResult.success(WebCodeMessageEnum.CUSTOMER_LEADS_PROTECT_SUCCESS.getMsg(),addCustomerLeads.getId().toString());
    }


    /**
     * 获取leads分页
     */
    @PostMapping("/page")
    public WebResult<WebPageInfo<CustomerLeadsWebView>> getLeadsPage(@Valid @RequestBody CustomerLeadsPageWebDto customerLeadsPageWebDto) {
        Page<CustomerLeadsDubboView> leadsPageByCustomerId = customerThirdService.getLeadsPageByCustomerId(
                customerLeadsPageWebDto.getCustomerId(), customerLeadsPageWebDto.getPageNum(),
                customerLeadsPageWebDto.getPageSize());

        if (CollectionUtils.isEmpty(leadsPageByCustomerId.getRecords())) {
            WebPageInfo<CustomerLeadsWebView> rsWebPage = new WebPageInfo<>();
            rsWebPage.setTotal(0L);
            rsWebPage.setList(Collections.emptyList());
            return WebResult.success(rsWebPage);
        }

        List<CustomerLeadsWebView> customerLeadsWebViews = new ArrayList<>();
        try {
            List<CustomerLeadsDubboView> records = leadsPageByCustomerId.getRecords();
            List<String> phones = records.stream().map(CustomerLeadsDubboView::getMobile).collect(Collectors.toList());
            CallStatusThirdDto callStatus = callCenterThirdService.getCallStatus(CallStatusBusinessDto.builder().empId(customerLeadsPageWebDto.getLoginEmployeeId()).subId(customerLeadsPageWebDto.getLoginSubId()).phones(phones).build());

            for (CustomerLeadsDubboView record : records) {
                CustomerLeadsWebView customerLeadsWebView = BeanCopyUtils.convertToVo(record, CustomerLeadsWebView.class);
                if (StringUtils.isNotBlank(record.getOthers())) {

                    String decodedOthers = decodeUnicodeAndHtmlEntities(record.getOthers());
                    Map<String, String> othersMap = JSONObject.parseObject(decodedOthers, Map.class);
                    List<CustomerLeadsWebView.other> ortherList = new ArrayList<>();
                    for (Map.Entry<String, String> entry : othersMap.entrySet()) {
                        CustomerLeadsWebView.other other = new CustomerLeadsWebView.other();
                        other.setKey(entry.getKey());
                        other.setValue(entry.getValue());
                        ortherList.add(other);
                    }
                    customerLeadsWebView.setOthers(ortherList);
                    //获取拨打状态值
                    if (callStatus != null && callStatus.getCallcenterStatusViewMap() != null) {
                        CallStatusThirdDto.CallCenterStatusThirdDto callCenterStatusThirdDto = callStatus.getCallcenterStatusViewMap().get(record.getMobile());
                        if (callCenterStatusThirdDto != null) {
                            customerLeadsWebView.setCallStatus(callCenterStatusThirdDto.getCallStatus());
                        }
                    }
                }
                customerLeadsWebViews.add(customerLeadsWebView);
            }
        } catch (Exception e) {
            log.error("拼装客户详情线索返回结果异常", e);
        }
        WebPageInfo<CustomerLeadsWebView> rsWebPage = new WebPageInfo<>();
        rsWebPage.setTotal(leadsPageByCustomerId.getTotal());
        rsWebPage.setPageNum(leadsPageByCustomerId.getCurrent());
        rsWebPage.setPageSize(leadsPageByCustomerId.getSize());
        rsWebPage.setPages(leadsPageByCustomerId.getPages());
        rsWebPage.setList(customerLeadsWebViews);

        return WebResult.success(rsWebPage);
    }


    /**
     * 批量获取客户通话记录
     * @param phones 客户手机号列表
     * @return 通话记录映射，key为手机号，value为通话记录URL列表
     */
    @PostMapping("/callRecords")
    public WebResult<Map<String, Object>> getCallRecordsByPhones(@RequestBody List<String> phones) {
        if (CollectionUtils.isEmpty(phones)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        try {
            // 批量查询通话记录
            CloudCustomerServiceCallLogBatchPageDubboDto pageDubboDto = new CloudCustomerServiceCallLogBatchPageDubboDto();
            pageDubboDto.setQueryStartTime(LocalDateTime.now().minusYears(1));
            pageDubboDto.setQueryEndTime(LocalDateTime.now());
            pageDubboDto.setCustomerNumbers(phones);
            pageDubboDto.setSourceType(2);
            DubboResult<DubboPageInfo<CloudCustomerServiceCallLogDubboView>> dubboPageInfoDubboResult = cloudCustomerServiceCallLogDubbo.batchPageList(pageDubboDto);
            if (!dubboPageInfoDubboResult.checkSuccess()) {
                log.warn("查询客服通话记录失败，参数为:{},返回数据为:{}", JSONObject.toJSONString(pageDubboDto), JSONObject.toJSONString(dubboPageInfoDubboResult));
                return WebResult.error(WebCodeMessageEnum.CUSTOMER_LEADS_CALL_RECORDS_FAIL);
            }
            DubboPageInfo<CloudCustomerServiceCallLogDubboView> dubboPageInfo = dubboPageInfoDubboResult.getData();

            // 组装通话记录列表
            List<CloudCustomerServiceCallLogWebView> recordList = new ArrayList<>();
            if (dubboPageInfo != null && !CollectionUtils.isEmpty(dubboPageInfo.getList())) {
                recordList = dubboPageInfo.getList().stream()
                        .map(record -> {
                            CloudCustomerServiceCallLogWebView webView = new CloudCustomerServiceCallLogWebView();
                            webView.setCallId(record.getCallId());
                            webView.setStartTime(record.getStartTime());
                            webView.setCallDuration(record.getCallDuration());
                            webView.setCustomerNumber(record.getCustomerNumber());
                            webView.setRecordUrl(record.getRecordUrl());
                            return webView;
                        })
                        .collect(Collectors.toList());
            }

            // 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", recordList);

            return WebResult.success(result);
        } catch (Exception e) {
            log.error("调用Dubbo服务客户通话记录异常,参数={}", JSONObject.toJSONString(phones));
            return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION, "调用客户通话记录异常");
        }
    }

    /**
     * 解码Unicode转义字符
     * @param str 包含Unicode转义字符的字符串
     * @return 解码后的字符串
     */
    private String decodeUnicodeAndHtmlEntities(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        // 首先解码Unicode转义字符
        String result = decodeUnicodeEscapes(str);
        // 然后解码常见的HTML实体
        result = result.replace("&mdash;", "—")
                .replace("&ndash;", "–")
                .replace("&amp;", "&")
                .replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&quot;", "\"")
                .replace("&apos;", "'");
        return result;
    }
    private String decodeUnicodeEscapes(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        // 使用StringBuilder提高性能
        StringBuilder sb = new StringBuilder();
        int i = 0;
        while (i < str.length()) {
            char c = str.charAt(i);
            if (c == '\\' && i + 1 < str.length() && str.charAt(i + 1) == 'u' && i + 5 < str.length()) {
                try {
                    // 提取Unicode码点
                    String hexCode = str.substring(i + 2, i + 6);
                    char unicodeChar = (char) Integer.parseInt(hexCode, 16);
                    sb.append(unicodeChar);
                    i += 6;
                } catch (NumberFormatException e) {
                    // 如果解析失败，按原样添加字符
                    sb.append(c);
                    i++;
                }
            } else {
                sb.append(c);
                i++;
            }
        }
        return sb.toString();
    }

    /**
     * 后台配置表
     */
    @PostMapping("/configInfo")
    public WebResult<WebPageInfo<CustomerConfigPageWebView>> backendConfigInfo(@Valid @RequestBody CustomerMarketingActivityPageWebDto customerMarketingActivityPageWebDto) {
        Page<PotentialCustomerMarketingRulesBusinessView> page = potentialCustomerRulesConfigBusiness.page(customerMarketingActivityPageWebDto.getPageSize(), customerMarketingActivityPageWebDto.getPageNum());
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return WebResult.success(new WebPageInfo<>());
        }
        WebPageInfo<CustomerConfigPageWebView> webPage = WebPageInfo.pageConversion(page, CustomerConfigPageWebView.class);
        return WebResult.success(webPage);
    }

    /**
     * 导入历史数据
     */
    @GetMapping("/importHistoryData")
    public WebResult<String> importHistoryData() {
        try {
            List<Clue> all = ClueService.findAllClue();
            if (CollectionUtils.isEmpty(all)) {
                return WebResult.error(WebCodeMessageEnum.DATA_NOT_EXIST);
            }

            List<ClueInfo> clueViewList = new ArrayList<>();
            Map<String, ClueInfo> clueViews = all.stream()
                    .map(clueVo -> BeanCopyUtils.convertToVo(clueVo, ClueInfo.class))
                    .collect(Collectors.toMap(ClueInfo::getId, clueView -> clueView));

            List<String> ClueIdCollect = all.stream()
                    .map(Clue::getId)
                    .collect(Collectors.toList());

            List<BusinessOpportunity> businessOppListByClue = businessAppService.findByClueIdList(ClueIdCollect);
            if (CollectionUtils.isEmpty(businessOppListByClue)) {
                return WebResult.error(WebCodeMessageEnum.DATA_NOT_EXIST);
            }

            // 匹配clueViews和bySjCodeList，设置客户ID
            for (BusinessOpportunity businessOpportunity :businessOppListByClue) {
                ClueInfo clueView = clueViews.get(businessOpportunity.getClueId());
                if (clueView != null) {
                    clueView.setCustomerId(businessOpportunity.getCustId());
                    clueViewList.add(clueView);
                }
            }

            // 按是否有客户ID分类线索数据
            List<ClueInfo> clueViewsWithCustId = clueViewList.stream()
                    .filter(clueView -> !StringUtils.isEmpty(clueView.getCustomerId()))
                    .collect(Collectors.toList());

            List<ClueInfo> clueViewsWithoutCustId = clueViewList.stream()
                    .filter(clueView -> StringUtils.isEmpty(clueView.getCustomerId()))
                    .collect(Collectors.toList());
            // 调用业务接口处理线索数据
            CustomerLeadsImportOrDistributeDto customerLeadsImportOrDistributeDto = new CustomerLeadsImportOrDistributeDto();
            customerLeadsImportOrDistributeDto.setClueListWithCid(clueViewsWithCustId);
            customerLeadsImportOrDistributeDto.setClueList(clueViewsWithoutCustId);
            customerLeadsImportOrDistributeDto.setLeadsImportFrom(1); // 1表示从历史数据导入

            Boolean result = customerLeadsBusiness.customerLeadsHandle(customerLeadsImportOrDistributeDto);
            if (!result) {
                return WebResult.error(WebCodeMessageEnum.CUSTOMER_LEADS_IMPORT_FAIL);
            }else {
                return WebResult.success(WebCodeMessageEnum.REQUEST_SUCCESS.getMsg());
            }

        } catch (Exception e) {
            log.error("导入历史数据异常", e);
            return WebResult.error(WebCodeMessageEnum.CUSTOMER_LEADS_IMPORT_FAIL);
        }
    }

}
