package com.ce.scrm.center.web.enums;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:28:30
 * @desc 客户剩余保护时长筛选项枚举
 */
public enum CustomerRemainProtectFilterEnum implements EnumBase {

    CRPF0(0, "今天"),
    CRPF1(1, "近1日内"),
    CRPF2(2, "3日内"),
    CRPF3(3, "7日内"),
    CRPF4(4, "15日内"),
    CRPF5(5, "30日内"),
    ;

    @Getter
    private final Integer label;
    @Getter
    private final String value;

    // Getter 方法
    public String getKey() {
        return String.valueOf(label);
    }

    // 构造函数
    CustomerRemainProtectFilterEnum(Integer label, String value) {
        this.label = label;
        this.value = value;
    }

    public static String getDateRange(Integer label) {
        Date startDate = new Date();
        DateTime endDate = null;
        switch (label) {
            case 0:
                endDate = DateUtil.endOfDay(startDate);
                break;
            case 1:
                endDate = DateUtil.offsetHour(startDate, 24);
                break;
            case 2:
                endDate = DateUtil.offsetHour(startDate, 24 * 3);
                break;
            case 3:
                endDate = DateUtil.offsetHour(startDate, 24 * 7);
                break;
            case 4:
                endDate = DateUtil.offsetHour(startDate, 24 * 15);
                break;
            case 5:
                endDate = DateUtil.offsetHour(startDate, 24 * 30);
                break;
            default:
                endDate = DateUtil.endOfDay(startDate);
        }
        String strStartDate = DateUtil.format(startDate, "yyyy-MM-dd HH:mm:ss");
        String strEndDate = DateUtil.format(endDate, "yyyy-MM-dd HH:mm:ss");
        return strStartDate + "," + strEndDate;
    }
}
