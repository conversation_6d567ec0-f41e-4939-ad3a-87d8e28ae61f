package com.ce.scrm.center.web.entity.dto.abm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 *  转商机参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AbmAddReviewCheckWebDto extends LoginInfo implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 联系人名称
     */
    private String contactPersonName;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 通讯地址
     */
    private String address;

    /**
     * 意向产品 支持多个  A,B,C,D
     */
    private String intentProduct;

    /**
     * 推荐理由
     */
    private String recommendedReason;

    /**
     * 附件信息 多个格式：
     *      [{"fileUrl":"http://test-cesupport-images.ceboss.cn/upload/notice/202508/9F539523ABC44B70B1B54D0136ED10AF.jpg","fileName":"微信图片_2025-05-28_101945_177.jpg","fileId":"9F539523ABC44B70B1B54D0136ED10AF"}]
     */
    private List<Map<String, Object>> attachment;
}