package com.ce.scrm.center.web.controller.operationLog;

import com.ce.scrm.center.service.enums.CustomerStageEnum;
import com.ce.scrm.center.service.enums.SkbFlag7Enum;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/21
 */
public enum EmployeeOperationMethodEnum {

    method1("/api-scrm/security/cluecustomer/release","线索释放"),
    method2("/api-scrm/security/newcustomer/getCustPidByCustId","通过客户id查看pid"),
    method3("/api-scrm/security/customer/custCoupon","查询客户的优惠券信息"),
    method4("/api-scrm/security/convertLog/getPageByCustId","获取客户流转日志分页列表"),
    method5("/api-scrm/security/visitlog/getHistoryVisitLinkMan","获取历史写跟进时填写的联系人信息"),
    method6("/api-scrm/security/customerfollow/getLastTimeCustomerFollow","查询客户跟进"),
    method7("/api-scrm/security/newcustomer/getCustomerDetailAndProtectById","获取客户基本信息和保护关系"),
    method8("/api-scrm/security/productInstance/getProductsByCustId","查询客户的产品列表"),
    method9("/api-scrm/security/visitlog/getVisitLogs","查询跟进记录列表"),
    method10("/api-scrm/security/gj/serviceInfo/getServiceInfos","查看历史服务列表"),
    method11("/api-scrm/security/newcustomer/checkIsCurrentSalerProtect","检查是否当前商务的客户"),
    method12("/api-scrm/security/clueAccessLog/record","商务查看线索详情日志"),
    method13("/api-scrm/security/operate/clueToMyCust","收藏夹-转保护"),
    method14("/scrm-web/linkman/getCustomerLinkmanListByCustomerId","获取客户联系人列表"),
    method15("/api-scrm/security/performance/findEmpAchDetail","获取商务的业绩明细"),
    method16("/api-scrm/entwxapi/customerfollow/getLastTimeCustomerFollow","查询客户跟进"),
    method17("/api-scrm/security/cooperation/getInfoToCooperation","查询商务间合作信息"),
    method18("/api-scrm/security/tagOpportunityOrigin","获取商机客户、报价客户、转介绍客户"),
    method19("/api-scrm/security/operate/releaseMyCust","释放我的客户"),
    method20("/api-scrm/entwxapi/operate/tagOpportunityOrigin","获取商机客户、报价客户、转介绍客户"),
    method21("/api-scrm/security/sop/serviceOrderInfo","服务单详情"),
    method22("/api-scrm/security/operate/majorReleaseSjCust","总监从商机待分释放商机客户"),
    method23("/api-scrm/security/custdetail/findOrderInfoByCustId","通过客户id查询订单信息"),
    method24("/api-scrm/security/custdetail/findBossOrder","获取boss结算单"),
    method25("/api-scrm/entwxapi/visitlog/getHistoryVisitLinkMan","获取历史写跟进时填写的联系人信息"),
    method26("/scrm-web/entwx/linkman/getCustomerLinkmanListByCustomerId","获取客户联系人列表"),
    method27("/api-scrm/security/custMaterial/findCustOfficialSealAndContractSeal","从客户资料中获取公章和合同章"),
    method28("/api-scrm/entwxapi/operate/releaseMyCust","释放我的客户"),
    method29("/api-scrm/entwxapi/empCustSiteClock/save/v2","地理位置打卡-微信端新逻辑，跟随腾讯地图转搜客宝需求"),
    method30("/api-scrm/security/newcustomer/isMemberByCustId","通过客户id查看是否存在会员"),
    method31("/api-scrm/entwxapi/operate/custpoolToMyCust","客户池客户转客户"),
    method32("/api-scrm/security/newcustomer/getBigDataCustomerByCustName","根据客户名查询搜客宝客户信息"),
    method33("/api-scrm/security/transaction/chengjiao","成交客户列表"),
    method34("/api-scrm/entwxapi/customer/findEsCustByCustName","根据客户名称模糊搜索"),
    method35("/api-scrm/entwxapi/customer/exactQueryNew","精查客户"),
    method36("/scrm-web/clue/favorite/page","收藏夹列表"),
    method37("/api-scrm/security/exactmatch/matchByName","精查客户"),
    method38("/scrm-web/customer/hasHistoryNameFlag","是否有曾用名"),
    method39("/api-scrm/entwxapi/customer/info/getByName","根据客户名查询搜客宝客户信息"),
    method40("/api-scrm/entwxapi/transaction/chengjiao","成交客户列表"),
    method41("/scrm-web/potential/findMyProtectCustomer","我的保护列表"),
    method42("/api-scrm/security/cluecustomer/turnToClue","转收藏"),
    method43("/api-scrm/entwxapi/potential/findMyProtectCustomer","我的保护列表"),
    method44("/api-scrm/entwxapi/cluecustomerapp/turnToClue","转收藏"),
    method45("/api-scrm/security/newcustomerdetail/updateCustomerRegionBySkb","精查客户-纠正市场"),
    method46("/api-scrm/security/cust/transfer/apply","调拨申请"),
    method47("/api-scrm/security/customerfollow/selectPage","我的跟进表"),
    method48("/api-scrm/security/telClue/findByComplexPage","意向系统线索列表页综合查询"),
    method49("/api-scrm/security/post/telClue/list","意向系统线索列表页"),
    method50("/api-scrm/security/telBusiness/findByComplexPage","意向系统商机列表页综合查询"),
    method51("/api-scrm/security/telBusiness/list","意向系统商机列表页"),
    method52("/api-scrm/security/assign/findWillAssignList","获取总监、经理待分配线索"),
    method53("/api-scrm/security/assign/findWillAssignByDept","查询部门待分配客户列表"),
    method54("/api-scrm/security/stress/getStressCustPage","获取重点客户列表"),
    method55("/scrm-web/circulationLoss/getCirculationList","查询即将流转客户"),
    method56("/api-scrm/security/potential/findSjCustsAmountTotal","根据条件查询客户当前预计交易金额总数"),
    method57("/api-scrm/security/potential/findSjCusts","查询商机客户"),
    method58("/api-scrm/security/cooperation/selectPage","查询合作列表"),
    method59("/api-scrm/security/assginMarket/zbGetCanAssgin","总部获取可调拨客户"),
    method60("/api-scrm/security/assginMarket/zbGetClueCanAssgin","获取区总可调拨的线索客户"),
    method61("/api-scrm/entwxapi/customerfollow/selectPage","我的跟进表"),
    method62("/api-scrm/entwxapi/visitlog/getVisitedCustPage","查询跟进记录"),
    method63("/api-scrm/security/sjcust/getSjCustTrackListPage","商机跟踪列表"),
    method64("/api-scrm/security/visitlog/getVisitLogsForVisit","查询拜访记录，只查询是拜访的"),
    method65("/api-scrm/entwxapi/customer/newcustomerdetail/queryPersonalCust","精查个人客户"),
    method66("/api-scrm/security/newcustomerdetail/queryPersonalCust","查询个人客户"),
    method67("/api-scrm/security/visitlog/getVisitedCustPage","查询跟进记录"),
    method68("/api-scrm/security/newcustomer/add","添加客户(只校验客户名是否重复）"),
    method69("/api-scrm/security/newcustomer/getCustomerlByName","根据客户名字获取客户基本信息"),
    method70("/api-scrm/security/operate/selectCustProtect","协同工具-快捷工具-修改客户阶段-查询"),
    method71("/api-scrm/security/operate/custpoolToMyCust","客户池客户转客户"),
    method72("/api-scrm/security/potential/updateCustomTags","商务更新客户自定义标签"),
    method73("/api-scrm/entwxapi/customerfollow/saveCustomerFollow/v2","线索客户转客户-微信端新逻辑，跟随腾讯地图转搜客宝需求"),
    method74("/api-scrm/security/newcustomerlink/findLinkManListForUpdate","根据客户id获取新客联系人用于编辑联系人"),
    method75("/api-scrm/entwxapi/newcustomerlink/saveOrUpdateLinkMan","保存或更新客户联系人信息"),
    method76("/api-scrm/security/sop/searchServiceOrderList","获取服务单列表"),
    method77("/api-scrm/security/gj/planTask/getGjPlanTasksByCustId","查询客户名下的计划任务"),
    method78("/api-scrm/security/gj/taskCust/getTaskCustsByCustId", "查询客户名下的任务列表（未处理）"),
    method79("/api-scrm/entwxapi/operate/clueToMyCust","线索客户转客户"),
    method80("/api-scrm/entwxapi/order/findOrderInfoByCustId","通过客户id查询订单信息"),
    method81("/api-scrm/entwxapi/customer/account/get","查询客户会员账户信息"),
    method82("/api-scrm/entwxapi/proinstance/getProductsByCustId","通过客户id查询产品实例信息"),
    method83("/api-scrm/security/cust/transfer/approve","调拨申请通过"),
    method84("/api-scrm/security/assign/zbSjAssign","获取总监、经理待分配"),
    method85("/api-scrm/entwxapi/empCustSiteClock/checkCustIsExistAndSave","地理位置打卡-检测用户是否存在，如果不存在则新增"),
    method86("/api-scrm/entwxapi/operate/clueToMyCust/v2","线索客户转客户-微信端新逻辑，跟随腾讯地图转搜客宝需求"),
    method87("/api-scrm/security/empCustSiteClock/findPageList","查询打卡记录分页"),
    method88("/api-scrm/entwxapi/sjcust/addsjCloudCustomerMade","商机云定制添加"),
    method89("/scrm-web/eqixiu/getActivityUrl","易企秀获取活动链接"),
    method90("/api-scrm/entwxapi/empCustSiteClock/findPageList","查询打卡记录分页"),
    method91("/api-scrm/security/empCustSiteClock/findNoBindFollowRecordList","查询打卡记录分页"),
    method92("/api-scrm/entwxapi/cluecustomerapp/release","线索释放"),
    method93("/api-scrm/entwxapi/empCustSiteClock/cooperationSalerSave","保护商务打卡"),
    method94("/scrm-web/circulationLoss/saveLoseReason","总监、流失客户、保存流失理由"),
    method95("/api-scrm/security/operate/personalToMyCust","协同工具-快捷工具-修改客户名称-个人变企业"),
    method96("/api-scrm/security/operate/updateCustStage","协同工具-快捷工具-修改客户阶段-变更"),
    method97("/api-scrm/security/newcustomerlink/findLinkManListFromCmaLinkMan","根据客户id获取新客联系人"),
    method98("/scrm-web/customerLoss/getCustomerLossList","获取即将流转客户"),
    method99("/api-scrm/security/newcustomerdetail/exactQuery","精查客户"),
    method100("/api-scrm/security/findEsCustByCustName","根据客户名称模糊检索"),
    method101("/scrm-web/customer/affiliatedEnterprise","客户所关联的其它企业信息"),
    method102("/api-scrm/entwxapi/homepage/getPerformances","首页业绩"),

    method103("/api-scrm/entwxapi/homepage/getRanking","首页业绩排行"),

    method104("/api-scrm/security/callcenter/zhibo/checkBeforeCall","拨打电话之前手机号黑名单和超频校验"),

    method105("/api-scrm/security/newcustomerdetail/checkCustIsExis","精查判断用户是否存在"),

    method106("/scrm-web/customer/getCustomerLocationInfo","根据客户名称获取客户位置"),

    method107("/api-scrm/security/telBusiness/allList","查询商机列表"),
    method108("/api-scrm/security/telClue/handleClue","处理线索"),

    method109("/api-scrm/security/telClue/clueDetail","查询线索详情"),

    method110("/api-scrm/security/telBusiness/businessDetail","查询商机详情"),

    method111("/api-scrm/entwxapi/customer/getNearbyCustomersFromES","附近客户"),
    method112("/api-scrm/security/customerfollow/saveCustomerFollow","写跟进"),












    ;

    private final String code;
    private final String msg;

    EmployeeOperationMethodEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static EmployeeOperationMethodEnum getByUrl(String url) {
        for (EmployeeOperationMethodEnum e : EmployeeOperationMethodEnum.values()) {
            if (e.getCode().equals(url)) {
                return e;
            }
        }
        return null;
    }
}
