package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 中小商务查询高呈商机分页参数
 * <AUTHOR>
 * @date 2024/5/28 下午3:26
 * @version 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CeQueryGcBusinessOpportunityPageWebDto extends LoginInfo implements Serializable {
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 每页数量
     */
    private Integer pageSize = 10;
}