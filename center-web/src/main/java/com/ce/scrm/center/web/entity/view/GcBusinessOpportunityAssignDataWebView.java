package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 高呈商机分配数据
 * <AUTHOR>
 * @date 2024/5/22 下午1:51
 * @version 1.0.0
 */
@Data
public class GcBusinessOpportunityAssignDataWebView implements Serializable {

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户阶段
     */
    private Integer customerStage;

    /**
     * 客户阶段名称
     */
    private String customerStageName;

    /**
     * 期望高呈区域ID
     */
    private String expectGcAreaId;

    /**
     * 期望高呈区域名称
     */
    private String expectGcAreaName;

    /**
     * 期望高呈分司ID
     */
    private String expectGcSubId;

    /**
     * 期望高呈分司名称
     */
    private String expectGcSubName;

    /**
     * 期望高呈部门ID
     */
    private String expectGcDeptId;

    /**
     * 期望高呈部门名称
     */
    private String expectGcDeptName;

    /**
     * 期望高呈商务ID
     */
    private String expectGcSalerId;

    /**
     * 期望高呈商务名称
     */
    private String expectGcSalerName;

    /**
     * 高呈员工数据列表
     */
    private List<GcEmployeeData> gcEmployeeDataList;

    /**
     * 高呈员工数据
     */
    @Data
    public static class GcEmployeeData implements Serializable {

        /**
         * 员工ID
         */
        private String employeeId;

        /**
         * 员工名称
         */
        private String employeeName;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 部门ID
         */
        private String deptId;

        /**
         * 部门名称
         */
        private String deptName;

        /**
         * 职位
         */
        private String position;
    }
}