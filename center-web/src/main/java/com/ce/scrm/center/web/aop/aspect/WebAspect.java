package com.ce.scrm.center.web.aop.aspect;

import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.ce.sso.common.dto.CurrentUser;
import cn.ce.sso.common.utils.CookieUtil;
import cn.ce.sso.common.utils.PubConstant;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.cache.EntVxCodeCacheHandler;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.support.log.LogObject;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.util.constant.UtilConstant;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.aop.base.LoginType;
import com.ce.scrm.center.web.entity.dto.AddLogMqData;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.util.CustomKeyAES;
import com.ce.scrm.center.web.util.MD5;
import com.ce.scrm.center.web.util.RequestUtil;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.filter.AbstractRequestLoggingFilter;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_OPERATION_LOG_TOPIC;
import static org.apache.http.entity.ContentType.APPLICATION_FORM_URLENCODED;
import static org.springframework.web.util.WebUtils.getNativeRequest;

/**
 * web业务切面
 * <AUTHOR>
 * @date 2023/4/6 20:48
 * @version 1.0.0
 **/
@Slf4j
@Aspect
@Component
public class WebAspect {

    // 微信登录前缀
    private static final String ENT_WX_USERID_ = "ent_wx_userid_";
    private static final String ENT_WX_PARAMETER_PREFIX_ = "N2Wsxr_";

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private EntVxCodeCacheHandler entVxCodeCacheHandler;

    /**
     * 执行成功消息
     */
    private final static String EXECUTE_SUCCESS = "success";

    /**
     * 默认代指前端调用
     */
    public final static String WEB_INVOKE = "webInvoke";

    @Value("${auth.login.kuajing.secret}")
    private String kuajingSecretKey;

    @Value("${auth.login.cesupport.secret}")
    private String cesupportSecretKey;

    @Resource
    private RocketMqOperate rocketMqOperate;

    /**
     * 定义cat监控扫描路径
     * 定义切点
     * <AUTHOR>
     * @date 2023/4/6 20:48
     **/
    @Pointcut("execution(public * com.ce.scrm.center.web.controller..*.*(..))")
    public void requestConfigPointCut() {
    }

    /**
     * 对于监控到的方法进行监控增强处理
     * 定义环绕类型的Advise（增强器）
     * @param joinPoint 切面连接点（被代理方法的相关封装）
     * <AUTHOR>
     * @date 2023/4/6 20:48
     * @return java.lang.Object
     **/
    @Around("requestConfigPointCut()")
    public Object around(ProceedingJoinPoint joinPoint) {
        //添加cat监控
        Class<?> clazz = joinPoint.getTarget().getClass();
        String className = clazz.getSimpleName();
        Signature signature = joinPoint.getSignature();
        String methodName = signature.getName();
        Transaction serviceCat = Cat.newTransaction("Controller", className + UtilConstant.CAT_SEPARATOR + methodName);
        serviceCat.setStatus(Transaction.SUCCESS);
        Object proceed = null;
        LogObject logObject = new LogObject();
        String traceId = MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME);
        Object[] args = joinPoint.getArgs();
	    try {
		    boolean hasMultipart = Arrays.stream(args).anyMatch(arg -> arg instanceof MultipartFile || (arg != null && arg.getClass().isArray() && MultipartFile.class.isAssignableFrom(arg.getClass().getComponentType())));
		    if (hasMultipart) {
			    logObject.setRequest("包含 MultipartFile 类型参数，未打印");
		    } else {
			    logObject.setRequest(JSON.toJSONString(args));
		    }
	    } catch (Exception ex) {
		    logObject.setRequest("参数序列化失败：" + ex.getMessage());
	    }
        logObject.setInvokerName(WEB_INVOKE)
                .setInvokerIp(RequestUtil.getIp())
                .setEventName(className + UtilConstant.DATA_SEPARATOR + methodName)
                .setTraceId(traceId);
        String msg = EXECUTE_SUCCESS;
		String sourceEmpId = StringUtils.EMPTY;
        buildLoginInfo(joinPoint);
        try {
            //校验登录
            ServletRequestAttributes requestAttributes = (ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes());
            String requestUri = requestAttributes.getRequest().getRequestURI();
            WebResult<Boolean> checkLogin = checkLogin(joinPoint, clazz, signature, requestAttributes.getRequest());
            if (!checkLogin.checkSuccess()) {
                log.warn("接口验证登录失败，接口地址为:{}", requestUri);
                return checkLogin;
            }
            LoginInfo baseLoginInfo = getBaseLoginInfo(joinPoint);
            if (baseLoginInfo != null) {
                logObject.setLoginInfo(baseLoginInfo);
	            sourceEmpId = baseLoginInfo.getLoginEmployeeId();
            }
            proceed = joinPoint.proceed();
        } catch (Throwable e) {
            // 全局业务异常处理
            if(e instanceof ApiException) {
                CodeMessageEnum apiResult = ((ApiException) e).getApiResult();
                return WebResult.error(apiResult.getCode(),apiResult.getMsg());
            }
            WebResult<Object> webResult = WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
            msg = e.getMessage();
            serviceCat.setStatus(e);
            Cat.logError(e);
            log.error("{}" + UtilConstant.DATA_SEPARATOR + "{}方法异常:", className, methodName, e);
            //全局异常捕获
            return (proceed = webResult);
        } finally {
            if (proceed instanceof WebResult) {
                WebResult<?> resultEntity = (WebResult<?>) proceed;
                resultEntity.setTraceId(traceId);
                //非正常返回，则返回traceId
                if (!resultEntity.checkSuccess()) {
                    logObject.setResponse(JSON.toJSONString(proceed));
                }
            }
            logObject.setMsg(msg).setCostTime(System.currentTimeMillis() - Long.parseLong(MDC.get(UtilConstant.Mdc.START_TIME)));
            log.info(JSON.toJSONString(logObject));
            serviceCat.complete();
        }
        return proceed;
    }

    private void buildLoginInfo(ProceedingJoinPoint joinPoint) {
        // 封装登录信息 此处不需要验证登录 有则存入
        try {
            RequestAttributes requestAttributes1 = RequestContextHolder.getRequestAttributes();
            if (Objects.nonNull(requestAttributes1)) {
                HttpServletRequest request = ((ServletRequestAttributes) requestAttributes1).getRequest();
                String url = request.getRequestURL().toString();
                WebResult<String> empIdResult;
                if (StringUtils.isNotBlank(url) && url.contains("scrm-web/entwx/")) {
                    empIdResult = checkEntWxLogin(request);
                }else {
                    empIdResult = checkPcLogin(request);
                }
                if (empIdResult != null && empIdResult.checkSuccess() && empIdResult.getData() != null) {
                    String empId = empIdResult.getData();
	                mdcSetEmpId(empId);
	                //获取员工的部门分司等数据
                    Optional<EmployeeDataThirdView> employeeDataThirdViewOptional = employeeThirdService.getEmployeeData(empId);
                    if (employeeDataThirdViewOptional.isPresent()) {
                        LoginInfo baseLoginInfo = getBaseLoginInfo(joinPoint);
                        baseLoginInfo.convert(employeeDataThirdViewOptional.get());
                    }

                    try {
                        //发送员工操作日志
                        AddLogMqData addLogMqData = new AddLogMqData();
                        addLogMqData.setEmpId(empId);
                        addLogMqData.setCreateTime(new Date());
                        addLogMqData.setUrl(request.getRequestURI());
                        String p = getPrettyParam(request).toString();
                        if (p.length() > 2000) {
                            return;
                        }
                        addLogMqData.setParams(p);
                        rocketMqOperate.syncSend(SCRM_OPERATION_LOG_TOPIC, JSON.toJSONString(addLogMqData));
                    }catch (Exception e){
                        log.error("记录操作日志失败,empId={}",empId);
                    }
                }
            }
        }catch (Exception e) {
            log.error("封装登录信息失败",e);
        }
    }

	/**
	 * MDC中设置员工id
	 * @param sourceEmpId 员工id
	 */
	private void mdcSetEmpId(String sourceEmpId) {
		// 获取当前线程的 MDC 上下文,并在子线程中恢复 MDC 上下文
		Map<String, String> contextMap = MDC.getCopyOfContextMap();
		MDC.setContextMap(contextMap);
		MDC.put(UtilConstant.Mdc.SOURCE_EMP_ID, sourceEmpId);
		RpcContext.getContext().setAttachment(UtilConstant.Mdc.SOURCE_EMP_ID, sourceEmpId);
	}

	/**
     * 校验员工是否登录
     * @param joinPoint 切点信息
     * @param clazz 类信息
     * @param signature 方法签名信息
     * @param request   请求信息
     * <AUTHOR>
     * @date 2024/3/11 14:03
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    private WebResult<Boolean> checkLogin(ProceedingJoinPoint joinPoint, Class<?> clazz, Signature signature, HttpServletRequest request) {
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        Login methodAnnotationPresent = method.getAnnotation(Login.class);
        Login classAnnotationPresent = clazz.getAnnotation(Login.class);
        Login loginAnnotation = (methodAnnotationPresent != null) ? methodAnnotationPresent : classAnnotationPresent;
        if (loginAnnotation == null) {
            return WebResult.success();
        }
        //是否校验登录
        boolean isCheckLogin = loginAnnotation.checkLogin();
        if (!isCheckLogin) {
            return WebResult.success();
        }
        LoginInfo baseLoginInfo = getBaseLoginInfo(joinPoint);
        if (baseLoginInfo == null) {
            log.warn("scrm验证登录，参数没有继承LoginInfo，无法设置员工信息，接口地址为:{}", clazz.getSimpleName() + "." + signature.getName());
            return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
        }
        LoginType loginType = loginAnnotation.loginType();
        WebResult<String> empIdResult;
        switch (loginType) {
            case PC:
                empIdResult = checkPcLogin(request);
                break;
            case MOBILE:
                empIdResult = checkMobileLogin(request);
                break;
            case ENT_WECHAT:
                empIdResult = checkEntWxLogin(request);
                break;
            default:
                log.error("scrm验证登录，登录类型设置无效，无法验证登录，接口地址为:{}", clazz.getSimpleName() + "." + signature.getName());
                return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
        }
        if (!empIdResult.checkSuccess()) {
            log.warn("scrm验证登录，登录失败，接口地址为:{}", clazz.getSimpleName() + "." + signature.getName());
            return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
        }
        //获取员工的部门分司等数据
        Optional<EmployeeDataThirdView> employeeDataThirdViewOptional = employeeThirdService.getEmployeeData(empIdResult.getData());
        if (!employeeDataThirdViewOptional.isPresent()) {
            log.warn("scrm验证登录，获取员工信息为空，员工ID为：{}", empIdResult.getData());
            return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
        }
        baseLoginInfo.convert(employeeDataThirdViewOptional.get());
        return WebResult.success(Boolean.TRUE);
    }

    private WebResult<String> checkEntWxLogin(HttpServletRequest request) {
//        String empId = request.getParameter("empId");
        String empIdValue = request.getHeader("authorization");
        if (StringUtils.isBlank(empIdValue)) {
            return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
        }
        String empId = null;
        try {
            empId = CustomKeyAES.decrypt(empIdValue,cesupportSecretKey);
        } catch (Exception e) {
            log.error("checkEntWxLogin员工解密失败,empIdValue={}",empIdValue);
            return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
        }
        String code = entVxCodeCacheHandler.get(empId);
        if (StringUtils.isBlank(code)) {
            log.warn("scrm entwx企微端接口验证登录，获取redis中员工信息为空，员工ID为：{}", empId);
            return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
        }
        entVxCodeCacheHandler.setCacheData(empId, code);
        return WebResult.success(empId);
    }

    /**
     * 校验pc登录
     * @param request   请求信息
     * <AUTHOR>
     * @date 2024/3/11 15:55
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.String>
     **/
    public WebResult<String> checkPcLogin(HttpServletRequest request) {
        String signUserId = CookieUtil.getValue(request, "CBOST");
        if (StringUtils.isNotBlank(signUserId)){
            try {
               String empId = CustomKeyAES.decrypt(signUserId, kuajingSecretKey);
                return WebResult.success(empId);
            }catch (Exception e){
                log.info("跨境解密员工ID失败,signUserId={}",signUserId);
            }
            return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
        }else {
            //新单点获取员工id的方式
            HttpSession session = request.getSession();
            CurrentUser currentUser = (CurrentUser) session.getAttribute(PubConstant.SESSION_USER_KEY);
            if (null == currentUser) {
                return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
            }
            String empId = (String) currentUser.getUserInfo().get("empId");
            if (StringUtils.isEmpty(empId)) {
                log.warn("scrm pc接口验证登录，session中的员工ID为空，session中的员工信息为:{}", JSON.toJSONString(currentUser));
                return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
            }
            return WebResult.success(empId);
        }
    }

    /**
     * 校验企微端登录
     * @param request   请求信息
     * <AUTHOR>
     * @date 2024/3/11 16:26
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.String>
     **/
    private WebResult<String> checkMobileLogin(HttpServletRequest request) {
        String empId = request.getParameter("empId");
        String code = redisTemplate.opsForValue().get(ENT_WX_USERID_ + empId);
        if (StringUtils.isBlank(code)) {
            log.warn("scrm企微端接口验证登录，获取redis中员工信息为空，员工ID为：{}", empId);
            return WebResult.error(WebCodeMessageEnum.WX_LOGIN_EXPIRED);
        }
        redisTemplate.opsForValue().set(ENT_WX_USERID_ + empId, code, 10, TimeUnit.HOURS);

        String token = request.getParameter(ENT_WX_PARAMETER_PREFIX_ + "sing");
        String timestamp = request.getParameter(ENT_WX_PARAMETER_PREFIX_ + "timestamp");
        String requestParams = StringUtils.isBlank(request.getParameter("requestParams")) ? "{}" : request.getParameter("requestParams");
        String md5 = MD5.md5crypt(requestParams + code + timestamp);
        if (!md5.equals(token)) {
            log.warn("scrm企微端接口验证登录，token验证失败，员工ID为：{}, 参数requestParams为：{}", empId, requestParams);
            return WebResult.error(WebCodeMessageEnum.WX_LOGIN_EXPIRED);
        }
        return WebResult.success(empId);
    }

    /**
     * 从切点中获取登录信息参数
     * @param joinPoint 切点
     * <AUTHOR>
     * @date 2024/3/11 14:06
     * @return com.ce.scrm.center.web.aop.base.LoginInfo
     **/
    private LoginInfo getBaseLoginInfo(ProceedingJoinPoint joinPoint) {
        LoginInfo loginInfo = new LoginInfo();
        Object[] arguments = joinPoint.getArgs();
        if (arguments != null) {
            for (Object object : arguments) {
                if (object instanceof LoginInfo) {
                    loginInfo = (LoginInfo) object;
                    break;
                }
            }
        }
        return loginInfo;
    }

    /**
     * 获取请求的参数
     *
     * @param request 请求
     * @return 参数
     */
    public static StringBuilder getPrettyParam(HttpServletRequest request) {
        if (HttpMethod.GET.matches(request.getMethod()) || StringUtils.contains(request.getContentType(), APPLICATION_FORM_URLENCODED.getMimeType())) {
            StringBuilder sb = new StringBuilder();
            Map<String, String[]> parameterMap = request.getParameterMap();
            return sb.append(JSONObject.toJSONString(parameterMap));
        }
        return new StringBuilder().append(getMessagePayloadForCacheFilter(request));
    }

    public static String getURLAndQueryParameter(HttpServletRequest request) {

        String queryString = request.getQueryString();
        if (queryString == null) {
            queryString = request.getRequestURI();
        } else {
            queryString = request.getRequestURI() + "?" + queryString;
        }
        return queryString;
    }

    /**
     * 获取json参数
     *
     * @see AbstractRequestLoggingFilter#getMessagePayload(HttpServletRequest)
     */
    @SuppressWarnings("JavadocReference")
    private static String getMessagePayloadForCacheFilter(HttpServletRequest request) {
        ContentCachingRequestWrapper wrapper = getNativeRequest(request, ContentCachingRequestWrapper.class);
        if (wrapper == null) {
            FirewallHttpServletRequetWrapper firewallHttpServletRequetWrapper = getNativeRequest(request, FirewallHttpServletRequetWrapper.class);
            if (Objects.nonNull(firewallHttpServletRequetWrapper)) {
                try {
                    byte[] bytes = StreamUtils.copyToByteArray(firewallHttpServletRequetWrapper.getInputStream());
                    return new String(bytes, StandardCharsets.UTF_8);
                } catch (Exception exception) {
                    log.error("WebUtils getMessagePayloadForCacheFilter ,fail", exception);
                }
            }
            return null;
        }
        byte[] buf = wrapper.getContentAsByteArray();
        if (buf.length <= 0) {
            return null;
        }
        try {
            return new String(buf, wrapper.getCharacterEncoding());
        } catch (UnsupportedEncodingException ex) {
            return "[unknown]";
        }
    }
}
