package com.ce.scrm.center.web.controller.ai.cdp;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.business.AiCdpBusiness;
import com.ce.scrm.center.service.business.entity.dto.ai.AiBusinessDto;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.ai.AiWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @version 1.0
 * @Description: ai控制层-cdp
 * @Author: lijinpeng
 * @Date: 2025/2/17 13:42
 */
@Login
@RestController
@RequestMapping("/ai/cdp")
public class AiCdpController {

    @Resource
    private AiCdpBusiness aiCdpBusiness;


    @PostMapping(value = "cdpInfo")
    public WebResult<String> getCdpInfoFromDb(@RequestBody AiWebDto aiWebDto) {
        AiBusinessDto businessDto = BeanCopyUtils.convertToVo(aiWebDto, AiBusinessDto.class);
        JSONObject result = aiCdpBusiness.getAiCdpData(businessDto);
        return WebResult.success(result.toJSONString());
    }

    @PostMapping(value = "workOrderCreateInfo")
    public WebResult<String> getCdpCreateWorkOrderData(@RequestBody AiWebDto aiWebDto) {
        AiBusinessDto businessDto = BeanCopyUtils.convertToVo(aiWebDto, AiBusinessDto.class);
        JSONObject result = aiCdpBusiness.getCdpCreateWorkOrderData(businessDto);
        return WebResult.success(result.toJSONString());
    }


}
