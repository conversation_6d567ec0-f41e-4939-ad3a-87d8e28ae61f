package com.ce.scrm.center.web.entity.dto;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import com.ce.scrm.center.service.business.entity.dto.CustomerCirculationSpecialSettingPageBusinessDto;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 不流转不流失客户表-分页请求体
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
public class CustomerCirculationSpecialSettingPageDto extends LoginInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 10;

    @NotNull(message = "不流转类型不能为空")
    private Integer notCirculationType;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 分公司ID
     */
    private String subId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 商户Id
     */
    private String salerId;

    /**
     * 客户名称
     */
    private String custName;

    public CustomerCirculationSpecialSettingPageBusinessDto packageBusinessDto(CustomerCirculationSpecialSettingPageDto webPageDto) {
        CustomerCirculationSpecialSettingPageBusinessDto businessDto = new CustomerCirculationSpecialSettingPageBusinessDto();
        BeanUtils.copyProperties(webPageDto, businessDto);
        // 组织架构筛选条件
        if (EmpPositionConstant.BUSINESS_AREA.equals(webPageDto.getLoginPosition())) {
            businessDto.setAreaId(webPageDto.getLoginAreaId());
        } else if (PositionUtil.isBusinessMajor(webPageDto.getLoginPosition())) {
            businessDto.setSubId(webPageDto.getLoginSubId());
        } else if (PositionUtil.isBusinessManager(webPageDto.getLoginPosition())) {
            businessDto.setDeptId(webPageDto.getLoginOrgId());
        } else if (PositionUtil.isBusinessSaler(webPageDto.getLoginPosition())) {
            businessDto.setSalerId(webPageDto.getLoginEmployeeId());
        } else if (EmpPositionConstant.BUSINESS_SYS_MANAGER.equals(webPageDto.getLoginPosition())) {
            // 系统管理员默认显示全部数据
        } else {
            // 其他角色默认不显示数据
            return null;
        }
        return businessDto;
    }

}
