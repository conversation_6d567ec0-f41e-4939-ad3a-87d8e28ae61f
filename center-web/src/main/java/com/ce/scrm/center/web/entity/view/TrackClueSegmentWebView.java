package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 专项线索追踪
 */
@Data
public class TrackClueSegmentWebView implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	private Integer id;

	/**
	 * 分群id
	 */
	private String segmentId;

	/**
	 * 分群名称
	 */
	private String segmentName;

	/**
	 * 下发总量
	 */
	private Integer segmentCount;

	/**
	 * 分群下发日期
	 */
	private Date segmentBeginTime;

	/**
	 * 昨日拜访量
	 */
	private Long yesterdayVisitCount;

	/**
	 * 昨日签单量
	 */
	private Long yesterdaySignCount;

	/**
	 * 累计拜访
	 */
	private Long allVisitCount;

	/**
	 * 累计签单
	 */
	private Long allSignCount;

}