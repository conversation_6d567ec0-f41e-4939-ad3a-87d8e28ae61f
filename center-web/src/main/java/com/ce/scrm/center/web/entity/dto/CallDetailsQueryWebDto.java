package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class CallDetailsQueryWebDto extends LoginInfo implements Serializable {

    /**
     * 筛选天
     */
    private Integer dayEnum;

    /**
     * 本月打卡次数筛选
     */
    private Integer currentMonthClockCountEnum;

    /**
     * 打卡次数筛选
     */
    private Integer clockCountEnum;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 20;



}
