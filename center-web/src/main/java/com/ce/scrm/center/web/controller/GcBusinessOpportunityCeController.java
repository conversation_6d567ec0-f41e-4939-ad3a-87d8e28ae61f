package com.ce.scrm.center.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.GcBusinessOpportunityBusiness;
import com.ce.scrm.center.service.business.entity.dto.CeQueryGcBusinessOpportunityPageBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.GcBusinessOpportunityPageCustDto;
import com.ce.scrm.center.service.business.entity.dto.GcSJCooperationSaveBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.MajorCheckCustBusinessDto;
import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityBusinessView;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.controller.base.BaseController;
import com.ce.scrm.center.web.entity.dto.*;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.GcBusinessOpportunityWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 高呈商机相关接口
 * 已废弃
 * <AUTHOR>
 * @date 2024/5/20 上午11:16
 * @version 1.0.0
 */
@Deprecated
@Login
@Slf4j
@RestController
@RequestMapping("gaocheng/shangji/ce")
public class GcBusinessOpportunityCeController extends BaseController {

    @Resource
    private GcBusinessOpportunityBusiness gcBusinessOpportunityBusiness;

    /**
     * 提交商机
     * @param gcBusinessOpportunityOperateWebDto    商机提交参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:41
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("submit")
    public WebResult<Boolean> submit(@Validated @RequestBody GcBusinessOpportunityOperateWebDto gcBusinessOpportunityOperateWebDto) {
        Optional<String> deleteOptional = gcBusinessOpportunityBusiness.submit(gcBusinessOpportunityOperateWebDto.packageOperateParam());
        return deleteOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * 撤回商机
     * @param gcBusinessOpportunityOperateWebDto    商机撤回参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:41
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("revocation")
    public WebResult<Boolean> revocation(@Validated @RequestBody GcBusinessOpportunityOperateWebDto gcBusinessOpportunityOperateWebDto) {
        Optional<String> deleteOptional = gcBusinessOpportunityBusiness.revocation(gcBusinessOpportunityOperateWebDto.packageOperateParam());
        return deleteOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * 删除商机
     * @param gcBusinessOpportunityOperateWebDto    商机删除参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:41
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("delete")
    public WebResult<Boolean> delete(@Validated @RequestBody GcBusinessOpportunityOperateWebDto gcBusinessOpportunityOperateWebDto) {
        Optional<String> deleteOptional = gcBusinessOpportunityBusiness.ceDelete(gcBusinessOpportunityOperateWebDto.packageOperateParam());
        return deleteOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * Description: 转高呈（中小商务）
     * @author: JiuDD
     * @param webDto  商机添加参数
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     * date: 2024/5/22 17:32
     */
    @PostMapping("addGcShangJi")
    public WebResult<Boolean> addGcShangJi(
            @Validated(GcSJCooperationSaveWebDto.SalerTransferToGc.class)
            @RequestBody GcSJCooperationSaveWebDto webDto) {
        GcSJCooperationSaveBusinessDto businessDto = CglibUtil.copy(webDto, GcSJCooperationSaveBusinessDto.class);
        businessDto.setSalerId(webDto.getLoginEmployeeId());
        businessDto.setSalerName(webDto.getLoginEmployeeName());
        businessDto.setOperator(webDto.getLoginEmployeeId());
        businessDto.setOperatorName(webDto.getLoginEmployeeName());
        Optional<String> addOptional = gcBusinessOpportunityBusiness.addGcBusinessOpportunityCe(businessDto);
        return addOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * Description: 编辑转高呈的商机（中小商务）
     * @author: JiuDD
     * @param webDto  商机编辑参数
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     * date: 2024/5/22 16:14
     */
    @PostMapping("updateGcShangJi")
    public WebResult<Boolean> updateGcShangJi(@Validated(GcSJCooperationSaveWebDto.SalerUpdateShangJi.class) @RequestBody GcSJCooperationSaveWebDto webDto) {
        GcSJCooperationSaveBusinessDto businessDto = CglibUtil.copy(webDto, GcSJCooperationSaveBusinessDto.class);
        businessDto.setId(webDto.getSjId());
        businessDto.setOperator(webDto.getLoginEmployeeId());
        businessDto.setOperatorName(webDto.getLoginEmployeeName());
        Optional<String> addOptional = gcBusinessOpportunityBusiness.updateGcBusinessOpportunity(businessDto);
        return addOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * Description: 手动分配商机到高呈分司或部门（市场部）
     *              前端判断当前选定的区域是KA销售部时，调用此新接口，否则还是调用老接口
     * @author: JiuDD
     * @param webDto  商机添加参数
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     * date: 2024/5/21 19:40
     */
    @PostMapping("assignShangJiToGc")
    public WebResult<Boolean> assignBusinessOpportunityToGc(@RequestBody GcSJCooperationSaveWebDto webDto) {
        GcSJCooperationSaveBusinessDto businessDto = CglibUtil.copy(webDto, GcSJCooperationSaveBusinessDto.class);
        businessDto.setOperator(webDto.getLoginEmployeeId());
        businessDto.setOperatorName(webDto.getLoginEmployeeName());
        Optional<String> addOptional = gcBusinessOpportunityBusiness.assignBusinessOpportunityToGc(businessDto);
        return addOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * Description: 转高呈待总监审批商机列表
     * @author: JiuDD
     * @param queryWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo < com.ce.scrm.center.web.entity.view.GcBusinessOpportunityWebView>>
     * date: 2024/5/22 11:31
     */
    @PostMapping("waitMajorApproveList")
    public WebResult<WebPageInfo<GcBusinessOpportunityWebView>> waitMajorApproveList(@RequestBody GcCustQueryWebDto queryWebDto) {
        String subId = queryWebDto.getLoginSubId();
        if (StrUtil.isBlank(subId)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, "当前操作人subId为空");
        }
        GcBusinessOpportunityPageCustDto dto = BeanUtil.copyProperties(queryWebDto, GcBusinessOpportunityPageCustDto.class);
        dto.setSubId(subId);
        Page<GcBusinessOpportunityBusinessView> gcCustList = gcBusinessOpportunityBusiness.getWaitMajorCheckCustList(dto);
        WebPageInfo<GcBusinessOpportunityWebView> ompCustList = WebPageInfo.pageConversion(gcCustList, GcBusinessOpportunityWebView.class);
        ompCustList.getList().forEach(e -> {
            e.setShowCurrentInfo(true);
            packageGcBusinessData(e);
        });
        return WebResult.success(ompCustList);
    }

    /**
     * Description: 审核转高呈商机（中小总监）
     * @author: JiuDD
     * @param webDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     * date: 2024/5/22 14:56
     */
    @PostMapping("majorApproveCust")
    public WebResult<Boolean> majorCheckCust(@RequestBody MajorCheckCustWebDto webDto) {
        Boolean loginGcEmployeeFlag = webDto.getLoginGcEmployeeFlag();
        if (loginGcEmployeeFlag) {
            log.warn("非中小商务无权操作，参数为:{}", JSON.toJSONString(webDto));
            return WebResult.error(WebCodeMessageEnum.REQUEST_POSITION_NOT_MATCH, "非中小商务无权操作");
        }
        MajorCheckCustBusinessDto businessDto = BeanUtil.copyProperties(webDto, MajorCheckCustBusinessDto.class);
        businessDto.setId(webDto.getSjId());
        businessDto.setCheckMajorId(webDto.getLoginEmployeeId());
        businessDto.setSubId(webDto.getLoginSubId());
        businessDto.setOperator(webDto.getLoginEmployeeId());
        businessDto.setOperatorName(webDto.getLoginEmployeeName());
        Optional<String> updateOptional = gcBusinessOpportunityBusiness.majorCheckCust(businessDto);
        return updateOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * 转高呈分页列表查询
     * @param ceQueryGcBusinessOpportunityPageWebDto  转高呈分页参数
     * <AUTHOR>
     * @date 2024/5/28 下午3:29
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo < com.ce.scrm.center.web.entity.view.GcBusinessOpportunityWebView>>
     **/
    @PostMapping("sendGcPageList")
    public WebResult<WebPageInfo<GcBusinessOpportunityWebView>> sendGcPageList(@RequestBody CeQueryGcBusinessOpportunityPageWebDto ceQueryGcBusinessOpportunityPageWebDto) {
        CeQueryGcBusinessOpportunityPageBusinessDto ceQueryGcBusinessOpportunityPageBusinessDto = CglibUtil.copy(ceQueryGcBusinessOpportunityPageWebDto, CeQueryGcBusinessOpportunityPageBusinessDto.class);
        ceQueryGcBusinessOpportunityPageBusinessDto.setOperatorPosition(ceQueryGcBusinessOpportunityPageWebDto.getLoginPosition());
        ceQueryGcBusinessOpportunityPageBusinessDto.setOperatorSubId(ceQueryGcBusinessOpportunityPageWebDto.getLoginSubId());
        ceQueryGcBusinessOpportunityPageBusinessDto.setOperatorDeptId(ceQueryGcBusinessOpportunityPageWebDto.getLoginOrgId());
        ceQueryGcBusinessOpportunityPageBusinessDto.setOperator(ceQueryGcBusinessOpportunityPageWebDto.getLoginEmployeeId());
        WebPageInfo<GcBusinessOpportunityWebView> gcBusinessOpportunityWebViewWebPageInfo = WebPageInfo.pageConversion(gcBusinessOpportunityBusiness.sendGcList(ceQueryGcBusinessOpportunityPageBusinessDto), GcBusinessOpportunityWebView.class);
        gcBusinessOpportunityWebViewWebPageInfo.getList().forEach(this::packageGcBusinessData);
        return WebResult.success(gcBusinessOpportunityWebViewWebPageInfo);
    }

    /**
     * 申请合作分页列表查询
     * @param ceQueryGcBusinessOpportunityPageWebDto  申请合作分页参数
     * <AUTHOR>
     * @date 2024/5/28 下午3:29
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo < com.ce.scrm.center.web.entity.view.GcBusinessOpportunityWebView>>
     **/
    @PostMapping("applyCooperationPageList")
    public WebResult<WebPageInfo<GcBusinessOpportunityWebView>> applyCooperationPageList(@RequestBody CeQueryGcBusinessOpportunityPageWebDto ceQueryGcBusinessOpportunityPageWebDto) {
        CeQueryGcBusinessOpportunityPageBusinessDto ceQueryGcBusinessOpportunityPageBusinessDto = CglibUtil.copy(ceQueryGcBusinessOpportunityPageWebDto, CeQueryGcBusinessOpportunityPageBusinessDto.class);
        ceQueryGcBusinessOpportunityPageBusinessDto.setOperator(ceQueryGcBusinessOpportunityPageWebDto.getLoginEmployeeId());
        WebPageInfo<GcBusinessOpportunityWebView> gcBusinessOpportunityWebViewWebPageInfo = WebPageInfo.pageConversion(gcBusinessOpportunityBusiness.applyCooperationPageList(ceQueryGcBusinessOpportunityPageBusinessDto), GcBusinessOpportunityWebView.class);
        gcBusinessOpportunityWebViewWebPageInfo.getList().forEach(this::packageGcBusinessData);
        return WebResult.success(gcBusinessOpportunityWebViewWebPageInfo);
    }

    /**
     * 确认高呈商机合作
     * @param gcBusinessOpportunityOperateWebDto    确认合作参数
     * <AUTHOR>
     * @date 2024/5/28 下午5:05
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("confirmCooperation")
    public WebResult<Boolean> confirmCooperation(@Validated @RequestBody GcBusinessOpportunityOperateWebDto gcBusinessOpportunityOperateWebDto) {
        Optional<String> confirmCooperationOptional = gcBusinessOpportunityBusiness.confirmCooperation(gcBusinessOpportunityOperateWebDto.packageOperateParam());
        return confirmCooperationOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * 拒绝高呈商机合作
     * @param gcBusinessOpportunityOperateWebDto    拒绝合作参数
     * <AUTHOR>
     * @date 2024/5/28 下午5:05
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("refuseCooperation")
    public WebResult<Boolean> refuseCooperation(@Validated @RequestBody GcBusinessOpportunityOperateWebDto gcBusinessOpportunityOperateWebDto) {
        Optional<String> refuseCooperationOptional = gcBusinessOpportunityBusiness.refuseCooperation(gcBusinessOpportunityOperateWebDto.packageOperateParam());
        return refuseCooperationOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * 所有数据分页列表查询
     * @param ceQueryGcBusinessOpportunityPageWebDto  分页参数
     * <AUTHOR>
     * @date 2024/5/28 下午3:29
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo < com.ce.scrm.center.web.entity.view.GcBusinessOpportunityWebView>>
     **/
    @PostMapping("allPageList")
    public WebResult<WebPageInfo<GcBusinessOpportunityWebView>> allPageList(@RequestBody CeQueryGcBusinessOpportunityPageWebDto ceQueryGcBusinessOpportunityPageWebDto) {
        CeQueryGcBusinessOpportunityPageBusinessDto ceQueryGcBusinessOpportunityPageBusinessDto = CglibUtil.copy(ceQueryGcBusinessOpportunityPageWebDto, CeQueryGcBusinessOpportunityPageBusinessDto.class);
        ceQueryGcBusinessOpportunityPageBusinessDto.setOperator(ceQueryGcBusinessOpportunityPageWebDto.getLoginEmployeeId());
        WebPageInfo<GcBusinessOpportunityWebView> gcBusinessOpportunityWebViewWebPageInfo = WebPageInfo.pageConversion(gcBusinessOpportunityBusiness.allPageList(ceQueryGcBusinessOpportunityPageBusinessDto), GcBusinessOpportunityWebView.class);
        gcBusinessOpportunityWebViewWebPageInfo.getList().forEach(this::packageGcBusinessData);
        return WebResult.success(gcBusinessOpportunityWebViewWebPageInfo);
    }
}