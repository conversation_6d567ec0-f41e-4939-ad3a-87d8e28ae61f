package com.ce.scrm.center.web.util.gptstreamutil;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageContent {

    private String role;

    private String content;

    private List<ImageContent> imageContent;

    public MessageContent(String role, String content) {
        this.role = role;
        this.content = content;
    }

}
