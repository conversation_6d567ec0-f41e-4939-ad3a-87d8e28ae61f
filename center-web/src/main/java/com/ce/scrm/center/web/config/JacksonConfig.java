package com.ce.scrm.center.web.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 序列化和反序列化配置
 * <AUTHOR>
 * @date 2024/5/23 下午3:40
 * @version 1.0.0
 **/
@Configuration
public class JacksonConfig {

    /**
     * 添加自定义序列化和反序列化转换器
     * <AUTHOR>
     * @date 2024/5/23 下午3:41
     * @return com.fasterxml.jackson.databind.ObjectMapper
     **/
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        //java8时间处理
        module.addSerializer(LocalDateTime.class, CustomLocalDateTimeSerializer.INSTANCE);
        module.addDeserializer(LocalDateTime.class, CustomLocalDateTimeDeserializer.INSTANCE);
        module.addSerializer(Date.class, new DateSerializer(false, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
        //返回值Long转String处理
        module.addSerializer(Long.class, CustomToStringSerializer.INSTANCE);
        module.addSerializer(Long.TYPE, CustomToStringSerializer.INSTANCE);
        module.addSerializer(long.class, CustomToStringSerializer.INSTANCE);
        mapper.registerModule(module);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return mapper;
    }
}
