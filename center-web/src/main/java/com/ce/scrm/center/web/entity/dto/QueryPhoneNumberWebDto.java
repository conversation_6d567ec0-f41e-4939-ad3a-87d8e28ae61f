package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 查询商机手机号
 * @Author: lijinpeng
 * @Date: 2025/4/11 17:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryPhoneNumberWebDto extends LoginInfo implements Serializable {

    private String sjCode;

    private String busiOppoId;

}
