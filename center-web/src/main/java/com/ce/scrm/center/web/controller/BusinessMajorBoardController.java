package com.ce.scrm.center.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.ZqCustomerFollowStageSummary;
import com.ce.scrm.center.dao.entity.ZqPerformancePredictSummary;
import com.ce.scrm.center.service.business.BusinessMajorBoardBusiness;
import com.ce.scrm.center.service.business.entity.dto.BusinessMajorBoardBusinessDto;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.BusinessMajorBoardWebDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.BusinessMajorBoardWebView;
import com.ce.scrm.center.web.entity.view.BusinessMajorBoardStageBoardWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/businessMajor")
@Login
public class BusinessMajorBoardController {

    @Resource
    private BusinessMajorBoardBusiness businessMajorBoardBusiness;

    /**
     * 总监看板-阶段汇总
     * @param businessMajorBoardWebDto
     * @return
     */
    @PostMapping("stageBoard")
    public WebResult<WebPageInfo<BusinessMajorBoardStageBoardWebView>> stageBoard(@RequestBody BusinessMajorBoardWebDto businessMajorBoardWebDto) {
        BusinessMajorBoardBusinessDto businessMajorBoardBusinessDto = BeanCopyUtils.convertToVo(businessMajorBoardWebDto, BusinessMajorBoardBusinessDto.class);
        Page<ZqCustomerFollowStageSummary> resultBusiness = businessMajorBoardBusiness.stageBoard(businessMajorBoardBusinessDto);

        WebPageInfo<BusinessMajorBoardStageBoardWebView> businessMajorBoardWebViewWebPageInfo = WebPageInfo.pageConversion(resultBusiness, BusinessMajorBoardStageBoardWebView.class);
        return WebResult.success(businessMajorBoardWebViewWebPageInfo);
    }

    /**
     * 总监看板-业绩
     * @param
     * @return
     */
    @PostMapping("board")
    public WebResult<WebPageInfo<BusinessMajorBoardWebView>> board(@RequestBody BusinessMajorBoardWebDto businessMajorBoardWebDto) {
        BusinessMajorBoardBusinessDto businessMajorBoardBusinessDto = BeanCopyUtils.convertToVo(businessMajorBoardWebDto, BusinessMajorBoardBusinessDto.class);
        Page<ZqPerformancePredictSummary> resultBusiness = businessMajorBoardBusiness.board(businessMajorBoardBusinessDto);

        WebPageInfo<BusinessMajorBoardWebView> businessMajorBoardWebViewWebPageInfo = WebPageInfo.pageConversion(resultBusiness, BusinessMajorBoardWebView.class);
        return WebResult.success(businessMajorBoardWebViewWebPageInfo);
    }

}
