package com.ce.scrm.center.web.controller.follow;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.FollowBusiness;
import com.ce.scrm.center.service.business.entity.dto.follow.CustomerFollowBusinessCreateDto;
import com.ce.scrm.center.service.business.entity.dto.follow.QueryBusinessMonthBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.follow.QueryFollowDetailListBusinessDto;
import com.ce.scrm.center.service.business.entity.response.BusinessResult;
import com.ce.scrm.center.service.business.entity.view.BusinessMonthBusinessView;
import com.ce.scrm.center.service.business.entity.view.FollowProductInfoBusinessView;
import com.ce.scrm.center.service.business.entity.view.follow.FollowDetailBusinessView;
import com.ce.scrm.center.service.enums.FollowCallStatusEnum;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.entity.dto.follow.CustomerFollowCreateVo;
import com.ce.scrm.center.web.entity.dto.follow.QueryBusinessMonthWebDto;
import com.ce.scrm.center.web.entity.dto.follow.QueryFollowDetailListWebDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.BusinessMonthWebView;
import com.ce.scrm.center.web.entity.view.EnumWebView;
import com.ce.scrm.center.web.entity.view.FollowProductInfoWebView;
import com.ce.scrm.center.web.entity.view.follow.FollowDetailWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/follow")
@Login
public class FollowController {

    @Resource
    private FollowBusiness followBusiness;

    /**
     * 写跟进-意向产品的产品列表
     * @param loginInfo
     * @return
     */
    @PostMapping("getProductInfoEnum")
    public WebResult<List<FollowProductInfoWebView>> getProductInfoEnum(@RequestBody LoginInfo loginInfo) {
        List<FollowProductInfoBusinessView> businessResult  = followBusiness.getProductInfoEnum();
        List<FollowProductInfoWebView> followProductInfoWebViews = BeanUtil.copyToList(businessResult, FollowProductInfoWebView.class);
        for (FollowProductInfoWebView item : followProductInfoWebViews) {
            item.setChildren(BeanUtil.copyToList(item.getChildren(), FollowProductInfoWebView.class));
        }
        return WebResult.success(followProductInfoWebViews);
    }

    /**
     * 查询跟进阶段明细
     * @param queryWebDto
     * @return
     */
    @PostMapping("getFollowDetailList")
    public WebResult<WebPageInfo<FollowDetailWebView>> getFollowDetailList(@RequestBody QueryFollowDetailListWebDto queryWebDto) {
        QueryFollowDetailListBusinessDto queryBusinessDto = BeanUtil.copyProperties(queryWebDto, QueryFollowDetailListBusinessDto.class);
        Page<FollowDetailBusinessView> businessResult = followBusiness.getFollowDetailList(queryBusinessDto);
        WebPageInfo<FollowDetailWebView> webResult = WebPageInfo.pageConversion(businessResult, FollowDetailWebView.class);
        return WebResult.success(webResult);
    }

    @PostMapping("getBusinessMonthInfoByName")
    public WebResult<BusinessMonthWebView> getBusinessMonthInfoByName(@RequestBody QueryBusinessMonthWebDto queryWebDto) {
        QueryBusinessMonthBusinessDto queryBusinessDto = BeanUtil.copyProperties(queryWebDto, QueryBusinessMonthBusinessDto.class);
        BusinessMonthBusinessView businessMonthInfoByName = followBusiness.getBusinessMonthInfoByName(queryBusinessDto);
        BusinessMonthWebView businessMonthWebView = BeanUtil.copyProperties(businessMonthInfoByName, BusinessMonthWebView.class);
        return WebResult.success(businessMonthWebView);
    }


    /***
     * TODO 
     * @param loginInfo
     * <AUTHOR>
     * @date 2025/8/9 23:45
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.util.List<com.ce.scrm.center.web.entity.view.EnumWebView>>
    **/
    @PostMapping("getFollowCallStatusEnum")
    public WebResult<List<EnumWebView>> getFollowCallStatusEnum(@RequestBody LoginInfo loginInfo) {
        List<EnumWebView> result = new ArrayList<>();
        for (FollowCallStatusEnum followCallStatusEnum : FollowCallStatusEnum.values()) {
            EnumWebView item = new EnumWebView();
            item.setValue(followCallStatusEnum.getValue());
            item.setLabel(followCallStatusEnum.getLabel());
            result.add(item);
        }
        return WebResult.success(result);
    }

    /**
     * sdr 写跟进
     * 创建跟进
     * @param customerFollowCreateVo
     * @return
     */
    @PostMapping("/sdr/create")
    public WebResult<Boolean> create(@RequestBody CustomerFollowCreateVo customerFollowCreateVo) {
        log.info("创建跟进,参数={}", JSONObject.toJSONString(customerFollowCreateVo));
        CustomerFollowBusinessCreateDto customerFollowBusinessCreateDto = BeanUtil.copyProperties(customerFollowCreateVo, CustomerFollowBusinessCreateDto.class);
        BusinessResult<Boolean> booleanBusinessResult = followBusiness.create(customerFollowBusinessCreateDto);
        if (booleanBusinessResult.checkSuccess()){
           return WebResult.success(booleanBusinessResult.getData(),booleanBusinessResult.getMsg());
        }else{
           return WebResult.error(booleanBusinessResult.getCode(),booleanBusinessResult.getMsg());
        }
    }
}
