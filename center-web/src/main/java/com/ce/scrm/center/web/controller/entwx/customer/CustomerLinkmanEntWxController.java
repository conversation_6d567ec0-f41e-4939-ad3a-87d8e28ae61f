package com.ce.scrm.center.web.controller.entwx.customer;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.service.business.CustomerLinkmanBusiness;
import com.ce.scrm.center.service.business.entity.dto.CustomerLinkmanBusinessByCustomerIdDto;
import com.ce.scrm.center.service.business.entity.view.customer.CustomerLinkmanBusinessView;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginType;
import com.ce.scrm.center.web.entity.dto.customer.CustomerLinkmanListByCustomerIdWebDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.CustomerLinkmanView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @classname CustomerLinkmanController
 * @description 客户业务联系人管理
 * @date 2025/1/20 16:22
 * @create by gaomeijing
 */
@Slf4j
@RestController
@Login(loginType = LoginType.ENT_WECHAT)
@RequestMapping("entwx/linkman")
public class CustomerLinkmanEntWxController {

    @Resource
    private CustomerLinkmanBusiness customerLinkmanBusiness;

    /**
     * @Description 获取客户联系人列表
     * @return WebResult<List<CustomerLinkmanView>>
     */
    @PostMapping("getCustomerLinkmanListByCustomerId")
    public WebResult<List<CustomerLinkmanView>> getCustomerLinkmanListByCustomerId(@RequestBody CustomerLinkmanListByCustomerIdWebDto customerLinkmanListByCustomerIdWebDto) {
        String customerId = customerLinkmanListByCustomerIdWebDto.getCustomerId();
        CustomerLinkmanBusinessByCustomerIdDto customerLinkmanBusinessByCustomerIdDto = new CustomerLinkmanBusinessByCustomerIdDto();
        customerLinkmanBusinessByCustomerIdDto.setCustomerId(customerId);

        List<CustomerLinkmanBusinessView> resultList = customerLinkmanBusiness.getCustomerLinkmanBusinessViewList(customerLinkmanBusinessByCustomerIdDto);
        List<CustomerLinkmanView> customerLinkmanList = BeanUtil.copyToList(resultList, CustomerLinkmanView.class);

        return WebResult.success(customerLinkmanList);
    }

}