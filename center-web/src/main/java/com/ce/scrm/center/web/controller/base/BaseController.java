package com.ce.scrm.center.web.controller.base;

import cn.ce.cesupport.enums.GcSjInitiatorEnum;
import cn.ce.cesupport.enums.GcSjSourceEnum;
import cn.ce.cesupport.enums.GcSjStateEnum;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.business.entity.view.SmaDictionaryItemListView;
import com.ce.scrm.center.service.cache.SmaDictionaryCacheHandler;
import com.ce.scrm.center.service.enums.*;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.entity.view.FileDataThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.FtpFileThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.util.constant.UtilConstant;
import com.ce.scrm.center.web.entity.view.CirculationLossWebView;
import com.ce.scrm.center.web.entity.view.ConvertLogWebView;
import com.ce.scrm.center.web.entity.view.GcBusinessOpportunityWebView;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用处理
 * <AUTHOR>
 * @date 2024/5/28 下午5:35
 * @version 1.0.0
 */
@Component
public class BaseController {

    @Resource
    private FtpFileThirdService ftpFileThirdService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private SmaDictionaryCacheHandler smaDictionaryCacheHandler;

    @Resource
    private OrgThirdService orgThirdService;


    /**
     * 拼装商机数据
     * @param gcBusinessOpportunityWebView  商机数据
     * <AUTHOR>
     * @date 2024/5/27 下午4:41
     **/
    protected void packageGcBusinessData(GcBusinessOpportunityWebView gcBusinessOpportunityWebView) {
        GcSjSourceEnum sourceEnum = GcSjSourceEnum.get(gcBusinessOpportunityWebView.getSource());
        if (sourceEnum != null) {
            gcBusinessOpportunityWebView.setSourceName(sourceEnum.getSourceName());
        }
        GcSjStateEnum stateEnum = GcSjStateEnum.get(gcBusinessOpportunityWebView.getState());
        if (stateEnum != null) {
            gcBusinessOpportunityWebView.setStateName(stateEnum.getStateName());
        }
        GcSjLevelEnum levelEnum = GcSjLevelEnum.get(gcBusinessOpportunityWebView.getLevel());
        if (levelEnum != null) {
            gcBusinessOpportunityWebView.setLevelName(levelEnum.getLevelName());
        }
        GcSjRequirementEnum gcSjRequirementEnum = GcSjRequirementEnum.get(gcBusinessOpportunityWebView.getRequirementType());
        if (gcSjRequirementEnum != null) {
            gcBusinessOpportunityWebView.setRequirementName(gcSjRequirementEnum.getRequirementName());
        }
        GcSjInitiatorEnum gcSjInitiatorEnum = GcSjInitiatorEnum.get(gcBusinessOpportunityWebView.getInitiator());
        if (gcSjInitiatorEnum != null) {
            gcBusinessOpportunityWebView.setInitiatorName(gcSjInitiatorEnum.getInitiatorName());
        }
        // 商务的电话、邮箱
        String salerId = gcBusinessOpportunityWebView.getSalerId();
        if (StrUtil.isNotBlank(salerId)) {
            Optional<EmployeeDataThirdView> salerData = employeeThirdService.getEmployeeData(salerId);
            if (salerData.isPresent()) {
                EmployeeDataThirdView view = salerData.get();
                gcBusinessOpportunityWebView.setSalerPhone(view.getMobile());
                gcBusinessOpportunityWebView.setSalerEmail(view.getWorkMail());
            }
        }
        // 高呈商务的电话、邮箱
        String gcSalerId = gcBusinessOpportunityWebView.getGcSalerId();
        if (StrUtil.isNotBlank(gcSalerId)) {
            Optional<EmployeeDataThirdView> gcSalerData = employeeThirdService.getEmployeeData(gcSalerId);
            if (gcSalerData.isPresent()) {
                EmployeeDataThirdView view = gcSalerData.get();
                gcBusinessOpportunityWebView.setGcSalerPhone(view.getMobile());
                gcBusinessOpportunityWebView.setGcSalerEmail(view.getWorkMail());
            }
        }
        // 当前保护商务的电话、邮箱、部门名称、分司名称、区域名称
        Boolean showCurrentInfo = gcBusinessOpportunityWebView.getShowCurrentInfo();
        String customerId = gcBusinessOpportunityWebView.getCustomerId();
        if (Objects.equals(true, showCurrentInfo) && StrUtil.isNotBlank(customerId)) {
            CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, customerId).eq(CmCustProtect::getStatus, ProtectStateEnum.PROTECT.getState()).one();
            if (cmCustProtect != null) {
                employeeThirdService.getEmployeeData(cmCustProtect.getSalerId()).ifPresent(employeeDataThirdView -> {
                    gcBusinessOpportunityWebView.setCurrentSalerId(cmCustProtect.getSalerId());
                    gcBusinessOpportunityWebView.setCurrentSalerName(employeeDataThirdView.getName());
                    gcBusinessOpportunityWebView.setCurrentDeptId(employeeDataThirdView.getOrgId());
                    gcBusinessOpportunityWebView.setCurrentDeptName(employeeDataThirdView.getOrgName());
                    gcBusinessOpportunityWebView.setCurrentSubId(employeeDataThirdView.getSubId());
                    gcBusinessOpportunityWebView.setCurrentSubName(employeeDataThirdView.getSubName());
                    gcBusinessOpportunityWebView.setCurrentAreaId(employeeDataThirdView.getAreaId());
                    gcBusinessOpportunityWebView.setCurrentAreaName(employeeDataThirdView.getAreaName());
                });
            }
        }
        List<String> fileIdList;
        List<FileDataThirdView> fileDataThirdViewList;
        if (StrUtil.isNotBlank(gcBusinessOpportunityWebView.getAttachmentIds()) && CollectionUtil.isNotEmpty(fileIdList = Arrays.stream(gcBusinessOpportunityWebView.getAttachmentIds().split(UtilConstant.DATA_SEPARATOR)).filter(StrUtil::isNotBlank).collect(Collectors.toList())) && CollectionUtil.isNotEmpty(fileDataThirdViewList = ftpFileThirdService.get(fileIdList))) {
            gcBusinessOpportunityWebView.setFileDataList(fileDataThirdViewList.stream().map(fileDataThirdView -> {
                GcBusinessOpportunityWebView.FileData fileData = new GcBusinessOpportunityWebView.FileData();
                fileData.setFileId(fileDataThirdView.getId());
                fileData.setFileName(fileDataThirdView.getFileName());
                fileData.setFileUrl(fileDataThirdView.getFileUrl());
                return fileData;
            }).collect(Collectors.toList()));
        }
    }

    /**
     * Description: 拼装商务人员信息
     * @author: JiuDD
     * @param view
     * @return void
     * date: 2024/7/11 16:20
     */
    protected void packageSalerData(ConvertLogWebView view) {
        if (StrUtil.isNotBlank(view.getSalerId())) {
            employeeThirdService.getEmployeeData(view.getSalerId()).ifPresent(saler -> view.setSalerName(saler.getName()));
        }
        if (StrUtil.isNotBlank(view.getDeptOfSalerId())) {
            employeeThirdService.getOrgLeader(view.getDeptOfSalerId()).ifPresent(orgLeader -> view.setManagerName(orgLeader.getName()));
        }
        if (StrUtil.isNotBlank(view.getReleaseReason())) {
            SmaDictionaryItemListView listThirdView = smaDictionaryCacheHandler.get("DICT_RELEASE_REASON");
            if (Objects.nonNull(listThirdView)) {
                listThirdView.getList().stream().filter(item -> item.getCode().equals(view.getReleaseReason())).findFirst().ifPresent(item -> view.setReleaseReason(item.getName()));
            }
        }
    }

    /**
     * Description: 拼装流转流失信息
     * @author: JiuDD
     * @param view
     * @return void
     * date: 2024/7/22 14:15
     */
    protected void packageCirculationLossData(CirculationLossWebView view) {
        /*
        List<String> orgIdList = Arrays.asList(view.getAreaId(), view.getSubId(), view.getDeptId());
        Map<String, OrgDataThirdView> orgDataThirdViewMap = orgThirdService.getOrgData(orgIdList);
        view.setAreaName(orgDataThirdViewMap.getOrDefault(view.getAreaId(), new OrgDataThirdView()).getName());
        view.setSubName(orgDataThirdViewMap.getOrDefault(view.getSubId(), new OrgDataThirdView()).getName());
        view.setDeptName(orgDataThirdViewMap.getOrDefault(view.getDeptId(), new OrgDataThirdView()).getName());
        view.setSalerName(employeeThirdService.getEmployeeData(view.getSalerId()).orElse(new EmployeeDataThirdView()).getName());
        */
        // todo 暂时这样处理 vip客户 +9个月待流转显示时间
//        if (Objects.equals(view.getCustomerLayer(), CustomerLayerEnum.VIP.getCode())) {
//            Date preDate = view.getPreDate();
//            if (preDate != null) {
//                // 将Date转换为LocalDate
//                LocalDate localDate = preDate.toInstant()
//                        .atZone(ZoneId.systemDefault())
//                        .toLocalDate();
//                // 添加9个月
//                LocalDate newLocalDate = localDate.plusMonths(9);
//                Date newDate = Date.from(newLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
//                view.setPreDate(newDate);
//            }
//        }
        if (Objects.nonNull(view.getPreDate())) {
            long betweenMs = view.getPreDate().getTime()- new Date().getTime();
            long days = betweenMs / DateUnit.DAY.getMillis();
            view.setCirculationSurplusTime(days+"天");
        }
        view.setCustomerLayerStr(CustomerLayerEnum.getDescriptionByCode(view.getCustomerLayer()));
        view.setLastReachTypeStr(LastReachTypeEnum.getDescriptionByCode(view.getLastReachType()));
    }
}