package com.ce.scrm.center.web.enums;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:45:58
 * @desc 客户外贸属性筛选项
 */
public enum CustomerForeignTradeFilterEnum implements EnumBase {

    CFTF0("0", "全部"),
    CFTF1("tag10027", "海关活跃"),
    CFTF2("tag10028", "外贸招聘"),
    CFTF3("tag10038", "报关企业"),
    CFTF4("tag10078", "跨境报备"),
    CFTF5("tag10040", "海关认证"),
    CFTF6("tag10041", "有进出口信用"),
    CFTF7("tag10042", "进出口收发货人"),
    CFTF8("tag10043", "国际专利"),
    CFTF9("tag10044", "有英文名"),
    CFTF10("tag10037", "有官网")
    ;

    @Getter
    private final String label;
    @Getter
    private final String value;

    // Getter 方法
    public String getKey() {
        return String.valueOf(label);
    }

    // 构造函数
    CustomerForeignTradeFilterEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }
}
