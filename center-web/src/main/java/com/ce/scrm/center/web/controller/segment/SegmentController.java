package com.ce.scrm.center.web.controller.segment;

import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.ce.cesupport.enums.segment.SegmentStatusEnum;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.Segment;
import com.ce.scrm.center.dao.entity.SegmentDetail;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.SegmentBusiness;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.segment.*;
import com.ce.scrm.center.service.business.entity.view.segment.*;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.*;
import com.ce.scrm.center.web.entity.dto.segment.*;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.*;
import com.ce.scrm.center.web.entity.view.segment.QueryAreaSegmentWebView;
import com.ce.scrm.center.web.entity.view.segment.QueryRoleSegmentWebView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * @version 1.0
 * @Description: cdp分群下发
 * @Author: lijinpeng
 * @Date: 2025/2/27 11:55
 */
@Slf4j
@RestController
@RequestMapping("/segment")
@Login
public class SegmentController {

    @Resource
    private SegmentBusiness segmentBusiness;

    @Resource
    private RocketMqOperate rocketMqOperate;
	@Resource
	private EmployeeInfoBusiness employeeInfoBusiness;


    /***
     * 总部分群下发
     * @param segmentDistributeWebDto
     * <AUTHOR>
     * @date 2025/2/27 13:59
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
    **/
    @PostMapping("/distribute")
    public WebResult<Boolean> distribute(@RequestBody SegmentDistributeWebDto segmentDistributeWebDto) {
        //判断是否是总部角色
        //判断分群ID是否存在
        if (Objects.isNull(segmentDistributeWebDto) || Objects.isNull(segmentDistributeWebDto.getSegmentId())){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
        Boolean result = segmentBusiness.distribute(segmentDistributeWebDto.getSegmentId());
        return WebResult.success(result);
    }


    /***
     * 分配
     * @param segmentAssignmentWebDto
     * <AUTHOR>
     * @date 2025/2/27 14:29
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
    **/
    @PostMapping("/assignment")
    public WebResult<Boolean> assignment(@RequestBody SegmentAssignmentWebDto segmentAssignmentWebDto) {
        //参数校验
        if (Objects.isNull(segmentAssignmentWebDto) || Objects.isNull(segmentAssignmentWebDto.getSegmentId())
                || CollectionUtils.isEmpty(segmentAssignmentWebDto.getCustomerIds()) || Objects.isNull(segmentAssignmentWebDto.getAssignmentRoleType())
                || Objects.isNull(segmentAssignmentWebDto.getAssignmentType())){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
        SegmentAssignmentBusinessDto segmentAssignmentBusinessDto = BeanUtil.copyProperties(segmentAssignmentWebDto, SegmentAssignmentBusinessDto.class);
        segmentBusiness.assignment(segmentAssignmentBusinessDto);
        return WebResult.success(true);
    }

    /*
     * @Description 根据区域查询分群信息
     * <AUTHOR>
     * @date 2025/2/27 14:33
     * @param queryAreaSegmentWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.segment.QueryAreaSegmentWebView>
     */
    @PostMapping("getSegmentPageByAreaId")
    public WebResult<WebPageInfo<QueryAreaSegmentWebView>> getSegmentPageByAreaId(@RequestBody @Valid QueryAreaSegmentWebDto queryAreaSegmentWebDto) {
        QueryAreaSegmentBusinessDto queryAreaSegmentBusinessDto = BeanUtil.copyProperties(queryAreaSegmentWebDto, QueryAreaSegmentBusinessDto.class);
        Page<QueryAreaSegmentBusinessView> queryAreaSegmentBusinessViewPage = segmentBusiness.getSegmentPageByAreaId(queryAreaSegmentBusinessDto);
        WebPageInfo<QueryAreaSegmentWebView> queryAreaSegmentWebViewWebPageInfo = WebPageInfo.pageConversion(queryAreaSegmentBusinessViewPage, QueryAreaSegmentWebView.class);
        return WebResult.success(queryAreaSegmentWebViewWebPageInfo);
    }

    /*
     * @Description 根据角色查询分群信息
     * <AUTHOR>
     * @date 2025/2/27 14:33
     * @param queryRoleSegmentWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.segment.QueryRoleSegmentWebView>
     */
    @PostMapping("getSegmentPageByRole")
    public WebResult<WebPageInfo<QueryRoleSegmentWebView>> getSegmentPageByRole(@RequestBody @Valid QueryRoleSegmentWebDto queryRoleSegmentWebDto) {
        QueryRoleSegmentBusinessDto queryRoleSegmentBusinessDto = BeanUtil.copyProperties(queryRoleSegmentWebDto, QueryRoleSegmentBusinessDto.class);
        Page<QueryRoleSegmentBusinessView> queryRoleSegmentBusinessViewPage = segmentBusiness.getSegmentPageByRole(queryRoleSegmentBusinessDto);
        WebPageInfo<QueryRoleSegmentWebView> queryRoleSegmentWebViewWebPageInfo = WebPageInfo.pageConversion(queryRoleSegmentBusinessViewPage, QueryRoleSegmentWebView.class);
        return WebResult.success(queryRoleSegmentWebViewWebPageInfo);
    }

    /*
     * @Description 根据id 修改商务标注
     * <AUTHOR>
     * @date 2025/2/27 14:41
     * @param updateCustomTagWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     */
    @PostMapping("updateSalerCustomTagById")
    public WebResult<Boolean> updateSalerCustomTagById(@RequestBody @Valid UpdateCustomTagWebDto updateCustomTagWebDto) {
		UpdateCustomTagBusinessDto updateCustomTagBusinessDto = BeanUtil.copyProperties(updateCustomTagWebDto, UpdateCustomTagBusinessDto.class);
		Boolean result = segmentBusiness.updateSalerCustomTagById(updateCustomTagBusinessDto);
        return WebResult.success(result);
    }

	/*
	 * @Description 专项线索追踪
	 * <AUTHOR>
	 * @date 2025/2/28 10:31
	 * @param trackClueSegmentPageWebDto
	 * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo<com.ce.scrm.center.web.entity.view.TrackClueSegmentWebView>>
	 */
	@PostMapping("trackClueList")
	public WebResult<WebPageInfo<TrackClueSegmentWebView>> trackClueList(@RequestBody TrackClueSegmentPageWebDto trackClueSegmentPageWebDto) {
		TrackClueSegmentPageBusinessDto trackClueSegmentPageBusinessDto = BeanUtil.copyProperties(trackClueSegmentPageWebDto, TrackClueSegmentPageBusinessDto.class);
		Page<TrackClueSegmentBusinessView> result = segmentBusiness.trackClueList(trackClueSegmentPageBusinessDto);
		WebPageInfo<TrackClueSegmentWebView> trackClueSegmentWebViewWebPageInfo = WebPageInfo.pageConversion(result, TrackClueSegmentWebView.class);
		return WebResult.success(trackClueSegmentWebViewWebPageInfo);
	}


	/**
	 * @Description 拉群 | 编辑群信息
	 * <AUTHOR>
	 * @date 2025/2/27 17:41
	 * @param pullGroupSegmentWebDto 拉群 ｜ 编辑 入参
	 * @return {@link WebResult}
	 */
	@PostMapping("pullGroup")
	public WebResult<Boolean> pullGroup(@RequestBody @Valid PullGroupSegmentWebDto pullGroupSegmentWebDto) {
		if ((segmentBusiness.detail(pullGroupSegmentWebDto.getSegmentId()) != null) && Objects.isNull(pullGroupSegmentWebDto.getId())) {
			return WebResult.error("100001", pullGroupSegmentWebDto.getSegmentId() + "群已存在");
		}
		PullGroupSegmentBusinessDto pullGroupSegmentBusinessDto = BeanCopyUtils.convertToVo(pullGroupSegmentWebDto, PullGroupSegmentBusinessDto.class);
		Boolean saveOrUpdateResult = segmentBusiness.pullGroup(pullGroupSegmentBusinessDto);
		return WebResult.success(saveOrUpdateResult);
	}


	/**
	 * @Description 分群移除
	 * <AUTHOR>
	 * @date 2025/2/27 17:41
	 * @param removeGroupSegmentWebDto 分群移除入参
	 * @return {@link WebResult}
	 */
	@PostMapping("removeGroup")
	public WebResult<Boolean> removeGroup(@RequestBody @Valid RemoveGroupSegmentWebDto removeGroupSegmentWebDto) {
		RemoveGroupSegmentBusinessDto removeGroupSegmentBusinessDto = BeanCopyUtils.convertToVo(removeGroupSegmentWebDto, RemoveGroupSegmentBusinessDto.class);
		Segment segment = segmentBusiness.detail(removeGroupSegmentBusinessDto.getSegmentId());
		if (segment == null) {
			return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
		}
		EmployeeInfoBusinessDto currentUser = employeeInfoBusiness.getEmployeeInfoByEmpId(removeGroupSegmentWebDto.getLoginEmployeeId());
		boolean errStatus = !Objects.equals(segment.getSegmentStatus(), SegmentStatusEnum.NOT_DISTRIBUTE.getCode());
		boolean errCenterRole = !Objects.equals(currentUser.getIsZbReport(), YesOrNoEnum.YES.getCode());
		if (errStatus || errCenterRole) {
			return WebResult.error(WebCodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
		}
		// 只有总部角色且未下发可以移除
		Boolean removeGroupResult = segmentBusiness.removeGroup(removeGroupSegmentBusinessDto);
		return WebResult.success(removeGroupResult);
	}


	/**
	 * @Description 总部专项线索列表-分页
	 * <AUTHOR>
	 * @date 2025/2/27 17:41
	 * @param clueSegmentPageWebDto 专项线索追踪入参
	 * @return {@link WebResult}
	 */
	@PostMapping("clue")
	public WebResult<WebPageInfo<ClueSegmentWebView>> cluePage(@RequestBody ClueSegmentPageWebDto clueSegmentPageWebDto) {
		Page<ClueSegmentBusinessView> clueSegmentBusinessViewPage = segmentBusiness.cluePage(BeanCopyUtils.convertToVo(clueSegmentPageWebDto, ClueSegmentPageBusinessDto.class));
		WebPageInfo<ClueSegmentWebView> clueSegmentWebViewWebPageInfo = WebPageInfo.pageConversion(clueSegmentBusinessViewPage, ClueSegmentWebView.class);
		return WebResult.success(clueSegmentWebViewWebPageInfo);
	}


	/**
	 * @Description 转化看板
	 * <AUTHOR>
	 * @date 2025/2/27 17:41
	 * @param kanbanSegmentWebDto 看板信息入参
	 * @return {@link WebResult}
	 */
	@PostMapping("kanban")
	public WebResult<KanbanSegmentWebView> transformKanban(@RequestBody @Valid KanbanSegmentWebDto kanbanSegmentWebDto) {
		// 总部、区域、分司、事业部、部门 都可查看
		KanbanSegmentBusinessView view = segmentBusiness.kanbanInfo(BeanCopyUtils.convertToVo(kanbanSegmentWebDto, KanbanSegmentBusinessDto.class));
		KanbanSegmentWebView kanbanSegmentWebView = BeanCopyUtils.convertToVo(view, KanbanSegmentWebView.class);
		return WebResult.success(kanbanSegmentWebView);
	}


	/**
	 * @Description 转化看板详情
	 * <AUTHOR>
	 * @date 2025/2/27 17:41
	 * @param kanbanDetailSegmentWebDto 看板详情入参
	 * @return {@link WebResult}
	 */
	@PostMapping("kanbanDetail")
	public WebResult<List<KanbanDetailSegmentWebView>> transformKanbanDetail(@RequestBody @Valid KanbanDetailSegmentWebDto kanbanDetailSegmentWebDto) {
		List<KanbanDetailBusinessView> kanbanDetails = segmentBusiness.getKanbanDetail(BeanCopyUtils.convertToVo(kanbanDetailSegmentWebDto, KanbanDetailSegmentBusinessDto.class));
		return WebResult.success(BeanCopyUtils.convertToVoList(kanbanDetails, KanbanDetailSegmentWebView.class));
	}


	/**
	 * @Description 分群信息
	 * <AUTHOR>
	 * @date 2025/2/27 17:41
	 * @param segmentInfoWebDto 分群信息入参
	 * @return {@link WebResult}
	 */
	@PostMapping("segmentInfo")
	public WebResult<SegmentInfoWebView> segmentInfo(@RequestBody SegmentInfoWebDto segmentInfoWebDto) {
		Segment segment = segmentBusiness.detail(segmentInfoWebDto.getSegmentId());
		if (Objects.isNull(segment)) {
			return WebResult.success(null);
		}
		SegmentInfoWebView segmentInfoWebView = BeanCopyUtils.convertToVo(segment, SegmentInfoWebView.class);
		return WebResult.success(segmentInfoWebView);
	}

	@PostMapping("/segment/init")
	public WebResult<Boolean> segmentInit(@RequestBody SegmentAssignmentWebDto segmentAssignmentWebDto) {
		List<String> customerIds = segmentAssignmentWebDto.getCustomerIds();
		if (CollectionUtils.isNotEmpty(customerIds)){
			for (String customerId : customerIds){
				SegmentDetail segmentDetail = new SegmentDetail();
				segmentDetail.setSegmentId(segmentAssignmentWebDto.getSegmentId());
				segmentDetail.setCustomerId(customerId);
				segmentBusiness.segmentDetailCreate(segmentDetail);
			}
		}
		return WebResult.success(true);
	}

	@PostMapping("/addFavoriteBySegmentDetailId")
	public WebResult<Boolean> addFavoriteBySegmentDetailId(@RequestBody @Valid AddSegmentFavoriteWebDto addSegmentFavoriteWebDto) {
		AddSegmentFavoriteBusinessDto addSegmentFavoriteBusinessDto = BeanCopyUtils.convertToVo(addSegmentFavoriteWebDto, AddSegmentFavoriteBusinessDto.class);
		Boolean result = segmentBusiness.addFavoriteBySegmentDetailId(addSegmentFavoriteBusinessDto);
		return WebResult.success(result);
	}

}
