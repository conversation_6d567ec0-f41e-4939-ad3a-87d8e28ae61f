package com.ce.scrm.center.web.entity.dto.cooperation;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @version 1.0
 * @Description: 同意并发起企业微信审批
 * @Author: lijinpeng
 * @Date: 2024/10/17 15:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AgreeAndApproveWebDto extends LoginInfo {

    /**
     * t_apply_cooperation 合作表id
     */
    @NotNull
    private String applyCooperationId;

}
