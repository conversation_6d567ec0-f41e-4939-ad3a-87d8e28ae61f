package com.ce.scrm.center.web.controller;

import cn.ce.cesupport.emp.service.OrgAppService;
import cn.ce.cesupport.framework.base.vo.MapResultBean;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.controller.base.BaseController;
import com.ce.scrm.center.web.entity.dto.CirculationLossWebPageDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Login
@Slf4j
@RestController
@RequestMapping("org")
// TODO : 测试完成  自动流转到部门后   删除此类
public class OrgController extends BaseController {
    @DubboReference
    private OrgAppService orgAppService;

    @RequestMapping("getDeptAndSalers")
    public WebResult<MapResultBean> getDeptAndSalers(CirculationLossWebPageDto circulationLossWebPageDto) {
        MapResultBean deptAndSalers = orgAppService.getDeptAndSalers(circulationLossWebPageDto.getLoginSubId());
        return WebResult.success(deptAndSalers);
    }
}
