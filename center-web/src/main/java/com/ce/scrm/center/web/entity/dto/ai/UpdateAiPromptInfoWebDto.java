package com.ce.scrm.center.web.entity.dto.ai;

import com.ce.scrm.center.dao.entity.AiPromptAuthInfo;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 更新提示词信息表
 * @Author: lijinpeng
 * @Date: 2025/2/20 10:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateAiPromptInfoWebDto extends LoginInfo implements Serializable {

    @NotNull
    private Long id;

    private String title;

    private String content;

    private Integer startFlag;

    private String remark;

    private Integer deleteFlag;

    private Integer promptType;

    private List<AiPromptAuthInfo> aiPromptAuthInfos;
    /**
     * ce 中企 gboss 跨境
     */
    private String company;
}
