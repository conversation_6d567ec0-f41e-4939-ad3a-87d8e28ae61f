package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * description: 高呈客户查询web参数
 * @author: DD.Jiu
 * date: 2024/5/15.
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GcCustQueryWebDto extends LoginInfo {
    /**
     * 客户ID
     */
    @NotBlank(groups = GcCustProjectList.class, message = "获取高呈商机客户的项目列表，客户ID不能为空")
    private String customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 高呈商务id
     */
    private String gcSalerId;
    /**
     * 高呈商机id
     */
    @NotBlank(groups = GcCustProjectDetail.class, message = "获取高呈商机客户的项目详情，高呈商机ID不能为空")
    private String sjId;
    /**
     * 高呈商机状态，GcSjStateEnum
     */
    private Integer state;
    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 每页数量
     */
    private Integer pageSize = 10;

    /**
     * 高呈商机客户列表查询web参数接口
     */
    public interface GcCustProjectList{}
    /**
     * 高呈商机客户项目详情查询web参数接口
     */
    public interface GcCustProjectDetail{}
}

