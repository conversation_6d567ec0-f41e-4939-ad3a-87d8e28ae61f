package com.ce.scrm.center.web.controller.abm;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ce.scrm.center.service.business.abm.CustomerImportBusiness;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmCustomerTemplateErrorExcel;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmCustomerTemplateExcel;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.entity.dto.abm.AbmCustImportRequestDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ABM客户导入控制器
 * <AUTHOR>
 * @date 2025-07-10
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/abm/customer/import")
public class AbmCustImportController {

	@Resource
	private CustomerImportBusiness customerImportBusiness;

	/**
	 * 线索导入，导入的是绿化后的客户数据
	 * <AUTHOR>
	 */
	@Login
	@PostMapping("/importLeads")
	public WebResult<JSONObject> importLeadsFromExcel(@RequestParam("file") MultipartFile file, @RequestBody LoginInfo loginInfo) {
		if (file == null || file.isEmpty()) {
			return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION.getCode(), "未找到上传文件");
		}
		try {
			log.info("开始解析导入文件，文件名：{}, 文件大小：{} bytes", file.getOriginalFilename(), file.getSize());
			if (file.isEmpty()) {
				return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION.getCode(), "上传文件不能为空");
			}
			String fileName = file.getOriginalFilename();
			if (fileName == null) {
				return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION.getCode(), "文件名不能为空");
			}
			JSONObject result;
			if (fileName.toLowerCase().endsWith(".xlsx") || fileName.toLowerCase().endsWith(".xls")) { // 解析Excel文件
				result = parseExcelFile(file);
			} else if (fileName.toLowerCase().endsWith(".csv")) { // 解析CSV文件
				result = parseCsvFile(file);
			} else {
				return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION.getCode(), "不支持的文件格式，请上传Excel(.xlsx/.xls)或CSV(.csv)文件");
			}

			List<AbmCustomerTemplateErrorExcel> failedList = result.getObject("validFailed", new TypeReference<List<AbmCustomerTemplateErrorExcel>>() {});
			if (!CollectionUtils.isEmpty(failedList)) {
				log.warn("基本格式校验出错，不会导入客户，请修正导入Excel/CSV文件的必填字段校验错误：{}", JSON.toJSONString(failedList));
				return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION.getCode(), "必填项校验错误，请检查后重新上传");
			} else {
				List<AbmCustomerTemplateExcel> successList = result.getObject("validSuccess", new TypeReference<List<AbmCustomerTemplateExcel>>() {});
				if (!CollectionUtils.isEmpty(successList)) { // 格式校验成功，开始导入customer and tag
					String loginEmployeeId = loginInfo.getLoginEmployeeId();
					successList = successList.stream().filter(Objects::nonNull).collect(Collectors.toList());
					String addCustomerErrorUrl = customerImportBusiness.importCustomerAndTags(loginEmployeeId, successList);
					if (StringUtils.isNotBlank(addCustomerErrorUrl)) {
						result.put("errUrl", addCustomerErrorUrl);
					}
				}
			}
			return WebResult.success(result);
		} catch (Exception e) {
			log.error("解析导入文件失败，文件名：{}", file.getOriginalFilename(), e);
			return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION.getCode(), "文件解析失败：" + e.getMessage());
		}
	}

	/**
	 * 解析Excel文件
	 * <AUTHOR>
	 */
	private JSONObject parseExcelFile(MultipartFile file) throws IOException {
		List<AbmCustomerTemplateExcel> successList = new ArrayList<>();
		List<AbmCustomerTemplateErrorExcel> failedList = new ArrayList<>();

		// 使用EasyExcel读取文件
		EasyExcel.read(file.getInputStream())
				.head(AbmCustomerTemplateExcel.class)
				.sheet()
				.headRowNumber(1) // 表头行数
				.registerReadListener(new AnalysisEventListener<AbmCustomerTemplateExcel>() {
					@Override
					public void invoke(AbmCustomerTemplateExcel data, AnalysisContext context) {
						// 验证必填字段
						boolean isValid = validateRequiredFields(data, context.readRowHolder().getRowIndex() + 1);
						if (isValid) {
							successList.add(data);
						} else {
							AbmCustomerTemplateErrorExcel abmCustomerTemplateErrorExcel = BeanCopyUtils.convertToVo(data, AbmCustomerTemplateErrorExcel.class);
							abmCustomerTemplateErrorExcel.setErrorMsg("必填字段校验失败");
							failedList.add(abmCustomerTemplateErrorExcel);
						}
					}
					@Override
					public void doAfterAllAnalysed(AnalysisContext context) {
						log.info("Excel文件解析完成，成功：{}条，失败：{}条", successList.size(), failedList.size());
					}
				})
				.doRead();

		// 构造返回结果
		JSONObject result = new JSONObject();
		result.put("validSuccess", successList);
		result.put("validFailed", failedList);
		return result;
	}

	/**
	 * 解析CSV文件
	 * <AUTHOR>
	 */
	private JSONObject parseCsvFile(MultipartFile file) throws IOException {
		List<AbmCustomerTemplateExcel> successList = new ArrayList<>();
		List<AbmCustomerTemplateErrorExcel> failedList = new ArrayList<>();

		try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
			String line;
			int rowIndex = 0;
			if ((line = reader.readLine()) != null) {
				rowIndex++;
			}
			// 逐行解析数据
			while ((line = reader.readLine()) != null) {
				rowIndex++;
				if (line.trim().isEmpty()) {
					continue; // 跳过空行
				}

				AbmCustomerTemplateExcel customer = parseCsvLine(line, rowIndex);
				if (customer != null) {
					boolean isValid = validateRequiredFields(customer, rowIndex);
					if (isValid) {
						successList.add(customer);
					} else {
						AbmCustomerTemplateErrorExcel abmCustomerTemplateErrorExcel = BeanCopyUtils.convertToVo(customer, AbmCustomerTemplateErrorExcel.class);
						abmCustomerTemplateErrorExcel.setErrorMsg("必填字段校验失败");
						failedList.add(abmCustomerTemplateErrorExcel);
					}
				}
			}
		}

		log.info("CSV文件解析完成，成功：{}条，失败：{}条", successList.size(), failedList.size());

		// 构造返回结果
		JSONObject result = new JSONObject();
		result.put("validSuccess", successList);
		result.put("validFailed", failedList);
		return result;
	}

	/**
	 * 解析CSV行数据（使用反射和注解动态映射）
	 */
	private AbmCustomerTemplateExcel parseCsvLine(String line, int rowIndex) {
		try {
			// 简单的CSV解析（处理逗号分隔）
			String[] fields = line.split(",", -1); // -1保留空字段

			// 获取字段映射信息
			Map<Integer, Field> fieldMapping = getFieldMapping();
			int expectedFieldCount = fieldMapping.size();

			if (fields.length < expectedFieldCount) {
				log.warn("第{}行数据字段数量不足，期望{}个字段，实际{}个字段", rowIndex, expectedFieldCount, fields.length);
				// 扩展数组到期望的字段数量
				String[] newFields = new String[expectedFieldCount];
				System.arraycopy(fields, 0, newFields, 0, fields.length);
				for (int i = fields.length; i < expectedFieldCount; i++) {
					newFields[i] = "";
				}
				fields = newFields;
			}

			// 使用反射动态创建对象并赋值
			AbmCustomerTemplateExcel customer = new AbmCustomerTemplateExcel();
			for (Map.Entry<Integer, Field> entry : fieldMapping.entrySet()) {
				int index = entry.getKey();
				Field field = entry.getValue();

				if (index < fields.length) {
					String value = fields[index] == null ? "" : fields[index].trim();
					field.setAccessible(true);
					field.set(customer, value);
				}
			}
			return customer;
		} catch (Exception e) {
			log.error("解析第{}行CSV数据失败：{}", rowIndex, line, e);
			return null;
		}
	}

	/**
	 * 获取字段映射关系（基于@ExcelProperty注解的index属性）
	 */
	private Map<Integer, Field> getFieldMapping() {
		Map<Integer, Field> fieldMapping = new HashMap<>();
		try {
			Field[] fields = AbmCustomerTemplateExcel.class.getDeclaredFields();
			for (Field field : fields) {
				if (field.isAnnotationPresent(ExcelProperty.class)) {
					ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
					int index = excelProperty.index();
					fieldMapping.put(index, field);
				}
			}
		} catch (Exception e) {
			log.error("获取字段映射关系失败", e);
		}
		return fieldMapping;
	}

	/**
	 * 验证必填字段（使用反射和注解动态验证）
	 * <AUTHOR>
	 * @param customer 客户数据对象
	 * @param rowIndex 行号
	 * @return true-校验通过，false-校验失败
	 */
	private boolean validateRequiredFields(AbmCustomerTemplateExcel customer, int rowIndex) {
		List<String> missingFields = new ArrayList<>();
		try {
			Field[] fields = AbmCustomerTemplateExcel.class.getDeclaredFields();
			for (Field field : fields) {
				if (field.isAnnotationPresent(ExcelProperty.class)) {
					ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
					String[] values = excelProperty.value();

					// 判断是否为必填字段（字段名包含"（必填）"）
					if (values.length > 0 && (values[0].contains("必填") || values[0].endsWith("*"))) {
						field.setAccessible(true);
						Object fieldValue = field.get(customer);

						if (fieldValue == null || fieldValue.toString().trim().isEmpty()) {
							// 提取字段的中文名称（去掉"（必填）"标识）
							String fieldName = values[0].replace("（必填）", "");
							missingFields.add(fieldName);
						}
					}
				}
			}
		} catch (Exception e) {
			log.error("验证必填字段时发生异常", e);
			return false;
		}

		if (!missingFields.isEmpty()) {
			log.warn("第{}行数据缺少必填字段：{}", rowIndex, String.join("、", missingFields));
			return false;
		}

		return true;
	}

	/**
	 * 去除字段前后空格
	 */
	private String trimField(String field) {
		return field == null ? "" : field.trim();
	}

	/**
	 * 判断字段是否为空
	 */
	private boolean isEmpty(String field) {
		return field == null || field.trim().isEmpty();
	}

	/**
     * 生成并下载客户数据模板文件
     *
     * @param requestDto 请求参数
     */
    @PostMapping("/downloadTemplate")
    public void downloadCustomerTemplate(@RequestBody AbmCustImportRequestDto requestDto) {
        // 从RequestContextHolder获取response，避免AOP序列化冲突
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
	    assert attributes != null;
	    HttpServletResponse response = attributes.getResponse();
	    assert response != null;

        // 根据csvFile参数决定生成CSV还是Excel文件
        boolean isCSV = Boolean.TRUE.equals(requestDto.getCsvFile());
        if (isCSV) {
            generateCSVTemplate(response, requestDto);
        } else {
            generateExcelTemplate(response, requestDto);
        }
    }

    /**
     * 生成CSV模板
     */
    private void generateCSVTemplate(HttpServletResponse response, AbmCustImportRequestDto requestDto) {
        PrintWriter writer = null;
        try {
            log.info("开始生成客户导入CSV模板，请求参数：{}", requestDto);

            // 设置CSV响应头
            String fileName = generateFileName(true);
            setCSVResponseHeaders(response, fileName);

            // 获取输出流
            writer = response.getWriter();

            // 写入CSV表头
            writer.println("客户名称*,法人姓名*,主联系人手机号*,其他手机号 (多个手机号, 用英文分号隔开),邮箱,其他邮箱 (多个邮箱, 用英文分号隔开),办公地址（通讯地址）,leads code（必填）,更多信息");

            writer.flush();
            log.info("客户导入CSV模板生成完成，文件名：{}", fileName);

        } catch (Exception e) {
            log.error("生成客户导入CSV模板失败", e);
            handleException(response, e);
        } finally {
            if (writer != null) {
                writer.close();
            }
        }
    }

    /**
     * 生成Excel模板
     */
    private void generateExcelTemplate(HttpServletResponse response, AbmCustImportRequestDto requestDto) {
        OutputStream outputStream = null;
        try {
            log.info("开始生成客户导入Excel模板，请求参数：{}", requestDto);

            // 设置Excel响应头
            String fileName = generateFileName(false);
            setExcelResponseHeaders(response, fileName);

            // 获取输出流
            outputStream = response.getOutputStream();

            // 创建样式策略
            HorizontalCellStyleStrategy styleStrategy = createStyleStrategy();

            // 使用EasyExcel生成固定字段模板
            EasyExcel.write(outputStream, AbmCustomerTemplateExcel.class)
                    .registerWriteHandler(styleStrategy)
                    .sheet("客户导入模板")
                    .doWrite(Collections.emptyList());

            outputStream.flush();
            log.info("客户导入Excel模板生成完成，文件名：{}", fileName);

        } catch (Exception e) {
            log.error("生成客户导入Excel模板失败", e);
            handleException(response, e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭Excel输出流失败", e);
                }
            }
        }
    }

    /**
     * 创建Excel样式策略
     */
    private HorizontalCellStyleStrategy createStyleStrategy() {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
	    headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
	    contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
	    contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
	    contentWriteCellStyle.setWrapped(false);
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * 设置CSV文件HTTP响应头
     */
    private void setCSVResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("text/csv;charset=utf-8");
        response.setCharacterEncoding("utf-8");

        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                .replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
    }

    /**
     * 设置Excel文件HTTP响应头
     */
    private void setExcelResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                .replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
    }

    /**
     * 生成文件名
     */
    private String generateFileName(boolean isCSV) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String extension = isCSV ? ".csv" : ".xlsx";
        return "客户导入模板_" + timestamp + extension;
    }

    /**
     * 处理异常
     */
    private void handleException(HttpServletResponse response, Exception e) {
        try {
            // 检查响应是否已经提交
            if (response.isCommitted()) {
                log.error("响应已提交，无法处理异常: {}", e.getMessage());
                return;
            }

            // 重置响应
            response.reset();
            response.setContentType("application/json;charset=utf-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);

            // 构建错误响应
            WebResult<String> errorResult = WebResult.error(
                WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION.getCode(),
                "生成模板文件失败：" + e.getMessage()
            );

            // 写入错误响应
            response.getWriter().write(com.alibaba.fastjson.JSON.toJSONString(errorResult));
            response.getWriter().flush();
        } catch (IOException ioException) {
            log.error("处理异常时发生IO错误", ioException);
        }
    }
}
