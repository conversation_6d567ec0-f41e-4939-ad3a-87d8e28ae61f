package com.ce.scrm.center.web.entity.dto.segment;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 编辑商务标注
 * @Author: lijinpeng
 * @Date: 2025/2/27 14:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateCustomTagWebDto extends LoginInfo implements Serializable {

    /**
     * segment_detail表id
     */
    @NotNull
    private String id;

    /**
     * 商务标注
     */
    @NotNull
    private Integer salerCustomTag;

}
