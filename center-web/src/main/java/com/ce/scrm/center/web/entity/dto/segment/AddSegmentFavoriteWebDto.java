package com.ce.scrm.center.web.entity.dto.segment;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 添加收藏参数
 * @Author: lijinpeng
 * @Date: 2025/3/4 18:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddSegmentFavoriteWebDto extends LoginInfo implements Serializable {

    /**
     * segment_detail表id
     */
    @NotNull(message = "参数不能为空")
    private String segmentDetailId;

}
