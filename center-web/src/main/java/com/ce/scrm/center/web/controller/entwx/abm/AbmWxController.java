package com.ce.scrm.center.web.controller.entwx.abm;

import cn.ce.cesupport.base.utils.PositionUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.abm.BizOppDistributeBusiness;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.abm.ReceiptConfirmDto;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginType;
import com.ce.scrm.center.web.entity.dto.abm.AbmWxReceiptWebDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.FollowProductInfoWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/entwx/abm")
@Login(loginType = LoginType.ENT_WECHAT)
public class AbmWxController {

    @Resource
    private BizOppDistributeBusiness bizOppDistributeBusiness;

    @Resource
    EmployeeInfoBusiness employeeInfoBusiness;

    /**
     * 企微回执
     *
     * @param abmWxReceiptWebDto
     * @return
     */
    @PostMapping("/receipt")
    public WebResult<List<FollowProductInfoWebView>> reviewReceipt(@RequestBody AbmWxReceiptWebDto abmWxReceiptWebDto) {
        log.info("企微回执：{}", JSON.toJSONString(abmWxReceiptWebDto));
        ReceiptConfirmDto receiptConfirmDto = new ReceiptConfirmDto();
        String loginEmployeeId = abmWxReceiptWebDto.getLoginEmployeeId();
        if (StrUtil.isEmpty(loginEmployeeId)) {
            return WebResult.error(WebCodeMessageEnum.WX_LOGIN_EXPIRED);
        }

        String loginPosition = abmWxReceiptWebDto.getLoginPosition();
        if (StrUtil.isEmpty(loginPosition)) {
            EmployeeInfoBusinessDto employeeInfoBusinessDto = employeeInfoBusiness.getEmployeeInfoByEmpId(loginEmployeeId);
            loginPosition = employeeInfoBusinessDto.getPosition();
        }

        if (!PositionUtil.isBusinessSaler(loginPosition)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }

        String customerId = abmWxReceiptWebDto.getCustomerId();
        if (StringUtils.isEmpty(customerId)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "客户id不能为空");
        }

        if (StringUtils.isEmpty(loginEmployeeId)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "商务id不能为空");
        }
        receiptConfirmDto.setSalerId(loginEmployeeId);
        receiptConfirmDto.setCustomerId(customerId);
        Optional<String> optional = bizOppDistributeBusiness.doReceipt(receiptConfirmDto);
        return optional.<WebResult<List<FollowProductInfoWebView>>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, s))
                .orElseGet(WebResult::success);
    }

}
