package com.ce.scrm.center.web.enums;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:45:58
 * @desc 线下展会筛选项枚举
 */
public enum CustomerOfflineExFilterEnum implements EnumBase {

    COEF0("0", "全部"),
    COEF1("tag10045", "广交会"),
    ;

    @Getter
    private final String label;
    @Getter
    private final String value;

    // Getter 方法
    public String getKey() {
        return String.valueOf(label);
    }

    // 构造函数
    CustomerOfflineExFilterEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }
}
