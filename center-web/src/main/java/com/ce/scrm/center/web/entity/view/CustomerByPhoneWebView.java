package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 客户根据手机号查询视图
 * @Author: lijinpeng
 * @Date: 2024/12/9 10:55
 */
@Data
public class CustomerByPhoneWebView implements Serializable {

    /**
     * 客户名称
     */
    private String entName;

    /**
     * 客户状态
     */
    private String customerState;

    /**
     * 登记状态：1、在营/存续，2、迁入/迁出，3、吊销/撤销，4、注销，5、停业，6、其他
     */
    private String entStatus;

    /**
     * 所属行业
     */
    private String firstIndustry;

    /**
     * 成立日期
     */
    private Date createData;

    /**
     * 注册资本
     */
    private String regCapUnify;

    /**
     * 注册地址
     */
    private String contactAddress;

    /**
     * 联系人id
     */
//    private String contactPersonId;

    /**
     * 联系人姓名
     */
    private String contactPersonName;

    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 手机号
     */
    private String contactWay;

    /**
     * 搜客宝数据ID
     */
    private String pid;

    /**
     * 是否是精准联系人 1是 0否
     */
    private Integer exactContactPersonFlag;

    /**
     * 市场名称
     */
    private String marketName;

    /**
     * 是否可以收藏 1是 0否
     */
    private Integer favoriteFlag;

    /**
     * 是否可以保护 1是 0否
     */
    private Integer protectFlag;

    /**
     * 社会信用编码
     */
    private String socialCreditCode;

}
