package com.ce.scrm.center.web.controller.entwx.ai;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.AiAccessAuth;
import com.ce.scrm.center.dao.service.AiAccessAuthService;
import com.ce.scrm.center.service.config.NlpProperties;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.aop.base.LoginType;
import com.ce.scrm.center.web.entity.dto.QueryPhoneNumberWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.util.gptstreamutil.AiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/entwx/analysis")
@Login(loginType = LoginType.ENT_WECHAT)
public class EntAiAuth {

    @Autowired
    private AiService aiService;

    @PostMapping(value = "/person/auth")
    public WebResult<JSONObject> personAuth(@RequestBody LoginInfo loginInfo) {
        return WebResult.success(aiService.personAuth(loginInfo));
    }

}
