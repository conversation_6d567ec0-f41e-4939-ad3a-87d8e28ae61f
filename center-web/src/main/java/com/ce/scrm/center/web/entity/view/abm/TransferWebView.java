package com.ce.scrm.center.web.entity.view.abm;

import com.ce.scrm.center.web.entity.dto.abm.ApplicationTransferWebDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TransferWebView implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;
	/**
	 * 客户id
	 */
	private String customerId;

	/**
	 * 客户名称
	 */
	private String customerName;

	/**
	 * 调拨原因
	 */
	private String transferReason;

	/**
	 * 证明信息url
	 */
	private List<ApplicationTransferWebDto.FileIn> proofInfoUrl;

	/**
	 * 申请时间
	 */
	private Date applicationTime;

	/**
	 * 申请人
	 */
	private String applicant;

	/**
	 * 处理时间
	 */
	private Date processingTime;

	/**
	 * 处理人
	 */
	private String processor;

	/**
	 * 处理状态(1.未处理 2.通过 3.驳回 4.超时自动处理)
	 */
	private Integer processingStatus;

	/**
	 * 备注信息
	 */
	private String remarks;


}