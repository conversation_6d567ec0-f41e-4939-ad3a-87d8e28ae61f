package com.ce.scrm.center.web.entity.view;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrgEmployeeView {

    private String id;
    /**
     * 机构名称
     */
    private String name;
    /**
     * 机构父id
     */
    private String parentId;

    /**
     * 机构层级类型
     */
    private String type;

    private String position;

    private List<OrgEmployeeView> children;

}
