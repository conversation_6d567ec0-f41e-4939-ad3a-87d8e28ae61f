package com.ce.scrm.center.web.controller.customer;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.CustomerBusiness;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.entity.dto.*;
import com.ce.scrm.center.service.business.entity.view.CustomerLocationBusinessView;
import com.ce.scrm.center.service.business.entity.view.CustomerNearByTwentyBusinessView;
import com.ce.scrm.center.service.business.entity.view.CustomerTwoNameBusinessView;
import com.ce.scrm.center.service.business.entity.view.customer.CustomerAffiliatedEnterpriseBusinessView;
import com.ce.scrm.center.service.business.entity.view.customer.CustomerBusinessView;
import com.ce.scrm.center.service.business.entity.view.customer.CustomerByPhoneBusinessView;
import com.ce.scrm.center.service.business.entity.view.customer.CustomerTagsBusinessView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.CustomerTwoNameWebDto;
import com.ce.scrm.center.web.entity.dto.QueryCustomerLocationWebDto;
import com.ce.scrm.center.web.entity.dto.QueryCustomerWebDto;
import com.ce.scrm.center.web.entity.dto.customer.*;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.*;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * @version 1.0
 * @Description: 客户控制层
 * @Author: lijinpeng
 * @Date: 2024/12/5 14:54
 */
@Slf4j
@RestController
@Login
@RequestMapping("customer")
public class CustomerController {

    @Resource
    private CustomerBusiness customerBusiness;
	@Resource
	private EmployeeInfoBusiness employeeInfoBusiness;

    /*
     * @Description 根据名字获取客户信息，如果name是曾用名那么old会用name查询，new会用现用名查；如果name是现用名，那么old就用name查询即可，new为空
     * <AUTHOR>
     * @date 2024/12/5 16:27
     * @param name
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.CustomerTwoNameWebView>
     */
    @PostMapping("getCustomerTwoNameByName")
    public WebResult<CustomerTwoNameWebView> getCustomerTwoNameByName(@RequestBody CustomerTwoNameWebDto customerTwoNameWebDto) {
        CustomerTwoNameBusinessView customerTwoNameByName  = customerBusiness.getCustomerTwoNameByName(customerTwoNameWebDto.getName());
        CustomerTwoNameWebView customerTwoNameWebView = BeanUtil.copyProperties(customerTwoNameByName, CustomerTwoNameWebView.class);
        return WebResult.success(customerTwoNameWebView);
    }

    /*
     * @Description 根据联系方式和类型查询客户信息
     * <AUTHOR>
     * @date 2024/12/11 15:50
     * @param customerByPhoneWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.util.List<com.ce.scrm.center.web.entity.view.CustomerByPhoneWebView>>
     */
    @PostMapping("getCustomerByPhone")
    public WebResult<List<CustomerByPhoneWebView>> getCustomerByPhone(@RequestBody CustomerByPhoneWebDto customerByPhoneWebDto) {
        CustomerByPhoneBusinessDto customerByPhoneBusinessDto = BeanCopyUtils.convertToVo(customerByPhoneWebDto, CustomerByPhoneBusinessDto.class);
        List<CustomerByPhoneBusinessView> result = customerBusiness.getCustomerByPhone(customerByPhoneBusinessDto);
        List<CustomerByPhoneWebView> customerByPhoneWebViews = BeanCopyUtils.convertToVoList(result, CustomerByPhoneWebView.class);
        return WebResult.success(customerByPhoneWebViews);
    }

	/**
	 * 客户关联企业
	 * @param customerAffiliatedEnterpriseWebDto 关联企业信息查询入参信息
	 * @return 客户关联企业信息
	 */
	@PostMapping("affiliatedEnterprise")
	public WebResult<CustomerAffiliatedEnterpriseWebView> customerAffiliatedEnterprise(@Valid @RequestBody CustomerAffiliatedEnterpriseWebDto customerAffiliatedEnterpriseWebDto) {
		Transaction t = Cat.newTransaction("Controller", "CustomerController.customerAffiliatedEnterprise");
		try {
			CustomerAffiliatedEnterpriseBusinessDto businessDto = BeanCopyUtils.convertToVo(customerAffiliatedEnterpriseWebDto, CustomerAffiliatedEnterpriseBusinessDto.class);
			CustomerAffiliatedEnterpriseBusinessView businessView = customerBusiness.getAffiliatedEnterprise(businessDto);
			CustomerAffiliatedEnterpriseWebView webView = BeanCopyUtils.convertToVo(businessView, CustomerAffiliatedEnterpriseWebView.class);
			List<CustomerAffiliatedEnterpriseWebView.CustomerAffiliatedEnterpriseDetailWebView> webViewItemList = BeanCopyUtils.convertToVoList(businessView.getItems(),
				CustomerAffiliatedEnterpriseWebView.CustomerAffiliatedEnterpriseDetailWebView.class);
			webView.setItems(webViewItemList);
			return WebResult.success(webView);
		} catch (Exception e) {
			log.error("查询客户关联企业 失败,参数={},异常{}", JSONObject.toJSONString(customerAffiliatedEnterpriseWebDto), JSONObject.toJSONString(e.getMessage()));
			t.setStatus(e);
			Cat.logError(e);
			return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
		} finally {
			t.complete();
		}
	}

	/**
	 * 该客户名是否是曾用名
	 * @param customerHasHistoryFlagWebDto 客户历史曾用名查询入参信息
	 * @return true-是曾用名 false-不是曾用名
	 */
	@PostMapping("hasHistoryNameFlag")
	public WebResult<Boolean> hasHistoryNameFlag(@Valid @RequestBody CustomerHasHistoryFlagWebDto customerHasHistoryFlagWebDto) {
		Boolean existing =  customerBusiness.hasHistoryNameFlag(customerHasHistoryFlagWebDto.getCustomerName());
		return WebResult.success(existing);
	}

	/**
	 * 查询客户附近20公里内的相同二级行业成交客户
	 * @param customerNearByTwentyWebDto 客户历史曾用名查询入参信息
	 * @return true-是曾用名 false-不是曾用名
	 * 注意：搜客宝ES里 flag1=6中目前既有流失的客户，也有成交的客户。等通过初始化修正程序将flag1=6全部代表成交客户修正之后，可以使用此接口来分页
	 */
	@PostMapping("nearby/twenty")
	public WebResult<WebPageInfo<CustomerNearByTwentyMileWebView>> nearbyCustomerPage(@Valid @RequestBody CustomerNearByTwentyWebDto customerNearByTwentyWebDto) {
		EmployeeInfoBusinessDto currentUser = employeeInfoBusiness.getEmployeeInfoByEmpId(customerNearByTwentyWebDto.getLoginEmployeeId());
		if (currentUser == null) {
			return WebResult.error(WebCodeMessageEnum.WX_LOGIN_EXPIRED);
		}
		log.info("附近20公里内的相同二级行业成交客户入参:{}", JSON.toJSONString(customerNearByTwentyWebDto));
		CustomerNearByTwentyBusinessDto busRequest =  BeanCopyUtils.convertToVo(customerNearByTwentyWebDto, CustomerNearByTwentyBusinessDto.class);
		Page<CustomerNearByTwentyBusinessView> customerNearByTwentyBusinessViews = customerBusiness.nearbyTwentyKmCustomerPage(busRequest);
		log.info("{}查询了客户{}附近20公里内的相同二级行业成交客户列表:{}", currentUser.getId(), customerNearByTwentyWebDto.getPid(), JSON.toJSONString(customerNearByTwentyBusinessViews));
		WebPageInfo<CustomerNearByTwentyMileWebView> result = WebPageInfo.pageConversion(customerNearByTwentyBusinessViews, CustomerNearByTwentyMileWebView.class);
		return WebResult.success(result);
	}

	/**
	 * 查询客户附近20公里内的相同二级行业成交客户 不分页
	 * @param customerNearByTwentyWebDto 客户历史曾用名查询入参信息
	 * @return true-是曾用名 false-不是曾用名
	 */
	@PostMapping("nearby/twenty/list")
	public WebResult<List<CustomerNearByTwentyMileWebView>> nearbyCustomerList(@Valid @RequestBody CustomerNearByTwentyWebDto customerNearByTwentyWebDto) {
		EmployeeInfoBusinessDto currentUser = employeeInfoBusiness.getEmployeeInfoByEmpId(customerNearByTwentyWebDto.getLoginEmployeeId());
		if (currentUser == null) {
			return WebResult.error(WebCodeMessageEnum.WX_LOGIN_EXPIRED);
		}
		log.info("附近20公里内的相同二级行业成交客户入参(不使用分页):{}", JSON.toJSONString(customerNearByTwentyWebDto));
		CustomerNearByTwentyBusinessDto busRequest =  BeanCopyUtils.convertToVo(customerNearByTwentyWebDto, CustomerNearByTwentyBusinessDto.class);
		List<CustomerNearByTwentyBusinessView> customerNearByTwentyBusinessViews = customerBusiness.nearbyTwentyKmCustomerList(busRequest);
		log.info("{}查询了客户{}附近20公里内的相同二级行业成交客户列表(不使用分页):{}", currentUser.getId(), customerNearByTwentyWebDto.getPid(), JSON.toJSONString(customerNearByTwentyBusinessViews));
		if (CollectionUtils.isEmpty(customerNearByTwentyBusinessViews)) {
			return WebResult.success(Collections.emptyList());
		}
		List<CustomerNearByTwentyMileWebView> result = BeanCopyUtils.convertToVoList(customerNearByTwentyBusinessViews, CustomerNearByTwentyMileWebView.class);
		return WebResult.success(result);
	}

	/*
	 * @Description 根据客户名称获取客户位置
	 * <AUTHOR>
	 * @date 2025/4/1 11:19
	 * @param queryCustomerLocationWebDto
	 * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.CustomerLocationWebView>
	 */
	@PostMapping("/getCustomerLocationInfo")
	public WebResult<CustomerLocationWebView> getCustomerLocationInfo(@Valid @RequestBody QueryCustomerLocationWebDto queryCustomerLocationWebDto) {
		QueryCustomerLocationBusinessDto queryCustomerLocationBusinessDto = BeanCopyUtils.convertToVo(queryCustomerLocationWebDto, QueryCustomerLocationBusinessDto.class);
		CustomerLocationBusinessView customerLocationBusinessView = customerBusiness.getCustomerLocationInfo(queryCustomerLocationBusinessDto);
		CustomerLocationWebView customerLocationWebView = BeanCopyUtils.convertToVo(customerLocationBusinessView, CustomerLocationWebView.class);
		return WebResult.success(customerLocationWebView);
	}


	/**
	 * 根据客户名称查询客户信息
	 * @param queryCustomerWebDto
	 * @return
	 */
	@PostMapping("/getCustomerByCustomerName")
	public WebResult<CustomerWebView> getCustomerByCustomerName(@RequestBody QueryCustomerWebDto queryCustomerWebDto) {
		if (StringUtils.isEmpty(queryCustomerWebDto.getCustomerName())){
			return WebResult.success(null);
		}
		QueryCustomerBusinessDto queryCustomerBusinessDto = BeanCopyUtils.convertToVo(queryCustomerWebDto, QueryCustomerBusinessDto.class);
		CustomerBusinessView result = customerBusiness.getCustomerByCustomerName(queryCustomerBusinessDto);
		CustomerWebView customerWebView = BeanCopyUtils.convertToVo(result, CustomerWebView.class);
		return WebResult.success(customerWebView);
	}

	/**
	 * 根据pid查询客户信息
	 * @param queryCustomerWebDto
	 * @return
	 */
	@PostMapping("/getCustomerInfoFromElasticByPid")
	public WebResult<CustomerWebView> getCustomerFromSkbByPid(@RequestBody QueryCustomerWebDto queryCustomerWebDto) {
		if (StringUtils.isEmpty(queryCustomerWebDto.getPid())){
			return WebResult.success(null);
		}
		CustomerBusinessView result = customerBusiness.getCustomerFromSkbByPid(queryCustomerWebDto.getPid());
		CustomerWebView customerWebView = BeanCopyUtils.convertToVo(result, CustomerWebView.class);
		return WebResult.success(customerWebView);
	}

	/**
	 * 查询跨境ABM客户标签列表
	 * @param customerIdWebDto 客户id
	 * @return 客户标签列表，权重大小排序
	 */
	@PostMapping("/getCustomerTagByCustomerId")
	public WebResult<List<CustomerTagsWebView>> getCustomerTagByCustomerId(@Valid @RequestBody CustomerIdWebDto customerIdWebDto) {
		List<CustomerTagsBusinessView> customerTags = customerBusiness.getCustomerTags(customerIdWebDto.getCustomerId());
		List<CustomerTagsWebView> tags = BeanCopyUtils.convertToVoList(customerTags, CustomerTagsWebView.class);
		return WebResult.success(tags);
	}

}
