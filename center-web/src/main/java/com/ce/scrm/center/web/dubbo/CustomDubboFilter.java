package com.ce.scrm.center.web.dubbo;

import cn.hutool.core.util.StrUtil;
import com.ce.scrm.center.support.init.InitConsole;
import com.ce.scrm.center.util.constant.UtilConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.*;
import org.slf4j.MDC;

/**
 * 自定义dubbo过滤器
 * <AUTHOR>
 * @date 2023/4/6 14:02
 * @version 1.0.0
 **/
@Slf4j
public class CustomDubboFilter implements Filter {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        UtilConstant.Mdc.MDC_LIST.forEach((key, value) -> {
            if (RpcContext.getContext().isProviderSide()) {
                String rpcValue = RpcContext.getContext().getAttachment(key);
                if (StrUtil.isBlank(rpcValue)) {
                    rpcValue = value.get();
                }
                MDC.put(key, rpcValue);
            } else {
                RpcContext.getContext().setAttachment(key, MDC.get(key));
            }
        });
        if (RpcContext.getContext().isConsumerSide()) {
	        RpcContext.getContext().setRemoteApplicationName(InitConsole.APPLICATION_NAME);
	        RpcContext.getContext().setAttachment(UtilConstant.Mdc.REQUEST_ID_NAME, MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME));
	        RpcContext.getContext().setAttachment(UtilConstant.Mdc.START_TIME, MDC.get(UtilConstant.Mdc.START_TIME));
	        RpcContext.getContext().setAttachment(UtilConstant.Mdc.SOURCE_EMP_ID, MDC.get(UtilConstant.Mdc.SOURCE_EMP_ID));
        }
	    log.info("scrm-center-web dubbo DubboLogConsumerFilter日志，来源ip:{}, 方法名:{}, 参数:{}", RpcContext.getContext().getRemoteHost(), RpcContext.getContext().getMethodName(), RpcContext.getContext().getArguments());
        return invoker.invoke(invocation);
    }
}
