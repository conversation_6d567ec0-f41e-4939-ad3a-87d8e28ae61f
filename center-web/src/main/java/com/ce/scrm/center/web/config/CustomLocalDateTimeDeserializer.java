package com.ce.scrm.center.web.config;

import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;

import java.time.format.DateTimeFormatter;

/**
 * 自定义时间反序列化转换器
 * <AUTHOR>
 * @date 2024/5/23 下午2:42
 * @version 1.0.0
 */
public class CustomLocalDateTimeDeserializer extends LocalDateTimeDeserializer {
    /**
     * 单例
     */
    public static final CustomLocalDateTimeDeserializer INSTANCE = new CustomLocalDateTimeDeserializer();

    private CustomLocalDateTimeDeserializer() {
        super(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}