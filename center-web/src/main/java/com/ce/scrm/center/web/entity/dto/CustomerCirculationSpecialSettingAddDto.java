package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 成交客户流转流失特例配置表-添加请求体
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
public class CustomerCirculationSpecialSettingAddDto extends LoginInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "不流转类型不能为空")
    private Integer notCirculationType;

    @NotBlank(message = "客户Id不能为空")
    private String custId;

    /**
     * 不流转添加原因：字典NOT_CIRCULATION
     */
    private String notCirculationAddReason;

    /**
     * 不流失添加原因：字典NOT_LOSE
     */
    private String notLoseAddReason;

}
