package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.service.business.entity.dto.EmployeeIntentClueWhiteListBusinessDto;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * Description: 市场部人员组织
 * @author: JiuDD
 * date: 2024/11/5 10:01
 */
@Data
public class EmployeeIntentClueWhiteListWebPageDto extends LoginInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;


    public EmployeeIntentClueWhiteListBusinessDto packageBusinessDto(EmployeeIntentClueWhiteListWebPageDto webPageDto) {
        EmployeeIntentClueWhiteListBusinessDto businessDto = new EmployeeIntentClueWhiteListBusinessDto();
        BeanUtils.copyProperties(webPageDto, businessDto);
        if (Objects.isNull(webPageDto.getPageNum())) {
            businessDto.setPageNum(1);
        }
        if (Objects.isNull(webPageDto.getPageSize())) {
            businessDto.setPageSize(Integer.MAX_VALUE);
        }
        return businessDto;
    }
}