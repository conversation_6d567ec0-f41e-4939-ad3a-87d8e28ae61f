package com.ce.scrm.center.web.enums;

public enum SexEnum  {

    // 男
    MALE("男", 1),
	// 女
    FEMALE("女", 2),
    //未知
    UNKNOWN("未知", 3)
    ;

    /**
     * lable
     */
    private String lable;

    /**
     * value
     */
    private Integer value;

    private SexEnum(String lableStr, Integer valueStr) {
        this.lable = lableStr;
        this.value = valueStr;
    }

    public String getLable() {
        return lable;
    }

    public void setLable(String lable) {
        this.lable = lable;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
    public static String parseByValue(Integer in) {
        for (SexEnum statusEnum : SexEnum.values()) {
            if (statusEnum.getValue().equals(in)) {
                return statusEnum.getLable();
            }
        }
        return null;
    }

}
