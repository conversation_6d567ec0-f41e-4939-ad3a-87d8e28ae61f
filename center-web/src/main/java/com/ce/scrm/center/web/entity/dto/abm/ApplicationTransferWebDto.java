package com.ce.scrm.center.web.entity.dto.abm;

import lombok.Data;
import com.ce.scrm.center.web.aop.base.LoginInfo;

import java.io.Serializable;
import java.util.List;

/**
 * Description:申请调度参数
 */

@Data
public class ApplicationTransferWebDto extends LoginInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户id
     */
    private String customerId;
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 调拨原因
     * @see com.ce.scrm.center.service.enums.TransferReasonEnum
     */
    private Integer transferReason;

    /**
     * 文件路径列表
     */
    private List<FileIn> files;


    @Data
    public static class FileIn {
        private String fileUrl;
        private String fileName;
    }

}