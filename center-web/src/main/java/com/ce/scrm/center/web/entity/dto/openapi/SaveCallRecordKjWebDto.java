package com.ce.scrm.center.web.entity.dto.openapi;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/3/19 09:25
 */
@Data
public class SaveCallRecordKjWebDto implements Serializable {

    /**
     * 主通话ID
     */
    private String mainUniqueId;

    /**
     * 呼叫类型
     */
    private String callType;

    /**
     * 企业ID
     */
    private String enterpriseId;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 应答时间
     */
    private Long answerTime;

    /**
     * 桥接时间
     */
    private Long bridgeTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 客户号码
     */
    private String customerNumber;

    /**
     * 客户号码区号
     */
    private String customerAreaCode;

    /**
     * 客户省份
     */
    private String customerProvince;

    /**
     * 客户城市
     */
    private String customerCity;

    /**
     * 热线号码
     */
    private String hotline;

    /**
     * 目的码
     */
    private String numberTrunk;

    /**
     * 录音文件名
     */
    private String recordFile;

    /**
     * 队列号
     */
    private String qno;

    /**
     * 呼叫结果
     */
    private String statusResult;

    /**
     * 挂断方
     */
    private String onHookSource;

    /**
     * 机器人接听状态
     */
    private String statusRobot;

    /**
     * 首呼座席
     */
    private String firstCallCno;

    /**
     * 首呼座席电话
     */
    private String clientNumber;

    /**
     * 首呼队列
     */
    private String firstCallQno;

    /**
     * 从通话ID
     */
    private String uniqueId;

}
