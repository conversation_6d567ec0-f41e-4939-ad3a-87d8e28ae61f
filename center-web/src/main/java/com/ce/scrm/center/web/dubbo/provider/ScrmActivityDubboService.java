package com.ce.scrm.center.web.dubbo.provider;

import cn.ce.cesupport.emp.service.EmployeeAppService;
import cn.ce.cesupport.emp.vo.EmployeeVo;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.EqixiuActivityInfo;
import com.ce.scrm.center.dao.entity.EqixiuActivityVisibleRangeInfo;
import com.ce.scrm.center.dao.entity.EqixiuDataResult;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dao.service.EqixiuActivityVisibleRangeInfoService;
import com.ce.scrm.center.dao.service.IEqixiuActivityInfoService;
import com.ce.scrm.center.dao.service.IEqixiuDataResultService;
import com.ce.scrm.center.dubbo.api.ScrmActivityDubbo;
import com.ce.scrm.center.dubbo.entity.dto.EqixiuActivityInfoDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.EqixiuActivityUrlDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.config.UniqueIdService;
import com.ce.scrm.center.service.eqixiu.support.entity.EqixiuCodeInfo;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.entity.view.OrgDataThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.extend.dubbo.api.ShortUrlDubboService;
import com.ce.scrm.extend.dubbo.entity.dto.ShortUrlParam;
import com.ce.scrm.extend.dubbo.entity.view.ShortUrlView;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ce.scrm.center.service.eqixiu.support.entity.EqixiuConstant.SYSTEM_SHARE_LOTTERY;

@Slf4j
@DubboService(interfaceClass = ScrmActivityDubbo.class)
public class ScrmActivityDubboService implements ScrmActivityDubbo {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private ShortUrlDubboService shortUrlDubboService;

    @Resource
    private IEqixiuActivityInfoService eqixiuActivityInfoService;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private IEqixiuDataResultService eqixiuDataResultService;


    @Resource
    private OrgThirdService orgThirdService;

    @Resource
    private UniqueIdService uniqueIdService;


    @DubboReference
    private EmployeeAppService employeeAppService;

    @Resource
    private EmployeeInfoBusiness employeeInfoBusiness;

    @Resource
    private CmCustProtectService custProtectService;

    @Autowired
    private SendWxMessage sendWxMessage;

    @Resource
    private EqixiuActivityVisibleRangeInfoService eqixiuActivityVisibleRangeInfoService;

    private static final String customerMessage = "商务【%s】生成了客户【%s】 【%s】的邀请链接。";


    @Override
    public DubboResult<Map<String, Object>> getShareLinkUrl(String custId, EqixiuActivityUrlDubboDto eqixiuCodeDto) {
        //获取员工的部门分司等数据
        if (StringUtils.isBlank(eqixiuCodeDto.getActivityId())) {
            log.warn("活动信息为空，活动：{}", JSONObject.toJSONString(eqixiuCodeDto));
            return DubboResult.error(DubboCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
        }
        // 查询活动信息
        EqixiuActivityInfo eqixiuActivityInfo = eqixiuActivityInfoService.getById(eqixiuCodeDto.getActivityId());
        if (null == eqixiuActivityInfo) {
            log.warn("活动信息为空，活动：{}", JSONObject.toJSONString(eqixiuCodeDto));
            return DubboResult.error(DubboCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
        }
        // 查询 分享人信息
        EmployeeInfoBusinessDto employeeDataThirdViewShareInfo = employeeInfoBusiness.getEmployeeInfoByEmpId(custId);
        log.info("根据员工id获取员工信息={}", employeeDataThirdViewShareInfo);
        // 获取客户的保护关系,查询保护客户信息

        CustProtectView custProtect = custProtectService.selectOneByCondition(CmCustProtect.builder().custId(eqixiuCodeDto.getCustomerId()).build());
        EmployeeDataThirdView employeeDataThirdViewProtect = new EmployeeDataThirdView();
        try {
            if (custProtect != null) {
                // 设置 `salerId` 根据 `status` 判断是否设置为空
                employeeDataThirdViewProtect.setId(custProtect.getStatus() == 1 ? custProtect.getSalerId() : null);
                // 设置 `subId`、`areaId` 和 `orgId`
                employeeDataThirdViewProtect.setSubId(custProtect.getSubcompanyId());
                employeeDataThirdViewProtect.setAreaId(custProtect.getAreaId());
                employeeDataThirdViewProtect.setOrgId(custProtect.getStatus() == 2 ? null : custProtect.getBussdeptId());
                employeeDataThirdViewProtect.setBuId(custProtect.getBuId());
            }
        } catch (Exception e) {
            log.warn("获取保护关系等失败", e);
        }
        if (null != employeeDataThirdViewProtect.getId()) {
            EmployeeVo employeeVo = employeeAppService.selectOneNofilter(employeeDataThirdViewProtect.getId());
            if (null != employeeVo) {
                employeeDataThirdViewProtect.setName(employeeVo.getName());
            }
        }
        /****************************************************************************************/
        Map<String, OrgDataThirdView> suborgDataThirdViewMap = orgThirdService.getOrgData(Stream.of(
                employeeDataThirdViewProtect.getSubId(),
                employeeDataThirdViewProtect.getAreaId(),
                employeeDataThirdViewProtect.getOrgId(),
                employeeDataThirdViewProtect.getBuId(),
                employeeDataThirdViewShareInfo.getSubId(),
                employeeDataThirdViewShareInfo.getAreaId(),
                employeeDataThirdViewShareInfo.getOrgId(),
                employeeDataThirdViewShareInfo.getBuId()
        ).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
        employeeDataThirdViewProtect.setSubName(suborgDataThirdViewMap.getOrDefault(employeeDataThirdViewProtect.getSubId(), new OrgDataThirdView()).getName());
        employeeDataThirdViewProtect.setAreaName(suborgDataThirdViewMap.getOrDefault(employeeDataThirdViewProtect.getAreaId(), new OrgDataThirdView()).getName());
        employeeDataThirdViewProtect.setOrgName(suborgDataThirdViewMap.getOrDefault(employeeDataThirdViewProtect.getOrgId(), new OrgDataThirdView()).getName());
        employeeDataThirdViewProtect.setBuName(suborgDataThirdViewMap.getOrDefault(employeeDataThirdViewProtect.getBuId(), new OrgDataThirdView()).getName());
        // name set
        employeeDataThirdViewShareInfo.setSubName(suborgDataThirdViewMap.getOrDefault(employeeDataThirdViewShareInfo.getSubId(), new OrgDataThirdView()).getName());
        employeeDataThirdViewShareInfo.setAreaName(suborgDataThirdViewMap.getOrDefault(employeeDataThirdViewShareInfo.getAreaId(), new OrgDataThirdView()).getName());
        employeeDataThirdViewShareInfo.setOrgName(suborgDataThirdViewMap.getOrDefault(employeeDataThirdViewShareInfo.getOrgId(), new OrgDataThirdView()).getName());
        employeeDataThirdViewShareInfo.setBuName(suborgDataThirdViewMap.getOrDefault(employeeDataThirdViewShareInfo.getBuId(), new OrgDataThirdView()).getName());
        //保存信息
        String dataResultId = uniqueIdService.getId();
        EqixiuDataResult eqixiuDataResultSave = new EqixiuDataResult();
        eqixiuDataResultSave.setId(dataResultId);
        eqixiuDataResultSave.setActivityId(eqixiuActivityInfo.getActivityId());
        eqixiuDataResultSave.setActivityName(eqixiuActivityInfo.getActivityName());
        eqixiuDataResultSave.setActivityUrl(eqixiuActivityInfo.getActivityUrl());
        eqixiuDataResultSave.setCustomerId(eqixiuCodeDto.getCustomerId());
        eqixiuDataResultSave.setActivityType(eqixiuActivityInfo.getActivityType());
        eqixiuDataResultSave.setCustomerName(eqixiuCodeDto.getCustomerName());
        eqixiuDataResultSave.setCustType(String.valueOf(custProtect.getStatus()));
        eqixiuDataResultSave.setSalerId(employeeDataThirdViewShareInfo.getId());
        eqixiuDataResultSave.setSalerName(employeeDataThirdViewShareInfo.getName());
        eqixiuDataResultSave.setSalerDeptId(employeeDataThirdViewShareInfo.getOrgId());
        eqixiuDataResultSave.setSalerDeptName(employeeDataThirdViewShareInfo.getOrgName());
        eqixiuDataResultSave.setSalerSubId(employeeDataThirdViewShareInfo.getSubId());
        eqixiuDataResultSave.setSalerSubName(employeeDataThirdViewShareInfo.getSubName());
        eqixiuDataResultSave.setSalerAreaId(employeeDataThirdViewShareInfo.getAreaId());
        eqixiuDataResultSave.setSalerAreaName(employeeDataThirdViewShareInfo.getAreaName());
        eqixiuDataResultSave.setSalerBuId(employeeDataThirdViewShareInfo.getBuId());
        eqixiuDataResultSave.setSalerBuName(employeeDataThirdViewShareInfo.getBuName());
        eqixiuDataResultSave.setProtectSalerId(employeeDataThirdViewProtect.getId());
        eqixiuDataResultSave.setProtectSalerName(employeeDataThirdViewProtect.getName());
        eqixiuDataResultSave.setProtectAreaId(employeeDataThirdViewProtect.getAreaId());
        eqixiuDataResultSave.setProtectArea(employeeDataThirdViewProtect.getAreaName());
        eqixiuDataResultSave.setProtectSubId(employeeDataThirdViewProtect.getSubId());
        eqixiuDataResultSave.setProtectSubName(employeeDataThirdViewProtect.getSubName());
        eqixiuDataResultSave.setProtectDeptId(employeeDataThirdViewProtect.getOrgId());
        eqixiuDataResultSave.setProtectDeptName(employeeDataThirdViewProtect.getOrgName());
        eqixiuDataResultSave.setProtectBuId(employeeDataThirdViewProtect.getBuId());
        eqixiuDataResultSave.setProtectBuName(employeeDataThirdViewProtect.getBuName());
        eqixiuDataResultSave.setContactId(eqixiuCodeDto.getContactId());
        eqixiuDataResultSave.setContactPhone(eqixiuCodeDto.getContactPhone());
        eqixiuDataResultSave.setContactName(eqixiuCodeDto.getContactName());
        eqixiuDataResultSave.setShareType(eqixiuCodeDto.getShareType());
        eqixiuDataResultSave.setOperatingTerminal(eqixiuCodeDto.getOperatingTerminal());
        eqixiuDataResultSave.setTriggerId(null);
        eqixiuDataResultSave.setEventType(SYSTEM_SHARE_LOTTERY);
        eqixiuDataResultSave.setRecentOpenTime(null);
        eqixiuDataResultSave.setRecentLotteryTime(null);
        eqixiuDataResultSave.setRecentShareTime(new Date());
        eqixiuDataResultSave.setIsWin(0);
        eqixiuDataResultSave.setWinInfo(null);
        eqixiuDataResultSave.setCreateTime(new Date());
        eqixiuDataResultSave.setUpdateTime(new Date());
        // 生成短链
        EqixiuCodeInfo eqixiuCodeInfo = new EqixiuCodeInfo();
        eqixiuCodeInfo.setEqixiuDataResultId(dataResultId);
        ShortUrlParam shortUrlParam = new ShortUrlParam();
        shortUrlParam.setLongUrl(JSONObject.toJSONString(eqixiuCodeInfo));
        String code = null;
        try {
            ShortUrlView shortUrlView = shortUrlDubboService.generateShortUrl(shortUrlParam).getData();
            code = shortUrlView.getUniqueCode();
        } catch (Exception e) {
            log.error("生成短链失败", e);
        }
        if (StringUtils.isBlank(code)) {
            return DubboResult.error(DubboCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
        }
        String url = UriComponentsBuilder.fromUriString(eqixiuActivityInfo.getActivityUrl())
                .queryParam("code", code)
                .toUriString();
        eqixiuDataResultSave.setShareLink(url);
        eqixiuDataResultService.save(eqixiuDataResultSave);
        //发送邀请通知
        try {
            String customerMessageFormat = String.format(customerMessage,
                    employeeDataThirdViewShareInfo.getName(),
                    eqixiuCodeDto.getCustomerName(),
                    eqixiuActivityInfo.getActivityName()
            );
            sendWechatMessage(customerMessageFormat);
            sendMessage(eqixiuDataResultSave.getSalerDeptId(), eqixiuDataResultSave.getSalerSubId(), customerMessageFormat, eqixiuDataResultSave.getSalerAreaId());
        } catch (Exception e) {
            log.error("生成邀请链接发送机器人消失失败");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("url", url);
        return DubboResult.success(jsonObject);
    }

    @Override
    public DubboResult<Map<String, Object>> getActivityList(String custId, EqixiuActivityInfoDubboDto eqixiuActivityInfoDto) {
        Optional<EmployeeDataThirdView> employeeDataThirdViewOptional = employeeThirdService.getEmployeeData(custId);
        if (!employeeDataThirdViewOptional.isPresent()) {
            log.warn("获取员工信息为空，员工ID为：{}", custId);
            return DubboResult.error(DubboCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
        }
        EmployeeDataThirdView employeeDataThirdViewShareInfo = employeeDataThirdViewOptional.get();
        // 如果登录人是伍商务 查询所有的
        Page<EqixiuActivityInfo> page = new Page<>(eqixiuActivityInfoDto.getPageNum(), eqixiuActivityInfoDto.getPageSize());
        Page<EqixiuActivityInfo> eqixiuActivityInfos = new Page<>();
        // 测试分公司1  和测试分公司2
        String subId = "f3863ee8f6ba46569ee9fa7403c3da1f,f3863ee8f6ba46569ee9fa7403c3da1g";
        if (Objects.nonNull(employeeDataThirdViewShareInfo.getSubId()) && Arrays.asList(subId.split(",")).contains(employeeDataThirdViewShareInfo.getSubId())) {
            eqixiuActivityInfos = eqixiuActivityInfoService.page(page, this.buildQueryCommon(eqixiuActivityInfoDto));
            log.info("取所有活动");
        } else {
            List<String> activityIdsVis = new ArrayList<>();
            LambdaQueryWrapper<EqixiuActivityInfo> queryWrapperVis = this.buildQueryCommon(eqixiuActivityInfoDto);
            queryWrapperVis.eq(EqixiuActivityInfo::getVisibleRange, 2);
            List<EqixiuActivityInfo> eqixiuActivityInfos2 = eqixiuActivityInfoService.list(queryWrapperVis);
            if (!CollectionUtils.isEmpty(eqixiuActivityInfos2)) {
                // id in 查询 课件查询范围 4 个or 查询 获取ID
                LambdaQueryWrapper<EqixiuActivityVisibleRangeInfo> queryWrapperRange = new LambdaQueryWrapper<>();
                //在进行中的活动
                queryWrapperRange.select(EqixiuActivityVisibleRangeInfo::getActivityId).and((e) -> e.or()
                                .eq(EqixiuActivityVisibleRangeInfo::getDeptId, employeeDataThirdViewShareInfo.getOrgId())
                                .or()
                                .eq(EqixiuActivityVisibleRangeInfo::getSubId, employeeDataThirdViewShareInfo.getSubId())
                                .or()
                                .eq(EqixiuActivityVisibleRangeInfo::getAreaId, employeeDataThirdViewShareInfo.getAreaId())
                                .or()
                                .eq(EqixiuActivityVisibleRangeInfo::getCustId, employeeDataThirdViewShareInfo.getId()))
                        .in(EqixiuActivityVisibleRangeInfo::getActivityId, eqixiuActivityInfos2.stream().map(EqixiuActivityInfo::getActivityId).collect(Collectors.toList()));
                List<EqixiuActivityVisibleRangeInfo> eqixiuActivityVisibleRangeInfos = eqixiuActivityVisibleRangeInfoService.list(queryWrapperRange);
                activityIdsVis = eqixiuActivityVisibleRangeInfos.stream().map(EqixiuActivityVisibleRangeInfo::getActivityId).collect(Collectors.toList());
                log.info("取有限范围活动ID{}", JSONObject.toJSONString(activityIdsVis));
            }
            LambdaQueryWrapper<EqixiuActivityInfo> queryWrapperFil = this.buildQueryCommon(eqixiuActivityInfoDto);
            queryWrapperFil.eq(EqixiuActivityInfo::getVisibleRange, 1);
            if (!CollectionUtils.isEmpty(activityIdsVis)) {
                queryWrapperFil.or().in(EqixiuActivityInfo::getActivityId, activityIdsVis);
            }
            eqixiuActivityInfos = eqixiuActivityInfoService.page(page, queryWrapperFil);
        }
        WebPageInfo webPageInfo = WebPageInfo.pageConversion(eqixiuActivityInfos, EqixiuActivityInfo.class);
        webPageInfo.setList(eqixiuActivityInfos.getRecords().stream().map(T -> JSONObject.parseObject(JSONObject.toJSONString(T))).collect(Collectors.toList()));
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(webPageInfo));
        return DubboResult.success(jsonObject);
    }

    private LambdaQueryWrapper<EqixiuActivityInfo> buildQueryCommon(EqixiuActivityInfoDubboDto eqixiuActivityInfoDto) {
        LambdaQueryWrapper<EqixiuActivityInfo> queryWrapperCommon = new LambdaQueryWrapper<>();
        queryWrapperCommon
                .select(EqixiuActivityInfo::getActivityId, EqixiuActivityInfo::getId, EqixiuActivityInfo::getActivityName, EqixiuActivityInfo::getSharePosterInfo, EqixiuActivityInfo::getSharePic, EqixiuActivityInfo::getShareTitle, EqixiuActivityInfo::getShareDesc)
                .eq(null != eqixiuActivityInfoDto.getActivityType(), EqixiuActivityInfo::getActivityType, eqixiuActivityInfoDto.getActivityType())
                .and((e) -> e.ge(EqixiuActivityInfo::getEndTime, new Date()).or().isNull(EqixiuActivityInfo::getEndTime))
                .and((e) -> e.le(EqixiuActivityInfo::getStartTime, new Date()).or().isNull(EqixiuActivityInfo::getStartTime))
                .eq(null != eqixiuActivityInfoDto.getCardShare(), EqixiuActivityInfo::getCardShare, eqixiuActivityInfoDto.getCardShare())
                .eq(null != eqixiuActivityInfoDto.getPosterShare(), EqixiuActivityInfo::getPosterShare, eqixiuActivityInfoDto.getPosterShare())
                .orderByDesc(EqixiuActivityInfo::getUpdateTime);
        return queryWrapperCommon;
    }


    /**
     * 消息最大长度
     */
    private final static int MSG_MAX_LENGTH = 4096;

    public Boolean sendWechatMessage(String message) {
        try {
            if (message.length() > MSG_MAX_LENGTH) {
                message = message.substring(0, MSG_MAX_LENGTH);
            }
            Map<String, String> contentMap = new HashMap<>();
            contentMap.put("content", message);
            Map<String, Object> param = new HashMap<>();
            param.put("msgtype", "markdown");
            param.put("markdown", contentMap);
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, JSONObject.toJSONString(param));
            Request request = new Request.Builder()
                    .url("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8c0309df-43d6-41df-af22-03d4f4fc74d5")
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            response.close();
        } catch (Exception e) {
            log.error("活动消息通知发送失败");
        }
        return true;
    }

    private void sendMessage(String deptId, String subId, String message, String areaId) {
        Optional<EmployeeLiteThirdView> orgLeaderCooperation = employeeThirdService.getOrgLeader(deptId);
        if (orgLeaderCooperation.isPresent() && !StringUtils.isEmpty(orgLeaderCooperation.get().getId())) {
            sendWxMessage.sendMessage(orgLeaderCooperation.get().getId(), message);
            log.info("给部门经理发送消息成功:{},{}", orgLeaderCooperation.get().getId(), message);
        }
//        Optional<EmployeeLiteThirdView> subLeaderProtect = employeeThirdService.getOrgLeader(subId);
//        if (subLeaderProtect.isPresent() && !StringUtils.isEmpty(subLeaderProtect.get().getId())) {
//            sendWxMessage.sendMessage(subLeaderProtect.get().getId(), message);
//            log.info("给部分司总监发送消息成功:{},{}", subLeaderProtect.get().getId(), message);
//        }
//        Optional<EmployeeLiteThirdView> areaLeaderProtect = employeeThirdService.getOrgLeader(areaId);
//        if (areaLeaderProtect.isPresent() && !StringUtils.isEmpty(areaLeaderProtect.get().getId())) {
//            sendWxMessage.sendMessage(areaLeaderProtect.get().getId(), message);
//            log.info("给部区域总监发送消息成功:{},{}", areaLeaderProtect.get().getId(), message);
//        }
    }
}
