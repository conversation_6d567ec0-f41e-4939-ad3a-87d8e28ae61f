package com.ce.scrm.center.web.controller.ai.crm;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.business.AiCrmBusiness;
import com.ce.scrm.center.service.business.entity.ai.crm.BusinessOpportunityView;
import com.ce.scrm.center.service.business.entity.ai.crm.CustomerFollowView;
import com.ce.scrm.center.service.business.entity.dto.ai.AiBusinessDto;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.ai.AiWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @version 1.0
 * @Description: ai控制层-crm
 * @Author: lijinpeng
 * @Date: 2025/2/17 13:41
 */
@Login
@RestController
@RequestMapping("/ai/crm")
public class AiCrmController {


    @Resource
    private AiCrmBusiness aiCrmBusiness;

    @PostMapping(value = "getCustomerFollow")
    public WebResult<String> getCustomerFollow(@RequestBody AiWebDto aiWebDto) {
        AiBusinessDto aiBusinessDto = BeanCopyUtils.convertToVo(aiWebDto, AiBusinessDto.class);
		List<CustomerFollowView> result = aiCrmBusiness.getCustomerFollow(aiBusinessDto);
        return WebResult.success(result.toString());
    }

	@PostMapping(value = "getBusinessOpportunity")
	public WebResult<String> getBusinessOpportunity(@RequestBody AiWebDto aiWebDto) {
		AiBusinessDto aiBusinessDto = BeanCopyUtils.convertToVo(aiWebDto, AiBusinessDto.class);
		JSONObject result = aiCrmBusiness.getBusinessOpportunityNew(aiBusinessDto);
		return WebResult.success(result.toString());
	}


	/**
	 * 客户交易数据-已购产品
	 * @param aiWebDto customerId
	 * @return WebResult
	 */
	@PostMapping(value = "getCustomerDealingsFromCrm")
	public WebResult<String> getCustomerDealings(@RequestBody AiWebDto aiWebDto) {
		AiBusinessDto aiBusinessDto = BeanCopyUtils.convertToVo(aiWebDto, AiBusinessDto.class);
		String result = aiCrmBusiness.getCustomerDealings(aiBusinessDto);
		return WebResult.success(result);
	}


}
