package com.ce.scrm.center.web.controller;

import cn.ce.cesupport.enums.GcSjSourceEnum;
import cn.ce.cesupport.enums.GcSjStateEnum;
import cn.ce.cesupport.enums.GcSjOperateLogTypeEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.GcBusinessOpportunityBusiness;
import com.ce.scrm.center.service.business.entity.dto.*;
import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityAssignDataBusinessView;
import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityBusinessView;
import com.ce.scrm.center.service.business.entity.view.GcBusinessOpportunityEnumData;
import com.ce.scrm.center.service.enums.GcSjLevelEnum;
import com.ce.scrm.center.service.enums.GcSjRequirementEnum;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.entity.view.OrgDataThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.controller.base.BaseController;
import com.ce.scrm.center.web.entity.dto.*;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.GcBusinessOpportunityAssignDataWebView;
import com.ce.scrm.center.web.entity.view.GcBusinessOpportunityWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 高呈商机相关接口
 * 已废弃
 * <AUTHOR>
 * @date 2024/5/20 上午11:16
 * @version 1.0.0
 */
@Deprecated
@Login
@Slf4j
@RestController
@RequestMapping("gaocheng/shangji")
public class GcBusinessOpportunityController extends BaseController {

    @Resource
    private GcBusinessOpportunityBusiness gcBusinessOpportunityBusiness;

    @Resource
    private OrgThirdService orgThirdService;

    @Resource
    private EmployeeThirdService employeeThirdService;


    @PostMapping("enums")
    public WebResult<GcBusinessOpportunityEnumData> enums(@RequestBody LoginInfo loginInfo) {
        return WebResult.success(gcBusinessOpportunityBusiness.enums());
    }

    /**
     * 高呈商机分页列表
     * @param gcBusinessOpportunityPageWebDto   分页参数
     * <AUTHOR>
     * @date 2024/5/20 上午11:34
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo < com.ce.scrm.center.web.entity.view.GcBusinessOpportunityWebView>>
     **/
    @PostMapping("pageList")
    public WebResult<WebPageInfo<GcBusinessOpportunityWebView>> pageList(@Validated @RequestBody GcBusinessOpportunityPageWebDto gcBusinessOpportunityPageWebDto) {
        GcSjSourceEnum gcSjSourceEnum = null;
        if (gcBusinessOpportunityPageWebDto.getSource() != null && (gcSjSourceEnum = GcSjSourceEnum.get(gcBusinessOpportunityPageWebDto.getSource())) == null) {
            log.warn("获取高呈商机分页列表，来源字段传入有误，参数为:{}", JSON.toJSONString(gcBusinessOpportunityPageWebDto));
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, "来源字段传入有误");
        }
        List<Integer> stateList = gcBusinessOpportunityPageWebDto.getState();
        List<GcSjStateEnum> gcSjStateEnumList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(stateList)) {
            for (Integer state : stateList) {
                GcSjStateEnum gcSjStateEnum = GcSjStateEnum.get(state);
                if (gcSjStateEnum != null) {
                    gcSjStateEnumList.add(gcSjStateEnum);
                }
            }
        }
        GcBusinessOpportunityPageBusinessDto gcBusinessOpportunityPageBusinessDto = CglibUtil.copy(gcBusinessOpportunityPageWebDto, GcBusinessOpportunityPageBusinessDto.class);
        gcBusinessOpportunityPageBusinessDto.setGcSjSourceEnum(gcSjSourceEnum);
        gcBusinessOpportunityPageBusinessDto.setGcSjStateEnumList(gcSjStateEnumList);
        WebPageInfo<GcBusinessOpportunityWebView> gcBusinessOpportunityWebViewWebPageInfo = WebPageInfo.pageConversion(gcBusinessOpportunityBusiness.pageList(gcBusinessOpportunityPageBusinessDto), GcBusinessOpportunityWebView.class);
        gcBusinessOpportunityWebViewWebPageInfo.getList().forEach(this::packageGcBusinessData);
        return WebResult.success(gcBusinessOpportunityWebViewWebPageInfo);
    }

    /**
     * 获取商机详情
     * @param gcBusinessOpportunityOperateWebDto    商机详情参数
     * <AUTHOR>
     * @date 2024/5/27 下午4:27
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.GcBusinessOpportunityWebView>
     **/
    @PostMapping("detail")
    public WebResult<GcBusinessOpportunityWebView> detail(@Validated @RequestBody GcBusinessOpportunityOperateWebDto gcBusinessOpportunityOperateWebDto) {
        Optional<GcBusinessOpportunityBusinessView> gcBusinessOpportunityBusinessViewOptional = gcBusinessOpportunityBusiness.detail(gcBusinessOpportunityOperateWebDto.packageOperateParam());
        GcBusinessOpportunityWebView gcBusinessOpportunityWebView = null;
        if (gcBusinessOpportunityBusinessViewOptional.isPresent()) {
            GcBusinessOpportunityBusinessView gcBusinessOpportunityBusinessView = gcBusinessOpportunityBusinessViewOptional.get();
            gcBusinessOpportunityWebView = CglibUtil.copy(gcBusinessOpportunityBusinessView, GcBusinessOpportunityWebView.class);
            gcBusinessOpportunityWebView.setShowCurrentInfo(true);
            packageGcBusinessData(gcBusinessOpportunityWebView);
            gcBusinessOpportunityWebView.setOperateLogList(gcBusinessOpportunityBusiness.getOperateLog(gcBusinessOpportunityOperateWebDto.getSjId()).stream().map(gcBusinessOpportunityLog -> {
                GcBusinessOpportunityWebView.OperateLog operateLog = new GcBusinessOpportunityWebView.OperateLog();
                operateLog.setOperatorName(gcBusinessOpportunityLog.getOperatorName());
                operateLog.setOperateTime(gcBusinessOpportunityLog.getOperateTime());
                GcSjOperateLogTypeEnum gcSjOperateLogTypeEnum = GcSjOperateLogTypeEnum.get(gcBusinessOpportunityLog.getOperateType());
                if (gcSjOperateLogTypeEnum != null) {
                    operateLog.setOperateTypeName(gcSjOperateLogTypeEnum.getOperateName());
                }
                operateLog.setRemark(gcBusinessOpportunityLog.getRemark());
                return operateLog;
            }).collect(Collectors.toList()));
        }
        return WebResult.success(gcBusinessOpportunityWebView);
    }

    /**
     * 添加商机
     * @param gcBusinessOpportunitySaveWebDto   商机添加参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:25
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("add")
    public WebResult<Boolean> add(@Validated @RequestBody GcBusinessOpportunitySaveWebDto gcBusinessOpportunitySaveWebDto) {
        Optional<String> addOptional = gcBusinessOpportunityBusiness.add(packageSaveParam(gcBusinessOpportunitySaveWebDto));
        return addOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * 拼装保存参数
     * @param gcBusinessOpportunitySaveWebDto 保存参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:38
     * @return com.ce.scrm.center.service.business.entity.dto.GcBusinessOpportunitySaveBusinessDto
     **/
    private GcBusinessOpportunitySaveBusinessDto packageSaveParam(GcBusinessOpportunitySaveWebDto gcBusinessOpportunitySaveWebDto) {
        GcBusinessOpportunitySaveBusinessDto gcBusinessOpportunitySaveBusinessDto = CglibUtil.copy(gcBusinessOpportunitySaveWebDto, GcBusinessOpportunitySaveBusinessDto.class);
        gcBusinessOpportunitySaveBusinessDto.setId(gcBusinessOpportunitySaveWebDto.getSjId());
        gcBusinessOpportunitySaveBusinessDto.setOperator(gcBusinessOpportunitySaveWebDto.getLoginEmployeeId());
        gcBusinessOpportunitySaveBusinessDto.setOperatorName(gcBusinessOpportunitySaveWebDto.getLoginEmployeeName());
        gcBusinessOpportunitySaveBusinessDto.setLevel(GcSjLevelEnum.get(gcBusinessOpportunitySaveWebDto.getLevel()));
        gcBusinessOpportunitySaveBusinessDto.setSource(GcSjSourceEnum.get(gcBusinessOpportunitySaveWebDto.getSource()));
        gcBusinessOpportunitySaveBusinessDto.setRequirementType(GcSjRequirementEnum.get(gcBusinessOpportunitySaveWebDto.getRequirementType()));
        gcBusinessOpportunitySaveBusinessDto.setGcAreaId(OrgThirdService.KA_SALE_DEPT_AREA_ID);
        gcBusinessOpportunitySaveBusinessDto.setGcAreaName(orgThirdService.getOrgData(OrgThirdService.KA_SALE_DEPT_AREA_ID).orElseGet(OrgDataThirdView::new).getName());
        return gcBusinessOpportunitySaveBusinessDto;
    }

    /**
     * 更新商机
     * @param gcBusinessOpportunitySaveWebDto   商机更新参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:25
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("update")
    public WebResult<Boolean> update(@Validated @RequestBody GcBusinessOpportunitySaveWebDto gcBusinessOpportunitySaveWebDto) {
        Optional<String> updateOptional = gcBusinessOpportunityBusiness.update(packageSaveParam(gcBusinessOpportunitySaveWebDto));
        return updateOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * 删除商机
     * @param gcBusinessOpportunityOperateWebDto    商机删除参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:41
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("delete")
    public WebResult<Boolean> delete(@Validated @RequestBody GcBusinessOpportunityOperateWebDto gcBusinessOpportunityOperateWebDto) {
        Optional<String> deleteOptional = gcBusinessOpportunityBusiness.delete(gcBusinessOpportunityOperateWebDto.packageOperateParam());
        return deleteOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * 受理商机
     * @param gcBusinessOpportunityOperateWebDto    商机受理参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:41
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("accept")
    public WebResult<Boolean> accept(@Validated @RequestBody GcBusinessOpportunityOperateWebDto gcBusinessOpportunityOperateWebDto) {
        Optional<String> acceptOptional = gcBusinessOpportunityBusiness.accept(gcBusinessOpportunityOperateWebDto.packageOperateParam());
        return acceptOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * 退回商机
     * @param gcBusinessOpportunitySendBackWebDto    商机退回参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:41
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("sendBack")
    public WebResult<Boolean> sendBack(@Validated @RequestBody GcBusinessOpportunitySendBackWebDto gcBusinessOpportunitySendBackWebDto) {
        Optional<String> sendBackOptional = gcBusinessOpportunityBusiness.sendBack(gcBusinessOpportunitySendBackWebDto.packageOperateBackParam());
        return sendBackOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * 获取高呈商机分配数据
     * @param gcBusinessOpportunityOperateWebDto    分配数据获取参数
     * <AUTHOR>
     * @date 2024/5/22 下午1:58
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.GcBusinessOpportunityAssignDataWebView>
     **/
    @PostMapping("assignData")
    public WebResult<GcBusinessOpportunityAssignDataWebView> assignData(@Validated @RequestBody GcBusinessOpportunityOperateWebDto gcBusinessOpportunityOperateWebDto) {
        GcBusinessOpportunityAssignDataBusinessView gcBusinessOpportunityAssignDataBusinessView = gcBusinessOpportunityBusiness.assignData(gcBusinessOpportunityOperateWebDto.packageOperateParam());
        if (gcBusinessOpportunityAssignDataBusinessView == null) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "商机不存在");
        }
        return WebResult.success(CglibUtil.copy(gcBusinessOpportunityAssignDataBusinessView, GcBusinessOpportunityAssignDataWebView.class));
    }

    /* 分配商机
     * @param gcBusinessOpportunityAssignWebDto    商机分配参数
     * <AUTHOR>
     * @date 2024/5/20 下午7:41
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("assign")
    public WebResult<Boolean> assign(@Validated @RequestBody GcBusinessOpportunityAssignWebDto gcBusinessOpportunityAssignWebDto) {
        GcBusinessOpportunityAssignBusinessDto gcBusinessOpportunityAssignBusinessDto = CglibUtil.copy(gcBusinessOpportunityAssignWebDto, GcBusinessOpportunityAssignBusinessDto.class);
        gcBusinessOpportunityAssignBusinessDto.setId(gcBusinessOpportunityAssignWebDto.getSjId());
        gcBusinessOpportunityAssignBusinessDto.setOperator(gcBusinessOpportunityAssignWebDto.getLoginEmployeeId());
        gcBusinessOpportunityAssignBusinessDto.setOperatorName(gcBusinessOpportunityAssignWebDto.getLoginEmployeeName());
        Optional<String> assignOptional = gcBusinessOpportunityBusiness.assign(gcBusinessOpportunityAssignBusinessDto);
        return assignOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

    /**
     * 获取高呈的区域员工树
     * @param loginInfo 登录信息
     * <AUTHOR>
     * @date 2024/5/24 下午3:00
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.util.List < cn.hutool.core.lang.tree.Tree < java.lang.String>>>
     **/
    @PostMapping("area/employee/tree")
    public WebResult<List<Tree<String>>> areaEmployeeTree(@RequestBody LoginInfo loginInfo) {
        List<EmployeeDataThirdView> employeeDataThirdViewList = employeeThirdService.getGcAllSalerData();
        List<TreeNode<String>> nodeList = new ArrayList<>();
        for (EmployeeDataThirdView employeeDataThirdView : employeeDataThirdViewList) {
            nodeList.add(new TreeNode<>(employeeDataThirdView.getAreaId(), "0", employeeDataThirdView.getAreaName(), 1));
            nodeList.add(new TreeNode<>(employeeDataThirdView.getSubId(), employeeDataThirdView.getAreaId(), employeeDataThirdView.getSubName(), 2));
            nodeList.add(new TreeNode<>(employeeDataThirdView.getOrgId(), employeeDataThirdView.getSubId(), employeeDataThirdView.getOrgName(), 3));
            nodeList.add(new TreeNode<>(employeeDataThirdView.getId(), employeeDataThirdView.getOrgId(), employeeDataThirdView.getName(), 4));
        }
        nodeList = nodeList.stream().distinct().collect(Collectors.toList());
        return WebResult.success(TreeUtil.build(nodeList, "0"));
    }

    /**
     * Description: 获取高呈商机客户列表（高呈商务、高呈经理、高呈总监）
     * @author: JiuDD
     * @param queryWebDto 高呈商机客户查询参数
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.GcBusinessOpportunityWebView>>
     * date: 2024/5/20 11:07
     */
    @PostMapping("/custList")
    public WebResult<WebPageInfo<GcBusinessOpportunityWebView>> getGcCustPageList(@RequestBody GcCustQueryWebDto queryWebDto) {
        String loginEmployeeId = queryWebDto.getLoginEmployeeId();
        if (StrUtil.isBlank(loginEmployeeId)) {
            log.warn("获取高呈商机客户列表，员工Id为空，参数为:{}", JSON.toJSONString(queryWebDto));
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, "员工Id为空");
        }
        GcBusinessOpportunityPageCustDto dto = BeanUtil.copyProperties(queryWebDto, GcBusinessOpportunityPageCustDto.class);
        dto.setOperatorPosition(queryWebDto.getLoginPosition());
        dto.setOperatorAreaId(queryWebDto.getLoginAreaId());
        dto.setOperatorSubId(queryWebDto.getLoginSubId());
        dto.setOperatorDeptId(queryWebDto.getLoginOrgId());
        dto.setOperator(loginEmployeeId);
        Page<GcBusinessOpportunityBusinessView> gcCustList = gcBusinessOpportunityBusiness.getGcCustList(dto);
        WebPageInfo<GcBusinessOpportunityWebView> gcBusinessOpportunityWebViewWebPageInfo = WebPageInfo.pageConversion(gcCustList, GcBusinessOpportunityWebView.class);
        gcBusinessOpportunityWebViewWebPageInfo.getList().forEach(e -> {
            e.setShowCurrentInfo(true);
            packageGcBusinessData(e);
        });
        gcBusinessOpportunityWebViewWebPageInfo.getList().forEach(this::packageGcBusinessData);
        return WebResult.success(gcBusinessOpportunityWebViewWebPageInfo);
    }

    /**
     * Description: 获取高呈商机客户的项目列表（高呈商务）
     * @param queryWebDto 客户id必传
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.GcBusinessOpportunityWebView>>
     * date: 2024/5/20 14:14
     * @author: JiuDD
     */
    @PostMapping("/custProjectList")
    public WebResult<WebPageInfo<GcBusinessOpportunityWebView>> getGcCustProjectList(@Validated(GcCustQueryWebDto.GcCustProjectList.class) @RequestBody GcCustQueryWebDto queryWebDto) {
        String loginEmployeeId = queryWebDto.getLoginEmployeeId();
        if (StrUtil.isBlank(loginEmployeeId)) {
            log.warn("获取高呈商机客户的项目列表，员工Id为空，参数为:{}", JSON.toJSONString(queryWebDto));
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, "员工Id为空");
        }
        GcBusinessOpportunityPageCustDto dto = BeanUtil.copyProperties(queryWebDto, GcBusinessOpportunityPageCustDto.class);
        dto.setOperatorPosition(queryWebDto.getLoginPosition());
        dto.setOperatorAreaId(queryWebDto.getLoginAreaId());
        dto.setOperatorSubId(queryWebDto.getLoginSubId());
        dto.setOperatorDeptId(queryWebDto.getLoginOrgId());
        //客户购买的所有的产品 不需分页
        dto.setPageSize(Integer.MAX_VALUE);
        dto.setOperator(loginEmployeeId);
        Page<GcBusinessOpportunityBusinessView> ompCustList = gcBusinessOpportunityBusiness.getGcCustProjectList(dto);
        WebPageInfo<GcBusinessOpportunityWebView> webPageInfo = WebPageInfo.pageConversion(ompCustList, GcBusinessOpportunityWebView.class);
        webPageInfo.getList().forEach(this::packageGcBusinessData);
        return WebResult.success(webPageInfo);
    }

    /**
     * Description: 申请合作（高呈商务）
     * @author: JiuDD
     * @param webDto  商机添加参数
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     * date: 2024/5/21 18:40
     */
    @PostMapping("addGcShangJi")
    public WebResult<Boolean> addGcShangJi(
            @Validated(GcSJCooperationSaveWebDto.GcSalerApplyCooperation.class)
            @RequestBody GcSJCooperationSaveWebDto webDto) {
        Boolean loginGcEmployeeFlag = webDto.getLoginGcEmployeeFlag();
        if (!loginGcEmployeeFlag) {
            log.warn("非高呈人员无权操作", JSON.toJSONString(webDto));
            return WebResult.error(WebCodeMessageEnum.REQUEST_POSITION_NOT_MATCH, "非高呈人员无权操作");
        }
        GcSJCooperationSaveBusinessDto businessDto = CglibUtil.copy(webDto, GcSJCooperationSaveBusinessDto.class);
        businessDto.setGcSalerId(webDto.getLoginEmployeeId());
        businessDto.setOperator(webDto.getLoginEmployeeId());
        businessDto.setOperatorName(webDto.getLoginEmployeeName());
        Optional<String> addOptional = gcBusinessOpportunityBusiness.addGcBusinessOpportunity(businessDto);
        return addOptional.<WebResult<Boolean>>map(s -> WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, s)).orElseGet(() -> WebResult.success(true));
    }

}