package com.ce.scrm.center.web.entity.response;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * web返回分页信息
 * <AUTHOR>
 * @date 2021/6/2 上午11:02
 * @version 1.0.0
 **/
@Data
public class WebPageInfo<T> implements Serializable {

    private static final long serialVersionUID = -6455234157678276815L;
    protected long total;
    private long pageNum;
    private long pageSize;
    private long pages;

    protected List<T> list;

    public static <O, N> WebPageInfo<N> pageConversion(Page<O> page, Class<N> clazz) {
        WebPageInfo<N> pageInfo = new WebPageInfo<>();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getCurrent());
        pageInfo.setPageSize(page.getSize());
        pageInfo.setPages(page.getPages());
        List<O> records = page.getRecords();
        List<N> list = BeanUtil.copyToList(records, clazz);
        pageInfo.setList(list);
        return pageInfo;
    }
}
