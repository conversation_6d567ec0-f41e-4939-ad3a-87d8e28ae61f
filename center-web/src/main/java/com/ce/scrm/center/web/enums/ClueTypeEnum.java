package com.ce.scrm.center.web.enums;

public enum ClueTypeEnum {

//53客服
    KF53("53客服", 1),
    //中企动力测试类型
    ZQDLTEST("中企动力测试类型", 2),
    //惠企添利2025
    HQTL2025("惠企添利2025", 3),

    ;

    /**
     * lable
     */
    private String lable;

    /**
     * value
     */
    private Integer value;

    private ClueTypeEnum(String lableStr, Integer value) {
        this.lable = lableStr;
        this.value = value;
    }

    public String getLable() {
        return lable;
    }

    public void setLable(String lable) {
        this.lable = lable;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
    public static String ByValue(Integer in) {
        for (ClueTypeEnum statusEnum : ClueTypeEnum.values()) {
            if (statusEnum.getValue().equals(in)) {
                return statusEnum.getLable();
            }
        }
        return null;
    }

}
