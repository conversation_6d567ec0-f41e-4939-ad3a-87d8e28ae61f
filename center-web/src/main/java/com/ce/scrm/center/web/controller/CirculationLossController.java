package com.ce.scrm.center.web.controller;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.enums.DeleteFlagEnum;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.business.CirculationLossBusiness;
import com.ce.scrm.center.service.business.CustomerLossLogBusiness;
import com.ce.scrm.center.service.business.ProtectBusiness;
import com.ce.scrm.center.service.business.SmaDictionaryItemBusiness;
import com.ce.scrm.center.service.business.entity.dto.CirculationLossUpdateBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.CmCustProtectBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.CustomerLossLogBusinessDto;
import com.ce.scrm.center.service.business.entity.view.CirculationLossPageBusinessView;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.controller.base.BaseController;
import com.ce.scrm.center.web.entity.dto.CirculationLossReasonWebDto;
import com.ce.scrm.center.web.entity.dto.CirculationLossWebPageDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.CirculationLossWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

import static com.ce.scrm.center.web.entity.response.WebCodeMessageEnum.*;

/**
 * description: 流转流失客户
 * @author: DD.Jiu
 * date: 2024/7/22.
 */
@Login
@Slf4j
@RestController
@RequestMapping("circulationLoss")
public class CirculationLossController extends BaseController {
    @Resource
    private CirculationLossBusiness circulationLossBusiness;
    @Autowired
    private ProtectBusiness protectBusiness;
    @Autowired
    private CmCustProtectService cmCustProtectService;
    @Autowired
    private SmaDictionaryItemBusiness smaDictionaryItemBusiness;
    @Resource
    private CustomerLossLogBusiness customerLossLogBusiness;

    /**
     * Description: 即将流转客户
     *
     * @param webPageDto 查询参数
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo < com.ce.scrm.center.web.entity.view.CirculationLossWebView>>
     * date: 2024/7/22 13:45
     * @author: JiuDD
     */
    @PostMapping("getCirculationList")
    public WebResult<WebPageInfo<CirculationLossWebView>> getCirculationList(@Validated @RequestBody CirculationLossWebPageDto webPageDto) {
        Page<CirculationLossPageBusinessView> pageForReleaseReason = circulationLossBusiness.getCirculationList(webPageDto.packageBusinessDto(webPageDto));
        WebPageInfo<CirculationLossWebView> webPageInfo = WebPageInfo.pageConversion(pageForReleaseReason, CirculationLossWebView.class);
        webPageInfo.getList().forEach(this::packageCirculationLossData);
        return WebResult.success(webPageInfo);
    }

    /**
     * 总监
     * 流失客户
     * 保存流失理由
     *
     * @param webDto
     * @return
     */
    @PostMapping("saveLoseReason")
    public WebResult<Boolean> saveLoseReason(@Validated @RequestBody CirculationLossReasonWebDto webDto) {
        // 查询是否是是分公司总监 只有总监有流失的权限
        boolean isBusinessMajor = PositionUtil.isBusinessMajor(webDto.getLoginPosition());
        if (!isBusinessMajor) {
            return WebResult.error(REQUEST_POSITION_NOT_MATCH);
        }
        // 查询操作的数据是否是自己对应分司
        CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, webDto.getCustId()).one();
        if (Objects.isNull(cmCustProtect)) {
            return WebResult.error(DATA_NOT_EXIST);
        }
        if (!webDto.getLoginSubId().equals(cmCustProtect.getSubcompanyId())){
            return WebResult.error(REQUEST_POSITION_NOT_MATCH);
        }

        CirculationLossUpdateBusinessDto circulationLossUpdateBusinessDto = new CirculationLossUpdateBusinessDto();
        BeanUtils.copyProperties(webDto, circulationLossUpdateBusinessDto);
        circulationLossUpdateBusinessDto.setDeleteFlag(DeleteFlagEnum.DELETE.getCode());
        AtomicReference<String> reasonName = new AtomicReference<>("");
        smaDictionaryItemBusiness.findByCode(webDto.getReason()).ifPresent(reason -> reasonName.set(reason.getName()));
        circulationLossUpdateBusinessDto.setReason(reasonName.get());
        boolean updateFlag = circulationLossBusiness.updateCirculationLoss(circulationLossUpdateBusinessDto);
        if (!updateFlag) {
            log.error("保存流失理由，更新CirculationLoss表出错！{}", JSONObject.toJSONString(webDto));
            return WebResult.error(DATA_NOT_EXIST);
        }
        // 保存到 流失记录表
        CustomerLossLogBusinessDto lossLogBusinessDto = new CustomerLossLogBusinessDto();
        lossLogBusinessDto.setCustId(webDto.getCustId());
        lossLogBusinessDto.setReason(webDto.getReason());
        boolean saveFlag = customerLossLogBusiness.saveCustomerLossLog(lossLogBusinessDto);
        if (!saveFlag) {
            log.error("总监流失客户保存流失log到customer_loss_log表出错！{}", JSONObject.toJSONString(webDto));
            return WebResult.error(RPC_EXCEPTION);
        }
        CmCustProtectBusinessDto cmCustProtectBusinessDto = new CmCustProtectBusinessDto();
        BeanUtils.copyProperties(webDto, cmCustProtectBusinessDto);
        updateFlag = protectBusiness.updateLossCustProtect(cmCustProtectBusinessDto, cmCustProtect, reasonName.get());
        if (!updateFlag) {
            log.error("保存流失理由，更新CmCustProtectBusiness表出错！{}", JSONObject.toJSONString(webDto));
            return WebResult.error(DATA_NOT_EXIST);
        }
        return WebResult.success(updateFlag);
    }
}
