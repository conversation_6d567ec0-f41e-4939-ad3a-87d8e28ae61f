package com.ce.scrm.center.web.entity.dto.abm;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerLeadsWebDto extends LoginInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据来源 0:crm原有渠道  1:营销活动  2:手动录入
     */
    private Integer dataFromSource;

    /**
     * 省
     */
    private String provinceCode;
    /**
     *  市
     */
    private String cityCode;
    /**
     * 区
     */
    private String areaCode;

    /**
     * 详细地址
     */
    private String address;
    /**
     * 线索类型
     */
    private String clueType;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 活动
     */
    private String activity;

    /**
     * 端口
     */
    private String clientType;

    /**
     * 意向产品
     */
    private List<Integer> intentionProduct;

    /**
     * 需求备注
     */
    private String demandRemark;

    /**
     * 附件url
     */
    private List<ApplicationTransferWebDto.FileIn> attachmentUrl;



    /**
     * 联系人
     */
    private String linkmanName;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 客户名称
     */
    private String customerName;


    /**
     * 性别：1、未知，2、男，3、女
     */
    private Integer gender;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 职位
     */
    private String position;

    /**
     * 微信账号
     */
    private String weChat;
    /**
     * 固定电话
     */
    private String fixedPhone;



}