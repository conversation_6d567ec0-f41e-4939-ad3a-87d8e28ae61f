package com.ce.scrm.center.web.enums;

public enum IntentionPorductEnum {


    BUILD_SITE("建站", 1),
    PROMOTE("推广", 2),
    SOCIAL_MEDIA("社媒", 3),
    CRM("CRM", 4),
    ;

    /**
     * lable
     */
    private String lable;

    /**
     * value
     */
    private Integer value;

    private IntentionPorductEnum(String lableStr, Integer value) {
        this.lable = lableStr;
        this.value = value;
    }

    public String getLable() {
        return lable;
    }

    public void setLable(String lable) {
        this.lable = lable;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
    public static String parseByValue(Integer in) {
        for (IntentionPorductEnum statusEnum : IntentionPorductEnum.values()) {
            if (statusEnum.getValue().equals(in)) {
                return statusEnum.getLable();
            }
        }
        return null;
    }

}
