package com.ce.scrm.center.web.controller;

import cn.ce.cesupport.emp.service.EmployeeAppService;
import cn.ce.cesupport.emp.service.OrgAppService;
import cn.ce.cesupport.emp.vo.EmployeeVo;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.business.OrgInfoBusiness;
import com.ce.scrm.center.service.business.entity.dto.org.OrgChildrenQueryBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.org.QueryOrgTreeBusinessDto;
import com.ce.scrm.center.service.business.entity.view.org.OrgChildrenQueryBusinessView;
import com.ce.scrm.center.service.business.entity.view.org.OrgEmployeeBusinessView;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.OrgChildrenQueryWebDto;
import com.ce.scrm.center.web.entity.dto.QueryOrgTreeWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.OrgChildrenQueryWebView;
import com.ce.scrm.center.web.entity.view.OrgEmployeeView;
import com.ce.scrm.center.web.entity.view.OrgEmployeeWebView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description: 组织机构信息
 * @Author: lijinpeng
 * @Date: 2024/11/21 11:50
 */
@RestController
@Login(checkLogin = false)
@Slf4j
@RequestMapping("org")
public class OrgInfoController {

    @Resource
    private OrgInfoBusiness orgInfoBusiness;

    @DubboReference
    private OrgAppService orgAppService;

    //'AREA'  'BU' 'DEPT' 'SUB'
    private static final List<String> types = Arrays.asList("AREA", "BU", "DEPT", "SUB");

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private static final String ORG_TYPE_KEY = "CRM:AI:org:employee:list";

    @DubboReference
    private EmployeeAppService employeeAppService;

    @Autowired
    private EmployeeThirdService employeeThirdService;


    /*
     * @Description 根据id获取其本身以及子集数据
     * <AUTHOR>
     * @date 2024/11/21 15:10
     * @param orgChildrenQueryWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.OrgChildrenQueryWebView>
     */
    @PostMapping("getChildrenList")
    public WebResult<OrgChildrenQueryWebView> getChildrenList(@RequestBody OrgChildrenQueryWebDto orgChildrenQueryWebDto) {
        OrgChildrenQueryBusinessDto orgChildrenQueryBusinessDto = BeanUtil.copyProperties(orgChildrenQueryWebDto, OrgChildrenQueryBusinessDto.class);
        OrgChildrenQueryBusinessView orgChildrenQueryBusinessView = orgInfoBusiness.getChildrenList(orgChildrenQueryBusinessDto);
        OrgChildrenQueryWebView orgChildrenQueryWebView = BeanUtil.copyProperties(orgChildrenQueryBusinessView, OrgChildrenQueryWebView.class);
        return WebResult.success(orgChildrenQueryWebView);
    }

    @PostMapping("getOrgAndEmployeeTree")
    @Login
    public WebResult<List<OrgEmployeeWebView>> getOrgAndEmployeeTree(@RequestBody QueryOrgTreeWebDto queryOrgTreeWebDto) {
        QueryOrgTreeBusinessDto queryOrgTreeBusinessDto = BeanUtil.copyProperties(queryOrgTreeWebDto, QueryOrgTreeBusinessDto.class);
        List<OrgEmployeeBusinessView> businessResult = orgInfoBusiness.getOrgAndEmployeeTree(queryOrgTreeBusinessDto);
        List<OrgEmployeeWebView> result = BeanUtil.copyToList(businessResult, OrgEmployeeWebView.class);
        return WebResult.success(result);
    }


    @PostMapping("findAllOrgs")
    public WebResult<List<OrgEmployeeView>> findAllOrgs(@RequestBody(required = false) Map<String, Object> body) {
        String employeeInfo = "0";
        if (null != body) {
            employeeInfo = body.getOrDefault("employeeInfo", "").toString();
        }
        String value = stringRedisTemplate.opsForValue().get(ORG_TYPE_KEY + ":" + employeeInfo);
        if (StringUtils.hasText(value)) {
            List<OrgEmployeeView> orgEmployeeViews = JSONObject.parseArray(value, OrgEmployeeView.class);
            return WebResult.success(orgEmployeeViews);
        }
        List<OrgEmployeeView> result = buildOrgTree("-1", employeeInfo);
        stringRedisTemplate.opsForValue().set(ORG_TYPE_KEY + ":" + employeeInfo, JSONObject.toJSONString(result), 1, TimeUnit.DAYS);
        return WebResult.success(result);
    }

    @GetMapping("delAllOrgs")
    public WebResult<?> delAllOrgs(@RequestParam("employeeInfo") String employeeInfo) {
        stringRedisTemplate.delete(ORG_TYPE_KEY + ":" + employeeInfo);
        return WebResult.success();
    }

    public List<OrgEmployeeView> buildOrgTree(String id, String employeeInfo) {
        // 先获取所有节点的扁平列表
        List<OrgEmployeeView> flatList = new ArrayList<>();
        collectOrgTreeFlat(id, flatList);
        List<OrgEmployeeView> employeeList = new ArrayList<>();
        if (StringUtils.hasText(employeeInfo) && Objects.equals(employeeInfo, "1")) {
            // 获取最下一级的 dept
            List<OrgEmployeeView> deptList = flatList.stream()
                    .filter(node -> "DEPT".equals(node.getType()))
                    .collect(Collectors.toList());
            List<EmployeeVo> employeeVos = employeeAppService.selectListByOrgIds(deptList.stream().map(OrgEmployeeView::getId).collect(Collectors.toList()));
            for (OrgEmployeeView orgEmployeeView : flatList) {
                if (deptList.contains(orgEmployeeView)) {
                    List<OrgEmployeeView> orgEmployeeViews = employeeVos.stream().filter(T -> T.getOrgId().equals(orgEmployeeView.getId())).map(E -> new OrgEmployeeView(String.valueOf(E.getId()), E.getName(), orgEmployeeView.getId() + "org", "employee", E.getPosition(), null)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(orgEmployeeViews)) {
                        orgEmployeeView.setChildren(orgEmployeeViews);
                        orgEmployeeView.setId(orgEmployeeView.getId() + "org");
                        orgEmployeeView.setParentId(orgEmployeeView.getParentId() + "org");
                        employeeList.add(orgEmployeeView);
                    }
                } else {
                    Optional<EmployeeLiteThirdView> employeeLiteThirdView = employeeThirdService.getOrgLeader(orgEmployeeView.getId());
                    employeeLiteThirdView.ifPresent(liteThirdView -> orgEmployeeView.setChildren(new ArrayList<>(Collections.singletonList(new OrgEmployeeView(liteThirdView.getId(), liteThirdView.getName(), orgEmployeeView.getId() + "org", "employee", liteThirdView.getPosition(), null)))));
                    orgEmployeeView.setId(orgEmployeeView.getId() + "org");
                    orgEmployeeView.setParentId(orgEmployeeView.getParentId() + "org");
                    employeeList.add(orgEmployeeView);
                }
            }
        } else {
            employeeList = flatList;
        }
        // 构建 id -> OrgView 映射
        Map<String, OrgEmployeeView> idMap = employeeList.stream()
                .collect(Collectors.toMap(OrgEmployeeView::getId, v -> v));
        // 构建树结构
        List<OrgEmployeeView> treeList = new ArrayList<>();
        for (OrgEmployeeView node : employeeList) {
            if (node.getParentId() == null || !idMap.containsKey(node.getParentId())) {
                // 顶层节点
                treeList.add(node);
            } else {
                // 子节点加入父节点的 children 列表
                OrgEmployeeView parent = idMap.get(node.getParentId());
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(node);
            }
        }
        return treeList;
    }

    // 递归收集所有节点为扁平列表
    private void collectOrgTreeFlat(String id, List<OrgEmployeeView> flatList) {
        OrgChildrenQueryBusinessDto queryWebDto = new OrgChildrenQueryBusinessDto();
        queryWebDto.setId(id);
        OrgChildrenQueryBusinessView childrenList = orgInfoBusiness.getChildrenList(queryWebDto);
        if (childrenList != null) {
            if (childrenList.getOrgVo() != null) {
                OrgThirdDto dto = childrenList.getOrgVo();
                if (dto.getType().equals("AREA")) {
                    dto.setParentId("9");
                }
                OrgEmployeeView view = new OrgEmployeeView();
                view.setId(dto.getId());
                view.setName(dto.getName());
                view.setType(dto.getType());
                view.setParentId(dto.getParentId());
                flatList.add(view);
            }
            if (CollectionUtils.isNotEmpty(childrenList.getChildren())) {
                for (OrgThirdDto child : childrenList.getChildren()) {
                    if (child.getType().equals("AREA")) {
                        child.setParentId("9");
                    }
                    collectOrgTreeFlat(child.getId(), flatList);
                }
            }
        }
    }


}
