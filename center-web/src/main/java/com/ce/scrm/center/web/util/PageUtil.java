package com.ce.scrm.center.web.util;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ce.scrm.center.dubbo.entity.response.DubboPageInfo;

import java.util.List;

public class PageUtil {

    /**
     * IPage转换为DubboPage
     * @param page
     * @param clazz
     * @param <O>
     * @param <N>
     * @return
     */
    public static <O, N> DubboPageInfo<N> pageConversion(IPage<O> page, Class<N> clazz) {
        DubboPageInfo<N> pageInfo = new DubboPageInfo();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getCurrent());
        pageInfo.setPageSize(page.getSize());
        pageInfo.setPages(page.getPages());
        List<O> records = page.getRecords();
        List<N> list = BeanUtil.copyToList(records, clazz);
        pageInfo.setList(list);
        return pageInfo;
    }
}