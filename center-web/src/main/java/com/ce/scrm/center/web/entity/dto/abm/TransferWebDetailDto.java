package com.ce.scrm.center.web.entity.dto.abm;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransferWebDetailDto  extends LoginInfo implements Serializable {
    /**
     * 申请单ID
     */
    private Long id;

    /**
     * 备注信息
     */
    private String remarks;
    /**
     * 处理结果
     */
    private Integer processingResult;
}
