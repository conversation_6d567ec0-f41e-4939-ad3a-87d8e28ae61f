package com.ce.scrm.center.web.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/21
 */
@Data
public class EmployeeOperationLogPageWebDto implements Serializable {

    /**
     * 员工id
     */
    private String empId;

    /**
     * customerId
     */
    private String customerId;

    /**
     * customerName
     */
    private String customerName;

    /**
     * 查询区间-开始时间
     */
    private Date beginTime;

    /**
     * 查询区间-结束时间
     */
    private Date endTime;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 10;
}
