package com.ce.scrm.center.web.controller.histroy;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.business.TradeshowHistoryBusiness;
import com.ce.scrm.center.service.business.entity.dto.TradeshowHistoryBusinessDto;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.TradeshowHistoryWebDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/***
 * 参展历史
 * <AUTHOR>
 * @date 2025/4/28 15:55
 * @version 1.0.0
 * @return 
**/
@Slf4j
@RestController
@RequestMapping("/tradeshow/history")
@Login
public class TradeshowHistoryController {

    @Autowired
    private TradeshowHistoryBusiness tradeshowHistoryBusiness;

    /***
     * 导入公司展会记录
     * @param tradeshowHistoryWebDto
     * <AUTHOR>
     * @date 2025/4/28 16:01
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult
    **/
    @PostMapping("/import")
    public WebResult<String> importTradeshow(@RequestBody TradeshowHistoryWebDto tradeshowHistoryWebDto) {
        Transaction t = Cat.newTransaction("Controller", "TradeshowHistoryController.importTradeshow");
        log.info("importTradeshow req={}", JSONObject.toJSONString(tradeshowHistoryWebDto));
        try {
            TradeshowHistoryBusinessDto tradeshowHistoryBusinessDto = new TradeshowHistoryBusinessDto();
            BeanUtil.copyProperties(tradeshowHistoryWebDto, tradeshowHistoryBusinessDto);
            Optional<String> stringOptional = tradeshowHistoryBusiness.importTradeshowHistory(tradeshowHistoryBusinessDto);
            return stringOptional.<WebResult<String>>map(s -> WebResult.error(WebCodeMessageEnum.RPC_EXCEPTION, s)).orElseGet(WebResult::success);
        } catch (Exception e) {
            log.error("导入公司展会记录,参数={},异常{}", JSONObject.toJSONString(tradeshowHistoryWebDto), JSONObject.toJSONString(e.getMessage()));
            t.setStatus(e);
            Cat.logError(e);
            return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
        } finally {
            t.complete();
        }
    }

}
