package com.ce.scrm.center.web.controller;

import cn.ce.cesupport.base.vo.ScrmResult;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.dao.entity.CustomerLossLog;
import com.ce.scrm.center.dao.service.CustomerLossLogService;
import com.ce.scrm.center.dubbo.api.CboCustomerDubbo;
import com.ce.scrm.center.dubbo.entity.dto.CboCustomerDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.CboCustomerPayDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.CboProtectDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.view.CboContactPersonDubboView;
import com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.util.SignatureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 跨境专用
 * <AUTHOR>
 * @description
 * @date 2024/11/14
 */

@Slf4j
@RestController
@RequestMapping("/cbo")
public class CboCustomerController {

    @DubboReference(group = "scrm-center-api", version = "1.0.0", check = false)
    private CboCustomerDubbo cboCustomerDubbo;

    @Value("${third.customer.secret}")
    private String secret;

    @Resource
    private CustomerLossLogService customerLossLogService;


    /***
     * 保护关系查询
     * @param cboProtectDubboDto
     * <AUTHOR>
     * @date 2024/11/14 17:46
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.response.DubboPageInfo<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>>
     **/
    @PostMapping("/getProtectByCondition")
    public WebResult<WebPageInfo<CboProtectDubboView>> getProtectByCondition(@RequestBody CboProtectDubboDto cboProtectDubboDto, @RequestHeader("sign") String sign){
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(cboProtectDubboDto.getSalerId(), secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        DubboResult<DubboPageInfo<CboProtectDubboView>> protectByCondition = cboCustomerDubbo.getProtectByCondition(cboProtectDubboDto);
        WebPageInfo<CboProtectDubboView> webPageInfo = BeanUtil.copyProperties(protectByCondition.getData(), WebPageInfo.class);
        if (protectByCondition.checkSuccess() && protectByCondition.getData()!=null){
            webPageInfo.setList(protectByCondition.getData().getList());
        }
        return WebResult.success(webPageInfo);
    }

    /**
     * 根据custId查询流失客户信息
     * @param customerId
     * @param sign
     * @return
     */
    @GetMapping("/getCustomerLossDateList")
    public WebResult<List<Date>> getCustomerLossDateList(@RequestParam(value = "customerId") String customerId, @RequestHeader("sign") String sign){
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(customerId, secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        List<CustomerLossLog> list = customerLossLogService.lambdaQuery().eq(CustomerLossLog::getCustId, customerId).list();
        List<Date> collect = list.stream().map(CustomerLossLog::getPreDate).collect(Collectors.toList());
        return WebResult.success(collect);
    }

    /***
     * 联系人查询
     * @param customerId
     * <AUTHOR>
     * @date 2024/11/14 17:47
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.center.dubbo.entity.view.CboContactPersonDubboView>>
     **/
    @GetMapping("/getContactPersonByCustomerId/{customerId}")
    public WebResult<List<CboContactPersonDubboView>> getContactPersonByCondition(@PathVariable("customerId") String customerId,@RequestHeader("sign") String sign){
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(customerId, secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        DubboResult<List<CboContactPersonDubboView>> contactPersonByCondition = cboCustomerDubbo.getContactPersonByCondition(customerId);
        WebResult<List<CboContactPersonDubboView>> webPageInfo = BeanUtil.copyProperties(contactPersonByCondition, WebResult.class);
        return webPageInfo;
    }


    /***
     * 根据客户ID查询保护关系
     * @param customerId
     * @param sign
     * <AUTHOR>
     * @date 2024/12/9 10:58
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>
    **/
    @GetMapping("/getProtectByCustomerId/{customerId}")
    public WebResult<CboProtectDubboView> getProtectByCustomerId(@PathVariable("customerId") String customerId, @RequestHeader("sign") String sign){
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(customerId, secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        CboProtectDubboDto cboProtectDubboDto = new CboProtectDubboDto();
        cboProtectDubboDto.setCustomerId(customerId);
        DubboResult<CboProtectDubboView> dubboResult = cboCustomerDubbo.getProtectByCustomerId(cboProtectDubboDto);
        WebResult<CboProtectDubboView> webResult = BeanUtil.copyProperties(dubboResult, WebResult.class);
        return webResult;
    }

    /***
     * 根据客户名称查询保护关系
     * @param customerName
     * @param sign
     * <AUTHOR>
     * @date 2024/12/9 10:58
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>
     **/
    @GetMapping("/getProtectByCustomerName/{customerName}")
    public WebResult<CboProtectDubboView> getProtectByCustomerName(@PathVariable("customerName") String customerName, @RequestHeader("sign") String sign){
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(customerName, secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        CboProtectDubboDto cboProtectDubboDto = new CboProtectDubboDto();
        cboProtectDubboDto.setCustomerName(customerName);
        DubboResult<CboProtectDubboView> dubboResult = cboCustomerDubbo.getProtectByCustomerName(cboProtectDubboDto);
        WebResult<CboProtectDubboView> webResult = BeanUtil.copyProperties(dubboResult, WebResult.class);
        return webResult;
    }

    /***
     * 根据客户名称查询客户信息
     * @param cboCustomerDubboDto
     * @param sign
     * <AUTHOR>
     * @date 2024/12/9 10:58
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>
     **/
    @PostMapping("/getCustomerInfoByCondition")
    public WebResult<CboProtectDubboView> getCustomerInfoByCondition(@RequestBody CboCustomerDubboDto cboCustomerDubboDto, @RequestHeader("sign") String sign){
        if (cboCustomerDubboDto==null || (StringUtils.isBlank(cboCustomerDubboDto.getCustomerId()) && StringUtils.isBlank(cboCustomerDubboDto.getCustomerName()))){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        if (StringUtils.isNotBlank(cboCustomerDubboDto.getCustomerId())){
            String expectedSignature = SignatureUtils.generateSignature(cboCustomerDubboDto.getCustomerId(), secret);
            if (!Objects.equals(expectedSignature,sign)){
                return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
            }
        }else {
            if (StringUtils.isNotBlank(cboCustomerDubboDto.getCustomerName())) {
                String expectedSignature = SignatureUtils.generateSignature(cboCustomerDubboDto.getCustomerName(), secret);
                if (!Objects.equals(expectedSignature, sign)) {
                    return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
                }
            }
        }
        DubboResult<CboProtectDubboView> dubboResult = cboCustomerDubbo.getCustomerInfoByCondition(cboCustomerDubboDto);
        WebResult<CboProtectDubboView> webResult = BeanUtil.copyProperties(dubboResult, WebResult.class);
        return webResult;
    }


    /***
     * 支付完成
     * @param cboCustomerPayDubboDto
     * @param sign
     * <AUTHOR>
     * @date 2024/12/17 09:13
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>
    **/
    @PostMapping("/orderPay")
    public WebResult<Boolean> orderPay(@RequestBody CboCustomerPayDubboDto cboCustomerPayDubboDto, @RequestHeader("sign") String sign){
        if (cboCustomerPayDubboDto==null || StringUtils.isBlank(cboCustomerPayDubboDto.getCustomerId()) || StringUtils.isBlank(cboCustomerPayDubboDto.getSalerId()) || cboCustomerPayDubboDto.getPayTime()==null){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(cboCustomerPayDubboDto.getCustomerId(), secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        DubboResult<Boolean> dubboResult = cboCustomerDubbo.orderPay(cboCustomerPayDubboDto);
        WebResult<Boolean> webResult = BeanUtil.copyProperties(dubboResult, WebResult.class);
        return webResult;
    }
}
