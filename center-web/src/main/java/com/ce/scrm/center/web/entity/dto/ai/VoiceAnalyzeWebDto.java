package com.ce.scrm.center.web.entity.dto.ai;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class VoiceAnalyzeWebDto extends LoginInfo implements Serializable {

    private Long id;
    /**
     * 员工 ID
     */
    private String salerId;


    private String custName;


    /**
     * 客户 cid
     */
    private String custId;


    /**
     * 分公司ID
     */
    private String subId;


    /**
     * 事业部ID
     */
    private String buId;


    /**
     * 组织ID
     */
    private String deptId;

    /**
     * 区域ID
     */
    private String areaId;


    /**
     * 分析类型 1 跟进记录  0:普通分析  逗号分割
     */
    private String voiceType;

    /**
     * 1 语音识别中 2 识别成功 3 识别失败
     */
    private Integer voiceStatus;

    /**
     * 0 未开始   1 进行中 2 ai 分析完成
     */
    private Integer analyzeStatus;

    /**
     * 页码
     */
    private Integer currentPage = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 10;


}
