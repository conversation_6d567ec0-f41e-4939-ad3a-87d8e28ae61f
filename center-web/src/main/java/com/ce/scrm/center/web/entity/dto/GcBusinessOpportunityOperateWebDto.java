package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.service.business.entity.dto.GcBusinessOpportunityOperateBusinessDto;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 高呈商机操作参数
 * <AUTHOR>
 * @date 2024/5/16 下午4:45
 * @version 1.0.0
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class GcBusinessOpportunityOperateWebDto extends LoginInfo implements Serializable {

    /**
     * 高呈商机ID（更新必传）
     */
    @NotNull(message = "商机ID不能为空")
    private Long sjId;

    /**
     * 拼装商机处理参数
     * <AUTHOR>
     * @date 2024/5/21 下午2:13
     * @return com.ce.scrm.center.service.business.entity.dto.GcBusinessOpportunityOperateBusinessDto
     **/
    public GcBusinessOpportunityOperateBusinessDto packageOperateParam() {
        GcBusinessOpportunityOperateBusinessDto gcBusinessOpportunityOperateBusinessDto = new GcBusinessOpportunityOperateBusinessDto();
        gcBusinessOpportunityOperateBusinessDto.setId(this.getSjId());
        gcBusinessOpportunityOperateBusinessDto.setOperator(this.getLoginEmployeeId());
        gcBusinessOpportunityOperateBusinessDto.setOperatorName(this.getLoginEmployeeName());
        return gcBusinessOpportunityOperateBusinessDto;
    }
}