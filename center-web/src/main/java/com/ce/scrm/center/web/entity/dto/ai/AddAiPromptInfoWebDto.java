package com.ce.scrm.center.web.entity.dto.ai;

import com.ce.scrm.center.dao.entity.AiPromptAuthInfo;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 添加提示词信息
 * @Author: lijinpeng
 * @Date: 2025/2/20 09:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AddAiPromptInfoWebDto extends LoginInfo implements Serializable {

    @NotNull
    private String title;

    @NotNull
    private String content;

    /**
     * ce 中企 gboss 跨境
     */
    private String company;

    private Integer promptType;

    private String remark;

    private List<AiPromptAuthInfo> aiPromptAuthInfos;


}
