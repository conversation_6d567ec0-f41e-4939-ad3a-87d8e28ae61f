package com.ce.scrm.center.web.entity.dto.favorite;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 收藏夹列表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FavoritesPageWebDto extends LoginInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 页码
	 */
	private Integer pageNum = 1;

	/**
	 * 每页数量
	 */
	private Integer pageSize = 10;

	/**
	 * 来源 0-收藏夹列表  1-待办任务-分配任务-收藏夹列表
	 */
	@Range(min = 0, max = 1, message = "请输入正确的查询类型")
	private Integer from = 0;

	/**
	 * 来源
	 */
	private String lablFrom;

	/**
	 * 运营行业编码
	 */
	private String industryZhongqi;

	/**
	 * 开始时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDateTime endTime;

	/**
	 * 分司id
	 */
	private String subId;

	/**
	 * 事业部id
	 */
	private String buId;

	/**
	 * 部门id
	 */
	private String deptId;

	/**
	 * 商务id
	 */
	private String salerId;

	/**
	 * 商务操作类型
	 */
	private String bussOppType;

	/**
	 * 客户id
	 */
	private String custId;

	/**
	 * 客户名称
	 */
	private String custName;

	/**
	 * 地址
	 */
	private String address;

	/**
	 * 任务
	 */
	private String clueMission;

	/**
	 * 二级国标行业
	 */
	private String secondIndustry;

	/**
	 * 二级国标行业
	 */
	private String salerClueFrom;


	/**
	 * 二级获客来源
	 */
	private String salerClueFromSub;

	/**
	 * 跟进状态
	 */
	private String clueVisitStage;

	/**
	 * 下次跟进日期
	 */
	private String nextVisitDate;
}
