package com.ce.scrm.center.web.entity.dto.openapi;

import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 跨境录音推送
 * @Author: lijinpeng
 * @Date: 2025/3/17 10:20
 */
@Data
public class SaveRecordKjWebDto implements Serializable {

    /**
     * 企业ID
     */
    private String enterpriseId;

    /**
     * 通话ID
     */
    private String mainUniqueId;

    /**
     * 录音时长
     */
    private String recordDuration;

    /**
     * 录音文件名
     */
    private String recordFileIndex;

    /**
     * mp3录音地址
     */
    private String preSignUrl;

    /**
     * wav客户侧录音地址
     */
    private String preSignClientUrl;

    /**
     * wav座席侧录音地址
     */
    private String preSignAgentUrl;

    /**
     * wav录音地址
     */
    private String preSignWavUrl;

}
