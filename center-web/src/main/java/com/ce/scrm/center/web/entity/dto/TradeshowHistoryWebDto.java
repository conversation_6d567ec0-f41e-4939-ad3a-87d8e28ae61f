package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * @classname TradeshowHistoryWebDto
 * @description 参展历史
 * @date 2025/1/20 18:10
 * @create by gaomeijing
 */
@Data
public class TradeshowHistoryWebDto extends LoginInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 展会名称
     */
    private String tradeshowName;
    /**
     * 展会时间
     */
    private String tradeshowTime;
}
