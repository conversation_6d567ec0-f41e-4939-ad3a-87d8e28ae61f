package com.ce.scrm.center.web.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * description: 手动分配商机到高呈分司或部门（市场部） web参数
 * @author: DD.Jiu
 * date: 2024/6/6.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AssignBusinessOpportunityToGcWebDto extends LoginInfo implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 来源：GcSjSourceEnum
     */
    @NotNull(message = "商机来源不能为空")
    private Integer source;
    /**
     * 高呈分司ID
     */
    @NotBlank(message = "高呈分司ID不能为空")
    private String gcSubId;
    /**
     * 高呈区域ID
     */
    @NotBlank(message = "高呈区域ID不能为空")
    private String gcAreaId;
}
