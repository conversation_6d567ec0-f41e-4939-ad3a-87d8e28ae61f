package com.ce.scrm.center.web.controller.abm;

import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ce.scrm.center.dao.entity.SjAssignDetail;
import com.ce.scrm.center.dao.service.SjAssignDetailService;
import com.ce.scrm.center.service.business.abm.BizOppDistributeBusiness;
import com.ce.scrm.center.service.business.abm.SdrPushSaleReviewBusiness;
import com.ce.scrm.center.service.business.entity.dto.SdrPushSaleReviewBusinessAddDto;
import com.ce.scrm.center.service.business.entity.dto.SdrPushSaleReviewBusinessUpdateDto;
import com.ce.scrm.center.service.business.entity.dto.SdrPushSaleReviewBusinessViewDto;
import com.ce.scrm.center.service.business.entity.dto.abm.ReceiptConfirmDto;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.abm.*;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.abm.SdrPushSaleReviewWebView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 跨境ABM商机流转
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-07-23
 */
@Slf4j
@Login
@RestController
@RequestMapping("/abm/")
public class AbmBizOppDistributeController {

    @Resource
    private SdrPushSaleReviewBusiness sdrPushSaleReviewBusiness;

    @Resource
    BizOppDistributeBusiness bizOppDistributeBusiness;

    @Resource
    private SjAssignDetailService sjAssignDetailService;

    /***
     * 转商机
     * @param abmAddReviewCheckWebDto
     * <AUTHOR>
     * @date 2025/8/7 14:53
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.String>
     **/
    @PostMapping("/review/add")
    public WebResult<String> addReview(@Valid @RequestBody AbmAddReviewCheckWebDto abmAddReviewCheckWebDto) {
        log.info("转商机 req={}", JSON.toJSONString(abmAddReviewCheckWebDto));
        if (Objects.isNull(abmAddReviewCheckWebDto)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
        String customerId = abmAddReviewCheckWebDto.getCustomerId();
        if (StringUtils.isBlank(customerId)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION,"客户id不能为空");
        }

        String customerName = abmAddReviewCheckWebDto.getCustomerName();
        if (StringUtils.isBlank(customerName)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION,"客户名称不能为空");
        }
        if (sdrPushSaleReviewBusiness.countToBeReview(customerId) > 0) {
            return WebResult.error(WebCodeMessageEnum.RPC_EXCEPTION, "已存在待审的审批申请");
        }

        SdrPushSaleReviewBusinessAddDto reviewBusinessAddDto = BeanUtil.copyProperties(abmAddReviewCheckWebDto, SdrPushSaleReviewBusinessAddDto.class);
        reviewBusinessAddDto.setAttachment(JSON.toJSONString(abmAddReviewCheckWebDto.getAttachment()));
        String loginPosition = abmAddReviewCheckWebDto.getLoginPosition();
        // 审核来源类型 1：SDR 2：CC 必填非空
        if (PositionUtil.isSdr(loginPosition)) {
            reviewBusinessAddDto.setReviewSrcType(1);
        } else if (PositionUtil.isCc(loginPosition)) {
            reviewBusinessAddDto.setReviewSrcType(2);
        } else {
            return WebResult.error(WebCodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }
        reviewBusinessAddDto.setCreatedId(abmAddReviewCheckWebDto.getLoginEmployeeId());
        reviewBusinessAddDto.setCreatedName(abmAddReviewCheckWebDto.getLoginEmployeeName());
        Optional<String> optionalString = sdrPushSaleReviewBusiness.add(reviewBusinessAddDto);
        return optionalString.<WebResult<String>>map(s -> WebResult.error(WebCodeMessageEnum.RPC_EXCEPTION, "添加审核记录败," + s)).orElseGet(WebResult::success);
    }

    /***
     * 审核
     * @param abmReviewCheckWebDto
     * <AUTHOR>
     * @date 2025/8/7 22:05
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.String>
     **/
    @PostMapping("/review/check")
    public WebResult<String> cehckReview(@Valid @RequestBody AbmReviewCheckWebDto abmReviewCheckWebDto) {
        log.info("审核 req={}", JSON.toJSONString(abmReviewCheckWebDto));
        String loginEmployeeId = abmReviewCheckWebDto.getLoginEmployeeId();
        if (StrUtil.isEmpty(loginEmployeeId)) {
            return WebResult.error(WebCodeMessageEnum.WX_LOGIN_EXPIRED);
        }

        SdrPushSaleReviewBusinessUpdateDto reviewBusinessUpdateDto = BeanUtil.copyProperties(abmReviewCheckWebDto, SdrPushSaleReviewBusinessUpdateDto.class);
        reviewBusinessUpdateDto.setReviewId(loginEmployeeId);
        Optional<String> review = sdrPushSaleReviewBusiness.review(reviewBusinessUpdateDto);
        return review.<WebResult<String>>map(s -> WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION, s)).orElseGet(WebResult::success);
    }


    /***
     * 审核
     * @param lastReviewDetailWebDto
     * <AUTHOR>
     * @date 2025/8/7 22:05
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.String>
     **/
    @PostMapping("/review/getLast")
    public WebResult<SdrPushSaleReviewWebView> getLastTimeReview(@Valid @RequestBody AbmLastReviewDetailWebDto lastReviewDetailWebDto) {
        log.info("获取最近一次审核记录 req={}", JSON.toJSONString(lastReviewDetailWebDto));
        String loginEmployeeId = lastReviewDetailWebDto.getLoginEmployeeId();
        if (StrUtil.isEmpty(loginEmployeeId)) {
            return WebResult.error(WebCodeMessageEnum.WX_LOGIN_EXPIRED);
        }
        String customerId = lastReviewDetailWebDto.getCustomerId();

        Optional<SdrPushSaleReviewBusinessViewDto> lastTimeReview = sdrPushSaleReviewBusiness.getLastTimeReview(loginEmployeeId, customerId);
        if (lastTimeReview.isPresent()) {
            SdrPushSaleReviewBusinessViewDto businessViewDto = lastTimeReview.get();
            log.info("businessViewDto={}", JSON.toJSONString(businessViewDto));
            SdrPushSaleReviewWebView sdrPushSaleReviewWebView = new SdrPushSaleReviewWebView();
            sdrPushSaleReviewWebView.setId(businessViewDto.getId());
            sdrPushSaleReviewWebView.setReviewSrcType(businessViewDto.getReviewSrcType());
            sdrPushSaleReviewWebView.setCustomerId(businessViewDto.getCustomerId());
            sdrPushSaleReviewWebView.setCustomerName(businessViewDto.getCustomerName());
            sdrPushSaleReviewWebView.setContactPersonId(businessViewDto.getContactPersonId());
            sdrPushSaleReviewWebView.setContactPersonName(businessViewDto.getContactPersonName());
            sdrPushSaleReviewWebView.setProvince(businessViewDto.getProvince());
            sdrPushSaleReviewWebView.setCity(businessViewDto.getCity());
            sdrPushSaleReviewWebView.setDistrict(businessViewDto.getDistrict());
            sdrPushSaleReviewWebView.setAddress(businessViewDto.getAddress());
            sdrPushSaleReviewWebView.setIntentProduct(businessViewDto.getIntentProduct());
            sdrPushSaleReviewWebView.setRecommendedReason(businessViewDto.getRecommendedReason());
            sdrPushSaleReviewWebView.setReviewStatus(businessViewDto.getReviewStatus());
            sdrPushSaleReviewWebView.setReviewId(businessViewDto.getReviewId());
            sdrPushSaleReviewWebView.setReviewTime(businessViewDto.getReviewTime());
            sdrPushSaleReviewWebView.setAssignSubId(businessViewDto.getAssignSubId());
            sdrPushSaleReviewWebView.setRemarkReason(businessViewDto.getRemarkReason());
            sdrPushSaleReviewWebView.setCreatedId(businessViewDto.getCreatedId());
            sdrPushSaleReviewWebView.setCreateTime(businessViewDto.getCreateTime());
            sdrPushSaleReviewWebView.setUpdatedId(businessViewDto.getUpdatedId());
            sdrPushSaleReviewWebView.setUpdatedTime(businessViewDto.getUpdatedTime());
            String attachment = businessViewDto.getAttachment();
            if (org.springframework.util.StringUtils.hasText(attachment)) {
                try {
                    sdrPushSaleReviewWebView.setAttachment(JSONArray.parseArray(attachment));
                } catch (Exception e) {
                    log.error("转换附件出现问题, {}", attachment);
                }
            }
            return WebResult.success(sdrPushSaleReviewWebView);
        } else {
            return WebResult.success();
        }
    }


    /***
     * 回执确认
     * @param abmReceiptCOnfirmWebDto
     * <AUTHOR>
     * @date 2025/8/8 16:57
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.service.business.entity.dto.SdrPushSaleReviewBusinessViewDto>
     **/
    @PostMapping("/review/doReceipt")
    public WebResult<SdrPushSaleReviewBusinessViewDto> doReceipt(@Valid @RequestBody AbmReceiptConfirmWebDto abmReceiptCOnfirmWebDto) {
        log.info("回执确认 req={}", JSON.toJSONString(abmReceiptCOnfirmWebDto));
        String loginEmployeeId = abmReceiptCOnfirmWebDto.getLoginEmployeeId();
        if (StrUtil.isEmpty(loginEmployeeId)) {
            return WebResult.error(WebCodeMessageEnum.WX_LOGIN_EXPIRED);
        }

        String loginPosition = abmReceiptCOnfirmWebDto.getLoginPosition();
         if (!PositionUtil.isBusinessSaler(loginPosition)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }

        String customerId = abmReceiptCOnfirmWebDto.getCustomerId();
        ReceiptConfirmDto receiptConfirmDto = new ReceiptConfirmDto();
        receiptConfirmDto.setCustomerId(customerId);
        receiptConfirmDto.setSalerId(loginEmployeeId);
        Optional<String> optional = bizOppDistributeBusiness.doReceipt(receiptConfirmDto);
        return optional.<WebResult<SdrPushSaleReviewBusinessViewDto>>map(s -> WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION, s)).orElseGet(WebResult::success);
    }


    @PostMapping("/review/addXXlJob")
    public WebResult<Integer> addXXlJob(@Valid @RequestBody AbmReceiptXxlJobWebDto abmReceiptXxlJobWebDto) {
        log.info("addXXlJob req={}", JSON.toJSONString(abmReceiptXxlJobWebDto));
        String loginEmployeeId = abmReceiptXxlJobWebDto.getLoginEmployeeId();
        if (StrUtil.isEmpty(loginEmployeeId)) {
            return WebResult.error(WebCodeMessageEnum.WX_LOGIN_EXPIRED);
        }
        Long sjAssignDetailId = abmReceiptXxlJobWebDto.getSjAssignDetailId();
        SjAssignDetail sjAssignDetail = sjAssignDetailService.getById(sjAssignDetailId);
        Integer jobId = bizOppDistributeBusiness.addReceiptXxlTask(sjAssignDetail);
        return WebResult.success(jobId);
    }

    @PostMapping("/review/addCustFollowXxlJob")
    public WebResult<Integer> addCustFollowXxlJob(@Valid @RequestBody AbmReceiptXxlJobWebDto abmReceiptXxlJobWebDto) {
        log.info("addCustFollowXxlJob req={}", JSON.toJSONString(abmReceiptXxlJobWebDto));
        String loginEmployeeId = abmReceiptXxlJobWebDto.getLoginEmployeeId();
        if (StrUtil.isEmpty(loginEmployeeId)) {
            return WebResult.error(WebCodeMessageEnum.WX_LOGIN_EXPIRED);
        }
        Long sjAssignDetailId = abmReceiptXxlJobWebDto.getSjAssignDetailId();
        SjAssignDetail sjAssignDetail = sjAssignDetailService.getById(sjAssignDetailId);

        LocalDateTime nextExecutionTime = LocalDateTime.now();
        nextExecutionTime = nextExecutionTime.minusHours(48);
        Date followEndTime = Date.from(nextExecutionTime.atZone(ZoneId.systemDefault()).toInstant());
        sjAssignDetail.setFollowEndTime(followEndTime);
        Integer jobId = bizOppDistributeBusiness.addCustFollowXxlTask(sjAssignDetail);
        return WebResult.success(jobId);
    }
}
