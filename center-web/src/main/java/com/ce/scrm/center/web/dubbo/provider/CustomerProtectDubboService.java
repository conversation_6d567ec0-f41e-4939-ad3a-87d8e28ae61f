package com.ce.scrm.center.web.dubbo.provider;

import cn.ce.sequence.api.request.SequenceIdRequest;
import cn.ce.sequence.api.service.SequenceService;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dubbo.api.CustomerProtectDubbo;
import com.ce.scrm.center.dubbo.entity.dto.CustomerProtectDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.TurnoverCustQueryConditionDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.dto.AddProtectTimeEventDubboDto;
import com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew;
import com.ce.scrm.center.dubbo.entity.view.TurnoverCustQueryConditionDubboView;
import com.ce.scrm.center.service.business.ProtectBusiness;
import com.ce.scrm.center.service.business.entity.dto.AddProtectTimeEventBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.ProtectUpdateBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.TurnoverCustQueryConditionBusinessDto;
import com.ce.scrm.center.service.business.entity.view.TurnoverCustQueryConditionBusinessView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.util.PageUtil;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
* @Description: 保护关系
* @Author: lijinpeng
* @Date: 2024/11/19 09:51
* @version 1.0
*/
@Slf4j
@DubboService(interfaceClass = CustomerProtectDubbo.class)
public class CustomerProtectDubboService implements CustomerProtectDubbo {

    @Resource
    private ProtectBusiness protectBusiness;

    @Resource
    private CmCustProtectService cmCustProtectService;

    @Resource
    private SequenceService sequenceService;

    @Override
    public DubboResult<String> save(CustomerProtectDubboDto customerProtectDubboDto) {
        if (customerProtectDubboDto == null) {
            return DubboResult.success("success");
        }
        SequenceIdRequest sequenceIdRequest = new SequenceIdRequest();
        sequenceIdRequest.setBusinessType(10);
        Long sequenceId = sequenceService.generateId(sequenceIdRequest).getData();
        log.info("插入一条保护关系,customerProtectDubboDto={}", JSON.toJSONString(customerProtectDubboDto));
        ProtectUpdateBusinessDto saveData = BeanUtil.copyProperties(customerProtectDubboDto, ProtectUpdateBusinessDto.class);
        saveData.setId(String.valueOf(sequenceId));
        Boolean b = protectBusiness.saveData(saveData);
        return DubboResult.success("success");
    }

    @Override
    public DubboResult<CustomerProtectDubboVew> selectCustomerById(String customerId) {
        if (StringUtils.isBlank(customerId)) {
            return DubboResult.success(null);
        }
        CustProtectView custProtectView = cmCustProtectService.selectOneByCondition(CmCustProtect.builder().custId(customerId).build());
        if (custProtectView == null) {
            return DubboResult.success(null);
        }
        CustomerProtectDubboVew customerProtectDubboVew = BeanUtil.copyProperties(custProtectView, CustomerProtectDubboVew.class);
        return DubboResult.success(customerProtectDubboVew);
    }

    @Override
    public DubboResult<List<CustomerProtectDubboVew>> selectCustomerByUncid(String uncid) {
        return null;
    }

    @Override
    public DubboResult<CustomerProtectDubboVew> selectCustomerByName(String customerName) {
        return null;
    }

    @Override
    public DubboResult<List<CustomerProtectDubboVew>> selectCustomerBySalerId(String salerId) {
        return null;
    }

    @Override
    public DubboResult<List<CustomerProtectDubboVew>> selectByCondition(CustomerProtectDubboDto customerProtectDubboDto) {
        return null;
    }

    /*
     * @Description 根据custId更新保护关系信息，注：里面对last字段进行特殊处理，更新操作应走此方法
     * <AUTHOR>
     * @date 2024/11/19 11:47
     * @param customerProtectDubboDto
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.Integer>
     */
    @Override
    public DubboResult<Integer> updateByCustId(CustomerProtectDubboDto customerProtectDubboDto) {
        log.info("根据custId修改保护关系,customerProtectDubboDto={}", JSON.toJSONString(customerProtectDubboDto));

        if (customerProtectDubboDto == null || StringUtils.isBlank(customerProtectDubboDto.getCustId())) {
            return DubboResult.success(-1);
        }

        ProtectUpdateBusinessDto protectUpdateBusinessDto = BeanUtil.copyProperties(customerProtectDubboDto, ProtectUpdateBusinessDto.class);
        Integer result = protectBusiness.updateByCustId(protectUpdateBusinessDto);
        log.info("根据custId修改保护关系,result={}", result);
        return DubboResult.success(result);
    }

    /**
     * @description: 根据custId更新，可以更新空值，所以要注意【先查在更】确保查询全量字段 ！！！
     * @author: lijinpeng
     * @date: 2025/7/16 10:47
     * @param: [customerProtectDubboDto]
     * @return: com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.Integer>
     **/
    @Override
    public DubboResult<Integer> updateNullableByCustId(CustomerProtectDubboDto customerProtectDubboDto) {
        log.info("updateNullableByCustId,customerProtectDubboDto={}", JSON.toJSONString(customerProtectDubboDto));

        if (customerProtectDubboDto == null || StringUtils.isBlank(customerProtectDubboDto.getCustId())) {
            return DubboResult.success(-1);
        }

        ProtectUpdateBusinessDto protectUpdateBusinessDto = BeanUtil.copyProperties(customerProtectDubboDto, ProtectUpdateBusinessDto.class);
        Integer result = protectBusiness.updateNullableByCustId(protectUpdateBusinessDto);
        log.info("updateNullableByCustId,result={}", result);
        return DubboResult.success(result);
    }

    @Override
    public DubboResult<Integer> updateByCustomerId(CustomerProtectDubboDto customerProtectDubboDto) {
        return null;
    }

    @Override
    public DubboResult<Integer> deleteByCustomerId(String customerId) {
        return null;
    }

    @Override
    public DubboResult<DubboPageInfo<TurnoverCustQueryConditionDubboView>> selectTurnoverCustByCondition(TurnoverCustQueryConditionDubboDto turnoverCustQueryConditionDubboDto) {
        log.info("根据条件查询成交客户,turnoverCustQueryConditionDubboDto={}", JSON.toJSONString(turnoverCustQueryConditionDubboDto));
        TurnoverCustQueryConditionBusinessDto turnoverCustQueryConditionBusinessDto = BeanCopyUtils.convertToVo(turnoverCustQueryConditionDubboDto, TurnoverCustQueryConditionBusinessDto.class);

        //特殊记录cat
        Transaction t = Cat.newTransaction("dubbo", "CustomerProtectDubboService.selectTurnoverCustByCondition");

        if (StringUtils.isNotBlank(turnoverCustQueryConditionBusinessDto.getCustName())){
            Transaction byname = Cat.newTransaction("dubbo", "CustomerProtectDubboService.selectTurnoverCustByCondition.byName");
            byname.setStatus(Transaction.SUCCESS);
            byname.complete();;
        }

        Page<TurnoverCustQueryConditionBusinessView> r = new Page<>();
        try {
            r = protectBusiness.selectTurnoverCustByCondition(turnoverCustQueryConditionBusinessDto);
        }catch (Exception e){
            t.setStatus(e);
        }finally {
            t.complete();
        }

        DubboPageInfo<TurnoverCustQueryConditionDubboView> result = PageUtil.pageConversion(r, TurnoverCustQueryConditionDubboView.class);
        return DubboResult.success(result);
    }

    @Override
    public DubboResult<Boolean> addProtectTimeByEvent(AddProtectTimeEventDubboDto addProtectTimeEventDubboDto) {
        if (addProtectTimeEventDubboDto == null) {
            return DubboResult.success(false);
        }
        AddProtectTimeEventBusinessDto addProtectTimeEventBusinessDto = BeanUtil.copyProperties(addProtectTimeEventDubboDto, AddProtectTimeEventBusinessDto.class);
        return DubboResult.success(protectBusiness.addProtectTimeByEvent(addProtectTimeEventBusinessDto));
    }

}
