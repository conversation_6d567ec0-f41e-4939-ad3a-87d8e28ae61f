package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/4/1 10:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryCustomerLocationWebDto extends LoginInfo implements Serializable {

    /**
     * 客户名称
     */
    @NotBlank
    private String customerName;

}
