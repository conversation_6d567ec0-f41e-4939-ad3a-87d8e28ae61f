package com.ce.scrm.center.web.entity.dto.abm;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 转商机参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AbmReviewCheckWebDto extends LoginInfo implements Serializable {

    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 联系人名称
     */
    private String contactPersonName;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 通讯地址
     */
    private String address;

    /**
     * 意向产品 支持多个  A,B,C,D
     */
    private String intentProduct;

    /**
     * 推荐理由
     */
    private String recommendedReason;

    /**
     * 附件信息 多个格式： A,B,C,D
     */
    private String attachment;

    /**
     * 审核状态 0:待审核 1:审核通过 2:审核驳回
     */
    private Integer reviewStatus;

    /**
     * 调配分司
     */
    private String assignSubId;

    /**
     * 备注原因
     */
    private String remarkReason;

}