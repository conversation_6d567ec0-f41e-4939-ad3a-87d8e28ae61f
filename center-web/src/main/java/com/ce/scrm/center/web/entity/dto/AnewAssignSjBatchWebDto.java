package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 重新分配商机
 * @Author: lijinpeng
 * @Date: 2025/1/7 15:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AnewAssignSjBatchWebDto extends LoginInfo implements Serializable {

    /**
     * 商机code集合
     */
    @NotEmpty
    private List<String> sjCodeList;

    /**
     * 分配的分司id
     */
    @NotNull
    private String assignToSubId;

}
