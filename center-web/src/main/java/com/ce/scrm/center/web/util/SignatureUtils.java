package com.ce.scrm.center.web.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/15
 */
public class SignatureUtils {
    public static String generateSignature(String data, String secretKey) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] hmacBytes = mac.doFinal(data.getBytes());
            return Base64.getEncoder().encodeToString(hmacBytes);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate HMAC signature", e);
        }
    }

    public static void main(String[] args) {
        String secretKey="********************************";
        String customerId = "C1168B7B7581447AADF6B4552353DDE0";
        String salerId ="96dc187f54a64e89b506b160c8b43531";
        String customerIdSign = generateSignature(customerId, secretKey);
        String salerIdSign = generateSignature(salerId, secretKey);
        System.out.println(customerIdSign);
        System.out.println(salerIdSign);
    }
}
