package com.ce.scrm.center.web.entity.dto.customer;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * @classname CustomerLinkmanListByCustomerIdWebDto
 * @description 客户业务联系人列表查询条件
 * @date 2025/1/20 18:10
 * @create by gaomeijing
 */
@Data
public class CustomerLinkmanListByCustomerIdWebDto extends LoginInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    // 客户ID
    private String customerId;
}
