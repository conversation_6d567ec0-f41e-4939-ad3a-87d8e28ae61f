package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 流转日志
 * @author: JiuDD
 * date: 2024/7/11
 */
@Data
public class ConvertLogWebView implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private String id;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 原商务代表id
     */
    private String salerId;
    /**
     * 原商务代表名称
     */
    private String salerName;
    /**
     * 原商务代表部门
     */
    private String deptOfSalerId;
    /**
     * 原商务代表部门名称
     */
    private String deptNameOfSalerId;
    /**
     * 原商务代表分公司
     */
    private String subcompanyOfSalerId;
    /**
     * 原商务代表分公司名称
     */
    private String subcompanyNameOfSalerId;
    /**
     * 原商务代表区域
     */
    private String areaOfSalerId;
    /**
     * 原商务代表区域
     */
    private String areaNameOfSalerId;
    /**
     * 创建人id
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 转换类型
     */
    private Integer convertType;

    /**
     * 释放原因
     */
    private String releaseReason;

    /**
     * 当前商务代表id
     */
    private String curSalerId;
    /**
     * 当前商务代表部门
     */
    private String deptOfCurSalerId;
    /**
     * 当前商务代表分公司
     */
    private String subcompanyOfCurSalerId;
    /**
     * 当前商务代表分区域
     */
    private String areaOfCurSalerId;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 原客户类型
     */
    private Integer custType;
    /**
     * 目标
     */
    private Integer dest;

    /**
     * 企业id
     */
    private String entId;
    /**
     * 意向级别
     */
    private Integer intentionType;
    /**
     * 商机来源 1：商机 2：报价 3：转介绍 4：商机,报价 5：商机,转介绍 6：报价,转介绍 7：商机,报价,转介绍
     */
    private Integer tagOpportunityOrigin;
    private String  tagOpportunityOriginDesp;
    /**
     * 商务释放理由
     */
    private String swReasonText;
    /**
     * 经理补充理由
     */
    private String jlReasonText;
    /**
     * 经理补充时间
     */
    private Date jlReleaseTime;
    /**
     * 经理姓名
     */
    private String managerName;
}