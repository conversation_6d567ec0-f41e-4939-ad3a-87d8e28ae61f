package com.ce.scrm.center.web.entity.dto.follow;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryFollowDetailListWebDto extends LoginInfo implements Serializable {

    /**
     * 分司id
     */
    private String subId;

    /**
     * 事业部id
     */
    private String buId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 跟进日期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date followTimeStart;

    /**
     * 跟进日期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date followTimeEnd;

    /**
     * 跟进阶段
     */
    private String salesStage;

    /**
     * 跟进阶段多选筛选
     */
    private List<String> salesStageList;

    /**
     * 预计签单日期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date predictSignDateStart;

    /**
     * 预计签单日期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date predictSignDateEnd;

    /**
     * 预计到账日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date predictAccountDateStart;

    /**
     * 预计到账日期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date predictAccountDateEnd;

    /**
     * 商务月参数
     */
    private String businessMonth;

    /**
     * 类型
     */
    private String type;

    /**
     * 当前页
     */
    private Integer currentPage = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

}
