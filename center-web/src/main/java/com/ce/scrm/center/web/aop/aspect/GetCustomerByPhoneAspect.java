package com.ce.scrm.center.web.aop.aspect;

import com.ce.scrm.center.dao.entity.GetCustomerByPhoneLog;
import com.ce.scrm.center.dao.service.GetCustomerByPhoneLogService;
import com.ce.scrm.center.web.entity.dto.customer.CustomerByPhoneWebDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @version 1.0
 * @Description: 根据手机号查客户记录日志
 * @Author: lijinpeng
 * @Date: 2024/12/25 15:21
 */
@Slf4j
@Aspect
@Component
public class GetCustomerByPhoneAspect {

    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private GetCustomerByPhoneLogService getCustomerByPhoneLogService;

    @Pointcut("execution(public * com.ce.scrm.center.web.controller.customer.CustomerController.getCustomerByPhone(..))")
    public void addLog() {
    }

    @Around("addLog()")
    public Object around(ProceedingJoinPoint joinPoint) {
        Object proceed = null;
        try {
            proceed = joinPoint.proceed();
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }

        String empId = null;
        String empName = null;
        String phone = null;
        try {
            Object[] args = joinPoint.getArgs();
            CustomerByPhoneWebDto customerByPhoneWebDto = null;
            for (Object arg : args) {
                if (arg instanceof CustomerByPhoneWebDto) {
                    customerByPhoneWebDto = (CustomerByPhoneWebDto) arg;
                }
            }
            empId = customerByPhoneWebDto==null?null:customerByPhoneWebDto.getLoginEmployeeId();
            empName = customerByPhoneWebDto==null?null:customerByPhoneWebDto.getLoginEmployeeName();
            phone = customerByPhoneWebDto==null?null:customerByPhoneWebDto.getPhone();
//            WebResult<List<CustomerByPhoneWebView>> result = (WebResult<List<CustomerByPhoneWebView>>)proceed;
//            String resultStr = result==null?null:JSON.toJSONString(result.getData());
            getCustomerByPhoneLogService.save(GetCustomerByPhoneLog.builder().empId(empId).empName(empName).phone(phone).build());
        }catch (Throwable e) {
            log.error("根据手机号查询客户信息保存日志报错！");
        }
        return proceed;
    }

}
