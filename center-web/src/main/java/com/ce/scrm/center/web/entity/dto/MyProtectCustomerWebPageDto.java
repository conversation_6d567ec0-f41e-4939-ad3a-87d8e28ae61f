package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 根据名字获取客户信息
 * @Author: lijinpeng
 * @Date: 2024/12/5 16:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MyProtectCustomerWebPageDto extends LoginInfo implements Serializable {
	/**
	 * 页码
	 */
	private Integer pageNum = 1;
	/**
	 * 每页数量
	 */
	private Integer pageSize = 10;
	/**
	 * 客户名
	 */
	private String custName;
	/**
	 * 商务ID
	 */
	private String salerId;
	/**
	 * 部门ID
	 */
	private String deptId;
	/**
	 * 分司ID
	 */
	private String subId;
	/**
	 * 事业部id
	 */
	private String buId;
	/**
	 * 跟进状态
	 */
	private Integer visitStatus;
	/**
	 * 保护原因
	 */
	private String protectReason;
	/**
	 * 销售阶段
	 */
	private String salesStage;

	/**
	 * 销售阶段多选筛选项
	 */
	private List<String> salesStageList;
	/**
	 * 保护开始时间
	 */
	private String startProtectDate;
	/**
	 * 保护结束时间
	 */
	private String endProtectDate;
	private Integer isIntentJudge;
	/**
	 * 是否转高呈
	 */
	private Integer isTurnGc;
	/**
	 * 客户来源
	 */
	private String custSources;
	private Integer custSourceSub;
	/**
	 * 是否占库容
	 */
	private Integer isOccupy;
	/**
	 * 客户类型
	 * 2:跟进中
	 * 3.网站
	 * 4.非网站
	 */
	private Integer custType;

	/**
	 * 搜客宝 1:规模工业,2:规上服务业,3:规上建筑业,4:规上批发零售业,5:规上住宿餐饮业,6:规上房地产开发与经营业
	 */
	private List<String> tagFlag7;

	/**
	 * 搜客宝 行业 1:律师,2:学校,3:医院
	 */
	private List<String> tagFlag8;

	/**
	 * 自定义标签
	 */
	private String customTags;

	/**
	 * 绑定客户标识 0未绑定 1绑定
	 */
	private Integer bindFlag;

	/**
	 * 对应前端【类型】1未保护确认的商机、2保护确认的商机、3所有商机
	 */
	private Integer queryType;

}
