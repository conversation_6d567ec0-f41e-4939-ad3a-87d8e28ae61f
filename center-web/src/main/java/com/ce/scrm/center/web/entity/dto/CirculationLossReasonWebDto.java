package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description: 流失原因保存
 * @author: lyc
 * date: 2024/7/22
 */
@EqualsAndHashCode
@Data
public class CirculationLossReasonWebDto extends LoginInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 流失理由
     */
    @NotNull(message = "流失理由不能为空")
    private String reason;

    /**
     * 客户id
     */
    @NotNull(message = "客户ID不能为空")
    private String custId;

}