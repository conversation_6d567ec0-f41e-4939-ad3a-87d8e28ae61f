package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * description: 推荐客户参数
 * @author: liyechao
 * date: 2024/8/13.
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RecommendCustWebDto extends LoginInfo implements Serializable {
    /**
     * 被推荐客户ID
     */
    private String recommendedCustId;
    /**
     * 推荐客户ID
     */
    private String recommendCustId;

}

