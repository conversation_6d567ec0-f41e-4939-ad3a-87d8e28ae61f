package com.ce.scrm.center.web.entity.dto.abm;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 回执
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AbmReceiptXxlJobWebDto extends LoginInfo implements Serializable {

    /**
     * 下发明细id
     */
    private Long sjAssignDetailId;

}