package com.ce.scrm.center.web.controller.abm;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.Transfertable;
import com.ce.scrm.center.dao.service.TransfertableService;
import com.ce.scrm.center.service.business.abm.TransferBusiness;
import com.ce.scrm.center.service.business.entity.dto.abm.TransferDto;
import com.ce.scrm.center.service.business.entity.dto.abm.TransferWebDto;
import com.ce.scrm.center.service.business.entity.view.TransferWebDtoView;
import com.ce.scrm.center.service.enums.TransferProcessingStatusEnum;
import com.ce.scrm.center.service.enums.TransferReasonEnum;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.controller.base.BaseController;
import com.ce.scrm.center.web.entity.dto.abm.ApplicationTransferWebDto;
import com.ce.scrm.center.web.entity.dto.abm.TransferWebDetailDto;
import com.ce.scrm.center.web.entity.dto.abm.TransferWebPageDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.abm.TransferPageWebView;
import com.ce.scrm.center.web.entity.view.abm.TransferWebView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 调拨相关处理
 * @Data 2025/8/7 22:24
 */

@Login
@Slf4j
@RestController
@RequestMapping("/abm/transfer")
public class TransferController extends BaseController {


    @Value("${file.upload.dir}")
    private String uploadDir;

    @Value("${file.upload.url}")
    private String uploadUrl;

    @Resource
    private TransferBusiness transferBusiness;

    @Resource
    private TransfertableService transfertableService;

    /**
     * 申请调度
     * @param applicationTransferDto
     * @return
     */
    @PostMapping("/apply")
    public WebResult<String> apply(@RequestBody ApplicationTransferWebDto applicationTransferDto) {
        if (CollectionUtils.isEmpty(applicationTransferDto.getFiles())){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION.getCode(), "未找到上传文件");
        }
        //去重判断
        List<Transfertable> list = transfertableService.list(new LambdaQueryWrapper<Transfertable>()
                .eq(Transfertable::getCustomerId, applicationTransferDto.getCustomerId())
                .eq(Transfertable::getApplicantId, applicationTransferDto.getLoginEmployeeId())
                .eq(Transfertable::getProcessingStatus, TransferProcessingStatusEnum.UNPROCESSED.getCode())
        );
        if (CollectionUtils.isNotEmpty(list)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION.getCode(), "您已发送调度申请，请勿重复发起");
        }
        TransferDto transferDto = new TransferDto();
        transferDto.setLoginEmployeeId(applicationTransferDto.getLoginEmployeeId());
        transferDto.setLoginEmployeeName(applicationTransferDto.getLoginEmployeeName());
        transferDto.setLoginOrgName(applicationTransferDto.getLoginOrgName());
        transferDto.setLoginSubName(applicationTransferDto.getLoginSubName());
        transferDto.setLoginBuName(applicationTransferDto.getLoginBuName());
        transferDto.setLoginAreaName(applicationTransferDto.getLoginAreaName());
        transferDto.setCustomerId(applicationTransferDto.getCustomerId());
        transferDto.setCustomerName(applicationTransferDto.getCustomerName());
        transferDto.setTransferReason(TransferReasonEnum.getBy(applicationTransferDto.getTransferReason()));
        transferDto.setProofInfoUrl(JSONObject.toJSONString(applicationTransferDto.getFiles()));
        Boolean b = transferBusiness.insertApplicationTransfer(transferDto);
        if (!b) {
            return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION.getCode(), "申请失败");
        }
        return WebResult.success(WebCodeMessageEnum.REQUEST_SUCCESS.getCode(), "调度申请已成功提交，请耐心等待处理。");
    }

    /**
     * 分页查询
     * @param webReq
     * @return
     */
    @PostMapping("/page")
    public WebResult<WebPageInfo<TransferPageWebView>> page(@RequestBody TransferWebPageDto webReq) {
        TransferWebDto thirdQueryDto = BeanCopyUtils.convertToVo(webReq, TransferWebDto.class);
        Page<TransferWebDtoView> page = transferBusiness.page(thirdQueryDto);
        WebPageInfo<TransferPageWebView> webPageInfo = WebPageInfo.pageConversion(page, TransferPageWebView.class);
        return WebResult.success(webPageInfo);
    }

    /**
     * 调拨信息详情
     * @param webReq
     * @return
     */
    @PostMapping("/detail")
    public WebResult<TransferWebView> detail(@Validated @RequestBody TransferWebDetailDto webReq) {
        TransferWebDtoView detail = transferBusiness.detail(webReq.getId());
        TransferWebView transferWebView = BeanCopyUtils.convertToVo(detail, TransferWebView.class);
        transferWebView.setProofInfoUrl(JSONObject.parseArray(detail.getProofInfoUrl(), ApplicationTransferWebDto.FileIn.class));
        return WebResult.success(transferWebView);
    }

    /**
     * 调拨处理
     * @param webReq
     * @return
     */
    @PostMapping("/deal")
    public WebResult<String> deal(@Validated @RequestBody TransferWebDetailDto webReq) {
        Boolean b = transferBusiness.deal(webReq.getId(), webReq.getRemarks(), webReq.getProcessingResult(), webReq.getLoginEmployeeName());
        if (!b) {
            return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION.getCode(), "处理失败");
        }
        return WebResult.success(WebCodeMessageEnum.REQUEST_SUCCESS.getCode(), "处理成功");
    }

}


