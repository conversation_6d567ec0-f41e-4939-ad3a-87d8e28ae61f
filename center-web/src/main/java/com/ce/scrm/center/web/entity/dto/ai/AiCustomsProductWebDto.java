package com.ce.scrm.center.web.entity.dto.ai;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/***
* 海关产品名称查询参数
* <AUTHOR>
* @date 2025/5/21 11:47
*/
@Data
public class AiCustomsProductWebDto extends LoginInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 产品名称
     */
    private String productName;

    private Integer startYear;
    private Integer endYear;

    /**
     * 前端页面选择的产品code 列表
     */
    private List<String> productCodes;

    private String parentEventId;

    /**
     * 平台，取值为 WECOM/ PC
     */
    private String platform;

    /**
     * 问题来源：0.自问，1.预置问题
     */
    private Integer questionSource;

    /**
     * 聊天问题
     */
    private String chatQuestion;

    /**
     * 聊天ID
     */
    private String chatId;



    /**
     * 页码
     */
    private Integer pageNum=1;

    /**
     * 每页数量
     */
    private Integer pageSize=10;
}
