package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * @classname CustomerLinkmanView
 * @description 业务联系人
 * @date 2025/1/20 17:06
 * @create by gaomeijing
 */
@Data
public class CustomerLinkmanView implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  联系人id
     */
    private String linkManId;
    /**
     *  联系人名称
     */
    private String manNameCn;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 职位
     */
    private String manPost;

    /**
     * 电话集合(含tid)
     */
    private Set<String> telSet;

    /**
     * 邮箱集合
     */
    private Set<String> emailSet;
    /**
     * 微信号集合
     */
    private Set<String> wxSet;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 性别 1男2女
     */
    private Integer manSex;

    /**
     * creator
     */
    private String creator;

    /**
     * sourceTag 来源
     */
    private String sourceTag;

    /**
     *  是否法人 1是 0否
     */
    private Integer legalPersonFlag;

    /**
     *  手机号是否已验证 1是 0否
     */
    private Integer phoneVerifiedFlag;
}
