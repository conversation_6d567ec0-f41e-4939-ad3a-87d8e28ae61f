package com.ce.scrm.center.web.util;

import javax.servlet.http.HttpServletRequest;

/**
 * 网络工具类
 * <AUTHOR>
 * @date 2023/4/6 22:13
 * @version 1.0.0
 */
public class NetUtil {

    private final static String BASE_PROXY_IP = "Proxy-Client-IP";

    private final static String WL_PROXY_IP = "WL-Proxy-Client-IP";

    private final static String BASE_HTTP_IP = "HTTP_CLIENT_IP";

    private final static String X_HTTP_IP = "HTTP_X_FORWARDED_FOR";

    /**
     * 多级代理或转发的ip
     */
    private final static String FORWARDED_FOR_IP = "x-forwarded-for";
    /**
     * 未知ip
     */
    private final static String UNKNOWN = "unknown";


    /**
     * 获取用户真实IP地址，不使用request.getRemoteAddr();的原因是有可能用户使用了代理软件方式避免真实IP地址
     * 如果通过了多级反向代理的话，取X-Forwarded-For中第一个非unknown的有效IP字符串
     * 如：X-Forwarded-For：*************, *************, *************,*************,用户真实IP为： *************
     * @param request   请求信息
     * <AUTHOR>
     * @date 2023/4/6 22:13
     * @return java.lang.String
     **/
    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader(FORWARDED_FOR_IP);
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader(BASE_PROXY_IP);
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader(WL_PROXY_IP);
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader(BASE_HTTP_IP);
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader(X_HTTP_IP);
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
