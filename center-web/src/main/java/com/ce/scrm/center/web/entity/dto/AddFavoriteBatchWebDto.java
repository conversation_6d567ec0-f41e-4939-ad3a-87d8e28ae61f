package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 添加收藏批量
 * @Author: lijinpeng
 * @Date: 2025/1/9 11:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddFavoriteBatchWebDto extends LoginInfo implements Serializable {

    /**
     * pid集合
     */
//    @NotEmpty
    private List<String> pidList;

    private List<String> customerIdList;

    /**
     * 分配的商务id
     */
    private String assignToSalerId;

    /**
     * 分配的部门id
     */
    private String assignToDeptId;

    /**
     * 分配的事业部id
     */
    private String assignToBuId;

    /**
     * 分配的分司id
     */
    private String assignToSubId;

    /**
     * 动作类型
     */
    private Integer actionType;

}
