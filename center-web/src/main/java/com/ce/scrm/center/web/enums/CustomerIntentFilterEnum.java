package com.ce.scrm.center.web.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.ce.scrm.center.service.third.invoke.CustomerTagsThirdService;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerTagDubboView;
import lombok.Getter;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:45:58
 * @desc 客户意愿行为筛选项枚举
 */
public enum CustomerIntentFilterEnum implements EnumBase {

    CIF0("0", "全部"),
    CIF1("tag10078", "A类-意愿表达"),
    CIF2("tag10079", "B类-兴趣表达"),
    CIF3("tag10080", "C类-行为记录"),
    CIF4("8", "电话意愿"),
    CIF5("14", "官网兴趣"),
    CIF6("18", "活动兴趣"),
    CIF7("16", "社媒意愿"),
    CIF8("17", "广告兴趣");

    @Getter
    private final String label;
    @Getter
    private final String value;

    // Getter 方法
    public String getKey() {
        return String.valueOf(label);
    }

    // 构造函数
    CustomerIntentFilterEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }

}
