package com.ce.scrm.center.web.entity.dto.protect;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @program: scrm-center
 * @ClassName ProtectConfirmWebDto
 * @description:
 * @author: lijinpeng
 * @create: 2025-07-21 11:43
 * @Version 1.0
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ProtectAllotWebDto extends LoginInfo implements Serializable {

    @NotBlank
    private String customerId;

    @NotBlank
    private String oldSalerId;

    @NotBlank
    private String newSalerId;

}
