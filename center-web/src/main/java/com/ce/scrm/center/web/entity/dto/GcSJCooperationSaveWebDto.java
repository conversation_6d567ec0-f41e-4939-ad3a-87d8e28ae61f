package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Description: 申请合作 / 转高呈
 * @author: JiuDD
 * date: 2024/5/21 17:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GcSJCooperationSaveWebDto extends LoginInfo implements Serializable {
    /**
     * 发起方：0、无，1、中小商务，2、高呈
     */
    private Integer initiator;
    /**
     * 高呈商机ID（更新必传）
     */
    @NotNull(message="高呈商机ID不能为空", groups = {SalerUpdateShangJi.class})
    private Long sjId;
    /**
     * 中小市场部商机code
     */
    private String telSjCode;
    /**
     * 中小市场部商机id
     */
    private String busiOppoId;
    /**
     * 客户id
     */
    @NotBlank(message = "客户id不能为空", groups = {GcSalerApplyCooperation.class, SalerTransferToGc.class, SalerUpdateShangJi.class})
    private String customerId;
    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空", groups = {GcSalerApplyCooperation.class, SalerTransferToGc.class, SalerUpdateShangJi.class})
    private String customerName;
    /**
     * 来源：GcSjSourceEnum
     */
    @NotNull(message = "商机来源不能为空", groups = {GcSalerApplyCooperation.class, SalerTransferToGc.class})
    private Integer source;
    /**
     * 需求详情
     */
    @NotBlank(message = "需求不能为空", groups = {GcSalerApplyCooperation.class, SalerTransferToGc.class, SalerUpdateShangJi.class})
    private String requirementDetail;
    /**
     * 附件ID（多个使用,分隔）
     */
    private String attachmentIds;
    /**
     * 是否已面谈
     */
    private Integer interviewState;
    /**
     * 项目类型
     */
    @NotNull(message = "项目类型不能为空", groups = {GcSalerApplyCooperation.class, SalerTransferToGc.class, SalerUpdateShangJi.class})
    private Integer requirementType;
    /**
     * 客户预算
     */
    @NotNull(message = "客户预算不能为空", groups = {GcSalerApplyCooperation.class, SalerTransferToGc.class})
    private BigDecimal customerBudget;
    /**
     * 期望高呈区域ID
     */
    private String expectGcAreaId;

    /**
     * 期望高呈区域名称
     */
    private String expectGcAreaName;
    /**
     * 期望高呈分司ID
     */
    private String expectGcSubId;
    /**
     * 期望高呈分司名称
     */
    private String expectGcSubName;
    /**
     * 期望高呈部门ID
     */
    private String expectGcDeptId;
    /**
     * 期望高呈部门名称
     */
    private String expectGcDeptName;
    /**
     * 期望高呈商务ID
     */
    private String expectGcSalerId;
    /**
     * 期望高呈商务名称
     */
    private String expectGcSalerName;
    /**
     * 高呈商务ID
     */
    private String gcSalerId;
    /**
     * 高呈部门ID
     */
    private String gcDeptId;
    /**
     * 高呈部门名称
     */
    private String gcDeptName;
    /**
     * 高呈分司ID
     */
    private String gcSubId;
    /**
     * 高呈分司名称
     */
    private String gcSubName;
    /**
     * 高呈区域ID
     */
    private String gcAreaId;
    /**
     * 高呈区域名称
     */
    private String gcAreaName;
    /**
     * 联系人名称
     */
    @NotBlank(message = "客户联系人名称不能为空", groups = {SalerTransferToGc.class, SalerUpdateShangJi.class})
    private String linkmanName;
    /**
     * 联系人手机号
     */
    @NotBlank(message = "客户联系人手机号不能为空", groups = {SalerTransferToGc.class, SalerUpdateShangJi.class})
    private String linkmanPhone;
    /**
     * 联系人邮箱
     */
    private String linkmanEmail;
    /**
     * 联系人部门
     */
    private String linkmanDept;
    /**
     * 联系人性别：0、未知，1、男，2、女
     */
    private Integer linkmanSex;
    /**
     * 联系人座机
     */
    private String linkmanLandline;
    /**
     * 联系人职务
     */
    private String linkmanJob;
    /**
     * 联系人微信
     */
    private String linkmanWechat;


    /**
     * 申请合作（高呈商务）
     */
    public interface GcSalerApplyCooperation{}
    /**
     * 转高呈（中小商务）
     */
    public interface SalerTransferToGc{}
    /**
     * 中小商务编辑转高呈的商机
     */
    public interface SalerUpdateShangJi{}
}