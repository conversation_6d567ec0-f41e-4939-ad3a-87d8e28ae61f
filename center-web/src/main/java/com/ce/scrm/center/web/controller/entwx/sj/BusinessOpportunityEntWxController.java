package com.ce.scrm.center.web.controller.entwx.sj;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.service.business.BusinessOpportunityBusiness;
import com.ce.scrm.center.service.business.QueryPhoneNumberBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.AcceptBusinessDto;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginType;
import com.ce.scrm.center.web.entity.dto.AcceptWebDto;
import com.ce.scrm.center.web.entity.dto.QueryPhoneNumberWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @version 1.0
 * @Description: 商机企业微信端
 * @Author: lijinpeng
 * @Date: 2025/3/27 15:45
 */
@Slf4j
@RestController
@RequestMapping("/entwx/opportunity")
@Login(loginType = LoginType.ENT_WECHAT)
public class BusinessOpportunityEntWxController {

    @Resource
    private BusinessOpportunityBusiness businessOpportunityBusiness;

    /*
     * @Description 商机48小时内 确认接收/拒绝
     * <AUTHOR>
     * @date 2025/3/27 11:28
     * @param acceptWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     */
    @PostMapping("accept")
    public WebResult<Boolean> accept(@RequestBody @Valid AcceptWebDto acceptWebDto) {
        AcceptBusinessDto acceptBusinessDto = BeanUtil.copyProperties(acceptWebDto, AcceptBusinessDto.class);
        Boolean result = businessOpportunityBusiness.accept(acceptBusinessDto);
        return WebResult.success(result);
    }

    /*
     * @Description 根据条件获取商机手机号
     * <AUTHOR>
     * @date 2025/4/11 18:34
     * @param queryPhoneNumberWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.String>
     */
    @PostMapping("getPhoneNumber")
    public WebResult<String> getPhoneNumber(@RequestBody QueryPhoneNumberWebDto queryPhoneNumberWebDto) {
        QueryPhoneNumberBusinessDto queryPhoneNumberBusinessDto = BeanUtil.copyProperties(queryPhoneNumberWebDto, QueryPhoneNumberBusinessDto.class);
        String result = businessOpportunityBusiness.getPhoneNumber(queryPhoneNumberBusinessDto);
        return WebResult.success(result);
    }

}
