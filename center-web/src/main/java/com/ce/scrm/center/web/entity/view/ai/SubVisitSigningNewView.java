package com.ce.scrm.center.web.entity.view.ai;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 分司拜访-报价-签单漏斗数据
 * @Author: lijinpeng
 * @Date: 2025/4/3 15:19
 */
@Data
public class SubVisitSigningNewView implements Serializable {

    @JSONField(name = "时间")
    private String time;

    //总体转化用户数
    @JSONField(name = "总体转化用户数")
    private Integer stepFoldUserCount;

    //总体转化率
    @JSONField(name = "总体转化率")
    private Float stepFoldConversionRate;

    //第1步用户数
    @JSONField(name = "第1步用户数")
    private Integer step1UserCount;

    //第1步流失用户数
    @JSONField(name = "第1步流失用户数")
    private Integer step1WastageUser;

    //第1步转化时长中位数
    @JSONField(name = "第1步转化时长中位数")
    private Integer step1MediumConvertedTime;

    //第1步转化率
    @JSONField(name = "第1步转化率")
    private Float step1ConversionRate;

    //第2步用户数
    @JSONField(name = "第2步用户数")
    private Integer step2UserCount;

    //第2步流失用户数
    @JSONField(name = "第2步流失用户数")
    private Integer step2WastageUser;

    //第2步转化时长中位数
    @JSONField(name = "第2步转化时长中位数")
    private Integer step2MediumConvertedTime;

    //第2步转化率
    @JSONField(name = "第2步转化率")
    private Float step2ConversionRate;

    //第3步用户数
    @JSONField(name = "第3步用户数")
    private Integer step3UserCount;

}
