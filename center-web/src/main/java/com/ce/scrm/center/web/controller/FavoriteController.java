package com.ce.scrm.center.web.controller;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.service.business.FavoriteBusiness;
import com.ce.scrm.center.service.business.entity.dto.AddFavoriteBatchBusinessDto;
import com.ce.scrm.center.service.business.entity.view.BatchResultBusinessView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.AddFavoriteBatchWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.BatchResultWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @version 1.0
 * @Description: 收藏夹
 * @Author: lijinpeng
 * @Date: 2025/1/9 10:13
 */
@Slf4j
@RestController
@RequestMapping("favorite")
@Login
public class FavoriteController {

    @Resource
    private FavoriteBusiness favoriteBusiness;

    /*
     * @Description 添加收藏批量
     * <AUTHOR>
     * @date 2025/1/9 11:34
     * @param assignProtectBatchWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.BatchResultWebView>
     */
    @PostMapping("addFavoriteBatch")
    public WebResult<BatchResultWebView> addFavoriteBatch(@RequestBody @Valid AddFavoriteBatchWebDto assignProtectBatchWebDto) {
        AddFavoriteBatchBusinessDto addFavoriteBatchBusinessDto = BeanUtil.copyProperties(assignProtectBatchWebDto, AddFavoriteBatchBusinessDto.class);
        BatchResultBusinessView businessResult = favoriteBusiness.addFavoriteBatch(addFavoriteBatchBusinessDto);
        BatchResultWebView batchResultWebView = BeanCopyUtils.convertToVo(businessResult, BatchResultWebView.class);
        return WebResult.success(batchResultWebView);
    }

}
