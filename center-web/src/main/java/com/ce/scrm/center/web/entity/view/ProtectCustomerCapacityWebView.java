package com.ce.scrm.center.web.entity.view;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProtectCustomerCapacityWebView implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 现有库容
	 */
	private Integer nowCapacityedCount;

	/**
	 * 总库容
	 * */
	private Integer totalCapacityedCount;
}