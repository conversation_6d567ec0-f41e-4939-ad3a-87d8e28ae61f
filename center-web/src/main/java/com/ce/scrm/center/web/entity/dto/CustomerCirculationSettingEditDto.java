package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 成交客户流转配置编辑实体
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
public class CustomerCirculationSettingEditDto extends LoginInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "主键不能为空")
    private Long id;

    @NotBlank(message = "分公司ID不能为空")
    private String subId;

    @NotBlank(message = "分公司名称不能为空")
    private String subName;

    @NotNull(message = "未打卡自然月数不能为空")
    private Integer monthWithoutClock;

    @NotNull(message = "流转方式不能为空")
    private Integer circulationType;


}
