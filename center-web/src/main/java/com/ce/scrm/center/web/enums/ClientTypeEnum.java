package com.ce.scrm.center.web.enums;

public enum ClientTypeEnum {

    PC("PC端", 1),
    MOBILE("移动端", 2),
    UNKNOWN("未知", 3),
    MINI_PROGRAM("小程序", 4),

    ;

    /**
     * lable
     */
    private String lable;

    /**
     * value
     */
    private Integer value;

    private ClientTypeEnum(String lableStr, Integer value) {
        this.lable = lableStr;
        this.value = value;
    }

    public String getLable() {
        return lable;
    }

    public void setLable(String lable) {
        this.lable = lable;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
    public static String ByValue(Integer in) {
        for (ClientTypeEnum statusEnum : ClientTypeEnum.values()) {
            if (statusEnum.getValue().equals(in)) {
                return statusEnum.getLable();
            }
        }
        return null;
    }

}
