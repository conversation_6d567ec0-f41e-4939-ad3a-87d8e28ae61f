package com.ce.scrm.center.web.controller.ai;

import cn.ce.cesupport.enums.YesOrNoEnum;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.AiPromptInternalInfo;
import com.ce.scrm.center.dao.service.AiPromptInternalInfoService;
import com.ce.scrm.center.web.entity.dto.AiQueryDto;
import com.ce.scrm.center.web.entity.dto.ai.AiOrgPerformanceInfo;
import com.ce.scrm.center.web.entity.dto.ai.QueryAiPromptInfoWebDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.util.gptstreamutil.AiService;
import com.ce.scrm.center.web.util.gptstreamutil.ExtralInfo;
import com.ce.scrm.center.web.util.gptstreamutil.ResponseConstant;
import com.ce.scrm.center.web.util.gptstreamutil.SseEmitterUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;


@Slf4j
@RestController
@RequestMapping("analysis/internal")
public class AiSmartInternalController {

    @Autowired
    private AiService aiService;

    @Autowired
    private AiPromptInternalInfoService aiPromptInternalInfoService;

    @RequestMapping(value = "/completions/getAnalysisiTips", method = RequestMethod.POST, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<SseEmitter> getAiTips(@RequestBody AiQueryDto aiQueryDto) {
        try {
            String systemContent = null;
            if (StringUtils.isNotBlank(aiQueryDto.getSystemContent())) {
                systemContent = aiQueryDto.getSystemContent();
            } else {
                AiPromptInternalInfo aiPromptInternalInfo = aiPromptInternalInfoService.getById(aiQueryDto.getPromptId());
                systemContent = aiPromptInternalInfo.getContent();
            }
            String analyzerJson = StringUtils.isBlank(aiQueryDto.getAnalyzeJson()) ? aiService.buildPerformanceAndVisitSigningData(aiQueryDto.getOrgId()) : aiQueryDto.getAnalyzeJson();
            return aiService.getAiTips(analyzerJson, messageRes -> {
            }, ExtralInfo.builder()
                    .promptId(aiQueryDto.getPromptId())
                    .promptContent(systemContent)
                    .build());
        } catch (Exception e) {
            log.error("请求失败", e);
            SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
            SseEmitterUtils.sendMessage(ResponseConstant.ERROR_RESPONSE_MESSAGE, errorSseEmitter);
            return ResponseEntity.ok(errorSseEmitter);
        }
    }

    @RequestMapping(value = "/getAnalysisPromptInfoPage", method = RequestMethod.POST)
    public WebResult<WebPageInfo<AiPromptInternalInfo>> getAnalysisPromptInfoPage(@RequestBody QueryAiPromptInfoWebDto queryAiPromptInfoWebDto) {
        Page<AiPromptInternalInfo> result = aiPromptInternalInfoService
                .page(new Page<>(queryAiPromptInfoWebDto.getPageNum(), queryAiPromptInfoWebDto.getPageSize()),
                        new LambdaQueryWrapper<AiPromptInternalInfo>().eq(AiPromptInternalInfo::getDeleteFlag, YesOrNoEnum.NO.getCode()));
        return WebResult.success(WebPageInfo.pageConversion(result, AiPromptInternalInfo.class));
    }

    @RequestMapping(value = "/saveOrUpdateAnalysisPrompt", method = RequestMethod.POST)
    public WebResult<Boolean> saveOrUpdateAnalysisPrompt(@RequestBody @Valid AiPromptInternalInfo aiPromptInternalInfo) {
        boolean saveOrUpdate = aiPromptInternalInfoService.saveOrUpdate(aiPromptInternalInfo);
        return WebResult.success(saveOrUpdate);
    }

    @PostMapping("getOrgPerformanaceData")
    public WebResult<JSONObject> getOrgPerformanaceData(@RequestBody @Valid AiOrgPerformanceInfo aiPerformanaceData) {
        String performanceData = aiService.buildPerformanceAndVisitSigningData(aiPerformanaceData.getOrgId());
        return WebResult.success(JSONObject.parseObject(performanceData));
    }

}
