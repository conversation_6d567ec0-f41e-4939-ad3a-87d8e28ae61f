package com.ce.scrm.center.web.controller;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.service.business.CooperationBusiness;
import com.ce.scrm.center.service.business.entity.dto.cooperation.AgreeAndApproveBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.cooperation.AgreeCancelCooperationBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.cooperation.ApplyCancelCooperationBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.cooperation.GetApproveDetailsBusinessDto;
import com.ce.scrm.center.service.business.entity.view.CmEntWechatApproveDetailWebView;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.cooperation.AgreeAndApproveWebDto;
import com.ce.scrm.center.web.entity.dto.cooperation.AgreeCancelCooperationWebDto;
import com.ce.scrm.center.web.entity.dto.cooperation.ApplyCancelCooperationWebDto;
import com.ce.scrm.center.web.entity.dto.cooperation.GetApproveDetailsWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @version 1.0
 * @Description: 合作控制层
 * @Author: lijinpeng
 * @Date: 2024/10/17 15:34
 */
@Login
@Slf4j
@RestController
@RequestMapping("cooperation")
public class CooperationController {

    @Resource
    private CooperationBusiness cooperationBusiness;

    /*
     * @Description 合作：同意并发起审批
     * <AUTHOR>
     * @date 2024/10/21 14:45
     * @param agreeAndApproveWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     */
    @PostMapping("agreeAndApprove")
    public WebResult<Boolean> agreeAndApprove(@RequestBody AgreeAndApproveWebDto agreeAndApproveWebDto) {
        AgreeAndApproveBusinessDto agreeAndApproveBusinessDto = BeanUtil.copyProperties(agreeAndApproveWebDto, AgreeAndApproveBusinessDto.class);
        agreeAndApproveBusinessDto.setLoginEmployeeId(agreeAndApproveWebDto.getLoginEmployeeId());
        Boolean result = cooperationBusiness.agreeAndApprove(agreeAndApproveBusinessDto);
        return WebResult.success(result);
    }

    /*
     * @Description 申请取消合作
     * <AUTHOR>
     * @date 2024/10/23 15:00
     * @param applyCancelCooperationWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     */
    @PostMapping("applyCancelCooperation")
    public WebResult<Boolean> applyCancelCooperation(@RequestBody ApplyCancelCooperationWebDto applyCancelCooperationWebDto) {
        ApplyCancelCooperationBusinessDto applyCancelCooperationBusinessDto = BeanUtil.copyProperties(applyCancelCooperationWebDto, ApplyCancelCooperationBusinessDto.class);
        applyCancelCooperationBusinessDto.setSalerId(applyCancelCooperationWebDto.getLoginEmployeeId());
        Boolean result = cooperationBusiness.applyCancelCooperation(applyCancelCooperationBusinessDto);
        return WebResult.success(result);
    }

    /*
     * @Description 同意取消合作
     * <AUTHOR>
     * @date 2024/10/23 15:18
     * @param agreeCancelCooperationWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     */
    @PostMapping("agreeCancelCooperation")
    public WebResult<Boolean> agreeCancelCooperation(@RequestBody AgreeCancelCooperationWebDto agreeCancelCooperationWebDto) {
        AgreeCancelCooperationBusinessDto agreeCancelCooperationBusinessDto = BeanUtil.copyProperties(agreeCancelCooperationWebDto, AgreeCancelCooperationBusinessDto.class);
        Boolean result = cooperationBusiness.agreeCancelCooperation(agreeCancelCooperationBusinessDto);
        return WebResult.success(result);
    }

    /*
     * @Description 拒绝取消合作
     * <AUTHOR>
     * @date 2024/10/24 11:34
     * @param agreeCancelCooperationWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     */
    @PostMapping("disagreeCancelCooperation")
    public WebResult<Boolean> disagreeCancelCooperation(@RequestBody AgreeCancelCooperationWebDto agreeCancelCooperationWebDto) {
        AgreeCancelCooperationBusinessDto agreeCancelCooperationBusinessDto = BeanUtil.copyProperties(agreeCancelCooperationWebDto, AgreeCancelCooperationBusinessDto.class);
        Boolean result = cooperationBusiness.disagreeCancelCooperation(agreeCancelCooperationBusinessDto);
        return WebResult.success(result);
    }

    /*
     * @Description 获取审批详情
     * <AUTHOR>
     * @date 2024/10/23 15:39
     * @param getApproveDetailsWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.service.business.entity.view.CmEntWechatApproveDetailWebView>
     */
    @PostMapping("getApproveDetailsBySpNo")
    public WebResult<CmEntWechatApproveDetailWebView> getApproveDetailsBySpNo(@RequestBody GetApproveDetailsWebDto getApproveDetailsWebDto) {
        GetApproveDetailsBusinessDto getApproveDetailsBusinessDto = BeanUtil.copyProperties(getApproveDetailsWebDto, GetApproveDetailsBusinessDto.class);
        CmEntWechatApproveDetailWebView result = cooperationBusiness.getApproveDetailsBySpNo(getApproveDetailsBusinessDto);
        return WebResult.success(result);
    }

}
