package com.ce.scrm.center.web.controller.entwx.ai;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.AiVoiceAnalyze;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginType;
import com.ce.scrm.center.web.entity.dto.ai.VoiceAnalyzeWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.util.gptstreamutil.AiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ce.scrm.center.web.controller.ai.AiSmartController.simpleDateFormat;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/2/24 17:28
 */
@Slf4j
@RestController
@Login(loginType = LoginType.ENT_WECHAT)
@RequestMapping("entwx/voice")
public class AiSmartEntwxController {

    @Autowired
    private AiService aiService;

    @RequestMapping(value = "/getResPageList", produces = {"application/json;charset=UTF-8"})
    public WebResult<?> getResPageList(@RequestBody VoiceAnalyzeWebDto voiceAnalyzeWebDto) {
        log.info("当前登录人信息:{}", JSONObject.toJSONString(voiceAnalyzeWebDto));
        Page<AiVoiceAnalyze> page = aiService.getVoiceInfoPageList(voiceAnalyzeWebDto);
        Map<String, Object> map = new HashMap<>();
        map.put("pageSize", voiceAnalyzeWebDto.getPageSize());
        map.put("currentPage", voiceAnalyzeWebDto.getCurrentPage());
        map.put("totalPages", page.getPages());
        map.put("totalCount", page.getTotal());
        JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(page.getRecords()));
        List<JSONObject> jsonObjectList = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            try {
                jsonObject.put("createTime", simpleDateFormat.format(jsonObject.getDate("createTime")));
            } catch (Exception e) {
                jsonObject.put("createTime", "");
            }
            try {
                jsonObject.put("updateTime", simpleDateFormat.format(jsonObject.getDate("updateTime")));
            } catch (Exception e) {
                jsonObject.put("updateTime", "");
            }
            jsonObjectList.add(jsonObject);
        }
        map.put("list", jsonObjectList);
        return WebResult.success(map);
    }

    @RequestMapping(value = "/voiceReAnalyze", produces = {"application/json;charset=UTF-8"})
    public WebResult<?> voiceReAnalyze(@RequestBody VoiceAnalyzeWebDto voiceAnalyzeWebDto) {
        log.info("当前登录人信息:{}", JSONObject.toJSONString(voiceAnalyzeWebDto));
        JSONObject jsonObject = aiService.voiceReAnalyze(voiceAnalyzeWebDto);
        if (null == jsonObject) {
            return WebResult.error("400", "录音分析异常");
        }
        return WebResult.success(jsonObject);
    }


    @RequestMapping(value = "/getVoiceInfoById", produces = {"application/json;charset=UTF-8"})
    public WebResult<?> getVoiceInfoById(@RequestBody VoiceAnalyzeWebDto voiceAnalyzeWebDto) {
        log.info("当前登录人信息:{}", JSONObject.toJSONString(voiceAnalyzeWebDto));
        AiVoiceAnalyze analyze = aiService.getVoiceInfoById(voiceAnalyzeWebDto);
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(analyze));
        jsonObject.put("businessChatResponse", JSONObject.parseObject(jsonObject.getString("businessChatResponse")));
        return WebResult.success(jsonObject);
    }



}
