package com.ce.scrm.center.web.entity.view;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 历史线索导入字段
 */
@Data
public class ClueView implements Serializable {

    private static final long serialVersionUID = 1L;
    private String id;
    private String customerId;
    private String linkmanName;
    private Integer SEX;
    private String POSITION;
    private String MOBILE;
    private String TELEPHONE;
    private String MAIL;
    private String PROVINCE;
    private String CITY;

    private String ADDRESS;


    private String WE_CHAT;

    /**
     * 线索单号
     */
    private String clueCode;

    /**
     * 来源(线索类型)
     */
    private String source;

    /**
     * 创建人ID(如果是头条记录头条中的线索ID)
     */
    private String createrId;

    /**
     * 创建人名称
     */
    private String createrName;

    /**
     * 处理人部门ID
     */
    private String handleDeptId;

    /**
     * 处理人部门名称
     */
    private String handleDeptName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最新处理人ID
     */
    private String handlePersonId;

    /**
     * 最新处理人名称
     */
    private String handlePersonName;

    /**
     * 创建人部门ID
     */
    private String createDeptId;

    /**
     * 创建人部门名称
     */
    private String createDeptName;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 区县CODE
     */
    private String district;

    /**
     * QQ号
     */
    private String qq;

    /**
     * 一级行业CODE
     */
    private String industryOneCode;

    /**
     * 二级行业CODE
     */
    private String industryTwoCode;

    /**
     *
     */
    private String mainBusiness;

    /**
     * 客户需求
     */
    private String custRequirement;

    /**
     * 备注
     */
    private String remark;

    /**
     * 百度URL
     */
    private String baiduUrl;

    /**
     * 线索类型-子类型
     */
    private String baiduSource;

    /**
     *
     */
    private String clueLabel;

    /**
     * 线索来源 400的二级渠道
     */
    private String child400;

    /**
     * 来源URL
     */
    private String sourceUrl;

    /**
     * 落地URL
     */
    private String landUrl;

    /**
     * 活动特征码
     */
    private String promoteSlug;

    /**
     * 渠道特征码
     */
    private String channelSlug;

    /**
     * 特征码
     */
    private String slug;

    /**
     * 表单类型
     */
    private String formType;

    /**
     * 300咨询信息
     */
    private String others;

    /**
     * 表单提交URL
     */
    private String formSubmitUrl;

    /**
     * 端口
     */
    private String vdevice;

    /**
     * 二级渠道特征码
     */
    private String channelChildSlug;

    /**
     * 附件ID
     */
    private String fileId;

    /**
     *
     */
    private String cityLabel;

    /**
     *
     */
    private String districtLabel;

    /**
     *
     */
    private String provinceLabel;

    /**
     * 客户ID
     */
    private String custId;

    /**
     * 聊天记录
     */
    private String chat;

    /**
     * 关键词1
     */
    private String keyword1;

    /**
     * 关键词2
     */
    private String keyword2;

    /**
     * 关键词3
     */
    private String utmTerm;

    /**
     * 来源域名
     */
    private String fromPage;

    /**
     * 三方推送的线索ID
     */
    private String sourceDataId;

}
