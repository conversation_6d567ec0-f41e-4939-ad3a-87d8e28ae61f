package com.ce.scrm.center.web.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:28:30
 * @desc 客户剩余保护时长筛选项枚举
 */
public enum CustomerSdrFollowCountFilterEnum implements EnumBase {

    CFCF0(0, "未拜访"),
    CFCF1(1, "1次"),
    CFCF2(2, "2次"),
    CFCF3(3, "3次"),
    CFCF4(4, "4次"),
    CFCF5(5, "5次"),
    CFCF6(6, "大于5次"),
    CFCF7(7, "全部"),
    ;

    @Getter
    private final Integer label;
    @Getter
    private final String value;

    // Getter 方法
    public String getKey() {
        return String.valueOf(label);
    }

    // 构造函数
    CustomerSdrFollowCountFilterEnum(Integer label, String value) {
        this.label = label;
        this.value = value;
    }

    public static String getCount(Integer label) {
        switch (label) {
            case 0:
                return "!@";
            case 1:
                return "1";
            case 2:
                return "2";
            case 3:
                return "3";
            case 4:
                return "4";
            case 5:
                return "5";
            case 6:
                return "r:>5";
            case 7:
            default:
                return null;
        }
    }
}
