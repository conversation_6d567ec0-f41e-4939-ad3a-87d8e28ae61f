package com.ce.scrm.center.web.entity.dto.customer;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 客户历史曾用名
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerNearByTwentyWebDto extends LoginInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 页码
	 */
	private Integer pageNum = 1;

	/**
	 * 每页数量
	 */
	private Integer pageSize = 10;

	/**
	 * pid
	 */
	private String pid;

	/**
	 * custId 客户id
	 */
	@NotBlank(message = "custId不能为空")
	private String custId;

	/**
	 * 附近多少米的客户
	 */
	@NotBlank(message = "距离范围不能为空")
	private String distanceRangeMeter = "2000";
}
