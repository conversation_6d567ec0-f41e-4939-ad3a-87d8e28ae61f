package com.ce.scrm.center.web.config;

import com.fasterxml.jackson.databind.ser.std.ToStringSerializerBase;

/**
 * 自定义转String序列化转换器
 * <AUTHOR>
 * @date 2024/5/23 下午5:42
 * @version 1.0.0
 */
public class CustomToStringSerializer extends ToStringSerializerBase {
    /**
     * 单例
     */
    public static final CustomToStringSerializer INSTANCE = new CustomToStringSerializer();

    private CustomToStringSerializer() {
        super(Object.class);
    }

    public CustomToStringSerializer(Class<?> handledType) {
        super(handledType);
    }

    /**
     * 自定义转换方式
     * @param value 原始值
     * <AUTHOR>
     * @date 2024/5/23 下午5:45
     * @return java.lang.String
     **/
    @Override
    public final String valueToString(Object value) {
        if (value == null) {
            return null;
        }
        return String.valueOf(value);
    }
}