package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 根据名字获取客户信息
 * @Author: lijinpeng
 * @Date: 2024/12/5 16:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerTwoNameWebDto extends LoginInfo implements Serializable {

    @NotNull
    private String name;

}
