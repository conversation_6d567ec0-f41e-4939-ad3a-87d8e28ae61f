package com.ce.scrm.center.web.util.gptstreamutil;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/7/4
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class StreamAnswer {

    private String content;

    private int type;

    private String reasoningContent;

    private ExtralInfo extraInfo;

    public StreamAnswer(String content, int type) {
        this.content = content;
        this.type = type;
    }

    public StreamAnswer(String content, int type, String reasoningContent) {
        this.content = content;
        this.type = type;
        this.reasoningContent = reasoningContent;
    }


}
