package com.ce.scrm.center.web.dubbo.provider;

import cn.ce.cesupport.enums.ProtectStatusEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.dubbo.api.CboCustomerDubbo;
import com.ce.scrm.center.dubbo.entity.dto.CboCustomerDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.CboCustomerPayDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.CboProtectDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.center.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.view.CboContactPersonDubboView;
import com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogBusinessDto;
import com.ce.scrm.center.service.enums.ConvertRelationEnum;
import com.ce.scrm.center.service.enums.ProtectCustTypeEnum;
import com.ce.scrm.center.service.third.invoke.SmaConvertLogThirdService;
import com.ce.scrm.center.service.yml.ThirdCustomerConfig;
import com.ce.scrm.center.web.util.PageUtil;
import com.ce.scrm.customer.dubbo.api.IContactPersonDubbo;
import com.ce.scrm.customer.dubbo.api.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerConditionDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerDetailDubboDto;
import com.ce.scrm.customer.dubbo.entity.view.ContactInfoDubboView;
import com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboView;
import com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/14
 */
@Slf4j
@DubboService(interfaceClass = CboCustomerDubbo.class)
public class CboCustomerDubboService implements CboCustomerDubbo {

    @Resource
    private ThirdCustomerConfig thirdCustomerConfig;

    @Autowired
    private CmCustProtectService cmCustProtectService;

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private ICustomerDubbo customerDubbo;

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private IContactPersonDubbo contactPersonDubbo;

    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;

    /***
     * 保护关系查询
     * @param cboProtectDubboDto
     * <AUTHOR>
     * @date 2024/11/14 18:31
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.response.DubboPageInfo<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>>
     **/
    @Override
    public DubboResult<DubboPageInfo<CboProtectDubboView>> getProtectByCondition(CboProtectDubboDto cboProtectDubboDto) {
        if (Objects.isNull(cboProtectDubboDto) || StringUtils.isBlank(cboProtectDubboDto.getSalerId())){
            return DubboResult.error(DubboCodeMessageEnum.PARAM_EMPTY);
        }
        if (cboProtectDubboDto.getPageNum()==null || cboProtectDubboDto.getPageNum()<1){
            cboProtectDubboDto.setPageNum(1);
        }
        if (cboProtectDubboDto.getPageSize()==null || cboProtectDubboDto.getPageSize()<1 || cboProtectDubboDto.getPageSize()>100){
            cboProtectDubboDto.setPageSize(10);
        }
        LambdaQueryWrapper<CmCustProtect> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmCustProtect::getSalerId, cboProtectDubboDto.getSalerId());
        queryWrapper.eq(CmCustProtect::getStatus, 1);
        if (StringUtils.isNotBlank(cboProtectDubboDto.getCustomerName())) {
            queryWrapper.like(CmCustProtect::getCustName, cboProtectDubboDto.getCustomerName());
        }
        if (StringUtils.isNotBlank(cboProtectDubboDto.getCustomerId())) {
            queryWrapper.eq(CmCustProtect::getCustId, cboProtectDubboDto.getCustomerId());
        }
        queryWrapper.orderByDesc(CmCustProtect::getUpdateTime);
        log.info("当前参数为:{}", JSON.toJSONString(queryWrapper.getSqlSelect()));
        Page<CmCustProtect> page = Page.of(cboProtectDubboDto.getPageNum(), cboProtectDubboDto.getPageSize());
        cmCustProtectService.page(page, queryWrapper);
        List<CmCustProtect> records = page.getRecords();
        List<CboProtectDubboView> list = Optional.ofNullable(records).orElse(Lists.newArrayList()).stream().map(record -> {
            CboProtectDubboView protectDubboView = new CboProtectDubboView();
            protectDubboView.setCustomerId(record.getCustId());
            protectDubboView.setCustomerName(record.getCustName());
            //默认是企业
            protectDubboView.setCustomerType(1);

            CustomerDetailDubboDto customerDetailDubboDto =new CustomerDetailDubboDto();
            customerDetailDubboDto.setCustomerId(record.getCustId());
            setSignData(customerDetailDubboDto);
            com.ce.scrm.customer.dubbo.entity.response.DubboResult<CustomerDubboView> detail = customerDubbo.detail(customerDetailDubboDto);
            if (detail!=null && detail.checkSuccess() && detail.getData()!=null){
                if (Objects.equals(detail.getData().getCustomerType(),2)) {
                    protectDubboView.setCustomerType(2);
                }
            }
            protectDubboView.setSalerId(record.getSalerId());
            return protectDubboView;
        }).collect(Collectors.toList());
        DubboPageInfo<CboProtectDubboView> returnPage = PageUtil.pageConversion(page, CboProtectDubboView.class);
        returnPage.setList(list);
        return DubboResult.success(returnPage);
    }

    @Override
    public DubboResult<List<CboContactPersonDubboView>> getContactPersonByCondition(String customerId) {

        if (StringUtils.isBlank(customerId)){
            return DubboResult.error(DubboCodeMessageEnum.PARAM_EMPTY);
        }
        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setCustomerId(customerId);
        setSignData(customerDetailDubboDto);
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<CustomerDubboView> dubboViewDubboResult = contactPersonDubbo.customerContactDetail(customerDetailDubboDto);
        if (dubboViewDubboResult==null || !dubboViewDubboResult.checkSuccess() || dubboViewDubboResult.getData()==null){
            return DubboResult.success();
        }
//        log.info("cbo联系人列表={}", JSONObject.toJSONString(dubboViewDubboResult));
        CustomerDubboView customerDubboView = dubboViewDubboResult.getData();
        List<ContactPersonDubboView> contactPersonDubboViewList = customerDubboView.getContactPersonDubboViewList();

        List<CboContactPersonDubboView> list = new ArrayList<>();
        Optional.ofNullable(contactPersonDubboViewList).orElse(Lists.newArrayList()).stream().forEach(contactPerson -> {
            //邮箱列表
            List<String> mailList = Optional.ofNullable(contactPerson.getContactInfoList()).orElse(Lists.newArrayList()).stream()
                    .filter(contactInfo -> contactInfo.getContactType()==3).filter(contactInfo -> StringUtils.isNotBlank(contactInfo.getContactWay())).map(ContactInfoDubboView::getContactWay).collect(Collectors.toList());
            //手机号列表
            Optional.ofNullable(contactPerson.getContactInfoList()).orElse(Lists.newArrayList()).stream()
                    .filter(contactInfo -> contactInfo.getContactType()==1).filter(contactInfo -> StringUtils.isNotBlank(contactInfo.getContactWay()))
                    .forEach(contactInfo -> {
                        CboContactPersonDubboView contactPersonDubboView = new CboContactPersonDubboView();
                        contactPersonDubboView.setCustomerId(customerDubboView.getCustomerId());
                        contactPersonDubboView.setCustomerName(customerDubboView.getCustomerName());
                        contactPersonDubboView.setContactPersonId(contactPerson.getContactPersonId());
                        contactPersonDubboView.setContactPersonName(contactPerson.getContactPersonName());
                        contactPersonDubboView.setGender(contactPerson.getGender());
                        contactPersonDubboView.setMobile(contactInfo.getContactWay());
                        contactPersonDubboView.setSource(contactPerson.getSourceTag());
                        if (Objects.equals(contactInfo.getPhoneVerifiedFlag(),1)){
                            contactPersonDubboView.setMobileStatus(1);
                        }else{
                            contactPersonDubboView.setMobileStatus(0);
                        }
                        if (CollectionUtils.isNotEmpty(mailList)){
                            contactPersonDubboView.setEmail(mailList.get(0));
                        }
                        contactPersonDubboView.setCreateTime(contactPerson.getCreateTime());
                        contactPersonDubboView.setLegalPersonFlag(contactPerson.getLegalPersonFlag());
                        list.add(contactPersonDubboView);
                    });

        });
        if (CollectionUtils.isEmpty(list)){
            return DubboResult.success(Lists.newArrayList());
        }
        return DubboResult.success(list);
    }

    @Override
    public DubboResult<CboProtectDubboView> getProtectByCustomerId(CboProtectDubboDto cboProtectDubboDto) {
        if (Objects.isNull(cboProtectDubboDto) || StringUtils.isBlank(cboProtectDubboDto.getCustomerId())){
            return DubboResult.error(DubboCodeMessageEnum.PARAM_EMPTY);
        }
        LambdaQueryWrapper<CmCustProtect> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmCustProtect::getCustId, cboProtectDubboDto.getCustomerId());
        queryWrapper.eq(CmCustProtect::getAreaId, "3950");
        queryWrapper.eq(CmCustProtect::getStatus, 1).last("limit 1");
        CmCustProtect cmCustProtect = cmCustProtectService.getOne(queryWrapper);
        if (!Objects.isNull(cmCustProtect)){
            CboProtectDubboView protectDubboView = new CboProtectDubboView();
            protectDubboView.setCustomerType(1);
            CustomerDetailDubboDto customerDetailDubboDto =new CustomerDetailDubboDto();
            customerDetailDubboDto.setCustomerId(cmCustProtect.getCustId());
            setSignData(customerDetailDubboDto);
            com.ce.scrm.customer.dubbo.entity.response.DubboResult<CustomerDubboView> detail = customerDubbo.detail(customerDetailDubboDto);
            if (detail!=null && detail.checkSuccess() && detail.getData()!=null){
                if (Objects.equals(detail.getData().getCustomerType(),2)) {
                    protectDubboView.setCustomerType(2);
                }
            }
            protectDubboView.setCustomerId(cmCustProtect.getCustId());
            protectDubboView.setCustomerName(cmCustProtect.getCustName());
            protectDubboView.setSalerId(cmCustProtect.getSalerId());
            protectDubboView.setDeptId(cmCustProtect.getBussdeptId());
            protectDubboView.setSubId(cmCustProtect.getSubcompanyId());
            protectDubboView.setAreaId(cmCustProtect.getAreaId());
            return DubboResult.success(protectDubboView);
        }
        return DubboResult.success(null);
    }

    @Override
    public DubboResult<CboProtectDubboView> getProtectByCustomerName(CboProtectDubboDto cboProtectDubboDto) {
        if (Objects.isNull(cboProtectDubboDto) || StringUtils.isBlank(cboProtectDubboDto.getCustomerName())){
            return DubboResult.error(DubboCodeMessageEnum.PARAM_EMPTY);
        }
        LambdaQueryWrapper<CmCustProtect> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmCustProtect::getCustName, cboProtectDubboDto.getCustomerName());
        queryWrapper.eq(CmCustProtect::getAreaId, "3950");
        queryWrapper.eq(CmCustProtect::getStatus, 1).last("limit 1");
        CmCustProtect cmCustProtect = cmCustProtectService.getOne(queryWrapper);
        if (!Objects.isNull(cmCustProtect)){
            CboProtectDubboView protectDubboView = new CboProtectDubboView();
            protectDubboView.setCustomerType(1);
            CustomerDetailDubboDto customerDetailDubboDto =new CustomerDetailDubboDto();
            customerDetailDubboDto.setCustomerId(cmCustProtect.getCustId());
            setSignData(customerDetailDubboDto);
            com.ce.scrm.customer.dubbo.entity.response.DubboResult<CustomerDubboView> detail = customerDubbo.detail(customerDetailDubboDto);
            if (detail!=null && detail.checkSuccess() && detail.getData()!=null){
                if (Objects.equals(detail.getData().getCustomerType(),2)) {
                    protectDubboView.setCustomerType(2);
                }
            }
            protectDubboView.setCustomerId(cmCustProtect.getCustId());
            protectDubboView.setCustomerName(cmCustProtect.getCustName());
            protectDubboView.setSalerId(cmCustProtect.getSalerId());
            protectDubboView.setDeptId(cmCustProtect.getBussdeptId());
            protectDubboView.setSubId(cmCustProtect.getSubcompanyId());
            protectDubboView.setAreaId(cmCustProtect.getAreaId());
            return DubboResult.success(protectDubboView);
        }
        return DubboResult.success(null);
    }

    @Override
    public DubboResult<CboProtectDubboView> getCustomerInfoByCondition(CboCustomerDubboDto cboCustomerDubboDto) {
        if (StringUtils.isNotBlank(cboCustomerDubboDto.getCustomerId())){
            CustomerDetailDubboDto customerDetailDubboDto =new CustomerDetailDubboDto();
            customerDetailDubboDto.setCustomerId(cboCustomerDubboDto.getCustomerId());
            setSignData(customerDetailDubboDto);
            com.ce.scrm.customer.dubbo.entity.response.DubboResult<CustomerDubboView> dubboResult = customerDubbo.detail(customerDetailDubboDto);
            if (dubboResult!=null && dubboResult.checkSuccess() && dubboResult.getData()!=null) {
                CustomerDubboView customerDubboView = dubboResult.getData();
                CboProtectDubboView protectDubboView = new CboProtectDubboView();
                if (Objects.equals(customerDubboView.getCustomerType(),2)){
                    protectDubboView.setCustomerType(2);
                }else{
                    protectDubboView.setCustomerType(1);
                }
                protectDubboView.setCustomerId(customerDubboView.getCustomerId());
                protectDubboView.setCustomerName(customerDubboView.getCustomerName());
                protectDubboView.setProvinceCode(customerDubboView.getProvinceCode());
                protectDubboView.setProvinceName(customerDubboView.getProvinceName());
                protectDubboView.setCityCode(customerDubboView.getCityCode());
                protectDubboView.setCityName(customerDubboView.getCityName());
                protectDubboView.setDistrictCode(customerDubboView.getDistrictCode());
                protectDubboView.setDistrictName(customerDubboView.getDistrictName());
                protectDubboView.setTagLostCust(customerDubboView.getTagLostCust());
                protectDubboView.setTagLostTime(customerDubboView.getTagLostTime());
                protectDubboView.setFirstIndustryCode(customerDubboView.getFirstIndustryCode());
                protectDubboView.setFirstIndustryName(customerDubboView.getFirstIndustryName());
                protectDubboView.setSecondIndustryCode(customerDubboView.getSecondIndustryCode());
                protectDubboView.setSecondIndustryName(customerDubboView.getSecondIndustryName());
                return DubboResult.success(protectDubboView);
            }
        }else{
            CustomerConditionDubboDto customerConditionDubboDto = new CustomerConditionDubboDto();
            customerConditionDubboDto.setCustomerName(cboCustomerDubboDto.getCustomerName());
            setSignData(customerConditionDubboDto);
            com.ce.scrm.customer.dubbo.entity.response.DubboResult<List<CustomerDubboView>> dubboResult = customerDubbo.findByCondition(customerConditionDubboDto);
            if (dubboResult!=null && dubboResult.checkSuccess() && CollectionUtils.isNotEmpty(dubboResult.getData())){
                CustomerDubboView customerDubboView = dubboResult.getData().get(0);
                CboProtectDubboView protectDubboView = new CboProtectDubboView();
                if (Objects.equals(customerDubboView.getCustomerType(),2)){
                    protectDubboView.setCustomerType(2);
                }else{
                    protectDubboView.setCustomerType(1);
                }
                protectDubboView.setCustomerId(customerDubboView.getCustomerId());
                protectDubboView.setCustomerName(customerDubboView.getCustomerName());
                protectDubboView.setProvinceCode(customerDubboView.getProvinceCode());
                protectDubboView.setProvinceName(customerDubboView.getProvinceName());
                protectDubboView.setCityCode(customerDubboView.getCityCode());
                protectDubboView.setCityName(customerDubboView.getCityName());
                protectDubboView.setDistrictCode(customerDubboView.getDistrictCode());
                protectDubboView.setDistrictName(customerDubboView.getDistrictName());
                protectDubboView.setTagLostCust(customerDubboView.getTagLostCust());
                protectDubboView.setTagLostTime(customerDubboView.getTagLostTime());
                protectDubboView.setFirstIndustryCode(customerDubboView.getFirstIndustryCode());
                protectDubboView.setFirstIndustryName(customerDubboView.getFirstIndustryName());
                protectDubboView.setSecondIndustryCode(customerDubboView.getSecondIndustryCode());
                protectDubboView.setSecondIndustryName(customerDubboView.getSecondIndustryName());
                return DubboResult.success(protectDubboView);
            }
        }
        return DubboResult.success();
    }

    @Override
    public DubboResult<Boolean> orderPay(CboCustomerPayDubboDto cboCustomerPayDubboDto) {
        Date now = new Date();
        LambdaQueryWrapper<CmCustProtect> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmCustProtect::getCustId, cboCustomerPayDubboDto.getCustomerId());
        queryWrapper.eq(CmCustProtect::getSalerId, cboCustomerPayDubboDto.getSalerId());
        queryWrapper.eq(CmCustProtect::getStatus, ProtectStatusEnum.PROTECT.getCode());
        CmCustProtect custProtect = cmCustProtectService.getOne(queryWrapper);
        if (custProtect ==null){
            log.error("保护关系不存在,无法处理签单逻辑,参数={}",JSONObject.toJSONString(cboCustomerPayDubboDto));
            return DubboResult.error("10010","保护关系不存在,无法处理签单逻辑");
        }
        if (ProtectCustTypeEnum.PROTECT_FOLLOW.getValue().equals(custProtect.getCustType())) {
            custProtect.setCustType(ProtectCustTypeEnum.SECOND_DEVELOPMENT.getValue());
            // 首次签单时间：当前商务保护的这个客户首次签单时，set首次签单时间
            custProtect.setFirstSignTime(cboCustomerPayDubboDto.getPayTime());
            //SELF_ORDER("自签",6),
            custProtect.setCustSource(6);
            custProtect.setExceedTime(null);
            custProtect.setUpdateBy("admin");
            custProtect.setUpdateTime(now);
            custProtect.setLastPayTime(cboCustomerPayDubboDto.getPayTime());
            custProtect.setCooperate(null);
            // 新的流转逻辑上线，客户再签单（无论新老客），都不再加 绝对保护期
            cmCustProtectService.updateById(custProtect);
            try{
                ConvertLogBusinessDto build = ConvertLogBusinessDto.builder()
                        .custId(custProtect.getCustId())
                        .areaOfCurSalerId(custProtect.getAreaId())
                        .subcompanyOfCurSalerId(custProtect.getSubcompanyId())
                        .buOfCurSalerId(custProtect.getBuId())
                        .deptOfCurSalerId(custProtect.getBussdeptId())
                        .curSalerId(custProtect.getSalerId())
                        .createBy(custProtect.getSalerId())
                        .createTime(new Date())
                        .areaOfSalerId(custProtect.getAreaId())
                        .subcompanyOfSalerId(custProtect.getSubcompanyId())
                        .buOfSalerId(custProtect.getBuId())
                        .deptOfSalerId(custProtect.getBussdeptId())
                        .salerId(custProtect.getSalerId())
                        .convertType(ConvertRelationEnum.SIGNED.getValue())
                        .custName(custProtect.getCustName())
                        .releaseReason(ConvertRelationEnum.SIGNED.getLable())
                        .build();
                smaConvertLogThirdService.insertLog(build);
            }catch (Exception e){
                log.error("保存流转日志失败,参数={}",JSONObject.toJSONString(cboCustomerPayDubboDto),e);
            }
        }
        return DubboResult.success(true);
    }

    private void setSignData(SignData signData) {
        signData.setSourceKey(thirdCustomerConfig.getKey());
        signData.setSourceSecret(thirdCustomerConfig.getSecret());
    }
}
