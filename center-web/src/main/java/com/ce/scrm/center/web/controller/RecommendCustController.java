package com.ce.scrm.center.web.controller;

import cn.hutool.extra.cglib.CglibUtil;
import com.ce.scrm.center.service.business.RecommendCustBusiness;
import com.ce.scrm.center.service.business.entity.dto.RecommendCustBusinessDto;
import com.ce.scrm.center.service.business.entity.view.RecommendCustBusinessView;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.controller.base.BaseController;
import com.ce.scrm.center.web.entity.dto.RecommendCustWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.RecommendCustWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Optional;

import static com.ce.scrm.center.web.entity.response.WebCodeMessageEnum.DATA_NOT_EXIST;
import static com.ce.scrm.center.web.entity.response.WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL;

/**
 * <AUTHOR>
 * @description 推荐客户
 * @createDate 2024-08-13
 */
@Login
@Slf4j
@RestController
@RequestMapping("recommend/cust")
public class RecommendCustController extends BaseController {

    @Resource
    private RecommendCustBusiness recommendCustBusiness;

    /**
     * 获取推荐人详细信息
     *
     * @param recommendCustWebDto 推荐客户参数
     * <AUTHOR>
     * @date 2024/8/13
     **/
    @PostMapping("firstDetail")
    public WebResult<RecommendCustWebView> firstDetail(@RequestBody RecommendCustWebDto recommendCustWebDto) {
        if (null == recommendCustWebDto || (recommendCustWebDto.getRecommendCustId() == null && recommendCustWebDto.getRecommendedCustId() == null)) {
            return WebResult.error(REQUEST_PARAM_NOT_NULL);
        }
        Optional<RecommendCustBusinessView> recommendCustBusinessViewOptional = recommendCustBusiness.findFirstRecommendCust(CglibUtil.copy(recommendCustWebDto, RecommendCustBusinessDto.class));
        if (recommendCustBusinessViewOptional.isPresent()) {
            RecommendCustBusinessView recommendCustBusinessView = recommendCustBusinessViewOptional.get();
            return WebResult.success(CglibUtil.copy(recommendCustBusinessView, RecommendCustWebView.class));
        }
        return WebResult.success();
    }

}