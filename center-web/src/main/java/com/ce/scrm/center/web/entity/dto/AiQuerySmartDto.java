package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import java.util.List;

@Data
public class AiQuerySmartDto extends LoginInfo {

    /**
     * channelId 默认 空
     */
    private String channelId;

    /**
     * pid 直接分析的时候传
     */
    private String pid;
    /**
     * custId  直接分析的时候传
     */
    private String custId;

    /**
     * 商机code 直接分析的时候传
     */
    private String busiOppoCode;

    /**
     * promptId 直接分析的时候传
     */
    private String promptId;

    /**
     * 2025-07-10 新增
     * extraPromptId 直接分析的时候 可以多传一个行业解决方案
     */
    private String extraPromptId;

    /**
     * 来源 WECOM / pc  所有的接口都需要传
     */
    private String platform;

    /**
     * 高级对话: 聊天ID 首次不传
     */
    private String chatId;

    /**
     * 高级对话: 是否需要联网搜索
     */
    private boolean needSearch;

    /**
     * 高级对话: 图片生成
     */
    private boolean imageGenerator;

    /**
     * 高级对话:  问题来源：0.自问，1.预置问题
     */
    private Integer questionSource;

    /**
     * 问题类型：0.默认值，1.文字提问，2.图片类型提问，3.ppt方案整合问题，4.海关数据提问，5.生成图片问题，6.分析方向直接生成,7 网站检测分析,8:新生成,9:老方案修改
     */
    private Integer questionType;


    /**
     * 操作行为：0.无操作，1.点赞，2.点low，3.复制，4.生成ppt，5.聊天，6.直接分析，7.重新分析，8.故障上报
     */
    private Integer operateType;


    /**
     * 父级事件ID
     * 高级对话的时候传    点赞   点low  复制  生成ppt 故障上报     的时候传
     */
    private String parentEventId;

    /**
     * 点low原因
     */
    private String lowReason;

    /**
     * ppt方案整合问题 的时候传
     */
    private List<String> linkedEventIds;

    /**
     * 问题
     */
    private String chatQuestion;

    /**
     * 图片提问时候的图片地址
     */
    private List<String> imageUrls;

    private String analyzeDomain;

    private Long voiceId;


}
