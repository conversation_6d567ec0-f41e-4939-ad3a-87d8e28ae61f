package com.ce.scrm.center.web.entity.dto;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import com.ce.scrm.center.service.business.entity.dto.CirculationLossPageBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum;
import com.ce.scrm.center.service.router.RouterContext;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

/**
 * Description: 流转流失客户查询参数
 * @author: JiuDD
 * date: 2024/7/22
 */
@EqualsAndHashCode
@Data
public class CirculationLossWebPageDto extends LoginInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 区域
     */
    private String areaId;
    /**
     * 分公司
     */
    private String subId;
    /**
     * 事业部id
     */
    private String buId;
    /**
     * 部门
     */
    private String deptId;
    /**
     * 商务ID
     */
    private String salerId;
    /**
     * 预计流转（流失）日期
     */
    private LocalDate preDate;
    /**
     * 来源，1流转 2流失
     */
    @NotNull(message = "来源不能为空")
    private Integer origin;
    /**
     * 排序字段：1预计流转日期
     */
    private Integer orderBy;
    /**
     * 排序方式：asc 升序 desc 降序
     */
    private String orderByType;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 客户分层（商务）
     */
    private Integer customerLayer;


    public CirculationLossPageBusinessDto packageBusinessDto(CirculationLossWebPageDto webPageDto) {
        EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
        CirculationLossPageBusinessDto businessDto = new CirculationLossPageBusinessDto();
        BeanUtils.copyProperties(webPageDto, businessDto);
        // 组织架构筛选条件
        if (EmpPositionConstant.BUSINESS_AREA.equals(webPageDto.getLoginPosition())) {
            businessDto.setAreaId(webPageDto.getLoginAreaId());
        } else if (PositionUtil.isBusinessMajor(webPageDto.getLoginPosition())) {
            businessDto.setAreaId(webPageDto.getLoginAreaId());
            businessDto.setSubId(webPageDto.getLoginSubId());
        } else if (PositionUtil.isBusinessBu(webPageDto.getLoginPosition())) {
            businessDto.setAreaId(webPageDto.getLoginAreaId());
            businessDto.setSubId(webPageDto.getLoginSubId());
            businessDto.setBuId(Objects.isNull(currentUser) ? null : currentUser.getBuId());
        } else if (PositionUtil.isBusinessManager(webPageDto.getLoginPosition())) {
            businessDto.setAreaId(webPageDto.getLoginAreaId());
            businessDto.setSubId(webPageDto.getLoginSubId());
            businessDto.setDeptId(webPageDto.getLoginOrgId());
        } else if (PositionUtil.isBusinessSaler(webPageDto.getLoginPosition())) {
            businessDto.setAreaId(webPageDto.getLoginAreaId());
            businessDto.setSubId(webPageDto.getLoginSubId());
            businessDto.setDeptId(webPageDto.getLoginOrgId());
            businessDto.setSalerId(webPageDto.getLoginEmployeeId());
        } else if (EmpPositionConstant.BUSINESS_SYS_MANAGER.equals(webPageDto.getLoginPosition())) {
            // 系统管理员默认显示全部数据
        } else {
            // 其他角色默认不显示数据
            return null;
        }
        if (1 == webPageDto.getOrigin()) {
            businessDto.setOrigin(AssignCustSourceSpecialEnum.CIRCULATION.getValue());
        } else if (2 == webPageDto.getOrigin()) {
            businessDto.setOrigin(AssignCustSourceSpecialEnum.LOSS.getValue());
        }
        return businessDto;
    }
}