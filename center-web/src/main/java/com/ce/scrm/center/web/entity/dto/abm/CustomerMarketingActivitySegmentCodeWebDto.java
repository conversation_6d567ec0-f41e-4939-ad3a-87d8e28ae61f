package com.ce.scrm.center.web.entity.dto.abm;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 客户营销列表请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerMarketingActivitySegmentCodeWebDto extends LoginInfo implements Serializable {

	/**
	 * CDP分群code
	 */
	@NotBlank(message = "CDP分群code不能为空")
	private String segmentCode;

}
