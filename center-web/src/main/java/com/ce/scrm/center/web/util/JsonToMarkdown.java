package com.ce.scrm.center.web.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

@Slf4j
public class JsonToMarkdown {
    public static String convert2TmarkDown(String jsonString) {
        try {
            Object root = JSON.parse(jsonString, Feature.OrderedField);  // 保序解析
            return convert(root, "", 2, 0);  // 从 h2 开始，缩进从0层
        } catch (Exception e) {
            log.error("转换json to markdown失败", e);
        }
        return jsonString;
    }

    private static String convert(Object obj, String title, int level, int indentLevel) {
        StringBuilder markdown = new StringBuilder();
        String indent = repeat("> ", indentLevel);
        String headingPrefix = repeat("#", Math.min(level, 6));
        String fullHeading = indent + headingPrefix + " " + title + "\n\n";
        if (obj instanceof JSONObject) {
            markdown.append(fullHeading);
            JSONObject jsonObj = (JSONObject) obj;
            for (String key : jsonObj.keySet()) {
                Object value = jsonObj.get(key);
                if (value instanceof JSONArray || value instanceof JSONObject) {
                    markdown.append(convert(value, key + "明细", level + 1, indentLevel + 1));
                } else {
                    markdown.append(indent).append("- ").append(key).append(": ").append(value).append("\n");
                }
            }
            markdown.append("\n");
        } else if (obj instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray) obj;
            markdown.append(fullHeading);
            boolean isPrimitive = true;
            for (Object item : jsonArray) {
                if (item instanceof JSONObject || item instanceof JSONArray) {
                    isPrimitive = false;
                    break;
                }
            }
            if (isPrimitive) {
                markdown.append(indent).append("| 序号 | 值 |\n").append(indent).append("| --- | --- |\n");
                for (int i = 0; i < jsonArray.size(); i++) {
                    markdown.append(indent).append("| ").append(i + 1).append(" | ").append(jsonArray.get(i)).append(" |\n");
                }
                markdown.append("\n");
                return markdown.toString();
            }
            // 收集字段（以第一条为准补全）
            LinkedHashSet<String> fieldSet = new LinkedHashSet<>();
            for (Object item : jsonArray) {
                if (item instanceof JSONObject) {
                    JSONObject row = (JSONObject) item;
                    fieldSet.addAll(row.keySet());
                }
            }
            // 表头
            markdown.append(indent).append("| 序号 | ");
            for (String field : fieldSet) {
                markdown.append(field).append(" | ");
            }
            markdown.append("\n").append(indent).append("| --- | ");
            for (int i = 0; i < fieldSet.size(); i++) {
                markdown.append("--- | ");
            }
            markdown.append("\n");
            // 表体
            List<String> subTables = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject row = jsonArray.getJSONObject(i);
                markdown.append(indent).append("| ").append(i + 1).append(" | ");
                for (String field : fieldSet) {
                    Object cell = row.get(field);
                    if (cell instanceof JSONArray || cell instanceof JSONObject) {
                        String subTitle = field + "明细（来自 " + title + " 第" + (i + 1) + "条）";
                        markdown.append("[查看").append(subTitle).append("] | ");
                        subTables.add(convert(cell, subTitle, level + 1, indentLevel + 1));
                    } else {
                        markdown.append(cell != null ? cell.toString() : "").append(" | ");
                    }
                }
                markdown.append("\n");
            }
            for (String sub : subTables) {
                markdown.append("\n").append(sub);
            }
            markdown.append("\n");
        } else {
            markdown.append(fullHeading).append(obj.toString());
        }
        return markdown.toString();
    }

    private static String repeat(String s, int count) {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < count; i++) {
            builder.append(s);
        }
        return builder.toString();
    }

    public static void main(String[] args) {
        System.out.println(convert2TmarkDown("{\n" +
                "    \"跟进分析\": {\n" +
                "        \"销售阶段\": \"初步建联\",\n" +
                "        \"跟进内容\": \"该段录音内容含大量无意义语句和模糊表达，未能明确提取客户画像、需求痛点或沟通进展等关键信息。需进一步确认对话背景及核心内容以提炼有效信息。\"\n" +
                "    },\n" +
                "    \"对话分析\": {\n" +
                "        \"分析结果\": \"从这段谈话内容来看，信息较为模糊且缺乏明确的商务意图或客户需求。以下是提炼出的一些可能影响客户签约的信息点，但重要程度较低：\\n\\n1. **沟通语言不清晰**：谈话中使用了大量不连贯的中英文混合表达，可能会导致双方在沟通上存在障碍，无法准确传递核心需求或服务价值。（重要程度：低）\\n   \\n2. **潜在的合作意向**：尽管表达不清，但多次提到“托你”“一起待一起”等词汇，可能暗示一种合作的意愿或期待进一步交流的需求。（重要程度：低）\\n\\n3. **对服务的信任感未建立**：谈话内容中没有明确提到对服务的认可或信任，缺乏对中企动力产品或服务的具体讨论，这可能是客户尚未决定签约的原因之一。（重要程度：中）\\n\\n建议：  \\n在后续沟通中，需进一步明确客户的实际需求，确保语言表达清晰，并围绕客户的核心痛点（如品牌展示、营销推广、外贸出海等）提供针对性的解决方案，以提升签约可能性。\"\n" +
                "    }\n" +
                "}"));
    }
}
