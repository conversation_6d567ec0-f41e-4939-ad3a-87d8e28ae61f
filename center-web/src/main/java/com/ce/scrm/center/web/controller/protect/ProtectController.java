package com.ce.scrm.center.web.controller.protect;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.ProtectBusiness;
import com.ce.scrm.center.service.business.entity.dto.protect.AssignProtectBatchBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.protect.CustPoolQueryBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.protect.ProtectAllotBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.protect.ProtectConfirmBusinessDto;
import com.ce.scrm.center.service.business.entity.view.BatchResultBusinessView;
import com.ce.scrm.center.service.business.entity.view.protect.CustPoolQueryBusinessView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.protect.AssignProtectBatchWebDto;
import com.ce.scrm.center.web.entity.dto.protect.CustPoolQueryWebDto;
import com.ce.scrm.center.web.entity.dto.protect.ProtectAllotWebDto;
import com.ce.scrm.center.web.entity.dto.protect.ProtectConfirmWebDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.BatchResultWebView;
import com.ce.scrm.center.web.entity.view.protect.CustPoolQueryWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
* @Description: 保护控制层
* @Author: lijinpeng
* @Date: 2025/1/2 14:56
* @version 1.0
*/
@RestController
@Login
@Slf4j
@RequestMapping("protect")
public class ProtectController {

    @Resource
    private ProtectBusiness protectBusiness;

    /*
     * @Description 批量分配保护关系
     * <AUTHOR>
     * @date 2025/1/6 17:03
     * @param assignProtectBatchWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.BatchResultWebView>
     */
    @PostMapping("assignProtectBatch")
    public WebResult<BatchResultWebView> assignProtectBatch(@RequestBody @Valid AssignProtectBatchWebDto assignProtectBatchWebDto) {
        AssignProtectBatchBusinessDto assignProtectBatchBusinessDto = BeanUtil.copyProperties(assignProtectBatchWebDto, AssignProtectBatchBusinessDto.class);
        BatchResultBusinessView businessResult = protectBusiness.assignProtectBatch(assignProtectBatchBusinessDto);
        BatchResultWebView batchResultWebView = BeanCopyUtils.convertToVo(businessResult, BatchResultWebView.class);
        return WebResult.success(batchResultWebView);
    }

    /*
     * @Description 客户池查询
     * <AUTHOR>
     * @date 2025/1/17 18:28
     * @param custPoolQueryWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.center.web.entity.view.protect.CustPoolQueryWebView>>
     */
    @PostMapping("getCustPoolListPage")
    public WebResult<WebPageInfo<CustPoolQueryWebView>> getCustPoolListPage(@RequestBody CustPoolQueryWebDto custPoolQueryWebDto) {
        CustPoolQueryBusinessDto custPoolQueryBusinessDto = BeanUtil.copyProperties(custPoolQueryWebDto, CustPoolQueryBusinessDto.class);
        Page<CustPoolQueryBusinessView> custPoolQueryBusinessView = protectBusiness.getCustPoolListPage(custPoolQueryBusinessDto);
        WebPageInfo<CustPoolQueryWebView> result = WebPageInfo.pageConversion(custPoolQueryBusinessView, CustPoolQueryWebView.class);
        return WebResult.success(result);
    }

    /**
     * @description: 商机：确认保护
     * @author: lijinpeng
     * @date: 2025/7/21 11:46
     * @param: [protectConfirmWebDto]
     * @return: com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("protectConfirm")
    public WebResult<Boolean> protectConfirm(@RequestBody ProtectConfirmWebDto protectConfirmWebDto) {
        ProtectConfirmBusinessDto protectConfirmBusinessDto = BeanUtil.copyProperties(protectConfirmWebDto, ProtectConfirmBusinessDto.class);
        Boolean result = protectBusiness.protectConfirm(protectConfirmBusinessDto);
        return WebResult.success(result);
    }

    /**
     * @description: 调拨
     * @author: lijinpeng
     * @date: 2025/8/8 15:24
     * @param: [protectAllotWebDto]
     * @return: com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("allot")
    public WebResult<Boolean> allot(@RequestBody ProtectAllotWebDto protectAllotWebDto) {
        ProtectAllotBusinessDto protectAllotBusinessDto = BeanUtil.copyProperties(protectAllotWebDto, ProtectAllotBusinessDto.class);
        Boolean result = protectBusiness.allot(protectAllotBusinessDto);
        return WebResult.success(result);
    }

}
