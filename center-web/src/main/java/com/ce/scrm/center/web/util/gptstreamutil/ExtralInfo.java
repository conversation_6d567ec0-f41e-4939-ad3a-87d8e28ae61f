package com.ce.scrm.center.web.util.gptstreamutil;

import lombok.*;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ExtralInfo implements Serializable {

    private String promptId;

    private String channelId;

    private String chatId;

    private String promptContent;

    private Integer retainCust;

    private String extralUrl;

    private String eventId;

    private String  parentId;

    /**
     * 2025-07-10 新增
     * extraPromptId 直接分析的时候 可以多传一个行业解决方案
     */
    private String extraPromptId;

}
