package com.ce.scrm.center.web.controller;

import cn.ce.cesupport.framework.base.vo.Pagination;
import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.service.business.BusinessOpportunityBusiness;
import com.ce.scrm.center.service.business.QueryPhoneNumberBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.AcceptBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.AnewAssignSjBatchBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.AreaBusinessOpportunityBusinessDto;
import com.ce.scrm.center.service.business.entity.view.AreaBusinessOpportunityBusinessView;
import com.ce.scrm.center.service.business.entity.view.BatchResultBusinessView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.AcceptWebDto;
import com.ce.scrm.center.web.entity.dto.AnewAssignSjBatchWebDto;
import com.ce.scrm.center.web.entity.dto.AreaBusinessOpportunityWebDto;
import com.ce.scrm.center.web.entity.dto.QueryPhoneNumberWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.AreaBusinessOpportunityWebView;
import com.ce.scrm.center.web.entity.view.BatchResultWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @version 1.0
 * @Description: 商机相关
 * @Author: lijinpeng
 * @Date: 2024/12/26 14:54
 */
@Slf4j
@RestController
@RequestMapping("/business/opportunity")
@Login
public class BusinessOpportunityController {

    @Resource
    private BusinessOpportunityBusiness businessOpportunityBusiness;

    /*
     * @Description 根据条件获取 区域商机
     * <AUTHOR>
     * @date 2024/12/26 15:14
     * @param areaBusinessOpportunityWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.util.List<com.ce.scrm.center.web.entity.view.AreaBusinessOpportunityWebView>>
     */
    @PostMapping("getAreaBusinessOpportunityByCondition")
    public WebResult<Pagination<AreaBusinessOpportunityWebView> > getAreaBusinessOpportunityByCondition(@RequestBody AreaBusinessOpportunityWebDto areaBusinessOpportunityWebDto) {
        AreaBusinessOpportunityBusinessDto areaBusinessOpportunityBusinessDto = BeanCopyUtils.convertToVo(areaBusinessOpportunityWebDto, AreaBusinessOpportunityBusinessDto.class);
        Pagination<AreaBusinessOpportunityBusinessView> areaBusinessOpportunityByCondition = businessOpportunityBusiness.getAreaBusinessOpportunityByCondition(areaBusinessOpportunityBusinessDto);

        Pagination<AreaBusinessOpportunityWebView> pagination = new Pagination<>();
        BeanUtils.copyProperties(areaBusinessOpportunityByCondition, pagination);
        List<AreaBusinessOpportunityWebView> areaBusinessOpportunityWebViews = BeanCopyUtils.convertToVoList(areaBusinessOpportunityByCondition.getList(), AreaBusinessOpportunityWebView.class);
        pagination.setList(areaBusinessOpportunityWebViews);
        return WebResult.success(pagination);
    }

    /*
     * @Description 商机重盘
     * <AUTHOR>
     * @date 2025/1/7 16:48
     * @param anewAssignSjBatchWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.BatchResultWebView>
     */
    @PostMapping("anewAssignSjBatch")
    public WebResult<BatchResultWebView> anewAssignSjBatch(@RequestBody @Valid AnewAssignSjBatchWebDto anewAssignSjBatchWebDto) {
        AnewAssignSjBatchBusinessDto anewAssignSjBatchBusinessDto = BeanUtil.copyProperties(anewAssignSjBatchWebDto, AnewAssignSjBatchBusinessDto.class);
        BatchResultBusinessView businessResult = businessOpportunityBusiness.anewAssignSjBatch(anewAssignSjBatchBusinessDto);
        BatchResultWebView batchResultWebView = BeanCopyUtils.convertToVo(businessResult, BatchResultWebView.class);
        return WebResult.success(batchResultWebView);
    }

    /*
     * @Description 商机48小时内 确认接收/拒绝
     * <AUTHOR>
     * @date 2025/3/27 11:28
     * @param acceptWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     */
    @PostMapping("accept")
    public WebResult<Boolean> accept(@RequestBody @Valid AcceptWebDto acceptWebDto) {
        AcceptBusinessDto acceptBusinessDto = BeanUtil.copyProperties(acceptWebDto, AcceptBusinessDto.class);
        Boolean result = businessOpportunityBusiness.accept(acceptBusinessDto);
        return WebResult.success(result);
    }

    /*
     * @Description 根据条件获取商机手机号
     * <AUTHOR>
     * @date 2025/4/11 18:34
     * @param queryPhoneNumberWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.String>
     */
    @PostMapping("getPhoneNumber")
    public WebResult<String> getPhoneNumber(@RequestBody QueryPhoneNumberWebDto queryPhoneNumberWebDto) {
        QueryPhoneNumberBusinessDto queryPhoneNumberBusinessDto = BeanUtil.copyProperties(queryPhoneNumberWebDto, QueryPhoneNumberBusinessDto.class);
        String result = businessOpportunityBusiness.getPhoneNumber(queryPhoneNumberBusinessDto);
        return WebResult.success(result);
    }

}
