package com.ce.scrm.center.web.controller;


import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.service.business.CustomerBusiness;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.entity.dto.ClockMarkBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.view.CallDetailsWebView;
import com.ce.scrm.center.service.business.entity.view.HomePageCallRateWebView;
import com.ce.scrm.center.service.third.invoke.EmpCustSiteClockThirdService;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.entity.dto.CallDetailsQueryWebDto;
import com.ce.scrm.center.web.entity.dto.ClockMarkWebDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.customer.dubbo.entity.dto.CallDetailsDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 打卡-控制层
 * @Author: 李金澎
 * @Date: 2024/9/25 15:55
 * @Version: 1.0
 **/
@Slf4j
@RestController
@RequestMapping("clock")
@Login
public class ClockController {

    @Resource
    private CustomerBusiness customerBusiness;


    @Resource
    private EmpCustSiteClockThirdService empCustSiteClockThirdService;

    @Resource
    private EmployeeInfoBusiness employeeInfoBusiness;

    /**
     * 获取拜访率
     * @param loginInfo
     * @return
     */
    @PostMapping("getCallRate")
    public WebResult<HomePageCallRateWebView> getCallRate(@RequestBody LoginInfo loginInfo) {
        HomePageCallRateWebView homePageCallRateWebView = customerBusiness.getCallRate(loginInfo.getLoginPosition(),
                loginInfo.getLoginAreaId(),
                loginInfo.getLoginSubId(),
                loginInfo.getLoginOrgId(),
                loginInfo.getLoginEmployeeId());
        return WebResult.success(homePageCallRateWebView);
    }

    /**
     * 获取打卡内页详情
     * @param callDetailsQueryWebDto
     * @return
     */
    @PostMapping("getCallDetails")
    public WebResult<WebPageInfo<CallDetailsWebView>> getCallDetails(@RequestBody CallDetailsQueryWebDto callDetailsQueryWebDto) {
        CallDetailsDubboDto callDetailsDubboDto = BeanUtil.copyProperties(callDetailsQueryWebDto, CallDetailsDubboDto.class);
        DubboPageInfo<CallDetailsWebView> callDetailsWebViewPage = customerBusiness.getCallDetails(callDetailsDubboDto);
        WebPageInfo<CallDetailsWebView> webPageInfo = BeanUtil.copyProperties(callDetailsWebViewPage, WebPageInfo.class);
        webPageInfo.setList(callDetailsWebViewPage.getList());
        return WebResult.success(webPageInfo);
    }

    /**
     * 标记打卡记录是否有效
     *
     * @param clockMarkWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     * <AUTHOR>
     * @date 2024/10/10 15:23
     */
    @PostMapping("updateClockMark")
    public WebResult<Boolean> updateClockMark(@RequestBody ClockMarkWebDto clockMarkWebDto) {
        // 参数校验
        if (clockMarkWebDto == null || StringUtils.isBlank(clockMarkWebDto.getId())){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        if (StringUtils.isBlank(clockMarkWebDto.getContent()) && clockMarkWebDto.getMarkClockValidFlag() == null){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }

        EmployeeInfoBusinessDto currentUser = employeeInfoBusiness.getEmployeeInfoByEmpId(clockMarkWebDto.getLoginEmployeeId());
        if (currentUser == null){
            return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
        }
        if (!Objects.equals(currentUser.getIsZbReport(),1)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }

        ClockMarkBusinessDto businessDto = BeanUtil.copyProperties(clockMarkWebDto, ClockMarkBusinessDto.class);
        businessDto.setOperatorId(clockMarkWebDto.getLoginEmployeeId());
        businessDto.setOperatorPosition(clockMarkWebDto.getLoginPosition());
        if (!empCustSiteClockThirdService.updateValidFlagById(businessDto)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_METHOD_EXCEPTION, "更新失败！");
        }
        return WebResult.success(true);
    }

}
