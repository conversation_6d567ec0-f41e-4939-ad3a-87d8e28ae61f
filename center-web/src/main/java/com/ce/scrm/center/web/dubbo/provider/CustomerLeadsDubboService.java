package com.ce.scrm.center.web.dubbo.provider;

import cn.ce.cesupport.enums.abm.LeadsImportSourceEnum;
import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.dao.entity.ClueInfo;
import com.ce.scrm.center.dubbo.api.CustomerLeadsDubbo;
import com.ce.scrm.center.dubbo.entity.dto.ClueInfoDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.view.CustomerLeadsDubboView;
import com.ce.scrm.center.service.business.abm.CustomerLeadsBusiness;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerLeadsImportOrDistributeDto;
import com.ce.scrm.center.service.business.entity.view.CustomerLeadsView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@DubboService(interfaceClass = CustomerLeadsDubbo.class)
public class CustomerLeadsDubboService implements CustomerLeadsDubbo {

    @Resource
    private CustomerLeadsBusiness customerLeadsBusiness;

	@Override
	public DubboResult<Boolean> clueImport(List<ClueInfoDubboDto> clueList) {
		CustomerLeadsImportOrDistributeDto dto = new CustomerLeadsImportOrDistributeDto();
		dto.setLeadsImportFrom(LeadsImportSourceEnum.INTENT_CLUE.getCode());
		if (CollectionUtils.isEmpty(clueList)) {
			log.error("线索导入参数异常, 参数为空");
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM);
		}
		dto.setClueList(BeanCopyUtils.convertToVoList(clueList, ClueInfo.class));
		return DubboResult.success(customerLeadsBusiness.customerLeadsHandle(dto));
	}

	@Override
	public DubboResult<List<CustomerLeadsDubboView>> getListByCustomerId(String customerId) {
		log.info("查询客户下的所有leads customerId={}", customerId);
		List<CustomerLeadsView> list = customerLeadsBusiness.getListByCustomerId(customerId);
		List<CustomerLeadsDubboView> customerLeadsDubboViews = BeanUtil.copyToList(list, CustomerLeadsDubboView.class);
		return DubboResult.success(customerLeadsDubboViews);
	}
}
