package com.ce.scrm.center.web.entity.dto.abm;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @project scrm-center
 * <AUTHOR>
 * @date 2025/7/16 11:05:43
 * @version 1.0
 * 查询客户ES Web层查询条件
 * 由于采用统一的ES查询方式，查询的字段都采用String，尽量不采用Integer等数据类型,Date等时间类型!!!
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerContactWebDto extends LoginInfo implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 状态值（1、保护；2、总监；3、经理；4、客户池）
     */
    private String status;

}