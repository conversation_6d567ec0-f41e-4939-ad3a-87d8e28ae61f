package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description 看板
 * <AUTHOR>
 * @Date 2025-02-27 13:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PullGroupSegmentWebDto extends LoginInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer id;
	/**
	 * cdp分群code
	 */
	@NotBlank(message = "分群code不能为空")
	private String segmentId;

	/**
	 * 专项线索名称（分司可见）
	 */
	@NotBlank(message = "分群名称不能为空")
	private String segmentName;

	/**
	 * 专项线索名称（分司可见）
	 */
	@NotBlank(message = "分群说明不能为空")
	private String segmentDesc;

	/**
	 * 跟踪开始时间
	 */
	@NotBlank(message = "分群开始时间不能为空")
	private String segmentStartDate;

	/**
	 * 跟踪结束时间
	 */
	@NotBlank(message = "分群结束时间不能为空")
	private String segmentEndDate;

}
