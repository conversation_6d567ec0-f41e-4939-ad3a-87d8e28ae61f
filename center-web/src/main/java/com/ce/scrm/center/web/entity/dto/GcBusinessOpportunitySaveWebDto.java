package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 高呈商机添加参数
 * <AUTHOR>
 * @date 2024/5/16 下午4:45
 * @version 1.0.0
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class GcBusinessOpportunitySaveWebDto extends LoginInfo implements Serializable {

    /**
     * 高呈商机ID（更新必传）
     */
    private Long sjId;

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空")
    private String customerName;

    /**
     * 等级：1、A（高），2、B（中），3、C（低），4、D（其他）
     */
    @NotNull(message = "等级不能为空")
    private Integer level;

    /**
     * 来源
     */
    @NotNull(message = "来源不能为空")
    private Integer source;

    /**
     * 分司ID
     */
    @NotBlank(message = "分司不能为空")
    private String gcSubId;

    /**
     * 分司名称
     */
    @NotBlank(message = "分司名称不能为空")
    private String gcSubName;

    /**
     * 联系人名称
     */
    @NotBlank(message = "联系人名称不能为空")
    private String linkmanName;

    /**
     * 联系人手机号
     */
    @NotBlank(message = "联系人手机号不能为空")
    private String linkmanPhone;

    /**
     * 联系人邮箱
     */
    private String linkmanEmail;

    /**
     * 联系人部门
     */
    private String linkmanDept;

    /**
     * 联系人性别：0、未知，1、男，2、女
     */
    private Integer linkmanSex = 0;

    /**
     * 联系人座机
     */
    private String linkmanLandline;

    /**
     * 联系人职务
     */
    private String linkmanJob;

    /**
     * 联系人微信
     */
    private String linkmanWechat;

    /**
     * 需求类型
     */
    @NotNull(message = "需求类型不能为空")
    private Integer requirementType;

    /**
     * 需求详情
     */
    @NotBlank(message = "需求不能为空")
    private String requirementDetail;

    /**
     * 客户预算
     */
    private BigDecimal customerBudget;

    /**
     * 附件ID（多个使用,分隔）
     */
    private String attachmentIds;
}