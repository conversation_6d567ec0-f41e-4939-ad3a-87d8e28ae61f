package com.ce.scrm.center.web.controller;

import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.entity.dto.ConvertLogUpdateBusinessDto;
import com.ce.scrm.center.service.business.entity.view.ConvertLogPageBusinessView;
import com.ce.scrm.center.service.third.entity.view.EmployeeLiteThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.service.third.invoke.SmaConvertLogThirdService;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.controller.base.BaseController;
import com.ce.scrm.center.web.entity.dto.ConvertLogUpdateWebDto;
import com.ce.scrm.center.web.entity.dto.ConvertLogWebPageDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.ConvertLogWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Optional;

/**
 * Description: 流转日志相关接口
 * @author: JiuDD
 * date: 2024/7/11
 */
@Login
@Slf4j
@RestController
@RequestMapping("convertLog")
public class ConvertLogController extends BaseController {
    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;
    @Autowired
    private EmployeeThirdService employeeThirdService;
    @Autowired
    private OrgThirdService orgThirdService;

    /**
     * Description: 查询流转日志，用于补写(查看)释放原因
     * @author: JiuDD
     * @param webPageDto      查询参数
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo<com.ce.scrm.center.web.entity.view.ConvertLogWebView>>
     * date: 2024/7/11 11:25
     */
    @PostMapping("getPageForReleaseReason")
    public WebResult<WebPageInfo<ConvertLogWebView>> submit(@Validated @RequestBody ConvertLogWebPageDto webPageDto) {
        Page<ConvertLogPageBusinessView> pageForReleaseReason = smaConvertLogThirdService.getPageForReleaseReason(webPageDto.packageBusinessDto());
        WebPageInfo<ConvertLogWebView> webPageInfo = WebPageInfo.pageConversion(pageForReleaseReason, ConvertLogWebView.class);
        webPageInfo.getList().forEach(this::packageSalerData);
        return WebResult.success(webPageInfo);
    }

    /**
     * Description: 经理补写释放客户原因
     * @author: JiuDD
     * @param updateWebDto      更新参数
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo<com.ce.scrm.center.web.entity.view.ConvertLogWebView>>
     * date: 2024/7/11 22:25
     */
    @PostMapping("updateJlReleaseReason")
    public WebResult<Boolean> updateJlReleaseReason(@Validated @RequestBody ConvertLogUpdateWebDto updateWebDto) {
        String loginEmployeeName = updateWebDto.getLoginEmployeeName();
        String loginSubId = updateWebDto.getLoginSubId();
        Optional<EmployeeLiteThirdView> orgLeader = employeeThirdService.getOrgLeader(loginSubId);

        ConvertLogUpdateBusinessDto businessDto = CglibUtil.copy(updateWebDto, ConvertLogUpdateBusinessDto.class);
        businessDto.setJlReleaseTime(new Date());
        businessDto.setJlName(loginEmployeeName);
        orgLeader.ifPresent(employeeLiteThirdView -> businessDto.setZjId(employeeLiteThirdView.getId()));

        return WebResult.success(smaConvertLogThirdService.updateJlReleaseReason(businessDto));
    }

}