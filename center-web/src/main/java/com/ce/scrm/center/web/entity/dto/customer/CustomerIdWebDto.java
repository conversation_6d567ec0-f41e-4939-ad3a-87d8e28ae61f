package com.ce.scrm.center.web.entity.dto.customer;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 客户id
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerIdWebDto extends LoginInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 客户id
	 */
	@NotBlank(message = "客户id不能为空")
	private String customerId;

}
