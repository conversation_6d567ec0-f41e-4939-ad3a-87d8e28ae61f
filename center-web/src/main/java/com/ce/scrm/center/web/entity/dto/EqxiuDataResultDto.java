package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import java.util.Date;

@Data
public class EqxiuDataResultDto extends LoginInfo {

    private String salerId;

    /**
     * 活动ID
     */

    private String activityId;

    /**
     * 活动ID
     */

    private String activityType;

    /**
     * 活动名称
     */

    private String activityName;
    /**
     * 客户ID
     */

    private String customerId;
    /**
     * 客户姓名
     */

    private String customerName;
    /**
     * 联系人ID
     */

    private String contactId;
    /**
     * 联系人姓名
     */

    private String contactName;
    /**
     * 联系人手机号
     */

    private String contactPhone;
    /**
     * 分享的商务信息
     */

    private String salerInfo;
    /**
     * 客户保护商务
     */

    private String protectSalerId;
    /**
     * 客户保护商务所在的地区ID
     */

    private String areaId;
    /**
     * 客户保护商务所在的地区
     */

    private String area;
    /**
     * 客户保护商务所在的分公司ID
     */

    private String subId;
    /**
     * 客户保护商务所在的分公司名称
     */

    private String subCompany;
    /**
     * 客户保护商务所在的部门ID
     */

    private String deptId;
    /**
     * 客户保护商务所在的部门
     */

    private String org;
    /**
     * 分享链接打开次数
     */

    private Integer openTimes;
    /**
     * 最近打开时间
     */

    private Date recentOpenTime;
    /**
     * 抽奖次数
     */

    private Integer lotteryTimes;
    /**
     * 中奖次数
     */

    private Integer isWin;
    /**
     * 分享次数
     */

    private Integer shareTimes;
    /**
     * 中奖信息
     */

    private String winInfo;
    /**
     * 最近抽奖时间
     */

    private Date recentLotteryTime;


    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 每页数量
     */
    private Integer pageSize = 10;


    /**
     * 0 正序 1 反序
     */
    private Integer shareTimesSort;
    /**
     * 打开次数
     */
    private Integer openTimesSort;
    /**
     * 抽奖次数
     */
    private Integer lotteryTimesSort;

    /**
     * 中奖次数
     */
    private Integer winTimesSort;

    private Integer level;
    private String buId;


}
