package com.ce.scrm.center.web.entity.dto.abm;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 客户营销列表请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerMarketingActivityCreateWebDto extends LoginInfo implements Serializable {

	/**
	 * 活动id
	 */
	private Long id;

	/**
	 * 活动名称
	 */
	@NotBlank(message = "活动名称不能为空")
	private String activityName;

	/**
	 * 活动类型/触达方式 1-短信 2-邮件 3-电话 4-其他
	 */
	@NotNull(message = "活动触达方式不能为空")
	private Integer activityType;

	/**
	 * 模板id
	 */
	private String templateId;

	/**
	 * 活动内容/触达内容
	 */
	// @NotBlank(message = "活动内容不能为空")
	private String activityContent;

	/**
	 * 关联活动链接
	 */
	// @NotBlank(message = "活动链接不能为空")
	private String activityUrl;

	/**
	 * 客群id
	 */
	@NotBlank(message = "客群id不能为空")
	private String segmentId;

	/**
	 * 客群人数
	 */
	// @NotNull(message = "客群人数不能为空")
	private Integer segmentCustCount;

	/**
	 * 客群名称
	 */
	// @NotBlank(message = "客群名称不能为空")
	private String segmentName;

	/**
	 * 执行时间
	 */
	@NotNull(message = "执行时间不能为空")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date executeTime;

	/**
	 * 触达方式选择电话时的分配方式 1-均分 2-指定数量
	 */
	private Integer phoneDistributionWay;

	/**
	 * 触达方式选择电话时,并且分配选择了均分的分配人
	 */
	private List<String> assigneeIds;

	/**
	 * 触达方式选择电话时,并且分配选择了指定数量的分配人。key:sdr_id value:分配客户数量
	 */
	private Map<String, Integer> sdrIdNumMap;

}
