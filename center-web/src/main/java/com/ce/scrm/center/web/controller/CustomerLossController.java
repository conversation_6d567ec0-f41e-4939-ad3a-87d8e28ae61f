package com.ce.scrm.center.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.CustomerLossLogBusiness;
import com.ce.scrm.center.service.business.entity.view.CustomerLossPageBusinessView;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.controller.base.BaseController;
import com.ce.scrm.center.web.entity.dto.CustomerLossWebPageDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.CustomerLossWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description: 客户流失
 *
 * @author: liyechao
 * date: 2024/9/2.
 */
@Login
@Slf4j
@RestController
@RequestMapping("customerLoss")
public class CustomerLossController extends BaseController {
    @Resource
    private CustomerLossLogBusiness customerLossLogBusiness;

    /**
     * Description: 即将流转客户
     *
     * @param webPageDto 查询参数
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo < com.ce.scrm.center.web.entity.view.CustomerLossWebView>>
     * date: 2024/9/2
     * @author: liyechao
     */
    @PostMapping("getCustomerLossList")
    public WebResult<WebPageInfo<CustomerLossWebView>> getCustomerLossList(@Validated @RequestBody CustomerLossWebPageDto webPageDto) {
        Page<CustomerLossPageBusinessView> pageForReleaseReason = customerLossLogBusiness.getCustomerList(webPageDto.packageBusinessDto(webPageDto));
        WebPageInfo<CustomerLossWebView> webPageInfo = WebPageInfo.pageConversion(pageForReleaseReason, CustomerLossWebView.class);
        return WebResult.success(webPageInfo);
    }

}
