package com.ce.scrm.extend.dubbo.entity.view;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 分司拜访-报价-签单漏斗数据
 * @Author: lijinpeng
 * @Date: 2025/4/3 15:19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubVisitSigningView implements Serializable {

    /**
     * CDP：日期
     * CRM：时间 (月)
     */
    @JSONField(name = "时间 (月)")
    private String date;

//    /**
//     * CDP:第一步-第二步,流失用户数
//     */
//    private Integer wastageUserStep1;

    /**
     * CDP:第一步-第三步，总转化用户数
     * CRM:总转化用户数
     */
    @JSONField(name = "总转化用户数")
    private Integer convertUserStep2;

    /**
     * CDP:第二步,用户数
     * CRM:报价客户数
     */
    @JSONField(name = "报价客户数")
    private Integer convertUserStep1;

//    private String funnel_root;
//    private String funnel_level_2;
//    private String funnel_level_1;

//    /**
//     * CDP:第二步-第三步,流失用户数
//     */
//    private Integer wastageUserStep2;

    /**
     * 0.2982 = 29.82%
     * CDP:第二步-第三步,转化率
     * CRM:报价客户到签单客户的转化率
     */
    @JSONField(name = "报价客户到签单客户的转化率（单位%）")
    private Float convertRateStep2;

    /**
     * CDP:第一步-第三步，总转化率
     * CRM:总转化率
     */
    @JSONField(name = "总转化率（单位%）")
    private Float completionRate;

    /**
     * CDP：第一步，用户数
     * CRM:拜访客户数
     */
    @JSONField(name = "拜访客户数")
    private Integer totalUser;

    /**
     * CDP:第一步-第二步,转化率
     * CRM:拜访客户到报价客户的转化率
     */
    @JSONField(name = "拜访客户到报价客户的转化率（单位%）")
    private Float convertRateStep1;

//    private String event.cust_followup_v2.$time;

//    /**
//     * CDP:第二步-第三步,转化时间中位数(单位是秒)
//     */
//    private Double medianConvertedTimeStep2;
//
//    /**
//     * CDP:第一步-第二步,转化时间中位数(单位是秒)
//     */
//    private Double medianConvertedTimeStep1;

    /**
     * CDP：第三步,用户数
     * CRM: 客户付款
     */
    @JSONField(name = "签单客户数")
    private Integer completionConvertedUser;

}
