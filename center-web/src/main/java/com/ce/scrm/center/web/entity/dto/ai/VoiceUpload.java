package com.ce.scrm.center.web.entity.dto.ai;

import com.ce.scrm.center.service.eqixiu.sdk.util.JSONObject;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class VoiceUpload extends LoginInfo implements Serializable {

    private String custId;

    private JSONObject extralInfo;

    /**
     * 分析类型，1代表跟进记录+普通分析，0 代表只进行普通分析 2.代表只进行语音识别
     */
    private String voiceType;

    private String platform;


    private List<FileContext> fileList;

}
