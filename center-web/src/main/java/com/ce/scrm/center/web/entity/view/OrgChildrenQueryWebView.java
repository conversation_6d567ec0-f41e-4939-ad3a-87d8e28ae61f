package com.ce.scrm.center.web.entity.view;

import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 组织机构查询子集以及本身的查询实体类
 * @Author: lijinpeng
 * @Date: 2024/11/21 13:44
 */
@Data
public class OrgChildrenQueryWebView implements Serializable {

    /**
     * 本身
     */
    private OrgThirdDto orgVo;

    /**
     * 子集
     */
    private List<OrgThirdDto> children;

}
