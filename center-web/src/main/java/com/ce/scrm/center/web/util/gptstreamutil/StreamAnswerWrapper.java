package com.ce.scrm.center.web.util.gptstreamutil;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/4
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class StreamAnswerWrapper {

    private boolean gptAnswer = false;

    private List<StreamAnswer> data;

    // 对话的数据库id
    private String chatId;

    private boolean cardMessage = false;

    private JSONObject extraInfo;


}
