package com.ce.scrm.center.web.entity.view;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrgEmployeeWebView {

    private String id;
    /**
     * 名称
     */
    private String name;
    /**
     * 父id
     */
    private String parentId;

    /**
     * 类型  1 组织机构 2 人员 3 部门领导
     */
    private Integer type;

    private List<OrgEmployeeWebView> children;

}
