package com.ce.scrm.center.web.entity.view;

import cn.ce.cesupport.contract.vo.CmaCustomerVo;
import com.ce.scrm.center.service.business.entity.dto.CmaCustProtectDto;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2024/12/5 15:05
 */
@Data
public class CustomerTwoNameWebView implements Serializable {

    /**
     * 原名称对应
     */
    CmaCustProtectDto oldCustomer;

    /**
     * 新名称对象
     */
    CmaCustProtectDto newCustomer;

}
