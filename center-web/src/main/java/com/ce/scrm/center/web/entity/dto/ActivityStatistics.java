package com.ce.scrm.center.web.entity.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class ActivityStatistics {

    private String activityName;

    private int invitedCustomerCount;
    private int openedCustomerCount;
    private int lotteryCustomerCount;
    private int winningCustomerCount;
    private int firstPrizeCustomerCount;
    private int secondPrizeCustomerCount;
    private int thirdPrizeCustomerCount;
    private int fourthPrizeCustomerCount;
    private int fifthPrizeCustomerCount;
    private int sixthPrizeCustomerCount;
    private int seventhPrizeCustomerCount;
    private int eighthPrizeCustomerCount;

    @JsonIgnore
    private List<String> invitedCustomerCountList;

    // 添加单个元素到邀约客户数列表的方法
    public void addToInvitedCustomerCountList(String element) {
        if (invitedCustomerCountList == null) {
            invitedCustomerCountList = new ArrayList<>();
        }
        invitedCustomerCountList.add(element);
    }

    // 重写get方法，从去重后的列表取size作为邀约客户数
    public int getInvitedCustomerCount() {
        if (invitedCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(invitedCustomerCountList);
        return uniqueSet.size();
    }

    @JsonIgnore
    private List<String> openedCustomerCountList;

    // 添加单个元素到打开客户数列表的方法
    public void addToOpenedCustomerCountList(String element) {
        if (openedCustomerCountList == null) {
            openedCustomerCountList = new ArrayList<>();
        }
        openedCustomerCountList.add(element);
    }

    // 重写get方法，从去重后的列表取size作为打开客户数
    public int getOpenedCustomerCount() {
        if (openedCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(openedCustomerCountList);
        return uniqueSet.size();
    }

    @JsonIgnore
    private List<String> lotteryCustomerCountList;

    // 添加单个元素到抽奖客户数列表的方法
    public void addToLotteryCustomerCountList(String element) {
        if (lotteryCustomerCountList == null) {
        }
        lotteryCustomerCountList = new ArrayList<>();
        lotteryCustomerCountList.add(element);
    }

    // 重写get方法，从去重后的列表取size作为抽奖客户数
    public int getLotteryCustomerCount() {
        if (lotteryCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(lotteryCustomerCountList);
        return uniqueSet.size();
    }

    @JsonIgnore
    private List<String> winningCustomerCountList;

    // 添加单个元素到中奖客户数列表的方法
    public void addToWinningCustomerCountList(String element) {
        if (winningCustomerCountList == null) {
            winningCustomerCountList = new ArrayList<>();
        }
        winningCustomerCountList.add(element);
    }

    // 重写get方法，从去重后的列表取size作为中奖客户数
    public int getWinningCustomerCount() {
        if (winningCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(winningCustomerCountList);
        return uniqueSet.size();
    }

    @JsonIgnore
    private List<String> firstPrizeCustomerCountList;

    // 添加单个元素到一等奖客户数列表的方法
    public void addToFirstPrizeCustomerCountList(String element) {
        if (firstPrizeCustomerCountList == null) {
            firstPrizeCustomerCountList = new ArrayList<>();
        }
        firstPrizeCustomerCountList.add(element);
    }

    // 重写get方法，从去重后的列表取size作为一等奖客户数
    public int getFirstPrizeCustomerCount() {
        if (firstPrizeCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(firstPrizeCustomerCountList);
        return uniqueSet.size();
    }

    @JsonIgnore
    private List<String> secondPrizeCustomerCountList;

    // 添加单个元素到二等奖客户数列表的方法
    public void addToSecondPrizeCustomerCountList(String element) {
        if (secondPrizeCustomerCountList == null) {
            secondPrizeCustomerCountList = new ArrayList<>();
        }
        secondPrizeCustomerCountList.add(element);
    }

    // 重写get方法，从去重后的列表取size作为二等奖客户数
    public int getSecondPrizeCustomerCount() {
        if (secondPrizeCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(secondPrizeCustomerCountList);
        return uniqueSet.size();
    }

    @JsonIgnore
    private List<String> thirdPrizeCustomerCountList;

    // 添加单个元素到三等奖客户数列表的方法
    public void addToThirdPrizeCustomerCountList(String element) {
        if (thirdPrizeCustomerCountList == null) {
            thirdPrizeCustomerCountList = new ArrayList<>();
        }
        thirdPrizeCustomerCountList.add(element);
    }

    // 重写get方法，从去重后的列表取size作为三等奖客户数
    public int getThirdPrizeCustomerCount() {
        if (thirdPrizeCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(thirdPrizeCustomerCountList);
        return uniqueSet.size();
    }

    @JsonIgnore
    private List<String> fourthPrizeCustomerCountList;

    // 添加单个元素到四等奖客户数列表的方法
    public void addToFourthPrizeCustomerCountList(String element) {
        if (fourthPrizeCustomerCountList == null) {
            fourthPrizeCustomerCountList = new ArrayList<>();
        }
    }

    // 重写get方法，从去重后的列表取size作为四等奖客户数
    public int getFourthPrizeCustomerCount() {
        if (fourthPrizeCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(fourthPrizeCustomerCountList);
        return uniqueSet.size();
    }

    @JsonIgnore
    private List<String> fifthPrizeCustomerCountList;

    // 添加单个元素到五等奖客户数列表的方法
    public void addToFifthPrizeCustomerCountList(String element) {
        if (fifthPrizeCustomerCountList == null) {
            fifthPrizeCustomerCountList = new ArrayList<>();
        }
        fifthPrizeCustomerCountList.add(element);
    }

    // 重写get方法，从去重后的列表取size作为五等奖客户数
    public int getFifthPrizeCustomerCount() {
        if (fifthPrizeCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(fifthPrizeCustomerCountList);
        return uniqueSet.size();
    }

    @JsonIgnore
    private List<String> sixthPrizeCustomerCountList;

    // 添加单个元素到六等奖客户数列表的方法
    public void addToSixthPrizeCustomerCountList(String element) {
        if (sixthPrizeCustomerCountList == null) {
            sixthPrizeCustomerCountList = new ArrayList<>();
        }
        sixthPrizeCustomerCountList.add(element);
    }

    // 重写get方法，从去重后的列表取size作为六等奖客户数
    public int getSixthPrizeCustomerCount() {
        if (sixthPrizeCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(sixthPrizeCustomerCountList);
        return uniqueSet.size();
    }

    @JsonIgnore
    private List<String> seventhPrizeCustomerCountList;

    // 添加单个元素到七等奖客户数列表的方法
    public void addToSeventhPrizeCustomerCountList(String element) {
        if (seventhPrizeCustomerCountList == null) {
            seventhPrizeCustomerCountList = new ArrayList<>();
        }
        seventhPrizeCustomerCountList.add(element);
    }

    // 重写get方法，从去重后的列表取size作为七等奖客户数
    public int getSeventhPrizeCustomerCount() {
        if (seventhPrizeCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(seventhPrizeCustomerCountList);
        return uniqueSet.size();
    }

    @JsonIgnore
    private List<String> eighthPrizeCustomerCountList;

    // 添加单个元素到八等奖客户数列表的方法
    public void addToEighthPrizeCustomerCountList(String element) {
        if (eighthPrizeCustomerCountList == null) {
            eighthPrizeCustomerCountList = new ArrayList<>();
        }
        eighthPrizeCustomerCountList.add(element);
    }

    // 重写get方法，从去重后的列表取size作为八等奖客户数
    public int getEighthPrizeCustomerCount() {
        if (eighthPrizeCustomerCountList == null) {
            return 0;
        }
        Set<String> uniqueSet = new HashSet<>(eighthPrizeCustomerCountList);
        return uniqueSet.size();
    }

    // 构造函数，可根据需要自行补充更完整的构造函数逻辑
    public ActivityStatistics() {
        invitedCustomerCountList = new ArrayList<>();
        openedCustomerCountList = new ArrayList<>();
        lotteryCustomerCountList = new ArrayList<>();
        winningCustomerCountList = new ArrayList<>();
        firstPrizeCustomerCountList = new ArrayList<>();
        secondPrizeCustomerCountList = new ArrayList<>();
        thirdPrizeCustomerCountList = new ArrayList<>();
        fourthPrizeCustomerCountList = new ArrayList<>();
        fifthPrizeCustomerCountList = new ArrayList<>();
        sixthPrizeCustomerCountList = new ArrayList<>();
        seventhPrizeCustomerCountList = new ArrayList<>();
        eighthPrizeCustomerCountList = new ArrayList<>();
    }
}