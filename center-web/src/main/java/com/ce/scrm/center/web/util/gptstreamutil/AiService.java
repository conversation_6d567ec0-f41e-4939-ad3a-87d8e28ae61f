package com.ce.scrm.center.web.util.gptstreamutil;

import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import cn.ce.cesupport.emp.service.OrgAppService;
import cn.ce.cesupport.emp.vo.OrgVo;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.ce.sequence.api.request.SequenceIdRequest;
import cn.ce.sequence.api.service.SequenceService;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.*;
import com.ce.scrm.center.dao.entity.view.CustomsTradeSummaryView;
import com.ce.scrm.center.dao.service.*;
import com.ce.scrm.center.service.business.AiCustCacheBusiness;
import com.ce.scrm.center.service.business.CustomsTradeSummaryBusiness;
import com.ce.scrm.center.service.business.ImageDownload;
import com.ce.scrm.center.service.business.SendWxMessage;
import com.ce.scrm.center.service.business.entity.ai.*;
import com.ce.scrm.center.service.business.entity.dto.AiBusinessDto;
import com.ce.scrm.center.service.cache.AiPromptCacheHandler;
import com.ce.scrm.center.service.config.NlpProperties;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.service.third.entity.view.BigDataCompanyDetail;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.BigDataThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.entity.dto.AiQuerySmartDto;
import com.ce.scrm.center.web.entity.dto.AudioList;
import com.ce.scrm.center.web.entity.dto.ai.AiCustomsProductWebDto;
import com.ce.scrm.center.web.entity.dto.ai.FileContext;
import com.ce.scrm.center.web.entity.dto.ai.VoiceAnalyzeWebDto;
import com.ce.scrm.center.web.entity.dto.ai.VoiceUpload;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.ai.SubVisitSigningNewView;
import com.ce.scrm.center.web.util.JsonToMarkdown;
import com.ce.scrm.extend.dubbo.api.FunnelDubbo;
import com.ce.scrm.extend.dubbo.entity.dto.SubVisitSigningDubboDto;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.SubVisitSigningNewDubboView;
import com.ce.scrm.extend.dubbo.entity.view.SubVisitSigningView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.URL;
import java.net.UnknownHostException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.ce.scrm.center.service.constant.ServiceConstant.LockKey.AICRM_IMAGE_GENERATOR_LIMIT;
import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_AI_IMAGE_GENERATOR_TOPIC;
import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_KEXF_MESSAGE_PPT_AI_NEW_NOTIFY_TOPIC;
import static com.ce.scrm.center.web.controller.ai.AiSmartController.containsUrl;
import static com.ce.scrm.center.web.controller.ai.AiSmartController.prefixPPt;

@Slf4j
@Component
public class AiService {

    @Autowired
    private NlpProperties nlpProperties;

    @Resource
    private AiPromptCacheHandler aiPromptCacheHandler;

    @Autowired
    private AiChannelConfigService aiChannelConfigService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private BigDataThirdService bigDataThirdService;
    @DubboReference
    private OrgAppService orgAppService;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false, timeout = 20000)
    private FunnelDubbo funnelDubbo;

    @Autowired
    private AiAccessAuthService aiAccessAuthService;

    @Autowired
    private AiEventLogService aiEventLogService;


    @Resource
    private SequenceService sequenceService;

    @Resource
    private AiCustCacheBusiness aiCustCacheBusiness;

    @Autowired
    private FunctionCallingService functionCallingService;


    @Autowired
    private RocketMqOperate rocketMqOperate;

    @Autowired
    private CustomsTradeSummaryBusiness customsTradeSummaryBusiness;

    @Autowired
    private AiVoiceAnalyzeService aiVoiceAnalyzeService;

    @Autowired
    private ImageDownload imageDownload;


    /**
     * SOP业绩信息
     */
    private static final String SOP_PERFORMANCE = "http://local-gateway.datauns.cn/ce15tvbigscreen/other/api/transactionSummary?orgId=";

    private static final Map<String, AtomicBoolean> keyStatusMap = new ConcurrentHashMap<>();

    @Value("${file.upload.dir}")
    private String uploadDir;

    @Value("${file.upload.url}")
    private String uploadUrl;

    private static String prefix = "SCRM:ENTER:AI:CHANNELQPS:";
    private static final String PERFORMANCE_CACHE_PREFIX = "SCRM:ENTER:AI:performance_data:";

    @Autowired
    private AiPromptInfoService aiPromptInfoService;

    @Autowired
    private SendWxMessage sendWxMessage;

    static Dispatcher dispatcher = new Dispatcher();

    static {
        dispatcher.setMaxRequests(500);
        dispatcher.setMaxRequestsPerHost(500);
    }

    @PostConstruct
    public void decrementValueAll() {
        // JVM 关闭 Hook，确保进程退出时 decrement
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            Iterator<Map.Entry<String, AtomicBoolean>> iterator = keyStatusMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, AtomicBoolean> entry = iterator.next();
                AtomicBoolean atomicBoolean = entry.getValue();
                if (atomicBoolean.compareAndSet(false, true)) {
                    String key = entry.getKey().split("\\|\\|")[0];
                    String name = entry.getKey().split("\\|\\|")[2];
                    log.info("程序异常终止主动释放被占用的 qps:{}", name);
                    Long value = stringRedisTemplate.opsForValue().decrement(key, 1);
                    stringRedisTemplate.expire(key, 1000, TimeUnit.SECONDS);
                    log.info("qps释放成功mapKey:{},info:{}，当前QPS:{}", entry.getKey(), name, value);
                    iterator.remove();
                }
            }
        }));
    }

    private static final OkHttpClient client = new OkHttpClient().newBuilder().dispatcher(dispatcher)
            .connectTimeout(10, TimeUnit.MINUTES)
            .readTimeout(10, TimeUnit.MINUTES)
            .writeTimeout(10, TimeUnit.MINUTES)
            .connectionPool(new ConnectionPool(500, 10, TimeUnit.MINUTES))
            .build();


    public ResponseEntity<SseEmitter> getAiTips(String reqMessage, Consumer<StreamAnswer> consumer, ExtralInfo extralInfo) {
        try {
            String content = extralInfo.getPromptContent();
            if (StringUtils.isBlank(extralInfo.getPromptContent())) {
                //主提示词
                AiPromptInfo mainPrompt = null;
                //额外提示词
                AiPromptInfo extraPrompt = null;
                if (StringUtils.isNotBlank(extralInfo.getPromptId())) {
                    mainPrompt = aiPromptCacheHandler.get(extralInfo.getPromptId());
                }
                if (StringUtils.isNotBlank(extralInfo.getExtraPromptId())) {
                    extraPrompt = aiPromptCacheHandler.get(extralInfo.getExtraPromptId());
                }
                if (Objects.nonNull(mainPrompt) && Objects.nonNull(extraPrompt)) {
                    // 提示词整合
                    String integration = "你是中企动力的商务助手，你需要根据给你的两个方案大纲和客户数据为商务生成方案。\n" +
                            "                    方案一 %s\n" +
                            "                    %s\n" +
                            "                    方案二 %s\n" +
                            "                    %s\n" +
                            "                    最后：你需要将方案一与方案二进行元素整合，需要涵盖每个方案中的子项细节，重复部分需要合并，不同部分必须保留，然后以方案一的大纲为主体进行生成，最终结合给你的客户数据，输出整合后的方案。";

                    content = String.format(integration, mainPrompt.getTitle(), mainPrompt.getContent(), extraPrompt.getTitle(), extraPrompt.getContent());
                } else {
                    if (Objects.nonNull(mainPrompt)) {
                        content = mainPrompt.getContent();
                    }
                }
                if (StringUtils.isBlank(content)) {
                    log.warn("ai提示词库无启用状态，暂启用nacos默认提示词");
                    content = nlpProperties.getSystemPromote();
                }
            }
            extralInfo.setRetainCust(Objects.equals(1, extralInfo.getRetainCust()) ? 1 : 0);
            SseEmitter sseEmitter = SseEmitterUtils.getSseEmitter(10 * 60 * 1000L);
            this.sendMsgStream(Arrays.asList(new MessageContent("system", content),
                    new MessageContent("user", reqMessage)), sseEmitter, consumer, extralInfo);
            return ResponseEntity.ok(sseEmitter);
        } catch (Exception e) {
            log.error("对话异常", e);
            SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
            SseEmitterUtils.sendMessage(ResponseConstant.ERROR_RESPONSE_MESSAGE, errorSseEmitter);
            return ResponseEntity.ok(errorSseEmitter);
        }
    }

    @SneakyThrows
    public void sendMsgStream(List<MessageContent> messages, SseEmitter sseEmitter, Consumer<StreamAnswer> consumer, ExtralInfo extralInfo) {
        List<String> channelIds = new ArrayList<>();
        if (Objects.equals(extralInfo.getChannelId(), "image")) {
            channelIds = Collections.singletonList("image");
        } else if (Objects.equals(extralInfo.getChannelId(), "code")) {
            channelIds = Collections.singletonList("code");
        } else {
            // 创建新线程，防止主程序阻塞
            List<AiChannelConfig> list = aiChannelConfigService.lambdaQuery().select(AiChannelConfig::getId).eq(AiChannelConfig::getDeleteFlag, 0).orderByDesc(AiChannelConfig::getStatus).orderByAsc(AiChannelConfig::getOrderNum)
                    .gt(AiChannelConfig::getQpsLimit, 0).list();
            log.info("当前可用的AI 列表:{}", JSONObject.toJSONString(list));
            channelIds = new ArrayList<>(Collections.singletonList(extralInfo.getChannelId()));
            channelIds.addAll(list.stream().map(T -> String.valueOf(T.getId())).collect(Collectors.toList()));
        }
        List<String> finalChannelIds = channelIds;
        new Thread(() -> {
            AiChannelConfigExtend aiChannelConfig = null;
            StringBuffer stringBuilderError = new StringBuffer();
            try {
                boolean isSuccessFinal = false;
                StringBuilder stringBuffer = new StringBuilder();
                // 请求 AI 的重试机制
                for (String channel : finalChannelIds) {
                    CountDownLatch latch = new CountDownLatch(1);
                    AtomicBoolean isSuccess = new AtomicBoolean(false);
                    AtomicReference<String> errorMsgRef = new AtomicReference<>("");
                    try {
                        aiChannelConfig = this.getAiChannelConfig(channel, stringBuffer.toString());
                        stringBuffer.append(aiChannelConfig.getId()).append(",");
                    } catch (Exception e) {
                        log.warn("获取配置异常", e);
                        stringBuilderError.append("配置异常: ").append(channel).append(" - ").append(e.getMessage()).append("\n");
                        continue;
                    }
                    try {
                        sseEmitter.onTimeout(() -> {
                            sseEmitter.complete();
                            log.warn("模型返回超时10分钟，释放线程");
                        });
                        AiChannelConfigExtend finalAiChannelConfig = aiChannelConfig;
                        sseEmitter.onCompletion(() -> {
                            log.info("请求结束onCompletion:{}", finalAiChannelConfig.getChannelName());
                            decrementValue(finalAiChannelConfig);
                        });
                        AiChannelConfigExtend finalAiChannelConfig1 = aiChannelConfig;
                        sseEmitter.onError((e) -> {
                            log.info("请求异常结束onCompletion:{}", finalAiChannelConfig1.getChannelName());
                            decrementValue(finalAiChannelConfig1);
                        });
                        client.newCall(this.buildRequest(messages, true, aiChannelConfig))
                                .enqueue(
                                        new SteamCallbackUtils(sseEmitter, consumer, aiChannelConfig.getSpeed().longValue(), extralInfo, 0, latch, isSuccess, errorMsgRef, stringRedisTemplate, nlpProperties, aiChannelConfig)
                                );
                        boolean await = latch.await(10, TimeUnit.MINUTES);
                        if (isSuccess.get() && await) {
                            isSuccessFinal = true;
                            break;
                        } else {
                            log.warn("请求异常:{},{}", aiChannelConfig.getChannelName(), errorMsgRef.get());
                            stringBuilderError.append(aiChannelConfig.getChannelName())
                                    .append(": ").append(errorMsgRef.get()).append("\n");
                            decrementValue(aiChannelConfig);
                        }
                    } catch (Exception e) {
                        log.warn("请求 AI 异常{}", e.getMessage());
                        decrementValue(aiChannelConfig);
                    }
                }
                if (!isSuccessFinal) {
                    throw new RuntimeException("请求 AI 异常:\n" + stringBuilderError);
                }
            } catch (Exception e) {
                log.error("请求ai异常:{},请求信息：{}", stringBuilderError, JSONObject.toJSONString(messages), e);
                decrementValue(aiChannelConfig);
                SseEmitterUtils.sendMessage(ResponseConstant.ERROR_RESPONSE_MESSAGE, sseEmitter);
            }
        }).start();
    }

    private void decrementValue(AiChannelConfigExtend aiChannelConfig) {
        if (null != aiChannelConfig) {
            Iterator<Map.Entry<String, AtomicBoolean>> iterator = keyStatusMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, AtomicBoolean> entry = iterator.next();
                if (entry.getKey().equals(aiChannelConfig.getMapKey())) {
                    AtomicBoolean atomicBoolean = entry.getValue();
                    if (atomicBoolean.compareAndSet(false, true)) {
                        String key = entry.getKey().split("\\|\\|")[0];
                        String name = entry.getKey().split("\\|\\|")[2];
                        Long value = stringRedisTemplate.opsForValue().decrement(key, 1);
                        stringRedisTemplate.expire(key, 1000, TimeUnit.SECONDS);
                        log.info("qps释放成功mapKey:{},info:{}，当前QPS:{}", entry.getKey(), name, value);
                        iterator.remove();
                    }
                }
            }
        }
    }


    private Request buildRequest(List<MessageContent> messages, boolean stream, AiChannelConfig aiChannelConfig) {
        JSONObject jsonObject = JSONObject.parseObject(aiChannelConfig.getExtraParam());
        jsonObject.put("stream", stream);
        MediaType mediaType = MediaType.parse("application/json");
        // 视觉模型请求
        if (Objects.equals(aiChannelConfig.getId(), 9999L)) {
            JSONArray jsonArray = new JSONArray();
            for (MessageContent messageContent : messages) {
                JSONObject jsonObjectItem = new JSONObject();
                jsonObjectItem.put("role", messageContent.getRole());
                jsonObjectItem.put("content", messageContent.getImageContent());
                jsonArray.add(jsonObjectItem);
            }
            jsonObject.put("messages", jsonArray);
        } else {
            MessageConfigContext messageConfigContext = this.resizeMessage(messages, aiChannelConfig);
            aiChannelConfig = messageConfigContext.getAiChannelConfig();
            messages = messageConfigContext.getMessageContents();
            jsonObject.put("messages", messages);
        }
        // 过滤敏感信息：
        String[] dataInspection = nlpProperties.getDataInspection().split(",|，");
        String jsonStr = JSONObject.toJSONString(jsonObject);
        for (String mingan : dataInspection) {
            if (jsonStr.contains(mingan)) {
                log.warn("敏感信息：{}", mingan);
                jsonStr = jsonStr.replace(mingan, "xxx");
            }
        }
        log.info("*****对话请求*******{}", jsonStr);
        RequestBody body = RequestBody.create(mediaType, jsonStr);
        Request request = new Request.Builder()
                .url(aiChannelConfig.getChannelUrl())
                .method("POST", body)
                .addHeader("accept", stream ? "text/event-stream" : "application/json")
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + aiChannelConfig.getAuthKey())
                .build();
        //log.info("*******请求模型" + JSONObject.toJSONString(aiChannelConfig) + "chat-gpt内容:{}", JSONObject.toJSONString(jsonObject));
        return request;
    }


    private MessageConfigContext resizeMessage(List<MessageContent> messages, AiChannelConfig aiChannelConfig) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(aiChannelConfig.getExtraParam());
            long maxTokens = Long.parseLong(jsonObject.getOrDefault("max_tokens", "2048").toString());
            long messageToken = calculateTokenize(JSONObject.toJSONString(messages));
            long totalTokens = messageToken + maxTokens;
            while (totalTokens > aiChannelConfig.getModelTokenLimit() && messages.size() > 2) {
                for (int i = 1; i < messages.size() - 2; i++) { // 确保不移除第一个和最后一个
                    if (messages.get(i).getRole().equals("user") && i + 1 < messages.size() - 1 && messages.get(i + 1).getRole().equals("assistant")) {
                        messages.remove(i);
                        messages.remove(i - 1);
                        log.warn("移除对话历史记录:{}", messages.get(i));
                        //重新设置 token
                        messageToken = calculateTokenize(JSONObject.toJSONString(messages));
                        totalTokens = messageToken + maxTokens;
                        break;
                    }
                }
            }
            if (totalTokens > aiChannelConfig.getModelTokenLimit()) {
                /// 不满足 基本请求条件限制 尝试缩短 预期返回 token 数量 2048
                if (messageToken + 2048 > aiChannelConfig.getModelTokenLimit()) {
                    log.error("请求参数过长req:{}", JSONObject.toJSONString(messages));
                } else {
                    // 尝试缩短 预期返回 token 数量 2048
                    jsonObject.put("max_tokens", 2048);
                    log.warn("开始尝试缩短最大预期返回 token 数量 为 2048");
                    aiChannelConfig.setExtraParam(jsonObject.toJSONString());
                }
            }
        } catch (Exception e) {
            log.warn("尝试重新计算messageSize异常", e);
        }
        return new MessageConfigContext(messages, aiChannelConfig);
    }


    private long calculateTokenize(String text) {
        try {
            OkHttpClient client = new OkHttpClient().newBuilder().dispatcher(dispatcher)
                    .connectTimeout(1, TimeUnit.MINUTES)
                    .readTimeout(1, TimeUnit.MINUTES)
                    .writeTimeout(1, TimeUnit.MINUTES)
                    .connectionPool(new ConnectionPool(500, 1, TimeUnit.MINUTES))
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            Map<String, String> req = new HashMap<>();
            req.put("text", text);
            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(req));
            Request request = new Request.Builder()
                    .url("http://ltm.300ce.cn/calculate/tokenize")
                    .method("POST", body)
                    .addHeader("Accept", "*/*")
                    .addHeader("Connection", "keep-alive")
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            JSONObject jsonObject = JSONObject.parseObject(new String(response.body().bytes()));
            long tokens = jsonObject.getLong("token_count");
            log.info("本次请求的 tokens数量：{}", tokens);
            return tokens;
        } catch (Exception e) {
            log.warn("tokenize 计算失败", e);
            return 0L;
        }
    }


    private AiChannelConfigExtend getAiChannelConfig(String channelId, String notChannelId) {
        List<AiChannelConfig> aiChannelConfigs = new ArrayList<>();
        if (Objects.equals(channelId, "image")) {
            AiChannelConfig aiChannelConfig = new AiChannelConfig();
            aiChannelConfig.setId(9999L);
            aiChannelConfig.setChannelName("图片");
            aiChannelConfig.setChannelUrl("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions");
            aiChannelConfig.setAuthKey("sk-7de039270e844634be26fb38451b4867");
            aiChannelConfig.setSpeed(20);
            aiChannelConfig.setOrderNum(1);
            aiChannelConfig.setQpsLimit(99999);
            aiChannelConfig.setModelTokenLimit(99999L);
            aiChannelConfig.setStatus(1);
            aiChannelConfig.setExtraParam("{\n" +
                    "    \"model\": \"qwen-vl-max-latest\",\n" +
                    "    \"max_tokens\": 5000\n" +
                    "}");
            aiChannelConfigs.add(aiChannelConfig);
        } else if (Objects.equals(channelId, "code")) {
            AiChannelConfig aiChannelConfig = new AiChannelConfig();
            aiChannelConfig.setId(9998L);
            aiChannelConfig.setChannelName("code");
            aiChannelConfig.setChannelUrl("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions");
            aiChannelConfig.setAuthKey("sk-7de039270e844634be26fb38451b4867");
            aiChannelConfig.setSpeed(20);
            aiChannelConfig.setOrderNum(1);
            aiChannelConfig.setQpsLimit(99999);
            aiChannelConfig.setModelTokenLimit(99999L);
            aiChannelConfig.setStatus(1);
            if (Objects.equals(nlpProperties.getCodeModel().trim(), "deepseek-r1")) {
                aiChannelConfig.setExtraParam("{\n" +
                        "    \"model\": \"" + nlpProperties.getCodeModel() + "\",\n" +
                        "    \"max_tokens\": 14000\n" +
                        "}");
            } else {
                aiChannelConfig.setExtraParam("{\n" +
                        "    \"model\": \"" + nlpProperties.getCodeModel() + "\",\n" +
                        "    \"max_tokens\": 50000\n" +
                        "}");
            }
            aiChannelConfigs.add(aiChannelConfig);
        } else {
            aiChannelConfigs = new LambdaQueryChainWrapper<>(aiChannelConfigService.getBaseMapper()).eq(AiChannelConfig::getDeleteFlag, 0)
                    // 默认模型永远第一位
                    .orderByDesc(AiChannelConfig::getStatus)
                    .gt(AiChannelConfig::getQpsLimit, 0)
                    .notIn(StringUtils.isNotBlank(notChannelId), AiChannelConfig::getId, Arrays.stream(notChannelId.split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList()))
                    // 其余模型按照 orderNum 去走
                    .orderByAsc(AiChannelConfig::getOrderNum).list();
            if (StringUtils.isNotBlank(channelId)) {
                aiChannelConfigs = aiChannelConfigs.stream().filter(T -> T.getId().equals(Long.parseLong(channelId))).collect(Collectors.toList());
            }
        }
        for (AiChannelConfig aiChannel : aiChannelConfigs) {
            String key = prefix + DigestUtils.md5DigestAsHex((aiChannel.getChannelUrl() + aiChannel.getAuthKey()).getBytes());
            Long qps = qpsList(key, aiChannel.getQpsLimit());
            if (!Objects.equals(qps, 0L)) {
                log.info("当前模型,channelId:{},currentQps:{},maxQps:{},modelName:{}", aiChannel.getId(), qps, aiChannel.getQpsLimit(), aiChannel.getChannelName());
                AiChannelConfigExtend aiChannelConfigExtend = new AiChannelConfigExtend();
                BeanUtils.copyProperties(aiChannel, aiChannelConfigExtend);
                // 生成唯一标识
                String mapKey = key + "||" + UUID.randomUUID() + "||" + aiChannel.getChannelName();
                keyStatusMap.put(mapKey, new AtomicBoolean(false));
                aiChannelConfigExtend.setMapKey(mapKey);
                return aiChannelConfigExtend;
            } else {
                log.warn("模型调用超限制,channelId:{},currentQps:{},modelName:{}", aiChannel.getId(), aiChannel.getQpsLimit(), aiChannel.getChannelName());
            }
        }
        log.warn("找不到对应的模型配置或者模型调用超限制,channelId:{}", channelId);
        throw new RuntimeException("找不到对应的模型配置或者模型调用超限制");
    }

    private Long qpsList(String key, int limit) {
        try {
            DefaultRedisScript<Long> script = new DefaultRedisScript<>();
            script.setScriptText(
                    "local key = KEYS[1] " +
                            "local limit = tonumber(ARGV[1]) " +
                            "local current = redis.call('incr', key) " +
                            "redis.call('expire', key, 1000) " +
                            "if current > limit then redis.call('decr', key) return 0 else return current end"
            );
            script.setResultType(Long.class);
            return stringRedisTemplate.execute(script, Collections.singletonList(key), String.valueOf(limit));
        } catch (Exception e) {
            log.error("redis-lua脚本执行异常", e);
            return 0L;
        }
    }

    public JSONObject getQpsList() {
        List<AiChannelConfig> aiChannelConfigs = new LambdaQueryChainWrapper<>(aiChannelConfigService.getBaseMapper()).eq(AiChannelConfig::getDeleteFlag, 0)
                .orderByDesc(AiChannelConfig::getStatus)
                .orderByAsc(AiChannelConfig::getOrderNum).list();
        JSONObject jsonObject = new JSONObject();
        for (AiChannelConfig aiChannelConfig : aiChannelConfigs) {
            String key = prefix + DigestUtils.md5DigestAsHex((aiChannelConfig.getChannelUrl() + aiChannelConfig.getAuthKey()).getBytes());
            String value = stringRedisTemplate.opsForValue().get(key);
            jsonObject.put(aiChannelConfig.getChannelName(), "当前QPS:" + Integer.parseInt(StringUtils.isNotBlank(value) ? value : "0") + ",最大QPS:" + aiChannelConfig.getQpsLimit());
        }
        String ip;
        try {
            ip = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            ip = null;
        }
        jsonObject.put("当前客户端" + ip + "存在的请求数量:", keyStatusMap.keySet().size());
        return jsonObject;
    }

    public CustomerInfoByNameBusinessView getCustomerInfoByName(@Valid CustomerInfoByNameBusinessDto queryDto) {

        String customerName = queryDto.getCustomerName();

        String customerId = null;
        Optional<CustomerDataThirdView> customerDataByCustomerName = customerThirdService.getCustomerDataByCustomerName(customerName);
        if (customerDataByCustomerName.isPresent()) {
            customerId = customerDataByCustomerName.get().getCustomerId();
        }

        String pid = null;
        BigDataCompanyDetail bigDataCustomerInfo = bigDataThirdService.getBigDataCustomerInfo(customerName);
        if (bigDataCustomerInfo != null) {
            pid = bigDataCustomerInfo.getPid();
        }

        return CustomerInfoByNameBusinessView.builder()
                .customerName(customerName).customerId(customerId).pid(pid).build();
    }

    public void imageGenerator(String askMessage, SseEmitter sseEmitter, Consumer<StreamAnswer> consumer, ExtralInfo extralInfo, String employeeId, String employeeName) {
        String message = "员工姓名：" + employeeName + "\n" +
                "图片描述：" + askMessage + "\n";
        JSONObject jsonObject = new JSONObject();
        try {
            JSONObject reqBody = JSONObject.parseObject("{\"model\":\"qwen-plus\",\"stream\":false}");
            reqBody.put("messages", Arrays.asList(new MessageContent("system", nlpProperties.getImageGenerator()), new MessageContent("user", askMessage)));
            RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSONObject.toJSONString(reqBody));
            Request request = new Request.Builder()
                    .url("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions")
                    .method("POST", body)
                    .addHeader("Authorization", "Bearer sk-7de039270e844634be26fb38451b4867")
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            String result = new String(response.body().bytes());
            log.info("图片生成 ai返回结果：{}", result);
            String url = getUrl(result);
            log.info("图片生成解析出来的 URL：{}", url);
            if (StringUtils.isBlank(url)) {
                url = "https://image.pollinations.ai/prompt/" + askMessage.trim() + "?width=1024&height=1024&seed=100&model=flux&nologo=true";
            }
            String imageName = UUID.randomUUID().toString().replace("-", "") + ".png";
            String path = "/aiimage/" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + "/";
            File file = new File(uploadDir + path);
            if (!file.exists()) {
                file.mkdirs();
            }
            String savePath = uploadDir + path + imageName;
            String viewUrl = uploadUrl + path + imageName;
            log.info("图片生成\n" + message);
            int limitimage = Integer.parseInt(nlpProperties.getImageGeneratorLimit());
            jsonObject.put("savePath", savePath);
            jsonObject.put("viewUrl", viewUrl);
            jsonObject.put("originUrl", url);
            jsonObject.put("question", askMessage);
            jsonObject.put("employeeId", employeeId);
            jsonObject.put("employeeName", employeeName);
            try {
                long limit = stringRedisTemplate.opsForValue().increment(AICRM_IMAGE_GENERATOR_LIMIT, 1L);
                stringRedisTemplate.expire(AICRM_IMAGE_GENERATOR_LIMIT, 10, TimeUnit.MINUTES);
                if (limit > limitimage) {
                    log.warn("请求生成图片的人太多了,请稍后再试...");
                    // 发送MQ消息
                    rocketMqOperate.syncSend(SCRM_AI_IMAGE_GENERATOR_TOPIC, JSONObject.toJSONString(jsonObject), 3);
                    SseEmitterUtils.sendMessage("您的图片生成任务已提交，正在处理中～ 完成后将通过企业微信给您发通知，请您注意查收。", sseEmitter);
                    return;
                }
                extralInfo.setExtralUrl(url);
                try {
                    imageDownload.downAndSaveImage(url, savePath);
                } catch (Exception e) {
                    log.warn("下载失败后转为,异步任务");
                    rocketMqOperate.syncSend(SCRM_AI_IMAGE_GENERATOR_TOPIC, JSONObject.toJSONString(jsonObject), 3);
                    SseEmitterUtils.sendMessage("您的图片生成任务已提交，正在处理中～ 完成后将通过企业微信给您发通知，请您注意查收。", sseEmitter);
                    return;
                }
                String urlNow = "![image](" + viewUrl + ")";
                message = message + "图片URL：" + viewUrl;
                this.sendWxMessage(message, "black");
                consumer.accept(new StreamAnswer(urlNow, MessageTypeEnum.TEXT.getType(), "", extralInfo));
                SseEmitterUtils.sendMessage(urlNow, "", sseEmitter, 0, null, true, JSONObject.parseObject(JSONObject.toJSONString(extralInfo)));
            } finally {
                stringRedisTemplate.opsForValue().decrement(AICRM_IMAGE_GENERATOR_LIMIT, 1L);
            }
        } catch (Exception e) {
            log.error("请求 ai 异常:{}", e.getMessage());
            message = message + "图片URL：图片生成失败。";
            this.sendWxMessage(message, "red");
            sendWxMessage.sendMessage(employeeId, message);
            SseEmitterUtils.sendMessage(ResponseConstant.ERROR_RESPONSE_MESSAGE, sseEmitter);
        }

    }


    public void sendWxMessage(String message, String type) {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .connectTimeout(10, TimeUnit.SECONDS)  // 连接超时时间
                .readTimeout(10, TimeUnit.SECONDS)     // 读取超时时间
                .writeTimeout(10, TimeUnit.SECONDS)    // 写入超时时间
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\n  \"msgtype\": \"markdown\",\n  \"markdown\": {\n    \"content\": \"<font color=\\\"" + type + "\\\">" + message + "</font>\"\n  }\n}");
        Request request = new Request.Builder()
                .url("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=044c53b3-939f-4c8b-be7c-b75bcebdf91f")
                .method("POST", body)
                .addHeader("Authorization", "Bearer sk-7de039270e844634be26fb38451b4867")
                .addHeader("Content-Type", "application/json")
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                log.info("发送企业微信消息成功:{}", response.body().string());
            } else {
                log.error("发送企业微信消息失败:{}", response.message());
            }
        } catch (Exception e) {
            log.error("发送企微消息失败", e);
        }
    }


    public static String getUrl(String text) {
        // 正则表达式匹配 URL
        String regex = "(https?://[\\w\\-\\.]+(:\\d+)?(/[\\w\\-\\./?%&=]*)?)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        List<String> urls = new ArrayList<>();
        while (matcher.find()) {
            urls.add(matcher.group());
        }
        if (!CollectionUtils.isEmpty(urls)) {
            return urls.get(0);
        }
        return null;
    }

    @Data
    @AllArgsConstructor
    public class MessageConfigContext {

        private List<MessageContent> messageContents;

        private AiChannelConfig aiChannelConfig;
    }

    /**
     * 构建业绩数据信息
     *
     * @param orgId 组织id
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/4/3 15:47
     * @version 1.0.0
     **/
    public String buildPerformanceData(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            log.warn("获取业绩信息，orgId不能为空");
            return StringUtils.EMPTY;
        }
        String cacheKey = PERFORMANCE_CACHE_PREFIX + orgId;
        String cachedData = stringRedisTemplate.opsForValue().get(cacheKey);
        if (cachedData != null) {
            log.info("从Redis缓存中获取业绩数据,orgId={}", orgId);
            return cachedData;
        }
        try {
            Request request = new Request.Builder()
                    .url(SOP_PERFORMANCE + orgId)
                    .get()
                    .addHeader("Request-Origion", "SwaggerBootstrapUi")
                    .build();
            Response execute = client.newCall(request).execute();
            String response = execute.body().string();
            JSONObject resJson = JSON.parseObject(response);
            if (Objects.equals("success", resJson.getString("status")) && StringUtils.isNotBlank(resJson.getString("data"))) {
                JSONObject data = resJson.getJSONObject("data");
                JSONObject summary = data.getJSONObject("summary");
                JSONArray details = data.getJSONArray("detail");
                if (Objects.isNull(details) || Objects.isNull(summary)) {
                    log.warn("业绩数据未空");
                }
                // 汇总数据构建
                JSONObject modifiedSummary = new JSONObject();
                for (Map.Entry<String, Object> entry : summary.entrySet()) { // 让业绩更易读，小数点后面保留两位
                    String key = entry.getKey() + "业绩";
                    BigDecimal value = new BigDecimal(entry.getValue().toString()).setScale(2, RoundingMode.HALF_UP);
                    modifiedSummary.put(key, value + "元");
                }
                // 详情数据构建
                List<AiOrgPerformanceInfoEnView> detailList = JSON.parseArray(details.toJSONString(), AiOrgPerformanceInfoEnView.class);
                JSONObject res = new JSONObject();
                res.put("业绩汇总", modifiedSummary);
                // detailList to List<AiOrgPerformanceInfoView>
                List<AiOrgPerformanceInfoView> view = detailList.stream().map(item -> AiOrgPerformanceInfoView.builder()
                        .toAccountMonth(item.getToAccountMonth())
                        .productName(item.getProductName())
                        .signAmount(new BigDecimal(item.getSignAmount()).setScale(2, RoundingMode.HALF_UP) + "元")
                        .signBusinessNum(item.getSignBusinessNum())
                        .signCustNum(item.getSignCustNum())
                        .signNewCustNum(item.getSignNewCustNum())
                        .signOldCustNum(item.getSignOldCustNum())
                        .build()).collect(Collectors.toList());
                res.put("业绩明细", view);
                String result = res.toJSONString();
                stringRedisTemplate.opsForValue().set(cacheKey, result, 1, TimeUnit.DAYS);
                return result;
            }
        } catch (Exception e) {
            log.error("调用SOP业绩接口信息出错：{}", e.getMessage());
        }
        return StringUtils.EMPTY;
    }

//    public List<SubVisitSigningView> buildSubVisitSigningData(String customerName) {
//
//
//        SubVisitSigningDubboDto subVisitSigningDubboDto = new SubVisitSigningDubboDto();
//        subVisitSigningDubboDto.setCustomerName(customerName);
//        DubboResult<List<SubVisitSigningDubboView>> dubboResult = funnelDubbo.getSubVisitSigningDubboView(subVisitSigningDubboDto);
//        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
//            log.info("buildPerformanceData，返回数据失败,customerName={}", customerName);
//            return null;
//        }
//        List<SubVisitSigningDubboView> data = dubboResult.getData();
//
//        Map<String, List<SubVisitSigningDubboView>> groupedByMonth = data.stream()
//                .collect(Collectors.groupingBy(
//                        item -> getYearMonthKey(item.getDate())
//                ));
//
//        List<SubVisitSigningView> result = new ArrayList<>();
//        groupedByMonth.forEach((month, items) -> {
//
//            SubVisitSigningView item = new SubVisitSigningView();
//            item.setDate(month);
//            int convertUserStep1Count = 0;
//            int convertUserStep2Count = 0;
//            int totalUserCount = 0;
//            int completionConvertedUserCount = 0;
//            for (SubVisitSigningDubboView dubboView : items) {
//
//                convertUserStep1Count += dubboView.getConvertUserStep1();
//                convertUserStep2Count += dubboView.getConvertUserStep2();
//                totalUserCount += dubboView.getTotalUser();
//                completionConvertedUserCount += dubboView.getCompletionConvertedUser();
//
//            }
//            if (convertUserStep1Count == 0 || totalUserCount == 0) {
//                item.setConvertRateStep1(0.0);
//            } else {
//                double convertRateStep1 = (double) convertUserStep1Count / totalUserCount;
//                item.setConvertRateStep1(convertRateStep1);
//            }
//
//            if (completionConvertedUserCount == 0 || convertUserStep1Count == 0) {
//                item.setConvertRateStep2(0.0);
//            } else {
//                double convertRateStep2 = (double) completionConvertedUserCount / convertUserStep1Count;
//                item.setConvertRateStep2(convertRateStep2);
//            }
//
//            if (completionConvertedUserCount == 0 || totalUserCount == 0) {
//                item.setCompletionRate(0.0);
//            } else {
//                double completionRate = (double) completionConvertedUserCount / totalUserCount;
//                item.setCompletionRate(completionRate);
//            }
//
//            item.setConvertUserStep1(convertUserStep1Count);
//            item.setConvertUserStep2(convertUserStep2Count);
//            item.setTotalUser(totalUserCount);
//            item.setCompletionConvertedUser(completionConvertedUserCount);
//            result.add(item);
//        });
//        return result;
//    }

    // 更安全的月份键生成方法
    private static String getYearMonthKey(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1; // 转换为1-12
        return year + "-" + (month < 10 ? "0" + month : month);
    }

    /**
     * 组装业绩和拜访报价签单漏斗数据信息
     */
    public String buildPerformanceAndVisitSigningData(String orgId) {
        String performanceStr = this.buildPerformanceData(orgId);
        OrgVo orgVo = orgAppService.selectOne(orgId);
        List<SubVisitSigningView> subVisitSigningViews = Collections.emptyList();
        try {
            subVisitSigningViews = this.buildSubVisitSigningDataNew(orgVo.getName());
        } catch (Exception e) {
            log.error("组装业绩和拜访报价签单漏斗数据信息出错：{}", e.getMessage());
        }

        JSONObject resultJson = new JSONObject();
        if (!CollectionUtils.isEmpty(subVisitSigningViews)) {
            JSONArray subVisitSigningArray = JSONArray.parseArray(JSON.toJSONString(subVisitSigningViews));
            resultJson.put("分司拜访-报价-签单漏斗数据", subVisitSigningArray);
        } else {
            log.warn("分司拜访-报价-签单漏斗数据为空,orgId={}", orgId);
        }
        if (StringUtils.isNotBlank(performanceStr)) {
            JSONObject performanceJson = JSON.parseObject(performanceStr);
            resultJson.put("业绩数据", performanceJson);
        } else {
            log.warn("业绩数据为空,orgId={}", orgId);
        }
        if (StringUtils.isBlank(resultJson.toJSONString())) {
            return StringUtils.EMPTY;
        }
        return resultJson.toJSONString();
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveAllAiAccessAuth(List<AiAccessAuth> aiAccessAuths, String operatorId) {
        Set<String> strings = new HashSet<>();
        aiAccessAuths = aiAccessAuths.stream().filter(T -> strings.add(T.getEmployeeId())).distinct().peek(T -> {
            T.setUpdateTime(new Date());
            T.setCreateTime(new Date());
            T.setOperator(operatorId);
        }).collect(Collectors.toList());
        aiAccessAuthService.lambdaUpdate().gt(AiAccessAuth::getId, 0).remove();
        aiAccessAuthService.saveBatch(aiAccessAuths);
    }

    public List<SubVisitSigningView> buildSubVisitSigningDataNew(String customerName) {
        SubVisitSigningDubboDto subVisitSigningDubboDto = new SubVisitSigningDubboDto();
        subVisitSigningDubboDto.setCustomerName(customerName);
        DubboResult<List<SubVisitSigningNewDubboView>> dubboResult = funnelDubbo.getSubVisitSigningDubboViewNew(subVisitSigningDubboDto);
        if (dubboResult == null || !dubboResult.checkSuccess() || dubboResult.getData() == null) {
            log.info("buildPerformanceData，返回数据失败,customerName={}", customerName);
            return null;
        }
        List<SubVisitSigningNewDubboView> data = dubboResult.getData();
        List<SubVisitSigningNewView> subVisitSigningNewViewList = BeanUtil.copyToList(data, SubVisitSigningNewView.class);

        List<SubVisitSigningView> result = new ArrayList<>();
        for (SubVisitSigningNewView item : subVisitSigningNewViewList) {

            SubVisitSigningView build = SubVisitSigningView.builder()
                    .date(item.getTime())
                    .convertUserStep2(item.getStepFoldUserCount())
                    .convertUserStep1(item.getStep2UserCount())
                    .convertRateStep2(item.getStep2ConversionRate())
                    .completionRate(item.getStepFoldConversionRate())
                    .totalUser(item.getStep1UserCount())
                    .convertRateStep1(item.getStep1ConversionRate())
                    .completionConvertedUser(item.getStep3UserCount())
                    .build();
            result.add(build);

        }
        return result;
    }

    /*******************新的逻辑**************************/

    /**
     * 单次分析
     */
    public ResponseEntity<SseEmitter> getAnalysisiTips(AiQuerySmartDto aiQueryDto) {
        log.info("请求信息:{}", JSONObject.toJSONString(aiQueryDto));
        if ((StringUtils.isBlank(aiQueryDto.getPid()) && StringUtils.isBlank(aiQueryDto.getCustId()))
                || StringUtils.isBlank(aiQueryDto.getPromptId())) {
            SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
            SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
            return ResponseEntity.ok(errorSseEmitter);
        }
        Integer retainCust = 0;
        String customerName = null;
        if (StringUtils.isNotBlank(aiQueryDto.getCustId())) {
            Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(aiQueryDto.getCustId());
            if (customerData.isPresent()) {
                CustomerDataThirdView customerDataThirdView = customerData.get();
                //埋点使用 是否是保有客户
                if (Objects.nonNull(customerDataThirdView.getTagRetainCust())) {
                    retainCust = customerDataThirdView.getTagRetainCust();
                }
                if (org.springframework.util.StringUtils.isEmpty(aiQueryDto.getPid())) {
                    aiQueryDto.setPid(customerDataThirdView.getSourceDataId());
                    customerName = customerDataThirdView.getCustomerName();
                }
            }
        }
        try {
            // 非强制重新分析时，从缓存中获取数据
            if (!Objects.equals(aiQueryDto.getOperateType(), 7)) {
                LambdaQueryChainWrapper<AiEventLog> lambdaQueryChainWrapper = aiEventLogService.lambdaQuery()
                        .eq(AiEventLog::getEmployeeId, aiQueryDto.getLoginEmployeeId())
                        .eq(StringUtils.isNotBlank(aiQueryDto.getPid()), AiEventLog::getPid, aiQueryDto.getPid())
                        .eq(StringUtils.isNotBlank(aiQueryDto.getCustId()), AiEventLog::getCustId, aiQueryDto.getCustId())
                        .eq(AiEventLog::getPromptId, aiQueryDto.getPromptId())
                        .eq(AiEventLog::getQuestionType, aiQueryDto.getQuestionType())
                        .orderByDesc(AiEventLog::getCreateTime).last(" limit 1");
                if (StringUtils.isNotBlank(aiQueryDto.getExtraPromptId())) {
                    lambdaQueryChainWrapper.eq(AiEventLog::getExtraPromptId, aiQueryDto.getExtraPromptId());
                } else {
                    lambdaQueryChainWrapper.isNull(AiEventLog::getExtraPromptId);
                }
                List<AiEventLog> aiEventLogs = lambdaQueryChainWrapper.list();
                if (!CollectionUtils.isEmpty(aiEventLogs)) {
                    AiEventLog aiEventLog = aiEventLogs.get(0);
                    SseEmitter cacheSseEmitter = SseEmitterUtils.getSseEmitter();
                    ExtralInfo build = ExtralInfo.builder()
                            .promptId(String.valueOf(aiEventLog.getPromptId()))
                            .channelId(aiQueryDto.getChannelId())
                            .chatId(aiEventLog.getChatId())
                            .retainCust(retainCust)
                            .eventId(aiEventLog.getEventId())
                            .parentId(aiEventLog.getEventId())
                            .build();
                    if (Objects.nonNull(aiEventLog.getExtraPromptId())) {
                        build.setExtraPromptId(String.valueOf(aiEventLog.getExtraPromptId()));
                    }
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(build));
                    SseEmitterUtils.sendMessage(aiEventLog.getChatResponse(), aiEventLog.getChatReasoning(), cacheSseEmitter, 0, null, true, jsonObject);
                    return ResponseEntity.ok(cacheSseEmitter);
                }
            }
            //从ai模型获取数据
            AiBusinessDto businessDto = BeanUtil.copyProperties(aiQueryDto, AiBusinessDto.class);
            JSONObject jsonObjectResult = aiCustCacheBusiness.buildJsonData(businessDto);
            if (jsonObjectResult.isEmpty()) {
                SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                SseEmitterUtils.sendMessage(ResponseConstant.NULL_INFO_ANSWER, errorSseEmitter);
                return ResponseEntity.ok(errorSseEmitter);
            }
            if (StringUtils.isNotBlank(customerName)) {
                jsonObjectResult.put("企业名称", customerName);
            }
            SequenceIdRequest sequenceIdRequest = new SequenceIdRequest();
            sequenceIdRequest.setBusinessType(10);
            Long eventId = sequenceService.generateId(sequenceIdRequest).getData();
            final String customerNameFinal = customerName;
            ResponseEntity<SseEmitter> responseEntity = this.getAiTips(
                    JsonToMarkdown.convert2TmarkDown(jsonObjectResult.toJSONString()),
                    messageRes -> {
                        AiEventLog aiEventLog = new AiEventLog();
                        aiEventLog.setEventId(String.valueOf(eventId));
                        aiEventLog.setEmployeeId(aiQueryDto.getLoginEmployeeId());
                        aiEventLog.setParentEventId(aiEventLog.getEventId());
                        aiEventLog.setEmployeeName(aiQueryDto.getLoginEmployeeName());
                        aiEventLog.setCustId(aiQueryDto.getCustId());
                        aiEventLog.setPid(aiQueryDto.getPid());
                        aiEventLog.setCustName(customerNameFinal);
                        aiEventLog.setPromptId(Long.valueOf(aiQueryDto.getPromptId()));
                        aiEventLog.setPlatform(aiQueryDto.getPlatform());
                        aiEventLog.setChatReasoning(messageRes.getReasoningContent());
                        aiEventLog.setChatResponse(messageRes.getContent());
                        aiEventLog.setQuestionSource(aiQueryDto.getQuestionSource());
                        aiEventLog.setQuestionType(aiQueryDto.getQuestionType());
                        aiEventLog.setOperateType(aiQueryDto.getOperateType());
                        if (StringUtils.isNotBlank(aiQueryDto.getExtraPromptId())) {
                            aiEventLog.setExtraPromptId(Long.valueOf(aiQueryDto.getExtraPromptId()));
                        }
                        aiEventLogService.save(aiEventLog);
                    }, ExtralInfo.builder()
                            .promptId(aiQueryDto.getPromptId())
                            .extraPromptId(aiQueryDto.getExtraPromptId())
                            .channelId(aiQueryDto.getChannelId())
                            .retainCust(retainCust)
                            .eventId(eventId.toString())
                            .parentId(eventId.toString())
                            .build());
            log.info("请求ai完成:{}", JSONObject.toJSONString(aiQueryDto));
            return responseEntity;
        } catch (Exception e) {
            log.error("请求失败", e);
            SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
            SseEmitterUtils.sendMessage(ResponseConstant.ERROR_RESPONSE_MESSAGE, errorSseEmitter);
            return ResponseEntity.ok(errorSseEmitter);
        }
    }

    /**
     * 保存 starlog
     */
    public boolean saveAiStarLog(AiQuerySmartDto aiQuerySmartDto) {
        if (StringUtils.isBlank(aiQuerySmartDto.getParentEventId())) {
            log.warn("点赞点low参数缺失:{}", JSONObject.toJSONString(aiQuerySmartDto));
            return false;
        }
        List<AiEventLog> aiEventLogList = aiEventLogService.lambdaQuery().eq(AiEventLog::getEventId, aiQuerySmartDto.getParentEventId()).list();
        if (CollectionUtils.isEmpty(aiEventLogList)) {
            log.warn("点赞点low参数缺失:{}", JSONObject.toJSONString(aiQuerySmartDto));
            return false;
        }
        AiEventLog aiEventLogOld = aiEventLogList.get(0);
        SequenceIdRequest sequenceIdRequest = new SequenceIdRequest();
        sequenceIdRequest.setBusinessType(10);
        Long eventId = sequenceService.generateId(sequenceIdRequest).getData();
        AiEventLog aiEventLog = new AiEventLog();
        aiEventLog.setEventId(String.valueOf(eventId));
        aiEventLog.setParentEventId(aiQuerySmartDto.getParentEventId());
        aiEventLog.setEmployeeId(aiQuerySmartDto.getLoginEmployeeId());
        aiEventLog.setEmployeeName(aiQuerySmartDto.getLoginEmployeeName());
        aiEventLog.setPlatform(aiQuerySmartDto.getPlatform());
        aiEventLog.setOperateType(aiQuerySmartDto.getOperateType());
        aiEventLog.setLowReason(aiQuerySmartDto.getLowReason());
        aiEventLog.setChatId(aiEventLogOld.getChatId());
        aiEventLog.setPid(aiEventLogOld.getPid());
        aiEventLog.setCustId(aiEventLogOld.getCustId());
        aiEventLog.setCustName(aiEventLogOld.getCustName());
        return aiEventLogService.save(aiEventLog);
    }


    /**
     * 聊天
     */
    public ResponseEntity<SseEmitter> getAiChatComplete(AiQuerySmartDto aiQueryDto) {
        // 语音分析存在首次不存在parentId 的情况
        if (!Objects.equals(aiQueryDto.getQuestionType(), 8) && !Objects.equals(aiQueryDto.getQuestionType(), 9)) {
            if (StringUtils.isBlank(aiQueryDto.getChatQuestion()) || StringUtils.isBlank(aiQueryDto.getParentEventId())) {
                SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
                return ResponseEntity.ok(errorSseEmitter);
            }
        }
        SequenceIdRequest sequenceIdRequest = new SequenceIdRequest();
        sequenceIdRequest.setBusinessType(10);
        Long eventIdNow = sequenceService.generateId(sequenceIdRequest).getData();
        if (StringUtils.isBlank(aiQueryDto.getChatId())) {
            Long chatId = sequenceService.generateId(sequenceIdRequest).getData();
            aiQueryDto.setChatId(String.valueOf(chatId));
        }
        AiEventLog aiEventLogOld = new AiEventLog();
        List<AiEventLog> aiEventLogsOld = aiEventLogService.lambdaQuery().eq(AiEventLog::getEventId, aiQueryDto.getParentEventId()).list();
        if (!Objects.equals(aiQueryDto.getQuestionType(), 8) && !Objects.equals(aiQueryDto.getQuestionType(), 9)) {
            if (CollectionUtils.isEmpty(aiEventLogsOld)) {
                SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
                return ResponseEntity.ok(errorSseEmitter);
            } else {
                aiEventLogOld = aiEventLogsOld.get(0);
            }
        }
        AiVoiceAnalyze analyze = new AiVoiceAnalyze();
        if (Objects.equals(aiQueryDto.getQuestionType(), 8) || Objects.equals(aiQueryDto.getQuestionType(), 9)) {
            // 找对应的 语音分析结果
            VoiceAnalyzeWebDto voiceAnalyzeWebDto = new VoiceAnalyzeWebDto();
            voiceAnalyzeWebDto.setId(aiQueryDto.getVoiceId());
            analyze = this.getVoiceInfoById(voiceAnalyzeWebDto);
            if (null == analyze) {
                SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
                return ResponseEntity.ok(errorSseEmitter);
            } else {
                aiEventLogOld.setCustName(analyze.getCustName());
                aiEventLogOld.setCustId(analyze.getCustId());
            }
        }
        try {
            AiEventLog finalAiEventLogOld = aiEventLogOld;
            Consumer<StreamAnswer> streamAnswerConsumer = result -> {
                if (result.getType() == -1) {
                    log.error("图片请求异常:{},请求参数{}", result.getContent(), JSONObject.toJSONString(aiQueryDto));
                    return;
                }
                AiEventLog aiEventLog = new AiEventLog();
                aiEventLog.setEventId(String.valueOf(eventIdNow));
                aiEventLog.setParentEventId(aiQueryDto.getParentEventId());
                aiEventLog.setEmployeeId(aiQueryDto.getLoginEmployeeId());
                aiEventLog.setEmployeeName(aiQueryDto.getLoginEmployeeName());
                if (StringUtils.isNotBlank(aiQueryDto.getPromptId())) {
                    aiEventLog.setPromptId(Long.valueOf(aiQueryDto.getPromptId()));
                }
                aiEventLog.setCustId(finalAiEventLogOld.getCustId());
                aiEventLog.setPid(finalAiEventLogOld.getPid());
                aiEventLog.setCustName(finalAiEventLogOld.getCustName());
                aiEventLog.setPlatform(aiQueryDto.getPlatform());
                aiEventLog.setChatId(aiQueryDto.getChatId());
                aiEventLog.setChatQuestion(aiQueryDto.getChatQuestion());
                aiEventLog.setChatReasoning(result.getReasoningContent());
                aiEventLog.setChatResponse(result.getContent());
                aiEventLog.setNeedSearch(aiQueryDto.isNeedSearch() ? 1 : 0);
                aiEventLog.setVoiceId(aiQueryDto.getVoiceId());
                if (!CollectionUtils.isEmpty(aiQueryDto.getImageUrls())) {
                    aiEventLog.setChatQuestionImages(String.join(",", aiQueryDto.getImageUrls()));
                }
                aiEventLog.setQuestionSource(aiQueryDto.getQuestionSource());
                aiEventLog.setQuestionType(aiQueryDto.getQuestionType());
                aiEventLog.setOperateType(aiQueryDto.getOperateType());
                aiEventLogService.save(aiEventLog);
            };
            SseEmitter sseEmitter = SseEmitterUtils.getSseEmitter();
            String userAsk = aiQueryDto.getChatQuestion();
            // 信息拦截
            if (containsUrl(userAsk)
                    && !aiQueryDto.isNeedSearch()
                    && !Objects.equals(aiQueryDto.getQuestionType(), 10)) {
                SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                SseEmitterUtils.sendMessage(ResponseConstant.ERROR_MU_RESPONSE_MESSAGE, errorSseEmitter);
                return ResponseEntity.ok(errorSseEmitter);
            }

            Integer retainCust = 0;
            if (StringUtils.isNotBlank(aiQueryDto.getCustId())) {
                Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(aiQueryDto.getCustId());
                if (customerData.isPresent()) {
                    CustomerDataThirdView customerDataThirdView = customerData.get();
                    //埋点使用 是否是保有客户
                    if (Objects.nonNull(customerDataThirdView.getTagRetainCust())) {
                        retainCust = customerDataThirdView.getTagRetainCust();
                    }
                }
            }
            ExtralInfo.ExtralInfoBuilder extralInfo = ExtralInfo.builder()
                    .channelId(aiQueryDto.getChannelId())
                    .chatId(aiQueryDto.getChatId())
                    .eventId(String.valueOf(eventIdNow))
                    .parentId(aiQueryDto.getParentEventId())
                    .retainCust(retainCust);
            if (Objects.equals(aiQueryDto.getQuestionType(), 10)) {
                extralInfo.channelId("code");
                List<MessageContent> messageContentsReq = new ArrayList<>();
                messageContentsReq.add(new MessageContent("user", userAsk));
                this.sendMsgStream(messageContentsReq, sseEmitter, streamAnswerConsumer, extralInfo.build());
                return ResponseEntity.ok(sseEmitter);
            }
            if (Objects.equals(aiQueryDto.getQuestionType(), 5)) {
                // 判断 是否要生成图片
                if (aiQueryDto.isImageGenerator()) {
                    this.imageGenerator(userAsk, sseEmitter, streamAnswerConsumer, extralInfo.build(), aiQueryDto.getLoginEmployeeId(), aiQueryDto.getLoginEmployeeName());
                    return ResponseEntity.ok(sseEmitter);
                }
            }
            // 判断 是否图片提问
            if (Objects.equals(aiQueryDto.getQuestionType(), 2)) {
                if (CollectionUtils.isEmpty(aiQueryDto.getImageUrls())) {
                    SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                    SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
                    return ResponseEntity.ok(errorSseEmitter);
                }
                List<MessageContent> messageContents = new ArrayList<>();
                MessageContent messageContent = new MessageContent();
                messageContent.setRole("user");
                ImageContent imageContent = new ImageContent();
                imageContent.setImage_url(new ImageContent.ImageUrl(aiQueryDto.getImageUrls().get(0)));
                imageContent.setType("image_url");
                ImageContent imageContentText = new ImageContent();
                imageContentText.setText(aiQueryDto.getChatQuestion());
                imageContentText.setType("text");
                messageContent.setImageContent(Arrays.asList(imageContent, imageContentText));
                messageContents.add(messageContent);
                this.sendMsgStream(messageContents, sseEmitter, streamAnswerConsumer, extralInfo
                        .channelId("image")
                        .build());
                return ResponseEntity.ok(sseEmitter);
            }
            // 方案整合
            if (Objects.equals(aiQueryDto.getQuestionType(), 3)) {
                if (CollectionUtils.isEmpty(aiQueryDto.getLinkedEventIds())) {
                    SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                    SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
                    return ResponseEntity.ok(errorSseEmitter);
                }
                // 查询关联事件
                List<AiEventLog> aiEventLogs = aiEventLogService.lambdaQuery().in(AiEventLog::getEventId, aiQueryDto.getLinkedEventIds()).list();
                if (CollectionUtils.isEmpty(aiEventLogs)) {
                    SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                    SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
                    return ResponseEntity.ok(errorSseEmitter);
                }
                List<MessageContent> messageContents = new ArrayList<>();
                for (AiEventLog aiEventLog : aiEventLogs) {
                    messageContents.add(new MessageContent("assistant", aiEventLog.getChatResponse()));
                }
                messageContents.add(new MessageContent("user", userAsk));
                this.sendMsgStream(messageContents, sseEmitter, streamAnswerConsumer, extralInfo.build());
                return ResponseEntity.ok(sseEmitter);
            }
            // 网站检测分析
            if (Objects.equals(aiQueryDto.getQuestionType(), 7)) {
                if (StringUtils.isBlank(aiQueryDto.getAnalyzeDomain())) {
                    SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                    SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
                    return ResponseEntity.ok(errorSseEmitter);
                }
                try {
                    String analyzeDomain = aiQueryDto.getAnalyzeDomain();
                    if (!analyzeDomain.matches("^[a-zA-Z]+://.*")) {
                        analyzeDomain = " https://" + analyzeDomain;
                    }
                    URL url = new URL(analyzeDomain);
                    aiQueryDto.setAnalyzeDomain(url.getHost());
                } catch (Exception e) {
                    SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                    SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
                    return ResponseEntity.ok(errorSseEmitter);
                }
                JSONObject webSiteReport = aiCustCacheBusiness.getWebSiteReport(null, aiQueryDto.getAnalyzeDomain(), aiQueryDto.getLoginEmployeeId());
                if (null == webSiteReport) {
                    SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                    SseEmitterUtils.sendMessage("网站分析报告未生产，请稍后再试", errorSseEmitter);
                    return ResponseEntity.ok(errorSseEmitter);
                }
                List<MessageContent> messageContents = new ArrayList<>();
                messageContents.add(new MessageContent("system", nlpProperties.getWebSiteReport() + "\n网站分析报告：\n" + JSONObject.toJSONString(webSiteReport)));
                messageContents.add(new MessageContent("user", aiQueryDto.getChatQuestion()));
                this.sendMsgStream(messageContents, sseEmitter, streamAnswerConsumer, extralInfo.build());
                return ResponseEntity.ok(sseEmitter);
            }
            // 语音分析整合
            if (Objects.equals(aiQueryDto.getQuestionType(), 8) || Objects.equals(aiQueryDto.getQuestionType(), 9)) {
                AiEventLog aiEventLog = null;
                Long parentId = null;
                if (Objects.equals(aiQueryDto.getQuestionType(), 9)) {
                    // 9:老方案修改
                    //如果之前老方案存在，直接从缓存获取
                    AiEventLog aiEventLogForQuestionTypeNine = getAiEventLogForQuestionTypeNine(aiQueryDto);
                    if (Objects.nonNull(aiEventLogForQuestionTypeNine)) {
                        SseEmitter cacheSseEmitter = SseEmitterUtils.getSseEmitter();
                        ExtralInfo build = ExtralInfo.builder()
                                .promptId(String.valueOf(aiEventLogForQuestionTypeNine.getPromptId()))
                                .channelId(aiQueryDto.getChannelId())
                                .chatId(aiEventLogForQuestionTypeNine.getChatId())
                                .retainCust(retainCust)
                                .eventId(aiEventLogForQuestionTypeNine.getEventId())
                                .parentId(aiEventLogForQuestionTypeNine.getEventId())
                                .build();
                        if (Objects.nonNull(aiEventLogForQuestionTypeNine.getExtraPromptId())) {
                            build.setExtraPromptId(String.valueOf(aiEventLogForQuestionTypeNine.getExtraPromptId()));
                        }
                        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(build));
                        SseEmitterUtils.sendMessage(aiEventLogForQuestionTypeNine.getChatResponse(), aiEventLogForQuestionTypeNine.getChatReasoning(), cacheSseEmitter, 0, null, true, jsonObject);
                        return ResponseEntity.ok(cacheSseEmitter);
                    }
                    aiEventLog = getAiEventLogOld(aiQueryDto);
                    if (aiEventLog != null) {
                        // 没有parent的情况 和有parent 的情况
                        parentId = Long.valueOf(null == aiEventLog.getParentEventId() ? aiEventLog.getEventId() : aiEventLog.getParentEventId());
                    }
                }
                if (Objects.isNull(parentId)) {
                    parentId = eventIdNow;
                }
                List<MessageContent> messageContents = this.getAiChatCompleteVoice(aiQueryDto, analyze, aiEventLog);
                if (CollectionUtils.isEmpty(messageContents)) {
                    SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                    SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
                    return ResponseEntity.ok(errorSseEmitter);
                }
                this.sendMsgStream(messageContents, sseEmitter, streamAnswerConsumer, extralInfo.parentId(parentId.toString()).build());
                return ResponseEntity.ok(sseEmitter);
            }
            SearchRes searchRes = new SearchRes();
            if (aiQueryDto.isNeedSearch()) {
                searchRes = functionCallingService.searchResult(userAsk, JSONObject.toJSONString(new MessageContent("user", userAsk)));
            }
            String sysPromptContent = (StringUtils.isNotBlank(searchRes.getSearchContent()) ? "\n #### 以下内容来自新的资讯 #### \n" + searchRes.getSearchContent() : "") +
                    (StringUtils.isNotBlank(searchRes.getSearchContent()) ? nlpProperties.getChatPromote() + ",\n需要结新的资讯进行分析\n" : nlpProperties.getChatPromote());
            String originContent = "";
            if (!CollectionUtils.isEmpty(aiEventLogsOld)) {
                originContent = aiEventLogsOld.get(0).getChatResponse();
            }
            List<MessageContent> messageContentsReq = new ArrayList<>();
            messageContentsReq.add(new MessageContent("system", originContent + sysPromptContent));
            List<AiEventLog> aiEventLogList = aiEventLogService.lambdaQuery().eq(AiEventLog::getChatId, aiQueryDto.getChatId()).orderByDesc(AiEventLog::getCreateTime).last(" limit 5 ").list();
            if (!CollectionUtils.isEmpty(aiEventLogList)) {
                Collections.reverse(aiEventLogList);
                for (AiEventLog aiEventLog : aiEventLogList) {
                    messageContentsReq.add(new MessageContent("user", aiEventLog.getChatQuestion()));
                    messageContentsReq.add(new MessageContent("assistant", aiEventLog.getChatResponse()));
                }
            }
            messageContentsReq.add(new MessageContent("user", userAsk));
            this.sendMsgStream(messageContentsReq, sseEmitter, streamAnswerConsumer, extralInfo.build());
            return ResponseEntity.ok(sseEmitter);
        } catch (Exception e) {
            log.error("请求失败", e);
        }
        SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
        SseEmitterUtils.sendMessage(ResponseConstant.ERROR_RESPONSE_MESSAGE, errorSseEmitter);
        return ResponseEntity.ok(errorSseEmitter);
    }


    /**
     * 生成PPT
     */
    public WebResult<String> generatorPPTFromAnalysisiOneTips(AiQuerySmartDto aiQueryDto) {
        if (StringUtils.isBlank(aiQueryDto.getParentEventId())) {
            return WebResult.error("500", "对话上下文缺失");
        }
        List<AiEventLog> aiEventLogList = aiEventLogService.lambdaQuery().eq(AiEventLog::getEventId, aiQueryDto.getParentEventId()).list();
        if (CollectionUtils.isEmpty(aiEventLogList)) {
            return WebResult.error("500", "对话上下文缺失");
        }
        SequenceIdRequest sequenceIdRequest = new SequenceIdRequest();
        sequenceIdRequest.setBusinessType(10);
        Long eventIdNow = sequenceService.generateId(sequenceIdRequest).getData();
        AiEventLog aiEventLogOrigin = aiEventLogList.get(0);
        AiEventLog aiEventLog = new AiEventLog();
        aiEventLog.setEventId(String.valueOf(eventIdNow));
        aiEventLog.setParentEventId(aiQueryDto.getParentEventId());
        aiEventLog.setEmployeeId(aiQueryDto.getLoginEmployeeId());
        aiEventLog.setEmployeeName(aiQueryDto.getLoginEmployeeName());
        aiEventLog.setCustId(aiEventLogOrigin.getCustId());
        aiEventLog.setPid(aiEventLogOrigin.getPid());
        aiEventLog.setCustName(aiEventLogOrigin.getCustName());
        aiEventLog.setChatId(aiEventLogOrigin.getChatId());
        aiEventLog.setPromptId(aiEventLogOrigin.getPromptId());
        aiEventLog.setPlatform(aiEventLogOrigin.getPlatform());
        aiEventLog.setQuestionSource(aiQueryDto.getQuestionSource());
        aiEventLog.setQuestionType(aiQueryDto.getQuestionType());
        aiEventLog.setOperateType(aiQueryDto.getOperateType());
        aiEventLog.setPptGenerateContent(aiEventLogOrigin.getChatResponse());
        aiEventLog.setPptStatus(0);
        String keyCount = stringRedisTemplate.opsForValue().get(prefixPPt + "keycount:" + aiQueryDto.getLoginEmployeeId());
        if (!StringUtils.isNotBlank(keyCount) || Integer.parseInt(keyCount) < 10) {
            aiEventLog.setPptGenerateResult("key次数不足");
            aiEventLog.setPptStatus(3);
            return WebResult.error("500", "key次数不足");
        }
        aiEventLogService.save(aiEventLog);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", aiEventLog.getId());
        log.info("发送到 mq信息:{}", JSONObject.toJSONString(jsonObject));
        rocketMqOperate.syncSend(SCRM_KEXF_MESSAGE_PPT_AI_NEW_NOTIFY_TOPIC, jsonObject);
        stringRedisTemplate.opsForValue().increment(prefixPPt + "keycount:" + aiQueryDto.getLoginEmployeeId(), -1);
        return WebResult.success("ok");
    }


    public JSONObject personAuth(LoginInfo aiQueryDto) {
        JSONObject jsonObjectRes = new JSONObject();
        boolean flag = false;
        try {
            String auth = nlpProperties.getAnalyzeAuth();
            JSONObject jsonObject = JSONObject.parseObject(auth);
            List<String> personIds = Arrays.asList(jsonObject.getString("personIds").split(","));
            List<String> areaIds = Arrays.asList(jsonObject.getString("areaIds").split(","));
            List<String> deptIds = Arrays.asList(jsonObject.getString("deptIds").split(","));
            List<String> buIds = Arrays.asList(jsonObject.getString("buIds").split(","));
            List<String> subIds = Arrays.asList(jsonObject.getString("subIds").split(","));
            if (personIds.contains(aiQueryDto.getLoginEmployeeId())
                    || areaIds.contains(aiQueryDto.getLoginAreaId())
                    || deptIds.contains(aiQueryDto.getLoginOrgId())
                    || buIds.contains(aiQueryDto.getLoginBuId())
                    || subIds.contains(aiQueryDto.getLoginSubId())
            ) {
                flag = true;
            }
        } catch (Exception e) {
            log.error("analyze 页面权限判断失败", e);
        }
        if (!flag) {
            // 如果是跨境区域 flag为false不展示
            List<String> kjAreaIds = Arrays.asList("3950", "4343");
            if (kjAreaIds.contains(aiQueryDto.getLoginAreaId())) {
                flag = false;
            } else {
                long count = aiAccessAuthService.lambdaQuery().eq(AiAccessAuth::getEmployeeId, aiQueryDto.getLoginEmployeeId()).count();
                flag = count > 0;
            }
        }
        jsonObjectRes.put("hasAuth", flag);
        return jsonObjectRes;
    }


    public JSONObject customsTradeRanking(AiCustomsProductWebDto customsProductWebDto) {
        List<Integer> yearnRange = new ArrayList<>();
        if (null == customsProductWebDto.getStartYear()) {
            yearnRange = getYearRange(2025, 2025);
        } else {
            yearnRange = getYearRange(customsProductWebDto.getStartYear(), customsProductWebDto.getEndYear());
        }
        List<CustomsTradeSummaryView> list = customsTradeSummaryBusiness.selectTop5ByProductCodes(customsProductWebDto.getProductCodes(), yearnRange);
        StringBuilder stringBuilder = new StringBuilder();
        String yearStr = "";
        if (yearnRange.size() == 1) {
            yearStr = String.valueOf(yearnRange.get(0));
        } else {
            yearStr = yearnRange.get(0) + "~" + yearnRange.get(yearnRange.size() - 1);
        }
        stringBuilder.append("## ").append(yearStr).append("年出口TOP5的国家和金额(美元)").append("\n");
        if (CollectionUtils.isEmpty(list)) {
            stringBuilder.append("### 暂无查到相关出口信息");
        } else {
            List<JSONObject> resultList = new ArrayList<>();
            for (CustomsTradeSummaryView view : list) {
                JSONObject resultJson = new JSONObject(new LinkedHashMap<>());
                resultJson.put("产品名称", view.getProductName());
                resultJson.put("出口国家", view.getPartnerName());
                resultJson.put("出口金额(美元)", view.getTotalSum());
                resultList.add(resultJson);
            }
            //String markDown = JsonToMarkdown.convert2TmarkDown(JSONObject.toJSONString(resultList));
            //stringBuilder.append(markDown);

            Map<String, List<CustomsTradeSummaryView>> customsTradeMap = list.stream().collect(Collectors.groupingBy(CustomsTradeSummaryView::getProductCode));
            customsTradeMap.forEach((key, value) -> {
                // 1. 提取公共前缀
                String productName = value.get(0).getProductName();
                stringBuilder.append("#### ")
                        .append(productName)
                        .append(" ：");  // 使用中文冒号
                // 2. 使用 StringJoiner 自动处理分隔符（避免末尾逗号）
                StringJoiner joiner = new StringJoiner("，"); // 中文逗号分隔
                for (CustomsTradeSummaryView view : value) {
                    joiner.add(view.getPartnerName() + "  " + convertToW(view.getTotalSum()));
                }
                // 3. 拼接结果并换行
                stringBuilder.append(joiner.toString())
                        .append("\n");
            });
        }
        List<AiEventLog> aiEventLogList = aiEventLogService.lambdaQuery().eq(AiEventLog::getEventId, customsProductWebDto.getParentEventId()).list();
        if (CollectionUtils.isEmpty(aiEventLogList)) {
            return new JSONObject();
        }
        AiEventLog aiEventLogOrigin = aiEventLogList.get(0);
        SequenceIdRequest sequenceIdRequest = new SequenceIdRequest();
        sequenceIdRequest.setBusinessType(10);
        Long eventIdNow = sequenceService.generateId(sequenceIdRequest).getData();
        AiEventLog aiEventLog = new AiEventLog();
        aiEventLog.setCustId(aiEventLogOrigin.getCustId());
        aiEventLog.setPid(aiEventLogOrigin.getPid());
        aiEventLog.setCustName(aiEventLogOrigin.getCustName());
        aiEventLog.setChatResponse(stringBuilder.toString());
        aiEventLog.setEventId(String.valueOf(eventIdNow));
        aiEventLog.setParentEventId(customsProductWebDto.getParentEventId());
        aiEventLog.setEmployeeId(customsProductWebDto.getLoginEmployeeId());
        aiEventLog.setEmployeeName(customsProductWebDto.getLoginEmployeeName());
        aiEventLog.setPlatform(customsProductWebDto.getPlatform());
        aiEventLog.setQuestionType(4);
        aiEventLog.setOperateType(5);
        aiEventLog.setQuestionSource(customsProductWebDto.getQuestionSource());
        aiEventLog.setChatQuestion(customsProductWebDto.getChatQuestion());
        if (StringUtils.isBlank(customsProductWebDto.getChatId())) {
            Long chatId = sequenceService.generateId(sequenceIdRequest).getData();
            aiEventLog.setChatId(chatId.toString());
        } else {
            aiEventLog.setChatId(customsProductWebDto.getChatId());
        }
        aiEventLogService.save(aiEventLog);
        JSONObject jsonObjectRes = new JSONObject();
        jsonObjectRes.put("result", stringBuilder.toString());
        jsonObjectRes.put("eventId", eventIdNow);
        jsonObjectRes.put("chatId", aiEventLog.getChatId());
        return jsonObjectRes;
    }


    /***
     * BigDecimal 转换成以W为单位的数值,保留整数
     * @param bigDecimal
     * <AUTHOR>
     * @date 2025/5/21 19:36
     * @version 1.0.0
     * @return java.lang.String
     **/
    private static final BigDecimal TEN_THOUSAND = new BigDecimal("10000");

    public static String convertToW(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return "0";
        }
        // 判断是否小于1万 [2](@ref)[3](@ref)
        if (bigDecimal.compareTo(TEN_THOUSAND) < 0) {
            // 小于1万时返回原值（自动去除末尾零）[2](@ref)[4](@ref)
            return bigDecimal.stripTrailingZeros().toPlainString() + "美元";
        } else {
            // 大于等于1万时转换为W单位（保留两位小数）[2](@ref)[7](@ref)
            BigDecimal valueInW = bigDecimal.divide(TEN_THOUSAND, 2, RoundingMode.HALF_UP);
            DecimalFormat df = new DecimalFormat("#"); // 自动省略末尾零[7](@ref)
            return df.format(valueInW) + "W";
        }
    }

    public static List<Integer> getYearRange(Integer startYear, Integer endYear) {
        if (startYear == null || endYear == null || startYear > endYear) {
            throw new IllegalArgumentException("起始年份和结束年份不能为空，且起始年份不能大于结束年份");
        }
        return IntStream.rangeClosed(startYear, endYear)
                .boxed().distinct()
                .collect(Collectors.toList());
    }


    public Page<AiVoiceAnalyze> getVoiceInfoPageList(VoiceAnalyzeWebDto voiceAnalyzeWebDto) {
        String position = voiceAnalyzeWebDto.getLoginPosition();
        if (EmpPositionConstant.BUSINESS_SALER.equals(position)
                || EmpPositionConstant.BUSINESS_SALER_OLD.equals(position)
                || EmpPositionConstant.GJ_DSGW.equals(position)) {
            voiceAnalyzeWebDto.setSalerId(voiceAnalyzeWebDto.getLoginEmployeeId());
        }
        if (EmpPositionConstant.BUSINESS_MANAGER.equals(position)) {
            voiceAnalyzeWebDto.setDeptId(voiceAnalyzeWebDto.getLoginOrgId());
        }
        if (EmpPositionConstant.BUSINESS_MAJOR.equals(position) || EmpPositionConstant.BUSINESS_PXS.equals(position)) {
            voiceAnalyzeWebDto.setSubId(voiceAnalyzeWebDto.getLoginSubId());
        }
        if (EmpPositionConstant.BUSINESS_AREA.equals(position)) {
            voiceAnalyzeWebDto.setAreaId(voiceAnalyzeWebDto.getLoginAreaId());
        }
        if (EmpPositionConstant.BUSINESS_MAJOR_BU.equals(position)) {
            voiceAnalyzeWebDto.setBuId(voiceAnalyzeWebDto.getLoginBuId());
        }
        Page<AiVoiceAnalyze> page = aiVoiceAnalyzeService.page(new Page<>(voiceAnalyzeWebDto.getCurrentPage(), voiceAnalyzeWebDto.getPageSize()),
                new QueryWrapper<AiVoiceAnalyze>().lambda()
                        .select(AiVoiceAnalyze::getId,
                                AiVoiceAnalyze::getEmployeeName,
                                AiVoiceAnalyze::getCustName,
                                AiVoiceAnalyze::getCustId,
                                AiVoiceAnalyze::getBuName,
                                AiVoiceAnalyze::getSubName,
                                AiVoiceAnalyze::getAreaName,
                                AiVoiceAnalyze::getOrgName,
                                AiVoiceAnalyze::getOriginUrl,
                                AiVoiceAnalyze::getMergeUrl,
                                AiVoiceAnalyze::getVoiceStatus,
                                AiVoiceAnalyze::getAnalyzeStatus,
                                AiVoiceAnalyze::getExtralInfo,
//                                AiVoiceAnalyze::getRespFormat,
//                                AiVoiceAnalyze::getChatResponse,
//                                AiVoiceAnalyze::getBusinessChatResponse,
                                AiVoiceAnalyze::getCreateTime,
                                AiVoiceAnalyze::getUpdateTime
                        )
                        .like(StringUtils.isNotBlank(voiceAnalyzeWebDto.getCustName()), AiVoiceAnalyze::getCustName, voiceAnalyzeWebDto.getCustName())
                        .eq(StringUtils.isNotBlank(voiceAnalyzeWebDto.getCustId()), AiVoiceAnalyze::getCustId, voiceAnalyzeWebDto.getCustId())
                        .eq(StringUtils.isNotBlank(voiceAnalyzeWebDto.getSalerId()), AiVoiceAnalyze::getEmployeeId, voiceAnalyzeWebDto.getSalerId())
                        .eq(StringUtils.isNotBlank(voiceAnalyzeWebDto.getSubId()), AiVoiceAnalyze::getSubId, voiceAnalyzeWebDto.getSubId())
                        .eq(StringUtils.isNotBlank(voiceAnalyzeWebDto.getBuId()), AiVoiceAnalyze::getBuId, voiceAnalyzeWebDto.getBuId())
                        .eq(StringUtils.isNotBlank(voiceAnalyzeWebDto.getDeptId()), AiVoiceAnalyze::getOrgId, voiceAnalyzeWebDto.getDeptId())
                        .eq(StringUtils.isNotBlank(voiceAnalyzeWebDto.getAreaId()), AiVoiceAnalyze::getAreaId, voiceAnalyzeWebDto.getAreaId())
                        .eq(null != voiceAnalyzeWebDto.getVoiceType(), AiVoiceAnalyze::getVoiceType, voiceAnalyzeWebDto.getVoiceType())
                        .eq(null != voiceAnalyzeWebDto.getVoiceStatus(), AiVoiceAnalyze::getVoiceStatus, voiceAnalyzeWebDto.getVoiceStatus())
                        .eq(null != voiceAnalyzeWebDto.getAnalyzeStatus(), AiVoiceAnalyze::getAnalyzeStatus, voiceAnalyzeWebDto.getAnalyzeStatus())
                        .eq(AiVoiceAnalyze::getAnalyzeType, 0)
                        .eq(AiVoiceAnalyze::getGroupType, 1)
                        .orderByDesc(AiVoiceAnalyze::getCreateTime)
        );
        return page;
    }


    @Transactional(rollbackFor = Exception.class)
    public JSONObject voiceReAnalyze(VoiceAnalyzeWebDto voiceAnalyzeWebDto) {
        AiVoiceAnalyze origin = aiVoiceAnalyzeService.getById(voiceAnalyzeWebDto.getId());
        if (null == origin) {
            return null;
        }
        if (Objects.equals(origin.getAnalyzeStatus(), 1) || Objects.equals(origin.getAnalyzeStatus(), 0)) {
            return null;
        }
        List<AiPromptInfo> aiPromptInfosNormalList = aiPromptInfoService.lambdaQuery()
                .eq(AiPromptInfo::getPromptType, 4)
                .eq(AiPromptInfo::getStartFlag, 1)
                .eq(AiPromptInfo::getDeleteFlag, 0).list();
        if (CollectionUtils.isEmpty(aiPromptInfosNormalList)) {
            log.error("未找到录音分析的提示语");
            return null;
        }
        AiVoiceAnalyze saveNew = new AiVoiceAnalyze();
        BeanUtils.copyProperties(origin, saveNew);
        saveNew.setId(null);
        saveNew.setEmployeeName(voiceAnalyzeWebDto.getLoginEmployeeName());
        saveNew.setEmployeeId(voiceAnalyzeWebDto.getLoginEmployeeId());
        saveNew.setBuId(voiceAnalyzeWebDto.getLoginBuId());
        saveNew.setBuName(voiceAnalyzeWebDto.getLoginBuName());
        saveNew.setOrgId(voiceAnalyzeWebDto.getLoginOrgId());
        saveNew.setOrgName(voiceAnalyzeWebDto.getLoginOrgName());
        saveNew.setSubId(voiceAnalyzeWebDto.getLoginSubId());
        saveNew.setSubName(voiceAnalyzeWebDto.getLoginSubName());
        saveNew.setAnalyzeStatus(0);
        saveNew.setVoiceType("0");
        saveNew.setAnalyzeType(1);
        saveNew.setChatResponse(null);
        saveNew.setUpdateTime(new Date());
        saveNew.setCreateTime(new Date());
        saveNew.setParentId(origin.getId());
        aiVoiceAnalyzeService.save(saveNew);
        aiVoiceAnalyzeService.lambdaUpdate()
                .eq(AiVoiceAnalyze::getId, origin.getId())
                .set(AiVoiceAnalyze::getAnalyzeStatus, 0)
                .update();
        rocketMqOperate.asyncSend(
                ServiceConstant.MqConstant.Topic.SCRM_VOICE_AI_NOTIFY_TOPIC,
                JSONObject.toJSONString(AiVoiceAnalyze.builder()
                        .id(saveNew.getId())
                        .parentId(saveNew.getParentId())
                        .build()));
        Integer retainCust = this.retainCust(voiceAnalyzeWebDto.getCustId());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("retainCust", retainCust);
        jsonObject.put("promptId", aiPromptInfosNormalList.get(0).getId());
        jsonObject.put("promptTitle", aiPromptInfosNormalList.get(0).getTitle());
        return jsonObject;
    }


    public AiVoiceAnalyze getVoiceInfoById(VoiceAnalyzeWebDto voiceAnalyzeWebDto) {
        AiVoiceAnalyze analyze = null;
        List<AiVoiceAnalyze> aiVoiceAnalyzeList = aiVoiceAnalyzeService.lambdaQuery()
                .select(
                        AiVoiceAnalyze::getCustId,
                        AiVoiceAnalyze::getCustName,
                        AiVoiceAnalyze::getExtralInfo,
                        AiVoiceAnalyze::getRespFormat,
                        AiVoiceAnalyze::getChatResponse,
                        AiVoiceAnalyze::getBusinessChatResponse
                )
                .eq(AiVoiceAnalyze::getAnalyzeType, 1)
                .eq(AiVoiceAnalyze::getParentId, voiceAnalyzeWebDto.getId())
                .orderByDesc(AiVoiceAnalyze::getUpdateTime)
                .last(" limit 1")
                .list();
        if (!CollectionUtils.isEmpty(aiVoiceAnalyzeList)) {
            analyze = aiVoiceAnalyzeList.get(0);
        } else {
            analyze = aiVoiceAnalyzeService.lambdaQuery()
                    .select(
                            AiVoiceAnalyze::getCustId,
                            AiVoiceAnalyze::getCustName,
                            AiVoiceAnalyze::getExtralInfo,
                            AiVoiceAnalyze::getRespFormat,
                            AiVoiceAnalyze::getChatResponse,
                            AiVoiceAnalyze::getBusinessChatResponse
                    )
                    .eq(AiVoiceAnalyze::getId, voiceAnalyzeWebDto.getId()).one();
        }
        return analyze;
    }

    @Transactional(rollbackFor = Exception.class)
    public JSONObject uploadFiles(VoiceUpload voiceAnalyzeWebDto) {
        // 数据会产生 n + 1条数据
        // n 是当前语音分析的子任务数 1 是主任务
        // 子任务只进行语音识别  主任务需要进行 文本分析和剩余任务的处理
        List<FileContext> fileList = voiceAnalyzeWebDto.getFileList();
        if (CollectionUtils.isEmpty(fileList)) {
            return null;
        }
        List<AiPromptInfo> aiPromptInfosNormalList = aiPromptInfoService.lambdaQuery()
                .eq(AiPromptInfo::getPromptType, 4)
                .eq(AiPromptInfo::getStartFlag, 1)
                .eq(AiPromptInfo::getDeleteFlag, 0).list();
        if (CollectionUtils.isEmpty(aiPromptInfosNormalList)) {
            log.error("未找到录音分析的提示语");
            return null;
        }
        SequenceIdRequest sequenceIdRequest = new SequenceIdRequest();
        sequenceIdRequest.setBusinessType(9);
        sequenceIdRequest.setCount(1);
        Long groupId = sequenceService.generateId(sequenceIdRequest).getData();
        CustomerDataThirdView customerDataThirdView = customerThirdService.getCustomerData(voiceAnalyzeWebDto.getCustId()).orElse(new CustomerDataThirdView());
        List<Long> aiVoiceAnalyzeList = new ArrayList<>();
        for (FileContext fileContext : fileList) {
            AiVoiceAnalyze analyzeNewItem = getAiVoiceAnalyze(voiceAnalyzeWebDto, groupId, customerDataThirdView);
            analyzeNewItem.setOriginUrl(fileContext.getFileUrl());
            analyzeNewItem.setFileSign(fileContext.getSignature());
            analyzeNewItem.setVoiceType("2");
            analyzeNewItem.setGroupType(0);
            analyzeNewItem.setGroupOrder(fileContext.getOrder());
            List<AiVoiceAnalyze> analyzeList = aiVoiceAnalyzeService.lambdaQuery().eq(AiVoiceAnalyze::getFileSign, fileContext.getSignature()).list();
            AiVoiceAnalyze analyze = !CollectionUtils.isEmpty(analyzeList) ? analyzeList.get(0) : null;
            if (null == analyze) {
                analyzeNewItem.setVoiceStatus(0);
                // 新增子
            } else {
                // 取旧信息,复制新增
                if (Objects.equals(analyze.getVoiceStatus(), 3)) {
                    analyzeNewItem.setVoiceStatus(0);
                } else {
                    analyzeNewItem.setVoiceStatus(analyze.getVoiceStatus());
                }
                analyzeNewItem.setReqInfo(analyze.getReqInfo());
                analyzeNewItem.setRespInfo(analyze.getRespInfo());
                analyzeNewItem.setRespFormat(analyze.getRespFormat());
            }
            aiVoiceAnalyzeService.save(analyzeNewItem);
            aiVoiceAnalyzeList.add(analyzeNewItem.getId());
        }
        // 新增主任务
        AiVoiceAnalyze saveNew = getAiVoiceAnalyze(voiceAnalyzeWebDto, groupId, customerDataThirdView);
        saveNew.setGroupType(1);
        saveNew.setVoiceType(voiceAnalyzeWebDto.getVoiceType());
        saveNew.setVoiceStatus(0);
        saveNew.setAnalyzeStatus(0);
        saveNew.setExtralInfo(JSONObject.toJSONString(voiceAnalyzeWebDto.getExtralInfo()));
        saveNew.setAnalyzeType(0);
        saveNew.setOriginUrl(fileList.stream().sorted(Comparator.comparing(FileContext::getOrder)).map(T -> T.getFileUrl()).collect(Collectors.joining(",")));
        aiVoiceAnalyzeService.save(saveNew);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("groupId", groupId);
        Integer retainCust = this.retainCust(voiceAnalyzeWebDto.getCustId());
        jsonObject.put("retainCust", retainCust);
        jsonObject.put("promptId", aiPromptInfosNormalList.get(0).getId());
        jsonObject.put("promptTitle", aiPromptInfosNormalList.get(0).getTitle());
        rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CRM_AUDIO_COMBINE_URL_TOPIC, JSONObject.toJSONString(AudioList.builder().id(saveNew.getId()).audioUrls(fileList.stream().map(FileContext::getFileUrl).collect(Collectors.toList())).build()));
        return jsonObject;
    }

    @NotNull
    private static AiVoiceAnalyze getAiVoiceAnalyze(VoiceUpload voiceAnalyzeWebDto, Long groupId, CustomerDataThirdView customerDataThirdView) {
        AiVoiceAnalyze analyzeNew = new AiVoiceAnalyze();
        analyzeNew.setGroupId(groupId);
        analyzeNew.setAnalyzeStatus(0);
        analyzeNew.setEmployeeId(voiceAnalyzeWebDto.getLoginEmployeeId());
        analyzeNew.setEmployeeName(voiceAnalyzeWebDto.getLoginEmployeeName());
        analyzeNew.setCustId(voiceAnalyzeWebDto.getCustId());
        analyzeNew.setCustName(customerDataThirdView.getCustomerName());
        analyzeNew.setSubId(voiceAnalyzeWebDto.getLoginSubId());
        analyzeNew.setSubName(voiceAnalyzeWebDto.getLoginSubName());
        analyzeNew.setBuId(voiceAnalyzeWebDto.getLoginBuId());
        analyzeNew.setBuName(voiceAnalyzeWebDto.getLoginBuName());
        analyzeNew.setOrgId(voiceAnalyzeWebDto.getLoginOrgId());
        analyzeNew.setAreaId(voiceAnalyzeWebDto.getLoginAreaId());
        analyzeNew.setAreaName(voiceAnalyzeWebDto.getLoginAreaName());
        analyzeNew.setOrgName(voiceAnalyzeWebDto.getLoginOrgName());
        analyzeNew.setPlatform(voiceAnalyzeWebDto.getPlatform());
        return analyzeNew;
    }


    public List<MessageContent> getAiChatCompleteVoice(AiQuerySmartDto aiQueryDto, AiVoiceAnalyze analyze, AiEventLog aiEventLog) {
        String[] userPromptList = handleChatResponse(analyze.getChatResponse());
        String userPrompt = "";
        String system = "";
        if (Objects.equals(aiQueryDto.getQuestionType(), 8)) {
            //8:新生成
            AiPromptInfo aiPromptInfo = new AiPromptInfo();
            List<AiPromptInfo> aiPromptInfosFlowList = aiPromptInfoService.lambdaQuery().eq(AiPromptInfo::getPromptType, 6).eq(AiPromptInfo::getStartFlag, 1).eq(AiPromptInfo::getDeleteFlag, 0).list();
            if (!CollectionUtils.isEmpty(aiPromptInfosFlowList)) {
                aiPromptInfo = aiPromptInfosFlowList.get(0);
            } else {
                return null;
            }
            // 查询原始模型
            AiPromptInfo aiPromptInfoOrigin = aiPromptInfoService.getById(aiQueryDto.getPromptId());
            if (null == aiPromptInfoOrigin) {
                return null;

            }
            system = aiPromptInfo.getContent() + "\n" + aiPromptInfoOrigin.getContent();
            // 查询用户json 数据
            String custInfo = this.getCustInfo(aiQueryDto);
            if (StringUtils.isBlank(custInfo)) {
                return null;
            }
            userPrompt = userPrompt + "以下为\"客户数据\":" + custInfo;
            if (userPromptList.length < 2) {
                log.info("语音分析结果错误无法区分细节和总结");
                userPrompt = userPrompt + "\n以下为谈单细节和谈单总结:\n" + analyze.getChatResponse();
            } else {
                userPrompt = userPrompt + "\n以下为\"谈单总结\":\n" + userPromptList[1]
                        + "\n以下为\"谈单细节\":" + userPromptList[0];
            }
            String end = "\n注意:\n" +
                    "你必须根据上面的“谈单总结\",\"谈单细节\"中的”客户痛点，价值呈现，异议解答，竞争分析，后续计划“等内容深入设1.\n" +
                    "计，并结合\"客户数据“后再输出方案。\n" +
                    "2.你必须在最后输出方案中哪些地方是根据\"谈单总结\"和\"谈单总结\"进行的定制设计。";
            userPrompt = userPrompt + end;
        }
        if (Objects.equals(aiQueryDto.getQuestionType(), 9)) {
            //9:老方案修改
            AiPromptInfo aiPromptInfo = new AiPromptInfo();
            if (null == aiEventLog) {
                return null;
            }
            List<AiPromptInfo> aiPromptInfosFlowList = aiPromptInfoService.lambdaQuery().eq(AiPromptInfo::getPromptType, 5).eq(AiPromptInfo::getStartFlag, 1).eq(AiPromptInfo::getDeleteFlag, 0).list();
            if (!CollectionUtils.isEmpty(aiPromptInfosFlowList)) {
                aiPromptInfo = aiPromptInfosFlowList.get(0);
            } else {
                return null;
            }
            userPrompt = userPrompt + "以下为\"谈单方案\":" + aiEventLog.getChatResponse();
            log.info("之前生成过方案:{}", JSONObject.toJSONString(aiEventLog));
            if (userPromptList.length < 2) {
                log.info("语音分析结果错误无法区分细节和总结");
                userPrompt = userPrompt + "\n以下为谈单细节和谈单总结:\n" + analyze.getChatResponse();
            } else {
                userPrompt = userPrompt + "\n以下为\"谈单总结\":\n" + userPromptList[1]
                        + "\n以下为\"谈单细节\":" + userPromptList[0];
            }
            system = aiPromptInfo.getContent();
        }
        List<MessageContent> messageContents = new ArrayList<>();
        messageContents.add(new MessageContent("system", system));
        messageContents.add(new MessageContent("user", userPrompt));
        return messageContents;
    }


    public static String[] handleChatResponse(String chatResponse) {
        if (chatResponse == null || chatResponse.isEmpty()) {
            return new String[0];
        }
        // 查找两个关键词的起始位置
        int detailIndex = chatResponse.indexOf("谈单细节");
        int summaryIndex = chatResponse.indexOf("谈单总结");
        // 情况1：两个关键词都存在，且"谈单细节"在"谈单总结"之前
        if (detailIndex != -1 && summaryIndex != -1 && detailIndex < summaryIndex) {
            // 以"谈单总结"为分割点，分成两部分
            String[] parts = chatResponse.split("(?=谈单总结)", 2);
            return parts.length == 2 ? parts : new String[]{chatResponse};
        }
        // 情况2：两个关键词都存在，但"谈单总结"在"谈单细节"之前（异常顺序）
        else if (detailIndex != -1 && summaryIndex != -1 && summaryIndex < detailIndex) {
            // 强制按"谈单细节"和"谈单总结"的逻辑顺序返回
            String summaryPart = chatResponse.substring(summaryIndex);
            String detailPart = chatResponse.substring(0, summaryIndex);
            // 再从detailPart中提取真正的"谈单细节"部分（如果需要更严格的分割）
            int realDetailIndex = detailPart.indexOf("谈单细节");
            if (realDetailIndex != -1) {
                detailPart = detailPart.substring(realDetailIndex);
            }
            return new String[]{detailPart, summaryPart};
        }
        // 情况3：只存在一个关键词或都不存在
        else {
            return new String[]{chatResponse};
        }
    }

    public AiEventLog getAiEventLogOld(AiQuerySmartDto aiQueryDto) {
        List<AiEventLog> aiEventLogs = aiEventLogService.lambdaQuery()
                .eq(AiEventLog::getEmployeeId, aiQueryDto.getLoginEmployeeId())
                .eq(AiEventLog::getCustId, aiQueryDto.getCustId())
                .eq(AiEventLog::getPromptId, aiQueryDto.getPromptId())
                .in(AiEventLog::getQuestionType, Arrays.asList(3, 6, 8, 9))
                .orderByDesc(AiEventLog::getCreateTime)
                .last(" limit 1").list();
        if (CollectionUtils.isEmpty(aiEventLogs)) {
            log.info("*************未找到event_log:{}*************", JSONObject.toJSONString(aiEventLogs));
            return null;
        } else {
            log.info("*************找到event_log*************");
            return aiEventLogs.get(0);
        }
    }

    /***
     * 最近一次
     * 老方案修改
     * @param aiQueryDto
     * <AUTHOR>
     * @date 2025/7/18 16:24
     * @version 1.0.0
     * @return com.ce.scrm.center.dao.entity.AiEventLog
     **/
    public AiEventLog getAiEventLogForQuestionTypeNine(AiQuerySmartDto aiQueryDto) {
        List<AiEventLog> aiEventLogs = aiEventLogService.lambdaQuery()
                .eq(AiEventLog::getEmployeeId, aiQueryDto.getLoginEmployeeId())
                .eq(AiEventLog::getCustId, aiQueryDto.getCustId())
                .eq(AiEventLog::getPromptId, aiQueryDto.getPromptId())
                .eq(AiEventLog::getVoiceId, aiQueryDto.getVoiceId())
                .eq(AiEventLog::getQuestionType, 9)
                .orderByDesc(AiEventLog::getCreateTime)
                .last(" limit 1").list();
        if (CollectionUtils.isEmpty(aiEventLogs)) {
            log.info("*************未找到event_log:{}*************", JSONObject.toJSONString(aiEventLogs));
            return null;
        } else {
            log.info("*************找到event_log*************");
            return aiEventLogs.get(0);
        }
    }

    public List<AiPromptInfo> getAllPromptsAnyUsed(AiQuerySmartDto aiQueryDto) {
        List<AiEventLog> aiEventLogs = aiEventLogService.lambdaQuery()
                .select(AiEventLog::getPromptId)
                .eq(AiEventLog::getEmployeeId, aiQueryDto.getLoginEmployeeId())
                .eq(AiEventLog::getCustId, aiQueryDto.getCustId())
                .in(AiEventLog::getQuestionType, Arrays.asList(3, 6, 8, 9))
                .isNotNull(AiEventLog::getPromptId)
                .orderByDesc(AiEventLog::getCreateTime)
                .list();
        if (CollectionUtils.isEmpty(aiEventLogs)) {
            return new ArrayList<>();
        }
        List<AiPromptInfo> aiPromptInfoPage = aiPromptInfoService.lambdaQuery().select(AiPromptInfo::getId, AiPromptInfo::getTitle, AiPromptInfo::getCreateTime)
                .eq(AiPromptInfo::getDeleteFlag, YesOrNoEnum.NO.getCode())
                .eq(AiPromptInfo::getStartFlag, 1)
                .in(AiPromptInfo::getId, aiEventLogs.stream().map(AiEventLog::getPromptId).distinct().collect(Collectors.toList()))
                .orderByDesc(AiPromptInfo::getCreateTime)
                .list();
        if (CollectionUtils.isEmpty(aiPromptInfoPage)) {
            return new ArrayList<>();
        } else {
            return aiPromptInfoPage;
        }
    }


    public String getCustInfo(AiQuerySmartDto aiQueryDto) {
        log.info("请求信息:{}", JSONObject.toJSONString(aiQueryDto));
        String customerName = "";
        if (org.springframework.util.StringUtils.hasText(aiQueryDto.getCustId()) && org.springframework.util.StringUtils.isEmpty(aiQueryDto.getPid())) {
            Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(aiQueryDto.getCustId());
            if (customerData.isPresent()) {
                CustomerDataThirdView customerDataThirdView = customerData.get();
                aiQueryDto.setPid(customerDataThirdView.getSourceDataId());
                customerName = customerDataThirdView.getCustomerName();
            }
        }
        String content = "";
        try {
            if (!org.springframework.util.StringUtils.isEmpty(aiQueryDto.getPid())) {
                content = stringRedisTemplate.opsForValue().get(prefix + aiQueryDto.getPid());
                if (org.springframework.util.StringUtils.isEmpty(content)) {
                    AiBusinessDto businessDto = BeanUtil.copyProperties(aiQueryDto, AiBusinessDto.class);
                    JSONObject jsonObjectResult = aiCustCacheBusiness.buildJsonData(businessDto);
                    content = JSONObject.toJSONString(jsonObjectResult);
                }
            } else {
                content = stringRedisTemplate.opsForValue().get(prefix + aiQueryDto.getCustId());
                if (org.springframework.util.StringUtils.isEmpty(content)) {
                    AiBusinessDto businessDto = BeanUtil.copyProperties(aiQueryDto, AiBusinessDto.class);
                    JSONObject jsonObjectResult = aiCustCacheBusiness.buildJsonData(businessDto);
                    content = JSONObject.toJSONString(jsonObjectResult);
                }
            }
            JSONObject jsonObject = JSONObject.parseObject(content, Feature.OrderedField);
            if (org.springframework.util.StringUtils.hasText(customerName)) {
                jsonObject.put("企业名称", customerName);
            }
            return JsonToMarkdown.convert2TmarkDown(JSONObject.toJSONString(jsonObject));
        } catch (Exception e) {
            log.error("请求失败", e);
            return null;
        }
    }


    public Integer retainCust(String custId) {
        Integer retainCust = 0;
        if (StringUtils.isNotBlank(custId)) {
            Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(custId);
            if (customerData.isPresent()) {
                CustomerDataThirdView customerDataThirdView = customerData.get();
                //埋点使用 是否是保有客户
                if (Objects.nonNull(customerDataThirdView.getTagRetainCust())) {
                    retainCust = customerDataThirdView.getTagRetainCust();
                }
            }
        }
        return retainCust;
    }


    public Page<AiEventLog> getPageEventLog(AiEventLogDto aiEventLogDto) {
        Page<AiEventLog> aiEventLogPage = aiEventLogService.lambdaQuery().select(
                        AiEventLog::getEventId,
                        AiEventLog::getParentEventId,
                        AiEventLog::getEmployeeId,
                        AiEventLog::getEmployeeName,
                        AiEventLog::getCustId,
                        AiEventLog::getPid,
                        AiEventLog::getCustName,
                        AiEventLog::getPromptId,
                        AiEventLog::getQuestionType,
                        AiEventLog::getExtraPromptId,
                        AiEventLog::getPlatform,
                        AiEventLog::getChatResponse,
                        AiEventLog::getChatQuestion,
                        AiEventLog::getCreateTime
                ).eq(AiEventLog::getQuestionType, aiEventLogDto.getQuestionType())
                .like(StringUtils.isNotBlank(aiEventLogDto.getChatQuestion()), AiEventLog::getChatQuestion, aiEventLogDto.getChatQuestion())
                .orderByDesc(AiEventLog::getCreateTime)
                .ge(null != aiEventLogDto.getStartTime(), AiEventLog::getCreateTime, aiEventLogDto.getStartTime())
                .le(null != aiEventLogDto.getEndTime(), AiEventLog::getCreateTime, aiEventLogDto.getEndTime())
                .page(Page.of(aiEventLogDto.getPageNum(), aiEventLogDto.getPageSize()));
        List<AiEventLog> aiEventLogList = aiEventLogPage.getRecords().stream().peek(T -> {
            if (Objects.equals(T.getQuestionType(), 5)) {
                if (org.springframework.util.StringUtils.hasText(T.getChatResponse())) {
                    String url = T.getChatResponse().replace("![image](", "").replace(")", "");
                    T.setChatResponse(url);
                }
            }
        }).collect(Collectors.toList());
        aiEventLogPage.setRecords(aiEventLogList);
        return aiEventLogPage;
    }

}
