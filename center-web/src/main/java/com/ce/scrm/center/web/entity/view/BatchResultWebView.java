package com.ce.scrm.center.web.entity.view;

import lombok.Data;

/**
 * @version 1.0
 * @Description: 批量返回
 * @Author: lijinpeng
 * @Date: 2025/1/2 15:16
 */
@Data
public class BatchResultWebView {

    private Integer successCount;

    private Integer failCount;

    private String cause = "操作完成";

    public Integer getAllCount() {
        return successCount+failCount;
    }

    public String getMessage() {
        return cause+"，成功"+successCount+"条，失败"+failCount+"条";
    }

}
