package com.ce.scrm.center.web.controller.abm;

import cn.ce.cesupport.enums.abm.CustomerMarketingActivityExecuteStateEnum;
import cn.ce.cesupport.enums.abm.CustomerMarketingActivityTypeEnum;
import cn.ce.cesupport.enums.abm.CustomerMarketingActivityTypePhoneEnum;
import cn.ce.cesupport.framework.base.vo.EmployeeVO;
import cn.ce.cesupport.sma.dto.ClueToMyCustDto;
import cn.ce.cesupport.sma.service.SalerRoleAppService;
import com.alibaba.fastjson.JSON;
import com.ce.cdp.dubbo.api.SensorsDBDubbo;
import com.ce.scrm.center.dao.entity.query.PotentialWillingBehaviorSelectedVo;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.abm.CustomerLeadsBusiness;
import com.ce.scrm.center.service.business.abm.CustomerMarketingActivityBusiness;
import com.ce.scrm.center.service.business.abm.PotentialCustomerRulesConfigBusiness;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerLeadsImportOrDistributeDto;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerMarketingActivityAssigneeSelectBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerMarketingActivityCreatorSelectBusinessDto;
import com.ce.scrm.center.service.enums.CdpDatabaseEnum;
import com.ce.scrm.center.service.enums.ConvertRelationEnum;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerDistributeSdrRecordThirdDto;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerMarketingActivityCreateThirdDto;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerMarketingActivityQueryThirdDto;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerMarketingActivityUpdateThirdDto;
import com.ce.scrm.center.service.third.invoke.CustomerMarketingActivityThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.abm.*;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.abm.*;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerMarketingActivitiesDubboView;
import com.google.common.collect.Lists;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

/**
 * 跨境ABM营销活动控制器
 * <AUTHOR>
 * @date 2025-07-11
 * @version 1.0.0
 */
@Login
@Slf4j
@RestController
@RequestMapping("/abm/customer/activity")
public class AbmMarketingActivityController {
	@Resource
	private CustomerMarketingActivityThirdService customerMarketingActivityThirdService;
	@Resource
	private CustomerMarketingActivityBusiness customerMarketingActivityBusiness;
	@DubboReference(group = "scrm-cdp-api", version = "1.0.0", check = false)
	private SensorsDBDubbo sensorsDBDubbo;
	/**
	 * 项目ID
	 */
	@Value("${sensors.projectId}")
	private String projectId;
	@Autowired
	private CustomerLeadsBusiness customerLeadsBusiness;
	@DubboReference
	private SalerRoleAppService salerRoleAppService;
	@Resource
	private EmployeeInfoBusiness employeeInfoBusiness;
	@Resource
	private PotentialCustomerRulesConfigBusiness potentialCustomerRulesConfigBusiness;

	/**
	 * 创建营销活动 | 修改营销活动
	 * @params webCreateReq 创建营销活动参数
	 * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo < com.ce.scrm.center.web.entity.view.CustomerLossWebView>>
	 * @date: 2025/7/11
	 */
	@PostMapping("/createOrUpdate")
	public WebResult<?> createOrUpdate(@Valid @RequestBody CustomerMarketingActivityCreateWebDto webCreateReq) {
		log.info("AbmMarketingActivityController#createOrUpdate request params = {}", JSON.toJSONString(webCreateReq));
		String empId = webCreateReq.getLoginEmployeeId();
		String empName = webCreateReq.getLoginEmployeeName();
		boolean isUpdate = Objects.nonNull(webCreateReq.getId());
		if (webCreateReq.getExecuteTime().before(new Date())) {
			if (isUpdate) {
				return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "活动执行时间已过,不允许修改");
			}
			return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "执行时间不能在当前时间之前");
		}
		Date now = new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(now);
		calendar.add(Calendar.MONTH, 3);
		Date threeMonthsLater = calendar.getTime();
		if (webCreateReq.getExecuteTime().after(threeMonthsLater)) {
			return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "执行时间不能超过未来三个月");
		}
		List<CustomerDistributeSdrRecordThirdDto> distributeSdrRecordThirdDtos = new ArrayList<>();
		// 电话触达方式的分配方式(创建时)
		if (Objects.equals(webCreateReq.getActivityType(), CustomerMarketingActivityTypeEnum.PHONE.getCode()) && Objects.isNull(webCreateReq.getId())) {
			List<String> customerIds = new ArrayList<>();
			if (Objects.equals(CustomerMarketingActivityTypePhoneEnum.AVERAGE.getCode(), webCreateReq.getPhoneDistributionWay())) { // 均分
				if (CollectionUtils.isEmpty(webCreateReq.getAssigneeIds())) {
					return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "电话触达，分配方式为均分时，分配角色不能为空");
				}
				distributeToSDR(webCreateReq.getAssigneeIds(), null, customerIds, distributeSdrRecordThirdDtos);
			} else if (Objects.equals(CustomerMarketingActivityTypePhoneEnum.DISTRIBUTION_BY_SPECIFIED_QUANTITY.getCode(), webCreateReq.getPhoneDistributionWay())) { // 按指定数量分配
				if (CollectionUtils.isEmpty(webCreateReq.getSdrIdNumMap())) {
					return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "电话触达，分配方式为按指定数量分配时，分配角色和数量不能为空");
				}
				Map<String, Integer> sdrIdMap = webCreateReq.getSdrIdNumMap();
				int distributeCustNum = sdrIdMap.values().stream().reduce(0, Integer::sum);
				String sql = "SELECT COUNT(1) AS customerNum FROM " + CdpDatabaseEnum.getEnumByProjectId(projectId).getDatabase() + ".user_group_" + webCreateReq.getSegmentId();
				int segmentCustCountFromCDP;
				try {
					customerIds = customerMarketingActivityBusiness.getCountBySegmentCode(sql);
					segmentCustCountFromCDP = customerIds.size();
				} catch (ApiException e) {
					log.error("获取CDP客群人数出错,客群id={} 异常信息={}", webCreateReq.getSegmentId(), e.getMessage());
					return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION.getCode(), "获取客群信息出错，请检查客群是否存在");
				}
				if (distributeCustNum != segmentCustCountFromCDP) {
					log.info("指定分配与CDP客群总人数不匹配, CDP客群总人数={}, 指定分发客户总数量={}", segmentCustCountFromCDP, distributeCustNum);
					return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION.getCode(), "分配客群信息异常，指定分配与CDP客群总人数不匹配");
				}

				distributeToSDR(webCreateReq.getAssigneeIds(), sdrIdMap, customerIds, distributeSdrRecordThirdDtos);
			} else {
				return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "电话触达，不支持的分配方式!");
			}
		}
		List<Integer> types = Lists.newArrayList(CustomerMarketingActivityTypeEnum.EMAIL.getCode(), CustomerMarketingActivityTypeEnum.SMS.getCode());
		if (StringUtils.isBlank(webCreateReq.getActivityUrl()) && types.contains(webCreateReq.getActivityType())) {
			return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "邮件/短信触达，活动链接不能为空");
		}

		if (isUpdate) {
			CustomerMarketingActivitiesDubboView activityDeatil = customerMarketingActivityThirdService.getCustomerActivitiesDetail(webCreateReq.getId());
			if (Objects.isNull(activityDeatil)) {
				return WebResult.error(WebCodeMessageEnum.DATA_NOT_EXIST);
			}
			List<Integer> canUpdateExecuteState = Arrays.asList(CustomerMarketingActivityExecuteStateEnum.WAIT_EXECUTE.getCode(), CustomerMarketingActivityExecuteStateEnum.EXECUTE_FAILED.getCode());
			if (!canUpdateExecuteState.contains(activityDeatil.getExecuteState())) {
				return WebResult.error(WebCodeMessageEnum.REQUEST_POSITION_NOT_MATCH, "仅待执行、执行失败的活动可编辑");
			}
			if (Objects.equals(activityDeatil.getActivityType(), CustomerMarketingActivityTypeEnum.PHONE.getCode())) { // 电话下发SDR不能改别的触达方式，因为在创建活动的时候就已经下发SDR了
				return WebResult.error(WebCodeMessageEnum.REQUEST_POSITION_NOT_MATCH, "电话触达活动不允许修改");
			}
			if (!Objects.equals(webCreateReq.getSegmentId(), activityDeatil.getSegmentId())
				|| !Objects.equals(webCreateReq.getSegmentCustCount(), activityDeatil.getSegmentCustCount())
				|| !Objects.equals(webCreateReq.getSegmentName(), activityDeatil.getSegmentName())
				|| !Objects.equals(webCreateReq.getActivityName(), activityDeatil.getActivityName())
			) {
				log.error("营销活动，除营销内容外不允许修改, 修改参数:{}，原活动信息:{}", JSON.toJSONString(webCreateReq), JSON.toJSONString(activityDeatil));
				return WebResult.error(WebCodeMessageEnum.REQUEST_POSITION_NOT_MATCH, "除营销内容外不允许修改");
			}
			CustomerMarketingActivityUpdateThirdDto updateThirdDto = BeanCopyUtils.convertToVo(webCreateReq, CustomerMarketingActivityUpdateThirdDto.class);
			updateThirdDto.setUpdateBy(empId);
			try {
				customerMarketingActivityThirdService.updateActivity(updateThirdDto);
			} catch (Exception ex) {
				return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION, ex.getMessage());
			}
		} else {
			CustomerMarketingActivityCreateThirdDto createThirdDto = BeanCopyUtils.convertToVo(webCreateReq, CustomerMarketingActivityCreateThirdDto.class);
			createThirdDto.setCreateBy(empId);
			createThirdDto.setCreateName(empName);
			createThirdDto.setDistributeSdrRecordThirdDtos(distributeSdrRecordThirdDtos);
			try {
				customerMarketingActivityThirdService.createActivity(createThirdDto);
			} catch (Exception ex) {
				return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION, ex.getMessage());
			}
		}
		return WebResult.success();
	}

	private void distributeToSDR(List<String> assigneeIds, Map<String, Integer> sdrIdMap, List<String> customerIds, List<CustomerDistributeSdrRecordThirdDto> distributeSdrRecordThirdDtos) {
		// 组装建立保护关系的结果
		Map<String, List<String>> sdrCustomerMap = assembleDistributeCustomerData(sdrIdMap, customerIds, assigneeIds);
		sdrCustomerMap.forEach((sdrId, custIds) -> {
			String distributeEmpName = "";
			for (String custId : custIds) {
				ClueToMyCustDto clueToMyCustDto = new ClueToMyCustDto();
				clueToMyCustDto.setCustId(custId);
				EmployeeInfoBusinessDto employeeVO = employeeInfoBusiness.getEmployeeInfoByEmpId(sdrId);
				distributeEmpName = employeeVO.getName();
				clueToMyCustDto.setEmployee(BeanCopyUtils.convertToVo(employeeVO, EmployeeVO.class));
				clueToMyCustDto.setConvertType(ConvertRelationEnum.ABM_ASSIGN_SALER.getValue());
				try {
					salerRoleAppService.clueToMyCustObject(clueToMyCustDto);
					CustomerDistributeSdrRecordThirdDto sdrRecordThirdDto = new CustomerDistributeSdrRecordThirdDto();
					sdrRecordThirdDto.setEmpId(sdrId);
					sdrRecordThirdDto.setCustomerId(custId);
					sdrRecordThirdDto.setEmpName(distributeEmpName);
					sdrRecordThirdDto.setPositionType(1);
					distributeSdrRecordThirdDtos.add(sdrRecordThirdDto);
				} catch (Exception e) {
					log.error("客户转保护失败，custId={}, sdrId={}, errorMsg={}", custId, sdrId, e.getMessage());
				}
			}
		});
	}


	/**
	 * 把customerIds这些客户id，按照sdrIdMap的value的个数分给对应的sdrId，sdrId就是sdrIdMap的key
	 * @param sdrIdMap 前端出来的，按照给指定sdr_id（key），分配指定个数的客户数量
	 * @param customerIds 客户id列表
	 * @param assigneeIds 均分时的sdrId，把customerId均分给这些sdrId
	 * @return key: sdrId ;  value: 客户id列表
	 */
	private Map<String, List<String>> assembleDistributeCustomerData(Map<String, Integer> sdrIdMap, List<String> customerIds, List<String> assigneeIds) {
		Map<String, List<String>> sdrCustomerMap = new HashMap<>();
		int startIndex = 0;
		if (CollectionUtils.isEmpty(customerIds)) {
			return sdrCustomerMap;
		}
		if (CollectionUtils.isEmpty(assigneeIds) && !CollectionUtils.isEmpty(sdrIdMap)) { // 按指定数量分
			for (Map.Entry<String, Integer> entry : sdrIdMap.entrySet()) {
				String sdrId = entry.getKey();
				Integer count = entry.getValue();

				if (startIndex >= customerIds.size()) {
					break; // 客户ID不够分配了
				}

				int endIndex = Math.min(startIndex + count, customerIds.size());
				List<String> assignedCustomers = customerIds.subList(startIndex, endIndex);

				sdrCustomerMap.put(sdrId, assignedCustomers);
				startIndex = endIndex;
			}
		}
		if (!CollectionUtils.isEmpty(assigneeIds) && CollectionUtils.isEmpty(sdrIdMap)) { // 均分
			// 轮询均匀分配：按顺序轮流分配客户给SDR
			int totalCustomers = customerIds.size();
			int totalAssignees = assigneeIds.size();

			// 初始化每个SDR的客户列表
			Map<String, List<String>> tempMap = new HashMap<>();
			for (String sdrId : assigneeIds) {
				tempMap.put(sdrId, new ArrayList<>());
			}
			for (int i = 0; i < totalCustomers; i++) {
				String customerId = customerIds.get(i);
				String sdrId = assigneeIds.get(i % totalAssignees); // 轮询分配
				tempMap.get(sdrId).add(customerId);
			}
			sdrCustomerMap = tempMap;
		}
		log.info("给SDR保护的客户列表，customerIds={}, sdrCustomerMap={}", JSON.toJSONString(customerIds), JSON.toJSONString(sdrCustomerMap));
		return sdrCustomerMap;

	}


	/**
	 * 获取营销活动列表
	 * @params webPageDto 查询分页和筛选参数
	 * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo < com.ce.scrm.center.web.entity.view.CustomerLossWebView>>
	 * @date: 2025/7/11
	 */
	@PostMapping("/page")
	public WebResult<WebPageInfo<CustomerMarketingActivityWebView>> page(@Valid @RequestBody CustomerMarketingActivityWebPageDto webReq) {
		CustomerMarketingActivityQueryThirdDto thirdQueryDto = BeanCopyUtils.convertToVo(webReq, CustomerMarketingActivityQueryThirdDto.class);
		DubboPageInfo<CustomerMarketingActivitiesDubboView> customerActivitiesPage = customerMarketingActivityThirdService.getCustomerActivitiesPage(thirdQueryDto);
		WebPageInfo<CustomerMarketingActivityWebView> webPageInfo = new WebPageInfo<>();
		if (customerActivitiesPage == null || CollectionUtils.isEmpty(customerActivitiesPage.getList())) {
			return WebResult.success(webPageInfo);
		}
		webPageInfo.setPages(customerActivitiesPage.getPages());
		webPageInfo.setTotal(customerActivitiesPage.getTotal());
		webPageInfo.setPageNum(customerActivitiesPage.getPageNum());
		webPageInfo.setPageSize(customerActivitiesPage.getPageSize());
		webPageInfo.setList(BeanCopyUtils.convertToVoList(customerActivitiesPage.getList(), CustomerMarketingActivityWebView.class));
		return WebResult.success(webPageInfo);
	}

	/**
	 * 获取营销活动详情
	 * @param webReq 活动id
	 * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.view.CustomerMarketingActivityWebView>
	 * @date: 2025/7/11
	 */
	@PostMapping("/detail")
	public WebResult<CustomerMarketingActivityWebView> detail(@Valid @RequestBody CustomerMarketingActivityDetailWebDto webReq) {
		CustomerMarketingActivitiesDubboView customerActivitiesDetail = customerMarketingActivityThirdService.getCustomerActivitiesDetail(webReq.getActivityId());
		if (Objects.isNull(customerActivitiesDetail)) {
			return WebResult.error(WebCodeMessageEnum.DATA_NOT_EXIST);
		}
		CustomerMarketingActivityWebView rs = BeanCopyUtils.convertToVo(customerActivitiesDetail, CustomerMarketingActivityWebView.class);
		if (Objects.equals(rs.getActivityType(), CustomerMarketingActivityTypeEnum.PHONE.getCode())) {
			rs.setSdrList(BeanCopyUtils.convertToVoList(customerActivitiesDetail.getDistributeSDRList(), CustomerMarketingActivityDistributeSDRWebView.class));
		}
		return WebResult.success(rs);
	}



	/**
	 * 营销活动列表，创建人下拉选择框
	 * @param webReq
	 * @return
	 */
	@Deprecated // 有老接口支持
	@PostMapping("/creatorSelect")
	public WebResult<List<CustomerMarketingActivityCreatorSelectWebView>> creatorSelect() {
		List<CustomerMarketingActivityCreatorSelectBusinessDto> creators = customerMarketingActivityBusiness.creatorSelect();
		if (CollectionUtils.isEmpty(creators)) {
			return WebResult.success(Collections.emptyList());
		}
		return WebResult.success(BeanCopyUtils.convertToVoList(creators, CustomerMarketingActivityCreatorSelectWebView.class));
	}

	/**
	 * SDR电话触达方式，对应分配人员的下拉选择框
	 * @return SDR人员列表
	 */
	@PostMapping("/assigneeSelect")
	public WebResult<List<CustomerMarketingActivityAssigneeSelectWebView>> assigneeSelect() {
		List<CustomerMarketingActivityAssigneeSelectBusinessDto> assignees = customerMarketingActivityBusiness.assigneeSelect();
		if (CollectionUtils.isEmpty(assignees)) {
			return WebResult.success(Collections.emptyList());
		}
		return WebResult.success(BeanCopyUtils.convertToVoList(assignees, CustomerMarketingActivityAssigneeSelectWebView.class));
	}

	/**
	 * 意愿行为的下拉筛选框
	 * @return key和value
	 */
	@PostMapping("/getWillingBehaviorSelected")
	public WebResult<List<PotentialCustomerConfigSelectWebView>> getWillingBehaviorSelected() {
		List<PotentialWillingBehaviorSelectedVo> willingBehaviorSelected = potentialCustomerRulesConfigBusiness.getWillingBehaviorSelected();
		if (CollectionUtils.isEmpty(willingBehaviorSelected)) {
			return WebResult.success(Collections.emptyList());
		}
		return WebResult.success(BeanCopyUtils.convertToVoList(willingBehaviorSelected, PotentialCustomerConfigSelectWebView.class));
	}

	/**
	 * 根据CDP分群code获取分群人数
	 * @return 人数
	 */
	@PostMapping("/segmentCount")
	public WebResult<Integer> getSegmentInfo(@Valid @RequestBody CustomerMarketingActivitySegmentCodeWebDto webDto) {
		String sql = "SELECT COUNT(1) AS customerNum FROM " + CdpDatabaseEnum.getEnumByProjectId(projectId).getDatabase() + ".user_group_" + webDto.getSegmentCode();
		log.info("获取CDP客群人数，getCountBySegmentCode,webDto={}, sql: {}", webDto.getSegmentCode(), sql);
		int countBySegmentCode;
		try {
			countBySegmentCode = customerMarketingActivityBusiness.getCountBySegmentCode(sql).size();
		} catch (ApiException e) {
			log.error("获取CDP客群人数出错, 异常信息={}", e.getMessage());
			// 前端约定返回0，前端会提示客群不存在，置灰
			return WebResult.success(0);
		}
		return WebResult.success(countBySegmentCode);
	}

	/**
	 * 营销效果回收
	 * @param webReq 线索列表
	 * @return 是否执行成功
	 */
	@PostMapping("/effectRecovery")
	public WebResult<Boolean> effectRecovery(@Valid @RequestBody CustomerMarketingActivityEffectRecoveryWebDto webReq) {
		CustomerLeadsImportOrDistributeDto customerLeadsImportOrDistributeDto = new CustomerLeadsImportOrDistributeDto();
		customerLeadsImportOrDistributeDto.setLeadsImportFrom(3);
		customerLeadsImportOrDistributeDto.setActivityClueInfos(webReq.getClueInfoList());
		return WebResult.success(customerLeadsBusiness.customerLeadsHandle(customerLeadsImportOrDistributeDto));
	}

}
