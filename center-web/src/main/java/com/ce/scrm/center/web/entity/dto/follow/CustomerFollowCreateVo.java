package com.ce.scrm.center.web.entity.dto.follow;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/8/9
 */
@Data
public class CustomerFollowCreateVo extends LoginInfo implements Serializable {
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 联系人ID
     */
    private String contactPersonId;
    /**
     * 联系人名称
     */
    private String contactPersonName;
    /**
     * 联系人手机号
     */
    private String contactPersonMobile;

    /**
     * 省
     */
    private String provinceCode;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 市
     */
    private String cityCode;

    /**
     * 市
     */
    private String cityName;

    /**
     * 区
     */
    private String districtCode;

    /**
     * 区
     */
    private String districtName;

    /**
     * 通讯地址
     */
    private String address;

    /**
     * 跟进方式
     * VisitTypeEnum
     */
    private Integer visitType;

    /**
     * 电话接通情况
     */
    private String followCallStatus;

    /**
     * 意向产品列表
     */
    private List<String> intentionProductList;

    /**
     * 跟进内容
     */
    private String content;

    /**
     * 附件文件ID 列表
     */
    private List<String> fileIds;

}
