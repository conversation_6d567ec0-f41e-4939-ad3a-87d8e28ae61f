package com.ce.scrm.center.web.entity.dto.ai;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * @classname AiChatLogWebDto
 * @description TODO
 * @date 2025/3/14 16:49
 * @create by gaomeijing
 */
@Data
public class AiChatLogWebDto extends LoginInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * pid
     */
    private String pid;

    /**
     * custId
     */
    private String custId;

    /**
     * 来源 WECOM / pc
     */
    private String platform;

    /**
     * 分析主题ID
     */
    private String promptId;

    /**
     * 聊天员工ID
     */
    private String loginEmployeeId;

    /**
     * 页码
     */
    private Integer pageNum=1;

    /**
     * 每页数量
     */
    private Integer pageSize=10;
}
