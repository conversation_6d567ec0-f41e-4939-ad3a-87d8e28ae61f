package com.ce.scrm.center.web.entity.dto.ai;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 查询ai提示词信息
 * @Author: lijinpeng
 * @Date: 2025/2/20 09:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryAiPromptInfoWebDto extends LoginInfo implements Serializable {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 100;

    private Integer startFlag;


    /**
     * 分司ID
     */
    private String subId;


    /**
     * 事业部id
     */
    private String buId;

    private Integer promptType;
    private List<Integer> promptTypeList;


    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 区域ID
     */
    private String areaId;


    /**
     * pid
     */
    private String pid;
    /**
     * custId
     */
    private String custId;

}
