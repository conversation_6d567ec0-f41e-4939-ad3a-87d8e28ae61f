package com.ce.scrm.center.web.entity.dto.protect;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 分配保护关系
 * @Author: lijinpeng
 * @Date: 2025/1/2 15:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AssignProtectBatchWebDto extends LoginInfo implements Serializable {

    /**
     * 客户id集合
     */
    @NotEmpty
    private List<String> custIdList;

    /**
     * 分配的商务id
     */
    private String assignToSalerId;

    /**
     * 分配的部门id
     */
    private String assignToDeptId;

    /**
     * 分配的事业部id
     */
    private String assignToBuId;

    /**
     * 分配的分司id
     */
    private String assignToSubId;

    /**
     * 动作类型
     */
    @NotNull
    private Integer actionType;

}
