package com.ce.scrm.center.web.entity.dto.abm;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户营销列表请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerMarketingActivityWebPageDto extends LoginInfo implements Serializable {
	/**
	 * 页码
	 */
	private Integer pageNum = 1;
	/**
	 * 每页数量
	 */
	private Integer pageSize = 10;

	/**
	 * 活动id
	 */
	private Long id;

	/**
	 * 活动code
	 */
	private String activityCode;

	/**
	 * 活动名称
	 */
	private String activityName;

	/**
	 * 创建人id
	 */
	private String createBy;

	/**
	 * 执行状态 0-待执行 1-执行中 2-执行成功 3-执行失败
	 */
	private Integer executeState;

	/**
	 * 创建时间开始
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createTimeStart;

	/**
	 * 创建时间结束
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createTimeEnd;

}
