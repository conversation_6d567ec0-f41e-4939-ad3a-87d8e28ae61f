package com.ce.scrm.center.web.util.gptstreamutil;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/11
 */
@AllArgsConstructor
public enum MessageTypeEnum {

    TEXT(0, "[文字]", "ぁ文字あ"),

    IMAGE(1, "[图片]", "ぁ图片あ"),

    VIDEO(2, "[视频]", "ぁ视频あ"),

    MARKDOWN(3, "[MARK_DOWN]", "ぁMARK_DOWNあ"),

    CARD_MESSAGE(4, "[CARD_MESSAGE]", "ぁCARD_MESSAGEあ"),

    END_POINT(99, "[终止]", "ぁ终止あ");

    @Getter
    private int type;

    @Getter
    private String value;

    /**
     * 这个值的存在是为了解决  markdown 格式中的一些特殊符号和正则表达式冲突的问题
     * ぁ 和 あ  这两个符号作为   [ 和 ]    这两个中括号的替代品存在
     */
    @Getter
    private String valueQ;

    private static final String MESSAGEPATTERN = Arrays.stream(values()).map(T -> T.value.replaceAll("\\[|\\]", "")).collect(Collectors.joining("|"));

    private static final String PATTERN_STR = "ぁ(" + MESSAGEPATTERN + ")あ[^ぁ]*";

    public static final Pattern PATTERN = Pattern.compile(PATTERN_STR);

    public static String formatContent(String content) {
        for (MessageTypeEnum messageTypeEnum : values()) {
            content = content.replace(messageTypeEnum.getValue(), messageTypeEnum.getValueQ());
        }
        return content;
    }

    public static String formatContentRevert(String content) {
        for (MessageTypeEnum messageTypeEnum : values()) {
            content = content.replace(messageTypeEnum.getValueQ(), messageTypeEnum.getValue());
        }
        return content;
    }
}
