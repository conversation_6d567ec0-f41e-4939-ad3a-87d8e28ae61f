package com.ce.scrm.center.web.controller.entwx.follow;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.service.business.FollowBusiness;
import com.ce.scrm.center.service.business.entity.view.FollowProductInfoBusinessView;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.aop.base.LoginType;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.FollowProductInfoWebView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("entwx/follow")
@Login(loginType = LoginType.ENT_WECHAT)
public class FollowEntController {

    @Resource
    private FollowBusiness followBusiness;

    /**
     * 写跟进-意向产品的产品列表
     * @param loginInfo
     * @return
     */
    @PostMapping("getProductInfoEnum")
    public WebResult<List<FollowProductInfoWebView>> getProductInfoEnum(@RequestBody LoginInfo loginInfo) {
        List<FollowProductInfoBusinessView> businessResult  = followBusiness.getProductInfoEnum();
        List<FollowProductInfoWebView> followProductInfoWebViews = BeanUtil.copyToList(businessResult, FollowProductInfoWebView.class);
        for (FollowProductInfoWebView item : followProductInfoWebViews) {
            item.setChildren(BeanUtil.copyToList(item.getChildren(), FollowProductInfoWebView.class));
        }
        return WebResult.success(followProductInfoWebViews);
    }

}
