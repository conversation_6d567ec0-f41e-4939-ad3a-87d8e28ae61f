package com.ce.scrm.center.web.aop.scanner;

import ce.ce.sso.client.start.ClientProperties;
import com.ce.scrm.center.util.constant.UtilConstant;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.sso.MyHttpSessionAttributeListener;
import com.ce.scrm.center.web.sso.MyHttpSessionListener;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 登录扫描器
 * <AUTHOR>
 * @date 2024/5/23 上午9:04
 * @version 1.0.0
 **/
@Component
public class LoginScanner implements BeanPostProcessor, ApplicationContextAware {

    /**
     * 应用上下文
     */
    private ApplicationContext applicationContext;

    /**
     * 获取应用上下文
     * @param applicationContext    应用上下文
     * <AUTHOR>
     * @date 2024/5/23 上午10:02
     * @return void
     **/
    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 初始化Bean之前做操作
     * @param bean  对象
     * @param beanName  对象名称
     * <AUTHOR>
     * @date 2024/5/23 上午9:44
     * @return java.lang.Object
     **/
    @Override
    public Object postProcessBeforeInitialization(@NotNull Object bean, @NotNull String beanName) throws BeansException {
        if(bean instanceof ClientProperties){
            RequestMappingHandlerMapping handlerMapping = applicationContext.getBean(RequestMappingHandlerMapping.class);
            //扫描所有需要登录的路径
            Map<RequestMappingInfo, HandlerMethod> handlerMethods = handlerMapping.getHandlerMethods();
            Set<String> annotatedPaths = new HashSet<>();

            for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet()) {
                RequestMappingInfo requestMappingInfo = entry.getKey();
                HandlerMethod handlerMethod = entry.getValue();
                if (isAnnotated(handlerMethod.getMethod(), handlerMethod.getBeanType())) {
                    Set<String> patterns = requestMappingInfo.getPatternsCondition().getPatterns();
                    annotatedPaths.addAll(patterns);
                }
            }
            ClientProperties clientProperties = (ClientProperties) bean;
            clientProperties.setUrlPatterns(String.join(UtilConstant.DATA_SEPARATOR, annotatedPaths));
            clientProperties.setSessionListenerClass(MyHttpSessionListener.class.getName());
            clientProperties.setSessionAttributeListenerClass(MyHttpSessionAttributeListener.class.getName());
        }
        return bean;
    }

    /**
     * 校验当前请求的方法是否需要登录
     * @param method    方法
     * @param clazz     类
     * <AUTHOR>
     * @date 2024/5/23 上午9:17
     * @return boolean
     **/
    private boolean isAnnotated(Method method, Class<?> clazz){
        Login methodAnnotationPresent = method.getAnnotation(Login.class);
        Login classAnnotationPresent = clazz.getAnnotation(Login.class);
        Login loginAnnotation = (methodAnnotationPresent != null) ? methodAnnotationPresent : classAnnotationPresent;
        return loginAnnotation != null && loginAnnotation.checkLogin();
    }
}
