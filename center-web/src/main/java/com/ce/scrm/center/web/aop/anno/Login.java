package com.ce.scrm.center.web.aop.anno;


import com.ce.scrm.center.web.aop.base.LoginType;

import java.lang.annotation.*;

/**
 * 登录注解
 * <AUTHOR>
 * @date 2024/3/11 13:37
 * @version 1.0.0
 */
//限制只能使用在方法和注解声明的类中
@Target({ElementType.TYPE, ElementType.METHOD})
//定义注解的生命周期为运行时阶段，即在程序运行时通过反射等手段获取到注解的相关信息来做相应处理
@Retention(value = RetentionPolicy.RUNTIME)
//定义注解可随着Java文件被定义到JavaDoc文档中
@Documented
public @interface Login {
    /**
     * 是否校验登录，默认校验登录
     */
    boolean checkLogin() default true;

    /**
     * 登录类型
     */
    LoginType loginType() default LoginType.PC;
}