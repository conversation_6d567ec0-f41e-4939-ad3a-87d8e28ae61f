package com.ce.scrm.center.web.util.gptstreamutil;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import static com.ce.scrm.center.web.util.gptstreamutil.ResponseConstant.ERROR_RESPONSE_MESSAGE;

/**
 * 阿里云 qwen ql 模型 图片识别异常提示
 */
@Getter
@AllArgsConstructor
public enum ApiErrorCode {
    // 400系列错误：请求参数类错误
    INVALID_PARAMETER_JSON_PHRASE(400, "InvalidParameter.JsonPhrase", "输入 JSON 错误", ERROR_RESPONSE_MESSAGE),
    INVALID_PARAMETER_FILE_DOWNLOAD(400, "InvalidParameter.FileDownload", "输入图像下载失败", ERROR_RESPONSE_MESSAGE),
    INVALID_PARAMETER_IMAGE_FORMAT(400, "InvalidParameter.ImageFormat", "读取图像失败", "上传图片不合法"),
    INVALID_PARAMETER_IMAGE_CONTENT(400, "InvalidParameter.ImageContent", "图像内容不合规", "图像内容不合规!"),
    INVALID_PARAMETER_IMAGE_CONTENT_NEW(400, "data_inspection_failed", "图像内容不合规", "图像内容不合规!"),
    INVALID_PARAMETER_DATA_INSPECTION(400, "InvalidParameter.DataInspection", "输出图像尺寸超限（大于10M）,或者图片异常", "图片内容不合规!"),
    INTERNAL_ERROR_ALGO(500, "InternalError.Algo", "算法错误", ERROR_RESPONSE_MESSAGE),
    INTERNAL_ERROR_FILE_UPLOAD(500, "InternalError.FileUpload", "文件上传失败", ERROR_RESPONSE_MESSAGE),
    INVALID_PARAMETER(400, "InvalidParameter", "输入参数值超出范围", ERROR_RESPONSE_MESSAGE);


    private final int httpCode;
    private final String errorCode;
    private final String messageZh;
    private final String tips;

    public static ApiErrorCode getTips(String message) {
        if (StringUtils.isBlank(message)) {
            return null;
        }
        for (ApiErrorCode error : ApiErrorCode.values()) {
            if (message.contains(error.getErrorCode())) {
                return error;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "ApiErrorCode{" +
                "httpCode=" + httpCode +
                ", errorCode='" + errorCode + '\'' +
                ", messageZh='" + messageZh + '\'' +
                ", tips='" + tips + '\'' +
                '}';
    }
}
