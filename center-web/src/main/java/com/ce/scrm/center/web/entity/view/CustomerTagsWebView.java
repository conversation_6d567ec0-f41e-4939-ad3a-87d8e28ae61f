package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CustomerTagsWebView implements Serializable {

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 标签代码，唯一标识
	 */
	private String tagCode;

	/**
	 * 标签名称
	 */
	private String tagName;

	/**
	 * 标签类型：1=正向标签, 2=负向标签
	 */
	private Integer tagType;

	/**
	 * 标签的分类id
	 */
	private Long tagCategoryId;

	/**
	 * 是否启用：1=启用, 0=禁用
	 */
	private Integer isActive;

	/**
	 * 当前标签积分权重分值
	 */
	private BigDecimal weight;

	/**
	 * 标签描述
	 */
	private String tagDesc;

	/**
	 * 创建时间
	 */
	private Date createdTime;

	/**
	 * 更新时间
	 */
	private Date updatedTime;

}
