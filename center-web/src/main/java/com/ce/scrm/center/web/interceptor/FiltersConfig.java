package com.ce.scrm.center.web.interceptor;

import ce.ce.sso.client.start.ClientProperties;
import cn.ce.sso.client.exception.SsoClientException;
import cn.ce.sso.common.utils.CommonUtils;
import com.ce.scrm.center.web.aop.aspect.FirewallFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;

/**
 * FilterConfig
 * web.xml 里面的filter 修改成springboot版本
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/24 10:49
 */
@Configuration
@EnableConfigurationProperties({ClientProperties.class})
public class FiltersConfig {

    private static final Logger log = LoggerFactory.getLogger(FiltersConfig.class);
    @Autowired
    private ClientProperties clientProperties;

    public FiltersConfig() {
    }

    @Bean
    public FilterRegistrationBean registerAuthFilter() {
        this.checkParam();
        String urlPatterns = this.clientProperties.getUrlPatterns();
        String[] ups = new String[0];
        if (urlPatterns.contains(",")) {
            ups = urlPatterns.split(",");
        } else {
            ups = new String[]{urlPatterns};
        }

        String name = this.clientProperties.getName();
        Integer order = this.clientProperties.getOrder();
        String ssoServer = this.clientProperties.getSsoServer();
        String localSsoServer = this.clientProperties.getLocalSsoServer();
        String clientDomain = this.clientProperties.getClientDomain();
        String loginUri = this.clientProperties.getLoginUri();
        String excludedPaths = this.clientProperties.getExcludedPaths();
        String sessionListenerClass = this.clientProperties.getSessionListenerClass();
        String sessionAttributeListenerClass = this.clientProperties.getSessionAttributeListenerClass();
        Integer sessionTimeout = this.clientProperties.getSessionTimeout();
        String redisHosts = this.clientProperties.getRedisHosts();
        Integer maxTotal = this.clientProperties.getRedisPoolMaxTotal();
        Integer maxIdle = this.clientProperties.getRedisPoolMaxIdle();
        Integer minIdle = this.clientProperties.getRedisPoolMinIdle();
        Integer maxWaitMillis = this.clientProperties.getRedisPoolMaxWaitMillis();
        Boolean local = this.clientProperties.getLocalCacheAttribute();
        boolean localCacheAttribute = false;
        if (local != null) {
            localCacheAttribute = local;
        }

        boolean testOnBorrow = this.clientProperties.isRedisPoolTestOnBorrow();
        boolean testWhileIdle = this.clientProperties.isRedisPoolTestWhileIdle();
        boolean testOnReturn = this.clientProperties.isRedisPoolTestOnReturn();
        boolean notPrimaryDomainForCookie = this.clientProperties.isNotPrimaryDomainForCookie();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new ScrmSsoFilter());
        registration.addUrlPatterns(ups);
        log.info("登录使用的filter={}",name);
        registration.setName(name);
        registration.setOrder(order);
        registration.addInitParameter("ssoServer", ssoServer);
        registration.addInitParameter("notPrimaryDomainForCookie", String.valueOf(notPrimaryDomainForCookie));
        if (!CommonUtils.isNullString(localSsoServer)) {
            registration.addInitParameter("localSsoServer", localSsoServer);
        }

        if (!CommonUtils.isNullString(clientDomain)) {
            registration.addInitParameter("clientDomain", clientDomain);
        }

        if (!CommonUtils.isNullString(excludedPaths)) {
            registration.addInitParameter("excludedPaths", excludedPaths);
        }

        String pwd = this.clientProperties.getRedisPassword();
        registration.addInitParameter("loginUri", loginUri);
        registration.addInitParameter("sessionListenerClass", sessionListenerClass);
        registration.addInitParameter("sessionAttributeListenerClass", sessionAttributeListenerClass);
        registration.addInitParameter("sessionTimeout", sessionTimeout.toString());
        registration.addInitParameter("localCacheAttribute", String.valueOf(localCacheAttribute));
        registration.addInitParameter("redis.hosts", redisHosts);
        registration.addInitParameter("redis.pool.maxTotal", maxTotal.toString());
        registration.addInitParameter("redis.pool.maxIdle", maxIdle.toString());
        registration.addInitParameter("redis.pool.minIdle", minIdle.toString());
        registration.addInitParameter("redis.pool.maxWaitMillis", maxWaitMillis.toString());
        registration.addInitParameter("redis.pool.testOnBorrow", String.valueOf(testOnBorrow));
        registration.addInitParameter("redis.pool.testWhileIdle", String.valueOf(testWhileIdle));
        registration.addInitParameter("redis.pool.testOnReturn", String.valueOf(testOnReturn));
        if (!CommonUtils.isNullString(pwd)) {
            registration.addInitParameter("redis.password", this.clientProperties.getRedisPassword());
        }

        return registration;
    }

    private void checkParam() {
        String urlPatterns = this.clientProperties.getUrlPatterns();
        if (CommonUtils.isNullString(urlPatterns)) {
            throw new SsoClientException("urlPatterns is not set");
        } else {
            String ssoServer = this.clientProperties.getSsoServer();
            if (CommonUtils.isNullString(ssoServer)) {
                throw new SsoClientException("ssoServer is not set");
            } else {
                String redisHosts = this.clientProperties.getRedisHosts();
                if (CommonUtils.isNullString(redisHosts)) {
                    throw new SsoClientException("redisHosts is not set");
                }
            }
        }
    }

    @Bean
    public FilterRegistrationBean<Filter> cacheJsonFilter() {
        FilterRegistrationBean<Filter> bean = new FilterRegistrationBean<>();
        bean.setFilter(new FirewallFilter());
        bean.setName(new Throwable().getStackTrace()[0].getMethodName());
        return bean;
    }

}
