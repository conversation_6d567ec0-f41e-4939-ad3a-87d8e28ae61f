package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 客户关联企业信息
 * @
 */
@Data
public class CustomerAffiliatedEnterpriseWebView implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 关联企业数量
	 */
	private Integer total;

	/**
	 * 关联企业详情列表
	 */
	private List<CustomerAffiliatedEnterpriseDetailWebView> items;


	/**
	 * 详情信息
	 */
	@Data
	public static class CustomerAffiliatedEnterpriseDetailWebView implements Serializable {
		private static final long serialVersionUID = 1L;

		/**
		 * 企业名称
		 */
		private String entName;

		/**
		 * 注册资本
		 */
		private String  regCapital;

		/**
		 * 职务/职位
		 */
		private String position;

		/**
		 * 任职状态
		 */
		private String  positionStatus;

		/**
		 * 市场
		 */
		private String marketName;

		/**
		 * 客户保护状态
		 */
		private String customerState;

		/**
		 * 是否可以保护 1是 0否
		 */
		private Integer protectFlag;


		/**
		 * 统一信用代码
		 */
		private String uncid;

		/**
		 * 企业唯一编码
		 */
		private String pid;

		/**
		 * custId
		 */
		private String custId;

		/**
		 * interfaceType： 1 线索转保护 2客户池转保护
		 *
		 * cn.ce.cesupport.enums.CustomerStageEnum
		 * 0，1，2 调第一个接口
		 * 8，10 第二个接口。其他情况不许转收藏，转保护了。
		 */
		private Integer interfaceType;
	}
}
