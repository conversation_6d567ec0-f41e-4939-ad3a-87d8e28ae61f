package com.ce.scrm.center.web.enums;

public enum ActivityEnum {

    //自然流量
    NATURAL_FLOW("自然流量", 1),

    //营销站流量
    MARKETING_STATION_FLOW("营销站流量", 2),
    //推广手机流量
    TELEPHONE_FLOW("推广手机流量", 3),

    //自然手机流量
    NATURAL_TELEPHONE_FLOW("自然手机流量", 4),
    //618活动
    ACTIVITY_618("618活动", 5),
    //618活动(线下推广)
    ACTIVITY_618_TELEPHONE_FLOW("618（线下推广）", 6),
    //推荐有礼
    RECOMMEND_GIFT("推荐有礼", 7),
    //916论坛活动
    FORUM_916_ACTIVITIES("916论坛活动", 8),
    //商学院自然
    BUSINESS_SCIENCE_NATURAL_FLOW("商学院自然", 9),
    //2018双11
    ACTIVITY_2018_DOUBLE_ELEVEN("2018年双十一", 10),
    //2018年短信课程推广
    TELEPHONE_MESSAGE_COURSES_PROMOTION("2018年短信课程推广", 11),
    //商学院学习卡
    BUSINESS_SCIENCE_STUDENT_CARD("商学院学习卡", 12),
    //2018双旦活动
    ACTIVITY_2018_DOUBLE_DAWN("2018年双旦", 13),
    //江门论坛
    JIANG_MEN_FORUM("江门论坛", 14),
    //中企校友福利项目
    ZHONG_QI_ALUMNI_GIFT_PROJECT("中企校友福利项目", 15),
    //2019年118建站节
    ACTIVITY_2019_118_BUILDING_JEWELRY_FESTIVAL("2019年118建站节", 16),
    //离职商务推荐
    RESIGNATION_COMMERCIAL_RECOMMENDATION("离职商务推荐", 17),
    //AI外呼线上转化
    AI_OUT_CALL_ONLINE_CONVERSION("AI外呼线上转化", 18),
    //19-315活动
    ACTIVITY_19_315("19-315活动", 19),
    //联合送礼2019
    UNION_GIFT_2019("19年联合送礼", 20),
    //中企智能名片
    ZHONG_QI_SMART_CARD("中企智能名片", 21),
    //6月企业推广月
    ACTIVITY_JUNE_ENTERPRISE_PROMOTION_MONTH("6月企业推广月", 22),
    //会员领福利
    MEMBER_GIFT("会员领福利", 23),
    //销管618
    ACTIVITY_618_SALES_MANAGEMENT("销管618", 24),
    //7月案例评选月
    ACTIVITY_JULY_CASE_SELECTION_MONTH("7月案例评选月", 25),
    //商学院VIP商务活动
    BUSINESS_SCIENCE_VIP_COMMERCIAL_ACTIVITIES("商学院VIP商务活动", 26),
    //汇推
    HUITUI("汇推", 27),
    //10月拉新
    ACTIVITY_10_NEW_LEADS("10月拉新", 28),
    //电销转化
    CONVERSION_ELECTRONIC_SALES("电销转化", 29),
    //双11
    ACTIVITY_11("双11", 30),
    //SEO直播活动
    SEO_LIVE_ACTIVITY("SEO直播活动", 31),
    //圣诞抽奖
    CHRISTMAS_LOTTERY("圣诞抽奖", 32),
    //VIP权益卡
    VIP_RIGHTS_CARD("VIP权益卡", 33),
    //建站节
    BUILDING_JEWELRY_FESTIVAL("建站节", 34),
    //企业扶持计划
    ENTERPRISE_SUPPORT_PLAN("企业扶持计划", 35),
    //移动商城
    MOBILE_SHOPPING("移动商城", 36),
    //速成建站
    BUILDING_JEWELRY_SELF_SUPPORT("速成建站", 37),
    //商学院直播
    BUSINESS_SCIENCE_LIVE("商学院直播", 38),
    //盟主直播
    MENT_LIVE("盟主直播", 39),
    //官网SEO
    WEBSITE_SEO("官网SEO", 40),
    //蓝猫微会
    BLUE_CAT_MICRO_MEETING("蓝猫微会", 41),
    //网站监测工具
    WEBSITE_MONITORING_TOOL("网站监测工具", 42),
    //公众号管理
    WECHAT_MANAGEMENT("公众号管理", 43),
    //速成建站全系列
    BUILDING_JEWELRY_SELF_SUPPORT_SERIES("速成建站全系列", 44),
    //企业复苏季
    ACTIVITY_ENTERPRISE_RECOVERY_SEASON("企业复苏季", 45),
    //商学院固定直播
    BUSINESS_SCIENCE_FIXED_LIVE("商学院固定直播", 46),
    //赵彦刚直播
    ZHAO_YAN_GANG_LIVE("赵彦刚直播", 47),
    //邹汐竺直播
    ZOU_XI_ZHOU_LIVE("邹汐竺直播", 48),
    //广交会云参展
    GLOBAL_EXHIBITION_YUN_EXHIBITION("广交会云参展", 49),
    //百度百科创建
    BAIDU_BIBLE_CREATION("百度百科创建", 50),
    //全民电商
    QUALITY_COMMERCE("全民电商", 51),
    //抽奖活动
    LOTTERY_ACTIVITY("抽奖活动", 52),
    //百科产品裂变
    BIBLE_PRODUCT_FRAGMENTATION("百科产品裂变", 53),
    //全球门户
    GLOBAL_PORTAL("全球门户", 54),
    //21周年
    ACTIVITY_21_YEARS("21周年", 55),
    //小程序
    MINI_PROGRAM("小程序", 56),
    //双11活动
    ACTIVITY_11_ACTIVITY("双11活动", 57),
    //双旦抽奖
    ACTIVITY_DOUBLE_DAWN_LOTTERY("双旦抽奖", 58),
    //浮窗广告
    FLOATING_WINDOW_ADVERTISEMENT("浮窗广告", 59),
    //心动5月-建站推广新体验
    ACTIVITY_5_MONTH_BUILDING_PROMOTION_NEW_EXPERIENCE("心动5月-建站推广新体验", 60),
    //618年中大促
    ACTIVITY_618_YEAR_PROMPT("618年中大促", 61),
    //916周年庆-活动
    ACTIVITY_916_YEAR_CELEBRATION_ACTIVITY("916周年庆-活动", 62),
    //2022—3月诚信周
    ACTIVITY_2022_3_MONTH_INTEGRITY_WEEK("2022—3月诚信周", 63),
    //行业站群
    INDUSTRY_STATION_GROUP("行业站群", 64),
    //2023诚信周
    ACTIVITY_2023_INTEGRITY_WEEK("2023诚信周", 65),
    //6月峰会
    ACTIVITY_6_CONFERENCE("6月峰会", 66),
    //跨境默认活动
    CROSS_BORDER_DEFAULT_ACTIVITY("跨境默认活动", 67),
    //跨境测试活动
    CROSS_BORDER_TEST_ACTIVITY("跨境测试活动", 68),
    //跨境投放流量
    CROSS_BORDER_PUT_FLOW("跨境投放流量", 69),


    ;

    /**
     * lable
     */
    private String lable;

    /**
     * value
     */
    private Integer value;

    private ActivityEnum(String lableStr, Integer value) {
        this.lable = lableStr;
        this.value = value;
    }

    public String getLable() {
        return lable;
    }

    public void setLable(String lable) {
        this.lable = lable;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public static String ByValue(Integer in) {
        for (ActivityEnum statusEnum : ActivityEnum.values()) {
            if (statusEnum.getValue().equals(in)) {
                return statusEnum.getLable();
            }
        }
        return null;
    }

}
