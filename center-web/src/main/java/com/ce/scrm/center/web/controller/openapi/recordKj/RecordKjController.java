package com.ce.scrm.center.web.controller.openapi.recordKj;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.business.RecordKjBusiness;
import com.ce.scrm.center.service.business.entity.dto.openapi.SaveCallRecordKjBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.openapi.SaveRecordKjBusinessDto;
import com.ce.scrm.center.web.entity.dto.openapi.SaveCallRecordKjWebDto;
import com.ce.scrm.center.web.entity.dto.openapi.SaveRecordKjWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @version 1.0
 * @Description: 录音推送免登录
 * @Author: lijinpeng
 * @Date: 2025/3/17 10:13
 */
@Slf4j
@RestController
@RequestMapping("/openapi/record")
public class RecordKjController {

    @Resource
    private RecordKjBusiness recordKjBusiness;

    /*
     * @Description 天润融通-录音状态推送
     * <AUTHOR>
     * @date 2025/3/19 09:23
     * @param saveRecordKjWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     */
    @PostMapping("saveRecordKj")
    public WebResult<Boolean> saveRecordKj(@RequestBody SaveRecordKjWebDto saveRecordKjWebDto) {
        SaveRecordKjBusinessDto saveRecordKjBusinessDto = BeanUtil.copyProperties(saveRecordKjWebDto, SaveRecordKjBusinessDto.class);
        Boolean result = recordKjBusiness.saveRecordKj(saveRecordKjBusinessDto);
        return WebResult.success(result);
    }

    /*
     * @Description 天润融通-话单推送
     * <AUTHOR>
     * @date 2025/3/19 09:23
     * @param saveRecordKjWebDto
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
     */
    @PostMapping("saveCallRecordKj")
    public WebResult<Boolean> saveCallRecordKj(@RequestBody SaveCallRecordKjWebDto saveCallRecordKjWebDto) {
        log.info("天润融通-话单推送,saveCallRecordKj:{}", JSONObject.toJSONString(saveCallRecordKjWebDto));
        SaveCallRecordKjBusinessDto saveCallRecordKjBusinessDto = BeanUtil.copyProperties(saveCallRecordKjWebDto, SaveCallRecordKjBusinessDto.class);
        Boolean result = recordKjBusiness.saveCallRecordKj(saveCallRecordKjBusinessDto);
        return WebResult.success(result);
    }


}
