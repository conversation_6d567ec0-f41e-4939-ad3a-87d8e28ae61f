package com.ce.scrm.center.web.dubbo.provider;

import com.ce.scrm.center.dubbo.api.GcBusinessOpportunityDubbo;
import com.ce.scrm.center.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.service.business.GcBusinessOpportunityBusiness;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 高呈客户商机
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/08/07
 */
@Slf4j
@DubboService(interfaceClass = GcBusinessOpportunityDubbo.class)
public class GcBusinessOpportunityDubboService implements GcBusinessOpportunityDubbo {
    @Resource
    private GcBusinessOpportunityBusiness gcBusinessOpportunityBusiness;


    /**
     * Description: 判断是否是流失客户
     *
     * @param custId 客户id
     * @return
     * @author: liyechao
     */
    @Override
    public DubboResult<Integer> getGcBusinessState(String custId) {
        Optional<Integer> businessState = gcBusinessOpportunityBusiness.getGcBusinessState(custId);
        return businessState.map(DubboResult::success).orElseGet(() -> DubboResult.error(DubboCodeMessageEnum.RETURN_NULL));
    }

}