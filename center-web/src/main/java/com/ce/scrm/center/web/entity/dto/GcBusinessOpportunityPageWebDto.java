package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 高呈商机分页参数
 * <AUTHOR>
 * @date 2024/5/15 上午10:53
 * @version 1.0.0
 */
@Data
public class GcBusinessOpportunityPageWebDto extends LoginInfo implements Serializable {

    /**
     * 高呈商机查询开始时间
     */
    private LocalDateTime startTime;

    /**
     * 高呈商机查询结束时间
     */
    private LocalDateTime endTime;

    /**
     * 查询字符串（客户名称、联系人姓名、手机号）
     */
    private String queryStr;

    /**
     * 商机来源
     */
    private Integer source;

    /**
     * 商机状态
     */
    private List<Integer> state;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 10;
}