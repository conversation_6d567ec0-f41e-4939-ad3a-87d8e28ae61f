package com.ce.scrm.center.web.controller.follow;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.VisitLogBusiness;
import com.ce.scrm.center.service.business.entity.dto.follow.QueryVisitLogBusinessDto;
import com.ce.scrm.center.service.business.entity.view.follow.VisitLogDetailBusinessView;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.follow.QueryVisitLogWebDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.follow.VisitLogWebView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2025/8/8
 */
@Slf4j
@RestController
@RequestMapping("/visitlog")
@Login
public class VisitLogController {

    @Resource
    private VisitLogBusiness visitLogBusiness;

    @PostMapping("list")
    public WebResult<WebPageInfo<VisitLogWebView>> list(@RequestBody QueryVisitLogWebDto queryVisitLogWebDto) {
        if (queryVisitLogWebDto == null || StringUtils.isBlank(queryVisitLogWebDto.getCustomerId())){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        QueryVisitLogBusinessDto queryBusinessDto = BeanUtil.copyProperties(queryVisitLogWebDto, QueryVisitLogBusinessDto.class);
        Page<VisitLogDetailBusinessView> businessResult = visitLogBusiness.getVisitLogDetailList(queryBusinessDto);
        WebPageInfo<VisitLogWebView> webResult = WebPageInfo.pageConversion(businessResult, VisitLogWebView.class);
        return WebResult.success(webResult);
    }

}
