package com.ce.scrm.center.web.entity.dto;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description: 流转日志更新参数
 * @author: JiuDD
 * date: 2024/7/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConvertLogUpdateWebDto extends LoginInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @NotBlank(message = "ID不能为空")
    private String id;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 经理补充原因
     */
    private String jlReasonText;
    /**
     * 经理补充原因时间
     */
    private LocalDateTime jlReleaseTime;
}