package com.ce.scrm.center.web.util.gptstreamutil;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ce.scrm.center.web.util.gptstreamutil.ConcurrentMarkdownCrawler.fetchMarkdownFromUrls;

@Slf4j
@Service
public class FunctionCallingService {

    private static final JSONObject message = JSONObject.parseObject("{\"role\":\"system\",\"content\":\"\\n            你是一个判断器，请判断用户的问题是否需要联网搜索。只需要回答是否需要搜索和需要查询的内容，不要回答其余内容\\n            比如如下词语则回答是\\n      \\\"今天\\\", \\\"最近\\\", \\\"最新\\\", \\\"今年\\\", \\\"明年\\\", \\\"2025年\\\", \\\"实时\\\", \\\"本周\\\", \\\"本月\\\",\\n    \\\"价格\\\", \\\"股市\\\", \\\"汇率\\\", \\\"基金\\\", \\\"债券\\\", \\\"物价\\\", \\\"通胀\\\", \\\"金融\\\", \\\"油价\\\", \\\"黄金价格\\\",\\n    \\\"天气\\\", \\\"气温\\\", \\\"台风\\\", \\\"暴雨\\\", \\\"空气质量\\\", \\\"地震\\\", \\\"灾害\\\", \\\"预警\\\",\\n    \\\"航班\\\", \\\"飞机\\\", \\\"高铁\\\", \\\"火车\\\", \\\"快递\\\", \\\"物流\\\", \\\"油价\\\",\\n    \\\"比赛\\\", \\\"比分\\\", \\\"NBA\\\", \\\"世界杯\\\", \\\"奥运会\\\", \\\"体育\\\", \\\"冠军\\\", \\\"赛事\\\",\\n    \\\"新闻\\\", \\\"突发\\\", \\\"头条\\\", \\\"时事\\\", \\\"热点\\\", \\\"社会\\\", \\\"疫情\\\", \\\"选举\\\", \\\"改革\\\",\\n    \\\"电影\\\", \\\"票房\\\", \\\"综艺\\\", \\\"电视剧\\\", \\\"演唱会\\\", \\\"豆瓣评分\\\", \\\"烂番茄评分\\\",\\n    \\\"科技\\\", \\\"芯片\\\", \\\"AI\\\", \\\"人工智能\\\", \\\"软件更新\\\", \\\"新机\\\", \\\"新款\\\", \\\"开源\\\",\\n    \\\"政策\\\", \\\"法规\\\", \\\"政府\\\", \\\"移民\\\", \\\"签证\\\", \\\"社保\\\", \\\"养老金\\\", \\\"医保\\\", \\\"考试\\\",\\n    \\\"热搜\\\", \\\"爆火\\\", \\\"网红\\\", \\\"直播\\\", \\\"短视频\\\", \\\"趋势\\\", \\\"TikTok\\\", \\\"YouTube\\\",\\n    \\\"折扣\\\", \\\"促销\\\", \\\"618\\\", \\\"双11\\\", \\\"黑五\\\", \\\"网购\\\", \\\"秒杀\\\", \\\"库存\\\", \\\"断货\\\"\\n    \\n            #### 输入 ####\\n              今天天气\\n            #### 输出 ####\\n            是\\n            \\n            \\n             #### 输入 ####\\n              牛顿第二定律\\n            #### 输出 ####\\n            否\\n            \\n             #### 输入 ####\\n              2024年全年人口增长率\\n            #### 输出 ####\\n            是\\n            \\n              #### 输入 ####\\n              重新总结一下上面的信息\\n            #### 输出 ####\\n            否\\n            \"}");

    private static final JSONObject questionReformat = JSONObject.parseObject("{\"role\":\"system\",\"content\":\"把内容进行整理,结合上下文，重新形成用户问题给搜索引擎搜索,要简短控制在 20字以内，直接给出问题 \"}");

    public SearchRes searchResult(String messageContent, String originContent) {
        log.info("请求判断信息：{}", messageContent);
        FunctionRes functionRes = needSearch(messageContent);
        if (!functionRes.isNeedSearch()) {
            log.info("不需要联网搜索:{}", messageContent);
            return new SearchRes();
        } else {
            log.info("需要联网搜索:{}", messageContent);
            return search(messageContent, originContent);
        }
    }


    public static SearchRes search(String ask, String searchOriginContent) {
        try {
            //执行搜索
            String searchContent = askReformat(ask, searchOriginContent);
            if (!StringUtils.hasText(searchContent)) {
                return new SearchRes();
            }
            log.info("搜索内容:{}", searchContent);
            String result = getSearchResult(searchContent);
            JSONObject jsonObjectRes = JSONObject.parseObject(result);
            StringBuilder stringBuilder = new StringBuilder();
            try {
                JSONObject jsonObject1 = jsonObjectRes.getJSONObject("answerBox");
                stringBuilder.append(jsonObject1.toJSONString()).append("\n");
            } catch (Exception e) {
                log.warn("搜索引擎请求返回 answerBox 异常");
            }
            try {
                List<String> jsonObject2 = jsonObjectRes.getJSONArray("organic").stream().map(T -> {
                            JSONObject json = (JSONObject) T;
                            return json.getString("link");
                        }).filter(T -> {
                            if (T.endsWith("pdf")) {
                                return false;
                            }
                            if (T.endsWith("xlsx")) {
                                return false;
                            }
                            return true;
                        })
                        .limit(10).collect(Collectors.toList());
                List<String> markdownResults = fetchMarkdownFromUrls(jsonObject2).stream().filter(StringUtils::hasText).collect(Collectors.toList());
                stringBuilder.append(truncate(JSONObject.toJSONString(markdownResults), 5000)).append("\n");
            } catch (Exception e) {
                log.warn("搜索引擎请求返回", e);
            }
            log.info("联网搜索结果：{}", stringBuilder);
            return new SearchRes(stringBuilder.toString());
        } catch (Exception e) {
            log.warn("请求搜索失败", e);
            return new SearchRes();
        }
    }

    private static FunctionRes needSearch(String messageContent) {
        FunctionRes functionRes = new FunctionRes();
        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .connectTimeout(1, TimeUnit.MINUTES)
                    .readTimeout(1, TimeUnit.MINUTES)
                    .writeTimeout(1, TimeUnit.MINUTES)
                    .build();
            JSONObject jsonObjectReq = new JSONObject();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("role", "user");
            jsonObject.put("content", messageContent);
            jsonObjectReq.put("messages", Arrays.asList(message, jsonObject));
            jsonObjectReq.put("stream", false);
            jsonObjectReq.put("model", "qwen-plus");
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(jsonObjectReq));
            Request request = new Request.Builder()
                    .url("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions")
                    .method("POST", body)
                    .addHeader("Authorization", "Bearer sk-7de039270e844634be26fb38451b4867")
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            String responseBody = new String(response.body().bytes());
            log.info("请求qwen-plus判断是否需要搜索:{}", responseBody);
            JSONObject jsonObjectRes = JSONObject.parseObject(responseBody);
            String param = jsonObjectRes.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
            functionRes = new FunctionRes(param.contains("是"), new ArrayList<>());
        } catch (Exception e) {
            log.warn("请求 functioncalling 失败", e);
        }
        return functionRes;
    }

    private static String getSearchResult(String messageContent) {
        try {
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("************", 9318));
            OkHttpClient client = new OkHttpClient().newBuilder().proxy(proxy)
                    .connectTimeout(1, TimeUnit.MINUTES)
                    .readTimeout(1, TimeUnit.MINUTES)
                    .writeTimeout(1, TimeUnit.MINUTES)
                    .build();
            JSONObject jsonObject = JSONObject.parseObject("{\n" +
                    "    \"q\": \"1\",\n" +
                    "    \"gl\": \"cn\",\n" +
                    "    \"hl\": \"zh-cn\"\n" +
                    "}");
            jsonObject.put("q", messageContent);
            RequestBody body = RequestBody.create(MediaType.parse("application/json"), jsonObject.toJSONString());
            Request request = new Request.Builder()
                    .url("https://google.serper.dev/search")
                    .method("POST", body)
                    .addHeader("X-API-KEY", "eab547a2693249b300eaaa7d2954b8bc3078992d")
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            String responseBody = new String(response.body().bytes());
            log.info("请求搜索返回结果{}", responseBody);
            return responseBody;
        } catch (Exception e) {
            log.warn("请求搜索失败", e);
            return null;
        }

    }

    private static String askReformat(String ask, String messageContent) {
        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .connectTimeout(1, TimeUnit.MINUTES)
                    .readTimeout(1, TimeUnit.MINUTES)
                    .writeTimeout(1, TimeUnit.MINUTES)
                    .build();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("role", "user");
            jsonObject.put("content", messageContent + "\n  结合上下文，补全下面问题 ：\n" + ask);
            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("stream", false);
            jsonObjectReq.put("model", "qwen-plus");
            jsonObjectReq.put("messages", Arrays.asList(questionReformat, jsonObject));
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(jsonObjectReq));
            Request request = new Request.Builder()
                    .url("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions")
                    .method("POST", body)
                    .addHeader("Authorization", "Bearer sk-7de039270e844634be26fb38451b4867")
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            String responseBody = new String(response.body().bytes());
            log.info("请求qwen-plus判断是否需要搜索:{}", responseBody);
            JSONObject jsonObjectRes = JSONObject.parseObject(responseBody);
            String param = jsonObjectRes.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
            log.info("请求qwen-plus判断是否需要搜索:{}", param);
            return param;
        } catch (Exception e) {
            log.warn("请求 functioncalling 失败", e);
        }
        return "";
    }

    public static String truncate(String input, int maxLength) {
        if (input == null) {
            return "";
        }
        return input.length() > maxLength ? input.substring(0, maxLength) : input;
    }


//    public static void main(String[] args) {
//        searchResult("今天天气", "[\n" +
//                "    {\n" +
//                "        \"content\": \"#### 客户信息 ####\\n{\\\"企业名称\\\":\\\"河南婉舟商贸有限公司\\\",\\\"企业基本信息\\\":{\\\"一级行业\\\":\\\"居民服务、修理和其他服务业\\\",\\\"三级行业\\\":\\\"其他未列明服务业\\\",\\\"二级行业\\\":[\\\"其他服务业\\\"],\\\"企业名称\\\":\\\"河南婉舟商贸有限公司\\\",\\\"企业注册地址\\\":\\\"河南省周口市鹿邑县栾台路北段万商京都小区6号楼2单元504室\\\",\\\"企业类型\\\":\\\"有限责任公司（自然人独资）\\\",\\\"企业英文名称\\\":\\\"HENAN WANZHOU TRADING CO., LTD.\\\",\\\"公司经营状态\\\":\\\"存续（在营、开业、在册）\\\",\\\"四级行业\\\":\\\"其他未列明服务业\\\",\\\"成立日期\\\":\\\"2020-08-17 00:00:00\\\",\\\"注册资金\\\":\\\"100万人民币\\\"},\\\"称号标签\\\":{\\\"是否是500强企业\\\":\\\"否\\\",\\\"是否是独角兽企业\\\":\\\"否\\\",\\\"是否是瞪羚企业\\\":\\\"否\\\"},\\\"商标信息列表\\\":[{\\\"商标名称\\\":\\\"丽顺康\\\",\\\"商标状态\\\":\\\"商标已注册\\\"}]}\\n#### 思考过程 ####\\n好的，我现在需要处理用户提供的关于河南婉舟商贸有限公司的数据，并按照他们的要求进行分析和建议。首先，我需要仔细阅读用户提供的客户数据，然后结合中企动力的主推服务来展开分析。\\n首先，客户的基本信息显示，这是一家位于河南省周口市鹿邑县的服务业公司，成立于2020年，注册资金100万人民币，属于其他未列明服务业。公司有英文名称，可能有一定的外贸需求，但不确定。商标信息显示他们注册了“丽顺康”这个商标，状态是已注册。目前没有提到专利信息，也没有500强、独角兽或瞪羚企业的称号。\\n接下来，根据用户的要求，我需要分三个部分来分析：360客户全景视图、网站优化建议和合作建议。\\n第一部分是整理客户信息并给出全景视图。我需要从企业基本信息、行业属性、经营特征、品牌资产和外贸属性等方面来总结。企业属于服务业，具体是其他未列明服务业，可能业务范围比较广泛或特殊。注册了商标，说明有一定的品牌意识。英文名称的存在可能意味着有外贸意向，但需要进一步确认。\\n第二部分是网站结构和内容优化建议。由于客户属于服务业，可能需要突出服务项目、优势、案例等。考虑到用户希望供应商能快速找到产品，导航栏需要清晰，产品/服务分类明确，搜索功能优化，CTA按钮设置，移动端适配等。另外，如果他们有“丽顺康”品牌，应该在网站上突出展示，增强品牌信任度。\\n第三部分是合作建议，这里有几个子项需要注意。首先，检查客户是否在招聘业务员，数据中没有直接提到，但作为商贸公司，可能会有业务团队，所以可以建议数字名片来提升效率。其次，关键词投放方面，如果客户有关键词投放，需要分析其局限性；如果没有，建议结合品牌和产品。这里客户有商标“丽顺康”，可以建议围绕这个品牌词和行业相关词进行投放。另外，商标的存在可以举例专利或品牌推广的例子，比如在Google Ads上推广品牌词。如果有外贸需求，可以推荐海外广告投放，但需要确认客户是否有这方面的业务，英文名称可能暗示有，但需要进一步沟通。此外，结合展会服务，比如智能AI客服大屏和数字名片，帮助他们在展会上吸引客户。\\n需要注意的是，用户提到的必须条件，比如招聘业务员时要推荐数字名片，但数据中没有明确说明，可能需要假设或询问用户。如果有外贸需求，必须推荐海外广告，这里由于有英文名称，可能存在，但不确定，所以建议中需要包含这一点，同时建议确认客户的外贸需求。\\n总结下来，我需要确保每个部分都覆盖用户的要求，并且结合客户的具体情况和中企动力的服务，给出切实可行的建议。同时，注意必须项是否满足，如数字名片、关键词建议、品牌推广和海外投放等。\\n#### 多维度分析结果 ####\\n一、360客户全景视图分析\\n\\n\\n\\n分析维度\\n河南婉舟商贸有限公司\\n\\n\\n\\n\\n企业基本信息\\n成立于2020年，注册资金100万人民币，属服务业中“其他未列明服务业”，主营商贸服务，注册英文名称，可能涉及外贸需求。\\n\\n\\n行业属性\\n三级行业为“其他服务业”，业务范围可能灵活多样，需通过清晰的服务分类提升客户触达效率。\\n\\n\\n经营特征\\n存续状态良好，但行业分类模糊，需强化品牌差异化（如商标“丽顺康”）以提升市场辨识度。\\n\\n\\n品牌资产\\n拥有注册商标“丽顺康”，已具备基础品牌保护意识，可进一步通过品牌推广扩大市场影响力。\\n\\n\\n外贸属性\\n英文名称显示潜在外贸需求，需验证是否涉及跨境业务，若有则可针对性布局海外推广。\\n\\n\\n\\n\\n二、网站结构及内容优化建议\\n行业痛点：服务业客户需快速展示服务项目、优势及案例，但“其他未列明服务业”分类模糊，需通过清晰导航和内容布局提升用户体验。\\n优化方向：\\n\\n\\n导航栏设计：\\n\\n一级菜单建议：首页 | 关于我们 | 服务项目（含“丽顺康”品牌专区） | 成功案例 | 联系我们\\n二级菜单（服务项目）：按服务类型分类（如咨询、代理、定制化服务），突出“丽顺康”品牌相关服务。\\n\\n\\n\\n首页布局：\\n\\n首屏：品牌标语 + “丽顺康”商标标识 + 核心服务关键词（如“商贸代理”“定制化解决方案”）。\\n中部：服务分类入口（图标+简短描述），搭配“立即咨询”按钮。\\n底部：成功案例展示（图文结合）+ 客户评价增强信任感。\\n\\n\\n\\n搜索功能：\\n\\n在服务项目页增加关键词搜索框，支持“服务名称”“行业”“区域”等多维度筛选。\\n\\n\\n\\n移动端适配：\\n\\n简化菜单层级，首页优先展示“服务项目”和“联系方式”，适配扫码添加数字名片功能。\\n\\n\\n\\n\\n三、合作建议（基于中企动力服务）\\n1. 数字名片+业务员赋能\\n\\n建议：为业务团队配置数字名片，支持一键添加微信、LinkedIn等社交账号，并嵌入官网链接、服务手册、客户评价等内容，提升客户跟进效率。\\n场景示例：展会中业务员扫码交换数字名片后，客户可立即查看“丽顺康”品牌介绍视频和服务案例，缩短决策链路。\\n\\n2. 精准关键词投放（国内+海外）\\n\\n现状分析：客户未提供现有投放关键词，需结合品牌和服务特性制定策略。\\n投放建议：\\n\\n国内：围绕“丽顺康”品牌词+行业词（如“商贸代理服务”“河南企业服务公司”），搭配长尾词（如“一站式商贸解决方案”）。\\n海外：若存在外贸需求，在Google/Yandex投放“China trading services”“Lishunkang brand”等词，定向英语/俄语区客户。\\n\\n\\n局限性规避：避免单一依赖通用词（如“商贸服务”），需通过品牌词+场景词（如“跨境商贸咨询”）吸引高意向客户。\\n\\n3. 品牌推广强化（专利/商标）\\n\\n商标利用：在官网、广告落地页中突出“丽顺康®”标识，并在Google Ads中投放品牌保护词（如“正品丽顺康服务”“丽顺康授权代理”），拦截竞品流量。\\n\\n4. 展会场景赋能\\n\\n智能AI客服大屏：在展会现场展示“丽顺康”品牌服务，支持多语言问答（如英语），吸引海外客户咨询。\\n扫码语音讲解：为每项服务生成独立二维码，客户扫码即可收听“丽顺康”服务优势的语音介绍（支持中英双语）。\\n\\n5. 全球化支持\\n\\n海外CDN加速：若客户官网需面向海外用户，通过CDN加速提升南美、中东等地的访问速度，适配外贸需求。\\n\\n\\n四、执行优先级推荐\\n\\n短期：部署数字名片 + 国内品牌词投放 + 官网导航优化（1个月内见效）。\\n中期：启动海外关键词投放 + 展会AI客服部署（3个月沉淀询盘）。\\n长期：通过社媒代运营（Facebook/YouTube）持续输出“丽顺康”品牌内容，塑造行业影响力。\\n\\n\\n########\\n接下来的回答必须在以上内容的范围内,如果超出这个范围必须拒绝回答\",\n" +
//                "        \"role\": \"system\"\n" +
//                "    },\n" +
//                "    {\n" +
//                "        \"content\": \"北京市今天天气\",\n" +
//                "        \"role\": \"user\"\n" +
//                "    },\n" +
//                "    {\n" +
//                "        \"content\": \"根据互联网搜索结果，北京市今天的天气情况如下（基于2025年3月24日数据）：\\n\\n温度范围：日间最高气温21℃，夜间最低气温7℃（中国天气网数据）/ 另有数据显示最低12℃（浮尘天气时段的夜间温度）\\n风力等级：3-4级西北风，阵风可达6-7级（尤其傍晚至前半夜西部有浮尘）\\n天气现象：白天晴转多云，傍晚局部有浮尘\\n体感参考：weather.com显示当前实时温度为20℃\\n\\n建议外出携带防风外套，西部区域居民需注意浮尘天气对能见度的影响。如需实时更新，可通过中国天气网或weather.com查看最新数据。\\n\",\n" +
//                "        \"role\": \"assistant\"\n" +
//                "    },\n" +
//                "    {\n" +
//                "        \"content\": \"你是谁\",\n" +
//                "        \"role\": \"user\"\n" +
//                "    },\n" +
//                "    {\n" +
//                "        \"content\": \"我是由深度求索（DeepSeek）开发的智能助手，专注于通过分析企业信息（如您提供的河南婉舟商贸有限公司数据）提供定制化商业建议。我的能力包括：\\n\\n行业洞察 - 根据企业所属行业（如您案例中的\\\"其他未列明服务业\\\"）解析行业特性\\n品牌诊断 - 针对商标/专利信息（如\\\"丽顺康\\\"商标）给出品牌建设建议\\n数字营销规划 - 结合企业特征推荐官网优化、关键词投放等方案\\n\\n所有分析均基于您提供的具体企业数据展开，不会涉及未经授权的信息获取。如需扩展分析范围，可补充更多企业相关信息。\\n\",\n" +
//                "        \"role\": \"assistant\"\n" +
//                "    },\n" +
//                "    {\n" +
//                "        \"content\": \"请结合 2025 年 这个行业的最新数据重新分析一下\",\n" +
//                "        \"role\": \"user\"\n" +
//                "    }\n" +
//                "]");
//    }
}
