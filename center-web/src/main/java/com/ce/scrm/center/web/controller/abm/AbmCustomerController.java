package com.ce.scrm.center.web.controller.abm;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.emp.service.OrgAppService;
import cn.ce.cesupport.emp.vo.OrgVo;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.DateUnit;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.query.SmaMarketAreaQuery;
import com.ce.scrm.center.dao.entity.query.SmaMarketSubcompanyQuery;
import com.ce.scrm.center.dao.entity.view.SmaMarketAreaView;
import com.ce.scrm.center.dao.entity.view.SmaMarketSubcompanyView;
import com.ce.scrm.center.dao.service.SmaMarketAreaService;
import com.ce.scrm.center.dao.service.SmaMarketSubcompanyService;
import com.ce.scrm.center.service.business.CustomerBusiness;
import com.ce.scrm.center.service.business.entity.dto.CustomerESPageBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.abm.ContactPersonCallStatusDto;
import com.ce.scrm.center.service.business.entity.view.customer.ContactPersonBusinessView;
import com.ce.scrm.center.service.business.entity.view.customer.CustomerESBusinessView;
import com.ce.scrm.center.service.eqixiu.sdk.util.StrUtil;
import com.ce.scrm.center.service.third.invoke.CustomerTagsThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.abm.CustomerContactWebDto;
import com.ce.scrm.center.web.entity.dto.customer.CustomerESPageWebDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.CustomerESWebView;
import com.ce.scrm.center.web.enums.*;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerTagDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 跨境ABM营销leads控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-07-23
 */
@Slf4j
@Login
@RestController
@RequestMapping("/abm/customer")
public class AbmCustomerController {

    @Resource
    private CustomerBusiness customerBusiness;

    @Resource
    CustomerTagsThirdService customerTagsThirdService;

    @DubboReference
    private OrgAppService orgAppService;

    @Resource
    private SmaMarketSubcompanyService smaMarketSubcompanyService;

    @Resource
    private SmaMarketAreaService smaMarketAreaService;

    /**
     * <AUTHOR>
     * @date 2025/8/5 21:33:25
     * @desc 公海列表, protect_status=4
     * 返回的邮箱和微信号的需要加密,规则如下：
     * 邮箱：首字 +*...* + 尾字 +@完整域名(e.g.,z*****<EMAIL>)
     * 微信号：前1位 +*...* + 后1位 (e.g.,j****2)
     */
    @PostMapping("/public/list")
    public WebResult<WebPageInfo<CustomerESWebView>> publicList(@Valid @RequestBody CustomerESPageWebDto customerESPageWebDto) {
        //默认排序项
        if (StrUtil.isEmpty(customerESPageWebDto.getOrderBy())) {
            //customerESPageWebDto.setOrderBy("lastFollowTime:desc,lastSdrFollowTime:desc,leadsCreateTime:desc,protectTime:desc,releaseTime:desc");
            customerESPageWebDto.setOrderBy("tagScore:desc");
        }

        //处理市场角色的所属区域
        setMarketCityCode(customerESPageWebDto);

        //处理省市区
        processProvinceCityDistrict(customerESPageWebDto);

        //处理标签列表
        processTagList(customerESPageWebDto);

        //请求筛选项处理
        processFilter(customerESPageWebDto);

        //处理国标行业
        processIndustryCode(customerESPageWebDto);

        CustomerESPageBusinessDto dto = BeanUtil.copyProperties(customerESPageWebDto, CustomerESPageBusinessDto.class);
        dto.setStatus("4");
        DubboPageInfo<CustomerESBusinessView> dubboPageInfo = customerBusiness.pageListFromCustomerES(dto);
        WebPageInfo<CustomerESWebView> webPageInfo = new WebPageInfo<>();
        BeanUtil.copyProperties(dubboPageInfo, webPageInfo);
        webPageInfo.setList(BeanCopyUtils.convertToVoList(Optional.ofNullable(dubboPageInfo.getList())
                .orElse(Collections.emptyList()), CustomerESWebView.class));

        List<CustomerESWebView> listView = webPageInfo.getList();
        if (CollectionUtil.isNotEmpty(listView)) {
            listView.forEach(item -> Optional.ofNullable(item.getContactPersonList())
                    .orElse(Collections.emptyList())
                    .forEach(contact -> {
                        if (StrUtil.isNotEmpty(contact.getPhone())) contact.setPhone(maskMobile(contact.getPhone()));
                        if (StrUtil.isNotEmpty(contact.getEmail())) contact.setEmail(maskEmail(contact.getEmail()));
                        if (StrUtil.isNotEmpty(contact.getWechat())) contact.setWechat(maskWechat(contact.getWechat()));
                    })
            );
        }

        formatRemainTime(webPageInfo.getList());
        return WebResult.success(webPageInfo);
    }

    /**
     * <AUTHOR>
     * @date 2025/8/5 21:33:32
     * @desc 销售保护列表, 我的客户列表, 要判断销售角色
     * 1.商务
     * protect_status=1 && protect_saler_id=商务ID
     * 2.商务经理
     * protect_status=1 && dept_id=部门ID
     * 3.事业部总监
     * protect_status=1 && bu_id=事业部ID
     * 4.分司总监
     * protect_status=1 && sub_id=分司ID
     * 5.区总
     * protect_status=1 && area_id=区域ID
     * 6.管理员
     * protect_status=1
     */
    @PostMapping("/saler/protect/list")
    public WebResult<WebPageInfo<CustomerESWebView>> salerProtectList(@Valid @RequestBody CustomerESPageWebDto customerESPageWebDto) {
        //默认排序项
        if (StrUtil.isEmpty(customerESPageWebDto.getOrderBy())) {
            //customerESPageWebDto.setOrderBy("lastFollowTime:desc,lastSdrFollowTime:desc,leadsCreateTime:desc,protectTime:desc,releaseTime:desc");
            customerESPageWebDto.setOrderBy("tagScore:desc");
        }

        //处理省市区,暂时可能没有此条件查询
        processProvinceCityDistrict(customerESPageWebDto);
        //处理标签列表
        processTagList(customerESPageWebDto);
        //时间选项处理
        processFilter(customerESPageWebDto);

        //成交客户选项
        if (Objects.equals(customerESPageWebDto.getSignFlag(), "1")) {
            customerESPageWebDto.setTradeProductType("3,4");
        }
        if (Objects.equals(customerESPageWebDto.getSignFlag(), "0")) {
            customerESPageWebDto.setTradeProductType("!3,4");
        }

        //权限判断
        String posId = customerESPageWebDto.getLoginPosition();
        String empId = customerESPageWebDto.getLoginEmployeeId();
        String deptId = customerESPageWebDto.getLoginOrgId();
        String buId = customerESPageWebDto.getLoginBuId();
        String subId = customerESPageWebDto.getLoginSubId();
        String areaId = customerESPageWebDto.getLoginAreaId();

        CustomerESPageBusinessDto dto = BeanUtil.copyProperties(customerESPageWebDto, CustomerESPageBusinessDto.class);
        dto.setStatus("1");
        if (PositionUtil.isBusinessSaler(posId)) {
            dto.setSalerId(empId);
        } else if (PositionUtil.isBusinessManager(posId)) {
            dto.setDeptId(deptId);
        } else if (PositionUtil.isBusinessBu(posId)) {
            dto.setBuId(buId);
        } else if (PositionUtil.isBusinessMajor(posId)) {
            dto.setSubId(subId);
        } else if (PositionUtil.isBusinessArea(posId)) {
            dto.setAreaId(areaId);
        }
        DubboPageInfo<CustomerESBusinessView> dubboPageInfo = customerBusiness.pageListFromCustomerES(dto);
        WebPageInfo<CustomerESWebView> webPageInfo = new WebPageInfo<>();
        BeanUtil.copyProperties(dubboPageInfo, webPageInfo);
        webPageInfo.setList(BeanCopyUtils.convertToVoList(Optional.ofNullable(dubboPageInfo.getList())
                .orElse(Collections.emptyList()), CustomerESWebView.class));

        formatRemainTime(webPageInfo.getList());
        return WebResult.success(webPageInfo);
    }

    /**
     * <AUTHOR>
     * @date 2025/8/5 21:34:47
     * @desc sdr保护列表，sdr工作台
     * protect_status=1 && protect_saler_id=xxx
     */
    @PostMapping("/sdr/protect/list")
    public WebResult<WebPageInfo<CustomerESWebView>> sdrProtectList(@Valid @RequestBody CustomerESPageWebDto customerESPageWebDto) {

        String posId = customerESPageWebDto.getLoginPosition();
        String empId = customerESPageWebDto.getLoginEmployeeId();
        if (!PositionUtil.isSdr(posId) && !PositionUtil.isCc(posId)) {
            WebPageInfo<CustomerESWebView> webPageInfo = new WebPageInfo<>();
            return WebResult.success(webPageInfo);
        }
        //默认排序项
        if (StrUtil.isEmpty(customerESPageWebDto.getOrderBy())) {
            //customerESPageWebDto.setOrderBy("lastFollowTime:desc,lastSdrFollowTime:desc,leadsCreateTime:desc,protectTime:desc,releaseTime:desc");
            customerESPageWebDto.setOrderBy("tagScore:desc");
        }
        //处理跟进次数
        if (null != customerESPageWebDto.getSdrFollowCountFilter()) {
            customerESPageWebDto.setSdrFollowCount(CustomerSdrFollowCountFilterEnum.getCount(customerESPageWebDto.getSdrFollowCountFilter()));
        }

        //处理省市区
        processProvinceCityDistrict(customerESPageWebDto);

        //处理标签列表
        processTagList(customerESPageWebDto);

        //请求筛选项处理
        processFilter(customerESPageWebDto);

        //权限判断
        CustomerESPageBusinessDto dto = BeanUtil.copyProperties(customerESPageWebDto, CustomerESPageBusinessDto.class);
        dto.setStatus("1");
        dto.setSalerId(empId);
        DubboPageInfo<CustomerESBusinessView> dubboPageInfo = customerBusiness.pageListFromCustomerES(dto);
        WebPageInfo<CustomerESWebView> webPageInfo = new WebPageInfo<>();
        BeanUtil.copyProperties(dubboPageInfo, webPageInfo);
        webPageInfo.setList(BeanCopyUtils.convertToVoList(Optional.ofNullable(dubboPageInfo.getList())
                .orElse(Collections.emptyList()), CustomerESWebView.class));

        formatRemainTime(webPageInfo.getList());

        return WebResult.success(webPageInfo);
    }

    /**
     * <AUTHOR>
     * @date 2025/8/5 21:35:20
     * @desc sdr主管工作台
     * 1.部门所有sdr
     * protect_status=1 && dept_id=xxx
     * 2.改部门下的某一个sdr
     * protect_status=1 && dept_id=xxx and protect_saler_id=xxx
     */
    @PostMapping("/sdrmanager/protect/list")
    public WebResult<WebPageInfo<CustomerESWebView>> sdrManagerProtectList(@Valid @RequestBody CustomerESPageWebDto customerESPageWebDto) {

        String posId = customerESPageWebDto.getLoginPosition();
        String deptId = customerESPageWebDto.getLoginOrgId();
        if (!PositionUtil.isSdrZg(posId) && !PositionUtil.isCcZg(posId)) {
            WebPageInfo<CustomerESWebView> webPageInfo = new WebPageInfo<>();
            return WebResult.success(webPageInfo);
        }
        //默认排序项
        if (StrUtil.isEmpty(customerESPageWebDto.getOrderBy())) {
            //customerESPageWebDto.setOrderBy("lastFollowTime:desc,lastSdrFollowTime:desc,leadsCreateTime:desc,protectTime:desc,releaseTime:desc");
            customerESPageWebDto.setOrderBy("tagScore:desc");
        }
        //sdr跟进时间和分发时间都是时间区间方式,无需处理

        //处理省市区
        processProvinceCityDistrict(customerESPageWebDto);

        //处理标签列表
        processTagList(customerESPageWebDto);

        //请求筛选项处理
        processFilter(customerESPageWebDto);
        //权限判断
        CustomerESPageBusinessDto dto = BeanUtil.copyProperties(customerESPageWebDto, CustomerESPageBusinessDto.class);
        dto.setStatus("1");
        if (PositionUtil.isSdrZg(posId) || PositionUtil.isCcZg(posId)) {
            dto.setDeptId(deptId);
        }
        DubboPageInfo<CustomerESBusinessView> dubboPageInfo = customerBusiness.pageListFromCustomerES(dto);
        WebPageInfo<CustomerESWebView> webPageInfo = new WebPageInfo<>();
        BeanUtil.copyProperties(dubboPageInfo, webPageInfo);
        webPageInfo.setList(BeanCopyUtils.convertToVoList(Optional.ofNullable(dubboPageInfo.getList())
                .orElse(Collections.emptyList()), CustomerESWebView.class));

        formatRemainTime(webPageInfo.getList());
        return WebResult.success(webPageInfo);
    }


    /**
     * <AUTHOR>
     * @date 2025/8/8 21:25:00
     * @desc 获取客户更多联系人的 call status
     */
    @PostMapping("/contactperson/more")
    public WebResult<List<ContactPersonBusinessView>> moreContacts(@Valid @RequestBody CustomerContactWebDto dto) {
        ContactPersonCallStatusDto contactDto = BeanUtil.copyProperties(dto, ContactPersonCallStatusDto.class);
        List<ContactPersonBusinessView> list = customerBusiness.getMoreContactWithCallStatus(contactDto, true);

        //公海的客户的联系方式要遮盖
        if (Objects.equals("4", dto.getStatus())) {
            if (CollectionUtil.isNotEmpty(list)) {
                list.forEach(contact -> {
                    if (StrUtil.isNotEmpty(contact.getPhone())) contact.setPhone(maskMobile(contact.getPhone()));
                    if (StrUtil.isNotEmpty(contact.getEmail())) contact.setEmail(maskEmail(contact.getEmail()));
                    if (StrUtil.isNotEmpty(contact.getWechat())) contact.setWechat(maskWechat(contact.getWechat()));
                });
            }

        }
        return WebResult.success(list);
    }

    @GetMapping("/getEnumsByTag")
    public WebResult<JSONArray> getEnumsByTag(@RequestParam String enumTag) {
        JSONArray result = new JSONArray();
        if (StringUtils.isEmpty(enumTag)) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, "枚举类型不能为空");
        }
        switch (enumTag) {
            case "FOREIGNTRADE_FILTER":
                result = enumToJSONArray(CustomerForeignTradeFilterEnum.values());
                break;
            case "OFFLINEEX_FILTER":
                result = enumToJSONArray(CustomerOfflineExFilterEnum.values());
                break;
            case "CBEC_FILTER":
                result = enumToJSONArray(CustomerCBECFilterEnum.values());
                break;
            case "INTENT_FILTER":
                result = enumToJSONArray(CustomerIntentFilterEnum.values());
                break;
            case "CUSTOMERCREATETIME_FILTER":
                result = enumToJSONArray(CustomerCreateTimeFilterEnum.values());
                break;
            case "ESTABLISHDATE_FILTER":
                result = enumToJSONArray(CustomerEstablishDateFilterEnum.values());
                break;
            case "LASTFOLLOWTIME_FILTER":
                result = enumToJSONArray(CustomerLastFollowTimeFilterEnum.values());
                break;
            case "LASTSITETIME_FILTER":
                result = enumToJSONArray(CustomerLastSiteTimeFilterEnum.values());
                break;
            case "LEADSCREATETIME_FILTER":
                result = enumToJSONArray(CustomerLeadsCreateTimeFilterEnum.values());
                break;
            case "PROTECTTIMER_FILTER":
                result = enumToJSONArray(CustomerProtectTimeFilterEnum.values());
                break;
            case "RELEASETIME_FILTER":
                result = enumToJSONArray(CustomerReleaseTimeFilterEnum.values());
                break;
            case "REMAINPROTECTTIME_FILTER":
                result = enumToJSONArray(CustomerRemainProtectFilterEnum.values());
                break;
            case "SDRFOLLOWCOUNT_FILTER":
                result = enumToJSONArray(CustomerSdrFollowCountFilterEnum.values());
                break;
            default:
                return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL, "枚举类型不存在");
        }
        log.info("获取枚举resp:{}", result);
        return WebResult.success(result);
    }
    /**
     * <AUTHOR>
     * @date 2025/8/6 17:16:52
     * @desc 遮盖手机号
     */
    private String maskMobile(String mobile) {
        if (mobile == null) return null;
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    /**
     * <AUTHOR>
     * @date 2025/8/6 17:18:35
     * @desc 遮盖邮箱号
     * 邮箱：首字 +*...* + 尾字 +@完整域名(e.g.,z*****<EMAIL>)
     */
    private String maskEmail(String email) {
        if (email == null || !email.contains("@")) return email;

        Pattern p = Pattern.compile("^(.)(.*)(.)@(.+)$");
        Matcher m = p.matcher(email);
        if (m.find()) {
            String first = m.group(1);
            String middle = m.group(2);
            String last = m.group(3);
            String domain = m.group(4);

            String stars = new String(new char[middle.length()]).replace("\0", "*");
            return first + stars + last + "@" + domain;
        }
        return email;
    }

    /**
     * <AUTHOR>
     * @date 2025/8/6 17:18:53
     * @desc 遮盖微信号
     * 微信号：前1位 +*...* + 后1位 (e.g.,j****2)
     */
    private String maskWechat(String wechat) {
        if (wechat == null || wechat.length() <= 1) return wechat;
        if (wechat.length() == 2) {  // 2位：保留首尾，中间1个*
            return wechat.charAt(0) + "*" + wechat.charAt(1);
        }
        // 长度 >=3
        String first = wechat.substring(0, 1);
        String last = wechat.substring(wechat.length() - 1);
        String middle = wechat.substring(1, wechat.length() - 1);

        String stars = new String(new char[middle.length()]).replace("\0", "*");
        return first + stars + last;
    }

    /**
     * <AUTHOR>
     * @date 2025/8/11 14:38:47
     * @desc 格式化剩余时长
     */
    private void formatRemainTime(List<CustomerESWebView> list) {
        list.forEach(view -> {
            //剩余保护时长
            if (null != view.getProtectEndTime()) {
                Date now = new Date();
                if (now.before(view.getProtectEndTime())) {
                    long betweenMs = DateUtil.betweenMs(now, view.getProtectEndTime());
                    // 转换为天、小时、分钟等格式
                    String protectRemaining = DateUtil.formatBetween(betweenMs, BetweenFormatter.Level.MINUTE);
                    view.setRemainProtectTime(protectRemaining);
                }
            }
            //回执倒计时
            if (null != view.getReceiptEndTime()) {
                if (!"1".equalsIgnoreCase(view.getStatus())) {
                    return;
                } else {
                    if (!Objects.equals(view.getReceiptSalerId(), view.getSalerId())) {
                        return;
                    } else {
                        if (Objects.equals(view.getReceiptFlag(), 1)) {
                            return;
                        }
                    }
                }
                Date now = new Date();
                if (now.before(view.getReceiptEndTime())) {
                    long betweenMs = DateUtil.betweenMs(now, view.getReceiptEndTime());
                    // 转换为天、小时、分钟等格式
                    String receiptCountdown = DateUtil.formatBetween(betweenMs, BetweenFormatter.Level.SECOND);
                    view.setRemainReceiptTime(receiptCountdown);
                }
            }
        });
    }

    private void processTagList(CustomerESPageWebDto customerESPageWebDto) {
        List<String> tags = new ArrayList<>();
        if (StrUtil.isNotEmpty(customerESPageWebDto.getCbecFilter())) {
            Arrays.stream(customerESPageWebDto.getCbecFilter().split(","))
                    .filter(tag -> !tag.isEmpty())
                    .filter(tag -> !tag.equalsIgnoreCase("0"))
                    .forEach(tag ->
                            tags.add(tag.trim()));
        }
        if (StrUtil.isNotEmpty(customerESPageWebDto.getForeignTradeFilter())) {
            Arrays.stream(customerESPageWebDto.getForeignTradeFilter().split(","))
                    .filter(tag -> !tag.isEmpty())
                    .filter(tag -> !tag.equalsIgnoreCase("0"))
                    .forEach(tag ->
                            tags.add(tag.trim()));
        }
        if (StrUtil.isNotEmpty(customerESPageWebDto.getOfflineExFilter())) {
            Arrays.stream(customerESPageWebDto.getOfflineExFilter().split(","))
                    .filter(tag -> !tag.isEmpty())
                    .filter(tag -> !tag.equalsIgnoreCase("0"))
                    .forEach(tag ->
                            tags.add(tag.trim()));
        }
        if (StrUtil.isNotEmpty(customerESPageWebDto.getIntentFilter())) {
            String intent = getTagsFromIntentFilter(customerESPageWebDto.getIntentFilter());
            Arrays.stream(intent.split(","))
                    .filter(tag -> !tag.isEmpty())
                    .filter(tag -> !tag.equalsIgnoreCase("0"))
                    .forEach(tag ->
                            tags.add(tag.trim()));
        }
        if(CollUtil.isNotEmpty(tags)){
            customerESPageWebDto.setTagList(String.join(",", tags));
        }
    }

    public String getTagsFromIntentFilter(String labels) {
        if (labels == null || labels.trim().isEmpty()) {
            return null;
        }
        // 分割原始标签
        String[] labelArray = labels.split(",");
        List<String> tags = new ArrayList<>();
        List<Long> ids = new ArrayList<>();

        // 分类标签
        for (String label : labelArray) {
            String trimmedLabel = label.trim();
            if (!trimmedLabel.isEmpty()) { // 忽略空标签
                if (trimmedLabel.startsWith("tag")) {
                    tags.add(trimmedLabel);
                } else {
                    Long id = safeParseLong(trimmedLabel);
                    if (null != id) {
                        ids.add(id);
                    }
                }
            }
        }

        Map<String, List<CustomerTagDubboView>> map = customerTagsThirdService.getTagsByCategoryIds(ids);
        Set<String> tagCodes = map.values()
                .stream()
                .filter(Objects::nonNull)  // 过滤掉null的List
                .flatMap(List::stream)
                .map(CustomerTagDubboView::getTagCode)
                .filter(Objects::nonNull)  // 过滤掉null的tagId
                .collect(Collectors.toSet());

        if (CollUtil.isNotEmpty(tagCodes)) {
            tags.addAll(tagCodes);
        }
        return String.join(",", tags);
    }

    private Long safeParseLong(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }
        try {
            return Long.parseLong(str.trim());
        } catch (NumberFormatException e) {
            return null; // 或者抛出特定业务异常
        }
    }

    /**
     * <AUTHOR>
     * @date 2025/8/13 11:14:02
     * @desc 时间筛选项处理
     */
    private void processFilter(CustomerESPageWebDto customerESPageWebDto) {

        //时间筛选项处理
        if (null != customerESPageWebDto.getEstablishDateFilter()) {
            customerESPageWebDto.setEstablishDate(CustomerEstablishDateFilterEnum.getDateRange(customerESPageWebDto.getEstablishDateFilter()));
        }
        if (null != customerESPageWebDto.getCreateTimeFilter()) {
            customerESPageWebDto.setCreateTime(CustomerCreateTimeFilterEnum.getDateRange(customerESPageWebDto.getCreateTimeFilter()));
        }
        if (null != customerESPageWebDto.getReleaseTimeFilter()) {
            customerESPageWebDto.setReleaseTime(CustomerReleaseTimeFilterEnum.getDateRange(customerESPageWebDto.getReleaseTimeFilter()));
        }
        if (null != customerESPageWebDto.getLastFollowTimeFilter()) {
            customerESPageWebDto.setLastFollowTime(CustomerLastFollowTimeFilterEnum.getDateRange(customerESPageWebDto.getLastFollowTimeFilter()));
        }
        if (null != customerESPageWebDto.getLastSiteTimeFilter()) {
            customerESPageWebDto.setLastSiteTime(CustomerLastSiteTimeFilterEnum.getDateRange(customerESPageWebDto.getLastSiteTimeFilter()));
        }
        if (null != customerESPageWebDto.getLeadsCreateTimeFilter()) {
            customerESPageWebDto.setLeadsCreateTime(CustomerLeadsCreateTimeFilterEnum.getDateRange(customerESPageWebDto.getLeadsCreateTimeFilter()));
        }

        //我的保护中的筛选项
        if (null != customerESPageWebDto.getProtectTimeFilter()) {
            customerESPageWebDto.setProtectTime(CustomerProtectTimeFilterEnum.getDateRange(customerESPageWebDto.getProtectTimeFilter()));
        }
        if (null != customerESPageWebDto.getRemainProtectFilter()) {
            customerESPageWebDto.setProtectEndTime(CustomerReleaseTimeFilterEnum.getDateRange(customerESPageWebDto.getRemainProtectFilter()));
        }

        //处理跟进次数
        if (null != customerESPageWebDto.getSdrFollowCountFilter()) {
            customerESPageWebDto.setSdrFollowCount(CustomerSdrFollowCountFilterEnum.getCount(customerESPageWebDto.getSdrFollowCountFilter()));
        }

    }

    /**
     * <AUTHOR>
     * @date 2025/8/13 14:31:06
     * @desc 处理国标行业编码，由于行业分级，需要采用如下查询方式
     * 其他查询条件 and (firstIndustryCode in(xx,xx) or secondIndustryCode in(xx,xx) or ...) and 其他查询条件
     */
    private void processIndustryCode(CustomerESPageWebDto customerESPageWebDto) {
        if (StrUtil.isNotEmpty(customerESPageWebDto.getFirstIndustryCode())) {
            customerESPageWebDto.setFirstIndustryCode("|in:" + customerESPageWebDto.getFirstIndustryCode());
        }
        if (StrUtil.isNotEmpty(customerESPageWebDto.getSecondIndustryCode())) {
            customerESPageWebDto.setSecondIndustryCode("|in:" + customerESPageWebDto.getSecondIndustryCode());
        }
        if (StrUtil.isNotEmpty(customerESPageWebDto.getThirdIndustryCode())) {
            customerESPageWebDto.setThirdIndustryCode("|in:" + customerESPageWebDto.getThirdIndustryCode());
        }
        if (StrUtil.isNotEmpty(customerESPageWebDto.getFourthIndustryCode())) {
            customerESPageWebDto.setFourthIndustryCode("|in:" + customerESPageWebDto.getFourthIndustryCode());
        }
    }

    /**
     * <AUTHOR>
     * @date 2025/8/14 18:29:42
     * @desc 处理省市区查询条区
     * ES查询条件：xxx and (provinceCode in(xx,xx) or cityCode in (xx,xx) or districtCode in(xx,xx)) and xxx
     */
    private void processProvinceCityDistrict(CustomerESPageWebDto customerESPageWebDto) {
        if(StrUtil.isNotEmpty(customerESPageWebDto.getProvinceCode())){
           customerESPageWebDto.setProvinceCode("|pcd:" + customerESPageWebDto.getProvinceCode());
        }
        if(StrUtil.isNotEmpty(customerESPageWebDto.getCityCode())){
            customerESPageWebDto.setCityCode("|pcd:" + customerESPageWebDto.getCityCode());
        }
        if(StrUtil.isNotEmpty(customerESPageWebDto.getDistrictCode())){
            customerESPageWebDto.setDistrictCode("|pcd:" + customerESPageWebDto.getDistrictCode());
        }
    }

    /**
     * <AUTHOR>
     * @date 2025/8/14 15:35:16
     * @desc 根据区域Id获取分司Id列表
     */
    private List<String> getSubIdList(String areaId){
       List<OrgVo> listOrg = orgAppService.getAllSubCompanyByAreaCode(areaId);
       return listOrg.stream().map(OrgVo::getId).collect(Collectors.toList());
    }

    /**
     * <AUTHOR>
     * @date 2025/8/14 15:34:22
     * @desc 获取商务体系所负责的区域Id列表
     */
    private void setMarketCityCode(CustomerESPageWebDto customerESPageWebDto){

        String posId = customerESPageWebDto.getLoginPosition();
        String subId = customerESPageWebDto.getLoginSubId();
        String areaId = customerESPageWebDto.getLoginAreaId();

        List<String> subIdList = new ArrayList<>();
        subIdList.add(subId);
        //角色是区总的话，需要获取所有分司的ID
        if (PositionUtil.isBusinessArea(posId)) {
            subIdList = getSubIdList(areaId);
        }
        SmaMarketSubcompanyQuery condition = new SmaMarketSubcompanyQuery();
        condition.setSubIdList(subIdList);
        List<SmaMarketSubcompanyView> smaMarketSubcompanyViewList = smaMarketSubcompanyService.selectByCondition(condition);
        List<String> marketIdList = smaMarketSubcompanyViewList.stream().map(SmaMarketSubcompanyView::getMarketId).collect(Collectors.toList());
        if(CollUtil.isEmpty(marketIdList)){
            return;
        }

        SmaMarketAreaQuery smaMarketAreaQuery = new SmaMarketAreaQuery();
        smaMarketAreaQuery.setMarketIdList(marketIdList);
        List<SmaMarketAreaView> smaMarketAreaViewList = smaMarketAreaService.selectByCondition(smaMarketAreaQuery);
        if(CollUtil.isEmpty(smaMarketAreaViewList)){
            return;
        }
        Set<String> citySet = new HashSet<>();

        smaMarketAreaViewList.forEach(smaMarketAreaView -> {
            citySet.add(smaMarketAreaView.getCityCode());
        });
        if(CollUtil.isNotEmpty(citySet)){
            customerESPageWebDto.setMarketCityCode(String.join(",", citySet));
        }
    }

    public static <E extends Enum<E> & EnumBase> JSONArray enumToJSONArray(E[] enumValues) {
        JSONArray jsonArray = new JSONArray();
        Arrays.stream(enumValues).forEach(enumValue -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("label", enumValue.getValue());
            jsonObject.put("value", enumValue.getKey());
            jsonArray.add(jsonObject);
        });
        return jsonArray;
    }
}
