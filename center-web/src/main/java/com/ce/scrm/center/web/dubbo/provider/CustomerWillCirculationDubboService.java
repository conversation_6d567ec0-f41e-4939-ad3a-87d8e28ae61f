package com.ce.scrm.center.web.dubbo.provider;

import com.ce.scrm.center.dubbo.api.CustomerWillCirculationDubbo;
import com.ce.scrm.center.dubbo.entity.dto.CirculationLossDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.service.business.CirculationLossBusiness;
import com.ce.scrm.center.service.business.entity.dto.CirculationLossUpdateBusinessDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 客户流失流转dubbo实现
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/7/23
 */
@Slf4j
@DubboService(interfaceClass = CustomerWillCirculationDubbo.class)
public class CustomerWillCirculationDubboService implements CustomerWillCirculationDubbo {
    @Resource
    private CirculationLossBusiness circulationLossBusiness;


    /**
     * Description: 判断是否是流失客户
     *
     * @param custId 客户id
     * @return
     * @author: liyechao
     */
    @Override
    public DubboResult<Boolean> isLossCust(String custId) {
        Optional<Boolean> lossCust = circulationLossBusiness.isLossCust(custId);
        if (lossCust.isPresent() && lossCust.get()) {
            return DubboResult.success(true);
        }
        return DubboResult.success(false);
    }

    /**
     * 更新流失表
     *
     * @param circulationLossDubboDto
     * @return
     */
    @Override
    public void updateCustomerWillCirculation(CirculationLossDubboDto circulationLossDubboDto) {
        CirculationLossUpdateBusinessDto circulationLossUpdateBusinessDto = new CirculationLossUpdateBusinessDto();
        BeanUtils.copyProperties(circulationLossDubboDto, circulationLossUpdateBusinessDto);
        circulationLossBusiness.updateCirculationLoss(circulationLossUpdateBusinessDto);
    }
}