package com.ce.scrm.center.web.entity.dto.abm;

import com.ce.scrm.center.web.aop.base.LoginInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 客户营销列表请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerLeadsPageWebDto extends LoginInfo implements Serializable {

	/**
	 * 客户id
	 */
	@NotBlank(message = "客户id不能为空")
	private String customerId;

	/**
	 * 页码
	 */
	private Integer pageNum = 1;

	/**
	 * 每页数量
	 */
	private Integer pageSize = 10;

}
