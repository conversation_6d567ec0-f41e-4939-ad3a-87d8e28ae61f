spring:
  datasource:
    url: ***************************************************************************************************************************************************************************************************
    username: MYS<PERSON>(hs930YRVa7tBBmikUYezNw==)
    password: MYSQL(06NOLLrj3J7RK3LoWUJ5nczQkaXzG0MZ)
  redis:
    password: mpRedis@019
    cluster:
      nodes: redis.ep:7000,redis.ep:7001,redis.ep:7002

dubbo:
  registry:
    address: ************:2181,************:2181,************:2181

sequence:
  zkAddress: ************
  zkPort: 2181

xxl:
  job:
    accessToken:
    admin:
      #调度中心地址
      addresses: http://pre-omo.aiyouyi.cn/xxl-job-admin/
    executor:
      appname: scrm-center-job-pre
      address:
      ip:
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 3
      port: 9359
      oneTimesJob:
        timeout: 10000

#业务平台
#rocketmq:
#  name-server: ************:9876
#  producer:
#    accessKey: b96c0783-1040-497e-93f7-c183768add07rfBd56
#    secretKey: c07bca4a-8a21-480b-a4a9-3ea49095cf4b
#  consumer:
#    accessKey: b96c0783-1040-497e-93f7-c183768add07rfBd56
#    secretKey: c07bca4a-8a21-480b-a4a9-3ea49095cf4b

#商城
rocketmq:
  name-server: mq.ep:9876
  producer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)
  consumer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)

mq:
  prefix: TEST_

nacos:
  config:
    server-addr: common.et:8848
    username: NACOS(oWUVxx3OeK0iuxxdbVU4Hw==)
    password: NACOS(H9klTNl9ZJoALwkWQzmeST4Wx7eEnHUiDC7BdC8YfWU=)
    namespace: pre

ce:
  sso:
    client:
      ssoServer: https://test-passportceboss.300.cn/CAS
      redisHosts: ************:7000,************:7001,************:7002,************:7003,************:7004,************:7005
      redisPassword: LyjB7]vUyoZ
      clientDomain: https://test-scrm.ceboss.cn
      name: scrmSsoFilter
sso:
  excludedPaths: /entwx/*

logging:
  config: classpath:logback-pre.xml
  level:
    com.ce.scrm.center.service: debug
    com.ce.scrm.center.dao.mapper.AiPromptInfoMapper: debug
    com.ce.scrm.center.dao.mapper.AiEventLogMapper: debug
    com.ce.scrm.center.dao.mapper.AiVoiceAnalyzeMapper: debug
    com.ce.scrm.center.dao.service.AiPromptInfoService: debug
    com.ce.scrm.center.dao.service.impl.AiPromptInfoServiceImpl: debug
    org.springframework: warn
    org.apache.ibatis.logging: debug
    com.baomidou.mybatisplus: debug  # 打印 MyBatis-Plus 日志


robot:
  force-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d199f33a-949e-4370-8eee-c573a51d185e

bigdata:
  enterpriseInfo: https://gateway.datauns.cn/ce22highlevelsearch/highSearch/baseinfo/company_detail?company_name=

third:
  customer:
    secret: 31b9763b659c49489d348c2e6a261a64
  file:
    url: http://test-cesupport-images.ceboss.cn/upload

wx:
  isopen: 1

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl #开启sql日志

#易企秀
ecmp:
  signatureKey: ktWVfoqNCSxEQU47
  encodingKey: og9pVN52dCeCXoEFx0sTbXTamcVkhx7SMi2LX1MoLwe
  secretKey: qOdqk33y8F6mMankOrEUl5Vh3WUQM5io
  secretId: 5222YVS

auth:
  login:
    kuajing:
      secret: jhs_dnsadkas-0w3
    cesupport:
      secret: jhs_dnsadkas-0w3

sensors:
  #projectId: 3
  projectId: 2

file:
  upload:
    dir: /data/share/www/offline/upload
    url: http://test-cesupport-images.ceboss.cn/upload


#企微保护列表
abm:
  protectionListUrl: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wwf1c477c862181a7f&redirect_uri=https%3A%2F%2Ftest-ewx-crm.ceboss.cn%2Fwecom-scrm%2Ffollow-client%2Flist&response_type=code&scope=snsapi_base&state=N2Wsxr#wechat_redirect