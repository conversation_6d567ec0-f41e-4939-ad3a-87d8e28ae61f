package com.ce.scrm.center.web;

import cn.ce.cesupport.enums.GcSjInitiatorEnum;
import cn.ce.cesupport.enums.GcSjOperateLogTypeEnum;
import cn.ce.cesupport.enums.GcSjSourceEnum;
import cn.ce.cesupport.enums.GcSjStateEnum;
import cn.hutool.core.util.StrUtil;
import com.ce.scrm.center.ScrmCenterWebApplication;
import com.ce.scrm.center.dao.entity.GcBusinessOpportunity;
import com.ce.scrm.center.dao.entity.GcBusinessOpportunityLog;
import com.ce.scrm.center.service.enums.GcSjRequirementEnum;
import com.ce.scrm.center.web.entity.GcEmployeeData;
import com.ce.scrm.center.web.enums.DataSourceEnum;
import com.ce.scrm.center.web.util.DataSourceUtil;
import com.ce.scrm.center.web.util.GcOrgDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 迁移高呈商机操作日志
 * <AUTHOR>
 * @date 2024/5/31 上午11:29
 * @version 1.0.0
 */
@Slf4j
@SpringBootTest(classes = ScrmCenterWebApplication.class)
public class ClearGcSjDataTests {

    private final static boolean CLOSE_STATE = true;

    private final static DataSourceEnum extendDataSource = DataSourceEnum.EXTEND_PROD;

    private final static DataSourceEnum smaDataSource = DataSourceEnum.SMA_PROD;

    private final static Integer PAGE_SIZE = 2000;

    @Test
    public void clearGcSj() throws SQLException {
        if (CLOSE_STATE) {
            return;
        }
        log.info("迁移高呈商机开始");
        List<GcBusinessOpportunity> gcBusinessOpportunityList = getAllData();
        log.info("迁移高呈商机--数据拼装完成");
        batchInsertData(gcBusinessOpportunityList);
        log.info("迁移高呈商机结束");
    }


    /**
     * 迁移操作日志
     */
    @Test
    public void clearGcSjOperateLog() throws SQLException {
        if (CLOSE_STATE) {
            return;
        }
        log.info("迁移高呈商机日志开始");
        List<GcBusinessOpportunityLog> gcBusinessOpportunityLogList = getAllLogData();
        log.info("迁移高呈商机日志--数据拼装完成");
        batchInsertLogData(gcBusinessOpportunityLogList);
        log.info("迁移高呈商机日志结束");
    }

    /**
     * 获取所有高呈商机数据，并转换成为新的表数据
     * <AUTHOR>
     * @date 2024/6/3 下午1:45
     * @return java.util.List<com.ce.scrm.center.dao.entity.GcBusinessOpportunity>
     **/
    private List<GcBusinessOpportunity> getAllData() throws SQLException {
        List<GcBusinessOpportunity> list = new ArrayList<>();
        Map<Long, String> refuseReason = getRefuseReason();
        int page = 1;
        while (true) {
            // 计算偏移量
            int offset = (page - 1) * PAGE_SIZE;
            // SQL 查询语句，使用 LIMIT 和 OFFSET 实现分页
            String query = "SELECT * FROM `t_sj_to_omp` where source = 1 LIMIT ? OFFSET ?";
            Connection connection = DataSourceUtil.getConnection(smaDataSource);
            PreparedStatement preparedStatement = connection.prepareStatement(query);
            // 设置 LIMIT 和 OFFSET 参数
            preparedStatement.setInt(1, PAGE_SIZE);
            preparedStatement.setInt(2, offset);
            // 执行查询
            ResultSet resultSet = preparedStatement.executeQuery();
            boolean existData = false;
            // 处理结果集
            while (resultSet.next()) {
                existData = true;
                GcBusinessOpportunity gcBusinessOpportunity = new GcBusinessOpportunity();
                gcBusinessOpportunity.setId(resultSet.getLong("ID"));
                gcBusinessOpportunity.setCustomerId(resultSet.getString("CUST_ID"));
                gcBusinessOpportunity.setCustomerName(resultSet.getString("CUST_NAME"));
                gcBusinessOpportunity.setInitiator(GcSjInitiatorEnum.INITIATOR_ZX.getInitiatorType());
                gcBusinessOpportunity.setSource(GcSjSourceEnum.SUB.getSource());
                int approvalState = resultSet.getInt("APPROVAL_STATE");
                GcSjStateEnum gcSjStateEnum;
                switch (approvalState) {
                    case 0:
                        gcSjStateEnum = GcSjStateEnum.UNCOMMIT;
                        break;
                    case 1:
                        gcSjStateEnum = GcSjStateEnum.WAIT_MAJOR_CHECK;
                        break;
                    case 2:
                        gcSjStateEnum = GcSjStateEnum.MAJOR_CHECK_REFUSE;
                        break;
                    default:
                        String ompFollowsStateCode = resultSet.getString("OMP_FOLLOWS_STATE_CODE");
                        if (StrUtil.isBlank(ompFollowsStateCode)) {
                            gcSjStateEnum = GcSjStateEnum.NON_HANDLE;
                            break;
                        }
                        switch (ompFollowsStateCode) {
                            case "ce_unhandled":
                                gcSjStateEnum = GcSjStateEnum.NON_HANDLE;
                                break;
                            case "ce_recieved":
                                gcSjStateEnum = GcSjStateEnum.ACCEPT;
                                break;
                            case "ce_return_back":
                                gcSjStateEnum = GcSjStateEnum.SEND_BACK;
                                break;
                            case "ce_following":
                                gcSjStateEnum = GcSjStateEnum.FOLLOW;
                                break;
                            case "ce_signed":
                                gcSjStateEnum = GcSjStateEnum.ALREADY_SIGN;
                                break;
                            default:
                                gcSjStateEnum = GcSjStateEnum.FOLLOW;
                        }
                }
                gcBusinessOpportunity.setState(gcSjStateEnum.getState());
                gcBusinessOpportunity.setRefuseReason(refuseReason.getOrDefault(gcBusinessOpportunity.getId(), ""));
                String linkmanName = resultSet.getString("LINKMAN_NAME");
                if (StrUtil.isBlank(linkmanName)) {
                    linkmanName = "";
                }
                gcBusinessOpportunity.setLinkmanName(linkmanName);
                String linkmanMobile = resultSet.getString("LINKMAN_MOBILE");
                if (StrUtil.isBlank(linkmanMobile)) {
                    linkmanMobile = "";
                }
                gcBusinessOpportunity.setLinkmanPhone(linkmanMobile);
                String linkmanEmail = resultSet.getString("LINKMAN_EMAIL");
                if (StrUtil.isBlank(linkmanEmail)) {
                    linkmanEmail = "";
                }
                gcBusinessOpportunity.setLinkmanEmail(linkmanEmail);
                String projectTypeCode = resultSet.getString("PROJECT_TYPE_CODE");
                GcSjRequirementEnum gcSjRequirementEnum;
                if (StrUtil.isBlank(projectTypeCode)) {
                    gcSjRequirementEnum = null;
                } else {
                    switch (projectTypeCode) {
                        case "ce_app":
                            gcSjRequirementEnum = GcSjRequirementEnum.CE_APP;
                            break;
                        case "ce_h5":
                            gcSjRequirementEnum = GcSjRequirementEnum.CE_H5;
                            break;
                        case "ce_mini_programs":
                            gcSjRequirementEnum = GcSjRequirementEnum.CE_MINI_PROGRAMS;
                            break;
                        case "ce_offical_website":
                            gcSjRequirementEnum = GcSjRequirementEnum.OFFICIAL_WEBSITE;
                            break;
                        case "ce_online_sales":
                            gcSjRequirementEnum = GcSjRequirementEnum.CE_ONLINE_SALES;
                            break;
                        case "ce_phone_website":
                            gcSjRequirementEnum = GcSjRequirementEnum.CE_PHONE_WEBSITE;
                            break;
                        case "ce_platform_dev":
                            gcSjRequirementEnum = GcSjRequirementEnum.CE_PLATFORM_DEV;
                            break;
                        case "ce_website_cluster":
                            gcSjRequirementEnum = GcSjRequirementEnum.CE_WEBSITE_CLUSTER;
                            break;
                        case "ce_wx_dev":
                            gcSjRequirementEnum = GcSjRequirementEnum.CE_WX_DEV;
                            break;
                        default:
                            gcSjRequirementEnum = null;
                    }
                }
                gcBusinessOpportunity.setRequirementType(gcSjRequirementEnum == null ? 0 : gcSjRequirementEnum.getRequirementType());
                String custDemand = resultSet.getString("CUST_DEMAND");
                if (StrUtil.isBlank(custDemand)) {
                    custDemand = "";
                }
                gcBusinessOpportunity.setRequirementDetail(custDemand);
                BigDecimal custBudget = resultSet.getBigDecimal("CUST_BUDGET");
                if (custBudget == null) {
                    custBudget = new BigDecimal(0);
                }
                gcBusinessOpportunity.setCustomerBudget(custBudget);
                String attachmentIds = resultSet.getString("ATTACHMENT_IDS");
                if (StrUtil.isBlank(attachmentIds)) {
                    attachmentIds = "";
                }
                gcBusinessOpportunity.setAttachmentIds(attachmentIds);
                gcBusinessOpportunity.setInterviewState(resultSet.getInt("IS_INTERVIEW"));
                String sjCode = resultSet.getString("SJ_CODE");
                if (StrUtil.isBlank(sjCode)) {
                    sjCode = "";
                }
                gcBusinessOpportunity.setTelSjCode(sjCode);
                String areaId = resultSet.getString("AREA_ID");
                if (StrUtil.isBlank(areaId)) {
                    areaId = "";
                }
                gcBusinessOpportunity.setAreaId(areaId);
                String areaName = resultSet.getString("AREA_NAME");
                if (StrUtil.isBlank(areaName)) {
                    areaName = "";
                }
                gcBusinessOpportunity.setAreaName(areaName);
                String subId = resultSet.getString("SUB_ID");
                if (StrUtil.isBlank(subId)) {
                    subId = "";
                }
                gcBusinessOpportunity.setSubId(subId);
                String subName = resultSet.getString("SUB_NAME");
                if (StrUtil.isBlank(subName)) {
                    subName = "";
                }
                gcBusinessOpportunity.setSubName(subName);
                String deptId = resultSet.getString("DEPT_ID");
                if (StrUtil.isBlank(deptId)) {
                    deptId = "";
                }
                gcBusinessOpportunity.setDeptId(deptId);
                String deptName = resultSet.getString("DEPT_NAME");
                if (StrUtil.isBlank(deptName)) {
                    deptName = "";
                }
                gcBusinessOpportunity.setDeptName(deptName);
                String salerId = resultSet.getString("SALER_ID");
                if (StrUtil.isBlank(salerId)) {
                    salerId = "";
                }
                gcBusinessOpportunity.setSalerId(salerId);
                String salerName = resultSet.getString("SALER_NAME");
                if (StrUtil.isBlank(salerName)) {
                    salerName = "";
                }
                gcBusinessOpportunity.setSalerName(salerName);
                String ompBusinessName = resultSet.getString("OMP_BUSINESS_NAME");
                if (StrUtil.isNotBlank(ompBusinessName)) {
                    GcEmployeeData gcEmployeeData = GcOrgDataUtil.EMPLOYEE_DATA_MAPPING.get(ompBusinessName);
                    if (gcEmployeeData != null) {
                        gcBusinessOpportunity.setExpectGcAreaId(gcEmployeeData.getAreaId());
                        gcBusinessOpportunity.setExpectGcAreaName(gcEmployeeData.getAreaName());
                        gcBusinessOpportunity.setExpectGcSubId(gcEmployeeData.getSubId());
                        gcBusinessOpportunity.setExpectGcSubName(gcEmployeeData.getSubName());
                        gcBusinessOpportunity.setExpectGcDeptId(gcEmployeeData.getDeptId());
                        gcBusinessOpportunity.setExpectGcDeptName(gcEmployeeData.getDeptName());
                        gcBusinessOpportunity.setExpectGcSalerId(gcEmployeeData.getEmployeeId());
                        gcBusinessOpportunity.setExpectGcSalerName(gcEmployeeData.getEmployeeName());
                        if (gcBusinessOpportunity.getState() >= GcSjStateEnum.FOLLOW.getState()) {
                            gcBusinessOpportunity.setGcAreaId(gcEmployeeData.getAreaId());
                            gcBusinessOpportunity.setGcAreaName(gcEmployeeData.getAreaName());
                            gcBusinessOpportunity.setGcSubId(gcEmployeeData.getSubId());
                            gcBusinessOpportunity.setGcSubName(gcEmployeeData.getSubName());
                            gcBusinessOpportunity.setGcDeptId(gcEmployeeData.getDeptId());
                            gcBusinessOpportunity.setGcDeptName(gcEmployeeData.getDeptName());
                            gcBusinessOpportunity.setGcSalerId(gcEmployeeData.getEmployeeId());
                            gcBusinessOpportunity.setGcSalerName(gcEmployeeData.getEmployeeName());
                        } else {
                            gcBusinessOpportunity.setGcAreaId("");
                            gcBusinessOpportunity.setGcAreaName("");
                            gcBusinessOpportunity.setGcSubId("");
                            gcBusinessOpportunity.setGcSubName("");
                            gcBusinessOpportunity.setGcDeptId("");
                            gcBusinessOpportunity.setGcDeptName("");
                            gcBusinessOpportunity.setGcSalerId("");
                            gcBusinessOpportunity.setGcSalerName("");
                        }
                    } else {
                        gcBusinessOpportunity.setExpectGcAreaId("");
                        gcBusinessOpportunity.setExpectGcAreaName("");
                        gcBusinessOpportunity.setExpectGcSubId("");
                        gcBusinessOpportunity.setExpectGcSubName("");
                        gcBusinessOpportunity.setExpectGcDeptId("");
                        gcBusinessOpportunity.setExpectGcDeptName("");
                        gcBusinessOpportunity.setExpectGcSalerId("");
                        gcBusinessOpportunity.setExpectGcSalerName("");
                        gcBusinessOpportunity.setGcAreaId("");
                        gcBusinessOpportunity.setGcAreaName("");
                        gcBusinessOpportunity.setGcSubId("");
                        gcBusinessOpportunity.setGcSubName("");
                        gcBusinessOpportunity.setGcDeptId("");
                        gcBusinessOpportunity.setGcDeptName("");
                        gcBusinessOpportunity.setGcSalerId("");
                        gcBusinessOpportunity.setGcSalerName("");
                    }
                } else {
                    gcBusinessOpportunity.setExpectGcAreaId("");
                    gcBusinessOpportunity.setExpectGcAreaName("");
                    gcBusinessOpportunity.setExpectGcSubId("");
                    gcBusinessOpportunity.setExpectGcSubName("");
                    gcBusinessOpportunity.setExpectGcDeptId("");
                    gcBusinessOpportunity.setExpectGcDeptName("");
                    gcBusinessOpportunity.setExpectGcSalerId("");
                    gcBusinessOpportunity.setExpectGcSalerName("");
                    gcBusinessOpportunity.setGcAreaId("");
                    gcBusinessOpportunity.setGcAreaName("");
                    gcBusinessOpportunity.setGcSubId("");
                    gcBusinessOpportunity.setGcSubName("");
                    gcBusinessOpportunity.setGcDeptId("");
                    gcBusinessOpportunity.setGcDeptName("");
                    gcBusinessOpportunity.setGcSalerId("");
                    gcBusinessOpportunity.setGcSalerName("");
                }
                String majordomoId = resultSet.getString("MAJORDOMO_ID");
                if (StrUtil.isBlank(majordomoId)) {
                    majordomoId = "";
                }
                gcBusinessOpportunity.setCheckMajorId(majordomoId);
                String majordomoName = resultSet.getString("MAJORDOMO_NAME");
                if (StrUtil.isBlank(majordomoName)) {
                    majordomoName = "";
                }
                gcBusinessOpportunity.setCheckMajorName(majordomoName);
                String majordomoMobile = resultSet.getString("MAJORDOMO_MOBILE");
                if (StrUtil.isBlank(majordomoMobile)) {
                    majordomoMobile = "";
                }
                gcBusinessOpportunity.setCheckMajorPhone(majordomoMobile);
                gcBusinessOpportunity.setDeleteFlag(resultSet.getInt("IS_DELETE"));
                Timestamp createTime = resultSet.getTimestamp("CREATE_TIME");
                if (createTime != null) {
                    gcBusinessOpportunity.setCreateTime(createTime.toLocalDateTime());
                } else {
                    gcBusinessOpportunity.setCreateTime(LocalDateTime.now());
                }
                Timestamp updateTime = resultSet.getTimestamp("UPDATE_TIME");
                if (updateTime != null) {
                    gcBusinessOpportunity.setUpdateTime(updateTime.toLocalDateTime());
                } else {
                    gcBusinessOpportunity.setUpdateTime(LocalDateTime.now());
                }
                list.add(gcBusinessOpportunity);
            }
            DataSourceUtil.releaseConnection(connection);
            if (!existData) {
                break;
            }
            page++;
        }
        return list;
    }

    /**
     * 批量插入高呈商机数据
     * @param dataList  需要保存的数据
     * <AUTHOR>
     * @date 2024/5/31 下午4:11
     **/
    public static void batchInsertData(List<GcBusinessOpportunity> dataList) throws SQLException {
        String insertQuery = "INSERT INTO `gc_business_opportunity` (`id`, `customer_id`, `customer_name`, `initiator`, `source`, `state`, `refuse_reason`, `linkman_name`, `linkman_phone`, `linkman_email`, `requirement_type`, `requirement_detail`, `customer_budget`, `attachment_ids`, `interview_state`, `tel_sj_code`, `area_id`, `area_name`, `sub_id`, `sub_name`, `dept_id`, `dept_name`, `saler_id`, `saler_name`, `gc_area_id`, `gc_area_name`, `gc_sub_id`, `gc_sub_name`, `gc_dept_id`, `gc_dept_name`, `gc_saler_id`, `gc_saler_name`, `expect_gc_area_id`, `expect_gc_area_name`, `expect_gc_sub_id`, `expect_gc_sub_name`, `expect_gc_dept_id`, `expect_gc_dept_name`, `expect_gc_saler_id`, `expect_gc_saler_name`, `check_major_id`, `check_major_name`, `check_major_phone`, `delete_flag`, `create_time`, `update_time`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        Connection connection = DataSourceUtil.getConnection(smaDataSource);
        PreparedStatement pstmt = connection.prepareStatement(insertQuery);
        // 设置自动提交为 false
        connection.setAutoCommit(false);
        // 遍历数据列表并添加到批处理中
        for (GcBusinessOpportunity gcBusinessOpportunity : dataList) {
            pstmt.setLong(1, gcBusinessOpportunity.getId());
            pstmt.setString(2, gcBusinessOpportunity.getCustomerId());
            pstmt.setString(3, gcBusinessOpportunity.getCustomerName());
            pstmt.setInt(4, gcBusinessOpportunity.getInitiator());
            pstmt.setInt(5, gcBusinessOpportunity.getSource());
            pstmt.setInt(6, gcBusinessOpportunity.getState());
            pstmt.setString(7, gcBusinessOpportunity.getRefuseReason());
            pstmt.setString(8, gcBusinessOpportunity.getLinkmanName());
            pstmt.setString(9, gcBusinessOpportunity.getLinkmanPhone());
            pstmt.setString(10, gcBusinessOpportunity.getLinkmanEmail());
            pstmt.setInt(11, gcBusinessOpportunity.getRequirementType());
            pstmt.setString(12, gcBusinessOpportunity.getRequirementDetail());
            pstmt.setBigDecimal(13, gcBusinessOpportunity.getCustomerBudget());
            pstmt.setString(14, gcBusinessOpportunity.getAttachmentIds());
            pstmt.setInt(15, gcBusinessOpportunity.getInterviewState());
            pstmt.setString(16, gcBusinessOpportunity.getTelSjCode());
            pstmt.setString(17, gcBusinessOpportunity.getAreaId());
            pstmt.setString(18, gcBusinessOpportunity.getAreaName());
            pstmt.setString(19, gcBusinessOpportunity.getSubId());
            pstmt.setString(20, gcBusinessOpportunity.getSubName());
            pstmt.setString(21, gcBusinessOpportunity.getDeptId());
            pstmt.setString(22, gcBusinessOpportunity.getDeptName());
            pstmt.setString(23, gcBusinessOpportunity.getSalerId());
            pstmt.setString(24, gcBusinessOpportunity.getSalerName());
            pstmt.setString(25, gcBusinessOpportunity.getGcAreaId());
            pstmt.setString(26, gcBusinessOpportunity.getGcAreaName());
            pstmt.setString(27, gcBusinessOpportunity.getGcSubId());
            pstmt.setString(28, gcBusinessOpportunity.getGcSubName());
            pstmt.setString(29, gcBusinessOpportunity.getGcDeptId());
            pstmt.setString(30, gcBusinessOpportunity.getGcDeptName());
            pstmt.setString(31, gcBusinessOpportunity.getGcSalerId());
            pstmt.setString(32, gcBusinessOpportunity.getGcSalerName());
            pstmt.setString(33, gcBusinessOpportunity.getExpectGcAreaId());
            pstmt.setString(34, gcBusinessOpportunity.getExpectGcAreaName());
            pstmt.setString(35, gcBusinessOpportunity.getExpectGcSubId());
            pstmt.setString(36, gcBusinessOpportunity.getExpectGcSubName());
            pstmt.setString(37, gcBusinessOpportunity.getExpectGcDeptId());
            pstmt.setString(38, gcBusinessOpportunity.getExpectGcDeptName());
            pstmt.setString(39, gcBusinessOpportunity.getExpectGcSalerId());
            pstmt.setString(40, gcBusinessOpportunity.getExpectGcSalerName());
            pstmt.setString(41, gcBusinessOpportunity.getCheckMajorId());
            pstmt.setString(42, gcBusinessOpportunity.getCheckMajorName());
            pstmt.setString(43, gcBusinessOpportunity.getCheckMajorPhone());
            pstmt.setInt(44, gcBusinessOpportunity.getDeleteFlag());
            pstmt.setTimestamp(45, Timestamp.valueOf(gcBusinessOpportunity.getCreateTime()));
            pstmt.setTimestamp(46, Timestamp.valueOf(gcBusinessOpportunity.getUpdateTime()));
            // 添加到批处理中
            pstmt.addBatch();
        }
        // 执行批处理
        pstmt.executeBatch();
        // 提交事务
        connection.commit();
        DataSourceUtil.releaseConnection(connection);
    }

    /**
     * 获取拒绝原因
     * @param gcSjId    高呈商机ID
     * <AUTHOR>
     * @date 2024/6/3 下午2:06
     * @return java.lang.String
     **/
    private Map<Long, String> getRefuseReason() throws SQLException {
        String query = "SELECT * FROM `sj_to_omp_log` where approval_state = '审批未通过'";
        Connection connection = DataSourceUtil.getConnection(extendDataSource);
        PreparedStatement preparedStatement = connection.prepareStatement(query);
        // 执行查询
        ResultSet resultSet = preparedStatement.executeQuery();
        Map<Long, String> refuseReason = new HashMap<>();
        while (resultSet.next()) {
            refuseReason.put(resultSet.getLong("sj_to_omp_id"), resultSet.getString("remark"));
        }
        DataSourceUtil.releaseConnection(connection);
        return refuseReason;
    }

    /**
     * 获取所有老的日志数据，并转换为新的日志表数据
     * <AUTHOR>
     * @date 2024/5/31 下午3:56
     * @return java.util.List<com.ce.scrm.center.dao.entity.GcBusinessOpportunityLog>
     **/
    private List<GcBusinessOpportunityLog> getAllLogData() throws SQLException {
        List<GcBusinessOpportunityLog> list = new ArrayList<>();
        int page = 1;
        while (true) {
            // 计算偏移量
            int offset = (page - 1) * PAGE_SIZE;
            // SQL 查询语句，使用 LIMIT 和 OFFSET 实现分页
            String query = "SELECT * FROM `sj_to_omp_log` LIMIT ? OFFSET ?";
            Connection connection = DataSourceUtil.getConnection(extendDataSource);
            PreparedStatement preparedStatement = connection.prepareStatement(query);
            // 设置 LIMIT 和 OFFSET 参数
            preparedStatement.setInt(1, PAGE_SIZE);
            preparedStatement.setInt(2, offset);
            // 执行查询
            ResultSet resultSet = preparedStatement.executeQuery();
            boolean existData = false;
            // 处理结果集
            while (resultSet.next()) {
                existData = true;
                GcBusinessOpportunityLog gcBusinessOpportunityLog = new GcBusinessOpportunityLog();
                gcBusinessOpportunityLog.setSjId(resultSet.getLong("sj_to_omp_id"));
                gcBusinessOpportunityLog.setOperateType(GcSjOperateLogTypeEnum.HISTORY_DATA.getOperateType());
                String remark = "";
                String ompFollowsState = resultSet.getString("omp_follows_state");
                if (StrUtil.isNotBlank(ompFollowsState)) {
                    remark = ompFollowsState;
                }
                String approvalState = resultSet.getString("approval_state");
                if (StrUtil.isNotBlank(approvalState)) {
                    if (StrUtil.isNotBlank(remark)) {
                        remark += "【" + approvalState + "】";
                    } else {
                        remark = approvalState;
                    }
                }
                String oldRemark = resultSet.getString("remark");
                if (StrUtil.isNotBlank(oldRemark)) {
                    if (StrUtil.isNotBlank(remark)) {
                        remark += "(" + oldRemark + ")";
                    } else {
                        remark = oldRemark;
                    }
                }
                gcBusinessOpportunityLog.setRemark(remark);
                gcBusinessOpportunityLog.setOperatorName(resultSet.getString("create_name"));
                Timestamp createTime = resultSet.getTimestamp("create_time");
                if (createTime != null) {
                    LocalDateTime dateTime = createTime.toLocalDateTime();
                    gcBusinessOpportunityLog.setOperateTime(dateTime);
                }
                list.add(gcBusinessOpportunityLog);
            }
            DataSourceUtil.releaseConnection(connection);
            if (!existData) {
                break;
            }
            page++;
        }
        return list;
    }

    /**
     * 批量插入日志数据
     * @param dataList  需要保存的数据
     * <AUTHOR>
     * @date 2024/5/31 下午4:11
     **/
    public static void batchInsertLogData(List<GcBusinessOpportunityLog> dataList) throws SQLException {
        String insertQuery = "INSERT INTO `gc_business_opportunity_log` (`sj_id`, `operate_type`, `remark`, `operator_name`, `operate_time`) VALUES (?, ?, ?, ?, ?)";
        Connection connection = DataSourceUtil.getConnection(smaDataSource);
        PreparedStatement preparedStatement = connection.prepareStatement(insertQuery);
        // 设置自动提交为 false
        connection.setAutoCommit(false);
        // 遍历数据列表并添加到批处理中
        for (GcBusinessOpportunityLog data : dataList) {
            preparedStatement.setLong(1, data.getSjId());
            preparedStatement.setInt(2, data.getOperateType());
            preparedStatement.setString(3, data.getRemark());
            preparedStatement.setString(4, data.getOperatorName());
            preparedStatement.setObject(5, data.getOperateTime());
            // 添加到批处理中
            preparedStatement.addBatch();
        }
        // 执行批处理
        preparedStatement.executeBatch();
        // 提交事务
        connection.commit();
        DataSourceUtil.releaseConnection(connection);
    }
}