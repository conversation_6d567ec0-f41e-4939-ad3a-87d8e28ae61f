package com.ce.scrm.center.web.util;

import com.ce.scrm.center.web.enums.DataSourceEnum;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据源
 * <AUTHOR>
 * @date 2023/10/13 14:04
 * @version 1.0.0
 */
public class DataSourceUtil {

    /**
     * 获取数据库连接
     * @param dataSourceEnum    数据库信息
     * <AUTHOR>
     * @date 2023/10/13 14:06
     * @return java.sql.Connection
     **/
    public static Connection getConnection(DataSourceEnum dataSourceEnum) {
        try {
            return DriverManager.getConnection(dataSourceEnum.getDbUrl(), dataSourceEnum.getDbUser(), dataSourceEnum.getDbPassword());
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 释放数据库连接
     * @param connection    数据库连接
     * <AUTHOR>
     * @date 2023/10/13 14:30
     **/
    public static void releaseConnection(Connection connection) {
        try {
            connection.close();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
}