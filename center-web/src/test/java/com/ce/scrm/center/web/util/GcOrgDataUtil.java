package com.ce.scrm.center.web.util;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.web.entity.GcEmployeeData;
import com.ce.scrm.center.web.entity.Organization;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 高呈组织数据
 * <AUTHOR>
 * @date 2024/6/3 下午2:37
 * @version 1.0.0
 */
@Slf4j
public class GcOrgDataUtil {

    public final static Map<String, GcEmployeeData> EMPLOYEE_DATA_MAPPING = new HashMap<>();

    static {
        String json = "[{\"id\":\"3949\",\"parentId\":\"0\",\"weight\":1,\"name\":\"KA销售部\",\"children\":[{\"id\":\"2576\",\"parentId\":\"3949\",\"weight\":2,\"name\":\"中企高呈-北京\",\"children\":[{\"id\":\"3218\",\"parentId\":\"2576\",\"weight\":3,\"name\":\"BU1部\",\"children\":[{\"id\":\"70046\",\"parentId\":\"3218\",\"weight\":4,\"name\":\"王艳\"},{\"id\":\"75021\",\"parentId\":\"3218\",\"weight\":4,\"name\":\"熊梦凡\"},{\"id\":\"85987\",\"parentId\":\"3218\",\"weight\":4,\"name\":\"蔡陆\"}]},{\"id\":\"3219\",\"parentId\":\"2576\",\"weight\":3,\"name\":\"BU2部\",\"children\":[{\"id\":\"63537\",\"parentId\":\"3219\",\"weight\":4,\"name\":\"徐建锋\"},{\"id\":\"78360\",\"parentId\":\"3219\",\"weight\":4,\"name\":\"耿丁得佰\"},{\"id\":\"87164\",\"parentId\":\"3219\",\"weight\":4,\"name\":\"翟康乐\"}]},{\"id\":\"3220\",\"parentId\":\"2576\",\"weight\":3,\"name\":\"BU3部\",\"children\":[{\"id\":\"101167\",\"parentId\":\"3220\",\"weight\":4,\"name\":\"李思瑶\"},{\"id\":\"79758\",\"parentId\":\"3220\",\"weight\":4,\"name\":\"张平\"},{\"id\":\"98274\",\"parentId\":\"3220\",\"weight\":4,\"name\":\"魏强\"}]},{\"id\":\"3724\",\"parentId\":\"2576\",\"weight\":3,\"name\":\"专项组\",\"children\":[{\"id\":\"67171\",\"parentId\":\"3724\",\"weight\":4,\"name\":\"楼京\"}]}]},{\"id\":\"2577\",\"parentId\":\"3949\",\"weight\":2,\"name\":\"中企高呈-广深\",\"children\":[{\"id\":\"3683\",\"parentId\":\"2577\",\"weight\":3,\"name\":\"BU部\",\"children\":[{\"id\":\"103936\",\"parentId\":\"3683\",\"weight\":4,\"name\":\"宋梓仪\"},{\"id\":\"81768\",\"parentId\":\"3683\",\"weight\":4,\"name\":\"邱海怀\"},{\"id\":\"94884\",\"parentId\":\"3683\",\"weight\":4,\"name\":\"陈庆坚\"}]}]},{\"id\":\"2578\",\"parentId\":\"3949\",\"weight\":2,\"name\":\"中企高呈-上海\",\"children\":[{\"id\":\"3995\",\"parentId\":\"2578\",\"weight\":3,\"name\":\"BU2部\",\"children\":[{\"id\":\"102685\",\"parentId\":\"3995\",\"weight\":4,\"name\":\"候波\"},{\"id\":\"102686\",\"parentId\":\"3995\",\"weight\":4,\"name\":\"蒋军\"}]},{\"id\":\"2588\",\"parentId\":\"2578\",\"weight\":3,\"name\":\"BU1部\",\"children\":[{\"id\":\"77401\",\"parentId\":\"2588\",\"weight\":4,\"name\":\"丁丞\"},{\"id\":\"78255\",\"parentId\":\"2588\",\"weight\":4,\"name\":\"马英鸽\"},{\"id\":\"87421\",\"parentId\":\"2588\",\"weight\":4,\"name\":\"冯娉\"}]}]}]}]";
        List<Organization> organizations = JSON.parseArray(json, Organization.class);
        processOrganization(organizations, null, null, null, null, null, null);
    }

    /**
     * 处理组织数据
     * @param organizations 组织数据
     * @param areaId    区域ID
     * @param areaName  区域名称
     * @param subId 分司ID
     * @param subName   分司名称
     * @param deptId    部门ID
     * @param deptName  部门名称
     * <AUTHOR>
     * @date 2024/6/3 下午5:58
     * @return void
     **/
    public static void processOrganization(List<Organization> organizations, String areaId, String areaName, String subId, String subName, String deptId, String deptName) {
        for (Organization org : organizations) {
            if (org.getChildren() == null || org.getChildren().isEmpty()) {
                // 第四层（员工）
                GcEmployeeData employeeData = new GcEmployeeData();
                employeeData.setAreaId(areaId);
                employeeData.setAreaName(areaName);
                employeeData.setSubId(subId);
                employeeData.setSubName(subName);
                employeeData.setDeptId(deptId);
                employeeData.setDeptName(deptName);
                employeeData.setEmployeeId(org.getId());
                employeeData.setEmployeeName(org.getName());
                EMPLOYEE_DATA_MAPPING.put(org.getName(), employeeData);
            } else if (areaId == null) {
                // 第一层（区域）
                processOrganization(org.getChildren(), org.getId(), org.getName(), subId, subName, deptId, deptName);
            } else if (subId == null) {
                // 第二层（分司）
                processOrganization(org.getChildren(), areaId, areaName, org.getId(), org.getName(), deptId, deptName);
            } else if (deptId == null) {
                // 第三层（部门）
                processOrganization(org.getChildren(), areaId, areaName, subId, subName, org.getId(), org.getName());
            }
        }
    }

}