package com.ce.scrm.center.web;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.util.UriComponentsBuilder;

public class CouponTest {
//    public static void main(String[] args) {
//        String aaa = "{\n" +
//                "    \"couponId\": \"YHQ20241119606897\",\n" +
//                "    \"giveWay\": 1,\n" +
//                "    \"giveObject\": \"TO_CUST_NOIMPORT\",\n" +
//                "    \"isNow\": 1,\n" +
//                "    \"giveReason\": \"1231\",\n" +
//                "    \"type\": 1,\n" +
//                "    \"custId\": \"49B6EDA570C344639B267B8097E0659D\",\n" +
//                "    \"custName\": \"低迷他专属测试客户（SF：21020419860903430X）\",\n" +
//                "    \"detailNum\": 1\n" +
//                "}";
//        JSONObject jsonObject = JSONObject.parseObject(aaa);
//        jsonObject.put("signDate", SignUtils.getTimestamp());
//        String sign = SignUtils.createSign(jsonObject, "JKUQ45qenW");
//        jsonObject.put("sign", sign);
//        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString("https://test-api-support.ceboss.cn/security/couponGiveRecord/saveCouponRecordNotLogin");
//        for (String key : jsonObject.keySet()) {
//            uriComponentsBuilder.queryParam(key, jsonObject.getString(key));
//        }
//        String reqUrl = uriComponentsBuilder.build().toUriString();
//        System.out.println(reqUrl);
//    }
}
