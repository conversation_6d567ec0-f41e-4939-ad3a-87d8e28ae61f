package com.ce.scrm.center.web.util;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.ce.scrm.center.web.entity.GcCustomerData;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
public class ExcelDataListener implements ReadListener<GcCustomerData> {
    /**
     * 缓存的数据
     */
    private final List<GcCustomerData> cachedDataList = new ArrayList<>();

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(GcCustomerData data, AnalysisContext context) {
        cachedDataList.add(data);
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("表格数据解析完成，数量为:{}", cachedDataList.size());
    }
}