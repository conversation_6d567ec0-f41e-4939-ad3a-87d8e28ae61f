package com.ce.scrm.center.web.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 高呈客户数据
 * <AUTHOR>
 * @date 2024/6/5 上午9:15
 * @version 1.0.0
 */
@Data
public class GcCustomerDataExport {
    /**
     * 高呈客户ID:业务无用，导出标识唯一数据所用
     */
    @ExcelProperty("原表格数据ID")
    private String id;
    /**
     * 客户名称：只处理客户名称不为空的数据
     */
    @ExcelProperty("客户名称")
    private String customerName;
    /**
     * 数据状态：following（跟进中）、converted（已转化）只处理这两种状态，其他状态仅维护客户数据
     */
    @ExcelProperty("客户状态")
    private String state;
    /**
     * 数据状态：following（跟进中）、converted（已转化）只处理这两种状态，其他状态仅维护客户数据
     */
    @ExcelProperty("客户状态描述")
    private String stateName;
    /**
     * 商务名称：仅处理名称不为空的数据，其他状态仅维护客户数据
     */
    @ExcelProperty("高呈客户经理")
    private String salerName;
    /**
     * 跟进内容
     */
    @ExcelProperty("跟进内容")
    private String followContent;
    /**
     * 操作结果
     */
    @ExcelProperty("操作成果")
    private String result;

    /**
     * 描述说明
     */
    @ExcelProperty("操作结果描述")
    private String desc;
}