package com.ce.scrm.center.web;

import cn.ce.cesupport.enums.CustSourceEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ce.scrm.center.ScrmCenterWebApplication;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.CustomerFollow;
import com.ce.scrm.center.service.config.UniqueIdService;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.third.entity.view.BigDataCompanyDetail;
import com.ce.scrm.center.service.third.entity.view.BigDataData;
import com.ce.scrm.center.service.third.entity.view.BigDataResult;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.web.entity.*;
import com.ce.scrm.center.web.enums.DataSourceEnum;
import com.ce.scrm.center.web.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.sql.*;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 迁移高呈商机操作日志
 * <AUTHOR>
 * @date 2024/5/31 上午11:29
 * @version 1.0.0
 */
@Slf4j
@ExtendWith({TestExceptionHandler.class})
@SpringBootTest(classes = ScrmCenterWebApplication.class)
public class ClearGcCustomerDataTests {

    private final static boolean CLOSE_STATE = false;

    private final static boolean isProd = false;

    private Connection customerConnection;

    private Connection smaConnection;

    private Connection extendConnection;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private UniqueIdService uniqueIdService;

    public static boolean testFailed = false;

    private final StopWatch stopWatch = new StopWatch("ClearGcCustomerDataTests");

    @BeforeEach
    public void before() throws SQLException {
        log.info("迁移高呈客户关系开始");
        stopWatch.start("before");
        DataSourceEnum customerDataSource;
        DataSourceEnum smaDataSource;
        DataSourceEnum extendDataSource;
        if (isProd) {
            customerDataSource = DataSourceEnum.CUSTOMER_PROD;
            smaDataSource = DataSourceEnum.SMA_PROD;
            extendDataSource = DataSourceEnum.EXTEND_PROD;
        } else {
            customerDataSource = DataSourceEnum.CUSTOMER_TEST;
            smaDataSource = DataSourceEnum.SMA_TEST;
            extendDataSource = DataSourceEnum.EXTEND_TEST;
        }
        customerConnection = DataSourceUtil.getConnection(customerDataSource);
        customerConnection.setAutoCommit(false);
        smaConnection = DataSourceUtil.getConnection(smaDataSource);
        smaConnection.setAutoCommit(false);
        extendConnection = DataSourceUtil.getConnection(extendDataSource);
        extendConnection.setAutoCommit(false);
        stopWatch.stop();
    }

    @AfterEach
    public void after() throws SQLException {
        stopWatch.start("after");
        if (testFailed) {
            customerConnection.rollback();
            smaConnection.rollback();
            extendConnection.rollback();
        } else {
            customerConnection.commit();
            smaConnection.commit();
            extendConnection.commit();
        }
        DataSourceUtil.releaseConnection(customerConnection);
        DataSourceUtil.releaseConnection(smaConnection);
        DataSourceUtil.releaseConnection(extendConnection);
        stopWatch.stop();
        log.info("耗时分布:{}", stopWatch.prettyPrint());
        log.info("迁移高呈客户关系结束");
    }

    @Test
    public void clearGcCustomerData() {
        if (CLOSE_STATE) {
            return;
        }
        stopWatch.start("parseExcelData");
        List<GcCustomerData> gcCustomerDataList = parseExcelData();
        stopWatch.stop();
        stopWatch.start("handlerCustomerData");
        log.info("excel解析完成,开始遍历处理客户数据");
        GcCustomerDataHandlerResult gcCustomerDataHandlerResult = handlerCustomerData(gcCustomerDataList);
        log.info("遍历处理客户数据完成，开始导出操作结果");
        stopWatch.stop();
        stopWatch.start("exportExcelData");
        String fileName = "/Users/<USER>/300Projects/scrm/scrm-center/center-web/src/test/java/com/ce/scrm/center/web/data/操作结果.xlsx";
        ArrayList<GcCustomerDataExport> exportData = CollUtil.newArrayList(gcCustomerDataHandlerResult.getSuccessList());
        exportData.addAll(gcCustomerDataHandlerResult.getFailList());
        EasyExcel.write(fileName, GcCustomerDataExport.class).sheet("操作结果").doWrite(exportData);
        log.info("导出操作结果完成");
        stopWatch.stop();
        int i = 1 / 0;
    }

    /**
     * 解析excel数据
     * <AUTHOR>
     * @date 2024/6/5 上午11:38
     * @return java.util.List<com.ce.scrm.center.web.entity.GcCustomerData>
     **/
    private List<GcCustomerData> parseExcelData() {
        String fileName = "/Users/<USER>/300Projects/scrm/scrm-center/center-web/src/test/java/com/ce/scrm/center/web/data/导出线索数据.xlsx";
        ExcelDataListener excelDataListener = new ExcelDataListener();
        EasyExcel.read(fileName, GcCustomerData.class, excelDataListener).sheet().doRead();
        return excelDataListener.getCachedDataList();
    }

    /**
     * 遍历处理客户数据
     * 1、客户名称为空，失败结束
     * 2、状态不是跟进中或已转化的，失败结束
     * 3、商务名称为空，失败结束
     * 4、商务不存在，失败结束
     * 5、客户库不存在且搜客宝不存在，失败结束
     * 6、客户库不存在，搜客宝存在，维护客户信息
     *      a、先根据uncid作为客户ID获取客户库数据
     * 	    b、根据pid获取客户库数据
     * 	    c、添加客户到客户库
     * 7、保护库不存在，直接添加保护关系，成功结束
     * 8、保护库存在且为客户池中，更新保护关系，成功结束
     * 9、如果当前保护关系在高呈商务身上，则不处理
     * 10、添加跟进记录，成功结束
     * @param gcCustomerDataList    所有客户数据
     * <AUTHOR>
     * @date 2024/6/5 上午11:38
     * @return com.ce.scrm.center.web.entity.GcCustomerDataHandlerResult
     **/
    private GcCustomerDataHandlerResult handlerCustomerData(List<GcCustomerData> gcCustomerDataList) {
        List<GcCustomerDataExport> failList = new CopyOnWriteArrayList<>();
        List<GcCustomerDataExport> successList = new CopyOnWriteArrayList<>();
        gcCustomerDataList.parallelStream().forEach(gcCustomerData -> {
            GcCustomerDataExport gcCustomerDataExport = CglibUtil.copy(gcCustomerData, GcCustomerDataExport.class);
            //1、客户名称为空，失败结束
            String customerName;
            if (StrUtil.isBlank(customerName = gcCustomerData.getCustomerName())) {
                gcCustomerDataExport.setDesc("客户名称为空");
                failList.add(gcCustomerDataExport);
                return;
            }
            //2、状态不是跟进中或已转化的，失败结束
            if (!"following".equals(gcCustomerData.getState()) && !"converted".equals(gcCustomerData.getState())) {
                gcCustomerDataExport.setDesc("不处理非跟进中和已转化的客户");
                failList.add(gcCustomerDataExport);
                return;
            }
            //3、商务名称为空，失败结束
            String salerName;
            if (StrUtil.isBlank(salerName = gcCustomerData.getSalerName())) {
                gcCustomerDataExport.setDesc("商务名称为空");
                failList.add(gcCustomerDataExport);
                return;
            }
            //4、商务不存在，失败结束
            GcEmployeeData gcEmployeeData = GcOrgDataUtil.EMPLOYEE_DATA_MAPPING.get(salerName);
            if (gcEmployeeData == null) {
                gcCustomerDataExport.setDesc("商务不存在");
                failList.add(gcCustomerDataExport);
                return;
            }
            try {
                Customer customer = getDataByCustomer(customerName);
                if (customer == null) {
                    //5、客户库不存在且搜客宝不存在，失败结束
                    BigDataCompanyDetail bigDataCompanyDetail = getDataByEsAndCustomer(customerName);
                    if (bigDataCompanyDetail == null) {
                        gcCustomerDataExport.setDesc("搜客宝查询不到客户");
                        failList.add(gcCustomerDataExport);
                        return;
                    }
                    //6、客户库不存在，搜客宝存在，维护客户信息
                    String uncid = bigDataCompanyDetail.getUncid();
                    //a、先根据uncid作为客户ID获取客户库数据
                    if (StrUtil.isBlank(uncid) || (customer = getDataByCustomerUncid(uncid)) == null) {
                        //b、根据pid获取客户库数据
                        String pid = bigDataCompanyDetail.getPid();
                        customer = getDataByCustomerPid(pid);
                        //c、添加客户到客户库
                        if (customer == null) {
                            customer = saveCustomer(bigDataCompanyDetail, gcCustomerData.getSalerName());
                        }
                    }
                }
                CmCustProtect cmCustProtect = getCmCustProtect(customer.getCustomerId());
                //7、保护库不存在，直接添加保护关系，成功结束
                if (cmCustProtect == null) {
                    cmCustProtect = getCmCustProtectByName(customer.getCustomerName());
                    if (cmCustProtect == null) {
                        addProtectData(gcEmployeeData, customer);
                        gcCustomerDataExport.setDesc("保护关系不存在，直接添加");
                        successList.add(gcCustomerDataExport);
                        return;
                    }
                }
                //8、保护库存在且为客户池中，更新保护关系，成功结束
                if (ProtectStateEnum.CUSTOMER_POOL.getState().equals(cmCustProtect.getStatus())) {
                    updateProtectData(cmCustProtect, gcEmployeeData, customer);
                    gcCustomerDataExport.setDesc("无人保护，直接更新");
                    successList.add(gcCustomerDataExport);
                    return;
                }
                //9、如果当前保护关系在高呈商务身上，则不处理
                if (OrgThirdService.KA_SALE_DEPT_AREA_ID.equals(cmCustProtect.getAreaId())) {
                    gcCustomerDataExport.setDesc("已在高呈商务身上，无需处理");
                    failList.add(gcCustomerDataExport);
                    return;
                }
                //10、添加跟进记录，成功结束
                saveCustomerFollowData(customer, gcEmployeeData, gcCustomerData.getFollowContent());
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            gcCustomerDataExport.setDesc("中小保护，添加跟进记录");
            successList.add(gcCustomerDataExport);
        });
        GcCustomerDataHandlerResult gcCustomerDataHandlerResult = new GcCustomerDataHandlerResult();
        gcCustomerDataHandlerResult.setSuccessList(successList.stream().peek(gcCustomerDataExport -> gcCustomerDataExport.setResult("操作成功")).collect(Collectors.toList()));
        gcCustomerDataHandlerResult.setFailList(failList.stream().peek(gcCustomerDataExport -> gcCustomerDataExport.setResult("操作失败")).collect(Collectors.toList()));
        return gcCustomerDataHandlerResult;
    }


    /**
     * 从客户库获取客户ID
     * @param customerName    客户名称
     * <AUTHOR>
     * @date 2024/6/3 下午2:06
     * @return java.lang.String
     **/
    private Customer getDataByCustomer(String customerName) throws SQLException {
        String query = "SELECT * FROM `customer` where customer_name = ? order by create_time desc limit 1";
        PreparedStatement preparedStatement = customerConnection.prepareStatement(query);
        // 设置 LIMIT 和 OFFSET 参数
        preparedStatement.setString(1, customerName);
        // 执行查询
        ResultSet resultSet = preparedStatement.executeQuery();
        Customer customer = null;
        while (resultSet.next()) {
            customer = new Customer();
            customer.setCustomerId(resultSet.getString("customer_id"));
            customer.setCustomerName(resultSet.getString("customer_name"));
            customer.setCertificateCode(resultSet.getString("certificate_code"));
            customer.setSourceDataId(resultSet.getString("source_data_id"));
            customer.setProvinceCode(resultSet.getString("province_code"));
            customer.setCityCode(resultSet.getString("city_code"));
            customer.setDistrictCode(resultSet.getString("district_code"));
        }
        preparedStatement.close();
        resultSet.close();
        return customer;
    }

    /**
     * 从搜客宝es获取数据
     * @param customerName  客户名称
     * <AUTHOR>
     * @date 2024/6/5 下午1:34
     * @return java.lang.String
     **/
    private BigDataCompanyDetail getDataByEsAndCustomer(String customerName) throws SQLException {
        String fullUrl = "https://gateway.datauns.cn/ce22highlevelsearch/highSearch/baseinfo/company_detail?company_name=" + customerName;
        //请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String uuid = UUID.fastUUID().toString(true);
        headers.set("uid", uuid);
        ResponseEntity<String> response;
        response = restTemplate.exchange(fullUrl, HttpMethod.GET, new HttpEntity<>(headers), String.class);
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("调用大数据接口失败,path={},状态码={}", fullUrl, response.getStatusCode().value());
            throw new RuntimeException("调用大数据接口失败");
        }
        String responseBody = response.getBody();
        if (StringUtils.isEmpty(responseBody)) {
            log.error("大数据接口根据名称获取企业基本信息异常，返回数据为空，url={}", fullUrl);
            throw new RuntimeException("调用大数据接口失败");
        }
        //大数据返回的数据格式有点得儿，需要多次转换
        BigDataResult<BigDataData<BigDataCompanyDetail>> bigDataResult = JSON.parseObject(responseBody, new TypeReference<BigDataResult<BigDataData<BigDataCompanyDetail>>>() {
        });
        if (!Objects.equals(HttpStatus.OK.value(), bigDataResult.getCode())) {
            log.error("大数据接口根据名称获取企业基本信息响应码异常，response={}, url={}", responseBody, fullUrl);
            throw new RuntimeException("调用大数据接口失败");
        }
        if (bigDataResult.getData() == null) {
            log.error("大数据接口根据名称获取企业基本信息响应数据异常，response={}, url={}", responseBody, fullUrl);
            throw new RuntimeException("调用大数据接口失败");
        }
        if (!Objects.equals(0, bigDataResult.getData().getCode())) {
            return null;
        }
        return bigDataResult.getData().getData();
    }

    /**
     * 从客户库获取客户ID
     * @param uncid    社会信用编码
     * <AUTHOR>
     * @date 2024/6/3 下午2:06
     * @return java.lang.String
     **/
    private Customer getDataByCustomerUncid(String uncid) throws SQLException {
        String query = "SELECT * FROM `customer` where customer_id = ? order by create_time desc limit 1";
        PreparedStatement preparedStatement = customerConnection.prepareStatement(query);
        // 设置 LIMIT 和 OFFSET 参数
        preparedStatement.setString(1, uncid);
        // 执行查询
        ResultSet resultSet = preparedStatement.executeQuery();
        Customer customer = null;
        while (resultSet.next()) {
            customer = new Customer();
            customer.setCustomerId(resultSet.getString("customer_id"));
            customer.setCustomerName(resultSet.getString("customer_name"));
            customer.setCertificateCode(resultSet.getString("certificate_code"));
            customer.setSourceDataId(resultSet.getString("source_data_id"));
            customer.setProvinceCode(resultSet.getString("province_code"));
            customer.setCityCode(resultSet.getString("city_code"));
            customer.setDistrictCode(resultSet.getString("district_code"));
        }
        preparedStatement.close();
        resultSet.close();
        return customer;
    }

    /**
     * 从客户库获取客户ID
     * @param pid    社会信用编码
     * <AUTHOR>
     * @date 2024/6/3 下午2:06
     * @return java.lang.String
     **/
    private Customer getDataByCustomerPid(String pid) throws SQLException {
        String query = "SELECT * FROM `customer` where source_data_id = ? order by create_time desc limit 1";
        PreparedStatement preparedStatement = customerConnection.prepareStatement(query);
        // 设置 LIMIT 和 OFFSET 参数
        preparedStatement.setString(1, pid);
        // 执行查询
        ResultSet resultSet = preparedStatement.executeQuery();
        Customer customer = null;
        while (resultSet.next()) {
            customer = new Customer();
            customer.setCustomerId(resultSet.getString("customer_id"));
            customer.setCustomerName(resultSet.getString("customer_name"));
            customer.setCertificateCode(resultSet.getString("certificate_code"));
            customer.setSourceDataId(resultSet.getString("source_data_id"));
            customer.setProvinceCode(resultSet.getString("province_code"));
            customer.setCityCode(resultSet.getString("city_code"));
            customer.setDistrictCode(resultSet.getString("district_code"));
        }
        preparedStatement.close();
        resultSet.close();
        return customer;
    }

    /**
     * 保存客户数据
     * @param bigDataCompanyDetail  搜客宝数据
     * @param operator  操作人
     * <AUTHOR>
     * @date 2024/6/5 下午2:40
     * @return java.lang.String
     **/
    private Customer saveCustomer(BigDataCompanyDetail bigDataCompanyDetail, String operator) throws SQLException {
        Customer customer = new Customer();
        customer = packageSkbCustomerData(customer, bigDataCompanyDetail);
        customer.setPresentStage(3);
        customer.setCreateWay(5);
        customer.setCreatorKey("scrm");
        customer.setCreator(operator);
        customer.setOperatorKey("scrm");
        customer.setOperator(operator);
        String sql = "INSERT INTO customer (" +
                "customer_id, source_data_id, customer_type, customer_name, certificate_type, certificate_code, " +
                "check_in_state, establish_date, register_capital, paid_in_capital, organization_code, register_no, " +
                "taxpayer_no, enterprise_type, open_start_time, open_end_time, taxpayer_qualification, staff_scale, " +
                "insured_num, approve_date, province_code, province_name, city_code, city_name, district_code, " +
                "district_name, registration_authority, import_export_enterprise_code, first_industry_code, " +
                "first_industry_name, second_industry_code, second_industry_name, register_address, business_scope, " +
                "present_stage, create_way, creator_key, creator, operator_key, operator) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement pstmt = customerConnection.prepareStatement(sql);
        pstmt.setString(1, customer.getCustomerId());
        pstmt.setString(2, customer.getSourceDataId());
        pstmt.setInt(3, customer.getCustomerType());
        pstmt.setString(4, customer.getCustomerName());
        pstmt.setInt(5, customer.getCertificateType());
        pstmt.setString(6, customer.getCertificateCode());
        pstmt.setString(7, customer.getCheckInState());
        pstmt.setDate(8, Date.valueOf(customer.getEstablishDate()));
        pstmt.setString(9, customer.getRegisterCapital());
        pstmt.setString(10, customer.getPaidInCapital());
        pstmt.setString(11, customer.getOrganizationCode());
        pstmt.setString(12, customer.getRegisterNo());
        pstmt.setString(13, customer.getTaxpayerNo());
        pstmt.setString(14, customer.getEnterpriseType());
        pstmt.setDate(15, Date.valueOf(customer.getOpenStartTime()));
        pstmt.setDate(16, Date.valueOf(customer.getOpenEndTime()));
        pstmt.setString(17, customer.getTaxpayerQualification());
        pstmt.setString(18, customer.getStaffScale());
        pstmt.setString(19, customer.getInsuredNum());
        pstmt.setDate(20, Date.valueOf(customer.getApproveDate()));
        pstmt.setString(21, customer.getProvinceCode());
        pstmt.setString(22, customer.getProvinceName());
        pstmt.setString(23, customer.getCityCode());
        pstmt.setString(24, customer.getCityName());
        pstmt.setString(25, customer.getDistrictCode());
        pstmt.setString(26, customer.getDistrictName());
        pstmt.setString(27, customer.getRegistrationAuthority());
        pstmt.setString(28, customer.getImportExportEnterpriseCode());
        pstmt.setString(29, customer.getFirstIndustryCode());
        pstmt.setString(30, customer.getFirstIndustryName());
        pstmt.setString(31, customer.getSecondIndustryCode());
        pstmt.setString(32, customer.getSecondIndustryName());
        pstmt.setString(33, customer.getRegisterAddress());
        pstmt.setString(34, customer.getBusinessScope());
        pstmt.setInt(35, customer.getPresentStage());
        pstmt.setInt(36, customer.getCreateWay());
        pstmt.setString(37, customer.getCreatorKey());
        pstmt.setString(38, customer.getCreator());
        pstmt.setString(39, customer.getOperatorKey());
        pstmt.setString(40, customer.getOperator());
        pstmt.executeUpdate();
        pstmt.close();
        return customer;
    }

    /**
     * 拼装客户数据
     * @param customer  客户数据
     * @param bigDataCompanyDetail  搜客宝数据
     * <AUTHOR>
     * @date 2024/6/5 下午2:10
     * @return com.ce.scrm.center.web.entity.Customer
     **/
    public Customer packageSkbCustomerData(Customer customer, BigDataCompanyDetail bigDataCompanyDetail) {
        Customer result = new Customer();
        BeanUtil.copyProperties(customer, result);
        String customerId = result.getCustomerId();
        if (StrUtil.isBlank(customerId)) {
            customerId = bigDataCompanyDetail.getUncid();
        }
        if (StrUtil.isBlank(customerId)) {
            customerId = uniqueIdService.getId();
        }
        result.setCustomerId(customerId);
        result.setSourceDataId(bigDataCompanyDetail.getPid());
        result.setCustomerType(1);
        result.setCustomerName(bigDataCompanyDetail.getEntname());
        result.setCertificateType(1);
        result.setCertificateCode(bigDataCompanyDetail.getUncid());
        result.setCheckInState(bigDataCompanyDetail.getEnt_status());
        result.setEstablishDate(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getEstablish_date())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        result.setRegisterCapital(registerCapital(bigDataCompanyDetail.getReg_cap(), bigDataCompanyDetail.getReg_cap_cur()));
        result.setRegisterNo(bigDataCompanyDetail.getReg_no());
        result.setTaxpayerNo(bigDataCompanyDetail.getUncid());
        result.setEnterpriseType(bigDataCompanyDetail.getEnt_type());
        result.setOpenStartTime(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getOp_from())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        result.setOpenEndTime(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getOp_end())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        result.setTaxpayerQualification("一般纳税人");
        result.setApproveDate(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getAppr_date())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        result.setProvinceCode(bigDataCompanyDetail.getProvince_code());
        result.setProvinceName(bigDataCompanyDetail.getProvince());
        result.setCityCode(bigDataCompanyDetail.getCity_code());
        result.setCityName(bigDataCompanyDetail.getCity());
        result.setDistrictCode(bigDataCompanyDetail.getDistrict_code());
        result.setDistrictName(bigDataCompanyDetail.getDistrict());
        result.setRegistrationAuthority(bigDataCompanyDetail.getReg_org());
        //维护一级行业数据
        if (StrUtil.isNotBlank(bigDataCompanyDetail.getIndustryL1_desc())) {
            result.setFirstIndustryName(bigDataCompanyDetail.getIndustryL1_desc());
            result.setFirstIndustryCode(IndustryUtil.FIRST_INDUSTRY_MAP.get(bigDataCompanyDetail.getIndustryL1_desc()));
        }
        //维护二级行业数据
        if (CollectionUtil.isNotEmpty(bigDataCompanyDetail.getIndustryL2_desc())) {
            String secondIndustryName = bigDataCompanyDetail.getIndustryL2_desc().get(0);
            result.setSecondIndustryName(secondIndustryName);
            result.setSecondIndustryCode(IndustryUtil.SECOND_INDUSTRY_MAP.get(secondIndustryName));
        }
        result.setRegisterAddress(bigDataCompanyDetail.getReg_address());
        result.setBusinessScope(bigDataCompanyDetail.getOp_scope());
        return result;
    }

    /**
     * 拼装注册资本
     * @param reg_cap   注册资金
     * @param reg_cap_cur   注册资金单位
     * <AUTHOR>
     * @date 2024/2/29 10:39
     * @return java.lang.String
     **/
    private String registerCapital(String reg_cap, String reg_cap_cur) {
        String target = "";
        if (StringUtils.isNotBlank(reg_cap)) {
            target += reg_cap;
        }
        if (StringUtils.isNotBlank(reg_cap_cur)) {
            target += reg_cap_cur;
        }
        return target;
    }

    /**
     * 根据客户ID获取保护关系
     * @param customerId    客户ID
     * <AUTHOR>
     * @date 2024/6/5 下午4:33
     * @return com.ce.scrm.center.dao.entity.CmCustProtect
     **/
    private CmCustProtect getCmCustProtect(String customerId) throws SQLException {
        String query = "SELECT * FROM `cm_cust_protect` where cust_id = ?";
        PreparedStatement preparedStatement = smaConnection.prepareStatement(query);
        // 设置 LIMIT 和 OFFSET 参数
        preparedStatement.setString(1, customerId);
        // 执行查询
        ResultSet resultSet = preparedStatement.executeQuery();
        CmCustProtect custProtect = null;
        while (resultSet.next()) {
            custProtect = new CmCustProtect();
            custProtect.setId(resultSet.getString("ID"));
            custProtect.setCustId(resultSet.getString("CUST_ID"));
            custProtect.setCustType(resultSet.getInt("CUST_TYPE"));
            custProtect.setIntentionalityType(resultSet.getInt("INTENTIONALITY_TYPE"));
            custProtect.setSalerId(resultSet.getString("SALER_ID"));
            custProtect.setBussdeptId(resultSet.getString("BUSSDEPT_ID"));
            custProtect.setSubcompanyId(resultSet.getString("SUBCOMPANY_ID"));
            custProtect.setAreaId(resultSet.getString("AREA_ID"));
            custProtect.setMarkId(resultSet.getString("MARK_ID"));
            custProtect.setProtectTime(resultSet.getDate("PROTECT_TIME"));
            custProtect.setExceedTime(resultSet.getDate("EXCEED_TIME"));
            custProtect.setIsVisitState(resultSet.getInt("IS_VISIT_STATE"));
            custProtect.setCreateBy(resultSet.getString("CREATE_BY"));
            custProtect.setCreateTime(resultSet.getDate("CREATE_TIME"));
            custProtect.setUpdateBy(resultSet.getString("UPDATE_BY"));
            custProtect.setUpdateTime(resultSet.getDate("UPDATE_TIME"));
            custProtect.setCustSource(resultSet.getInt("CUST_SOURCE"));
            custProtect.setDataState(resultSet.getInt("DATA_STATE"));
            custProtect.setLastVisitTime(resultSet.getDate("LAST_VISIT_TIME"));
            custProtect.setVisitExceedTime(resultSet.getDate("VISIT_EXCEED_TIME"));
            custProtect.setAbsoluteProtectTime(resultSet.getDate("ABSOLUTE_PROTECT_TIME"));
            custProtect.setCustName(resultSet.getString("CUST_NAME"));
            custProtect.setBusioppoCode(resultSet.getString("BUSIOPPO_CODE"));
            custProtect.setIsStress(resultSet.getInt("IS_STRESS"));
            custProtect.setAddStressTime(resultSet.getDate("ADD_STRESS_TIME"));
            custProtect.setLastPayTime(resultSet.getDate("LAST_PAY_TIME"));
            custProtect.setIsTurnGc(resultSet.getInt("IS_TURN_GC"));
            custProtect.setCustSourceSub(resultSet.getInt("CUST_SOURCE_SUB"));
            custProtect.setCooperate(resultSet.getInt("COOPERATE"));
            custProtect.setOccupy(resultSet.getInt("OCCUPY"));
            custProtect.setIsClock(resultSet.getInt("IS_CLOCK"));
            custProtect.setClockProvince(resultSet.getString("CLOCK_PROVINCE"));
            custProtect.setClockCity(resultSet.getString("CLOCK_CITY"));
            custProtect.setClockRegion(resultSet.getString("CLOCK_REGION"));
            custProtect.setRegProvince(resultSet.getString("REG_PROVINCE"));
            custProtect.setRegCity(resultSet.getString("REG_CITY"));
            custProtect.setRegRegion(resultSet.getString("REG_REGION"));
            custProtect.setUncid(resultSet.getString("UNCID"));
            custProtect.setStatus(resultSet.getInt("STATUS"));
            custProtect.setAssignTime(resultSet.getDate("ASSIGN_TIME"));
            custProtect.setAssignCustSource(resultSet.getInt("ASSIGN_CUST_SOURCE"));
            custProtect.setAssignDate(resultSet.getDate("ASSIGN_DATE"));
            custProtect.setEntId(resultSet.getString("ENT_ID"));
            custProtect.setSource(resultSet.getString("SOURCE"));
            custProtect.setReason(resultSet.getString("REASON"));
            custProtect.setOriginalDeptId(resultSet.getString("ORIGINAL_DEPT_ID"));
            custProtect.setDbInsertTime(resultSet.getDate("db_insert_time"));
            custProtect.setDbUpdateTime(resultSet.getDate("db_update_time"));
            custProtect.setTagFlag7(resultSet.getString("tag_flag7"));
            custProtect.setTagFlag8(resultSet.getString("tag_flag8"));
            custProtect.setTagTechcompany(resultSet.getString("tag_techcompany"));
            custProtect.setCustomTags(resultSet.getString("custom_tags"));
        }
        preparedStatement.close();
        resultSet.close();
        return custProtect;
    }

    /**
     * 根据客户名称获取保护关系
     * @param customerName    客户名称
     * <AUTHOR>
     * @date 2024/6/5 下午4:33
     * @return com.ce.scrm.center.dao.entity.CmCustProtect
     **/
    private CmCustProtect getCmCustProtectByName(String customerName) throws SQLException {
        String query = "SELECT * FROM `cm_cust_protect` where cust_name = ?";
        PreparedStatement preparedStatement = smaConnection.prepareStatement(query);
        // 设置 LIMIT 和 OFFSET 参数
        preparedStatement.setString(1, customerName);
        // 执行查询
        ResultSet resultSet = preparedStatement.executeQuery();
        CmCustProtect custProtect = null;
        while (resultSet.next()) {
            custProtect = new CmCustProtect();
            custProtect.setId(resultSet.getString("ID"));
            custProtect.setCustId(resultSet.getString("CUST_ID"));
            custProtect.setCustType(resultSet.getInt("CUST_TYPE"));
            custProtect.setIntentionalityType(resultSet.getInt("INTENTIONALITY_TYPE"));
            custProtect.setSalerId(resultSet.getString("SALER_ID"));
            custProtect.setBussdeptId(resultSet.getString("BUSSDEPT_ID"));
            custProtect.setSubcompanyId(resultSet.getString("SUBCOMPANY_ID"));
            custProtect.setAreaId(resultSet.getString("AREA_ID"));
            custProtect.setMarkId(resultSet.getString("MARK_ID"));
            custProtect.setProtectTime(resultSet.getDate("PROTECT_TIME"));
            custProtect.setExceedTime(resultSet.getDate("EXCEED_TIME"));
            custProtect.setIsVisitState(resultSet.getInt("IS_VISIT_STATE"));
            custProtect.setCreateBy(resultSet.getString("CREATE_BY"));
            custProtect.setCreateTime(resultSet.getDate("CREATE_TIME"));
            custProtect.setUpdateBy(resultSet.getString("UPDATE_BY"));
            custProtect.setUpdateTime(resultSet.getDate("UPDATE_TIME"));
            custProtect.setCustSource(resultSet.getInt("CUST_SOURCE"));
            custProtect.setDataState(resultSet.getInt("DATA_STATE"));
            custProtect.setLastVisitTime(resultSet.getDate("LAST_VISIT_TIME"));
            custProtect.setVisitExceedTime(resultSet.getDate("VISIT_EXCEED_TIME"));
            custProtect.setAbsoluteProtectTime(resultSet.getDate("ABSOLUTE_PROTECT_TIME"));
            custProtect.setCustName(resultSet.getString("CUST_NAME"));
            custProtect.setBusioppoCode(resultSet.getString("BUSIOPPO_CODE"));
            custProtect.setIsStress(resultSet.getInt("IS_STRESS"));
            custProtect.setAddStressTime(resultSet.getDate("ADD_STRESS_TIME"));
            custProtect.setLastPayTime(resultSet.getDate("LAST_PAY_TIME"));
            custProtect.setIsTurnGc(resultSet.getInt("IS_TURN_GC"));
            custProtect.setCustSourceSub(resultSet.getInt("CUST_SOURCE_SUB"));
            custProtect.setCooperate(resultSet.getInt("COOPERATE"));
            custProtect.setOccupy(resultSet.getInt("OCCUPY"));
            custProtect.setIsClock(resultSet.getInt("IS_CLOCK"));
            custProtect.setClockProvince(resultSet.getString("CLOCK_PROVINCE"));
            custProtect.setClockCity(resultSet.getString("CLOCK_CITY"));
            custProtect.setClockRegion(resultSet.getString("CLOCK_REGION"));
            custProtect.setRegProvince(resultSet.getString("REG_PROVINCE"));
            custProtect.setRegCity(resultSet.getString("REG_CITY"));
            custProtect.setRegRegion(resultSet.getString("REG_REGION"));
            custProtect.setUncid(resultSet.getString("UNCID"));
            custProtect.setStatus(resultSet.getInt("STATUS"));
            custProtect.setAssignTime(resultSet.getDate("ASSIGN_TIME"));
            custProtect.setAssignCustSource(resultSet.getInt("ASSIGN_CUST_SOURCE"));
            custProtect.setAssignDate(resultSet.getDate("ASSIGN_DATE"));
            custProtect.setEntId(resultSet.getString("ENT_ID"));
            custProtect.setSource(resultSet.getString("SOURCE"));
            custProtect.setReason(resultSet.getString("REASON"));
            custProtect.setOriginalDeptId(resultSet.getString("ORIGINAL_DEPT_ID"));
            custProtect.setDbInsertTime(resultSet.getDate("db_insert_time"));
            custProtect.setDbUpdateTime(resultSet.getDate("db_update_time"));
            custProtect.setTagFlag7(resultSet.getString("tag_flag7"));
            custProtect.setTagFlag8(resultSet.getString("tag_flag8"));
            custProtect.setTagTechcompany(resultSet.getString("tag_techcompany"));
            custProtect.setCustomTags(resultSet.getString("custom_tags"));
        }
        preparedStatement.close();
        resultSet.close();
        return custProtect;
    }

    /**
     * 添加保护关系
     * @param gcEmployeeData    高呈员工数据
     * @param customer  客户数据
     * <AUTHOR>
     * @date 2024/6/6 上午9:08
     * @return void
     **/
    private void addProtectData(GcEmployeeData gcEmployeeData, Customer customer) throws SQLException {
        CmCustProtect cmCustProtect = new CmCustProtect();
        cmCustProtect.setId(UUID.fastUUID().toString(true));
        cmCustProtect.setCustId(customer.getCustomerId());
        cmCustProtect.setCustName(customer.getCustomerName());
        cmCustProtect.setCustType(2);
        cmCustProtect.setSalerId(gcEmployeeData.getEmployeeId());
        cmCustProtect.setBussdeptId(gcEmployeeData.getDeptId());
        cmCustProtect.setSubcompanyId(gcEmployeeData.getSubId());
        cmCustProtect.setAreaId(gcEmployeeData.getAreaId());
        cmCustProtect.setMarkId(getMarketIdBySubId(gcEmployeeData.getSubId()));
        cmCustProtect.setCustSource(CustSourceEnum.GAOCHENG.getValue());
        cmCustProtect.setCustSourceSub(6);
        cmCustProtect.setOccupy(1);
        cmCustProtect.setStatus(ProtectStateEnum.PROTECT.getState());
        cmCustProtect.setProtectTime(new java.util.Date());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new java.util.Date());
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        java.util.Date newDate = calendar.getTime();
        cmCustProtect.setExceedTime(newDate);
        cmCustProtect.setUncid(customer.getCertificateCode());
        cmCustProtect.setEntId(customer.getSourceDataId());
        cmCustProtect.setRegProvince(customer.getProvinceCode());
        cmCustProtect.setRegCity(customer.getCityCode());
        cmCustProtect.setRegRegion(customer.getDistrictCode());
        cmCustProtect.setCreateBy(gcEmployeeData.getEmployeeId());
        cmCustProtect.setCreateTime(new java.util.Date());
        cmCustProtect.setUpdateBy(gcEmployeeData.getEmployeeId());
        cmCustProtect.setUpdateTime(new java.util.Date());
        String insertSQL = "INSERT INTO cm_cust_protect (ID, CUST_ID, CUST_NAME, CUST_TYPE, SALER_ID, BUSSDEPT_ID, SUBCOMPANY_ID, AREA_ID, MARK_ID, CUST_SOURCE, CUST_SOURCE_SUB, OCCUPY, STATUS, PROTECT_TIME, EXCEED_TIME, UNCID, ENT_ID, REG_PROVINCE, REG_CITY, REG_REGION, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement pstmt = smaConnection.prepareStatement(insertSQL);
        pstmt.setString(1, cmCustProtect.getId());
        pstmt.setString(2, cmCustProtect.getCustId());
        pstmt.setString(3, cmCustProtect.getCustName());
        pstmt.setInt(4, cmCustProtect.getCustType());
        pstmt.setString(5, cmCustProtect.getSalerId());
        pstmt.setString(6, cmCustProtect.getBussdeptId());
        pstmt.setString(7, cmCustProtect.getSubcompanyId());
        pstmt.setString(8, cmCustProtect.getAreaId());
        pstmt.setString(9, cmCustProtect.getMarkId());
        pstmt.setInt(10, cmCustProtect.getCustSource());
        pstmt.setInt(11, cmCustProtect.getCustSourceSub());
        pstmt.setInt(12, cmCustProtect.getOccupy());
        pstmt.setInt(13, cmCustProtect.getStatus());
        pstmt.setTimestamp(14, new java.sql.Timestamp(cmCustProtect.getProtectTime().getTime()));
        pstmt.setTimestamp(15, new java.sql.Timestamp(cmCustProtect.getExceedTime().getTime()));
        pstmt.setString(16, cmCustProtect.getUncid());
        pstmt.setString(17, cmCustProtect.getEntId());
        pstmt.setString(18, cmCustProtect.getRegProvince());
        pstmt.setString(19, cmCustProtect.getRegCity());
        pstmt.setString(20, cmCustProtect.getRegRegion());
        pstmt.setString(21, cmCustProtect.getCreateBy());
        pstmt.setTimestamp(22, new java.sql.Timestamp(cmCustProtect.getCreateTime().getTime()));
        pstmt.setString(23, cmCustProtect.getUpdateBy());
        pstmt.setTimestamp(24, new java.sql.Timestamp(cmCustProtect.getUpdateTime().getTime()));
        pstmt.executeUpdate();
        pstmt.close();
        SmaConvertLog smaConvertLog = new SmaConvertLog();
        smaConvertLog.setCustomerId(cmCustProtect.getCustId());
        smaConvertLog.setCustomerName(cmCustProtect.getCustName());
        smaConvertLog.setAreaId(cmCustProtect.getAreaId());
        smaConvertLog.setSubId(cmCustProtect.getSubcompanyId());
        smaConvertLog.setDeptId(cmCustProtect.getBussdeptId());
        smaConvertLog.setSalerId(cmCustProtect.getSalerId());
        smaConvertLog.setCustomerType(cmCustProtect.getCustType());
        smaConvertLog.setEntId(cmCustProtect.getEntId());
        smaConvertLog.setOperator(cmCustProtect.getSalerId());
        smaConvertLog.setOperateTime(new java.util.Date());
        addConvertLog(smaConvertLog);
    }

    /**
     * 添加流转日志
     * @param smaConvertLog 流转日志数据
     * <AUTHOR>
     * @date 2024/6/6 下午1:41
     * @return void
     **/
    private void addConvertLog(SmaConvertLog smaConvertLog) throws SQLException {
        String insertSQL = "INSERT INTO sma_convert_log (id, cust_id, cust_name, dept_of_saler_id, saler_id, area_of_cur_saler_id, " +
                "subcompany_of_cur_saler_id, release_reason, convert_type, cust_type, ent_id, create_by, create_time, dept_of_cur_saler_id, " +
                "cur_saler_id, area_of_saler_id, subcompany_of_saler_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement preparedStatement = extendConnection.prepareStatement(insertSQL);
        preparedStatement.setString(1, smaConvertLog.getId());
        preparedStatement.setString(2, smaConvertLog.getCustomerId());
        preparedStatement.setString(3, smaConvertLog.getCustomerName());
        preparedStatement.setString(4, smaConvertLog.getOriginDeptId());
        preparedStatement.setString(5, smaConvertLog.getOriginSalerId());
        preparedStatement.setString(6, smaConvertLog.getAreaId());
        preparedStatement.setString(7, smaConvertLog.getSubId());
        preparedStatement.setString(8, smaConvertLog.getReleaseReason());
        preparedStatement.setInt(9, smaConvertLog.getConvertType());
        preparedStatement.setInt(10, smaConvertLog.getCustomerType());
        preparedStatement.setString(11, smaConvertLog.getEntId());
        preparedStatement.setString(12, smaConvertLog.getOperator());
        preparedStatement.setTimestamp(13, new Timestamp(smaConvertLog.getOperateTime().getTime()));
        preparedStatement.setString(14, smaConvertLog.getDeptId());
        preparedStatement.setString(15, smaConvertLog.getSalerId());
        preparedStatement.setString(16, smaConvertLog.getOriginAreaId());
        preparedStatement.setString(17, smaConvertLog.getOriginSubId());
        preparedStatement.executeUpdate();
        preparedStatement.close();
    }

    /**
     * 更新保护关系
     * @param cmCustProtect    客户关系
     * @param gcEmployeeData    高呈员工数据
     * @param customer  客户数据
     * <AUTHOR>
     * @date 2024/6/6 上午9:08
     **/
    private void updateProtectData(CmCustProtect cmCustProtect, GcEmployeeData gcEmployeeData, Customer customer) throws SQLException {
        SmaConvertLog smaConvertLog = new SmaConvertLog();
        smaConvertLog.setOriginAreaId(cmCustProtect.getAreaId());
        smaConvertLog.setOriginSubId(cmCustProtect.getSubcompanyId());
        smaConvertLog.setOriginDeptId(cmCustProtect.getBussdeptId());
        smaConvertLog.setOriginSalerId(cmCustProtect.getSalerId());
        cmCustProtect.setSalerId(gcEmployeeData.getEmployeeId());
        cmCustProtect.setBussdeptId(gcEmployeeData.getDeptId());
        cmCustProtect.setSubcompanyId(gcEmployeeData.getSubId());
        cmCustProtect.setAreaId(gcEmployeeData.getAreaId());
        cmCustProtect.setMarkId(getMarketIdBySubId(gcEmployeeData.getSubId()));
        cmCustProtect.setOccupy(1);
        cmCustProtect.setStatus(ProtectStateEnum.PROTECT.getState());
        cmCustProtect.setProtectTime(new java.util.Date());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new java.util.Date());
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        java.util.Date newDate = calendar.getTime();
        cmCustProtect.setExceedTime(newDate);
        cmCustProtect.setUncid(customer.getCertificateCode());
        cmCustProtect.setEntId(customer.getSourceDataId());
        cmCustProtect.setRegProvince(customer.getProvinceCode());
        cmCustProtect.setRegCity(customer.getCityCode());
        cmCustProtect.setRegRegion(customer.getDistrictCode());
        cmCustProtect.setUpdateBy(gcEmployeeData.getEmployeeId());
        cmCustProtect.setUpdateTime(new java.util.Date());
        String updateSQL = "UPDATE cm_cust_protect SET SALER_ID = ?, BUSSDEPT_ID = ?, SUBCOMPANY_ID = ?, AREA_ID = ?, MARK_ID = ?, OCCUPY = ?, STATUS = ?, PROTECT_TIME = ?, EXCEED_TIME = ?, UNCID = ?, ENT_ID = ?, REG_PROVINCE = ?, REG_CITY = ?, REG_REGION = ?, UPDATE_BY = ?, UPDATE_TIME = ? WHERE ID = ?";
        PreparedStatement pstmt = smaConnection.prepareStatement(updateSQL);
        pstmt.setString(1, cmCustProtect.getSalerId());
        pstmt.setString(2, cmCustProtect.getBussdeptId());
        pstmt.setString(3, cmCustProtect.getSubcompanyId());
        pstmt.setString(4, cmCustProtect.getAreaId());
        pstmt.setString(5, cmCustProtect.getMarkId());
        pstmt.setInt(6, cmCustProtect.getOccupy());
        pstmt.setInt(7, cmCustProtect.getStatus());
        pstmt.setTimestamp(8, new java.sql.Timestamp(cmCustProtect.getProtectTime().getTime()));
        pstmt.setTimestamp(9, new java.sql.Timestamp(cmCustProtect.getExceedTime().getTime()));
        pstmt.setString(10, cmCustProtect.getUncid());
        pstmt.setString(11, cmCustProtect.getEntId());
        pstmt.setString(12, cmCustProtect.getRegProvince());
        pstmt.setString(13, cmCustProtect.getRegCity());
        pstmt.setString(14, cmCustProtect.getRegRegion());
        pstmt.setString(15, cmCustProtect.getUpdateBy());
        pstmt.setTimestamp(16, new java.sql.Timestamp(cmCustProtect.getUpdateTime().getTime()));
        pstmt.setString(17, cmCustProtect.getId());
        pstmt.executeUpdate();
        pstmt.close();
        smaConvertLog.setCustomerId(cmCustProtect.getCustId());
        smaConvertLog.setCustomerName(cmCustProtect.getCustName());
        smaConvertLog.setAreaId(cmCustProtect.getAreaId());
        smaConvertLog.setSubId(cmCustProtect.getSubcompanyId());
        smaConvertLog.setDeptId(cmCustProtect.getBussdeptId());
        smaConvertLog.setSalerId(cmCustProtect.getSalerId());
        smaConvertLog.setCustomerType(cmCustProtect.getCustType());
        smaConvertLog.setEntId(cmCustProtect.getEntId());
        smaConvertLog.setOperator(cmCustProtect.getSalerId());
        smaConvertLog.setOperateTime(new java.util.Date());
        addConvertLog(smaConvertLog);
    }

    /**
     * 根据分司ID获取市场ID
     * @param subId 分司ID
     * <AUTHOR>
     * @date 2024/6/6 上午9:28
     * @return java.lang.String
     **/
    private String getMarketIdBySubId(String subId) throws SQLException {
        String query = "SELECT MARKET_ID FROM sma_market_subcompay WHERE SUB_COMPANY = ?";
        String marketId = null;
        PreparedStatement pstmt = smaConnection.prepareStatement(query);

        pstmt.setString(1, subId);
        ResultSet rs = pstmt.executeQuery();
        while (rs.next()) {
            marketId = rs.getString("MARKET_ID");
        }
        pstmt.close();
        rs.close();
        return marketId;
    }

    /**
     * 保存跟进记录
     * @param customer  客户数据
     * @param gcEmployeeData    员工数据
     * @param content   跟进内容
     * <AUTHOR>
     * @date 2024/6/6 上午11:36
     **/
    private void saveCustomerFollowData(Customer customer, GcEmployeeData gcEmployeeData, String content) throws SQLException {
        CustomerFollow customerFollow = new CustomerFollow();
        customerFollow.setCustomerId(customer.getCustomerId());
        customerFollow.setCustomerName(customer.getCustomerName());
        customerFollow.setEmpId(gcEmployeeData.getEmployeeId());
        customerFollow.setDeptId(gcEmployeeData.getDeptId());
        customerFollow.setSubId(gcEmployeeData.getSubId());
        customerFollow.setContent(content);
        String insertSQL = "INSERT INTO customer_follow (customer_id, customer_name, emp_id, dept_id, sub_id, content) VALUES (?, ?, ?, ?, ?, ?)";
        PreparedStatement preparedStatement = smaConnection.prepareStatement(insertSQL);
        preparedStatement.setString(1, customerFollow.getCustomerId());
        preparedStatement.setString(2, customerFollow.getCustomerName());
        preparedStatement.setString(3, customerFollow.getEmpId());
        preparedStatement.setString(4, customerFollow.getDeptId());
        preparedStatement.setString(5, customerFollow.getSubId());
        preparedStatement.setString(6, customerFollow.getContent());
        preparedStatement.executeUpdate();
        preparedStatement.close();
    }
}