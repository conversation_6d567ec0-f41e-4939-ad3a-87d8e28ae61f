package com.ce.scrm.center.web.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 行业工具类
 * <AUTHOR>
 * @date 2024/6/5 下午2:22
 * @version 1.0.0
 */
public class IndustryUtil {
    /**
     * 一级行业数据
     */
    public final static Map<String, String> FIRST_INDUSTRY_MAP = new HashMap<>();
    /**
     * 二级行业数据
     */
    public final static Map<String, String> SECOND_INDUSTRY_MAP = new HashMap<>();

    static {
        // 一级数据
        FIRST_INDUSTRY_MAP.put("农、林、牧、渔业", "A");
        FIRST_INDUSTRY_MAP.put("采矿业", "B");
        FIRST_INDUSTRY_MAP.put("制造业", "C");
        FIRST_INDUSTRY_MAP.put("电力、热力、燃气及水生产和供应业", "D");
        FIRST_INDUSTRY_MAP.put("建筑业", "E");
        FIRST_INDUSTRY_MAP.put("批发和零售业", "F");
        FIRST_INDUSTRY_MAP.put("交通运输、仓储和邮政业", "G");
        FIRST_INDUSTRY_MAP.put("住宿和餐饮业", "H");
        FIRST_INDUSTRY_MAP.put("信息传输、软件和信息技术服务业", "I");
        FIRST_INDUSTRY_MAP.put("金融业", "J");
        FIRST_INDUSTRY_MAP.put("房地产业", "K");
        FIRST_INDUSTRY_MAP.put("租赁和商务服务业", "L");
        FIRST_INDUSTRY_MAP.put("科学研究和技术服务业", "M");
        FIRST_INDUSTRY_MAP.put("水利、环境和公共设施管理业", "N");
        FIRST_INDUSTRY_MAP.put("居民服务、修理和其他服务业", "O");
        FIRST_INDUSTRY_MAP.put("教育", "P");
        FIRST_INDUSTRY_MAP.put("卫生和社会工作", "Q");
        FIRST_INDUSTRY_MAP.put("文化、体育和娱乐业", "R");
        FIRST_INDUSTRY_MAP.put("公共管理、社会保障和社会组织", "S");
        FIRST_INDUSTRY_MAP.put("国际组织", "T");

        // 二级数据
        SECOND_INDUSTRY_MAP.put("农业", "1");
        SECOND_INDUSTRY_MAP.put("林业", "2");
        SECOND_INDUSTRY_MAP.put("畜牧业", "3");
        SECOND_INDUSTRY_MAP.put("渔业", "4");
        SECOND_INDUSTRY_MAP.put("农、林、牧、渔专业及辅助性活动", "5");
        SECOND_INDUSTRY_MAP.put("煤炭开采和洗选业", "6");
        SECOND_INDUSTRY_MAP.put("石油和天然气开采业", "7");
        SECOND_INDUSTRY_MAP.put("黑色金属矿采选业", "8");
        SECOND_INDUSTRY_MAP.put("有色金属矿采选业", "9");
        SECOND_INDUSTRY_MAP.put("非金属矿采选业", "10");
        SECOND_INDUSTRY_MAP.put("开采专业及辅助性活动", "11");
        SECOND_INDUSTRY_MAP.put("其他采矿业", "12");
        SECOND_INDUSTRY_MAP.put("农副食品加工业", "13");
        SECOND_INDUSTRY_MAP.put("食品制造业", "14");
        SECOND_INDUSTRY_MAP.put("酒、饮料和精制茶制造业", "15");
        SECOND_INDUSTRY_MAP.put("烟草制品业", "16");
        SECOND_INDUSTRY_MAP.put("纺织业", "17");
        SECOND_INDUSTRY_MAP.put("纺织服装、服饰业", "18");
        SECOND_INDUSTRY_MAP.put("皮革、毛皮、羽毛及其制品和制鞋业", "19");
        SECOND_INDUSTRY_MAP.put("木材加工和木、竹、藤、棕、草制品业", "20");
        SECOND_INDUSTRY_MAP.put("家具制造业", "21");
        SECOND_INDUSTRY_MAP.put("造纸和纸制品业", "22");
        SECOND_INDUSTRY_MAP.put("印刷和记录媒介复制业", "23");
        SECOND_INDUSTRY_MAP.put("文教、工美、体育和娱乐用品制造业", "24");
        SECOND_INDUSTRY_MAP.put("石油、煤炭及其他燃料加工业", "25");
        SECOND_INDUSTRY_MAP.put("化学原料和化学制品制造业", "26");
        SECOND_INDUSTRY_MAP.put("医药制造业", "27");
        SECOND_INDUSTRY_MAP.put("化学纤维制造业", "28");
        SECOND_INDUSTRY_MAP.put("橡胶和塑料制品业", "29");
        SECOND_INDUSTRY_MAP.put("非金属矿物制品业", "30");
        SECOND_INDUSTRY_MAP.put("黑色金属冶炼和压延加工业", "31");
        SECOND_INDUSTRY_MAP.put("有色金属冶炼和压延加工业", "32");
        SECOND_INDUSTRY_MAP.put("金属制品业", "33");
        SECOND_INDUSTRY_MAP.put("通用设备制造业", "34");
        SECOND_INDUSTRY_MAP.put("专用设备制造业", "35");
        SECOND_INDUSTRY_MAP.put("汽车制造业", "36");
        SECOND_INDUSTRY_MAP.put("铁路、船舶、航空航天和其他运输设备制造业", "37");
        SECOND_INDUSTRY_MAP.put("电气机械和器材制造业", "38");
        SECOND_INDUSTRY_MAP.put("计算机、通信和其他电子设备制造业", "39");
        SECOND_INDUSTRY_MAP.put("仪器仪表制造业", "40");
        SECOND_INDUSTRY_MAP.put("其他制造业", "41");
        SECOND_INDUSTRY_MAP.put("废弃资源综合利用业", "42");
        SECOND_INDUSTRY_MAP.put("金属制品、机械和设备修理业", "43");
        SECOND_INDUSTRY_MAP.put("电力、热力生产和供应业", "44");
        SECOND_INDUSTRY_MAP.put("燃气生产和供应业", "45");
        SECOND_INDUSTRY_MAP.put("水的生产和供应业", "46");
        SECOND_INDUSTRY_MAP.put("房屋建筑业", "47");
        SECOND_INDUSTRY_MAP.put("土木工程建筑业", "48");
        SECOND_INDUSTRY_MAP.put("建筑安装业", "49");
        SECOND_INDUSTRY_MAP.put("建筑装饰、装修和其他建筑业", "50");
        SECOND_INDUSTRY_MAP.put("批发业", "51");
        SECOND_INDUSTRY_MAP.put("零售业", "52");
        SECOND_INDUSTRY_MAP.put("铁路运输业", "53");
        SECOND_INDUSTRY_MAP.put("道路运输业", "54");
        SECOND_INDUSTRY_MAP.put("水上运输业", "55");
        SECOND_INDUSTRY_MAP.put("航空运输业", "56");
        SECOND_INDUSTRY_MAP.put("管道运输业", "57");
        SECOND_INDUSTRY_MAP.put("多式联运和运输代理业", "58");
        SECOND_INDUSTRY_MAP.put("装卸搬运和仓储业", "59");
        SECOND_INDUSTRY_MAP.put("邮政业", "60");
        SECOND_INDUSTRY_MAP.put("住宿业", "61");
        SECOND_INDUSTRY_MAP.put("餐饮业", "62");
        SECOND_INDUSTRY_MAP.put("电信、广播电视和卫星传输服务", "63");
        SECOND_INDUSTRY_MAP.put("互联网和相关服务", "64");
        SECOND_INDUSTRY_MAP.put("软件和信息技术服务业", "65");
        SECOND_INDUSTRY_MAP.put("货币金融服务", "66");
        SECOND_INDUSTRY_MAP.put("资本市场服务", "67");
        SECOND_INDUSTRY_MAP.put("保险业", "68");
        SECOND_INDUSTRY_MAP.put("其他金融业", "69");
        SECOND_INDUSTRY_MAP.put("房地产业", "70");
        SECOND_INDUSTRY_MAP.put("租赁业", "71");
        SECOND_INDUSTRY_MAP.put("商务服务业", "72");
        SECOND_INDUSTRY_MAP.put("研究和试验发展", "73");
        SECOND_INDUSTRY_MAP.put("专业技术服务业", "74");
        SECOND_INDUSTRY_MAP.put("科技推广和应用服务业", "75");
        SECOND_INDUSTRY_MAP.put("水利管理业", "76");
        SECOND_INDUSTRY_MAP.put("生态保护和环境治理业", "77");
        SECOND_INDUSTRY_MAP.put("公共设施管理业", "78");
        SECOND_INDUSTRY_MAP.put("土地管理业", "79");
        SECOND_INDUSTRY_MAP.put("居民服务业", "80");
        SECOND_INDUSTRY_MAP.put("机动车、电子产品和日用产品修理业", "81");
        SECOND_INDUSTRY_MAP.put("其他服务业", "82");
        SECOND_INDUSTRY_MAP.put("教育", "83");
        SECOND_INDUSTRY_MAP.put("卫生", "84");
        SECOND_INDUSTRY_MAP.put("社会工作", "85");
        SECOND_INDUSTRY_MAP.put("新闻和出版业", "86");
        SECOND_INDUSTRY_MAP.put("广播、电视、电影和录音制作业", "87");
        SECOND_INDUSTRY_MAP.put("文化艺术业", "88");
        SECOND_INDUSTRY_MAP.put("体育", "89");
        SECOND_INDUSTRY_MAP.put("娱乐业", "90");
        SECOND_INDUSTRY_MAP.put("中国共产党机关", "91");
        SECOND_INDUSTRY_MAP.put("国家机构", "92");
        SECOND_INDUSTRY_MAP.put("人民政协、民主党派", "93");
        SECOND_INDUSTRY_MAP.put("社会保障", "94");
        SECOND_INDUSTRY_MAP.put("群众团体、社会团体和其他成员组织", "95");
        SECOND_INDUSTRY_MAP.put("基层群众自治组织及其他组织", "96");
        SECOND_INDUSTRY_MAP.put("国际组织", "97");
    }
}