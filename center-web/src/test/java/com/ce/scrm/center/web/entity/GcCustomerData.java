package com.ce.scrm.center.web.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 高呈客户数据
 * <AUTHOR>
 * @date 2024/6/5 上午9:15
 * @version 1.0.0
 */
@Data
public class GcCustomerData {
    /**
     * 高呈客户ID:业务无用，导出标识唯一数据所用
     */
    @ExcelProperty(index = 0)
    private String id;
    /**
     * 客户名称：只处理客户名称不为空的数据
     */
    @ExcelProperty(index = 1)
    private String customerName;
    /**
     * 数据状态：following（跟进中）、converted（已转化）只处理这两种状态，其他状态仅维护客户数据
     */
    @ExcelProperty(index = 2)
    private String state;
    /**
     * 数据状态：following（跟进中）、converted（已转化）只处理这两种状态，其他状态仅维护客户数据
     */
    @ExcelProperty(index = 3)
    private String stateName;
    /**
     * 商务名称：仅处理名称不为空的数据，其他状态仅维护客户数据
     */
    @ExcelProperty(index = 4)
    private String salerName;
    /**
     * 跟进内容
     */
    @ExcelProperty(index = 9)
    private String followContent;
}