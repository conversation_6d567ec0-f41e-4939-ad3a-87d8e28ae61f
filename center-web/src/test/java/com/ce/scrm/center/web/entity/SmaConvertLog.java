package com.ce.scrm.center.web.entity;

import cn.hutool.core.lang.UUID;
import lombok.Data;

import java.util.Date;

/**
 * 流转日志
 * <AUTHOR>
 * @date 2024/6/6 上午11:00
 * @version 1.0.0
 */
@Data
public class SmaConvertLog {
    private String id = UUID.fastUUID().toString(true);
    private Integer convertType = 221;
    private String customerId;
    private String customerName;
    private String originAreaId;
    private String originSubId;
    private String originDeptId;
    private String originSalerId;
    private String areaId;
    private String subId;
    private String deptId;
    private String salerId;
    private String releaseReason = "高呈融合导入";
    private Integer customerType;
    private String entId;
    private String operator;
    private Date operateTime;
}