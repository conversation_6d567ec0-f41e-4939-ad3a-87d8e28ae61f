package com.ce.scrm.center.web.util;

import com.ce.scrm.center.web.ClearGcCustomerDataTests;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.TestExecutionExceptionHandler;

public class TestExceptionHandler implements TestExecutionExceptionHandler {

    @Override
    public void handleTestExecutionException(ExtensionContext context, Throwable throwable) throws Throwable {
        ClearGcCustomerDataTests.testFailed = true;
        throw throwable;  // 重新抛出异常
    }
}
