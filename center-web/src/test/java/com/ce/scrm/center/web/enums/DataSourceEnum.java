package com.ce.scrm.center.web.enums;

import lombok.Getter;

/**
 * 数据库配置
 * <AUTHOR>
 * @date 2024/5/31 下午1:44
 * @version 1.0.0
 */
@Getter
public enum DataSourceEnum {
    EXTEND_TEST("*******************************************************", "ecuser", "NrcpMktWx#21"),
    EXTEND_PROD("******************************************************", "ecuser", "NrcpMktWx#21"),
    SMA_TEST("****************************************************", "ecuser", "NrcpMktWx#21"),
    SMA_PROD("************************************************", "ecuser", "NrcpMktWx#21"),
    CUSTOMER_TEST("*********************************************************", "ecuser", "NrcpMktWx#21"),
    CUSTOMER_PROD("********************************************************", "ecuser", "NrcpMktWx#21"),
    ;

    private final String dbUrl;
    private final String dbUser;
    private final String dbPassword;

    DataSourceEnum(String dbUrl, String dbUser, String dbPassword) {
        this.dbUrl = dbUrl;
        this.dbUser = dbUser;
        this.dbPassword = dbPassword;
    }
}