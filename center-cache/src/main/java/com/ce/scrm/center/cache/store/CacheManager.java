package com.ce.scrm.center.cache.store;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 缓存的管理类
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
@Component
public class CacheManager implements ApplicationContextAware {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(CacheManager.class);
    /**
     * 应用上下文
     */
    private ApplicationContext applicationContext;

    /**
     * 获取所有可获取锁的方式
     */
    private final ConcurrentHashMap<String, Cache> cacheMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        Map<String, Cache> lockBeanList = applicationContext.getBeansOfType(Cache.class);
        for (Map.Entry<String, Cache> entry : lockBeanList.entrySet()) {
            cacheMap.put(entry.getValue().getName(), entry.getValue());
        }
        if (cacheMap.isEmpty()) {
            LOGGER.error("当前未获取到可以使用的缓存实现");
            throw new RuntimeException("当前未获取到可以使用的缓存实现");
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 根据内存的名称获取特定类型的内存数据库
     * @param cacheName  锁的名称
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return com.ce.scrm.center.cache.handler.Cache
     **/
    public Cache getCache(String cacheName) {
        return cacheMap.get(cacheName);
    }
}
