package com.ce.scrm.center.cache.lock;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 锁的管理类
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
@Component
public class LockManager implements ApplicationContextAware {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(LockManager.class);

    /**
     * 应用上下文
     */
    private ApplicationContext applicationContext;

    /**
     * 获取所有可获取锁的方式
     */
    private final ConcurrentHashMap<String, Lock> lockMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        Map<String, Lock> lockBeanList = applicationContext.getBeansOfType(Lock.class);
        for (Map.Entry<String, Lock> entry : lockBeanList.entrySet()) {
            lockMap.put(entry.getValue().getName(), entry.getValue());
        }
        if (lockMap.isEmpty()) {
            LOGGER.error("当前未获取到可以使用的分布式锁实现");
            throw new RuntimeException("当前未获取到可以使用的分布式锁实现");
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 根据锁的名称获取特定类型的分布式锁
     * @param lockName  锁的名称
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return com.ce.scrm.center.cache.lock.Lock
     **/
    public Lock getLock(String lockName) {
        return lockMap.get(lockName);
    }
}
