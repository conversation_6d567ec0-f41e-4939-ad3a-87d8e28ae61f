package com.ce.scrm.center.cache.store;

import com.ce.scrm.center.cache.entity.SortCollectionData;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 缓存处理
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
public interface Cache {
    /**
     * 获取当前操作缓存方式的名称
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.lang.String
     **/
    String getName();

    //--------------------------------------通用命令------------------------------

    /**
     * 模糊获取所有的key
     * @param prefix    查询条件
     * <AUTHOR>
     * @date 2023/6/25 10:32
     * @return java.util.Set<java.lang.String>
     **/
    Set<String> scan(String prefix);

    /**
     * 判断缓存数据是否存在
     * @param key   缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    boolean exist(String key);

    /**
     * 删除缓存
     * @param key 缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void del(String key);

    /**
     * 批量删除缓存
     * @param cacheKeyList  缓存键列表
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void batchDel(List<String> cacheKeyList);

    /**
     * 批量删除缓存
     * @param cacheKeyList  缓存键列表
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    List<String> batchGet(List<String> cacheKeyList);

    //--------------------------------------String类型------------------------------

    /**
     * 设置缓存数据，带有效期
     * @param key    缓存键
     * @param value  缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void set(String key, String value, long expire);

    /**
     * 获取缓存数据
     * @param key 缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.lang.String
     **/
    String get(String key);

    //--------------------------------------hash类型------------------------------

    /**
     * 设置hash数据
     * @param key   缓存键
     * @param hValue  缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void hSet(String key, Map<String, String> hValue, long expire);

    /**
     * 设置hash数据
     * @param key   缓存键
     * @param hashKey 缓存hashKey
     * @param hValueItem  单条缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void hSetItem(String key, String hashKey, String hValueItem);

    /**
     * 获取hash数据
     * @param key   缓存键
     * @param hashKey 缓存hashKey
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.lang.String
     **/
    String hGetItem(String key, String hashKey);

    /**
     * 获取hash数据
     * @param key   缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.util.Map<java.lang.String, java.lang.String>
     **/
    Map<String, String> hGet(String key);

    //--------------------------------------list类型------------------------------

    /**
     * 设置list数据
     * @param key   缓存键
     * @param lValue  缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void lSet(String key, List<String> lValue, long expire);

    /**
     * 给list中添加单条数据
     * @param key   缓存键
     * @param lValueItem  缓存值
     * @param insertNode 插入节点
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void lSetItem(String key, String lValueItem, int insertNode);

    /**
     * 获取list数据
     * @param key   缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.util.List<java.lang.String>
     **/
    List<String> lGet(String key);
    //--------------------------------------set类型-------------------------------

    /**
     * 设置set数据
     * @param key   缓存键
     * @param sValue  缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void sSet(String key, Set<String> sValue, long expire);

    /**
     * 设置set数据
     * @param key   缓存键
     * @param sValue  缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void sSetItem(String key, String sValue);

    /**
     * 获取set数据
     * @param key   缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.util.Set<java.lang.String>
     **/
    Set<String> sGet(String key);
    //--------------------------------------zSet类型------------------------------

    /**
     * 设置有序集合
     * @param key   缓存键
     * @param zValue 元素值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void zSet(String key, List<SortCollectionData> zValue, long expire);

    /**
     * 设置有序集合
     * @param key   缓存键
     * @param zValueItem 元素值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void zSetItem(String key, SortCollectionData zValueItem);

    /**
     * 获取有序集合
     * @param key   缓存键
     * @param orderBy 排序方式
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.util.Set<com.ce.scrm.center.cache.entity.SortCollectionData>
     **/
    List<SortCollectionData> zGet(String key, int orderBy);

    /**
     * 获取有序集合
     * @param key   缓存键
     * @param weight 权重
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.util.Set<com.ce.scrm.center.cache.entity.SortCollectionData>
     **/
    Set<SortCollectionData> zGetItem(String key, double weight);
}
