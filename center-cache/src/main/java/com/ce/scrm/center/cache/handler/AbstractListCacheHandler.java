package com.ce.scrm.center.cache.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.ce.scrm.center.cache.handler.core.AbstractCacheHandler;
import com.ce.scrm.center.cache.constant.CacheConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 列表缓存处理模板类
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
public abstract class AbstractListCacheHandler<T> extends AbstractCacheHandler<T, List<String>, List<T>> {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(AbstractListCacheHandler.class);

    /**
     * 头部
     */
    protected final static int HEAD = 0;
    /**
     * 尾部
     */
    protected final static int TAIL = 1;

    /**
     * 获取插入节点
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return int
     **/
    protected int getInsertNode() {
        return TAIL;
    }

    /**
     * 获取缓存数据
     * @param cacheKey  缓存key
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return List<String>
     **/
    @Override
    protected List<String> getCacheValue(String cacheKey) {
        return getCache().lGet(cacheKey);
    }

    /**
     * 设置缓存数据
     * @param cacheKey  缓存key
     * @param cacheValue    缓存值
     * @param expire    缓存有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected void setCacheData(String cacheKey, List<String> cacheValue, long expire) {
        getCache().lSet(cacheKey, cacheValue, expire);
    }

    /**
     * 检查是否为空数据
     * @param returnValue  缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    @Override
    protected boolean checkNullData(List<String> returnValue) {
        return getNullData().equals(returnValue);
    }

    /**
     * 设置缓存默认数据
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return List<String>
     **/
    @Override
    protected List<String> getNullData() {
        return CacheConstant.CacheNullData.LIST;
    }

    /**
     * 缓存数据判空
     * @param dataList 缓存数据
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    @Override
    protected boolean isEmpty(List<String> dataList) {
        return CollectionUtil.isEmpty(dataList);
    }

    /**
     * 封装缓存value
     *
     * @param customerValue 自定义传入的value
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return String
     **/
    @Override
    protected List<String> packageCacheValue(List<T> customerValue) {
        return customerValue.stream().map(this::objectToString).collect(Collectors.toList());
    }

    /**
     * 解析缓存数据
     *
     * @param cacheValue 缓存数据
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return T
     **/
    @Override
    protected List<T> parseCacheValue(List<String> cacheValue) {
        return cacheValue.stream().map(this::stringToObject).collect(Collectors.toList());
    }

    /**
     * 设置单个缓存数据
     * @param customerKey  自定义key
     * @param cacheValueItem  缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public final void setCacheItem(String customerKey, T cacheValueItem) {
        getCache().lSetItem(getKey(customerKey), objectToString(cacheValueItem), getInsertNode());
    }
}
