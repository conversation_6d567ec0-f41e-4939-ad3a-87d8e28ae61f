package com.ce.scrm.center.cache.access;

import com.ce.scrm.center.cache.lock.Lock;
import com.ce.scrm.center.cache.store.Cache;
import com.ce.scrm.center.cache.store.CacheTypeEnum;
import com.ce.scrm.center.cache.lock.LockManager;
import com.ce.scrm.center.cache.lock.LockTypeEnum;
import com.ce.scrm.center.cache.store.CacheManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 缓存基类
 * <AUTHOR>
 * @date 2021/6/19 下午11:49
 * @version 1.0.0
 */
@Component
public class BaseManager {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(BaseManager.class);

    /** 缓存管理 */
    @Resource
    private CacheManager cacheManager;

    /** 分布式锁管理 */
    @Resource
    private LockManager lockManager;

    /**
     * 获取缓存
     * @param cacheTypeEnum 缓存实现类型
     * <AUTHOR>
     * @date 2023/4/6 17:16
     * @return com.ce.scrm.center.cache.store.Cache
     **/
    public Cache getCache(CacheTypeEnum cacheTypeEnum) {
        if (this.cacheManager == null) {
            LOGGER.error("缓存管理注入为空");
            throw new RuntimeException("cacheManager autowired fail...");
        }
        return cacheManager.getCache(cacheTypeEnum.getName());
    }

    /**
     * 获取缓存
     * <AUTHOR>
     * @date 2023/4/6 17:16
     * @return com.ce.scrm.center.cache.store.Cache
     **/
    public Cache getCache() {
        return getCache(CacheTypeEnum.REDIS);
    }

    /**
     * 获取分布式锁
     * @param lockTypeEnum  锁实现类型
     * <AUTHOR>
     * @date 2023/4/6 17:16
     * @return com.ce.scrm.center.cache.lock.Lock
     **/
    public Lock getLock(LockTypeEnum lockTypeEnum) {
        if (this.lockManager == null) {
            LOGGER.error("分布式锁注入为空");
            throw new RuntimeException("lockManager autowired fail...");
        }
        return lockManager.getLock(lockTypeEnum.getName());
    }

    /**
     * 获取分布式锁
     * <AUTHOR>
     * @date 2023/4/6 17:16
     * @return com.ce.scrm.center.cache.lock.Lock
     **/
    public Lock getLock() {
        return getLock(LockTypeEnum.REDIS);
    }
}
