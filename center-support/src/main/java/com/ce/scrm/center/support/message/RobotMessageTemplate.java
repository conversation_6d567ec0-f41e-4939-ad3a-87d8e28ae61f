package com.ce.scrm.center.support.message;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 机器人消息操作模板
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
@Component
public class RobotMessageTemplate {
    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(RobotMessageTemplate.class);
    /**
     * 消息最大长度
     */
    private final static int MSG_MAX_LENGTH = 4096;

    /**
     * 企业微信消息返回code
     */
    private final static String RETURN_CODE = "errcode";

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RobotMessageTemplate robotMessageTemplate;

    @Resource
    private RobotConfig robotConfig;

    private String webHookAddress;

    private String bigBlackCatAddress;

    @Value("${spring.application.name}")
    private String projectName;

    @PostConstruct
    public void init() {
        SendErrorMsgAppender.ROBOT_MESSAGE_TEMPLATE = robotMessageTemplate;
        if (robotConfig == null || StrUtil.isBlank(webHookAddress = robotConfig.getForceAlarmAddress())) {
            LOGGER.error("强报警配置信息不能为空");
            throw new RuntimeException("强报警配置信息不能为空");
        }
        bigBlackCatAddress = robotConfig.getWeakAlarmAddress();
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("报警配置信息获取成功，信息为:{}", JSON.toJSONString(robotConfig));
        }
    }

    /**
     * 发送机器人消息
     * @param content   发送的消息
     * @param alarmEnum    报警枚举
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public void sendText(String content, AlarmEnum alarmEnum) {
        //企业微信 4096(5120) 长度限制
        if (content.length() > MSG_MAX_LENGTH) {
            content = content.substring(0, MSG_MAX_LENGTH);
        }
        //构建文本消息
        MessageBuilder.TextMessage textMessage = new MessageBuilder.TextMessage();
        textMessage.setContent(projectName + "\n" + content);
//        textMessage.setMentioned_mobile_list(Collections.singletonList("@all"));
        //构建消息
        MessageBuilder.Message message = new MessageBuilder.Message();
        message.setMsgtype(MessageBuilder.MessageTypeEnum.TEXT.getParam());
        JSONObject text = JSONObject.parseObject(JSONObject.toJSONString(textMessage));
        message.setText(text);
        if (AlarmEnum.WEAK_ALARM == alarmEnum && StrUtil.isNotBlank(bigBlackCatAddress)) {
            send(message, bigBlackCatAddress);
            return;
        }
        send(message, webHookAddress);
    }

    /**
     * 发送企业微信文本消息
     * @param webHookAddress    企业微信机器人地址
     * @param content   文本消息内容
     * <AUTHOR>
     * @date 2023/6/30 09:23
     **/
    private void sendText(String webHookAddress, String content) {
        if (StrUtil.isBlank(webHookAddress) || StrUtil.isBlank(content)) {
            return;
        }
        //企业微信 4096(5120) 长度限制
        if (content.length() > MSG_MAX_LENGTH) {
            content = content.substring(0, MSG_MAX_LENGTH);
        }
        //构建文本消息
        MessageBuilder.TextMessage textMessage = new MessageBuilder.TextMessage();
        textMessage.setContent(projectName + "\n" + content);
        //构建消息
        MessageBuilder.Message message = new MessageBuilder.Message();
        message.setMsgtype(MessageBuilder.MessageTypeEnum.TEXT.getParam());
        JSONObject text = JSONObject.parseObject(JSONObject.toJSONString(textMessage));
        message.setText(text);
        send(message, webHookAddress);
    }

    /**
     * 发送机器人消息
     * @param message   发送的消息
     * @param webHookAddress    机器人发送地址
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    private void send(MessageBuilder.Message message, String webHookAddress) {
        JSONObject jsonObject = restTemplate.postForObject(webHookAddress, message, JSONObject.class);
        if (LOGGER.isDebugEnabled() || jsonObject == null || jsonObject.getInteger(RETURN_CODE) != 0) {
            LOGGER.debug("发送机器人消息响应数据为:{}", jsonObject);
        }
    }
}
