package com.ce.scrm.center.support.init;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 项目启动打印相关信息
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
@Component
@EnableRetry
@EnableScheduling
public class InitConsole implements CommandLineRunner {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(InitConsole.class);

    @Resource
    private Environment environment;

    /**
     * 初始化应用名称
     */
    public static String APPLICATION_NAME;

    @Resource
    private ConfigurableApplicationContext configurableApplicationContext;

    @Override
    public void run(String... args) {
        try {
            String protocol = "http";
            String ssl = "server.ssl.key-store";
            if (environment.getProperty(ssl) != null) {
                protocol = "https";
            }
            //项目访问路径
            String contextPath = environment.getProperty("server.servlet.context-path");
            if (contextPath == null) {
                contextPath = "";
            }
            //项目访问端口
            String port = environment.getProperty("server.port");
            //druid资源路径
            String druidAddress = "/druid/index.html";
            //job任务后台地址
            String jobAddress = environment.getProperty("xxl.job.admin.addresses");

            LOGGER.error("\n----------------------------------------------------------\n\t" +
                            "Application '{}' is running! Access URLs:\n\t" +
                            "Local: \t\t{}://localhost:{}{}\n\t" +
                            "External: \t{}://{}:{}{}\n\t" +
                            "Druid: \t\t{}://{}:{}{}\n\t" +
                            "JobAdmin: \t{}\n----------------------------------------------------------",
                    APPLICATION_NAME = environment.getProperty("spring.application.name"),
                    protocol,
                    port,
                    contextPath,
                    protocol,
                    InetAddress.getLocalHost().getHostAddress(),
                    port,
                    contextPath,
                    protocol,
                    InetAddress.getLocalHost().getHostAddress(),
                    port,
                    druidAddress,
                    jobAddress
            );
        } catch (UnknownHostException e) {
            LOGGER.error("项目启动报错，异常为信息:{}", e.getMessage(), e);
            configurableApplicationContext.close();
        }
    }
}
