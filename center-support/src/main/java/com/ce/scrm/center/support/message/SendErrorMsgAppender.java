package com.ce.scrm.center.support.message;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.IThrowableProxy;
import ch.qos.logback.classic.spi.StackTraceElementProxy;
import ch.qos.logback.classic.spi.ThrowableProxy;
import ch.qos.logback.core.AppenderBase;
import cn.hutool.core.util.StrUtil;
import com.ce.scrm.center.util.constant.UtilConstant;
import org.slf4j.MDC;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 错误日志消息发送
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
public class SendErrorMsgAppender extends AppenderBase<ILoggingEvent> {

    private final static List<String> BIG_BLACK_CAT_CLASS_FILTER = Collections.singletonList("com.alibaba.druid.filter.stat.StatFilter");

    /**
     * 初始化发送企业微信的模板
     */
    public static RobotMessageTemplate ROBOT_MESSAGE_TEMPLATE = null;
    /**
     * 错误栈轨迹的行数
     */
    private final static int ERROR_MSG_LINE_NUM = 80;
    /**
     * 默认traceId
     */
    private final static String DEFAULT_TRACE_ID = "system";

    @Override
    protected void append(ILoggingEvent event) {
        String traceId = MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME);
        if (StrUtil.isBlank(traceId)) {
            MDC.put(UtilConstant.Mdc.REQUEST_ID_NAME, DEFAULT_TRACE_ID);
        }
        if (event.getLevel() == Level.ERROR) {
            IThrowableProxy iThrowableProxy = event.getThrowableProxy();
            StringBuilder sb = new StringBuilder(event.getLoggerName() + "\n");
            Map<String, String> mdcPropertyMap = event.getMDCPropertyMap();
            mdcPropertyMap.forEach((key, value) -> sb.append(key).append(UtilConstant.KEY_VALUE_SEPARATOR).append(value).append("\n"));
            //获取服务器Ip，告知哪台服务器抛异常
            String ip;
            try {
                ip = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                ip = null;
            }
            if (null != ip) {
                sb.append("当前IP为:").append(ip).append("\n");
            }
            if (iThrowableProxy instanceof ThrowableProxy) {
                ThrowableProxy throwableProxy = (ThrowableProxy) iThrowableProxy;
                Throwable throwable = throwableProxy.getThrowable();
                StackTraceElementProxy[] stackTraceElementProxy = iThrowableProxy.getStackTraceElementProxyArray();
                sb.append(event.getMessage()).append("\n");
                sb.append(throwable.toString()).append("\n");
                int i = 0;
                for (StackTraceElementProxy proxy : stackTraceElementProxy) {
                    //只打印40行的堆栈
                    if (i < ERROR_MSG_LINE_NUM) {
                        sb.append(proxy.getSTEAsString()).append("\n");
                    } else {
                        break;
                    }
                    i++;
                }
            } else {
                sb.append(event.getFormattedMessage());
            }
            String msg = sb.toString();
            if (StrUtil.isNotBlank(msg) && ROBOT_MESSAGE_TEMPLATE != null) {
                if (BIG_BLACK_CAT_CLASS_FILTER.contains(event.getLoggerName())) {
                    ROBOT_MESSAGE_TEMPLATE.sendText(msg, AlarmEnum.WEAK_ALARM);
                } else {
                    ROBOT_MESSAGE_TEMPLATE.sendText(msg, AlarmEnum.FORCE_ALARM);
                }
            }
        }
    }
}
