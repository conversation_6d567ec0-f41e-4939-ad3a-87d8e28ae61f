package com.ce.scrm.center.support.message;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * cat相关配置文件
 * <AUTHOR>
 * @date 2021/10/25 下午1:59
 * @version 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "robot")
public class RobotConfig {
    /**
     * 强报警地址
     */
    private String forceAlarmAddress;

    /**
     * 弱报警地址
     */
    private String weakAlarmAddress;
}
