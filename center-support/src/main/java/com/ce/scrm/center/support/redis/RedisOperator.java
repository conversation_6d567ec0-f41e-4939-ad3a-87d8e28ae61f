package com.ce.scrm.center.support.redis;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.geo.*;
import org.springframework.data.redis.connection.*;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Constructor;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * stringRedisTemplate操作redis
 *
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @date 2021/05/20 2:33 下午
 */
@Component
public class RedisOperator {

    /**
     * 集合数据头数据下标
     */
    public final static long COLLECTION_HEAD_INDEX = 0;
    /**
     * 集合数据尾数据下标
     */
    public final static long COLLECTION_TAIL_INDEX = -1;

    /**
     * 升序
     **/
    public final static int ASC = 0;

    /**
     * 降序
     **/
    public final static int DESC = 1;

    /**
     * 头部
     **/
    public final static int HEAD = 0;

    /**
     * 尾部
     **/
    public final static int TAIL = 1;
    /**
     * 任意字符
     */
    public final static String ALL = "*";
    /**
     * scan命令每次扫描的桶数量
     */
    public final static Integer SCAN_FIND_COUNT = 1000;
    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(RedisOperator.class);

    /**
     * redis操作模版
     */
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    /**
     * redis连接
     **/
    private RedisClusterConnection redisConnection;
    /**
     * 字符串处理
     **/
    private ValueOperations<String, String> valueOperations;
    /**
     * hash处理
     **/
    private HashOperations<String, String, String> hashOperations;
    /**
     * set处理
     **/
    private SetOperations<String, String> setOperations;
    /**
     * zSet处理
     **/
    private ZSetOperations<String, String> zSetOperations;
    /**
     * list处理
     **/
    private ListOperations<String, String> listOperations;
    /**
     * geo地理相关处理
     **/
    private GeoOperations<String, String> geoOperations;
    /**
     * hyperLogLog相关处理
     **/
    private HyperLogLogOperations<String, String> hyperLogLogOperations;


    /**
     * redis 锁
     * @param key
     * @param value
     * @param time
     * @param timeUnit
     * @return
     */
    public boolean setIfAbsent(String key, String value, Long time, TimeUnit timeUnit) {
        //true 成功  false 失败已存在
        return stringRedisTemplate.opsForValue().setIfAbsent(key, value, time, timeUnit);
    }

    /**
     * redisField操作子命令
     * <AUTHOR>
     * @version 1.0.0
     * @date 2021/05/23 7:41 下午
     **/
    public final static class RedisFieldOperateCommand {
        /**
         * get子命令
         **/
        public final static int GET = 1;
        /**
         * set子命令
         **/
        public final static int SET = 2;
        /**
         * incrBy子命令
         **/
        public final static int INCR_BY = 3;
        /**
         * overFlow子命令[fail（此类型有溢出不执行）,wrap(环绕，无符号溢出取最大值，有符号最大溢出从最大到最小，反之，从最小到最大)和sat(饱和算法，上溢最大值，下溢最小值)]
         **/
        public final static int OVER_STRING_ERROR_FLOW = 4;
        /**
         * 类型
         **/
        private int type;
        /**
         * 位操作类型
         **/
        private BitFieldSubCommands.BitFieldType bitFieldType;
        /**
         * 偏移量
         **/
        private long offset;
        /**
         * set命令设置的值
         **/
        private long value;
        /**
         * incr命令递增的值，负数为递减
         **/
        private long incrValue;
        /**
         * incr溢出处理
         **/
        private BitFieldSubCommands.BitFieldIncrBy.Overflow overflow;

        /**
         * 拼装bitField的多条子命令
         *
         * @param redisFieldOperateCommandList 拼装对象传入
         * <AUTHOR>
         * @date 2021/05/24 11:17 上午
         * @version 1.0.0
         * @return org.springframework.data.redis.connection.BitFieldSubCommands
         **/
        public static BitFieldSubCommands buildBitFieldSubCommands(List<RedisFieldOperateCommand> redisFieldOperateCommandList) {
            List<BitFieldSubCommands.BitFieldSubCommand> bitFieldSubCommandList = new ArrayList<>();
            BitFieldSubCommands bitFieldSubCommands = BitFieldSubCommands.create();
            AtomicReference<BitFieldSubCommands.BitFieldIncrBy.Overflow> overflowAtomic = new AtomicReference<>(null);
            AtomicBoolean addOverFlow = new AtomicBoolean(false);
            redisFieldOperateCommandList.forEach(redisFieldOperateCommand -> {
                if (redisFieldOperateCommand == null) {
                    return;
                }
                int type = redisFieldOperateCommand.getType();
                BitFieldSubCommands.BitFieldType bitFieldType = redisFieldOperateCommand.getBitFieldType();
                long offset = redisFieldOperateCommand.getOffset();
                long value = redisFieldOperateCommand.getValue();
                long incrValue = redisFieldOperateCommand.getIncrValue();
                BitFieldSubCommands.BitFieldIncrBy.Overflow overflow = redisFieldOperateCommand.getOverflow();
                if (type == GET) {
                    bitFieldSubCommandList.addAll(bitFieldSubCommands.get(bitFieldType).valueAt(offset).getSubCommands());
                }
                if (type == SET) {
                    bitFieldSubCommandList.addAll(bitFieldSubCommands.set(bitFieldType).valueAt(offset).to(value).getSubCommands());
                }
                if (type == INCR_BY) {
                    BitFieldSubCommands.BitFieldIncrByBuilder incr = bitFieldSubCommands.incr(bitFieldType).valueAt(offset);
                    if (addOverFlow.get()) {
                        incr.overflow(overflowAtomic.get());
                    }
                    bitFieldSubCommandList.addAll(incr.by(incrValue).getSubCommands());
                }
                if (type == OVER_STRING_ERROR_FLOW) {
                    addOverFlow.set(true);
                    overflowAtomic.set(overflow);
                }
            });
            BitFieldSubCommands result = null;
            try {
                Class<?> clazz = Class.forName("org.springframework.data.redis.connection.BitFieldSubCommands");
                Constructor<?> constructor = clazz.getDeclaredConstructor(List.class);
                constructor.setAccessible(true);
                result = (BitFieldSubCommands) constructor.newInstance(bitFieldSubCommandList);
            } catch (Exception e) {
                LOGGER.error("inputParams:{} and errorMessage:{}", JSON.toJSONString(redisFieldOperateCommandList), e.getMessage(), e);
            }
            return result;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public BitFieldSubCommands.BitFieldType getBitFieldType() {
            return bitFieldType;
        }

        public void setBitFieldType(BitFieldSubCommands.BitFieldType bitFieldType) {
            this.bitFieldType = bitFieldType;
        }

        public long getOffset() {
            return offset;
        }

        public void setOffset(long offset) {
            this.offset = offset;
        }

        public long getValue() {
            return value;
        }

        public void setValue(long value) {
            this.value = value;
        }

        public long getIncrValue() {
            return incrValue;
        }

        public void setIncrValue(long incrValue) {
            this.incrValue = incrValue;
        }

        public BitFieldSubCommands.BitFieldIncrBy.Overflow getOverflow() {
            return overflow;
        }

        public void setOverflow(BitFieldSubCommands.BitFieldIncrBy.Overflow overflow) {
            this.overflow = overflow;
        }
    }

    @PostConstruct
    private void init() {
        if (stringRedisTemplate == null) {
            LOGGER.error("RedisTemplate 初始化失败");
            throw new RuntimeException("RedisTemplate 初始化失败");
        }
        //redis连接
        RedisConnectionFactory connectionFactory = stringRedisTemplate.getConnectionFactory();
        if (connectionFactory == null) {
            LOGGER.error("RedisConnectionFactory 初始化失败");
            throw new RuntimeException("RedisConnectionFactory 初始化失败");
        }
        redisConnection = connectionFactory.getClusterConnection();
        //字符串操作初始化
        valueOperations = stringRedisTemplate.opsForValue();
        //hash操作初始化
        hashOperations = stringRedisTemplate.opsForHash();
        //set操作初始化
        setOperations = stringRedisTemplate.opsForSet();
        //zSet操作初始化
        zSetOperations = stringRedisTemplate.opsForZSet();
        //list操作初始化
        listOperations = stringRedisTemplate.opsForList();
        //geo地理相关操作初始化
        geoOperations = stringRedisTemplate.opsForGeo();
        //hyperLogLog操作初始化
        hyperLogLogOperations = stringRedisTemplate.opsForHyperLogLog();
        LOGGER.info("RedisOperator 初始化成功");
    }

    /*---------------------------------------------通用-------------------------------------------*/

    /**
     * 指定缓存失效时间
     * @param key      键
     * @param time     时间(秒)
     * @param timeUnit 时间单位
     * <AUTHOR>
     * @date 2021/05/20 4:22 下午
     * @version 1.0.0
     * @return boolean true表示指定成功，false表示指定不成功
     **/
    public boolean expire(String key, long time, TimeUnit timeUnit) {
        if (ObjectUtils.isEmpty(key) || time <= 0) {
            return false;
        }
        Boolean result = stringRedisTemplate.expire(key, time, timeUnit);
        return result == null ? false : result;
    }

    /**
     * 指定缓存失效时间截止到某个时间点
     * @param key  键
     * @param date 具体时间
     * <AUTHOR>
     * @date 2021/05/21 4:12 下午
     * @version 1.0.0
     * @return boolean
     **/
    public boolean expireAtDate(String key, Date date) {
        if (ObjectUtils.isEmpty(key) || date == null) {
            return false;
        }
        Boolean result = stringRedisTemplate.expireAt(key, date);
        return result == null ? false : result;
    }

    /**
     * 移除缓存失效时间
     * @param key 键
     * <AUTHOR>
     * @date 2021/05/21 12:47 下午
     * @version 1.0.0
     * @return boolean  true为设置成功，false为设置失败
     **/
    public boolean removeExpire(String key) {
        if (ObjectUtils.isEmpty(key)) {
            return false;
        }
        Boolean result = stringRedisTemplate.persist(key);
        return result == null ? false : result;
    }

    /**
     * 根据key获取过期时间
     * @param key      键
     * @param timeUnit 时间单位
     * <AUTHOR>
     * @date 2021/05/20 4:36 下午
     * @version 1.0.0
     * @return long 返回0代表永久有效,-1代表不存在这个key
     **/
    public long getExpire(String key, TimeUnit timeUnit) {
        if (ObjectUtils.isEmpty(key)) {
            return -1;
        }
        Long result = stringRedisTemplate.getExpire(key, timeUnit);
        return result == null ? -1 : result;
    }

    /**
     * 判断key是否存在
     * @param key 键
     * <AUTHOR>
     * @date 2021/05/20 4:43 下午
     * @version 1.0.0
     * @return boolean true表示存在，false表示不存在
     **/
    public boolean hasKey(String key) {
        if (ObjectUtils.isEmpty(key)) {
            return false;
        }
        Boolean result = stringRedisTemplate.hasKey(key);
        return result == null ? false : result;
    }

    /**
     * 删除缓存
     * @param keys 键（可以传多个）
     * <AUTHOR>
     * @date 2021/05/20 4:44 下午
     * @version 1.0.0
     * @return long 返回0代表没有删除，返回的数量表示删除的个数
     **/
    public final long del(String... keys) {
        if (keys == null || keys.length == 0) {
            return 0;
        }
        Long result = stringRedisTemplate.delete(Arrays.asList(keys));
        return result == null ? 0 : result;
    }

    /**
     * 仅当newKey不存在时，将oldKey改名为newKey
     * @param oldKey 原key
     * @param newKey 新key
     * <AUTHOR>
     * @date 2021/05/21 3:51 下午
     * @version 1.0.0
     * @return boolean true表示修改key名称成功，false表示失败
     **/
    public boolean renameKey(String oldKey, String newKey) {
        if (ObjectUtils.isEmpty(oldKey) || ObjectUtils.isEmpty(newKey)) {
            return false;
        }
        Boolean result = stringRedisTemplate.renameIfAbsent(oldKey, newKey);
        return result == null ? false : result;
    }

    /**
     * 返回key所储存的值的类型
     * @param key 键
     * <AUTHOR>
     * @date 2021/05/21 4:21 下午
     * @version 1.0.0
     * @return org.springframework.data.redis.connection.DataType   redis数据类型
     **/
    public DataType type(String key) {
        if (ObjectUtils.isEmpty(key)) {
            return DataType.NONE;
        }
        return stringRedisTemplate.type(key);
    }

    /**
     * 根据key列表删除key
     * @param keys  key列表
     * <AUTHOR>
     * @date 2021/7/19 下午8:31
     * @version 1.0.0
     * @return boolean
     **/
    public boolean deleteByKeys(Set<String> keys) {
        return stringRedisTemplate.delete(keys) != null;
    }

    /**
     * 模糊获取key列表
     * @param pattern   匹配key
     * <AUTHOR>
     * @date 2021/9/3 上午11:42
     * @version 1.0.0
     * @return java.util.Set<java.lang.String>
     **/
    public Set<String> scan(String pattern) {
        Set<String> keys = new LinkedHashSet<>();
        for (RedisClusterNode redisClusterNode : redisConnection.clusterGetNodes()) {
            if (redisClusterNode.isSlave()) {
                continue;
            }
            Cursor<byte[]> cursor = redisConnection.scan(redisClusterNode, ScanOptions.scanOptions().count(SCAN_FIND_COUNT).match(RedisOperator.ALL + pattern + RedisOperator.ALL).build());
            while (cursor.hasNext()) {
                keys.add(new String(cursor.next()));
            }
        }
        return keys;
    }

    /*---------------------------------------------操作String-------------------------------------------*/

    /**
     * 字符串缓存获取
     * @param key 键
     * <AUTHOR>
     * @date 2021/05/20 4:55 下午
     * @version 1.0.0
     * @return T
     **/
    public String get(String key) {
        if (ObjectUtils.isEmpty(key)) {
            return null;
        }
        return valueOperations.get(key);
    }

    /**
     * 字符串缓存获取
     * @param key 键
     * <AUTHOR>
     * @date 2021/05/20 4:55 下午
     * @version 1.0.0
     * @return T
     **/
    public List<String> mget(List<String> keyList) {
        if (CollectionUtil.isEmpty(keyList)) {
            return null;
        }
        return valueOperations.multiGet(keyList);
    }

    /**
     * 字符串缓存放入
     * @param key   键
     * @param value 值
     * <AUTHOR>
     * @date 2021/05/20 4:56 下午
     * @version 1.0.0
     * @return boolean true为添加缓存成功，false为失败
     **/
    public boolean set(String key, String value) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(value)) {
            return false;
        }
        valueOperations.set(key, value);
        return true;
    }

    /**
     * 字符串缓存放入并设置时间
     * @param key      键
     * @param value    值
     * @param time     时间(秒)
     * @param timeUnit 时间单位
     * <AUTHOR>
     * @date 2021/05/20 4:58 下午
     * @version 1.0.0
     * @return boolean true为添加缓存成功，false为失败
     **/
    public boolean set(String key, String value, long time, TimeUnit timeUnit) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(value) || time < 0) {
            return false;
        }
        if (time > 0) {
            valueOperations.set(key, value, time, timeUnit);
        } else {
            set(key, value);
        }
        return true;
    }

    /**
     * 递增
     * @param key   键
     * @param delta 递增因子
     * <AUTHOR>
     * @date 2021/05/20 5:07 下午
     * @version 1.0.0
     * @return long 返回当前的递增值，如果为空，则代表key不存在
     **/
    public Long incr(String key, long delta) {
        if (ObjectUtils.isEmpty(key)) {
            return null;
        }
        return valueOperations.increment(key, delta);
    }

    /*---------------------------------------------操作hash-------------------------------------------*/

    /**
     * 根据键和哈希对象的键进行值查询
     * @param key     键
     * @param hashKey 哈希对象的key
     * <AUTHOR>
     * @date 2021/05/20 5:11 下午
     * @version 1.0.0
     * @return long 返回当前的递增值，如果为空，则代表key不存在
     **/
    public Object hGet(String key, Object hashKey) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(hashKey)) {
            return null;
        }
        return hashOperations.get(key, hashKey);
    }

    /**
     * 获取hash类型Key对应的所有键值
     * @param key 键
     * <AUTHOR>
     * @date 2021/05/20 5:22 下午
     * @version 1.0.0
     * @return java.util.Map<K, String> 返回hash对象
     **/
    public Map<String, String> hGetAll(String key) {
        if (ObjectUtils.isEmpty(key)) {
            return null;
        }
        return hashOperations.entries(key);
    }

    /**
     * 添加哈希类型的数据
     * @param key 键
     * @param map 哈希值
     * <AUTHOR>
     * @date 2021/05/20 5:29 下午
     * @version 1.0.0
     * @return boolean  true为添加值成功，false为添加值失败
     **/
    public boolean hSet(String key, Map<String, String> map) {
        if (ObjectUtils.isEmpty(key) || MapUtil.isEmpty(map)) {
            return false;
        }
        hashOperations.putAll(key, map);
        return true;
    }

    /**
     * 添加哈希类型的数据并设置时间
     * @param key      键
     * @param map      哈希值
     * @param time     有效期
     * @param timeUnit 时间单位
     * <AUTHOR>
     * @date 2021/05/21 10:43 上午
     * @version 1.0.0
     * @return boolean  true为添加值成功，false为添加值失败
     **/
    public boolean hSet(String key, Map<String, String> map, long time, TimeUnit timeUnit) {
        if (time < 0) {
            return false;
        }
        return hSet(key, map) && expire(key, time, timeUnit);
    }

    /**
     * 向一张hash表中放入数据, 如果不存在将创建
     * @param key   键
     * @param item  哈希key
     * @param value 哈希值
     * <AUTHOR>
     * @date 2021/05/21 10:47 上午
     * @version 1.0.0
     * @return boolean  true为添加值成功，false为添加值失败
     **/
    public boolean hSet(String key, String item, String value) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(item) || ObjectUtils.isEmpty(value)) {
            return false;
        }
        hashOperations.put(key, item, value);
        return true;
    }

    /**
     * 向一张hash表中放入数据, 如果不存在将创建并设置时间
     * @param key      键
     * @param item     哈希key
     * @param value    哈希值
     * @param time     有效期
     * @param timeUnit 时间单位
     * <AUTHOR>
     * @date 2021/05/21 10:49 上午
     * @version 1.0.0
     * @return boolean  true为添加值成功，false为添加值失败
     **/
    public boolean hSet(String key, String item, String value, long time, TimeUnit timeUnit) {
        if (time < 0) {
            return false;
        }
        return hSet(key, item, value) && expire(key, time, timeUnit);
    }

    /**
     * 根据key和hashKey删除hash表数据
     * @param key   键
     * @param items 哈希key数组
     * <AUTHOR>
     * @date 2021/05/21 10:50 上午
     * @version 1.0.0
     * @return boolean  true为删除值成功，false为删除值失败
     **/
    public boolean hDel(String key, Object... items) {
        if (ObjectUtils.isEmpty(key) || ArrayUtils.isEmpty(items)) {
            return false;
        }
        hashOperations.delete(key, items);
        return true;
    }

    /**
     * 判断hash表中是否有该项的值
     * @param key  键
     * @param item 哈希key
     * <AUTHOR>
     * @date 2021/05/21 10:53 上午
     * @version 1.0.0
     * @return boolean  true为存在，false为不存在
     **/
    public boolean hHasKey(String key, String item) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(item)) {
            return false;
        }
        return hashOperations.hasKey(key, item);
    }

    /**
     * hash递增 如果不存在,就会创建一个
     * @param key  键
     * @param item 哈希key
     * @param by   递增因子
     * <AUTHOR>
     * @date 2021/05/21 10:55 上午
     * @version 1.0.0
     * @return double 返回当前值
     **/
    public Double hIncr(String key, String item, double by) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(item)) {
            return null;
        }
        return hashOperations.increment(key, item, by);
    }

    /*---------------------------------------------操作set-------------------------------------------*/

    /**
     * 根据key获取Set中的所有值
     * @param key 键
     * <AUTHOR>
     * @date 2021/05/21 11:10 上午
     * @version 1.0.0
     * @return java.util.Set<String> 返回列表
     **/
    public Set<String> sGet(String key) {
        if (ObjectUtils.isEmpty(key)) {
            return null;
        }
        return setOperations.members(key);
    }

    /**
     * 查询set中是否存在某个值
     * @param key   键
     * @param value 值
     * <AUTHOR>
     * @date 2021/05/21 11:11 上午
     * @version 1.0.0
     * @return boolean  true为存在，false为不存在
     **/
    public boolean sHasKey(String key, String value) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(value)) {
            return false;
        }
        Boolean result = setOperations.isMember(key, value);
        return result == null ? false : result;
    }

    /**
     * 将数据放入set缓存
     * @param key    键
     * @param values 值数组
     * <AUTHOR>
     * @date 2021/05/21 11:27 上午
     * @version 1.0.0
     * @return long 添加成功的数量
     **/
    public final long sSet(String key, String... values) {
        if (ObjectUtils.isEmpty(key) || ArrayUtils.isEmpty(values)) {
            return 0;
        }
        Long result = setOperations.add(key, values);
        return result == null ? 0 : result;
    }

    /**
     * 将数据放入set缓存并设置有效期
     * @param key      键
     * @param time     时间(秒)
     * @param timeUnit 时间单位
     * @param values   值数组
     * <AUTHOR>
     * @date 2021/05/21 11:29 上午
     * @version 1.0.0
     * @return boolean
     **/
    public final boolean sSetAndTime(String key, long time, TimeUnit timeUnit, String... values) {
        long result = sSet(key, values);
        return result > 0 && expire(key, time, timeUnit);
    }

    /**
     * 获取set缓存的长度
     * @param key 键
     * <AUTHOR>
     * @date 2021/05/21 11:33 上午
     * @version 1.0.0
     * @return long 返回缓存的长度
     **/
    public long sGetSetSize(String key) {
        if (ObjectUtils.isEmpty(key)) {
            return 0;
        }
        Long result = setOperations.size(key);
        return result == null ? 0 : result;
    }

    /**
     * 批量删除set列表的数据
     * @param key    键
     * @param values 值数组
     * <AUTHOR>
     * @date 2021/05/21 11:34 上午
     * @version 1.0.0
     * @return long 删除成功的数量
     **/
    public long setRemove(String key, Object... values) {
        if (ObjectUtils.isEmpty(key) || ArrayUtils.isEmpty(values)) {
            return 0;
        }
        Long result = setOperations.remove(key, values);
        return result == null ? 0 : result;
    }

    /*---------------------------------------------操作zSet-------------------------------------------*/

    /**
     * 添加单条数据到zSet
     * @param key   键
     * @param value 值
     * @param score 分数
     * <AUTHOR>
     * @date 2021/05/21 2:25 下午
     * @version 1.0.0
     * @return boolean  true为添加成功，false为添加失败
     **/
    public boolean zSet(String key, String value, double score) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(value)) {
            return false;
        }
        Boolean result = zSetOperations.add(key, value, score);
        return result == null ? false : result;
    }

    /**
     * 添加多条数据到zSet
     * @param key    键
     * @param values 值列表，需要拼装成DefaultTypedTuple对象，包含分数和值
     * <AUTHOR>
     * @date 2021/05/21 2:57 下午
     * @version 1.0.0
     * @return long 返回添加成功的条数
     **/
    public final long zSet(String key, DefaultTypedTuple<String>... values) {
        if (ObjectUtils.isEmpty(key) || ArrayUtils.isEmpty(values)) {
            return 0;
        }
        Long result = zSetOperations.add(key, new HashSet<>(Arrays.asList(values)));
        return result == null ? 0 : result;
    }

    /**
     * 添加多条数据到zSet
     * @param key    键
     * @param values 值列表，需要拼装成DefaultTypedTuple对象，包含分数和值
     * <AUTHOR>
     * @date 2021/05/21 2:57 下午
     * @version 1.0.0
     * @return boolean
     **/
    @SafeVarargs
    public final boolean zSet(String key, long expire, TimeUnit timeUnit, DefaultTypedTuple<String>... values) {
        return zSet(key, values) > 0 && expire(key, expire, timeUnit);
    }

    /**
     * 根据key和value删除数据，支持多条
     * @param key    键
     * @param values 值列表
     * <AUTHOR>
     * @date 2021/05/21 3:10 下午
     * @version 1.0.0
     * @return long 返回删除成功的条数
     **/
    public final long zDel(String key, Object... values) {
        if (ObjectUtils.isEmpty(key) || ArrayUtils.isEmpty(values)) {
            return 0;
        }
        Long result = zSetOperations.remove(key, values);
        return result == null ? 0 : result;
    }

    /**
     * 为指定元素加分
     * @param key   键
     * @param value 值
     * @param score 分数
     * <AUTHOR>
     * @date 2021/05/21 3:17 下午
     * @version 1.0.0
     * @return double 返回当前元素的分数
     **/
    public double zAddScore(String key, String value, double score) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(value)) {
            return 0;
        }
        Double result = zSetOperations.incrementScore(key, value, score);
        return result == null ? 0 : result;
    }

    /**
     * 获取元素的排名
     * @param key     键
     * @param value   值
     * @param orderBy 排序规则【0表示升序，1表示降序】
     * <AUTHOR>
     * @date 2021/05/21 3:29 下午
     * @version 1.0.0
     * @return long 返回元素在zSet中的排名
     **/
    public long zGetSort(String key, String value, int orderBy) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(value)) {
            return 0;
        }
        Long result = null;
        if (orderBy == ASC) {
            result = zSetOperations.rank(key, value);
        }
        if (orderBy == DESC) {
            result = zSetOperations.reverseRank(key, value);
        }
        return result == null ? 0 : result;
    }

    /**
     * 根据索引番位获取集合的元素
     * @param key   键
     * @param start 开始索引值
     * @param end   结束索引值
     * <AUTHOR>
     * @date 2021/05/21 4:26 下午
     * @version 1.0.0
     * @return java.util.List<String> 返回列表的数据
     **/
    public List<String> zGetSetByIndex(String key, long start, long end, int orderBy) {
        if (ObjectUtils.isEmpty(key)) {
            return null;
        }
        Set<String> set = null;
        if (orderBy == ASC) {
            set = zSetOperations.range(key, start, end);
        }
        if (orderBy == DESC) {
            set = zSetOperations.reverseRange(key, start, end);
        }
        if (CollectionUtil.isEmpty(set)) {
            return null;
        }
        return new ArrayList<>(set);
    }

    /**
     * 根据索引番位获取集合的元素
     * @param key   键
     * @param start 开始索引值
     * @param end   结束索引值
     * <AUTHOR>
     * @date 2021/05/21 4:26 下午
     * @version 1.0.0
     * @return java.util.List<String> 返回列表的数据
     **/
    public List<ZSetOperations.TypedTuple<String>> zGetSetWithScoreByIndex(String key, long start, long end, int orderBy) {
        if (ObjectUtils.isEmpty(key)) {
            return null;
        }
        Set<ZSetOperations.TypedTuple<String>> set = null;
        if (orderBy == ASC) {
            set = zSetOperations.rangeWithScores(key, start, end);
        }
        if (orderBy == DESC) {
            set = zSetOperations.reverseRangeWithScores(key, start, end);
        }
        if (CollectionUtil.isEmpty(set)) {
            return null;
        }
        return new ArrayList<>(set);
    }

    /**
     * 统计分数区间内元素的数量
     * @param key   集合key
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * <AUTHOR>
     * @date 2021/9/2 下午7:47
     * @version 1.0.0
     * @return int
     **/
    public long zGetNumByScore(String key, double minScore, double maxScore) {
        Long count = zSetOperations.count(key, minScore, maxScore);
        return count == null || count < 1 ? 0 : count;
    }

    /**
     * 根据分数获取元素
     * @param key   缓存key
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * <AUTHOR>
     * @date 2021/10/20 上午10:19
     * @version 1.0.0
     * @return java.lang.String
     **/
    public Set<String> zGetByScore(String key, double minScore, double maxScore) {
        return zSetOperations.rangeByScore(key, minScore, maxScore);
    }

    /**
     * 删除分数区间内的元素
     * @param key   集合key
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * <AUTHOR>
     * @date 2021/9/4 下午12:33
     * @version 1.0.0
     * @return boolean
     **/
    public boolean zRemRangeByScore(String key, double minScore, double maxScore) {
        Long count = zSetOperations.removeRangeByScore(key, minScore, maxScore);
        return count != null && count >= 1;
    }

    /**
     * 获取有序集合的数量
     * @param key   集合key
     * <AUTHOR>
     * @date 2021/9/4 下午12:33
     * @version 1.0.0
     * @return long
     **/
    public long zSize(String key) {
        Long count = zSetOperations.size(key);
        return count == null ? 0 : count;
    }

    /*---------------------------------------------操作list-------------------------------------------*/

    /**
     * 获取list类型的数据
     * @param key   键
     * @param start 开始索引
     * @param end   结束索引
     * <AUTHOR>
     * @date 2021/05/21 11:38 上午
     * @version 1.0.0
     * @return java.util.List<String> 返回列表的数据
     **/
    public List<String> lGet(String key, long start, long end) {
        if (ObjectUtils.isEmpty(key)) {
            return null;
        }
        return listOperations.range(key, start, end);
    }

    /**
     * 获取list缓存的长度
     * @param key 键
     * <AUTHOR>
     * @date 2021/05/21 11:42 上午
     * @version 1.0.0
     * @return long 返回list的长度
     **/
    public long lGetListSize(String key) {
        if (ObjectUtils.isEmpty(key)) {
            return 0;
        }
        Long result = listOperations.size(key);
        return result == null ? 0 : result;
    }

    /**
     * 通过索引获取list中的值
     * @param key   键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * <AUTHOR>
     * @date 2021/05/21 11:43 上午
     * @version 1.0.0
     * @return String   元素的值
     **/
    public String lGetIndex(String key, long index) {
        if (ObjectUtils.isEmpty(key)) {
            return null;
        }
        return listOperations.index(key, index);
    }

    /**
     * 将数据存入list列表
     * @param key   键
     * @param value 值
     * <AUTHOR>
     * @date 2021/05/21 11:45 上午
     * @version 1.0.0
     * @return boolean  true表示添加数据成功，false表示添加数据失败
     **/
    public boolean lSet(String key, String value, int insertNode) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(value)) {
            return false;
        }
        boolean result = false;
        if (HEAD == insertNode) {
            listOperations.leftPush(key, value);
            result = true;
        }
        if (TAIL == insertNode) {
            listOperations.rightPush(key, value);
            result = true;
        }
        return result;
    }

    /**
     * 将数据存入list列表并设置有效期
     * @param key      键
     * @param value    值
     * @param time     有效期
     * @param timeUnit 时间单位
     * <AUTHOR>
     * @date 2021/05/21 12:27 下午
     * @version 1.0.0
     * @return boolean  true表示添加数据成功，false表示添加数据失败
     **/
    public boolean lSet(String key, String value, long time, TimeUnit timeUnit) {
        return lSet(key, value, TAIL) && expire(key, time, timeUnit);
    }

    /**
     * 将list数据放入缓存
     * @param key   键
     * @param value 值列表
     * <AUTHOR>
     * @date 2021/05/21 12:30 下午
     * @version 1.0.0
     * @return boolean  true表示添加数据成功，false表示添加数据失败
     **/
    public boolean lSet(String key, List<String> value) {
        if (ObjectUtils.isEmpty(key) || CollectionUtil.isEmpty(value)) {
            return false;
        }
        listOperations.rightPushAll(key, value);
        return true;
    }

    /**
     * 将list数据放入缓存并设置有效期
     * @param key      键
     * @param value    值列表
     * @param time     有效期
     * @param timeUnit 时间单位
     * <AUTHOR>
     * @date 2021/05/21 12:33 下午
     * @version 1.0.0
     * @return boolean  true表示添加数据成功，false表示添加数据失败
     **/
    public boolean lSet(String key, List<String> value, long time, TimeUnit timeUnit) {
        return lSet(key, value) && expire(key, time, timeUnit);
    }

    /**
     * 根据索引修改list中的某条数据
     * @param key   键
     * @param index 索引值 0和正数代表从头部开始，负数代表从尾部开始
     * @param value 值列表
     * <AUTHOR>
     * @date 2021/05/21 12:34 下午
     * @version 1.0.0
     * @return boolean  true表示修改数据成功，false表示修改数据失败
     **/
    public boolean lUpdateByIndex(String key, long index, String value) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(value)) {
            return false;
        }
        listOperations.set(key, index, value);
        return true;
    }

    /**
     * 移除列表中与参数value相等的元素
     * @param key   键
     * @param count count > 0 : 从表头开始向表尾搜索，移除与 StringALUE 相等的元素，数量为 COUNT 。
     *              count < 0 : 从表尾开始向表头搜索，移除与 StringALUE 相等的元素，数量为 COUNT 的绝对值。
     *              count = 0 : 移除表中所有与 StringALUE 相等的值。
     * @param value 值
     * <AUTHOR>
     * @date 2021/05/21 12:39 下午
     * @version 1.0.0
     * @return long
     **/
    public long lRemove(String key, long count, String value) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(value)) {
            return 0;
        }
        Long result = listOperations.remove(key, count, value);
        return result == null ? 0 : result;
    }

    /*---------------------------------------------操作geoSpatial-------------------------------------------*/

    /**
     * 将指定的地理空间位置（纬度、经度、名称）添加到指定的key中
     * @param key       键
     * @param precision 经度
     * @param dimension 纬度
     * @param name      名称
     * <AUTHOR>
     * @date 2021/05/21 6:54 下午
     * @version 1.0.0
     * @return boolean true添加成功，false添加失败
     **/
    public boolean geoAdd(String key, double precision, double dimension, String name) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(name)) {
            return false;
        }
        Long result = geoOperations.add(key, new Point(precision, dimension), name);
        return result != null && result != 0;
    }

    /**
     * 从key里返回所有给定位置元素的位置（经度和纬度）
     * @param key      redis的key
     * @param nameList 名称的集合
     * <AUTHOR>
     * @date 2021/05/21 6:58 下午
     * @version 1.0.0
     * @return java.util.List<org.springframework.data.geo.Point> 返回指定位置的定位
     **/
    public final List<Point> geoGet(String key, String... nameList) {
        if (ObjectUtils.isEmpty(key) || ArrayUtils.isEmpty(nameList)) {
            return null;
        }
        return geoOperations.position(key, nameList);
    }

    /**
     * 返回两个给定位置之间的距离
     * @param key    redis的key
     * @param from   起点
     * @param to     终点
     * @param metric 距离单位
     * <AUTHOR>
     * @date 2021/05/21 7:20 下午
     * @version 1.0.0
     * @return org.springframework.data.geo.Distance 返回两个位置之间的距离
     **/
    public Distance geoDist(String key, String from, String to, Metric metric) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(from) || ObjectUtils.isEmpty(to)) {
            return null;
        }
        return geoOperations.distance(key, from, to, metric);
    }

    /**
     * 以给定的经纬度为中心， 返回键包含的位置元素当中， 与中心的距离不超过给定最大距离的所有位置元素
     * 并给出所有位置元素与中心的平均距离
     * @param key       redis的key
     * @param precision 经度
     * @param dimension 纬度
     * @param distance  距离
     * @param count     人数
     * @param metric    距离单位
     * <AUTHOR>
     * @date 2021/05/21 7:22 下午
     * @version 1.0.0
     * @return org.springframework.data.geo.GeoResults<org.springframework.data.redis.connection.RedisGeoCommands.GeoLocation < java.lang.String>>
     **/
    public GeoResults<RedisGeoCommands.GeoLocation<String>> geoNearByXy(String key, double precision, double dimension, int distance, int count, Metric metric) {
        if (ObjectUtils.isEmpty(key)) {
            return null;
        }
        Circle circle = new Circle(new Point(precision, dimension), new Distance(distance, metric));
        RedisGeoCommands.GeoRadiusCommandArgs args = RedisGeoCommands.GeoRadiusCommandArgs.newGeoRadiusArgs().includeDistance().includeCoordinates().sortAscending().limit(count);
        return geoOperations.radius(key, circle, args);
    }

    /**
     * 以给定的城市为中心， 返回键包含的位置元素当中，
     * 与中心的距离不超过给定最大距离的所有位置元素，并给出所有位置元素与中心的平均距离
     * @param key      redis的key
     * @param name     名称
     * @param distance 距离
     * @param count    人数
     * @param metric   距离单位
     * <AUTHOR>
     * @date 2021/05/21 7:27 下午
     * @version 1.0.0
     * @return org.springframework.data.geo.GeoResults<org.springframework.data.redis.connection.RedisGeoCommands.GeoLocation < java.lang.String>>
     **/
    public GeoResults<RedisGeoCommands.GeoLocation<String>> geoNearByPlace(String key, String name, int distance, int count, Metric metric) {
        if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(name)) {
            return null;
        }
        Distance distances = new Distance(distance, metric);
        RedisGeoCommands.GeoRadiusCommandArgs args = RedisGeoCommands.GeoRadiusCommandArgs.newGeoRadiusArgs().includeDistance().includeCoordinates().sortAscending().limit(count);
        return geoOperations.radius(key, name, distances, args);
    }

    /**
     * 返回一个或多个位置元素的 GeoHash 表示
     * @param key      redis的key
     * @param nameList 名称的集合
     * <AUTHOR>
     * @date 2021/05/21 7:34 下午
     * @version 1.0.0
     * @return java.util.List<java.lang.String>
     **/
    public final List<String> geoHash(String key, String... nameList) {
        if (ObjectUtils.isEmpty(key) || ArrayUtils.isEmpty(nameList)) {
            return null;
        }
        return geoOperations.hash(key, nameList);
    }

    /*---------------------------------------------操作hyperLogLog-------------------------------------------*/

    /**
     * 添加元素
     * @param key    键
     * @param values 元素名称数组
     * <AUTHOR>
     * @date 2021/05/23 10:55 上午
     * @version 1.0.0
     * @return boolean  true为添加成功，false为添加失败
     **/
    public final boolean hllAdd(String key, String... values) {
        if (ObjectUtils.isEmpty(key) || ArrayUtils.isEmpty(values)) {
            return false;
        }
        return hyperLogLogOperations.add(key, values).equals(1L);
    }

    /**
     * 统计所有key的总数
     * @param keys 键数组
     * <AUTHOR>
     * @date 2021/05/23 11:43 上午
     * @version 1.0.0
     * @return long 返回key对应的数量只和
     **/
    public final long hllCount(String... keys) {
        if (ObjectUtils.isEmpty(keys)) {
            return 0;
        }
        return hyperLogLogOperations.size(keys);
    }

    /**
     * 统计所有key的总数，将其合并为一个hyperLogLog
     * @param keys 键数组
     * <AUTHOR>
     * @date 2021/05/23 11:43 上午
     * @version 1.0.0
     * @return long 返回key对应的数量只和
     **/
    public final long hllUnion(String k, String... keys) {
        if (ObjectUtils.isEmpty(keys)) {
            return 0;
        }
        return hyperLogLogOperations.union(k, keys);
    }

    /*---------------------------------------------操作bitMap-------------------------------------------*/

    /**
     * 添加数据到位图结构指定索引
     * @param key    键
     * @param offset 偏移量
     * @param value  位值
     * <AUTHOR>
     * @date 2021/05/23 1:42 下午
     * @version 1.0.0
     * @return boolean  返回修改之前的值
     **/
    public boolean bitSet(String key, long offset, boolean value) {
        if (ObjectUtils.isEmpty(key)) {
            return false;
        }
        Boolean result = valueOperations.setBit(key, offset, value);
        return result == null ? false : result;
    }

    /**
     * 获取位图指定索引的值
     * @param key    键
     * @param offset 偏移量
     * <AUTHOR>
     * @date 2021/05/23 1:52 下午
     * @version 1.0.0
     * @return boolean  获取具体值
     **/
    public Boolean bitGet(String key, long offset) {
        if (ObjectUtils.isEmpty(key)) {
            return false;
        }
        return valueOperations.getBit(key, offset);
    }
}