package com.ce.scrm.center.support.redis;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * redis配置类
 *
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @date 2021/05/18 3:13 下午
 */
@Configuration
@AutoConfigureAfter(RedisAutoConfiguration.class)
public class RedisConfig {

    /**
     * 自定义配置redisTemplate的连接池
     * @param redisConnectionFactory  连接池信息
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>
     **/
    @Bean
    public StringRedisTemplate redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }
}
