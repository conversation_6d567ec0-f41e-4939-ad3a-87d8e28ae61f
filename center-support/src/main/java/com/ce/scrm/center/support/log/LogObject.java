package com.ce.scrm.center.support.log;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 统一日志对象
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
public class LogObject {

    @JSONField(ordinal = 1)
    private String invokerName;

    @JSONField(ordinal = 2)
    private String invokerIp;

    @JSONField(ordinal = 3)
    private String eventName;

    @JSONField(ordinal = 4)
    private String traceId;

    @JSONField(ordinal = 5)
    private String msg;

    @JSONField(ordinal = 6)
    private long costTime;

    @JSONField(ordinal = 7)
    private Object request;

    @JSONField(ordinal = 8)
    private Object response;

    @JSONField(ordinal = 9)
    private Object loginInfo;
}
