package com.ce.scrm.center.support.redis;

import com.ce.scrm.center.cache.entity.SortCollectionData;
import com.ce.scrm.center.cache.store.Cache;
import com.ce.scrm.center.cache.store.CacheTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * redis缓存处理
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @date 2021/05/11 下午4:08
 */
@Component
public class RedisCache implements Cache {

    private final static Logger LOGGER = LoggerFactory.getLogger(RedisCache.class);

    @Resource
    private RedisOperator redisOperator;

    /**
     * 获取当前操作缓存方式的名称
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.lang.String
     **/
    @Override
    public String getName() {
        return CacheTypeEnum.REDIS.getName();
    }

    /**
     * 模糊获取所有的key
     *
     * @param prefix 查询条件
     * <AUTHOR>
     * @date 2023/6/25 10:32
     * @return java.util.Set<java.lang.String>
     **/
    @Override
    public Set<String> scan(String prefix) {
        return redisOperator.scan(prefix);
    }

    /**
     * 判断缓存数据是否存在
     * @param key   缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    @Override
    public boolean exist(String key) {
        return redisOperator.hasKey(key);
    }

    /**
     * 删除redis缓存
     * @param key 键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void del(String key) {
        redisOperator.del(key);
    }

    /**
     * 批量删除缓存
     * @param cacheKeyList  缓存键列表
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void batchDel(List<String> cacheKeyList) {
        redisOperator.del(cacheKeyList.toArray(new String[0]));
    }

    /**
     * 批量删除缓存
     *
     * @param cacheKeyList 缓存键列表
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public List<String> batchGet(List<String> cacheKeyList) {
        return redisOperator.mget(cacheKeyList);
    }

    /**
     * 设置缓存数据，带有效期
     * @param key    键
     * @param value  值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void set(String key, String value, long expire) {
        if (!redisOperator.set(key, value, expire, TimeUnit.SECONDS)) {
            LOGGER.error("redis设置缓存失败，key:{},value:{},expire:{}", key, value, expire);
        }
    }

    /**
     * 获取redis缓存
     * @param key 键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return V
     **/
    @Override
    public String get(String key) {
        return redisOperator.get(key);
    }

    /**
     * 设置hash数据
     * @param key   缓存键
     * @param hValue  缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void hSet(String key, Map<String, String> hValue, long expire) {
        redisOperator.hSet(key, hValue, expire, TimeUnit.SECONDS);
    }

    /**
     * 设置hash数据
     * @param key   缓存键
     * @param hashKey 缓存hashKey
     * @param hValueItem  单条缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void hSetItem(String key, String hashKey, String hValueItem) {
        redisOperator.hSet(key, hashKey, hValueItem);
    }

    /**
     * 获取hash数据
     * @param key   缓存键
     * @param hashKey 缓存hashKey
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.lang.String
     **/
    @Override
    public String hGetItem(String key, String hashKey) {
        Object value = redisOperator.hGet(key, hashKey);
        return value == null ? null : value.toString();
    }

    /**
     * 获取hash数据
     * @param key   缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.util.Map<java.lang.String, java.lang.String>
     **/
    @Override
    public Map<String, String> hGet(String key) {
        return redisOperator.hGetAll(key);
    }

    /**
     * 设置list数据
     * @param key   缓存键
     * @param lValue  缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void lSet(String key, List<String> lValue, long expire) {
        redisOperator.lSet(key, lValue, expire, TimeUnit.SECONDS);
    }

    /**
     * 给list中添加单条数据
     * @param key   缓存键
     * @param lValueItem  缓存值
     * @param insertNode 插入节点
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void lSetItem(String key, String lValueItem, int insertNode) {
        redisOperator.lSet(key, lValueItem, insertNode);
    }

    /**
     * 获取list数据
     * @param key   缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.util.List<java.lang.String>
     **/
    @Override
    public List<String> lGet(String key) {
        return redisOperator.lGet(key, RedisOperator.COLLECTION_HEAD_INDEX, RedisOperator.COLLECTION_TAIL_INDEX);
    }

    /**
     * 设置set数据
     * @param key   缓存键
     * @param sValue  缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void sSet(String key, Set<String> sValue, long expire) {
        redisOperator.sSetAndTime(key, expire, TimeUnit.SECONDS, sValue.toArray(new String[0]));
    }

    /**
     * 设置set数据
     * @param key   缓存键
     * @param sValue  缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void sSetItem(String key, String sValue) {
        redisOperator.sSet(key, sValue);
    }

    /**
     * 获取set数据
     * @param key   缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.util.Set<java.lang.String>
     **/
    @Override
    public Set<String> sGet(String key) {
        return redisOperator.sGet(key);
    }

    /**
     * 设置有序集合
     * @param key   缓存键
     * @param zValue 元素值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    @SuppressWarnings("unchecked")
    public void zSet(String key, List<SortCollectionData> zValue, long expire) {
        DefaultTypedTuple<String>[] defaultTypedTupleArray = new DefaultTypedTuple[zValue.size()];
        List<DefaultTypedTuple<String>> defaultTypedTupleList = zValue.stream().map(item -> new DefaultTypedTuple<>(item.getValue(), item.getWeight())).collect(Collectors.toList());
        defaultTypedTupleList.toArray(defaultTypedTupleArray);
        redisOperator.zSet(key, expire, TimeUnit.SECONDS, defaultTypedTupleArray);
    }

    /**
     * 设置有序集合
     * @param key   缓存键
     * @param zValueItem 元素值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void zSetItem(String key, SortCollectionData zValueItem) {
        redisOperator.zSet(key, zValueItem.getValue(), zValueItem.getWeight());
    }

    /**
     * 获取有序集合
     * @param key   缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.util.Set<com.ce.scrm.center.cache.entity.SortCollectionData>
     **/
    @Override
    public List<SortCollectionData> zGet(String key, int orderBy) {
        List<ZSetOperations.TypedTuple<String>> typedTuples = redisOperator.zGetSetWithScoreByIndex(key, RedisOperator.COLLECTION_HEAD_INDEX, RedisOperator.COLLECTION_TAIL_INDEX, orderBy);
        return typedTuples.stream().map(typedTuple -> new SortCollectionData(typedTuple.getValue(), typedTuple.getScore())).collect(Collectors.toList());
    }

    /**
     * 获取有序集合
     * @param key   缓存键
     * @param weight 权重
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.util.Set<com.ce.scrm.center.cache.entity.SortCollectionData>
     **/
    @Override
    public Set<SortCollectionData> zGetItem(String key, double weight) {
        return redisOperator.zGetByScore(key, weight, weight).stream().map(SortCollectionData::new).collect(Collectors.toSet());
    }
}
