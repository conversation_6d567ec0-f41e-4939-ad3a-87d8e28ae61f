package com.ce.scrm.center.dubbo.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 流转、流失
 * <AUTHOR>
 * @date 2024/7/24
 */
@Data
public class CirculationLossDubboDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 区域id
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 状态 总监是否已经分配 0：未分配 1：已分配
     */
    private Integer status;
}
