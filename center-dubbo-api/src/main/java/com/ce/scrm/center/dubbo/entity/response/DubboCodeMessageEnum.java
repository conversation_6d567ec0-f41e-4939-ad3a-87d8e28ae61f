package com.ce.scrm.center.dubbo.entity.response;

/**
 * dubbo返回码枚举
 * <AUTHOR>
 * @date 2023/4/6 13:55
 * @version 1.0.0
 **/
public enum DubboCodeMessageEnum {
    /**
     * 成功返回
     */
    REQUEST_SUCCESS("200", "请求成功"),

    /**
     * 服务器内部异常
     */
    SERVER_INTERNAL_EXCEPTION("10001", "网络超时，请稍后重试哦～"),

    /**
     * 参数为空
     */
    PARAM_EMPTY ("10002", "参数为空"),

    /**
     * 无效参数
     */
    INVALID_PARAM ("10003", "无效参数"),

    /**
     * 返回结果为空
     */
    RETURN_NULL("10004", "返回结果为空"),
    ;

    private final String code;
    private final String message;

    DubboCodeMessageEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
