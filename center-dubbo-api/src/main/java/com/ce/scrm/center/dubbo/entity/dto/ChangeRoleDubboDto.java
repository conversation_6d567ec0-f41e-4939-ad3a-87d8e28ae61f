package com.ce.scrm.center.dubbo.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 切换员工角色
 * @Author: lijinpeng
 * @Date: 2024/12/23 14:15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeRoleDubboDto implements Serializable {

    /**
     * 员工id
     */
    private String empId;

    /**
     * 1、区域总监、分公司总监、事业部总监 传管理部id
     * 2、商务经理、小组长  传 商务部（组）id
     * 3、客户代表 null
     */
    private String orgId;

}
