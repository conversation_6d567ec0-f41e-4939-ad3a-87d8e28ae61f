package com.ce.scrm.center.dubbo.api;

import com.ce.scrm.center.dubbo.entity.dto.ChangeRoleDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.EmployeeInfoDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Description: 获取员工数据
 * @Author: lijinpeng
 * @Date: 2024/11/14 16:41
 */
public interface EmployeeInfoDubbo {

    /*
     * @Description 根据员工id获取员工属性 不检查state = 1
     * <AUTHOR>
     * @date 2024/11/14 16:49
     * @param empId
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.dto.EmployeeInfoDubboDto>
     */
    DubboResult<EmployeeInfoDubboDto> getEmployeeInfoByEmpId(String empId);

    /*
     * @Description 切换员工角色 根据empId和orgId
     * <AUTHOR>
     * @date 2024/12/23 14:16
     * @param changeRoleDubboDto
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.Boolean>
     */
    DubboResult<Boolean> changeRoleByEmpIdAndOrgId(ChangeRoleDubboDto changeRoleDubboDto);

    /*
     * @Description 根据员工id集合获取员工集合信息  不检查state=1
     * <AUTHOR>
     * @date 2024/12/3 17:10
     * @param empIdList
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.util.Map<java.lang.String,com.ce.scrm.center.dubbo.entity.dto.EmployeeInfoDubboDto>>
     */
    DubboResult<Map<String, EmployeeInfoDubboDto>> getEmployeeInfoByEmpIdList(List<String> empIdList);

    /*
     * @Description 根据员工id获取员工信息(state=1)
     * <AUTHOR>
     * @date 2024/12/3 17:10
     * @param empId
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.dto.EmployeeInfoDubboDto>
     */
    DubboResult<EmployeeInfoDubboDto> getEmployeeCheckStateByEmpId(String empId);

    /*
     * @Description 企业微信登录 存入redis key-userId value-code
     * <AUTHOR>
     * @date 2025/2/7 11:11
     * @param empId
     * @param code
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.Boolean>
     */
    DubboResult<Boolean> saveCodeOfRedis(String userId,String code);

    /*
     * @Description 企业微信登录 取出redis key-userId value-code
     * <AUTHOR>
     * @date 2025/2/7 13:52
     * @param userId
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.view.EmployeeInfoRedisDubboView>
     */
    DubboResult<String> getCodeOfRedisByUserId(String userId);

}
