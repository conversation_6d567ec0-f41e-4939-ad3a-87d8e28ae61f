package com.ce.scrm.center.dubbo.api;

import com.ce.scrm.center.dubbo.entity.dto.CboCustomerDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.CboCustomerPayDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.CboProtectDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.view.CboContactPersonDubboView;
import com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView;

import java.util.List;

/**
 * 跨境专用
 * <AUTHOR>
 * @description
 * @date 2024/11/14
 */
public interface CboCustomerDubbo {

    /***
     * 保护关系查询
     * @param cboProtectDubboDto
     * <AUTHOR>
     * @date 2024/11/14 17:46
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.response.DubboPageInfo<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>>
    **/
    DubboResult<DubboPageInfo<CboProtectDubboView>> getProtectByCondition(CboProtectDubboDto cboProtectDubboDto);

    /***
     * 联系人查询
     * @param customerId
     * <AUTHOR>
     * @date 2024/11/14 17:47
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.center.dubbo.entity.view.CboContactPersonDubboView>>
    **/
    DubboResult<List<CboContactPersonDubboView>> getContactPersonByCondition(String customerId);

    /***
     * 根据客户ID查询保护关系
     * @param cboProtectDubboDto
     * <AUTHOR>
     * @date 2024/12/9 10:58
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>
    **/
    DubboResult<CboProtectDubboView> getProtectByCustomerId(CboProtectDubboDto cboProtectDubboDto);

    /***
     * 根据客户名称查询保护关系
     * @param cboProtectDubboDto
     * <AUTHOR>
     * @date 2024/12/9 10:58
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>
     **/
    DubboResult<CboProtectDubboView> getProtectByCustomerName(CboProtectDubboDto cboProtectDubboDto);

    /***
     * 查询客户信息
     * @param cboCustomerDubboDto
     * <AUTHOR>
     * @date 2024/12/11 10:17
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>
    **/
    DubboResult<CboProtectDubboView> getCustomerInfoByCondition(CboCustomerDubboDto cboCustomerDubboDto);

    /***
     * 订单支付
     * @param cboCustomerPayDubboDto
     * <AUTHOR>
     * @date 2024/12/17 09:48
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.Boolean>
    **/
    DubboResult<Boolean> orderPay(CboCustomerPayDubboDto cboCustomerPayDubboDto);

}
