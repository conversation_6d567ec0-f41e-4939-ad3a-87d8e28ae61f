package com.ce.scrm.center.dubbo.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户leads
 */
@Data
public class CustomerLeadsDubboView implements Serializable {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 数据来源 0:crm原有渠道  1:营销活动   2:手动录入
     */
    private Integer dataFromSource;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 更多信息
     */
    private String moreInfo;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 省
     */
    private String provinceCode;

    /**
     * 市
     */
    private String cityCode;


    /**
     * 详细地址
     */
    private String address;

    /**
     * 线索类型
     */
    private String clueType;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 活动
     */
    private String activity;

    /**
     * 端口
     */
    private String clientType;

    /**
     * 意向产品
     */
    private String intentionProduct;

    /**
     * 需求备注
     */
    private String demandRemark;

    /**
     * 附件url a,b,c
     */
    private String attachmentUrls;

    /**
     * leads类别
     */
    private String leadsType;

    /**
     * leads code
     */
    private String leadsCode;

    /**
     * leads 来源
     */
    private String leadsSource;

    /**
     * leads url
     */
    private String leadsUrl;

    /**
     * leads来源说明
     */
    private String leadsDesc;

    /**
     * 联系人
     */
    private String linkmanName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 分发时间
     */
    private Date dispatchTime;

    /**
     * 创建者
     */
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 联系人id
     */
    private String contactId;

    /**
     * 性别
     */
    private String gender;

    /**
     * 邮件
     */
    private String email;

    /**
     * 职位
     */
    private String position;

    /**
     * 微信账号
     */
    private String weChat;

    /**
     * 固话
     */
    private String fixedPhone;

    /**
     * 线索单号
     */
    private String clueCode;

    /**
     * 处理人部门id
     */
    private String handleDeptId;

    /**
     * 处理人部门名称
     */
    private String handleDeptName;

    /**
     * 最新处理人id
     */
    private String handlePersonId;

    /**
     * 最新处理人名称
     */
    private String handlePersonName;

    /**
     * 创建人部门id
     */
    private String createDeptId;

    /**
     * 创建人部门名称
     */
    private String createDeptName;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * qq号
     */
    private String qq;

    /**
     * 一级行业code
     */
    private String industryOneCode;

    /**
     * 二级行业code
     */
    private String industryTwoCode;

    /**
     *
     */
    private String mainBusiness;

    /**
     * 备注
     */
    private String remark;

    /**
     * 百度url
     */
    private String baiduUrl;

    /**
     * 线索类型-子类型
     */
    private String baiduSource;

    /**
     *
     */
    private String clueLabel;

    /**
     * 线索来源 400的二级渠道
     */
    private String child400;

    /**
     * 来源url
     */
    private String sourceUrl;

    /**
     * 落地url
     */
    private String landUrl;

    /**
     * 活动特征码
     */
    private String promoteSlug;

    /**
     * 渠道特征码
     */
    private String channelSlug;

    /**
     * 特征码
     */
    private String slug;

    /**
     * 表单类型
     */
    private String formType;

    /**
     * 300咨询信息
     */
    private String others;

    /**
     * 表单提交url
     */
    private String formSubmitUrl;

    /**
     * 二级渠道特征码
     */
    private String channelChildSlug;

    /**
     * 关键词1
     */
    private String keyword1;

    /**
     * 关键词2
     */
    private String keyword2;

    /**
     * 关键词3
     */
    private String utmTerm;

    /**
     * 来源域名
     */
    private String fromPage;

    /**
     * 三方推送的线索id
     */
    private String sourceDataId;
}