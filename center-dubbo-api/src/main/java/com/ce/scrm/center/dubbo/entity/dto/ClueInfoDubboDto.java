package com.ce.scrm.center.dubbo.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ClueInfoDubboDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 潜客规则配置表对应的leadsCode。可以不传，但是分配前一定要有
	 */
	private String leadsCode;

	/**
	 * 客户id
	 */
	private String customerId;

	/*ID*/
	private String id;
	/*线索CODE*/
	private String clueCode;
	/*来源*/
	private String source;
	/*创建人ID*/
	private String createrId;
	/*创建人名称*/
	private String createrName;
	/*处理人部门ID*/
	private String handleDeptId;
	/*处理人名称*/
	private String handleDeptName;
	/*创建时间*/
	private Date createTime;
	/*处理人ID*/
	private String handlePersonId;
	/*处理人名称*/
	private String handlePersonName;
	/*创建人部门ID*/
	private String createDeptId;
	/*创建人部门名称*/
	private String createDeptName;
	/*处理时间*/
	private Date handleTime;

	private String trafficKey;
	/*客户名称*/
	private String custName;
	/*联系人名称*/
	private String linkmanName;
	/*姓别*/
	private String sex;
	/*职位*/
	private String position;
	/*手机*/
	private String mobile;
	/*座机*/
	private String telephone;
	/*邮箱*/
	private String mail;
	/*省-CODE*/
	private String province;
	/*省-名称*/
	private String provinceLabel;
	/*市-CODE*/
	private String city;
	/*市-名称*/
	private String cityLabel;
	/*区-CODE*/
	private String district;
	/*区-名称*/
	private String districtLabel;
	/*详细地址*/
	private String address;
	/*QQ*/
	private String qq;
	/*微信*/
	private String weChat;
	/*一级行业*/
	private String industryOneCode;
	/*二级行业*/
	private String industryTwoCode;
	/*主营业务*/
	private String mainBusiness;
	/*客户需求*/
	private String custRequirement;
	/*备注*/
	private String remark;
	/*线索状态*/
	private String status;
	/*一级处理结果*/
	private String handleResult;
	/*二级处理结果*/
	private String handleResultChild;
	/**/
	private String baiduUrl;

	private String baiduSource;
	/*线索标签*/
	private String clueLabel;
	/*重复标记-手机*/
	private String mobileFlag;
	/*重复标记-电话*/
	private String telephoneFlag;
	/*重复标记-邮箱*/
	private String mailFlag;
	/*重复标记-客户名称*/
	private String custNameFlag;

	// 线索来源 400的二级渠道
	private String child400;
	/*来源页*/
	private String sourceUrl;
	/*落地页*/
	private String landUrl;
	/*活动*/
	private String promoteSlug;
	/*渠道*/
	private String channelSlug;
	/*二级渠道*/
	private String channelChildSlug;
	/*活动码*/
	private String slug;
	/*提交表单类型*/
	private String formType;

	//表单来源页
	private String formSubmitUrl;

	// 业务类型
	private String serviceSource;
	// 业务单据ID
	private String serviceBillId;
	// 业务单据CODE
	private String serviceBillCode;
	// BI 需要增加 log id
	private String biLogId;
	/*其他*/
	private String others;
	//端
	private String vdevice;
	//附件ID
	private String fileId;
	// 是否已补全信息 YES:是  NO否
	private String isComplete;
	// 线索归属，隔离标记
	private String solationFlag;
	// 客户ID
	private String custId;
	// 会员Code
	private String memberCode;
	//推荐人会员code
	private String recoMemCode;
	//商务id
	private String salerId;
	/**
	 * 分级标签
	 * ABCDE
	 */
	private String intentTag;
	/**
	 * 关键词1
	 */
	private String keyword1;
	/**
	 * 关键词2
	 */
	private String keyword2;
	/**
	 * 关键词3
	 */
	private String utmTerm;
	/**
	 * 来源域名
	 */
	private String fromPage;

	/**
	 * 三方推送的线索ID
	 * 53
	 */
	private String sourceDataId;

	/**
	 * 整体预算
	 */
	private BigDecimal budget;

}