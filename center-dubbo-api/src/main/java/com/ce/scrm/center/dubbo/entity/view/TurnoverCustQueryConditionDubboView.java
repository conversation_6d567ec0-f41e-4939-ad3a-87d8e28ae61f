package com.ce.scrm.center.dubbo.entity.view;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 成交客户查询结果
 * @Author: lijinpeng
 * @Date: 2024/12/30 11:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TurnoverCustQueryConditionDubboView implements Serializable {

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 事业部id
     */
    private String buId;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 区域id
     */
    private String areaId;

}
