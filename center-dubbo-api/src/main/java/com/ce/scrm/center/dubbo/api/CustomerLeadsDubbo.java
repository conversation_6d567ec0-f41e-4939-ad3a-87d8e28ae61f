package com.ce.scrm.center.dubbo.api;

import com.ce.scrm.center.dubbo.entity.dto.ClueInfoDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.view.CustomerLeadsDubboView;

import java.util.List;

public interface CustomerLeadsDubbo {

	/**
	 * 线索导入
	 * @param clueList 线索列表
	 * @return 是否导入成功
	 */
    DubboResult<Boolean> clueImport(List<ClueInfoDubboDto> clueList);

	DubboResult<List<CustomerLeadsDubboView>> getListByCustomerId(String customerId);
}
