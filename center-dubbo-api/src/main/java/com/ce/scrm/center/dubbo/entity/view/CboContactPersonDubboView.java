package com.ce.scrm.center.dubbo.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/14
 */
@Data
public class CboContactPersonDubboView implements Serializable {

    /**
     * 客户id
     */
    private String customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 联系人id
     */
    private String contactPersonId;
    /**
     * 联系人姓名
     */
    private String contactPersonName;
    /**
     * 性别：1、未知，2、男，3、女
     */
    private Integer gender;
    /**
     * 联系人来源
     */
    private String source;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 手机是否实名
     * 0:未实名
     * 1:已实名
     */
    private Integer mobileStatus;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     *  是否法人 0、否，1、是
     */
    private Integer legalPersonFlag;
}
