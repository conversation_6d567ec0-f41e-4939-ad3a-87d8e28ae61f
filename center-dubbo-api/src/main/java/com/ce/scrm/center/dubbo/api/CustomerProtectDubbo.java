package com.ce.scrm.center.dubbo.api;

import com.ce.scrm.center.dubbo.entity.dto.CustomerProtectDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.TurnoverCustQueryConditionDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.dto.AddProtectTimeEventDubboDto;
import com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew;
import com.ce.scrm.center.dubbo.entity.view.TurnoverCustQueryConditionDubboView;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/19
 */
public interface CustomerProtectDubbo {
  
    /***
     * 创建保护关系
     * @param customerProtectDubboDto
     * <AUTHOR>
     * @date 2024/4/19 23:05
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.String>
    **/
    DubboResult<String> save(CustomerProtectDubboDto customerProtectDubboDto);


    /***
     * 根据客户ID查询客户信息
     * @param customerId
     * <AUTHOR>
     * @date 2024/4/19 23:07
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew>
    **/
    DubboResult<CustomerProtectDubboVew> selectCustomerById(String customerId);


    /***
     * 根据uncid查询客户信息
     * 历史原因 客户会出现 新老名称，但是uncid是一样的
     * @param uncid
     * <AUTHOR>
     * @date 2024/4/19 23:08
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew>>
    **/
    DubboResult<List<CustomerProtectDubboVew>> selectCustomerByUncid(String uncid);

    /***
     * 根据customerName查询
     * @param customerName
     * <AUTHOR>
     * @date 2024/4/19 23:09
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew>
    **/
    DubboResult<CustomerProtectDubboVew> selectCustomerByName(String customerName);


    /***
     * 查询商务的客户列表
     * @param salerId
     * <AUTHOR>
     * @date 2024/4/19 23:11
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew>>
    **/
    DubboResult<List<CustomerProtectDubboVew>> selectCustomerBySalerId(String salerId);

    /***
     * 综合查询
     * @param customerProtectDubboDto
     * <AUTHOR>
     * @date 2024/4/22 11:47
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew>>
    **/
    DubboResult<List<CustomerProtectDubboVew>> selectByCondition(CustomerProtectDubboDto customerProtectDubboDto);

    /***
     * 根据custId更新保护关系信息，注：里面对last字段进行特殊处理，更新操作应走此方法
     * @param customerProtectDubboDto
     * <AUTHOR>
     * @date 2024/4/22 11:47
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.Integer>
    **/
    DubboResult<Integer> updateByCustId(CustomerProtectDubboDto customerProtectDubboDto);

    /**
     * @description: 根据custId更新，可以更新空值，所以要注意【先查在更】确保查询全量字段 ！！！
     * @author: lijinpeng
     * @date: 2025/7/16 09:44
     * @param: [customerProtectDubboDto]
     * @return: com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.Integer>
     **/
    DubboResult<Integer> updateNullableByCustId(CustomerProtectDubboDto customerProtectDubboDto);


    /***
     * 根据客户ID更新客户信息
     * @param customerProtectDubboDto
     * <AUTHOR>
     * @date 2024/4/22 11:47
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.Integer>
    **/
    DubboResult<Integer> updateByCustomerId(CustomerProtectDubboDto customerProtectDubboDto);

    /***
     * 根据客户ID删除客户
     * @param customerId
     * <AUTHOR>
     * @date 2024/4/22 11:47
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.Integer>
    **/
    DubboResult<Integer> deleteByCustomerId(String customerId);

    /*
     * @Description 根据条件查询成交客户
     * <AUTHOR>
     * @date 2024/12/30 11:43
     * @param turnoverCustQueryConditionDubboDto
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.response.DubboPageInfo<com.ce.scrm.center.dubbo.entity.view.TurnoverCustQueryConditionDubboView>>
     */
    DubboResult<DubboPageInfo<TurnoverCustQueryConditionDubboView>> selectTurnoverCustByCondition(TurnoverCustQueryConditionDubboDto turnoverCustQueryConditionDubboDto);

    /**
     * @description: 由事件(会销签到、有效打卡、绑定用户)触发的增加保护时间动作
     * @author: lijinpeng
     * @date: 2025/7/16 10:21
     * @param: [addProtectTimeEventDubboDto]
     * @return: com.ce.scrm.center.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> addProtectTimeByEvent(AddProtectTimeEventDubboDto addProtectTimeEventDubboDto);

}
