package com.ce.scrm.center.dubbo.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/14
 */
@Data
public class CboProtectDubboView implements Serializable {
    /**
     * 客户id
     */
    private String customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户类型：
     * 1:国内企业
     * 2:个人
     */
    private Integer customerType;
    /**
     * 商务ID
     */
    private String salerId;

    /**
     * 商务部门ID
     */
    private String deptId;

    /**
     * 商务分司ID
     */
    private String subId;

    /**
     * 商务区域ID
     */
    private String areaId;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 是否流失客户 0:否，1:是
     */
    private Integer tagLostCust;

    /**
     * 流失时间
     */
    private Date tagLostTime;

    //行业信息
    private String firstIndustryCode;
    private String firstIndustryName;
    private String secondIndustryCode;
    private String secondIndustryName;

}
