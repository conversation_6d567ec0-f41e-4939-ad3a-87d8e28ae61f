package com.ce.scrm.center.dubbo.api;

import com.ce.scrm.center.dubbo.entity.dto.EqixiuActivityInfoDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.EqixiuActivityUrlDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;

import java.util.Map;

public interface ScrmActivityDubbo {

    DubboResult<Map<String, Object>> getShareLinkUrl(String custId, EqixiuActivityUrlDubboDto eqixiuActivityUrlDubboDto);

    DubboResult<Map<String, Object>> getActivityList(String custId, EqixiuActivityInfoDubboDto eqixiuActivityInfoDubboDto);

}
