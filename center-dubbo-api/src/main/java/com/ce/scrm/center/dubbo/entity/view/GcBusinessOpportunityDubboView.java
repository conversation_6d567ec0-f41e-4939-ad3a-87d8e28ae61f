package com.ce.scrm.center.dubbo.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 高呈商机dubbo返回数据
 * <AUTHOR>
 * @date 2024/5/15 上午11:27
 * @version 1.0.0
 **/
@Data
public class GcBusinessOpportunityDubboView implements Serializable {
    /**
     * 高呈商机ID（主键）
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 等级：1、A（高），2、B（中），3、C（低），4、D（其他）
     */
    private Integer level;

    /**
     * 来源：GcSjSourceEnum
     */
    private Integer source;

    /**
     * 高呈商机状态，GcSjStateEnum
     */
    private Integer state;

    /**
     * 联系人名称
     */
    private String linkmanName;

    /**
     * 联系人手机号
     */
    private String linkmanPhone;

    /**
     * 联系人邮箱
     */
    private String linkmanEmail;

    /**
     * 联系人部门
     */
    private String linkmanDept;

    /**
     * 联系人性别：0、未知，1、男，2、女
     */
    private Integer linkmanSex;

    /**
     * 联系人座机
     */
    private String linkmanLandline;

    /**
     * 联系人职务
     */
    private String linkmanJob;

    /**
     * 联系人微信
     */
    private String linkmanWechat;

    /**
     * 需求类型：GcSjRequirementEnum
     */
    private Integer requirementType;

    /**
     * 需求详情
     */
    private String requirementDetail;

    /**
     * 客户预算
     */
    private BigDecimal customerBudget;

    /**
     * 附件ID（多个使用,分隔）
     */
    private String attachmentIds;

    /**
     * 是否面谈（1、是，0、否）
     */
    private Integer interviewState;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分司ID
     */
    private String subId;

    /**
     * 分司名称
     */
    private String subName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商务ID
     */
    private String salerId;

    /**
     * 商务名称
     */
    private String salerName;

    /**
     * 高呈区域ID
     */
    private String gcAreaId;

    /**
     * 高呈区域名称
     */
    private String gcAreaName;

    /**
     * 高呈分司ID
     */
    private String gcSubId;

    /**
     * 高呈分司名称
     */
    private String gcSubName;

    /**
     * 高呈部门ID
     */
    private String gcDeptId;

    /**
     * 高呈部门名称
     */
    private String gcDeptName;

    /**
     * 高呈商务ID
     */
    private String gcSalerId;

    /**
     * 高呈商务名称
     */
    private String gcSalerName;

    /**
     * 期望高呈区域ID
     */
    private String expectGcAreaId;

    /**
     * 期望高呈区域名称
     */
    private String expectGcAreaName;

    /**
     * 期望高呈分司ID
     */
    private String expectGcSubId;

    /**
     * 期望高呈分司名称
     */
    private String expectGcSubName;

    /**
     * 期望高呈部门ID
     */
    private String expectGcDeptId;

    /**
     * 期望高呈部门名称
     */
    private String expectGcDeptName;

    /**
     * 期望高呈商务ID
     */
    private String expectGcSalerId;

    /**
     * 期望高呈商务名称
     */
    private String expectGcSalerName;

    /**
     * 签单金额
     */
    private BigDecimal orderAmount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}