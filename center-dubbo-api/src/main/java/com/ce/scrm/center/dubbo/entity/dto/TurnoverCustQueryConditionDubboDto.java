package com.ce.scrm.center.dubbo.entity.dto;

import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description: 成交客户查询条件
 * @Author: lijinpeng
 * @Date: 2024/12/30 11:39
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TurnoverCustQueryConditionDubboDto implements Serializable {

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 商务id
     */
    private String salerId;

    private String deptId;

    private String buId;

    private String subId;

    private String areaId;

    /**
     * 传2 查保护跟进 其他：查成交
     */
    private Integer custType;

    private List<String> subIdList;

    /**
     * 搜客宝 1:规模工业,2:规上服务业,3:规上建筑业,4:规上批发零售业,5:规上住宿餐饮业,6:规上房地产开发与经营业
     */
    private List<String> tagFlag7List;

    private Integer pageNum = 1;

    private Integer pageSize = 10;

}
