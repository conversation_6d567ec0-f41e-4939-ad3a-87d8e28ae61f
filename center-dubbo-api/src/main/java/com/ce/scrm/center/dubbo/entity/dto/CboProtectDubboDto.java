package com.ce.scrm.center.dubbo.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/14
 */
@Data
public class CboProtectDubboDto implements Serializable {
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 客户名称
     * 模糊查询
     */
    private String customerName;
    /**
     * 商务ID
     */
    private String salerId;

    /**
     * 页码
     * 默认 1
     */
    private Integer pageNum;
    /**
     * 每页大小
     * 默认 10
     */
    private Integer pageSize;
}
