package com.ce.scrm.center.dubbo.entity.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class EqixiuActivityInfoDubboDto implements Serializable {

    /**
     *
     */
    private String id;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动基础地址
     */
    private String activityUrl;

    /**
     * 1 应用场景 2:分司活动
     */
    private Integer activityType;

    /**
     * 活动开始时间
     */
    private String startTime;

    /**
     * 活动结束时间
     */
    private String endTime;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 分享标题
     */
    private String shareTitle;

    /**
     * 分享描述
     */
    private String shareDesc;

    /**
     *
     */
    private String sharePic;

    /**
     *
     */
    private String sharePosterInfo;

    /**
     * 可见范围 1: 全部 2 部分
     */
    private Integer visibleRange;


    /**
     * 是否可以卡片分享 0 不可以 1 可以
     */
    private Integer cardShare;

    /**
     * 是否可以海报分享 0 不可以 1 可以
     */
    private Integer posterShare;


    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 每页数量
     */
    private Integer pageSize = 10;


}
