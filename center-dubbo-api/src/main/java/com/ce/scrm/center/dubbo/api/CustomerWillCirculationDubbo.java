package com.ce.scrm.center.dubbo.api;

import com.ce.scrm.center.dubbo.entity.dto.CirculationLossDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;

/**
 * 客户流转流失dubbo接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/7/23
 */
public interface CustomerWillCirculationDubbo {

    /**
     * Description: 判断是否是流失客户
     *
     * @param custId 客户id
     * @return
     * @author: liyechao
     */
    DubboResult<Boolean> isLossCust(String custId);

    /**
     * Description: 更新流失、流转信息
     *
     * @param
     * @return
     * @author: liyechao
     */
    void updateCustomerWillCirculation(CirculationLossDubboDto circulationLossDubboDto);

}