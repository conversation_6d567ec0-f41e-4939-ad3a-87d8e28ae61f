<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>center-util</module>
        <module>center-support</module>
        <module>center-service</module>
        <module>center-dao</module>
        <module>center-cache</module>
        <module>center-dubbo-api</module>
<!--        <module>center-dubbo</module>-->
        <module>center-async</module>
        <module>center-web</module>
    </modules>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.6.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.ce.scrm</groupId>
    <artifactId>scrm-center</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>scrm-center</name>
    <description>scrm-center</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <!-- springboot版本 -->
        <spring-boot.version>2.3.6.RELEASE</spring-boot.version>
        <spring.version>5.2.11.RELEASE</spring.version>
        <!-- service版本 -->
        <service.version>1.0.0-SNAPSHOT</service.version>
        <!-- api7版本 -->
        <service7.version>1.0.7</service7.version>
        <!-- fastJson版本 -->
        <fastjson.version>1.2.83</fastjson.version>
        <!-- mysql版本 -->
        <mysql.version>8.0.29</mysql.version>
        <!-- druid加密 -->
        <druid.jasypt.version>1.0.0-SNAPSHOT</druid.jasypt.version>
        <!-- xxljob版本 -->
        <xxljob.version>1.0.11-RELEASE</xxljob.version>
        <!-- rocketMq版本 -->
        <rocketmq.spring.boot.version>*******</rocketmq.spring.boot.version>
        <!-- rocketMq版本 -->
        <rocketmq.jasypt.spring.boot.version>1.0.0-SNAPSHOT</rocketmq.jasypt.spring.boot.version>
        <!-- Dubbo SpringBoot Starter 版本 -->
        <dubbo.spring.boot.version>2.7.15</dubbo.spring.boot.version>
        <!-- cat版本 -->
        <cat.version>3.0.0</cat.version>
        <!-- zkclient（ZooKeeper客户端）版本 -->
        <zkclient.version>0.1</zkclient.version>
        <!-- zookeeper 版本 -->
        <zookeeper.version>3.4.13</zookeeper.version>
        <!-- Curator-frame版本 -->
        <curator.version>2.8.0</curator.version>
        <!-- curator-recipes版本 -->
        <curator.recipes.version>2.8.0</curator.recipes.version>
        <!-- mybatis版本 -->
        <mybatis.version>3.5.2</mybatis.version>
        <!-- nacos版本 -->
        <nocas.version>0.2.7</nocas.version>
        <!-- 发号器版本 -->
        <sequence.version>1.0.2</sequence.version>
        <!-- 发号器impl版本 -->
        <sequence.impl.version>1.0.0</sequence.impl.version>
        <!-- transmittable-thread-local版本-->
        <transmittable.thread.local.version>2.12.0</transmittable.thread.local.version>
        <!-- 升级tomcat版本为修复原版本漏洞-->
        <tomcat.version>9.0.55</tomcat.version>
        <!-- slf4j版本 -->
        <slf4j.version>1.7.32</slf4j.version>
        <!-- logback版本 -->
        <logback.version>1.2.9</logback.version>
        <!-- nacos动态切换redis集群 -->
        <spring.redis.nacos.switcher.version>2.0.1</spring.redis.nacos.switcher.version>
        <!--nacos加密 版本 -->
        <nacos.jasypt>1.0.0-SNAPSHOT</nacos.jasypt>
        <!--参数过滤-->
        <params-filter.version>1.3.0-SNAPSHOT</params-filter.version>
        <!-- hutool 版本-->
        <hutool.version>5.8.25</hutool.version>
        <!-- cglib版本 -->
        <cglib.version>3.3.0</cglib.version>
        <!-- Lombok版本-->
        <lombok.version>1.18.24</lombok.version>
        <!-- Logback版本-->
        <logback.version>1.2.9</logback.version>
        <!-- 员工dubbo -->
        <emp.dubbo.version>0.0.1-SNAPSHOT</emp.dubbo.version>
        <scrm.extend.version>1.0.0-SNAPSHOT</scrm.extend.version>
        <!-- 客户dubbo版本 -->
        <customer.dubbo.version>1.0.0-SNAPSHOT</customer.dubbo.version>
        <!-- rbac版本 -->
        <rbac.dubbo.version>0.0.1-SNAPSHOT</rbac.dubbo.version>
        <!-- 合同版本 -->
        <contract.dubbo.version>0.0.1-SNAPSHOT</contract.dubbo.version>
        <!-- 线索版本 -->
        <newcustclue.dubbo.version>0.0.1-SNAPSHOT</newcustclue.dubbo.version>
        <!--单点登录-->
        <sso.version>1.0.0-SNAPSHOT</sso.version>
        <!-- 表格工具 -->
        <easy.excel.version>3.3.4</easy.excel.version>
        <!-- scrm-common 常量 -->
        <scrm.common.base.version>0.0.1-SNAPSHOT</scrm.common.base.version>
        <!--财务领域 -->
        <performance.remoteservice.api.version>0.0.1-SNAPSHOT</performance.remoteservice.api.version>
        <!--获取客户产品相关-->
        <appservice.instance.api.version>0.0.1-SNAPSHOT</appservice.instance.api.version>
        <!--cdp的hive数据库-->
        <cdp.dubbo.version>1.0.0-SNAPSHOT</cdp.dubbo.version>
        <gj.version>0.0.1-SNAPSHOT</gj.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easy.excel.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ce.scrm.extend</groupId>
                <artifactId>extend-dubbo-api</artifactId>
                <version>${scrm.extend.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.ce.cesupport</groupId>
                <artifactId>appservice-emp-api</artifactId>
                <version>${emp.dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ce.scrm</groupId>
                <artifactId>center-dubbo-api</artifactId>
                <version>${service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ce.scrm</groupId>
                <artifactId>center-dao</artifactId>
                <version>${service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ce.scrm</groupId>
                <artifactId>center-util</artifactId>
                <version>${service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ce.scrm</groupId>
                <artifactId>center-cache</artifactId>
                <version>${service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ce.scrm</groupId>
                <artifactId>center-support</artifactId>
                <version>${service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ce.scrm</groupId>
                <artifactId>center-service</artifactId>
                <version>${service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>2.3</version>
            </dependency>
            <dependency>
                <groupId>cn.ce.omo</groupId>
                <artifactId>sequence-api</artifactId>
                <version>${sequence.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.ce.omo</groupId>
                <artifactId>sequence-api-impl</artifactId>
                <version>${sequence.impl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ce.omo.common</groupId>
                <artifactId>druid-jasypt-spring-boot-starter</artifactId>
                <version>${druid.jasypt.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.ce</groupId>
                <artifactId>spring-boot-starter-xxljob</artifactId>
                <version>${xxljob.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot</artifactId>
                <version>${rocketmq.spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ce.omo.common</groupId>
                <artifactId>rocketmq-jasypt-spring-boot-starter</artifactId>
                <version>${rocketmq.jasypt.spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.curator</groupId>
                        <artifactId>curator-framework</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.cat</groupId>
                <artifactId>cat-client</artifactId>
                <version>${cat.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.sgroschupf</groupId>
                <artifactId>zkclient</artifactId>
                <version>${zkclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.zookeeper</groupId>
                        <artifactId>zookeeper</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.zookeeper</groupId>
                        <artifactId>zookeeper</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.recipes.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.curator</groupId>
                        <artifactId>curator-framework</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.zookeeper</groupId>
                        <artifactId>zookeeper</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.junit.vintage</groupId>
                        <artifactId>junit-vintage-engine</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <optional>true</optional>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nocas.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.spring</groupId>
                        <artifactId>spring-context-support</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.boot</groupId>
                        <artifactId>nacos-config-spring-boot-autoconfigure</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable.thread.local.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.ce.omo.common</groupId>
                <artifactId>spring-redis-nacos-switcher</artifactId>
                <version>${spring.redis.nacos.switcher.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.ce.omo.common</groupId>
                <artifactId>nacos-jasypt-spring-boot-starter</artifactId>
                <version>${nacos.jasypt}</version>
            </dependency>
            <dependency>
                <groupId>com.ce.omo</groupId>
                <artifactId>params-filter-spring-boot-starter</artifactId>
                <version>${params-filter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <optional>true</optional>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ce.scrm.customer</groupId>
                <artifactId>customer-dubbo-api</artifactId>
                <version>${customer.dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.ce.cesupport</groupId>
                <artifactId>appservice-rbac-api</artifactId>
                <version>${rbac.dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.ce.cesupport</groupId>
                        <artifactId>service-rbac-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.ce</groupId>
                <artifactId>sso-client-spring-boot-starter</artifactId>
                <version>${sso.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>redis.clients</groupId>
                        <artifactId>jedis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 为了解决上面单点登录的bug -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>2.9.3</version>
            </dependency>
            <dependency>
                <groupId>cn.ce.cesupport</groupId>
                <artifactId>appservice-contract-api</artifactId>
                <version>${contract.dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.ce.cesupport</groupId>
                        <artifactId>service-orderce-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.ce.cesupport</groupId>
                <artifactId>appservice-newcustclue-api</artifactId>
                <version>${newcustclue.dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.ce.cesupport</groupId>
                        <artifactId>framework-cesupport-base</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.ce.cesupport</groupId>
                        <artifactId>appservice-callcenter-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--财务领域 -->
            <dependency>
                <groupId>cn.ce.performanceCenter</groupId>
                <artifactId>performance-remoteservice-api</artifactId>
                <version>${performance.remoteservice.api.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.ce.cesupport</groupId>
                <artifactId>scrm-common-base</artifactId>
                <version>${scrm.common.base.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.ce.cesupport</groupId>
                <artifactId>appservice-instance-api</artifactId>
                <version>${appservice.instance.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ce.scrm.extend</groupId>
                <artifactId>cdp-dubbo-api</artifactId>
                <version>${cdp.dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.ce.cesupport</groupId>
                <artifactId>appservice-gj-api</artifactId>
                <version>${gj.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <!-- SNAPSHOT组件发布到私服 mvn deploy -->
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://nexus.300.cn:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
        <!-- RELEASE组件发布到私服 mvn deploy -->
        <repository>
            <id>releases</id>
            <url>http://nexus.300.cn:8081/nexus/content/repositories/releases</url>
        </repository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>snapshots</id>
            <url>http://nexus.300.cn:8081/nexus/content/repositories/snapshots</url>
        </repository>
        <repository>
            <id>releases</id>
            <url>http://nexus.300.cn:8081/nexus/content/repositories/releases</url>
        </repository>
        <repository>
            <id>private-repos</id>
            <url>http://test-omo.aiyouyi.cn/nexus/repository/maven-public/</url>
        </repository>
<!--        <repository>-->
<!--            <id>maven_central</id>-->
<!--            <name>Maven Central</name>-->
<!--            <url>https://repo.maven.apache.org/maven2/</url>-->
<!--        </repository>-->
    </repositories>
    <build>
    </build>
</project>
